-- AlterTable
ALTER TABLE "containers" ADD COLUMN     "deleted_by_confirm" INTEGER,
ADD COLUMN     "deleted_reason" TEXT;

-- AlterTable
ALTER TABLE "invoices" ADD COLUMN     "deleted_by_confirm" INTEGER,
ADD COLUMN     "deleted_reason" TEXT;



-- AddForeignKey
ALTER TABLE "containers" ADD CONSTRAINT "containers_deleted_by_confirm_fkey" FOREIGN KEY ("deleted_by_confirm") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_deleted_by_confirm_fkey" FOREIGN KEY ("deleted_by_confirm") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION;
