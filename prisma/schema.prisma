generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch"]
}

datasource prisma {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model activity_log {
  id           Int       @id @default(autoincrement())
  log_name     String?   @prisma.VarChar(255)
  description  String
  subject_type String?   @prisma.VarChar(255)
  subject_id   Int?
  causer_type  String?   @prisma.VarChar(255)
  causer_id    Int?
  properties   Json?     @prisma.<PERSON><PERSON>
  created_at   DateTime? @default(now())
  updated_at   DateTime? @updatedAt

  @@index([log_name])
  @@index([causer_type, causer_id], map: "causer")
  @@index([subject_type, subject_id], map: "subject")
}

enum CustomerRegion {
  local
  export
  local_and_export
}

model companies {
  id                              Int             @id @default(autoincrement())
  name                            String          @prisma.VarChar(128)
  parent_id                       Int?
  parent_company                  companies?      @relation("SelfRelation", fields: [parent_id], references: [id])
  child_company                   companies[]     @relation("SelfRelation")
  days_limit                      Int?
  credit_limit                    Int?
  perecentage_of_in_process_cargo Int?
  address                         String?         @prisma.VarChar(255)
  phone                           String?         @prisma.VarChar(64)
  is_belong_to_used_car           Boolean         @default(false)
  scrap                           Boolean         @default(false)
  mix                             Boolean         @default(false)
  complete                        Boolean         @default(false)
  complete_halfcut                Boolean         @default(false)
  mix_halfcut                     Boolean         @default(false)
  suv                             Boolean         @default(false)
  sedan                           Boolean         @default(false)
  customer_region                 CustomerRegion?

  ///New Fields//
  join_date         DateTime? @prisma.Date
  preferred_com_way String?   @prisma.VarChar(64)
  company_city      String?   @prisma.VarChar(128)
  zip_code          String?   @prisma.VarChar(64)
  country           String?   @prisma.VarChar(64)
  // contract //
  contract          String?   @prisma.VarChar(255)
  trade_license     String?   @prisma.VarChar(255)
  id_passport       String?   @prisma.VarChar(255)
  code              String?   @prisma.VarChar(64)

  is_special_rate          Boolean               @default(false)
  is_mix_special_rate      Boolean               @default(false)
  show_shipping_rate       Boolean               @default(true)
  show_shipping_rate_terms Boolean               @default(false)
  has_customer             Boolean               @default(false)
  has_customer_invoice     Boolean               @default(false)
  shipping_rate_note       String?
  profile_name             String?               @prisma.Text
  logo                     String?
  rate_apply_date          rate_apply_date_enum?

  consignee                         String?                       @prisma.VarChar(128)
  consignee_street                  String?                       @prisma.VarChar(128)
  consignee_box                     String?                       @prisma.VarChar(128)
  consignee_city                    String?                       @prisma.VarChar(64)
  consignee_zip_code                String?                       @prisma.VarChar(64)
  consignee_country                 String?                       @prisma.VarChar(64)
  consignee_phone                   String?                       @prisma.VarChar(64)
  consignee_email                   String?                       @prisma.VarChar(64)
  consignee_fax                     String?                       @prisma.VarChar(64)
  consignee_poc                     String?                       @prisma.VarChar(64)
  notify_party                      String?                       @prisma.VarChar(64)
  notify_street                     String?                       @prisma.VarChar(128)
  notify_box                        String?                       @prisma.VarChar(64)
  notify_city                       String?                       @prisma.VarChar(64)
  notify_state                      String?                       @prisma.VarChar(64)
  notify_zip                        String?                       @prisma.VarChar(64)
  notify_country                    String?                       @prisma.VarChar(64)
  notify_phone                      String?                       @prisma.VarChar(64)
  notify_email                      String?                       @prisma.VarChar(64)
  notify_fax                        String?                       @prisma.VarChar(64)
  notify_poc                        String?                       @prisma.VarChar(64)
  loading_instruction               String?
  documentation_instruction         String?
  half_cut_loading_instruction      String?
  vehicles_number                   String? // Load Volume
  containers_number                 String? // Load Volume
  tier_level                        company_tier_level?
  bill_of_loading_type              bill_loading_type_enum?
  ////End////
  created_at                        DateTime?                     @default(now())
  created_by                        Int?
  updated_at                        DateTime?                     @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime?                     @prisma.Timestamptz(6)
  deleted_by                        Int?
  users_companies_created_byTousers users?                        @relation("companies_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_companies_deleted_byTousers users?                        @relation("companies_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_companies_updated_byTousers users?                        @relation("companies_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  containers                        containers[]
  customers                         customers[]
  customer_of_customer              customer_of_customer[]
  invoices                          invoices[]
  vehicles                          vehicles[]
  company_shipping_rates            company_shipping_rates[]
  special_shipping_rates            special_shipping_rates[]
  mix_shipping_invoices             mix_shipping_invoices[]
  company_notes                     company_notes[]
  //company_loading                   company_loading[]
  yard_locations                    yard_locations[]              @relation("company_loading")
  /// destination relations
  destination_id                    Int?
  destinations                      destinations?                 @relation(fields: [destination_id], references: [id], onUpdate: NoAction)
  delivery_charge_invoice           delivery_charge_invoice[]
  clear_logs                        clear_logs[]
  clearing_company_clear_logs       clear_logs[]                  @relation("ClearingCompany")
  company_loading_cost              company_loading_cost[]
  booking_parties                   bookings_booking_party_enum[]

  banned_locations               locations[]                      @relation("company_banned_locations")
  customer_payment_transactions  customer_payment_transactions[]
  company_general_shipping_rates company_general_shipping_rates[]
  mix_shipping_rates             mix_shipping_rates[]
  company_charges                company_charges[]
  exchange_rates                 exchange_rates[]
  shipping_rate                  shipping_rates_2[]
  buyer_numbers                  buyer_numbers[]
  customer_vehicle_invoice       customer_vehicle_invoice[]
  company_charges_v2             company_charges_v2[]
  company_consignees             company_consignees[]

  @@index([name])
  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

enum company_tier_level {
  golden
}

enum bill_loading_type_enum {
  Normal_bl
  VIP_bl
  VIP1_bl
  VIP2_bl
  VIP3_bl
  Scrap
  Individual
  Separate_Individual
  All_Mix
}

model company_consignees {
  id         Int       @id @default(autoincrement())
  company_id Int
  company    companies @relation(fields: [company_id], references: [id], onDelete: Cascade)

  consignee          String?  @prisma.VarChar(128)
  consignee_street   String?  @prisma.VarChar(128)
  consignee_box      String?  @prisma.VarChar(128)
  consignee_city     String?  @prisma.VarChar(64)
  consignee_zip_code String?  @prisma.VarChar(64)
  consignee_country  String?  @prisma.VarChar(64)
  consignee_phone    String?  @prisma.VarChar(64)
  consignee_email    String?  @prisma.VarChar(64)
  consignee_fax      String?  @prisma.VarChar(64)
  consignee_poc      String?  @prisma.VarChar(64)
  default_consignee  Boolean? @default(true)

  created_at DateTime? @default(now())
  updated_at DateTime? @updatedAt
  created_by Int?
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  createdByUser users? @relation("ConsigneeCreatedBy", fields: [created_by], references: [id])
  updatedByUser users? @relation("ConsigneeUpdatedBy", fields: [updated_by], references: [id])
  deletedByUser users? @relation("ConsigneeDeletedBy", fields: [deleted_by], references: [id])
}

model company_notes {
  id          Int       @id @default(autoincrement())
  location_id Int
  company_id  Int
  note        String?
  reason      String?   @prisma.Text
  created_at  DateTime? @default(now())
  created_by  Int?
  updated_at  DateTime? @updatedAt
  updated_by  Int?
  deleted_at  DateTime? @prisma.Timestamptz(6)
  deleted_by  Int?

  companies         companies? @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  locations         locations? @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  created_byToUsers users?     @relation("companyNote_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?     @relation("companyNote_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?     @relation("companyNote_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@unique([company_id, location_id])
  @@index([note])
  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model bookings {
  id                                 Int                           @id @default(autoincrement())
  booking_number                     String?                       @prisma.VarChar(255)
  eta                                DateTime?                     @prisma.Date
  eta_status                         brd_erd_status?
  cost                               Int                           @default(0)
  qty                                Int?
  vessel_id                          Int?
  port_of_discharge                  Int?
  size                               String?                       @prisma.VarChar(30)
  description                        String?
  status                             bookings_booking_status_enum?
  status_changed_at                  DateTime?                     @prisma.Date
  party                              bookings_booking_party_enum?
  si                                 bookings_si_enum?
  type                               bookings_booking_type_enum?
  check_status                       booking_check_status          @default(prechecked)
  free_days                          Int                           @default(7)
  cancelled_at                       DateTime?                     @prisma.Timestamptz(6)
  cancelled_by                       Int?
  reviewed_at                        DateTime?                     @prisma.Timestamptz(6)
  reviewed_by                        Int?
  created_at                         DateTime?                     @default(now())
  created_by                         Int?
  updated_at                         DateTime?                     @updatedAt
  updated_by                         Int?
  deleted_at                         DateTime?                     @prisma.Timestamptz(6)
  deleted_by                         Int?
  parent_id                          Int?
  parent                             bookings?                     @relation("ChildBookings", fields: [parent_id], references: [id], onDelete: NoAction)
  children                           bookings[]                    @relation("ChildBookings")
  users_bookings_cancelled_byTousers users?                        @relation("bookings_cancelled_byTousers", fields: [cancelled_by], references: [id], onUpdate: NoAction)
  users_bookings_created_byTousers   users?                        @relation("bookings_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_bookings_deleted_byTousers   users?                        @relation("bookings_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  destinations                       destinations?                 @relation(fields: [port_of_discharge], references: [id], onUpdate: NoAction)
  users_bookings_reviewed_byTousers  users?                        @relation("bookings_reviewed_byTousers", fields: [reviewed_by], references: [id], onUpdate: NoAction)
  users_bookings_updated_byTousers   users?                        @relation("bookings_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vessels                            vessels?                      @relation(fields: [vessel_id], references: [id], onUpdate: NoAction)
  containers                         containers[]
  tds_cost                           Float                         @default(0.00)
  eta_updated_at                     DateTime?                     @prisma.Timestamptz(6)
  is_cancelled_with_charges          Boolean?
  booking_assign                     Boolean                       @default(false)
  is_rated                           Boolean?                      @default(true)
  bill_of_loadings                   bill_of_loadings[]            @relation("billOfLoadingToBookings")
  dispute_bookingsId                 Int?
  dispute_bookings                   dispute_bookings[]

  @@index([booking_number])
  @@index([cancelled_by])
  @@index([created_by])
  @@index([deleted_by])
  @@index([port_of_discharge])
  @@index([reviewed_by])
  @@index([updated_by])
  @@index([vessel_id])
  @@index([parent_id], name: "IDX_bookings_parent_id")
}

enum booking_check_status {
  prechecked
  checked
}

model containers {
  id                           Int                      @id @default(autoincrement())
  container_number             String?                  @prisma.VarChar(64)
  booking_id                   Int?
  bill_of_loading_id           Int?
  shipping_document_id         Int?
  dispute_booking_id           Int?
  booking_suffix               String?                  @prisma.VarChar(255)
  aes_itn_number               String?                  @prisma.VarChar(64)
  tracking_contatiner          String?                  @prisma.VarChar(300)
  container_id_update_date     String?                  @prisma.VarChar(255)
  bill_of_loading_number       String?                  @prisma.VarChar(64)
  seal_number                  String?                  @prisma.VarChar(64)
  actions                      String?                  @prisma.VarChar(300)
  measurement                  String?                  @prisma.VarChar(64)
  no_units_load                String?
  invoice_number               String?                  @prisma.VarChar(255)
  company_id                   Int?
  loading_instruction          String?
  documentation_instruction    String?
  amount                       Decimal?                 @prisma.Decimal
  photo_link                   String?                  @prisma.VarChar(255)
  aes_filling_link             String?                  @prisma.VarChar(255)
  status                       container_status         @default(pending)
  pending_arrival              Boolean?
  clearance_status             clearance_status         @default(pending)
  clearance_ingate_date        DateTime?
  isPendingTrash               Boolean?                 @default(false)
  pin_in                       String?                  @prisma.VarChar(255)
  pin_out                      String?                  @prisma.VarChar(255)
  ingate                       Boolean?
  yard_location_id             Int?
  clearance_invoice_link       String?                  @prisma.VarChar(255)
  invisible_for_customer       Boolean?                 @default(false)
  shipment_type_approved       Boolean?                 @default(false)
  pl_status                    pl_status                @default(pending)
  uae_pl_status                uae_pl_status            @default(pending)
  status_changed_at            DateTime?                @prisma.Date
  container_number_assigned_at DateTime?                @prisma.Date
  release_doc_tracking         Int?
  created_at                   DateTime?                @default(now())
  created_by                   Int?
  updated_at                   DateTime?                @updatedAt
  updated_by                   Int?
  deleted_at                   DateTime?                @prisma.Timestamptz(6)
  deleted_by                   Int?
  deleted_by_confirm           Int?
  deleted_reason               String?
  loading_date                 DateTime?                @prisma.Date
  loadplan_sent_at             DateTime?                @prisma.Timestamptz(6)
  loadplan_sent_by             Int?
  pl_balance_diff              Float                    @default(0.00)
  prelim_pl_status             prelim_pl_status         @default(pending)
  title_status                 container_title_status?
  aes_status                   container_aes_status?
  prelim_pl_at                 DateTime?                @prisma.Timestamptz(6)
  pl_at                        DateTime?                @prisma.Timestamptz(6)
  uae_pl_at                    DateTime?                @prisma.Timestamptz(6)
  container_clearance_type     customer_clearance_type?
  clearance_remarks            String?
  pull_driver_id               Int?
  pull_driver_bonus            Float?
  pull_payment_id              Int?
  pull_payment_amount          Float?

  ingate_driver_id      Int?
  ingate_driver_bonus   Float?
  ingate_payment_id     Int?
  ingate_payment_amount Float?

  ingate_date         DateTime? @prisma.Date
  ingate_driver_notes String?

  pull_date         DateTime? @prisma.Date
  driver_notes      String?
  pull_driver_notes String?

  arrival_notice_sent_at DateTime?
  arrival_notice_sent_by Int?

  cover_photo      String?
  customer_comment String?

  pullDriver   drivers? @relation("containers_toPullDriver", fields: [pull_driver_id], references: [id], onUpdate: NoAction)
  inGateDriver drivers? @relation("containers_toIngateDriver", fields: [ingate_driver_id], references: [id], onUpdate: NoAction)

  pullPayment   driver_payments? @relation("containers_toPullDriverPayment", fields: [pull_payment_id], references: [id], onUpdate: NoAction)
  ingatePayment driver_payments? @relation("containers_toIngateDriverPayment", fields: [ingate_payment_id], references: [id], onUpdate: NoAction)

  loaders                            loaders[]                            @relation("containers_toLoader")
  loader_remark                      String?
  load_combination_type              load_combination_type?
  loadplan_sent_byToUser             users?                               @relation("loadplan_sent_byToUser", fields: [loadplan_sent_by], references: [id], onUpdate: NoAction)
  arrival_notice_sent_byTousers      users?                               @relation("arrival_notice_sent_byTousers", fields: [arrival_notice_sent_by], references: [id], onUpdate: NoAction)
  yards_location                     yard_locations?                      @relation(fields: [yard_location_id], references: [id], onUpdate: NoAction)
  bookings                           bookings?                            @relation(fields: [booking_id], references: [id], onUpdate: NoAction)
  bill_of_loadings                   bill_of_loadings?                    @relation(fields: [bill_of_loading_id], references: [id], onUpdate: NoAction)
  companies                          companies?                           @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  users_containers_created_byTousers users?                               @relation("containers_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_containers_deleted_byTousers users?                               @relation("containers_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_containers_deleted_by_confirmTousers users?                       @relation("containers_deleted_by_confirmTousers", fields: [deleted_by_confirm], references: [id], onUpdate: NoAction)
  shipping_documents                 shipping_documents?                  @relation(fields: [shipping_document_id], references: [id], onUpdate: NoAction)
  users_containers_updated_byTousers users?                               @relation("containers_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  invoices                           invoices[]
  vehicles                           vehicles[]
  containers_images                  containers_images[]
  mix_shipping_containers            mix_shipping_containers[]
  mix_shipping_invoices              mix_shipping_invoices[]
  container_costs                    container_costs[]
  delivery_charge_invoice            delivery_charge_invoice[]
  delivery_charge_invoice_details    delivery_charge_invoice_details[]
  clear_logs                         clear_logs[]
  clearance_combine_booking_invocies clearance_combine_booking_invocies[]
  trackings                          trackings[]                          @relation("tracking_containers")
  container_charges                  container_charges[]
  transaction_container_vehicle      transaction_container_vehicle[]      @relation("transaction_container_vehicle_container")

  transaction_no                         String?
  transaction_at                         DateTime?
  transaction_by                         Int?
  users_containers_transaction_byTousers users?              @relation("containers_transaction_byTousers", fields: [transaction_by], references: [id], onUpdate: NoAction)
  of_loading_photo                       String?             @prisma.VarChar(255)
  of_loading_video                       String?             @prisma.VarChar(255)
  etd_2025_check                         Boolean?            @default(true)
  is_issue_for_bol                       Boolean?            @default(false)
  profit_loss_sheets                     profit_loss_sheet[] @relation("PLSheetContainer")
  dispute_booking                        dispute_bookings?   @relation("ContainersToDisputeBookings", fields: [dispute_booking_id], references: [id], onUpdate: NoAction)
  customer_credits                       customer_credits[]

  @@index([booking_id])
  @@index([created_by])
  @@index([company_id])
  @@index([deleted_by])
  @@index([updated_by])
}

model customers {
  id              Int     @id @default(autoincrement())
  fullname        String  @prisma.VarChar(128)
  company_id      Int
  address         String? @prisma.VarChar(255)
  phone           String? @prisma.VarChar(64)
  gender          gender?
  photo           String? @prisma.VarChar(255)
  bio             String?
  lang            String? @prisma.VarChar(50)
  // since_date                        DateTime?      @prisma.Date
  secondary_email String? @prisma.VarChar(64)
  secondary_phone String? @prisma.VarChar(64)

  customer_type                     customer_type?
  customer_shipping_rate_type       customer_shipping_rate_type     @default(general)
  created_at                        DateTime?                       @default(now())
  created_by                        Int?
  updated_at                        DateTime?                       @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime?                       @prisma.Timestamptz(6)
  deleted_by                        Int?
  companies                         companies                       @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  users_customers_created_byTousers users?                          @relation("customers_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_customers_deleted_byTousers users?                          @relation("customers_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_customers_updated_byTousers users?                          @relation("customers_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vehicles_updated_byToCustomer     vehicles[]                      @relation("vehicles_updated_byToCustomer")
  loginable                         loginables?
  vehicles                          vehicles[]
  customer_notifications            customer_notifications[]
  announcements                     AnnouncementsToCustomers[]
  fcm_tokens                        fcm_tokens[]
  transactions                      transactions[]
  customer_payment_transactions     customer_payment_transactions[]
  settings                          Json?                           @default("{}")

  @@index([created_by])
  @@index([deleted_by])
  @@index([fullname])
  @@index([updated_by])
}

model customer_of_customer {
  id                               Int                                @id @default(autoincrement())
  fullname                         String                             @prisma.VarChar(128)
  password                         String?
  phone                            String?                            @prisma.VarChar(64)
  photo                            String?                            @prisma.VarChar(255)
  company_id                       Int?
  created_at                       DateTime?                          @default(now())
  updated_at                       DateTime?                          @updatedAt
  deleted_at                       DateTime?                          @prisma.Timestamptz(6)
  company                          companies?                         @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  loginable                        loginables?
  vehicles_of_customer_of_customer vehicles_of_customer_of_customer[]
  customer_of_customer_payments    customer_of_customer_payments[]
}

model customer_of_customer_payments {
  id                               Int                                @id @default(autoincrement())
  payment_amount                   Decimal?                           @prisma.Decimal
  payment_date                     DateTime?
  customer_of_customer_id          Int?
  vehicle_id                       Int?
  vehicles_customer_of_customer_id Int?
  vehicles_of_customer_of_customer vehicles_of_customer_of_customer[]
  customer_of_customer             customer_of_customer?              @relation(fields: [customer_of_customer_id], references: [id], onUpdate: NoAction)
  vechicles                        vehicles?                          @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)
  created_at                       DateTime?                          @default(now())
  updated_at                       DateTime?                          @updatedAt
}

model vehicles_of_customer_of_customer {
  id                              Int                            @id @default(autoincrement())
  customer_of_customer_id         Int?
  customer_of_customer            customer_of_customer?          @relation(fields: [customer_of_customer_id], references: [id], onUpdate: NoAction)
  vehicle_id                      Int?
  vechicles                       vehicles?                      @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)
  created_at                      DateTime?                      @default(now())
  updated_at                      DateTime?                      @updatedAt
  customer_of_customer_payments   customer_of_customer_payments? @relation(fields: [customer_of_customer_paymentsId], references: [id])
  customer_of_customer_paymentsId Int?
}

model customer_vehicle_invoice {
  id               Int                     @id @default(autoincrement())
  customer_id      String?
  date             DateTime?
  sale_date        DateTime?
  vechicles        mix_shipping_vehicles[]
  company_id       Int?
  company          companies?              @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  receiver_name    String?
  created_at       DateTime?               @default(now())
  created_by       Int?
  deleted_at       DateTime?
  updated_at       DateTime?               @updatedAt
  updated_by       Int?
  deleted_by       Int?
  bank_account_id  Int?
  bank_account     bank_accounts?          @relation(fields: [bank_account_id], references: [id])
  customer_vehicle customer_vehicle[]
}

model customer_vehicle {
  id                          Int                       @id @default(autoincrement())
  is_auction                  Boolean                   @default(false)
  is_shipment                 Boolean                   @default(false)
  created_at                  DateTime?                 @default(now())
  created_by                  Int?
  deleted_at                  DateTime?
  updated_at                  DateTime?                 @updatedAt
  updated_by                  Int?
  deleted_by                  Int?
  vehicle_id                  Int?
  mix_vehicle_id              Int?
  customer_vehicle_invoice_id Int?
  customer_vehicle_invoice    customer_vehicle_invoice? @relation(fields: [customer_vehicle_invoice_id], references: [id])
  vehicle                     vehicles?                 @relation(fields: [vehicle_id], references: [id])
  mix_vehicle                 mix_shipping_vehicles?    @relation(fields: [mix_vehicle_id], references: [id])
}

model destinations {
  id         Int        @id @default(autoincrement())
  name       String?    @unique(map: "destinations_name_idx") @prisma.VarChar(128)
  order      Int        @prisma.SmallInt
  created_at DateTime?  @default(now())
  created_by Int?
  updated_at DateTime?  @updatedAt
  updated_by Int?
  deleted_by Int?
  deleted_at DateTime?  @prisma.Timestamptz(6)
  bookings   bookings[]
  vehicles   vehicles[] @relation("destination_id")

  companies                            companies[]
  loading_cost                         loading_cost[]
  users_destinations_created_byTousers users?                               @relation("destinations_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_destinations_deleted_byTousers users?                               @relation("destinations_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_destinations_updated_byTousers users?                               @relation("destinations_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  general_shipping_rates               general_shipping_rates[]
  shipping_rates                       shipping_rates[]
  company_shipping_rates               company_shipping_rates[]
  special_shipping_rates               special_shipping_rates[]
  booking_estimations                  booking_estimations[]
  destination_booking_targets          vessel_destination_booking_targets[]
  shiplines_freight_rates              shiplines_freight_rates[]
  mix_shipping_rates                   mix_shipping_rates[]
  shipping_rate_destinations           shipping_rate_destinations[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model invoices {
  id                                Int            @id @default(autoincrement())
  invoice_number                    String?        @unique @prisma.VarChar(250)
  invoice_date                      DateTime?      @prisma.Date
  purpose                           String?        @prisma.VarChar(255)
  invoice_due_date                  DateTime?      @prisma.Date
  company_id                        Int?
  container_id                      Int?
  invoice_amount                    Int?
  payment_received                  Int?
  discount                          Decimal?       @prisma.Decimal(10, 2)
  received_date                     DateTime?      @prisma.Date
  payment_method                    String?        @prisma.VarChar(255)
  status                            invoice_status @default(pending)
  isPendingTrash                    Boolean?       @default(false)
  description                       String?
  checked_by                        Int?
  rejected_by                       Int?
  rejected_at                       DateTime?      @prisma.Date
  rejected_reason                   String?        @prisma.Text
  status_changed_at                 DateTime?      @prisma.Date
  evidence_proof                    String?        @prisma.VarChar(255)
  move_to_open_date                 DateTime?      @prisma.Date
  title_charge_visible              Boolean        @default(true)
  towing_charge_visible             Boolean        @default(true)
  customer_comment                  String?
  cleared_by                        Int?
  created_at                        DateTime?      @default(now())
  created_by                        Int?
  updated_at                        DateTime?      @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime?      @prisma.Timestamptz(6)
  deleted_by                        Int?
  deleted_by_confirm                Int?
  deleted_reason                    String?
  payments                          payments[]
  users_invoices_checked_byTousers  users?         @relation("invoices_checked_byTousers", fields: [checked_by], references: [id], onUpdate: NoAction)
  users_invoices_rejected_byTousers users?         @relation("invoices_rejected_byTousers", fields: [rejected_by], references: [id], onUpdate: NoAction)
  users_invoices_cleared_byTousers  users?         @relation("invoices_cleared_byTousers", fields: [cleared_by], references: [id], onUpdate: NoAction)
  companies                         companies?     @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  containers                        containers?    @relation(fields: [container_id], references: [id], onUpdate: NoAction)
  users_invoices_created_byTousers  users?         @relation("invoices_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_invoices_deleted_byTousers  users?         @relation("invoices_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_invoices_deleted_by_confirmTousers  users?         @relation("invoices_deleted_by_confirmTousers", fields: [deleted_by_confirm], references: [id], onUpdate: NoAction)
  users_invoices_updated_byTousers  users?         @relation("invoices_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model locations {
  id                                  Int                                   @id @default(autoincrement())
  name                                String                                @prisma.VarChar(255)
  time_zone                           String?
  orders                              Int?                                  @default(autoincrement())
  container_loading_cost              Int?
  created_at                          DateTime?                             @default(now())
  created_by                          Int?
  updated_at                          DateTime?                             @updatedAt
  updated_by                          Int?
  deleted_at                          DateTime?                             @prisma.Timestamptz(6)
  deleted_by                          Int?
  users_locations_created_byTousers   users?                                @relation("locations_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_locations_deleted_byTousers   users?                                @relation("locations_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_locations_updated_byTousers   users?                                @relation("locations_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vehicles_at_pol                     vehicles[]                            @relation("pol_locations")
  vessels                             vessels[]
  general_shipping_rates_details      general_shipping_rates_details[]
  shipping_rates                      shipping_rates[]
  company_shipping_rates              company_shipping_rates[]
  yards_location                      yard_locations[]
  terminals                           terminals[]
  loading_cost                        loading_cost[]
  towing_rates                        towing_rates[]
  half_towing_rates                   towing_rates[]                        @relation("half_locations")
  company_loading_cost                company_loading_cost[]
  company_notes                       company_notes[]
  booking_estimations                 booking_estimations[]
  drivers                             drivers[]                             @relation("driver_port")
  loaders                             loaders[]
  special_shipping_rates_locations    special_shipping_rates_locations[]
  shiplines_freight_rates             shiplines_freight_rates[]
  banned_locations_companies          companies[]                           @relation("company_banned_locations")
  mix_shipping_rates                  mix_shipping_rates[]
  shipping_rate_destination_locations shipping_rate_destination_locations[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([name])
  @@index([updated_by])
}

model buyer_numbers {
  id           Int       @id @default(autoincrement())
  buyer_number String    @prisma.VarChar(255)
  company_id   Int?
  created_at   DateTime  @default(now())
  created_by   Int?
  updated_at   DateTime? @updatedAt
  updated_by   Int?
  deleted_at   DateTime?
  deleted_by   Int?

  created_by_user users?     @relation("buyer_numbers_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_by_user users?     @relation("buyer_numbers_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_by_user users?     @relation("buyer_numbers_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)
  company         companies? @relation(fields: [company_id], references: [id], onUpdate: NoAction)

  @@unique([buyer_number, company_id]) // Composite unique constraint
  @@index([buyer_number])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
  @@map("buyer_numbers")
}

model yard_locations {
  id           Int     @id @default(autoincrement())
  name         String  @prisma.VarChar(255)
  location_id  Int?
  emails       Json?
  bio          String?
  address_info String?

  created_at                            DateTime?               @default(now())
  created_by                            Int?
  updated_at                            DateTime?               @updatedAt
  updated_by                            Int?
  deleted_at                            DateTime?               @prisma.Timestamptz(6)
  deleted_by                            Int?
  users_yardLocations_created_byTousers users?                  @relation("yardLocations_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_yardLocations_deleted_byTousers users?                  @relation("yardLocations_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_yardLocations_updated_byTousers users?                  @relation("yardLocations_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  locations                             locations?              @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  vehicles                              vehicles[]
  containers                            containers[]
  // company_loading                       company_loading[]
  companies                             companies[]             @relation("company_loading")
  loginable                             loginables?
  loading_company_rate                  loading_company_rates[]
}

model loading_company_rates {
  id                  Int                         @id @default(autoincrement())
  yard_location_id    Int
  status              loading_company_rate_status
  rate_40hc_4v        Float?
  rate_40hc_3v        Float?
  rate_40hc_halfcut   Float?
  rate_45hc_4v        Float?
  rate_45hc_3v        Float?
  rate_45hc_halfcut   Float?
  effective_date_from DateTime?                   @prisma.Date
  effective_date_to   DateTime?                   @prisma.Date

  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  yard_location yard_locations @relation(fields: [yard_location_id], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

enum loading_company_rate_status {
  pending
  active
  archived
}

model terminals {
  id          Int    @id @default(autoincrement())
  name        String @prisma.VarChar(255)
  location_id Int?

  created_at                        DateTime?  @default(now())
  created_by                        Int?
  updated_at                        DateTime?  @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime?  @prisma.Timestamptz(6)
  deleted_by                        Int?
  vessels                           vessels[]
  locations                         locations? @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  users_terminals_created_byTousers users?     @relation("terminals_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_terminals_deleted_byTousers users?     @relation("terminals_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_terminals_updated_byTousers users?     @relation("terminals_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
}

model loginables {
  id                                 Int                   @id @default(autoincrement())
  username                           String                @unique @prisma.VarChar(64)
  email                              String                @unique @prisma.VarChar(64)
  email_verified_at                  DateTime?             @prisma.Timestamptz(6)
  password                           String                @prisma.VarChar(128)
  status                             user_status           @default(deactive)
  timezone                           String                @default("Asia/Dubai") @prisma.VarChar(32)
  loginable_type                     loginable_type
  user_id                            Int?                  @unique
  customer_id                        Int?                  @unique
  customer_of_customer_id            Int?                  @unique
  loader_id                          Int?                  @unique
  remember_token                     String?               @prisma.VarChar(255)
  created_at                         DateTime?             @default(now())
  created_by                         Int?
  updated_at                         DateTime?             @updatedAt
  updated_by                         Int?
  users_loginables_created_byTousers users?                @relation("loginables_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  customer                           customers?            @relation(fields: [customer_id], references: [id])
  customer_of_customer               customer_of_customer? @relation(fields: [customer_of_customer_id], references: [id])
  users_loginables_updated_byTousers users?                @relation("loginables_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  user                               users?                @relation(fields: [user_id], references: [id])
  yard_locations                     yard_locations?       @relation(fields: [loader_id], references: [id])
  permissions                        permissions[]         @relation("loginablesTopermissions")
  roles                              roles[]               @relation("loginablesToroles")
  login_attempts                     login_attempts[]
  sesssions                          sessions[]

  @@index([loginable_type])
  @@index([user_id])
  @@index([customer_id])
  @@index([created_by])
  @@index([updated_by])
}

model sessions {
  id                     Int        @id @default(autoincrement())
  userId                 Int
  refreshToken           String     @unique
  ipAddress              String
  location               Json
  deviceInfo             Json
  lastActiveAt           DateTime   @default(now())
  createdAt              DateTime   @default(now())
  expiresAt              DateTime
  isActive               Boolean    @default(true)
  is_admin_impersonation Boolean    @default(false)
  admin_id               Int?
  loginables             loginables @relation(fields: [userId], references: [id])
  admin                  users?     @relation("admin_sessions", fields: [admin_id], references: [id])
}

model permissions {
  id         Int          @id @default(autoincrement())
  name       String       @unique @prisma.VarChar(255)
  label      String?      @prisma.VarChar(255)
  group_name String       @prisma.VarChar(255)
  created_at DateTime?    @default(now())
  updated_at DateTime?    @updatedAt
  loginables loginables[] @relation("loginablesTopermissions")
  roles      roles[]      @relation("permissionsToroles")
}

model roles {
  id                            Int           @id @default(autoincrement())
  name                          String        @unique @prisma.VarChar(255)
  created_at                    DateTime?     @default(now())
  created_by                    Int?
  updated_at                    DateTime?     @updatedAt
  updated_by                    Int?
  deleted_at                    DateTime?     @prisma.Timestamptz(6)
  deleted_by                    Int?
  // usersId                       Int?
  users_roles_created_byTousers users?        @relation("roles_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_roles_deleted_byTousers users?        @relation("roles_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_roles_updated_byTousers users?        @relation("roles_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  // users                         users?        @relation(fields: [usersId], references: [id])
  loginables                    loginables[]  @relation("loginablesToroles")
  permissions                   permissions[] @relation("permissionsToroles")
}

model shipping_documents {
  id                                         Int          @id @default(autoincrement())
  shipper_exporter                           String?      @prisma.VarChar(64)
  shipper_street_address                     String?      @prisma.VarChar(64)
  shipper_city                               String?      @prisma.VarChar(64)
  shipper_state                              String?      @prisma.VarChar(64)
  shipper_zip_code                           String?      @prisma.VarChar(64)
  shipper_email_address                      String?      @prisma.VarChar(64)
  shipper_phone_number                       String?      @prisma.VarChar(64)
  shipper_fax_number                         String?      @prisma.VarChar(64)
  shipper_poc                                String?      @prisma.VarChar(64)
  f_agent                                    String?      @prisma.VarChar(64)
  f_street_address                           String?      @prisma.VarChar(64)
  f_city                                     String?      @prisma.VarChar(64)
  f_state                                    String?      @prisma.VarChar(64)
  f_zip_code                                 String?      @prisma.VarChar(64)
  f_email_address                            String?      @prisma.VarChar(64)
  f_phone_number                             String?      @prisma.VarChar(64)
  f_fax_number                               String?      @prisma.VarChar(64)
  f_poc                                      String?      @prisma.VarChar(64)
  active                                     Boolean      @default(false)
  created_at                                 DateTime?    @default(now())
  created_by                                 Int?
  updated_at                                 DateTime?    @updatedAt
  updated_by                                 Int?
  deleted_at                                 DateTime?    @prisma.Timestamptz(6)
  deleted_by                                 Int?
  containers                                 containers[]
  users_shipping_documents_created_byTousers users?       @relation("shipping_documents_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_shipping_documents_deleted_byTousers users?       @relation("shipping_documents_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_shipping_documents_updated_byTousers users?       @relation("shipping_documents_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model suppliers {
  id                                Int       @id @default(autoincrement())
  name                              String    @prisma.VarChar(255)
  phone                             String?   @prisma.VarChar(255)
  email                             String?   @prisma.VarChar(255)
  created_at                        DateTime? @default(now())
  created_by                        Int?
  updated_at                        DateTime? @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime? @prisma.Timestamptz(6)
  deleted_by                        Int?
  users_suppliers_created_byTousers users?    @relation("suppliers_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_suppliers_deleted_byTousers users?    @relation("suppliers_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_suppliers_updated_byTousers users?    @relation("suppliers_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model users {
  id                           Int                      @id @default(autoincrement())
  fullname                     String                   @prisma.VarChar(128)
  photo                        String?                  @prisma.VarChar(255)
  type                         users_types?             @default(user)
  department_id                Int?
  created_at                   DateTime?                @default(now())
  created_by                   Int?
  updated_at                   DateTime?                @updatedAt
  updated_by                   Int?
  deleted_at                   DateTime?                @prisma.Timestamptz(6)
  deleted_by                   Int?
  customer_notifications       customer_notifications[] @relation("sender_id")
  bookings_cancelled_byTousers bookings[]               @relation("bookings_cancelled_byTousers")
  bookings_created_byTousers   bookings[]               @relation("bookings_created_byTousers")
  bookings_deleted_byTousers   bookings[]               @relation("bookings_deleted_byTousers")
  bookings_reviewed_byTousers  bookings[]               @relation("bookings_reviewed_byTousers")
  bookings_updated_byTousers   bookings[]               @relation("bookings_updated_byTousers")
  companies_created_byTousers  companies[]              @relation("companies_created_byTousers")
  companies_deleted_byTousers  companies[]              @relation("companies_deleted_byTousers")
  companies_updated_byTousers  companies[]              @relation("companies_updated_byTousers")

  companyNote_created_byTousers company_notes[] @relation("companyNote_created_byTousers")
  companyNote_deleted_byTousers company_notes[] @relation("companyNote_deleted_byTousers")
  companyNote_updated_byTousers company_notes[] @relation("companyNote_updated_byTousers")

  loadplan_sent_byToUser         containers[]   @relation("loadplan_sent_byToUser")
  arrival_notice_sent_byTousers  containers[]   @relation("arrival_notice_sent_byTousers")
  containers_created_byTousers   containers[]   @relation("containers_created_byTousers")
  containers_deleted_byTousers   containers[]   @relation("containers_deleted_byTousers")
  containers_deleted_by_confirmTousers   containers[]   @relation("containers_deleted_by_confirmTousers")
  containers_updated_byTousers   containers[]   @relation("containers_updated_byTousers")
  customers_created_byTousers    customers[]    @relation("customers_created_byTousers")
  customers_deleted_byTousers    customers[]    @relation("customers_deleted_byTousers")
  customers_updated_byTousers    customers[]    @relation("customers_updated_byTousers")
  destinations_created_byTousers destinations[] @relation("destinations_created_byTousers")
  destinations_deleted_byTousers destinations[] @relation("destinations_deleted_byTousers")
  destinations_updated_byTousers destinations[] @relation("destinations_updated_byTousers")

  invoices_rejected_byTousers invoices[]  @relation("invoices_rejected_byTousers")
  invoices_checked_byTousers  invoices[]  @relation("invoices_checked_byTousers")
  invoices_cleared_byTousers  invoices[]  @relation("invoices_cleared_byTousers")
  invoices_created_byTousers  invoices[]  @relation("invoices_created_byTousers")
  invoices_deleted_byTousers  invoices[]  @relation("invoices_deleted_byTousers")
  invoices_deleted_by_confirmTousers  invoices[]  @relation("invoices_deleted_by_confirmTousers")
  invoices_updated_byTousers  invoices[]  @relation("invoices_updated_byTousers")
  locations_created_byTousers locations[] @relation("locations_created_byTousers")
  locations_deleted_byTousers locations[] @relation("locations_deleted_byTousers")
  locations_updated_byTousers locations[] @relation("locations_updated_byTousers")

  column_settings    column_settings[]
  user_notifications user_notifications[]

  yardLocations_created_byTousers yard_locations[] @relation("yardLocations_created_byTousers")
  yardLocations_deleted_byTousers yard_locations[] @relation("yardLocations_deleted_byTousers")
  yardLocations_updated_byTousers yard_locations[] @relation("yardLocations_updated_byTousers")

  terminals_created_byTousers terminals[] @relation("terminals_created_byTousers")
  terminals_deleted_byTousers terminals[] @relation("terminals_deleted_byTousers")
  terminals_updated_byTousers terminals[] @relation("terminals_updated_byTousers")

  loginables_created_byTousers         loginables[]         @relation("loginables_created_byTousers")
  loginables_updated_byTousers         loginables[]         @relation("loginables_updated_byTousers")
  loginable                            loginables?
  roles_created_byTousers              roles[]              @relation("roles_created_byTousers")
  roles_deleted_byTousers              roles[]              @relation("roles_deleted_byTousers")
  roles_updated_byTousers              roles[]              @relation("roles_updated_byTousers")
  // roles                                roles[]
  shipping_documents_created_byTousers shipping_documents[] @relation("shipping_documents_created_byTousers")
  shipping_documents_deleted_byTousers shipping_documents[] @relation("shipping_documents_deleted_byTousers")
  shipping_documents_updated_byTousers shipping_documents[] @relation("shipping_documents_updated_byTousers")

  steamshiplines_created_byTousers             steamshiplines[]              @relation("steamshiplines_created_byTousers")
  steamshiplines_deleted_byTousers             steamshiplines[]              @relation("steamshiplines_deleted_byTousers")
  steamshiplines_updated_byTousers             steamshiplines[]              @relation("steamshiplines_updated_byTousers")
  suppliers_created_byTousers                  suppliers[]                   @relation("suppliers_created_byTousers")
  suppliers_deleted_byTousers                  suppliers[]                   @relation("suppliers_deleted_byTousers")
  suppliers_updated_byTousers                  suppliers[]                   @relation("suppliers_updated_byTousers")
  users_created_byTousers                      users?                        @relation("users_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  other_users_created_byTousers                users[]                       @relation("users_created_byTousers")
  users_deleted_byTousers                      users?                        @relation("users_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  other_users_deleted_byTousers                users[]                       @relation("users_deleted_byTousers")
  users_updated_byTousers                      users?                        @relation("users_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  other_users_updated_byTousers                users[]                       @relation("users_updated_byTousers")
  vehicle_costs_created_byTousers              vehicle_costs[]               @relation("vehicle_costs_created_byTousers")
  vehicle_costs_deleted_byTousers              vehicle_costs[]               @relation("vehicle_costs_deleted_byTousers")
  vehicle_costs_updated_byTousers              vehicle_costs[]               @relation("vehicle_costs_updated_byTousers")
  vehicle_storage_attachment_created_byTousers vehicle_storage_attachemnts[] @relation("vehicle_storage_attachemnts_created_byTousers")
  vehicle_storage_attachment_deleted_byTousers vehicle_storage_attachemnts[] @relation("vehicle_storage_attachemnts_deleted_byTousers")
  vehicle_storages_created_byTousers           vehicle_storages[]            @relation("vehicle_storages_created_byTousers")
  vehicle_towings_created_byTousers            vehicle_towings[]             @relation("vehicle_towings_created_byTousers")
  vehicle_towings_deleted_byTousers            vehicle_towings[]             @relation("vehicle_towings_deleted_byTousers")
  vehicle_towings_updated_byTousers            vehicle_towings[]             @relation("vehicle_towings_updated_byTousers")
  vehicles_created_byTousers                   vehicles[]                    @relation("vehicles_created_byTousers")
  vehicles_deleted_byTousers                   vehicles[]                    @relation("vehicles_deleted_byTousers")
  vehicles_deleted_by_confirmTousers           vehicles[]                    @relation("vehicles_deleted_by_confirmTousers")
  vehicles_gate_passed_byTousers               vehicles[]                    @relation("vehicles_gate_passed_byTousers")
  vehicles_updated_byTousers                   vehicles[]                    @relation("vehicles_updated_byTousers")
  vessels_cancelled_byTousers                  vessels[]                     @relation("vessels_cancelled_byTousers")
  vessels_created_byTousers                    vessels[]                     @relation("vessels_created_byTousers")
  vessels_deleted_byTousers                    vessels[]                     @relation("vessels_deleted_byTousers")
  vessels_reviewed_byTousers                   vessels[]                     @relation("vessels_reviewed_byTousers")
  vessels_updated_byTousers                    vessels[]                     @relation("vessels_updated_byTousers")
  profile_updated_byTousers                    pgl_profile[]                 @relation("profile_updated_byTousers")
  profile_created_byTousers                    pgl_profile[]                 @relation("profile_created_byTousers")

  loading_cities_created_by loading_cities[] @relation("created_byTousers")
  loading_cities_updated_by loading_cities[] @relation("updated_byTousers")
  loading_cities_deleted_by loading_cities[] @relation("deleted_byTousers")

  loading_cost_created_by loading_cost[] @relation("created_byTousers")
  loading_cost_updated_by loading_cost[] @relation("updated_byTousers")
  loading_cost_deleted_by loading_cost[] @relation("deleted_byTousers")

  towing_rates_created_by towing_rates[] @relation("created_byTousers")
  towing_rates_updated_by towing_rates[] @relation("updated_byTousers")
  towing_rates_deleted_by towing_rates[] @relation("deleted_byTousers")

  company_loading_cost_created_by company_loading_cost[] @relation("created_byTousers")
  company_loading_cost_updated_by company_loading_cost[] @relation("updated_byTousers")
  company_loading_cost_deleted_by company_loading_cost[] @relation("deleted_byTousers")

  loading_states_created_by loading_states[] @relation("loading_states_created_by")
  loading_states_updated_by loading_states[] @relation("loading_states_updated_by")
  loading_states_deleted_by loading_states[] @relation("loading_states_deleted_by")

  vehicle_images_created_by vehicle_images[] @relation("vehicle_images_created_by")
  vehicle_images_updated_by vehicle_images[] @relation("vehicle_images_updated_by")
  vehicle_images_deleted_by vehicle_images[] @relation("vehicle_images_deleted_by")

  vehicle_auction_images_created_by vehicle_auction_images[] @relation("vehicle_auction_images_created_by")
  vehicle_auction_images_updated_by vehicle_auction_images[] @relation("vehicle_auction_images_updated_by")
  vehicle_auction_images_deleted_by vehicle_auction_images[] @relation("vehicle_auction_images_deleted_by")

  container_images_created_by containers_images[] @relation("container_images_created_by")
  container_images_updated_by containers_images[] @relation("container_images_updated_by")
  container_images_deleted_by containers_images[] @relation("container_images_deleted_by")

  mixContainerCreatedByUser mix_shipping_containers[] @relation("mixContainerCreatedByUser")
  mixContainerUpdatedByUser mix_shipping_containers[] @relation("mixContainerUpdatedByUser")
  mixContainerDeletedByUser mix_shipping_containers[] @relation("mixContainerDeletedByUser")

  mixInvoiceCreatedByUser mix_shipping_invoices[] @relation("mixInvoiceCreatedByUser")
  mixInvoiceUpdatedByUser mix_shipping_invoices[] @relation("mixInvoiceUpdatedByUser")
  mixInvoiceDeletedByUser mix_shipping_invoices[] @relation("mixInvoiceDeletedByUser")

  mixVehicleCreatedByUser mix_shipping_vehicles[] @relation("mixVehicleCreatedByUser")
  mixVehicleUpdatedByUser mix_shipping_vehicles[] @relation("mixVehicleUpdatedByUser")

  mixVehicleChargesCreatedByUser mix_shipping_vehicle_charges[] @relation("mixVehicleChargesCreatedByUser")
  mixVehicleChargesUpdatedByUser mix_shipping_vehicle_charges[] @relation("mixVehicleChargesUpdatedByUser")
  mixVehicleChargesDeletedByUser mix_shipping_vehicle_charges[] @relation("mixVehicleChargesDeletedByUser")

  pglUsedCarsCreatedByUser pgl_used_cars[] @relation("pgl_used_cars_ibfk_3")
  pglUsedCarsUpdatedByUser pgl_used_cars[] @relation("pgl_used_cars_ibfk_4")
  pglUsedCarsDeletedByUser pgl_used_cars[] @relation("pgl_used_cars_ibfk_5")
  pglUsedCarsVCCApplyer    pgl_used_cars[] @relation("user_id_for_applyvcc")
  pglUsedCarsGatePasser    pgl_used_cars[] @relation("users_for_gatepasser")

  usedCarsYardCreatedByUser pgl_used_cars_yards[] @relation("pgl_used_cars_yards_ibfk_1")
  usedCarsYardUpdatedByUser pgl_used_cars_yards[] @relation("pgl_used_cars_yards_ibfk_2")
  usedCarsYardDeletedByUser pgl_used_cars_yards[] @relation("pgl_used_cars_yards_ibfk_3")

  yardInventoryCreatedByUser yard_inventories[] @relation("yard_inventories_ibfk_2")
  yardInventoryUpdatedByUser yard_inventories[] @relation("yard_inventories_ibfk_3")
  yardInventoryDeletedByUser yard_inventories[] @relation("yard_inventories_ibfk_4")

  announcements_created_by announcements[] @relation("announcements_created_byTousers")
  announcements_updated_by announcements[] @relation("announcements_updated_byTousers")
  announcements_deleted_by announcements[] @relation("announcements_deleted_byTousers")

  shipping_rates_send_byTousers shipping_rates[] @relation("shipping_rates_send_byTousers")
  shipping_rates_created_by     shipping_rates[] @relation("shipping_rates_created_byTousers")
  shipping_rates_updated_by     shipping_rates[] @relation("shipping_rates_updated_byTousers")
  shipping_rates_deleted_by     shipping_rates[] @relation("shipping_rates_deleted_byTousers")

  general_shipping_rates_send_by    general_shipping_rates[] @relation("general_shipping_rates_send_by")
  general_shipping_rates_created_by general_shipping_rates[] @relation("general_shipping_rates_created_by")
  general_shipping_rates_updated_by general_shipping_rates[] @relation("general_shipping_rates_updated_by")
  general_shipping_rates_deleted_by general_shipping_rates[] @relation("general_shipping_rates_deleted_by")

  general_rate_detail_created_by general_shipping_rates_details[] @relation("general_rate_detail_created_by")
  general_rate_detail_updated_by general_shipping_rates_details[] @relation("general_rate_detail_updated_by")
  general_rate_detail_deleted_by general_shipping_rates_details[] @relation("general_rate_detail_deleted_by")

  discount_rate_created_by company_shipping_rates[] @relation("discount_rate_created_byTousers")
  discount_rate_updated_by company_shipping_rates[] @relation("discount_rate_updated_byTousers")
  discount_rate_deleted_by company_shipping_rates[] @relation("discount_rate_deleted_byTousers")

  special_rate_created_by special_shipping_rates[] @relation("special_rate_created_byTousers")
  special_rate_updated_by special_shipping_rates[] @relation("special_rate_updated_byTousers")
  special_rate_deleted_by special_shipping_rates[] @relation("special_rate_deleted_byTousers")

  special_rate_location_created_by special_shipping_rates_locations[] @relation("special_rate_location_created_byTousers")
  special_rate_location_updated_by special_shipping_rates_locations[] @relation("special_rate_location_updated_byTousers")
  special_rate_location_deleted_by special_shipping_rates_locations[] @relation("special_rate_location_deleted_byTousers")

  pglUsedCarCostsCreatedByUser pgl_used_car_costs[] @relation("pgl_used_car_costs_ibfk_2")
  pglUsedCarCostsUpdatedByUser pgl_used_car_costs[] @relation("pgl_used_car_costs_ibfk_3")
  pglUsedCarCostsDeletedByUser pgl_used_car_costs[] @relation("pgl_used_car_costs_ibfk_4")

  containerCostsCreatedByUser container_costs[] @relation("containerCostsCreatedByUser")
  containerCostsUpdatedByUser container_costs[] @relation("containerCostsUpdatedByUser")
  containerCostsDeletedByUser container_costs[] @relation("containerCostsDeletedByUser")

  deliveryChargeInvoiceCreatedByUser delivery_charge_invoice[] @relation("deliveryChargeInvoiceCreatedByUser")
  deliveryChargeInvoiceUpdatedByUser delivery_charge_invoice[] @relation("deliveryChargeInvoiceUpdatedByUser")
  deliveryChargeInvoiceDeletedByUser delivery_charge_invoice[] @relation("deliveryChargeInvoiceDeletedByUser")

  clearLogsInvoiceCreatedByUser clear_logs[] @relation("clearLogsInvoiceCreatedByUser")
  clearLogsInvoiceUpdatedByUser clear_logs[] @relation("clearLogsInvoiceUpdatedByUser")
  clearLogsInvoiceDeletedByUser clear_logs[] @relation("clearLogsInvoiceDeletedByUser")

  clearLogsVehicleCreatedByUser clear_log_vehicles[] @relation("clearLogsVehicleCreatedByUser")
  clearLogsVehicleUpdatedByUser clear_log_vehicles[] @relation("clearLogsVehicleUpdatedByUser")
  clearLogsVehicleDeletedByUser clear_log_vehicles[] @relation("clearLogsVehicleDeletedByUser")

  logInvoicesCreatedByUser log_invoices[] @relation("logInvoicesCreatedByUser")
  logInvoicesUpdatedByUser log_invoices[] @relation("logInvoicesUpdatedByUser")
  logInvoicesDeletedByUser log_invoices[] @relation("logInvoicesDeletedByUser")

  singleVccCreatedByUser single_vcc[] @relation("singleVccCreatedByUser")
  singleVccUpdatedByUser single_vcc[] @relation("singleVccUpdatedByUser")
  singleVccDeletedByUser single_vcc[] @relation("singleVccDeletedByUser")

  exitClaimChargeCreatedByUser exit_claim_charge[] @relation("exitClaimChargeCreatedByUser")
  exitClaimChargeUpdatedByUser exit_claim_charge[] @relation("exitClaimChargeUpdatedByUser")
  exitClaimChargeDeletedByUser exit_claim_charge[] @relation("exitClaimChargeDeletedByUser")

  detentionChargeCreatedByUser detention_charge[] @relation("detentionChargeCreatedByUser")
  detentionChargeUpdatedByUser detention_charge[] @relation("detentionChargeUpdatedByUser")
  detentionChargeDeletedByUser detention_charge[] @relation("detentionChargeDeletedByUser")

  clearanceCombineBookingCreatedByUser clearance_combine_booking_invocies[] @relation("clearanceCombineBookingCreatedByUser")
  clearanceCombineBookingUpdatedByUser clearance_combine_booking_invocies[] @relation("clearanceCombineBookingUpdatedByUser")
  clearanceCombineBookingDeletedByUser clearance_combine_booking_invocies[] @relation("clearanceCombineBookingDeletedByUser")

  news_created_byTousers news[] @relation("news_created_byTousers")
  news_updated_byTousers news[] @relation("news_updated_byTousers")
  news_deleted_byTousers news[] @relation("news_deleted_byTousers")

  department_created_byTousers departments[] @relation("department_created_byTousers")
  department_updated_byTousers departments[] @relation("department_updated_byTousers")
  department_deleted_byTousers departments[] @relation("department_deleted_byTousers")
  departments                  departments?  @relation(fields: [department_id], references: [id], onUpdate: NoAction)

  transactions_created_byTousers        transactions[] @relation("transactions_created_byTousers")
  transactions_updated_byTousers        transactions[] @relation("transactions_updated_byTousers")
  transactions_deleted_byTousers        transactions[] @relation("transactions_deleted_byTousers")
  transactions_cancelled_byTousers      transactions[] @relation("transactions_cancelled_byTousers")
  transactions_double_checked_byTousers transactions[] @relation("transactions_double_checked_byTousers")

  transfers_created_byTousers transfers[] @relation("transfers_created_byTousers")
  transfers_updated_byTousers transfers[] @relation("transfers_updated_byTousers")
  transfers_deleted_byTousers transfers[] @relation("transfers_deleted_byTousers")

  transaction_container_vehicle_transaction_container_vehicle_created_byTousers transaction_container_vehicle[] @relation("transaction_container_vehicle_created_byTousers")
  transaction_container_vehicle_transaction_container_vehicle_deleted_byTousers transaction_container_vehicle[] @relation("transaction_container_vehicle_deleted_byTousers")

  reminders reminders[] @relation("receiver_id")

  categories categories[]

  categories_created_byTousers categories[] @relation("categories_created_byTousers")
  categories_updated_byTousers categories[] @relation("categories_updated_byTousers")
  categories_deleted_byTousers categories[] @relation("categories_deleted_byTousers")

  accounts_created_byTousers accounts[] @relation("accounts_created_byTousers")
  accounts_updated_byTousers accounts[] @relation("accounts_updated_byTousers")
  accounts_deleted_byTousers accounts[] @relation("accounts_deleted_byTousers")

  driversCreatedByUser drivers[] @relation("driversCreatedByUser")
  driversUpdatedByUser drivers[] @relation("driversUpdatedByUser")
  driversDeletedByUser drivers[] @relation("driversDeletedByUser")

  freightRatesCreatedByUser shiplines_booking_freight_rates[] @relation("freightRatesCreatedByUser")
  freightRatesUpdatedByUser shiplines_booking_freight_rates[] @relation("freightRatesUpdatedByUser")
  freightRatesDeletedByUser shiplines_booking_freight_rates[] @relation("freightRatesDeletedByUser")

  loadersCreatedByUser         loaders[]                 @relation("loadersCreatedByUser")
  loadersUpdatedByUser         loaders[]                 @relation("loadersUpdatedByUser")
  loadersDeletedByUser         loaders[]                 @relation("loadersDeletedByUser")
  amountsPerMoveCreatedByUser  driver_amounts_per_move[] @relation("amountsPerMoveCreatedByUser")
  amountsPerMoveUpdatedByUser  driver_amounts_per_move[] @relation("amountsPerMoveUpdatedByUser")
  amountsPerMoveDeletedByUser  driver_amounts_per_move[] @relation("amountsPerMoveDeletedByUser")
  driverDocumentsCreatedByUser driver_documents[]        @relation("driverDocumentsCreatedByUser")
  driverPaymentsCreatedByUser  driver_payments[]         @relation("driverPaymentsCreatedByUser")
  driverPaymentsUpdatedByUser  driver_payments[]         @relation("driverPaymentsUpdatedByUser")
  loaderDocumentsCreatedByUser loader_documents[]        @relation("loaderDocumentsCreatedByUser")

  vehicles_transaction_byTousers          vehicles[]                           @relation("vehicles_transaction_byTousers")
  containers_transaction_byTousers        containers[]                         @relation("containers_transaction_byTousers")
  users_vessels_targets_created_byTousers vessel_destination_booking_targets[] @relation("users_vessels_targets_created_byTousers")

  exit_papers_created_byTousers exit_papers[] @relation("exit_papers_created_byTousers")
  exit_papers_updated_byTousers exit_papers[] @relation("exit_papers_updated_byTousers")
  exit_papers_deleted_byTousers exit_papers[] @relation("exit_papers_deleted_byTousers")

  googleCredentials user_google_credentials[]

  loading_equipments_created_by loading_equipments[] @relation("loading_equipments_created_byTousers")
  loading_equipments_updated_by loading_equipments[] @relation("loading_equipments_updated_byTousers")
  loading_equipments_deleted_by loading_equipments[] @relation("loading_equipments_deleted_byTousers")

  vehicle_damages_created_by        vehicle_damages[] @relation("vehicle_damages_created_by_users")
  vehicle_damages_deleted_by        vehicle_damages[] @relation("vehicle_damages_deleted_by_users")
  vehicle_damages_updated_by        vehicle_damages[] @relation("vehicle_damages_updated_by_users")
  vehicle_damages_credited_by       vehicle_damages[] @relation("vehicle_damages_credited_by_users")
  vehicle_damages_audit_reviewed_by vehicle_damages[] @relation("vehicle_damages_audit_reviewed_by_users")

  vehicle_damages_images_created_by vehicle_damages_images[] @relation("vehicle_damages_images_created_by")
  vehicle_damages_images_updated_by vehicle_damages_images[] @relation("vehicle_damages_images_updated_by")
  vehicle_damages_images_deleted_by vehicle_damages_images[] @relation("vehicle_damages_images_deleted_by")

  mix_shipping_rates_created_by  mix_shipping_rates[]             @relation("mix_shipping_rates_created_by")
  mix_shipping_rates_updated_by  mix_shipping_rates[]             @relation("mix_shipping_rates_updated_by")
  mix_shipping_rates_deleted_by  mix_shipping_rates[]             @relation("mix_shipping_rates_deleted_by")
  general_rate_created_byTousers company_general_shipping_rates[] @relation("general_rate_created_byTousers")
  general_rate_deleted_byTousers company_general_shipping_rates[] @relation("general_rate_deleted_byTousers")

  customer_payment_transaction_created_by customer_payment_transactions[] @relation("customer_payment_transaction_created_byTousers")
  customer_payment_transaction_updated_by customer_payment_transactions[] @relation("customer_payment_transaction_updated_byTousers")
  customer_payment_transaction_deleted_by customer_payment_transactions[] @relation("customer_payment_transaction_deleted_byTousers")

  shipping_rate_last_email_sent_byTousers shipping_rates_2[] @relation("shipping_rate_last_email_sent_byTousers")
  shipping_rate_created_byTousers         shipping_rates_2[] @relation("shipping_rate_created_byTousers")
  shipping_rate_updated_byTousers         shipping_rates_2[] @relation("shipping_rate_updated_byTousers")
  shipping_rate_deletedByTousers          shipping_rates_2[] @relation("shipping_rate_deleted_byTousers")

  shipping_rate_rate_created_byTousers shipping_rate_destination_location_shiplines[] @relation("shipping_rate_rate_created_byTousers")
  shipping_rate_rate_updated_byTousers shipping_rate_destination_location_shiplines[] @relation("shipping_rate_rate_updated_byTousers")
  shipping_rate_rate_deletedByTousers  shipping_rate_destination_location_shiplines[] @relation("shipping_rate_rate_deleted_byTousers")

  holiday_created_by holidays[] @relation("holiday_created_by_users")
  holiday_deleted_by holidays[] @relation("holiday_deleted_by_users")
  holiday_updated_by holidays[] @relation("holiday_updated_by_users")

  exchange_rate_created_by exchange_rates[] @relation("exchange_rate_created_byTousers")
  exchange_rate_updated_by exchange_rates[] @relation("exchange_rate_updated_byTousers")
  exchange_rate_deleted_by exchange_rates[] @relation("exchange_rate_deleted_byTousers")

  payments_created_by payments[] @relation("payments_created_byTousers")
  payments_updated_by payments[] @relation("payments_updated_byTousers")
  payments_deleted_by payments[] @relation("payments_deleted_byTousers")

  payment_allocations_created_by payment_allocations[] @relation("payment_allocations_created_byTousers")
  payment_allocations_updated_by payment_allocations[] @relation("payment_allocations_updated_byTousers")
  payment_allocations_deleted_by payment_allocations[] @relation("payment_allocations_deleted_byTousers")

  kv_store_created_byTousers kv_store[] @relation("kv_store_created_byTousers")
  kv_store_updated_byTousers kv_store[] @relation("kv_store_updated_byTousers")
  kv_store_deleted_byTousers kv_store[] @relation("kv_store_deleted_byTousers")

  bill_loading_created_by      bill_of_loadings[]  @relation("billOfLoadingsCreatedByUser")
  bill_loading_deleted_by      bill_of_loadings[]  @relation("billOfLoadingsDeletedByUser")
  bill_loading_updated_by      bill_of_loadings[]  @relation("billOfLoadingsUpdatedByUser")
  bill_loading_checked_by      bill_of_loadings[]  @relation("billOfLoadingsCheckedByUser")
  dispute_bookings_created_by  dispute_bookings[]  @relation("disputeBookingsCreatedByUser")
  dispute_bookings_updated_by  dispute_bookings[]  @relation("disputeBookingsUpdatedByUser")
  dispute_bookings_deleted_by  dispute_bookings[]  @relation("disputeBookingsDeletedByUser")
  dispute_follow_up_created_by dispute_follow_up[] @relation("followUpCreatedByUser")
  dispute_follow_up_updated_by dispute_follow_up[] @relation("followUpUpdatedByUser")
  dispute_follow_up_deleted_by dispute_follow_up[] @relation("followUpDeletedByUser")

  created_buyer_numbers buyer_numbers[] @relation("buyer_numbers_created_by")
  updated_buyer_numbers buyer_numbers[] @relation("buyer_numbers_updated_by")
  deleted_buyer_numbers buyer_numbers[] @relation("buyer_numbers_deleted_by")

  payment_cards_created_by payment_cards[] @relation("payment_cards_created_byTousers")
  payment_cards_updated_by payment_cards[] @relation("payment_cards_updated_byTousers")
  payment_cards_deleted_by payment_cards[] @relation("payment_cards_deleted_byTousers")

  bank_accounts_created_by bank_accounts[] @relation("bankAccountCreatedByUser")
  bank_accounts_updated_by bank_accounts[] @relation("bankAccountUpdatedByUser")
  bank_accounts_deleted_by bank_accounts[] @relation("bankAccountDeletedByUser")

  customer_transaction_bank_details_created_by customer_transaction_bank_details[] @relation("bankDetailsCreatedByUser")
  customer_transaction_bank_details_updated_by customer_transaction_bank_details[] @relation("bankDetailsUpdatedByUser")
  customer_transaction_bank_details_deleted_by customer_transaction_bank_details[] @relation("bankDetailsDeletedByUser")

  profit_loss_sheet_created_by profit_loss_sheet[] @relation("PLSheetCreatedByUser")
  profit_loss_sheet_updated_by profit_loss_sheet[] @relation("PLSheetUpdatedByUser")
  profit_loss_sheet_deleted_by profit_loss_sheet[] @relation("PLSheetDeletedByUser")
  admin_sessions               sessions[]          @relation("admin_sessions")

  charges_created_by charges[] @relation("charges_created_byTousers")
  charges_deleted_by charges[] @relation("charges_updated_byTousers")
  charges_updated_by charges[] @relation("charges_deleted_byTousers")

  company_charges_created_by company_charges_v2[] @relation("company_charges_v2_created_byTousers")
  company_charges_updated_by company_charges_v2[] @relation("company_charges_v2_updated_byTousers")
  company_charges_deleted_by company_charges_v2[] @relation("company_charges__v2_deleted_byTousers")

  company_charges_pivot_created_by    company_charges_v2_pivot[] @relation("company_charges_v2_pivot_created_byTousers")
  company_charges_v2_pivot_updated_by company_charges_v2_pivot[] @relation("company_charges_v2_pivot_updated_byTousers")
  company_charges_v2_pivot_deleted_by company_charges_v2_pivot[] @relation("company_charges_v2_pivot_deleted_byTousers")

  // Back-relations for company_consignees
  consigneesCreated                       company_consignees[] @relation("ConsigneeCreatedBy")
  consigneesUpdated                       company_consignees[] @relation("ConsigneeUpdatedBy")
  consigneesDeleted                       company_consignees[] @relation("ConsigneeDeletedBy")
  // Vehicle Dispatches  relationships
  vehicle_dispatches_customer_informed_by vehicle_dispatches[] @relation("vehicle_dispatches_customer_informed_by")
  vehicle_dispatches_customer_approved_by vehicle_dispatches[] @relation("vehicle_dispatches_customer_approved_by")
  vehicle_dispatches_list_on_cd_by        vehicle_dispatches[] @relation("vehicle_dispatches_list_on_cd_by")
  vehicle_dispatches_driver_informed_by   vehicle_dispatches[] @relation("vehicle_dispatches_driver_informed_by")
  vehicle_dispatches_created_by           vehicle_dispatches[] @relation("vehicle_dispatches_created_by")
  vehicle_dispatches_updated_by           vehicle_dispatches[] @relation("vehicle_dispatches_updated_by")
  vehicle_dispatches_deleted_by           vehicle_dispatches[] @relation("vehicle_dispatches_deleted_by")

  // Vehicle comments relationships
  vehicle_comments_created_by vehicle_comments[] @relation("vehicle_comments_created_byTousers")
  vehicle_comments_updated_by vehicle_comments[] @relation("vehicle_comments_updated_byTousers")
  vehicle_comments_deleted_by vehicle_comments[] @relation("vehicle_comments_deleted_byTousers")

  @@index([created_by])
  @@index([deleted_by])
  @@index([fullname])
  @@index([updated_by])
}

model user_google_credentials {
  id      Int  @id @default(autoincrement())
  user_id Int?

  access_token  String
  refresh_token String
  scope         String
  token_type    String
  expiry_date   BigInt

  created_at DateTime? @default(now())
  user       users?    @relation(fields: [user_id], references: [id], onUpdate: NoAction, onDelete: Cascade)
}

model old_users {
  id           Int     @id @default(autoincrement())
  user_type_id Int?    @default(2)
  username     String  @prisma.VarChar(64)
  photo        String? @prisma.VarChar(255)
  email        String  @prisma.VarChar(64)
  password     String  @prisma.VarChar(128)
  status       String? @default("active")
  timezone     String  @default("Asia/Dubai") @prisma.VarChar(32)

  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  users_created_byTousers       old_users?  @relation("users_ibfk_1", fields: [created_by], references: [id], onUpdate: NoAction)
  other_users_created_byTousers old_users[] @relation("users_ibfk_1")
  users_updated_byTousers       old_users?  @relation("users_ibfk_2", fields: [updated_by], references: [id], onUpdate: NoAction)
  other_users_updated_byTousers old_users[] @relation("users_ibfk_2")
  users_deleted_byTousers       old_users?  @relation("users_ibfk_3", fields: [deleted_by], references: [id], onUpdate: NoAction)
  other_users_deleted_byTousers old_users[] @relation("users_ibfk_3")
}

model vehicles {
  id                              Int                                @id @default(autoincrement())
  vin                             String?                            @unique @prisma.VarChar(255)
  lot_number                      String?                            @prisma.VarChar(255)
  year                            String?                            @prisma.VarChar(128)
  make                            String?                            @prisma.VarChar(255)
  model                           String?                            @prisma.VarChar(255)
  customer_id                     Int?
  container_id                    Int?
  company_id                      Int?
  gate_passed_by                  Int?
  gate_passed_at                  DateTime?                          @prisma.Timestamptz(6)
  color                           String?                            @prisma.VarChar(128)
  weight                          String?                            @prisma.VarChar(255)
  price                           Int?                               @default(0)
  title_number                    String?                            @prisma.VarChar(255)
  title_state                     String?                            @prisma.VarChar(255)
  title_status                    String?                            @prisma.VarChar(255)
  hat_number                      String?                            @prisma.VarChar(255)
  yard_location_id                Int?
  yard_location                   String?
  yard_id                         Int?
  customer_remark                 String?
  auction_remark                  String?
  carstate                        carstate                           @default(on_the_way)
  isPendingTrash                  Boolean?                           @default(false)
  status_changed_at               DateTime?
  pickup_status                   pickup_status?
  halfcut_status                  vehicles_halfcut_status_enum?
  is_scrap                        Boolean?                           @default(false)
  load_status                     vehicles_load_status_enum?
  loading_company_halfcut_status  vehicles_load_halfcut_status_enum?
  tax_status                      vehicles_tax_status_enum           @default(No)
  tax_amount                      Int?
  tax_document_link               String?
  payment_date                    DateTime?                          @prisma.Date
  payment_date_to_pgl             DateTime?                          @prisma.Date
  point_of_loading                Int?
  buyer_number                    String?                            @prisma.VarChar(255)
  vehicle_document_link           String?                            @prisma.VarChar(255)
  auction_invoice                 String?
  photo_link                      String?
  is_key_present                  Boolean?
  vehicle_description             String?                            @prisma.VarChar(128)
  note                            String?
  auction_name                    String?                            @prisma.VarChar(255)
  auction_city                    String?                            @prisma.VarChar(255)
  auction_city_id                 Int?
  date_posted_in_central_dispatch DateTime?                          @prisma.Date
  posted_by_in_central_dispatch   String?                            @prisma.VarChar(128)
  pickup_due_date                 DateTime?                          @prisma.Date
  auction_photos_link             String?
  purchased_at                    DateTime?                          @prisma.Date
  pickup_date                     DateTime?                          @prisma.Date
  deliver_date                    DateTime?                          @prisma.Date
  // this is for storage_cost reports
  request_for_pickup_date         DateTime?                          @prisma.Date
  ready_for_pickup_date           DateTime?                          @prisma.Date
  moved_to_paid_date              DateTime?                          @prisma.Date
  driver_appointment_date         DateTime?                          @prisma.Date
  pickup_remark                   String?
  ready_for_pickup_date_editable  Boolean?

  ////New Fields///
  account_owner             account_owner_status?    @default(null)
  title_status_step_two     String?                  @prisma.VarChar(255)
  last_title_follow_up_date DateTime?                @default(now()) @prisma.Date
  title_receive_date        DateTime?                @default(now()) @prisma.Date
  ship_as                   ship_as?
  destination_id            Int?
  fuel_type                 fuel_type_enum?
  vehicle_size              vehicle_size_enum?
  is_title_exist            Boolean?                 @default(false)
  check_no                  String?
  dispatch_remark           String?
  cover_photo               String?
  title_delivery_location   String?
  trn                       String?
  storage_remark            String?
  //////
  created_at                DateTime?                @default(now())
  created_by                Int?
  updated_at                DateTime?                @updatedAt
  updated_by                Int?
  updated_by_customer       Int?
  deleted_at                DateTime?                @prisma.Timestamptz(6)
  deleted_by                Int?
  deleted_by_confirm        Int?
  deleted_reason            String?
  exit_papers_vehicles      exit_papers_vehicles[]
  vehicle_costs             vehicle_costs?
  vehicle_towings           vehicle_towings?
  vehicle_images            vehicle_images[]
  vehicle_auction_images    vehicle_auction_images[]
  loading_city_id           Int?
  loading_cities            loading_cities?          @relation(fields: [loading_city_id], references: [id], onUpdate: NoAction)
  auction_cities            loading_cities?          @relation("AuctionCity", fields: [auction_city_id], references: [id], onUpdate: NoAction)
  companies                 companies?               @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  containers                containers?              @relation(fields: [container_id], references: [id], onUpdate: NoAction)
  yards_location            yard_locations?          @relation(fields: [yard_location_id], references: [id], onUpdate: NoAction)
  yard                      pgl_used_cars_yards?     @relation("yard_for_vehicles", fields: [yard_id], references: [id], onUpdate: NoAction)
  destinations              destinations?            @relation("destination_id", fields: [destination_id], references: [id], onUpdate: NoAction)

  users_vehicles_created_byTousers         users?                  @relation("vehicles_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  customers                                customers?              @relation(fields: [customer_id], references: [id], onUpdate: NoAction)
  users_vehicles_deleted_byTousers         users?                  @relation("vehicles_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_vehicles_deleted_by_confirmTousers users?                  @relation("vehicles_deleted_by_confirmTousers", fields: [deleted_by_confirm], references: [id], onUpdate: NoAction)
  users_vehicles_gate_passed_byTousers     users?                  @relation("vehicles_gate_passed_byTousers", fields: [gate_passed_by], references: [id], onUpdate: NoAction)
  pol_locations                            locations?              @relation("pol_locations", fields: [point_of_loading], references: [id])
  users_vehicles_updated_byTousers         users?                  @relation("vehicles_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  customer_vehicles_updated_by             customers?              @relation("vehicles_updated_byToCustomer", fields: [updated_by_customer], references: [id], onUpdate: NoAction)
  mix_shipping_vehicles                    mix_shipping_vehicles[]
  pgl_used_cars                            pgl_used_cars[]         @relation("pgl_used_cars_ibfk_2")
  yard_inventories                         yard_inventories[]      @relation("yard_inventories_ibfk_5")
  trackings                                trackings[]             @relation("tracking_vehicles")
  ////New Cost Fields////
  storage_cost                             Int?                    @default(0)
  title_cost                               Int?                    @default(0)
  dismantle_costs                          Int?                    @default(0)
  damage_cost                              Int?                    @default(0)
  other_costs                              Int?                    @default(0)
  cost_remark                              String?
  ///////////////////////
  ///////// gate_pass_in///////
  gate_pass_in                             String?
  load_type                                load_type?

  transaction_container_vehicle transaction_container_vehicle[] @relation("transaction_container_vehicle_vehicle")
  //////////

  // PickUp Photo & Delivery Photo & ACH
  pickup_photos                        Boolean?                           @default(false)
  delivery_photos                      Boolean?                           @default(false)
  ach                                  Boolean?                           @default(false)
  damage_note                          String?
  customer_comment                     String?
  customer_checked                     Boolean                            @default(false)
  loading_company_note                 String?
  receiver_name                        String?                            @prisma.VarChar(255)
  is_printed                           Boolean?                           @default(false)
  is_in_united_trading                 Boolean?                           @default(false)
  transaction_no                       String?
  transaction_at                       DateTime?
  transaction_by                       Int?
  users_vehicles_transaction_byTousers users?                             @relation("vehicles_transaction_byTousers", fields: [transaction_by], references: [id], onUpdate: NoAction)
  vehicle_storages                     vehicle_storages[]
  vehicle_damages                      vehicle_damages?
  title_tracking_number                String?                            @prisma.VarChar(128)
  title_tracking_date                  DateTime?
  title_tracking_arrival_date          DateTime?
  title_tracking_status                vehicles_title_tracking_enum?
  title_tracking_status_updated_at     DateTime?
  inspection                           vehicles_inspection_enum?
  inspector_name                       String?                            @prisma.VarChar(255)
  payments                             payments[]
  dispatch_type                        vehicle_dispatch_type              @default(dispatch)
  vehicle_charges                      vehicle_charges[]
  vehicles_customer_of_customer        vehicles_of_customer_of_customer[]
  of_loading_photo                     String?                            @prisma.VarChar(255)
  of_loading_video                     String?                            @prisma.VarChar(255)
  customer_credits                     customer_credits[]
  customer_profit                      Int?                               @default(0)
  storage_charges                      Int?                               @default(0)
  customer_of_customer_payments        customer_of_customer_payments[]
  customer_vehicle                     customer_vehicle[]
  vehicle_dispatches                   vehicle_dispatches[]
  vehicle_comments                     vehicle_comments[]
  vehicle_storage_attachemnts          vehicle_storage_attachemnts[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([gate_passed_by])
  @@index([updated_by])
  @@index([vin])
}

enum dispatch_status {
  picked_up
  dispatched
  delivered
  list_on_cd
  cancelled_by_carrier
}

enum communication_method {
  email
  phone_call
  text
}

model vehicle_dispatches {
  id         Int              @id @default(autoincrement())
  vehicle_id Int
  status     dispatch_status?

  picked_up_by Int?
  picked_up_at DateTime?

  dispatched_by Int?
  dispatched_at DateTime?

  delivered_by Int?
  delivered_at DateTime?

  list_on_cd_by Int?
  list_on_cd_at DateTime?

  carrier_cancelled_by Int?
  carrier_cancelled_at DateTime?

  customer_informed    Boolean?  @default(false)
  customer_informed_by Int?
  customer_informed_at DateTime?

  customer_approved    Boolean?  @default(false)
  customer_approved_by Int?
  customer_approved_at DateTime?

  driver_informed_missing_parts    Boolean?              @default(false)
  driver_informed_missing_parts_by Int?
  driver_informed_missing_parts_at DateTime?
  driver_informed_method           communication_method?

  missing_key                 Boolean? @default(false)
  missing_catalytic_converter Boolean  @default(false)
  missing_parts               Boolean  @default(false)
  missing_title               Boolean  @default(false)

  // Timestamps
  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  // Relationships
  vehicle                   vehicles @relation(fields: [vehicle_id], references: [id], onDelete: Cascade)
  customer_informed_by_user users?   @relation("vehicle_dispatches_customer_informed_by", fields: [customer_informed_by], references: [id])
  customer_approved_by_user users?   @relation("vehicle_dispatches_customer_approved_by", fields: [customer_approved_by], references: [id])
  list_on_cd_by_user        users?   @relation("vehicle_dispatches_list_on_cd_by", fields: [list_on_cd_by], references: [id])
  driver_informed_by_user   users?   @relation("vehicle_dispatches_driver_informed_by", fields: [driver_informed_missing_parts_by], references: [id])
  created_by_user           users?   @relation("vehicle_dispatches_created_by", fields: [created_by], references: [id])
  updated_by_user           users?   @relation("vehicle_dispatches_updated_by", fields: [updated_by], references: [id])
  deleted_by_user           users?   @relation("vehicle_dispatches_deleted_by", fields: [deleted_by], references: [id])

  // Optimized indexes
  @@index([vehicle_id])
  @@index([status])
  @@index([created_at])
  @@index([customer_informed_by])
  @@index([customer_approved_by])
  @@index([list_on_cd_by])
  @@index([driver_informed_missing_parts_by])
  @@index([driver_informed_method])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model containers_images {
  id           Int       @id @default(autoincrement())
  container_id Int?
  name         String    @prisma.VarChar(128)
  size         Int?
  url          String    @prisma.VarChar(255)
  created_at   DateTime? @default(now())
  created_by   Int?
  updated_at   DateTime? @updatedAt
  updated_by   Int?
  deleted_at   DateTime? @prisma.Timestamptz(6)
  deleted_by   Int?

  containers                       containers? @relation(fields: [container_id], references: [id], onDelete: Cascade)
  users_container_image_created_by users?      @relation("container_images_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  users_container_image_updated_by users?      @relation("container_images_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  users_container_image_deleted_by users?      @relation("container_images_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
  @@index([container_id])
}

model vehicle_images {
  id         Int       @id @default(autoincrement())
  vehicle_id Int?
  name       String    @prisma.VarChar(128)
  size       Int?
  url        String    @prisma.VarChar(255)
  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  vehicles                       vehicles? @relation(fields: [vehicle_id], references: [id], onDelete: Cascade)
  users_vehicle_image_created_by users?    @relation("vehicle_images_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vehicle_image_updated_by users?    @relation("vehicle_images_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  users_vehicle_image_deleted_by users?    @relation("vehicle_images_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
  @@index([vehicle_id])
}

model vehicle_auction_images {
  id         Int       @id @default(autoincrement())
  vehicle_id Int?
  name       String    @prisma.VarChar(128)
  size       Int?
  url        String    @prisma.VarChar(255)
  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  vehicles                               vehicles? @relation(fields: [vehicle_id], references: [id], onDelete: Cascade)
  users_vehicle_auction_image_created_by users?    @relation("vehicle_auction_images_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vehicle_auction_image_updated_by users?    @relation("vehicle_auction_images_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  users_vehicle_auction_image_deleted_by users?    @relation("vehicle_auction_images_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
  @@index([vehicle_id])
}

model vehicle_towings {
  id                                      Int       @id @default(autoincrement())
  vehicle_id                              Int       @unique
  towing_request_date                     DateTime? @prisma.Date
  tow_amount                              Int?      @default(0)
  towing_company                          String?   @prisma.VarChar(255)
  towed_from                              String?   @prisma.VarChar(125)
  created_at                              DateTime? @default(now())
  created_by                              Int?
  updated_at                              DateTime? @updatedAt
  updated_by                              Int?
  deleted_at                              DateTime? @prisma.Timestamp(6)
  deleted_by                              Int?
  users_vehicle_towings_created_byTousers users?    @relation("vehicle_towings_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vehicle_towings_deleted_byTousers users?    @relation("vehicle_towings_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_vehicle_towings_updated_byTousers users?    @relation("vehicle_towings_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vehicles                                vehicles  @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([vehicle_id])
}

model vehicle_costs {
  id                                    Int       @id @default(autoincrement())
  vehicle_id                            Int       @unique
  dismantal_cost                        Int?      @default(0)
  ship_cost                             Float?    @default(0)
  pgl_storage_costs                     Int?      @default(0)
  title_charge                          Int       @default(0)
  dubai_custom_cost                     Int?      @default(0)
  other_cost                            Int?      @default(0)
  sales_cost                            Int?      @default(0)
  towing_cost                           Int?      @default(0)
  vehicle_price                         Int?      @default(0)
  invoice_description                   String?
  add_information                       String?
  created_at                            DateTime? @default(now())
  created_by                            Int?
  updated_at                            DateTime? @updatedAt
  updated_by                            Int?
  deleted_at                            DateTime? @prisma.Timestamp(6)
  deleted_by                            Int?
  users_vehicle_costs_created_byTousers users?    @relation("vehicle_costs_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vehicle_costs_deleted_byTousers users?    @relation("vehicle_costs_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_vehicle_costs_updated_byTousers users?    @relation("vehicle_costs_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vehicles                              vehicles  @relation(fields: [vehicle_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([vehicle_id])
}

model vehicle_storages {
  id         Int      @id @default(autoincrement())
  vehicle_id Int
  date       DateTime @prisma.Date
  cost       Float    @default(0)

  created_at DateTime? @default(now())
  created_by Int?
  vehicles   vehicles  @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)
  createdBy  users?    @relation("vehicle_storages_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)

  @@index([vehicle_id])
}

model vehicle_storage_attachemnts {
  id Int @id @default(autoincrement())

  name String
  type String
  url  String
  size Int

  vehicle_id Int
  vehicle    vehicles @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)

  created_at DateTime? @default(now())
  created_by Int?
  createdBy  users?    @relation("vehicle_storage_attachemnts_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)

  deleted_at DateTime?
  deleted_by Int?
  deletedBy  users?    @relation("vehicle_storage_attachemnts_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model holidays {
  id                             Int       @id @default(autoincrement())
  title                          String?   @prisma.VarChar(256)
  date                           DateTime  @prisma.Date
  description                    String?   @prisma.Text
  created_at                     DateTime? @default(now())
  created_by                     Int?
  updated_at                     DateTime? @updatedAt
  updated_by                     Int?
  deleted_at                     DateTime? @prisma.Timestamp(6)
  deleted_by                     Int?
  users_holiday_created_by_users users?    @relation("holiday_created_by_users", fields: [created_by], references: [id], onUpdate: NoAction)
  users_holiday_deleted_by_users users?    @relation("holiday_deleted_by_users", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_holiday_updated_by_users users?    @relation("holiday_updated_by_users", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

// Damage case schema
model vehicle_damages {
  id                                      Int                      @id @default(autoincrement())
  case_number                             Int?
  vehicle_id                              Int                      @unique
  damage_type                             damage_type?
  inspection_photo                        String?                  @prisma.VarChar(256)
  damage_happened_at                      damage_happened_at_tbl[]
  case_received_date                      DateTime?                @prisma.Date
  claim                                   Float?                   @default(0)
  claim_currency                          currency?
  confirmed                               Float?
  confirmed_currency                      currency?
  credit                                  Float?                   @default(0)
  credit_currency                         currency?
  lc_credit                               Float?                   @default(0)
  lc_credit_currency                      currency?
  claim_status                            claim_status?
  damage_status                           damage_status?
  credited_at                             DateTime?
  credited_by                             Int?
  email_sent_at                           DateTime?
  audit_reviewed_at                       DateTime?
  audit_reviewed_by                       Int?
  remark                                  String?                  @prisma.VarChar(256)
  vehicles                                vehicles                 @relation(fields: [vehicle_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  damage_details                          damage_details[]
  vehicle_damages_images                  vehicle_damages_images[]
  created_at                              DateTime?                @default(now())
  created_by                              Int?
  updated_at                              DateTime?                @updatedAt
  updated_by                              Int?
  deleted_at                              DateTime?                @prisma.Timestamp(6)
  deleted_by                              Int?
  users_vehicle_damages_credited_by       users?                   @relation("vehicle_damages_credited_by_users", fields: [credited_by], references: [id], onUpdate: NoAction)
  users_vehicle_damages_audit_reviewed_by users?                   @relation("vehicle_damages_audit_reviewed_by_users", fields: [audit_reviewed_by], references: [id], onUpdate: NoAction)
  users_vehicle_damages_created_by_users  users?                   @relation("vehicle_damages_created_by_users", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vehicle_damages_deleted_by_users  users?                   @relation("vehicle_damages_deleted_by_users", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_vehicle_damages_updated_by_users  users?                   @relation("vehicle_damages_updated_by_users", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
  @@index([audit_reviewed_by])
}

model vehicle_damages_images {
  id                Int       @id @default(autoincrement())
  vehicle_damage_id Int
  url               String
  original_name     String?
  type              String?   @default("inspection")
  created_at        DateTime  @default(now())
  created_by        Int?
  updated_at        DateTime? @updatedAt
  updated_by        Int?
  deleted_at        DateTime?
  deleted_by        Int?

  vehicle_damage  vehicle_damages @relation(fields: [vehicle_damage_id], references: [id], onDelete: Cascade)
  created_by_user users?          @relation("vehicle_damages_images_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_by_user users?          @relation("vehicle_damages_images_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_by_user users?          @relation("vehicle_damages_images_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([vehicle_damage_id])
  @@index([created_by])
  @@index([updated_by])
}

model damage_happened_at_tbl {
  id                 Int                 @id @default(autoincrement())
  vehicle_damages_id Int?
  happened_at        damage_happened_at?
  vehicles_damage    vehicle_damages?    @relation(fields: [vehicle_damages_id], references: [id])

  @@index([vehicle_damages_id])
}

model damage_details {
  id                 Int              @id @default(autoincrement())
  vehicle_damages_id Int?
  detail             damage_detail?
  vehicle_damage     vehicle_damages? @relation(fields: [vehicle_damages_id], references: [id])

  @@index([vehicle_damages_id])
}

enum damage_detail {
  All_Over
  Antenna
  Back_Fender
  Back_Light
  Bonnet
  Battery
  Bumper
  Catalytic_Converter
  Dashboard
  Door
  Engine
  Fender
  Fog_Light
  Front_Bumper
  Front_Door
  Front_Fender
  Front_Grill
  Front_windshield
  Glass
  Grill
  Head_Light
  Key
  Light
  Mirror
  Panorama
  Radar
  Rear_Door
  Rear_Glass
  Rear_Windshield
  Roof
  Roof_Pillar
  Running_Panel
  Seats
  Spoiler
  Tire
  Trunk
  Underneath
  Wheel
  Wheels
  Windshield
}

enum damage_happened_at {
  Unknown
  Auction
  Destination
  Container
  Yard
  Dispatch
  Loaded
  UnLoading
  CBP
}

enum currency {
  USD
  AED
  OMR
}

enum damage_type {
  Missing
  Damage
  Broken
  Scratch
  Dent
}

enum claim_status {
  Un_Credited
  Credited
}

enum fuel_type_enum {
  gasoline
  hybrid
  electric
}

enum vehicle_size_enum {
  sedan
  medium_suv
  full_size_suv
  large_suv
}

enum damage_status {
  Dismissed
  Closed
  In_process
  Initial_Review
  Pre_Credit
  Credited
  UnLoading
  Half_Cut
  Under_Investigation
  Pending_CA
  Forgotten
}

enum rate_apply_date_enum {
  purchase_date
  loading_date
  etd_date
  ingate_date
}

enum exit_paper_status {
  document_recieved
  submitted_to_custom
  recieved_from_custom
  suspended
  rejected
  credit_given_to_customer
  credit_not_given_to_customer
}

enum accounts_type {
  main
  pglu
}

enum customer_clearance_type {
  pgl
  customer
}

enum branch {
  UAE
  OMAN
  POTI
  KBL
}

model exit_papers {
  id                                  Int                            @id @default(autoincrement())
  issue_date                          DateTime?                      @prisma.Date
  service_charge                      Float?
  declaration_number                  String?                        @prisma.VarChar(64)
  declaration_date                    DateTime?
  claim_number                        String?                        @prisma.VarChar(64)
  claim_date                          DateTime?
  request_no                          String?                        @prisma.VarChar(64)
  status                              exit_paper_status?             @default(document_recieved)
  is_credit_given                     Boolean                        @default(false)
  is_mukhasa                          Boolean                        @default(false)
  prove                               String?                        @prisma.VarChar(255)
  description                         String?                        @prisma.VarChar(255)
  expired_date                        DateTime?                      @prisma.Date
  created_at                          DateTime?                      @default(now())
  created_by                          Int?
  updated_at                          DateTime?                      @updatedAt
  updated_by                          Int?
  deleted_at                          DateTime?                      @prisma.Timestamp(6)
  deleted_by                          Int?
  users_exit_papers_created_byTousers users?                         @relation("exit_papers_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_exit_papers_deleted_byTousers users?                         @relation("exit_papers_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_exit_papers_updated_byTousers users?                         @relation("exit_papers_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  exit_papers_vehicles                exit_papers_vehicles[]
  customerPaymentTransactionId        Int?
  customerPaymentTransaction          customer_payment_transactions? @relation(name: "TxnExitPapers", fields: [customerPaymentTransactionId], references: [id], onDelete: Cascade)
}

model exit_papers_vehicles {
  id                         Int                @id @default(autoincrement())
  vehicle_id                 Int?
  exit_paper_id              Int
  custom_vin                 String?            @unique @prisma.VarChar(255)
  custom_company_name        String?            @prisma.VarChar(255)
  custom_vehicle_description String?
  custom_vehicle_price       Int?
  custom_container_number    String?            @prisma.VarChar(255)
  vat                        Decimal?
  custom_duty                Decimal?
  currency                   mix_currency_type?
  claim_amount               Int?
  created_at                 DateTime?          @default(now())
  created_by                 Int?
  updated_at                 DateTime?          @updatedAt
  updated_by                 Int?
  deleted_at                 DateTime?          @prisma.Timestamptz(6)
  deleted_by                 Int?
  vehicles                   vehicles?          @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)
  exit_papers                exit_papers?       @relation(fields: [exit_paper_id], references: [id], onUpdate: NoAction)
}

model steamshiplines {
  id                                           Int                                            @id @default(autoincrement())
  name                                         String                                         @prisma.VarChar(255)
  contract_number                              String?                                        @prisma.VarChar(255)
  note                                         String?                                        @prisma.Text
  created_at                                   DateTime?                                      @default(now())
  created_by                                   Int?
  updated_at                                   DateTime?                                      @updatedAt
  updated_by                                   Int?
  deleted_at                                   DateTime?                                      @prisma.Timestamptz(6)
  deleted_by                                   Int?
  users_steamshipline_created_byTousers        users?                                         @relation("steamshiplines_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_steamshipline_deleted_byTousers        users?                                         @relation("steamshiplines_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  users_steamshipline_updated_byTousers        users?                                         @relation("steamshiplines_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  vessels                                      vessels[]
  steamshiplines_webLinks                      steamshiplines_webLinks[]
  steamshiplines_contacts                      steamshiplines_contacts[]
  steamshiplines_credentials                   steamshiplines_credentials[]
  shiplines_booking_freight_rates              shiplines_booking_freight_rates[]
  special_shipping_rates                       special_shipping_rates[]
  general_shipping_rates_details               general_shipping_rates_details[]               @relation("generalRateToShiplines")
  shipping_rate_destination_location_shiplines shipping_rate_destination_location_shiplines[]

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model steamshiplines_webLinks {
  id               Int             @id @default(autoincrement())
  web_link         String?         @prisma.VarChar(255)
  description      String?         @prisma.Text
  steamshipline_id Int
  created_at       DateTime        @default(now())
  deleted_at       DateTime?
  steamshiplines   steamshiplines? @relation(fields: [steamshipline_id], references: [id], onUpdate: NoAction)
}

model steamshiplines_contacts {
  id               Int                    @id @default(autoincrement())
  contact_type     shipline_contacts_emum
  contact_line     String?                @prisma.VarChar(255)
  point_of_contact String?                @prisma.VarChar(255)
  related_section  String?                @prisma.VarChar(255)
  steamshipline_id Int
  created_at       DateTime               @default(now())
  deleted_at       DateTime?
  steamshiplines   steamshiplines?        @relation(fields: [steamshipline_id], references: [id], onUpdate: NoAction)
}

enum shipline_contacts_emum {
  Phone
  Whatsapp
  Email
}

model steamshiplines_credentials {
  id               Int             @id @default(autoincrement())
  username         String?         @prisma.VarChar(255)
  password         String?         @prisma.Text
  steamshipline_id Int
  created_at       DateTime        @default(now())
  deleted_at       DateTime?
  steamshiplines   steamshiplines? @relation(fields: [steamshipline_id], references: [id], onUpdate: NoAction)
}

model vessels {
  id                                Int                         @id @default(autoincrement())
  name                              String?                     @prisma.VarChar(255)
  etd                               DateTime?                   @prisma.Date
  etd_status                        brd_erd_status?
  si_cut_off                        DateTime?                   @prisma.Date
  brd                               DateTime?                   @prisma.Date
  brd_status                        brd_erd_status?
  port_of_loading                   Int?
  terminal_id                       Int?
  erd                               DateTime?                   @prisma.Date
  erd_status                        brd_erd_status?
  steamshipline_id                  Int?
  w_load                            Int?                        @default(0)
  avg_load                          Int?                        @default(0)
  chasis_no                         Int?                        @default(0)
  scac_code                         String?                     @prisma.VarChar(64)
  vessel_status                     vessels_vessel_status_enum?
  bill_of_loading_type              bill_of_loading_status      @default(action_required)
  remark                            String?
  send_mail                         Boolean                     @default(false)
  status_changed_at                 DateTime?                   @prisma.Date
  cancelled_at                      DateTime?                   @prisma.Timestamptz(6)
  cancelled_by                      Int?
  reviewed_at                       DateTime?                   @prisma.Timestamptz(6)
  reviewed_by                       Int?
  created_at                        DateTime?                   @default(now())
  created_by                        Int?
  updated_at                        DateTime?                   @updatedAt
  updated_by                        Int?
  deleted_at                        DateTime?                   @prisma.Timestamptz(6)
  deleted_by                        Int?
  bookings                          bookings[]
  bill_of_loadings                  bill_of_loadings[]
  users_vessels_cancelled_byTousers users?                      @relation("vessels_cancelled_byTousers", fields: [cancelled_by], references: [id], onUpdate: NoAction)
  users_vessels_created_byTousers   users?                      @relation("vessels_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_vessels_deleted_byTousers   users?                      @relation("vessels_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  locations                         locations?                  @relation(fields: [port_of_loading], references: [id], onUpdate: NoAction)
  terminals                         terminals?                  @relation(fields: [terminal_id], references: [id], onUpdate: NoAction)
  users_vessels_reviewed_byTousers  users?                      @relation("vessels_reviewed_byTousers", fields: [reviewed_by], references: [id], onUpdate: NoAction)
  steamshiplines                    steamshiplines?             @relation(fields: [steamshipline_id], references: [id], onUpdate: NoAction)
  users_vessels_updated_byTousers   users?                      @relation("vessels_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  destination_booking_targets vessel_destination_booking_targets[]

  @@index([port_of_loading])
  @@index([terminal_id])
  @@index([steamshipline_id])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

enum bill_of_loading_status {
  action_required
  in_process
  review
  done
}

model vessel_destination_booking_targets {
  id             Int       @id @default(autoincrement())
  vessel_id      Int
  destination_id Int
  target         Int
  created_at     DateTime? @default(now())
  created_by     Int?

  vessel                                  vessels      @relation(fields: [vessel_id], references: [id])
  destination                             destinations @relation(fields: [destination_id], references: [id])
  users_vessels_targets_created_byTousers users?       @relation("users_vessels_targets_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
}

enum brd_erd_status {
  estimate
  actual
}

model pgl_profile {
  id                              Int       @id @default(autoincrement())
  name                            String?   @prisma.VarChar(255)
  street                          String?   @prisma.VarChar(255)
  city                            String?   @prisma.VarChar(255)
  state                           String?   @prisma.VarChar(255)
  zip_code                        String?   @prisma.VarChar(255)
  email                           String?   @prisma.VarChar(255)
  phone                           String?   @prisma.VarChar(255)
  fax                             String?   @prisma.VarChar(255)
  website                         String?   @prisma.VarChar(255)
  facebook                        String?   @prisma.VarChar(255)
  bank_name                       String?   @prisma.VarChar(255)
  account_name                    String?   @prisma.VarChar(255)
  account_number                  String?   @prisma.VarChar(255)
  aba                             String?   @prisma.VarChar(255)
  swift                           String?   @prisma.VarChar(255)
  b_street                        String?   @prisma.VarChar(255)
  b_city                          String?   @prisma.VarChar(255)
  b_state                         String?   @prisma.VarChar(255)
  b_zip                           String?   @prisma.VarChar(255)
  b_country                       String?   @prisma.VarChar(255)
  created_at                      DateTime? @default(now())
  updated_at                      DateTime? @updatedAt
  updated_by                      Int?
  created_by                      Int?
  users_profile_created_byTousers users?    @relation("profile_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_profile_updated_byTousers users?    @relation("profile_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
}

model loading_states {
  id                        Int              @id @default(autoincrement())
  name                      String?          @prisma.VarChar(255)
  parentId                  Int?
  created_at                DateTime?        @default(now())
  created_by                Int?
  updated_at                DateTime?        @updatedAt
  updated_by                Int?
  deleted_at                DateTime?        @prisma.Timestamptz(6)
  deleted_by                Int?
  parent                    loading_states?  @relation("parent", fields: [parentId], references: [id])
  children                  loading_states[] @relation("parent")
  loading_states_created_by users?           @relation("loading_states_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  loading_states_updated_by users?           @relation("loading_states_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  loading_states_deleted_by users?           @relation("loading_states_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)
  loading_cities            loading_cities[]
}

model loading_cities {
  id                        Int             @id @default(autoincrement())
  branch_id                 Int?
  city_name                 String?         @prisma.VarChar(255)
  created_at                DateTime?       @default(now())
  created_by                Int?
  updated_at                DateTime?       @updatedAt
  updated_by                Int?
  deleted_at                DateTime?       @prisma.Timestamptz(6)
  deleted_by                Int?
  loading_states            loading_states? @relation(fields: [branch_id], references: [id], onUpdate: NoAction)
  loading_cities_created_by users?          @relation("created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  loading_cities_updated_by users?          @relation("updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  loading_cities_deleted_by users?          @relation("deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  loading_cost          loading_cost[]
  towing_rates          towing_rates[]
  vehicles              vehicles[]
  vehicles_auction_city vehicles[]             @relation("AuctionCity")
  company_loading_cost  company_loading_cost[]
  mix_shipping_rates    mix_shipping_rates[]
}

model mix_shipping_rates {
  id              Int                            @id @default(autoincrement())
  towing          Int?
  shipping        Int?
  clearance       Int?
  TDS_charges     Int?
  profit          Int?
  location_id     Int?
  loading_city_id Int?
  created_at      DateTime?                      @default(now())
  created_by      Int?
  updated_at      DateTime?                      @updatedAt
  updated_by      Int?
  deleted_at      DateTime?                      @prisma.Timestamptz(6)
  deleted_by      Int?
  effective_date  DateTime?                      @prisma.Date
  destination_id  Int?
  status          mix_shipping_rate_status_enum?
  company_id      Int?
  destinations    destinations?                  @relation(fields: [destination_id], references: [id], onUpdate: NoAction)
  locations       locations?                     @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  loading_cities  loading_cities?                @relation(fields: [loading_city_id], references: [id], onUpdate: NoAction)
  companies       companies?                     @relation(fields: [company_id], references: [id], onUpdate: NoAction)

  mix_shipping_rates_created_by users? @relation("mix_shipping_rates_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  mix_shipping_rates_updated_by users? @relation("mix_shipping_rates_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  mix_shipping_rates_deleted_by users? @relation("mix_shipping_rates_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@map("mix_shipping_rates")
}

model loading_cost {
  id                       Int                            @id @default(autoincrement())
  towing                   Int?
  shipping                 Int?
  clearance                Int?
  TDS_charges              Int?
  profit                   Int?
  location_id              Int?
  loading_city_id          Int?
  created_at               DateTime?                      @default(now())
  created_by               Int?
  updated_at               DateTime?                      @updatedAt
  updated_by               Int?
  deleted_at               DateTime?                      @prisma.Timestamptz(6)
  deleted_by               Int?
  effective_date           DateTime?                      @prisma.Date
  destination_id           Int?
  status                   mix_shipping_rate_status_enum?
  destinations             destinations?                  @relation(fields: [destination_id], references: [id], onUpdate: NoAction)
  locations                locations?                     @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  loading_cities           loading_cities?                @relation(fields: [loading_city_id], references: [id], onUpdate: NoAction)
  loading_costs_created_by users?                         @relation("created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  loading_costs_updated_by users?                         @relation("updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  loading_costs_deleted_by users?                         @relation("deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  company_loading_cost     company_loading_cost[]
}

model towing_rates {
  id                      Int              @id @default(autoincrement())
  towing                  Int?
  loading_city_id         Int?
  location_id             Int?
  half_location_id        Int? // Corrected field name
  half_rate               Int?
  effective_date          DateTime?        @prisma.Date
  status                  TowingRateStatus @default(pending)
  created_at              DateTime?        @default(now())
  created_by              Int?
  updated_at              DateTime?        @updatedAt
  updated_by              Int?
  deleted_at              DateTime?        @prisma.Timestamptz(6)
  deleted_by              Int?
  towing_rates_created_by users?           @relation("created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  towing_rates_updated_by users?           @relation("updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  towing_rates_deleted_by users?           @relation("deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  locations               locations?       @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  half_locations          locations?       @relation("half_locations", fields: [half_location_id], references: [id], onUpdate: NoAction)
  loading_cities          loading_cities?  @relation(fields: [loading_city_id], references: [id], onUpdate: NoAction)
}

model company_loading_cost {
  id                               Int                  @id @default(autoincrement())
  loading_cost_id                  Int
  company_id                       Int
  status                           shipping_rate_status @default(pending)
  towing                           Int?
  shipping                         Int?
  clearance                        Int?
  TDS_charges                      Int?
  location_id                      Int?
  loading_city_id                  Int?
  effective_date                   DateTime             @prisma.Date
  created_at                       DateTime?            @default(now())
  created_by                       Int?
  updated_at                       DateTime?            @updatedAt
  updated_by                       Int?
  deleted_at                       DateTime?            @prisma.Timestamptz(6)
  deleted_by                       Int?
  loading_cost                     loading_cost?        @relation(fields: [loading_cost_id], references: [id], onUpdate: NoAction)
  companies                        companies?           @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  locations                        locations?           @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  loading_cities                   loading_cities?      @relation(fields: [loading_city_id], references: [id], onUpdate: NoAction)
  company_loading_costs_created_by users?               @relation("created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  company_loading_costs_updated_by users?               @relation("updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  company_loading_costs_deleted_by users?               @relation("deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model mix_shipping_containers {
  id            Int       @id @default(autoincrement())
  container_id  Int
  exchange_rate Float
  clearance     Float     @default(0.00)
  tax           Float     @default(0.00)
  forklieft     Float     @default(0.00)
  vcc           Float     @default(0.00)
  other_charges Float     @default(0.00)
  description   String?
  created_by    Int?
  created_at    DateTime  @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?

  containers    containers @relation(fields: [container_id], references: [id])
  createdByUser users?     @relation("mixContainerCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?     @relation("mixContainerUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?     @relation("mixContainerDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model mix_shipping_invoices {
  id                    Int                     @id @default(autoincrement())
  company_id            Int
  container_id          Int
  exchange_rate         Float
  inv_date              DateTime?
  inv_due_date          DateTime?
  status                invoice_status?
  status_changed_at     DateTime?               @prisma.Date
  discount              Float                   @default(0.00)
  irrecoverable_debt    Float                   @default(0.00)
  description           String?
  created_by            Int?
  created_at            DateTime                @default(now())
  updated_by            Int?
  updated_at            DateTime?
  deleted_by            Int?
  deleted_at            DateTime?
  companies             companies               @relation(fields: [company_id], references: [id])
  containers            containers              @relation(fields: [container_id], references: [id])
  createdByUser         users?                  @relation("mixInvoiceCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser         users?                  @relation("mixInvoiceUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser         users?                  @relation("mixInvoiceDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  mix_shipping_vehicles mix_shipping_vehicles[]
  type                  mix_full_type           @default(mix)
  title_charge_visible  Boolean                 @default(true)
  currency              mix_currency_type       @default(AED)

  @@unique([company_id, container_id], name: "idx_301861_mix_shipping_company_container_unique_id")
  @@index([container_id], name: "idx_301861_mix_container_invoice_container_fk")
  @@index([created_by], name: "idx_301861_mix_container_invoice_created_by")
  @@index([deleted_by], name: "idx_301861_mix_container_invoice_deleted_by")
  @@index([updated_by], name: "idx_301861_mix_container_invoice_updated_by")
}

model mix_shipping_vehicles {
  id                      Int            @id @default(autoincrement())
  vehicle_id              Int
  mix_shipping_invoice_id Int
  freight                 Int?
  vat_and_custom          Float          @default(0.00)
  tow_amount              Float          @default(0.00)
  clearance               Int?
  other_charges           Int?
  discount                Float          @default(0.00)
  irrecoverable_debt      Float          @default(0.00)
  description             String?
  payment_amount          Float?
  payment_date            DateTime?
  payment_description     String?
  vat_and_custom_cost     Float          @default(0.00)
  payment_status          payment_status @default(pending)

  created_by Int?
  created_at DateTime   @default(now())
  updated_by Int?
  updated_at DateTime?
  payments   payments[]

  createdByUser                users?                         @relation("mixVehicleCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser                users?                         @relation("mixVehicleUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  mix_shipping_invoices        mix_shipping_invoices          @relation(fields: [mix_shipping_invoice_id], references: [id], onUpdate: NoAction)
  vehicles                     vehicles                       @relation(fields: [vehicle_id], references: [id])
  mix_shipping_vehicle_charges mix_shipping_vehicle_charges[]
  customer_vehicle_inovice_id  Int?
  customer_vehicle_inovice     customer_vehicle_invoice?      @relation(fields: [customer_vehicle_inovice_id], references: [id])
  customer_vehicle             customer_vehicle[]

  @@unique([vehicle_id], name: "idx_301871_mix_shipping_vehicle_unique_id")
  @@index([created_by], name: "idx_301871_mix_container_vehicle_created_by")
  @@index([updated_by], name: "idx_301871_mix_container_vehicle_updated_by")
  @@index([mix_shipping_invoice_id], name: "idx_301871_mix_shipping_vehicle_invioce_fk")
}

model mix_shipping_vehicle_charges {
  id                    Int                               @id @default(autoincrement())
  mix_vehicle_id        Int
  name                  mix_shipping_vehicle_charges_name
  value                 Float                             @default(0.00)
  cost_value            Float                             @default(0.00)
  created_by            Int?
  created_at            DateTime                          @default(now())
  updated_by            Int?
  updated_at            DateTime?
  deleted_by            Int?
  deleted_at            DateTime?
  createdByUser         users?                            @relation("mixVehicleChargesCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser         users?                            @relation("mixVehicleChargesUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser         users?                            @relation("mixVehicleChargesDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  mix_shipping_vehicles mix_shipping_vehicles             @relation(fields: [mix_vehicle_id], references: [id])

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model pgl_used_cars {
  id                          Int                           @id @default(autoincrement())
  vehicle_id                  Int
  selling_status              Int?
  selling_status_color        String?                       @prisma.VarChar(64)
  comments                    String?                       @prisma.Text
  change_status_from_yts_date DateTime?                     @prisma.Date
  chasis_number               String?                       @prisma.VarChar(200)
  export_country              pgl_used_cars_export_country?
  gatepasser_id               Int?
  gatepass_date               DateTime?                     @prisma.Timestamp
  vcc_user                    Int?
  vcc_date                    DateTime?                     @prisma.Timestamp
  created_at                  DateTime                      @default(now()) @prisma.Timestamp
  created_by                  Int?
  updated_at                  DateTime                      @default(now()) @prisma.Timestamp
  updated_by                  Int?
  customer_id                 Int?
  purchaser_id                Int?
  checked_by                  Int?
  deleted_at                  DateTime?                     @prisma.Timestamp
  deleted_by                  Int?

  pgl_used_car_costs pgl_used_car_costs?

  vehicles      vehicles @relation("pgl_used_cars_ibfk_2", fields: [vehicle_id], references: [id], onUpdate: NoAction)
  createdByUser users?   @relation("pgl_used_cars_ibfk_3", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?   @relation("pgl_used_cars_ibfk_4", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?   @relation("pgl_used_cars_ibfk_5", fields: [deleted_by], references: [id], onUpdate: NoAction)
  vccUser       users?   @relation("user_id_for_applyvcc", fields: [vcc_user], references: [id], onUpdate: NoAction)
  gatepasser    users?   @relation("users_for_gatepasser", fields: [gatepasser_id], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model pgl_used_car_costs {
  id                     Int       @id @default(autoincrement())
  ten_percent_vat_duty   Float?    @default(0)
  clearance              Float?    @default(0)
  other_charges          Float?    @default(0)
  sell_price_aed         Float?    @default(0)
  damage_or_discount     Float?    @default(0)
  cancelled_amount       Float?    @default(0)
  cancell_transaction_id Int?
  first_advanced_pay     String?
  second_payment         String?
  payment_verification   String?
  payment_proof          String?
  auction_payment_source String?
  auction_pay_recipt     String?
  receipt_no             String?
  invoice_issue_date     DateTime? @default(now())
  pgl_used_car_id        Int?      @unique
  created_at             DateTime?
  created_by             Int?
  updated_at             DateTime?
  updated_by             Int?
  deleted_at             DateTime?
  deleted_by             Int?

  createdByUser users?         @relation("pgl_used_car_costs_ibfk_2", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?         @relation("pgl_used_car_costs_ibfk_3", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?         @relation("pgl_used_car_costs_ibfk_4", fields: [deleted_by], references: [id], onUpdate: NoAction)
  pgl_used_cars pgl_used_cars? @relation(fields: [pgl_used_car_id], references: [id])

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

enum pgl_used_cars_export_country {
  GCC
  Bahrain
  Iraq
  Kazakhstan
  Kuwait
  Kurdastan
  Nigeria
  Oman
  Qatar
  Saudi_Arabia
  Tajikistan
  United_Arab_Emirates
  Egypt
}

model pgl_used_cars_yards {
  id         Int       @id @default(autoincrement())
  name       String    @prisma.VarChar(200)
  created_at DateTime? @default(now()) @prisma.Timestamp
  created_by Int?
  updated_at DateTime? @default(now()) @prisma.Timestamp
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamp
  deleted_by Int?

  createdByUser    users?             @relation("pgl_used_cars_yards_ibfk_1", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser    users?             @relation("pgl_used_cars_yards_ibfk_2", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser    users?             @relation("pgl_used_cars_yards_ibfk_3", fields: [deleted_by], references: [id], onUpdate: NoAction)
  yard_inventories yard_inventories[] @relation("yard_inventories_ibfk_1")
  vehicles         vehicles[]         @relation("yard_for_vehicles")

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model yard_inventories {
  id                     Int       @id @default(autoincrement())
  yard_id                Int?
  vehicle_id             Int?
  date_in                DateTime? @prisma.Timestamp
  date_out               DateTime? @prisma.Timestamp
  comments               String?   @prisma.VarChar(500)
  storage_amount         Float     @default(0)
  storage_payment_status String    @default("0") @prisma.VarChar(50)
  hat_number             String?   @prisma.VarChar(64)
  created_at             DateTime? @default(now()) @prisma.Timestamp
  created_by             Int?
  updated_at             DateTime? @default(now()) @prisma.Timestamp
  updated_by             Int?
  deleted_at             DateTime? @prisma.Timestamp
  deleted_by             Int?

  yard          pgl_used_cars_yards? @relation("yard_inventories_ibfk_1", fields: [yard_id], references: [id], onUpdate: NoAction)
  createdByUser users?               @relation("yard_inventories_ibfk_2", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?               @relation("yard_inventories_ibfk_3", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?               @relation("yard_inventories_ibfk_4", fields: [deleted_by], references: [id], onUpdate: NoAction)
  vehicles      vehicles?            @relation("yard_inventories_ibfk_5", fields: [vehicle_id], references: [id], onUpdate: NoAction)

  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

model container_costs {
  id            Int                  @id @default(autoincrement())
  container_id  Int
  name          container_costs_name
  value         Float                @default(0.00)
  description   String?
  created_by    Int?
  created_at    DateTime             @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?               @relation("containerCostsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?               @relation("containerCostsUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?               @relation("containerCostsDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  containers    containers           @relation(fields: [container_id], references: [id])

  @@unique([container_id, name])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}

enum loginable_type {
  user
  customer
  customer_of_customer
  loader
}

enum bookings_booking_party_enum {
  PGL
  TGL
  MKL
  CAUCASUS
  INTELLIGENT
  FADEL
  CUSTOMER
  LOADING_COMPANY
}

enum bookings_booking_status_enum {
  up_coming
  in_process
  archived
  cancelled
  rolled_over
  applied
}

enum bookings_booking_type_enum {
  REGULAR
  SPOT
  HAZ
}

enum bookings_si_enum {
  red
  missing
  submitted
  confirmed
  amend_si
  bl_amendment
}

enum carstate {
  on_the_way
  on_hand_no_title
  on_hand_with_title
  shipped
  pending
  archived
  auction_paid
  auction_unpaid
  arrived
  at_port
  cost_analysis
  datelines
  added_by_customer
  pending_on_the_way
  pending_auction
}

enum pickup_status {
  picked_up
  delivered
}

enum container_status {
  pending
  at_loading
  on_the_way
  arrived
  reserved
  checked
  final_checked
  at_the_dock
  clearance
  cbp_inspection
}

enum clearance_status {
  pending
  document
  in_process
  cleared
  out_gate
  in_gate
  archived
}

enum customer_type {
  direct
  indirect
}

enum customer_shipping_rate_type {
  general
  special
}

enum gender {
  male
  female
}

enum invoice_status {
  auto_generated
  pending
  initial_review
  final_review
  open
  past_due
  irrecoverable_debt
  paid
  on_the_way
  origin
}

enum user_status {
  active
  deactive
  new_register
}

enum vehicles_halfcut_status_enum {
  not_ready_to_halfcut
  ready_to_halfcut
  ready_to_ship
  shipped
  otw_halfcut
  half_cut
  completed
  unknown
  scrap
}

enum vehicles_load_status_enum {
  Yes
  No
}

enum vehicles_load_halfcut_status_enum {
  Yes
  No
}

enum vehicles_tax_status_enum {
  No
  Yes
  Refund
}

enum account_owner_status {
  PGL
  Customer
  null
}

enum users_types {
  admin
  user
}

enum ship_as {
  whole_car
  dismantal
}

enum vessels_vessel_status_enum {
  up_coming
  in_process
  archived
  to_be_finalized
}

enum mix_shipping_vehicle_charges_name {
  attestation_fee
  inspection_charges
  title_charges
  auction_storage
  sharjah_yard_storage
  fed_ex_or_mailing_fee
  recovery_fee
  custom_hold
  relist_fee
  detention_charges
  shortage
  suv_charges
  tds_charges
  registration_fee
  transportation_fee
  office_fee_and_bank
  empty_containers
  coo_charges
  hybrid_charges
  other_charges
}

enum load_type {
  mix
  full
}

enum clearance_combine_booking_invoice_status {
  pending
  open
  past_due
  paid
}

model column_settings {
  id              Int     @id @default(autoincrement())
  user_id         Int
  setting_name    String
  page_name       String
  default_setting Boolean
  columns         Json
  users           users?  @relation(fields: [user_id], references: [id])
}

enum container_costs_name {
  booking_cost
  loading_cost
  inspection_origin
  inspection_destination
  customs_origin
  customs_destination
  detention
  demurrage
  others
}

enum vehicles_title_tracking_enum {
  in_process
  send
  arrived
}

enum mix_shipping_rate_status_enum {
  sent
  pending
  approved
  archived
  rejected
}

model user_notifications {
  id                Int       @id @default(autoincrement())
  title             String
  notification_type String
  description       Json
  seen_at           DateTime? @prisma.Timestamptz(6)
  created_at        DateTime  @default(now()) @prisma.Timestamp
  receiver_id       Int
  users             users?    @relation(fields: [receiver_id], references: [id])
}

model customer_notifications {
  id                Int        @id @default(autoincrement())
  title             String
  notification_type String
  description       String?    @prisma.Text
  seen_at           DateTime?  @prisma.Timestamptz(6)
  created_at        DateTime   @default(now()) @prisma.Timestamp
  receiver_id       Int
  sender_id         Int?
  data              Json?
  users             users?     @relation("sender_id", fields: [sender_id], references: [id])
  customers         customers? @relation(fields: [receiver_id], references: [id])
}

enum announcement_status {
  published
  draft
  archived
}

model announcements {
  id          Int                 @id @default(autoincrement())
  title       String
  excerpt     String?
  image       String?
  description String              @prisma.Text
  status      announcement_status @default(draft)

  created_at        DateTime                   @default(now()) @prisma.Timestamp
  created_by        Int?
  updated_at        DateTime?                  @updatedAt
  updated_by        Int?
  deleted_at        DateTime?                  @prisma.Timestamptz(6)
  deleted_by        Int?
  created_byToUsers users?                     @relation("announcements_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?                     @relation("announcements_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?                     @relation("announcements_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  customers         AnnouncementsToCustomers[]
}

model AnnouncementsToCustomers {
  announcement    announcements @relation(fields: [announcement_id], references: [id])
  announcement_id Int
  customer        customers     @relation(fields: [customer_id], references: [id])
  customer_id     Int
  created_at      DateTime      @default(now())
  seen_at         DateTime?     @prisma.Timestamptz(6)

  @@id([announcement_id, customer_id])
}

enum rate_type {
  complete
  booking
  halfcut
}

enum shipping_rate_status {
  pending
  sent
  approved
  rejected
  archived
}

enum vehicles_inspection_enum {
  Yes
  No
}

enum TowingRateStatus {
  pending
  active
  inactive
}

enum vehicle_dispatch_type {
  self_pickup_not_delivered_to_pgl
  dispatch
  self_pickup_delivered_to_pgl
}

model general_shipping_rates {
  id             Int                  @id @default(autoincrement())
  destination_id Int
  effective_date DateTime?            @prisma.Timestamptz(6)
  remark         String?
  status         shipping_rate_status @default(pending)
  type           rate_type            @default(complete)
  destinations   destinations?        @relation(fields: [destination_id], references: [id], onUpdate: Cascade)
  last_send_at   DateTime?            @prisma.Timestamptz(6)
  last_send_by   Int?
  created_at     DateTime             @default(now()) @prisma.Timestamp
  created_by     Int?
  updated_at     DateTime             @default(now()) @prisma.Timestamp
  updated_by     Int?
  deleted_at     DateTime?            @prisma.Timestamptz(6)
  deleted_by     Int?

  last_send_byToUsers            users?                           @relation("general_shipping_rates_send_by", fields: [last_send_by], references: [id], onUpdate: NoAction)
  created_byToUsers              users?                           @relation("general_shipping_rates_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers              users?                           @relation("general_shipping_rates_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers              users?                           @relation("general_shipping_rates_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)
  general_shipping_rates_details general_shipping_rates_details[]
  company_general_shipping_rates company_general_shipping_rates[]
}

model general_shipping_rates_details {
  id                       Int        @id @default(autoincrement())
  general_shipping_rate_id Int
  location_id              Int
  rate_45hc                Float      @default(0.00)
  rate_40hc                Float      @default(0.00)
  rate_20ft                Float      @default(0.00)
  mix_rate                 Float      @default(0.00)
  suv_dismantle_rate       Float      @default(0.00)
  sedan_dismantle_rate     Float      @default(0.00)
  locations                locations? @relation(fields: [location_id], references: [id], onUpdate: Cascade)

  created_at             DateTime                @default(now()) @prisma.Timestamp
  created_by             Int?
  updated_at             DateTime?               @updatedAt
  updated_by             Int?
  deleted_at             DateTime?               @prisma.Timestamptz(6)
  deleted_by             Int?
  general_shipping_rates general_shipping_rates? @relation(fields: [general_shipping_rate_id], references: [id], onUpdate: Cascade)
  created_byToUsers      users?                  @relation("general_rate_detail_created_by", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers      users?                  @relation("general_rate_detail_updated_by", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers      users?                  @relation("general_rate_detail_deleted_by", fields: [deleted_by], references: [id], onUpdate: NoAction)
  steamshiplines         steamshiplines[]        @relation("generalRateToShiplines")
}

model company_general_shipping_rates {
  id                     Int                     @id @default(autoincrement())
  shipping_rate_id       Int
  company_id             Int
  company                companies               @relation(fields: [company_id], references: [id])
  created_at             DateTime                @default(now()) @prisma.Timestamp
  created_by             Int?
  deleted_at             DateTime?               @prisma.Timestamptz(6)
  deleted_by             Int?
  seen_at                DateTime?               @prisma.Timestamptz(6)
  general_shipping_rates general_shipping_rates? @relation(fields: [shipping_rate_id], references: [id], onUpdate: Cascade)
  created_byToUsers      users?                  @relation("general_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers      users?                  @relation("general_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@unique([shipping_rate_id, company_id], name: "unique_general_rate")
}

model shipping_rates {
  id             Int                  @id @default(autoincrement())
  location_id    Int
  destination_id Int
  status         shipping_rate_status @default(pending)

  // shipment_rates                    shipment_rates?              @relation(fields: [shipping_rate_id], references: [id], onUpdate: Cascade)
  destinations destinations? @relation(fields: [destination_id], references: [id], onUpdate: Cascade)
  locations    locations?    @relation(fields: [location_id], references: [id], onUpdate: Cascade)
  type         rate_type     @default(complete)
  rate_45hc    Float         @default(0.00)
  rate_40hc    Float         @default(0.00)
  rate_20ft    Float         @default(0.00)

  sedan_rate          Float     @default(0.00)
  suv_rate            Float     @default(0.00)
  mix_rate            Float     @default(0.00)
  effective_date      DateTime? @prisma.Timestamptz(6)
  last_send_at        DateTime? @prisma.Timestamptz(6)
  last_send_by        Int?
  created_at          DateTime  @default(now()) @prisma.Timestamp
  created_by          Int?
  updated_at          DateTime  @default(now()) @prisma.Timestamp
  updated_by          Int?
  deleted_at          DateTime? @prisma.Timestamptz(6)
  deleted_by          Int?
  last_send_byToUsers users?    @relation("shipping_rates_send_byTousers", fields: [last_send_by], references: [id], onUpdate: NoAction)
  created_byToUsers   users?    @relation("shipping_rates_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers   users?    @relation("shipping_rates_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers   users?    @relation("shipping_rates_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  //company_general_shipping_rates company_general_shipping_rates[]

  @@unique([location_id, destination_id, type], name: "unique_shipping_rate")
  @@index([location_id])
  @@index([destination_id])
}

model company_shipping_rates {
  id             Int                  @id @default(autoincrement())
  company_id     Int
  location_id    Int
  destination_id Int
  status         shipping_rate_status @default(pending)
  type           rate_type            @default(complete)
  destinations   destinations?        @relation(fields: [destination_id], references: [id], onUpdate: Cascade)
  locations      locations?           @relation(fields: [location_id], references: [id], onUpdate: Cascade)
  ship_line      String?

  company    companies @relation(fields: [company_id], references: [id])
  rate_45hc  Float     @default(0.00)
  rate_40hc  Float     @default(0.00)
  rate_20ft  Float     @default(0.00)
  sedan_rate Float     @default(0.00)
  suv_rate   Float     @default(0.00)
  mix_rate   Float     @default(0.00)

  suv_dismantle_rate   Float     @default(0.00)
  sedan_dismantle_rate Float     @default(0.00)
  company_type         String?
  vehicle_type         String?
  effective_date       DateTime  @prisma.Timestamptz(6)
  created_at           DateTime  @default(now()) @prisma.Timestamp
  created_by           Int?
  deleted_at           DateTime? @prisma.Timestamptz(6)
  deleted_by           Int?
  updated_at           DateTime? @updatedAt
  updated_by           Int?
  seen_at              DateTime? @prisma.Timestamptz(6)
  created_byToUsers    users?    @relation("discount_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers    users?    @relation("discount_rate_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers    users?    @relation("discount_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model fcm_tokens {
  customer_id Int
  token       String
  customer    customers @relation(fields: [customer_id], references: [id])

  @@unique([customer_id, token], name: "unique_fcm_token")
}

model delivery_charge_invoice {
  id                              Int                               @id @default(autoincrement())
  container_id                    Int?
  company_id                      Int
  delivery_charges                String?                           @prisma.VarChar(255)
  del_invoice_number              String?                           @prisma.VarChar(250)
  status                          Int
  payment_received                String?                           @prisma.VarChar(255)
  payment_date                    DateTime?                         @prisma.Date
  evidence_link                   String?                           @prisma.VarChar(255)
  payment_method                  String?                           @prisma.VarChar(255)
  job_no                          String?                           @prisma.VarChar(255)
  description                     String?                           @prisma.Text
  log_after_print                 Boolean
  payments                        payments[]
  created_by                      Int?
  created_at                      DateTime                          @default(now())
  updated_by                      Int?
  updated_at                      DateTime?
  deleted_by                      Int?
  deleted_at                      DateTime?
  companies                       companies                         @relation(fields: [company_id], references: [id])
  containers                      containers?                       @relation(fields: [container_id], references: [id])
  createdByUser                   users?                            @relation("deliveryChargeInvoiceCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser                   users?                            @relation("deliveryChargeInvoiceUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser                   users?                            @relation("deliveryChargeInvoiceDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  delivery_charge_invoice_details delivery_charge_invoice_details[]
}

model delivery_charge_invoice_details {
  id                         Int                     @id @default(autoincrement())
  delivery_charge_invoice_id Int
  container_id               Int
  delivery_charge_invoice    delivery_charge_invoice @relation(fields: [delivery_charge_invoice_id], references: [id])
  containers                 containers              @relation(fields: [container_id], references: [id])
}

enum pl_status {
  pending
  reviewed
}

enum container_title_status {
  replace
  submitted
  done
}

enum container_aes_status {
  amend
  submitted
  checked
}

model clear_logs {
  id                      Int       @id @default(autoincrement())
  container_id            Int?
  company_id              Int
  imp_code                String?   @prisma.VarChar(20)
  container_series_number String?   @prisma.VarChar(255)
  do_charges              String?   @prisma.VarChar(20)
  clearance_status        String?   @prisma.VarChar(255)
  clearance_amount        String?   @prisma.VarChar(20)
  clear_date              DateTime? @prisma.Date
  other_charges           String?   @prisma.VarChar(255)
  container_number        String?   @prisma.VarChar(20)
  transporter_in_charge   String?   @prisma.VarChar(255)
  pull_out                String?   @prisma.VarChar(255)
  deposit                 String?   @prisma.VarChar(255)
  report_date             DateTime? @prisma.Timestamptz(6)
  report_by               Int?
  reason                  String?   @prisma.VarChar(1000)
  bl_no                   String?   @prisma.VarChar(255)
  eta                     DateTime? @prisma.Date
  clearing_company_id     Int?

  created_by       Int?
  created_at       DateTime   @default(now())
  updated_by       Int?
  updated_at       DateTime?
  deleted_by       Int?
  deleted_at       DateTime?
  companies        companies  @relation(fields: [company_id], references: [id])
  clearing_company companies? @relation("ClearingCompany", fields: [clearing_company_id], references: [id])

  containers         containers?          @relation(fields: [container_id], references: [id])
  createdByUser      users?               @relation("clearLogsInvoiceCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser      users?               @relation("clearLogsInvoiceUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser      users?               @relation("clearLogsInvoiceDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  log_invoices       log_invoices?
  single_vcc         single_vcc[]
  exit_claim_charge  exit_claim_charge[]
  detention_charge   detention_charge[]
  clear_log_vehicles clear_log_vehicles[]
}

model clear_log_vehicles {
  id     Int    @id @default(autoincrement())
  log_id Int
  vin    String @unique @prisma.VarChar(64)

  created_by Int?
  created_at DateTime  @default(now())
  updated_by Int?
  updated_at DateTime?
  deleted_by Int?
  deleted_at DateTime?

  clear_logs    clear_logs @relation(fields: [log_id], references: [id])
  createdByUser users?     @relation("clearLogsVehicleCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?     @relation("clearLogsVehicleUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?     @relation("clearLogsVehicleDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model log_invoices {
  id                                Int       @id @default(autoincrement())
  log_id                            Int       @unique
  delivery_invoice_id               Int?
  delivery_order_amount             Float     @default(0.00)
  custom_duty                       Float     @default(0.00)
  port_handling                     Float     @default(0.00)
  transporter_charges               Float     @default(0.00)
  e_token                           Float     @default(0.00)
  local_service_charges             Float     @default(0.00)
  bill_of_entry                     Float     @default(0.00)
  vcc                               Float     @default(0.00)
  handling_fee                      Float     @default(0.00)
  wash_fine_charges                 Float     @default(0.00)
  repairing_cost_charges            Float     @default(0.00)
  export_services_fees              Float     @default(0.00)
  detention_charges                 Float     @default(0.00)
  port_storage                      Float     @default(0.00)
  inspection_charges                Float     @default(0.00)
  terminal_handling_charges         Float     @default(0.00)
  invoice_number                    String?   @prisma.VarChar(250)
  invoice_date                      DateTime? @prisma.Date
  status                            String?   @prisma.VarChar(255)
  status_changed_at                 DateTime? @prisma.Date
  payment_recived                   Float     @default(0.00)
  payment_date                      DateTime? @prisma.Date
  evidence_link                     String?   @prisma.Text
  payment_method                    String?   @default("Cash") @prisma.VarChar(255)
  crune_charges                     Int       @default(0)
  doc_attestation                   Int       @default(0)
  log_after_print                   Boolean?
  job_no                            String?   @prisma.VarChar(255)
  be_no                             String?   @prisma.VarChar(255)
  consignee_charges                 Float?    @default(0.0)
  description                       String?   @prisma.Text
  custom_clearing_balance           Float     @default(0.00)
  discount                          Float     @default(0.00)
  damage_charges                    Float     @default(0.00)
  unloading                         Float     @default(0.00)
  other_charges                     Float     @default(0.00)
  demurrage                         Float     @default(0.00)
  vat                               Float     @default(0.00)
  country_of_origin_charges         Float     @default(0.00)
  hazardous_handling_import_charges Float     @default(0.00)

  port_handling_pgl_cost     Float      @default(0.00)
  inspection_pgl_cost        Float      @default(0.00)
  doc_attestation_pgl_cost   Float      @default(0.00)
  terminal_handling_pgl_cost Float      @default(0.00)
  custom_pgl_cost            Float      @default(0.00)
  wash_find_pgl_cost         Float      @default(0.00)
  port_storage_pgl_cost      Float      @default(0.00)
  additional_pgl_cost        Float      @default(0.00)
  demurrage_pgl_cost         Float      @default(0.00)
  repairing_pgl_cost         Float      @default(0.00)
  vcc_pgl_cost               Float      @default(0.00)
  damage_pgl_cost            Float      @default(0.00)
  detention_pgl_cost         Float      @default(0.00)
  delivery_pgl_cost          Float      @default(0.00)
  fuel_pgl_cost              Float      @default(0.00)
  tip_pgl_cost               Float      @default(0.00)
  crane_pgl_cost             Float      @default(0.00)
  token_pgl_cost             Float      @default(0.00)
  charges_on_truck_pgl_cost  Float      @default(0.00)
  taxi_truck_pgl_cost        Float      @default(0.00)
  additional_charges         Float      @default(0.00)
  vat_pgl_cost               Float      @default(0.00)
  country_of_origin_pgl_cost Float      @default(0.00)
  payments                   payments[]

  created_by    Int?
  created_at    DateTime    @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?      @relation("logInvoicesCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("logInvoicesUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("logInvoicesDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  clear_logs    clear_logs? @relation(fields: [log_id], references: [id])
}

model single_vcc {
  id               Int        @id @default(autoincrement())
  log_id           Int        @unique
  status           Int        @default(2)
  payment_received Float      @default(0.00)
  payment_date     DateTime?  @prisma.Date
  evidence_link    String?
  description      String?
  discount         Float      @default(0.00)
  vcc              Float
  payments         payments[]

  created_by    Int?
  created_at    DateTime    @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?      @relation("singleVccCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("singleVccUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("singleVccDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  clear_logs    clear_logs? @relation(fields: [log_id], references: [id])
}

model exit_claim_charge {
  id               Int        @id @default(autoincrement())
  log_id           Int        @unique
  vehicle_id       Int        @unique
  claim_number     Int
  status           Int        @default(2)
  payment_received Float      @default(0.00)
  payment_date     DateTime?  @prisma.Date
  evidence_link    String?
  description      String?
  discount         Float      @default(0.00)
  exit_charges     Float
  payments         payments[]

  created_by    Int?
  created_at    DateTime    @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?      @relation("exitClaimChargeCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("exitClaimChargeUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("exitClaimChargeDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  clear_logs    clear_logs? @relation(fields: [log_id], references: [id])
}

model detention_charge {
  id                Int        @id @default(autoincrement())
  log_id            Int        @unique
  detention_charges Float      @default(0.00)
  status            Int        @default(2)
  payment_received  Float      @default(0.00)
  payment_date      DateTime?  @prisma.Date
  evidence_link     String?
  description       String?
  discount          Float      @default(0.00)
  payments          payments[]

  created_by    Int?
  created_at    DateTime    @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?      @relation("detentionChargeCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("detentionChargeUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("detentionChargeDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  clear_logs    clear_logs? @relation(fields: [log_id], references: [id])
}

model clearance_combine_booking_invocies {
  id                Int                                       @id @default(autoincrement())
  container_id      Int                                       @unique
  booking_id        Int
  master_booking_id Int
  cost              Decimal                                   @default(0) @prisma.Decimal
  invoice_amount    Decimal                                   @default(0) @prisma.Decimal
  invoice_date      DateTime?                                 @prisma.Date
  status            clearance_combine_booking_invoice_status?
  payment_received  Decimal                                   @default(0) @prisma.Decimal
  payment_date      DateTime?                                 @prisma.Date
  evidence_link     String?
  description       String?
  created_at        DateTime?                                 @prisma.Timestamptz(6)
  created_by        Int?
  updated_at        DateTime?                                 @prisma.Timestamptz(6)
  updated_by        Int?
  deleted_at        DateTime?                                 @prisma.Timestamptz(6)
  deleted_by        Int?
  payments          payments[]                                @relation("ClearanceInvoicePayments")

  containers    containers? @relation(fields: [container_id], references: [id])
  createdByUser users?      @relation("clearanceCombineBookingCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("clearanceCombineBookingUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("clearanceCombineBookingDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model loaders {
  id            Int                @id @default(autoincrement())
  name          String             @unique
  email         String?            @unique @prisma.VarChar(64)
  phone         String?            @prisma.VarChar(64)
  status        loader_status      @default(active)
  photo         String?
  location_id   Int?
  portOfLoading locations?         @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  containers    containers[]       @relation("containers_toLoader")
  documents     loader_documents[]

  created_by    Int?
  created_at    DateTime  @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?    @relation("loadersCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?    @relation("loadersUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?    @relation("loadersDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model loader_documents {
  id            Int      @id @default(autoincrement())
  key           String
  document_type String
  loader_id     Int?
  loader        loaders? @relation(fields: [loader_id], references: [id], onUpdate: NoAction)
  created_at    DateTime @default(now())

  created_by    Int?
  createdByUser users? @relation("loaderDocumentsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
}

model drivers {
  id                     Int                       @id @default(autoincrement())
  name                   String                    @unique
  email                  String?                   @unique @prisma.VarChar(64)
  phone                  String?                   @prisma.VarChar(64)
  status                 driver_status             @default(active)
  type                   driver_type
  photo                  String?
  location_id            Int?
  portOfLoading          locations[]               @relation("driver_port")
  pullDriverContainers   containers[]              @relation("containers_toPullDriver")
  ingateDriverContainers containers[]              @relation("containers_toIngateDriver")
  amounts_per_move       driver_amounts_per_move[]
  documents              driver_documents[]
  payments               driver_payments[]

  created_by    Int?
  created_at    DateTime  @default(now())
  updated_by    Int?
  updated_at    DateTime?
  deleted_by    Int?
  deleted_at    DateTime?
  createdByUser users?    @relation("driversCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?    @relation("driversUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?    @relation("driversDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model driver_amounts_per_move {
  id        Int      @id @default(autoincrement())
  amount    Float    @default(0.00)
  driver_id Int?
  driver    drivers? @relation(fields: [driver_id], references: [id], onUpdate: NoAction)

  effective_from DateTime  @default(now())
  effective_to   DateTime?
  created_by     Int?
  created_at     DateTime  @default(now())
  createdByUser  users?    @relation("amountsPerMoveCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_by     Int?
  updated_at     DateTime  @default(now())
  updatedByUser  users?    @relation("amountsPerMoveUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_by     Int?
  deleted_at     DateTime?
  deletedByUser  users?    @relation("amountsPerMoveDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model driver_documents {
  id  Int    @id @default(autoincrement())
  key String

  document_type String
  expire_date   DateTime?
  driver_id     Int?
  driver        drivers?  @relation(fields: [driver_id], references: [id], onUpdate: NoAction)
  created_at    DateTime  @default(now())

  created_by    Int?
  createdByUser users? @relation("driverDocumentsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
}

model driver_payments {
  id            Int                   @id @default(autoincrement())
  status        driver_payment_status
  total_amount  Float
  start_date    DateTime?
  end_date      DateTime?
  driver_id     Int?
  payment_date  DateTime?
  payment_proof String?

  driver drivers? @relation(fields: [driver_id], references: [id], onUpdate: NoAction)

  created_at       DateTime     @default(now())
  created_by       Int?
  createdByUser    users?       @relation("driverPaymentsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_at       DateTime     @updatedAt
  updated_by       Int?
  updatedByUser    users?       @relation("driverPaymentsUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  pullContainers   containers[] @relation("containers_toPullDriverPayment")
  ingateContainers containers[] @relation("containers_toIngateDriverPayment")
}

enum driver_type {
  pgl_driver
  tgl_driver
  owner_operator
}

enum driver_status {
  active
  inactive
}

enum charges_status {
  active
  inactive
}

enum charge_type {
  per_vehicle
  per_container
}

enum company_charges_status {
  approved
  archived
}

enum driver_payment_status {
  pending
  processed
}

enum loader_status {
  active
  inactive
}

enum load_combination_type {
  easy
  tight
  normal
}

enum uae_pl_status {
  pending
  reviewed
}

enum mix_full_type {
  mix
  full
}

enum news_status {
  published
  draft
  archived
}

enum prelim_pl_status {
  pending
  reviewed
}

model news {
  id                Int         @id @default(autoincrement())
  title             String
  image             String
  category          String
  short_description String
  description       String      @prisma.Text
  status            news_status @default(draft)

  created_at        DateTime  @default(now()) @prisma.Timestamp
  created_by        Int?
  updated_at        DateTime? @updatedAt
  updated_by        Int?
  deleted_at        DateTime? @prisma.Timestamptz(6)
  deleted_by        Int?
  published_at      DateTime?
  created_byToUsers users?    @relation("news_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?    @relation("news_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?    @relation("news_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
}

model departments {
  id                    Int       @id @default(autoincrement())
  name                  String    @prisma.VarChar(255)
  orders                Int?      @default(autoincrement())
  created_at            DateTime? @default(now())
  created_by            Int?
  updated_at            DateTime? @updatedAt
  updated_by            Int?
  deleted_at            DateTime? @prisma.Timestamptz(6)
  deleted_by            Int?
  department_created_by users?    @relation("department_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  department_deleted_by users?    @relation("department_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  department_updated_by users?    @relation("department_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  users                 users[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([name])
  @@index([updated_by])
}

model categories {
  id         Int        @id(map: "idx_97576_primary") @default(autoincrement())
  name       String     @unique(map: "idx_97576_name") @prisma.VarChar(191)
  branch     branch     @default(UAE)
  enabled    Boolean    @default(true)
  parent_id  Int?
  created_by Int
  updated_by Int?
  deleted_by Int?
  created_at DateTime?  @default(now()) @prisma.Timestamptz(6)
  updated_at DateTime   @default(now()) @prisma.Timestamptz(6)
  deleted_at DateTime?  @prisma.Timestamptz(6)
  payments   payments[]

  users             users          @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "categories_created")
  created_byToUsers users?         @relation("categories_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?         @relation("categories_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?         @relation("categories_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  parent            categories?    @relation("categories_parent", fields: [parent_id], references: [id], onUpdate: NoAction)
  children          categories[]   @relation("categories_parent")
  transactions      transactions[] @relation("transactions_category")

  @@index([created_by], map: "idx_97576_categories_created")
  @@index([parent_id], map: "idx_97576_parent_id")
  @@index([parent_id], map: "idx_97576_parent_id_2")
}

model accounts {
  id                       Int           @id(map: "idx_97544_primary") @default(autoincrement())
  user_id                  Int
  opening_balance          Float         @default(0)
  name                     String        @unique(map: "idx_97544_name") @prisma.VarChar(191)
  order_num                Int           @prisma.SmallInt
  type                     accounts_type @default(main)
  only_access_self_account Boolean       @default(false)
  enabled                  Boolean       @default(true)
  branch                   branch        @default(UAE)
  created_by               Int
  deleted_by               Int?
  created_at               DateTime?     @default(now()) @prisma.Timestamptz(6)
  updated_at               DateTime      @default(now()) @prisma.Timestamptz(6)
  updated_by               Int
  deleted_at               DateTime?     @prisma.Timestamptz(6)

  created_byToUsers users? @relation("accounts_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("accounts_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("accounts_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([created_by], map: "idx_97544_accounts_created")
  @@index([user_id], map: "idx_97544_accounts_user")
}

model transfers {
  id                         Int       @id(map: "idx_97981_primary") @default(autoincrement())
  datetime                   DateTime  @default(now()) @prisma.Timestamptz(6)
  amount                     Float     @default(0)
  status                     Int       @default(1)
  source_account_id          BigInt
  destination_account_id     Int
  source_transaction_id      Int
  destination_transaction_id Int
  evidence_link              String?   @prisma.VarChar(1000)
  double_checked_by          String?   @prisma.VarChar(255)
  double_checked_at          DateTime? @prisma.Timestamptz(6)
  cancelled_at               DateTime? @prisma.Timestamptz(6)
  created_by                 Int
  branch                     branch    @default(UAE)
  created_at                 DateTime? @default(now()) @prisma.Timestamptz(6)
  updated_at                 DateTime? @prisma.Timestamptz(6)
  updated_by                 Int
  cancelled_by               Int?
  deleted_at                 DateTime? @prisma.Timestamptz(6)
  deleted_by                 Int?

  created_byToUsers users? @relation("transfers_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("transfers_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("transfers_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)

  @@index([deleted_by], map: "idx_97981_deleted_by_fk")
  @@index([destination_account_id], map: "idx_97981_destination_account_id")
  @@index([destination_account_id], map: "idx_97981_destination_account_id_2")
  @@index([source_account_id], map: "idx_97981_source_account_id")
  @@index([source_account_id], map: "idx_97981_source_account_id_2")
  @@index([cancelled_by], map: "idx_97981_transfers_cancelled")
}

model transactions {
  id                              Int       @id(map: "idx_97971_primary") @default(autoincrement())
  customer_type                   Int?      @prisma.SmallInt
  account_id                      Int
  category_id                     Int?
  customer_id                     Int?
  installment_id                  Int?
  customer_payment_transaction_id Int?
  datetime                        DateTime  @default(now()) @prisma.Timestamptz(6)
  amount                          Float
  evidence_link                   String?   @prisma.VarChar(1000)
  payment_method                  String    @prisma.VarChar(191)
  type                            Int
  branch                          branch    @default(UAE)
  status                          Int       @default(1)
  is_mail_sent_to_customer        Boolean   @default(false)
  description                     String?
  double_checked_by               Int?
  double_checked_at               DateTime? @prisma.Timestamptz(6)
  cancelled_at                    DateTime? @prisma.Timestamptz(6)
  updated_by                      Int?
  cancelled_by                    Int?
  created_by                      Int
  created_at                      DateTime? @prisma.Timestamptz(6)
  updated_at                      DateTime? @prisma.Timestamptz(6)
  deleted_at                      DateTime? @prisma.Timestamptz(6)
  deleted_by                      Int?

  created_byToUsers          users?                         @relation("transactions_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers          users?                         @relation("transactions_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  updated_byToUsers          users?                         @relation("transactions_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  cancelled_byToUsers        users?                         @relation("transactions_cancelled_byTousers", fields: [cancelled_by], references: [id], onUpdate: NoAction)
  double_checked_byToUsers   users?                         @relation("transactions_double_checked_byTousers", fields: [double_checked_by], references: [id], onUpdate: NoAction)
  category                   categories?                    @relation("transactions_category", fields: [category_id], references: [id], onUpdate: NoAction)
  customer                   customers?                     @relation(fields: [customer_id], references: [id], onUpdate: NoAction)
  customerPaymentTransaction customer_payment_transactions? @relation(fields: [customer_payment_transaction_id], references: [id], onUpdate: NoAction)

  transaction_container_vehicle transaction_container_vehicle[]

  @@index([account_id], map: "idx_97971_transactions_accounts")
  @@index([cancelled_by], map: "idx_97971_transactions_cancelled")
  @@index([category_id], map: "idx_97971_transactions_categories")
  @@index([double_checked_by], map: "idx_97971_transactions_checked")
  @@index([created_by], map: "idx_97971_transactions_created")
}

model transaction_container_vehicle {
  id                                                    Int           @id(map: "idx_97966_primary") @default(autoincrement())
  reference_id                                          Int
  transaction_id                                        Int
  type                                                  Int           @prisma.SmallInt
  created_by                                            Int
  deleted_by                                            Int?
  created_at                                            DateTime?     @prisma.Timestamptz(6)
  deleted_at                                            DateTime?     @prisma.Timestamptz(6)
  users_transaction_container_vehicle_created_byTousers users         @relation("transaction_container_vehicle_created_byTousers", fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "transaction_container_vehicle_created")
  users_transaction_container_vehicle_deleted_byTousers users?        @relation("transaction_container_vehicle_deleted_byTousers", fields: [deleted_by], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "transaction_container_vehicle_deleted")
  transaction                                           transactions? @relation(fields: [transaction_id], references: [id], onUpdate: NoAction)
  vehicle                                               vehicles?     @relation("transaction_container_vehicle_vehicle", fields: [reference_id], references: [id], onUpdate: NoAction, map: "transaction_container_vehicle_vehicles")
  container                                             containers?   @relation("transaction_container_vehicle_container", fields: [reference_id], references: [id], onUpdate: NoAction, map: "transaction_container_vehicle_containers")

  @@index([created_by], map: "idx_97966_transaction_container_vehicle_created")
  @@index([deleted_by], map: "idx_97966_transaction_container_vehicle_deleted")
}

model trackings {
  id           Int             @id @default(autoincrement())
  status       tracking_status
  container_id Int?
  vehicle_id   Int?
  created_at   DateTime?       @updatedAt
  vehicles     vehicles?       @relation("tracking_vehicles", fields: [vehicle_id], references: [id], onUpdate: NoAction)
  containers   containers?     @relation("tracking_containers", fields: [container_id], references: [id], onUpdate: NoAction)

  @@unique([status, container_id], name: "status_container_id_Unique")
  @@unique([status, vehicle_id], name: "status_vehicle_id_Unique")
}

enum tracking_status {
  on_the_way
  on_hand_no_title
  on_hand_with_title
  on_hand_with_load
  shipped
  pending
  archived
  auction_paid
  auction_unpaid
  arrived
  at_port
  at_loading
  reserved
  checked
  final_checked
  at_the_dock
  clearance
  cbp_inspection
  pending_on_the_way
  pending_auction
}

model booking_estimations {
  id             Int           @id @default(autoincrement())
  location_id    Int
  destination_id Int
  average        Int
  locations      locations?    @relation(fields: [location_id], references: [id], onUpdate: NoAction)
  destinations   destinations? @relation(fields: [destination_id], references: [id], onUpdate: NoAction)

  @@unique([location_id, destination_id], name: "unique_booking_estimation")
}

model reminders {
  id            Int           @id @default(autoincrement())
  title         String
  description   String?       @prisma.Text
  reminder_type reminder_type
  seen_at       DateTime?     @prisma.Timestamptz(6)
  created_at    DateTime      @default(now()) @prisma.Timestamp
  receiver_id   Int
  data          Json?
  users         users?        @relation("receiver_id", fields: [receiver_id], references: [id])
}

enum reminder_type {
  default
  si_cut_off
  erd
  eta
  delayed
  etd_update_reminder
  full_and_mix_container_eta
  etd_vessel_loading
}

enum shipline_rate_freight_rate_status {
  pending
  active
  archived
}

model shiplines_booking_freight_rates {
  id                  Int                               @id @default(autoincrement())
  shipline_id         Int
  effective_date_from DateTime?                         @prisma.Date
  effective_date_to   DateTime?                         @prisma.Date
  contruct_number     String
  attachment          String?
  status              shipline_rate_freight_rate_status @default(active)
  created_by          Int?
  created_at          DateTime                          @default(now())
  updated_by          Int?
  updated_at          DateTime?
  deleted_by          Int?
  deleted_at          DateTime?
  steamshiplines      steamshiplines?                   @relation(fields: [shipline_id], references: [id], onUpdate: Cascade)
  createdByUser       users?                            @relation("freightRatesCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser       users?                            @relation("freightRatesUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser       users?                            @relation("freightRatesDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  freight_rates       shiplines_freight_rates[]
}

model shiplines_freight_rates {
  id                              Int                              @id @default(autoincrement())
  point_of_loading_id             Int
  point_of_destination_id         Int
  booking_freight_rates_id        Int
  free_days                       Int                              @default(0)
  origin_note                     String?                          @prisma.Text
  destination_note                String?                          @prisma.Text
  locations                       locations?                       @relation(fields: [point_of_loading_id], references: [id], onUpdate: Cascade)
  destinations                    destinations?                    @relation(fields: [point_of_destination_id], references: [id], onUpdate: Cascade)
  shiplines_booking_freight_rates shiplines_booking_freight_rates? @relation(fields: [booking_freight_rates_id], references: [id], onUpdate: Cascade)
  rate_types                      shiplines_freight_rate_types[]
  created_at                      DateTime                         @default(now())
  updated_at                      DateTime?
  deleted_at                      DateTime?
}

enum container_types {
  hc45
  hc40
}

enum freight_rate_category {
  origin
  destination
}

model shiplines_freight_rate_types {
  id                        Int                      @id @default(autoincrement())
  shiplines_freight_rate_id Int
  container_type            container_types
  freight_rate_category     freight_rate_category
  rate_name                 String
  rate_value                Float
  shiplines_freight_rates   shiplines_freight_rates? @relation(fields: [shiplines_freight_rate_id], references: [id], onUpdate: Cascade)
  deleted_at                DateTime?
}

model special_shipping_rates {
  id             Int       @id @default(autoincrement())
  company_id     Int
  destination_id Int
  type           rate_type @default(complete)
  ship_line_id   Int?
  tds_amount     Int?
  company_type   String?
  vehicle_type   String?
  effective_date DateTime? @prisma.Timestamptz(6)
  remark         String?

  status                           shipping_rate_status               @default(pending)
  steamshiplines                   steamshiplines?                    @relation(fields: [ship_line_id], references: [id], onUpdate: Cascade)
  destinations                     destinations?                      @relation(fields: [destination_id], references: [id], onUpdate: Cascade)
  company                          companies                          @relation(fields: [company_id], references: [id])
  created_at                       DateTime                           @default(now()) @prisma.Timestamp
  created_by                       Int?
  deleted_at                       DateTime?                          @prisma.Timestamptz(6)
  deleted_by                       Int?
  updated_at                       DateTime?                          @updatedAt
  updated_by                       Int?
  seen_at                          DateTime?                          @prisma.Timestamptz(6)
  special_shipping_rates_locations special_shipping_rates_locations[]
  created_byToUsers                users?                             @relation("special_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers                users?                             @relation("special_rate_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers                users?                             @relation("special_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model special_shipping_rates_locations {
  id                       Int                     @id @default(autoincrement())
  special_shipping_rate_id Int
  special_shipping_rates   special_shipping_rates? @relation(fields: [special_shipping_rate_id], references: [id], onUpdate: Cascade, onDelete: Cascade)
  location_id              Int
  locations                locations?              @relation(fields: [location_id], references: [id], onUpdate: Cascade, onDelete: Cascade)

  rate_45hc            Float @default(0.00)
  rate_40hc            Float @default(0.00)
  rate_20ft            Float @default(0.00)
  sedan_rate           Float @default(0.00)
  suv_rate             Float @default(0.00)
  mix_rate             Float @default(0.00)
  suv_dismantle_rate   Float @default(0.00)
  sedan_dismantle_rate Float @default(0.00)

  created_at        DateTime  @default(now()) @prisma.Timestamp
  created_by        Int?
  deleted_at        DateTime? @prisma.Timestamptz(6)
  deleted_by        Int?
  updated_at        DateTime? @updatedAt
  updated_by        Int?
  created_byToUsers users?    @relation("special_rate_location_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?    @relation("special_rate_location_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?    @relation("special_rate_location_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

enum equipment_types {
  truck
  trailer
  forklift
}

enum company_names {
  PGL
  TGL
}

enum operation_states {
  GA
  TX
  NJ
}

enum insurance_types {
  pgl_insurance
  tgl_insurance
  no_insurance
}

model loading_equipments {
  id                Int               @id @default(autoincrement())
  make_year         Int?
  model             String?
  vin               String
  plate_number      String
  reg_exp_date      DateTime?         @prisma.Date
  company_name      company_names?
  equipment_type    equipment_types?
  equipment_id      String?
  insurance         insurance_types?
  truck_id          Int?
  manufacturer_name String?
  operation_state   operation_states?

  forklift_brand         String?
  forklift_serial_number String?
  forklift_model         String?
  forklift_capacity      Int?
  forklift_stage         Int?
  forklift_fork_size     String?
  forklift_fuelType      String?
  forklift_remark        String?

  created_at DateTime  @default(now()) @prisma.Timestamp
  created_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?

  created_byToUsers users? @relation("loading_equipments_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("loading_equipments_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("loading_equipments_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@unique([vin, plate_number])
}

model customer_payment_transactions {
  id                                Int                                 @id @default(autoincrement())
  payment_method                    payment_methods
  state                             payment_state_options               @default(pending)
  attachments                       customer_payment_attachment[]
  transaction_number                String?
  amount                            Decimal?                            @prisma.Decimal
  amount_applied                    Decimal?                            @prisma.Decimal
  transaction_fee                   Decimal?                            @default(0) @prisma.Decimal
  inapplicable_amount               Decimal?                            @default(0) @prisma.Decimal
  currency                          mix_currency_type
  exchange_rate                     Float
  customer_id                       Int?
  company_id                        Int?
  bank_id                           Int?
  remark                            String?
  link                              String?
  payment_date                      DateTime?                           @default(now()) @prisma.Timestamp
  before_march_15_check             Boolean?                            @default(false)
  rejection_reason                  String?
  customer                          customers?                          @relation(fields: [customer_id], references: [id], onUpdate: NoAction)
  companies                         companies?                          @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  payments                          payments[]                          @relation("CustomerPaymentTransactionPayments")
  payment_cards                     payment_cards[]                     @relation("CustomerPaymentTransactionPaymentCards")
  customer_transaction_bank_details customer_transaction_bank_details[] @relation("CustomerPaymentTransactionBankDetails")
  transactions                      transactions[]
  created_at                        DateTime                            @default(now()) @prisma.Timestamp
  created_by                        Int?
  deleted_at                        DateTime?                           @prisma.Timestamptz(6)
  deleted_by                        Int?
  updated_at                        DateTime?                           @updatedAt
  updated_by                        Int?
  bank_accounts                     bank_accounts?                      @relation(fields: [bank_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "CustomerPaymentTransactionBankAccount")
  created_byToUsers                 users?                              @relation("customer_payment_transaction_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers                 users?                              @relation("customer_payment_transaction_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers                 users?                              @relation("customer_payment_transaction_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  customer_credits                  customer_credits[]
  credit_date                       DateTime?                           @default(now()) @prisma.Timestamp
  service_charge                    Decimal?                            @prisma.Decimal
  exitPapers                        exit_papers[]                       @relation("TxnExitPapers")

  @@index([customer_id])
  @@index([company_id])
}

model customer_credits {
  id                             Int                           @id @default(autoincrement())
  vehicle_id                     Int?
  container_id                   Int?
  amount                         Decimal                       @prisma.Decimal
  remark                         String?
  customer_payment_transactionId Int
  customer_payment_transaction   customer_payment_transactions @relation(fields: [customer_payment_transactionId], references: [id], onUpdate: Cascade, onDelete: Cascade)
  vehicle                        vehicles?                     @relation(fields: [vehicle_id], references: [id], onDelete: SetNull)
  container                      containers?                   @relation(fields: [container_id], references: [id], onDelete: SetNull)
  created_at                     DateTime?                     @default(now()) @prisma.Timestamp
  created_by                     Int?
  updated_at                     DateTime?                     @updatedAt
  updated_by                     Int?
  deleted_at                     DateTime?                     @prisma.Timestamptz(6)
  deleted_by                     Int?
}

model payment_cards {
  id                            Int                            @id @default(autoincrement())
  limit                         Float                          @default(0.00)
  type                          payment_types
  exchange_rate                 Float                          @default(0.00)
  short_amount                  Float                          @default(0.00)
  customer_tran_id              Int?
  created_at                    DateTime                       @default(now()) @prisma.Timestamp
  created_by                    Int?
  updated_at                    DateTime?                      @updatedAt
  updated_by                    Int?
  deleted_at                    DateTime?                      @prisma.Timestamptz(6)
  deleted_by                    Int?
  customer_payment_transactions customer_payment_transactions? @relation(fields: [customer_tran_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "CustomerPaymentTransactionPaymentCards")

  created_byToUsers users? @relation("payment_cards_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("payment_cards_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("payment_cards_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

enum payment_methods {
  cash
  cashbook
  wire
  check
  mukhasa
  sales_tax
  damage_credit
  demurrage_credit
  storage_credit
  exit_paper_credit
  buyer_fee_discount
  zelle
  online_payment
  refund_of_suspended
  delivery_order
  extra_payment_copart_iaa
  loading_amendment_credit
  missing_loading_credit
  customer_purchased_vehicle_removed
  clearance_credit
  shortage_section
  other_credit
  delivery_order_credit
  commission
  discount_credit
  sale_tax_credit
  port_storage_and_demurrage_credit
  mailing_fee_credit
  relist_fee_credit
  kabul_cash
  hawala
}

model payments {
  id                   Int                   @id @default(autoincrement())
  amount_applied       Decimal?              @prisma.Decimal
  transaction_number   String?
  exchange_rate        Float?
  type                 payment_types
  state                payment_state_options @default(pending)
  customer_tran_id     Int?
  shipment_invoice_id  Int?
  clearance_invoice_id Int?
  payment_date         DateTime?
  auction_category_id  Int?

  customer_payment_transactions customer_payment_transactions?      @relation(fields: [customer_tran_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "CustomerPaymentTransactionPayments")
  shipmentInvoice               invoices?                           @relation(fields: [shipment_invoice_id], references: [id], onDelete: SetNull)
  clearanceInvoice              clearance_combine_booking_invocies? @relation(fields: [clearance_invoice_id], references: [id], onDelete: SetNull, name: "ClearanceInvoicePayments")

  payment_allocations payment_allocations[] @relation("PaymentAllocationsPayments")

  mix_shipping_vehicle_id    Int?
  log_invoice_id             Int?
  single_vcc_id              Int?
  exit_claim_charge_id       Int?
  detention_charge_id        Int?
  delivery_charge_invoice_id Int?
  payment_remark             String?
  vehicle_id                 Int?
  show_remaining_amount      Boolean? @default(false)

  mixShippingVehicle    mix_shipping_vehicles?   @relation(fields: [mix_shipping_vehicle_id], references: [id], onDelete: SetNull)
  logInvoice            log_invoices?            @relation(fields: [log_invoice_id], references: [id], onDelete: SetNull)
  singleVcc             single_vcc?              @relation(fields: [single_vcc_id], references: [id], onDelete: SetNull)
  exitClaimCharge       exit_claim_charge?       @relation(fields: [exit_claim_charge_id], references: [id], onDelete: SetNull)
  detentionCharge       detention_charge?        @relation(fields: [detention_charge_id], references: [id], onDelete: SetNull)
  deliveryChargeInvoice delivery_charge_invoice? @relation(fields: [delivery_charge_invoice_id], references: [id], onDelete: SetNull)
  vehicle               vehicles?                @relation(fields: [vehicle_id], references: [id], onDelete: SetNull)
  category              categories?              @relation(fields: [auction_category_id], references: [id], onDelete: SetNull)

  created_at DateTime  @default(now()) @prisma.Timestamp
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  created_byToUsers users? @relation("payments_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("payments_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("payments_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([customer_tran_id])
}

enum payment_types {
  shipment
  mix
  clearance
  exit_claim_charge
  detention_charge
  delivery_charge
  single_vcc
  clear_log
  auction
  cash
}

enum payment_state_options {
  pending
  approved
  reviewed
  bank_approve
  final_reviewed
  bank_reject
}

model payment_allocations {
  id         Int             @id @default(autoincrement())
  type       allocated_types
  amount     Decimal?        @prisma.Decimal
  payment_id Int?
  payments   payments?       @relation(fields: [payment_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "PaymentAllocationsPayments")

  created_at DateTime  @default(now()) @prisma.Timestamp
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  created_byToUsers users? @relation("payment_allocations_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users? @relation("payment_allocations_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users? @relation("payment_allocations_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([payment_id])
}

enum allocated_types {
  towing
  shipping
  title
  storage_clr
  storage_do
  charge
  other
  storage
  dismantle
  clearance
  vat_custom
  freight
  attestation_fee
  inspection_charges
  auction_storage
  sharjah_yard_storage
  fed_ex_or_mailing_fee
  recovery_fee
  custom_hold
  relist_fee
  detention_charges
  shortage
  suv_charges
  tds_charges
  registration_fee
  transportation_fee
  office_fee_and_bank
  empty_containers
  previous_payment
  vehicle_price
  coo_charges
  cash
  late_fee
  hybrid_charges
}

enum payment_status {
  pending
  partial
  paid
}

enum mix_currency_type {
  AED
  USD @map("$")
  OMR
  GEL
}

model customer_payment_attachment {
  id                             Int                           @id @default(autoincrement())
  url                            String
  name                           String
  deposit_date                   DateTime?
  customer_payment_transactionId Int
  customer_payment_transaction   customer_payment_transactions @relation(fields: [customer_payment_transactionId], references: [id], onUpdate: Cascade, onDelete: Cascade)
}

model login_attempts {
  id             Int         @id @default(autoincrement())
  loginable_id   Int?
  user_type      String //user, customer, yard_location
  username       String
  ip_address     String
  user_agent     String
  location       String? //from ip
  success        Boolean
  failure_reason String?
  created_at     DateTime    @default(now())
  loginable      loginables? @relation(fields: [loginable_id], references: [id], onUpdate: NoAction)
}

model company_charges {
  id         Int                         @id @default(autoincrement())
  company_id Int?
  category   company_charges_categories?
  name       company_charges_names?
  amount     Float?
  cost       Float?
  remark     String?                     @default("")
  created_at DateTime?                   @default(now()) @prisma.Timestamp
  created_by Int?
  updated_at DateTime?                   @updatedAt
  updated_by Int?
  deleted_at DateTime?                   @prisma.Timestamptz(6)
  deleted_by Int?
  companies  companies?                  @relation(fields: [company_id], references: [id], onUpdate: NoAction)
}

model company_charges_v2 {
  id                    Int                         @id @default(autoincrement())
  company_id            Int?
  status                company_charges_status?
  category              company_charges_categories?
  effective_from        DateTime?                   @prisma.Date
  effective_to          DateTime?                   @prisma.Date
  created_at            DateTime?                   @default(now()) @prisma.Timestamp
  created_by            Int?
  updated_at            DateTime?                   @updatedAt
  updated_by            Int?
  deleted_at            DateTime?                   @prisma.Timestamptz(6)
  deleted_by            Int?
  companies             companies?                  @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  company_charges_pivot company_charges_v2_pivot[]
  createdBy             users?                      @relation("company_charges_v2_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy             users?                      @relation("company_charges_v2_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy             users?                      @relation("company_charges__v2_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model charges {
  id                    Int                         @id @default(autoincrement())
  category              company_charges_categories?
  name                  String?
  cost                  Float?
  status                charges_status              @default(active)
  charge_type           charge_type                 @default(per_vehicle)
  created_at            DateTime?                   @default(now()) @prisma.Timestamp
  created_by            Int?
  updated_at            DateTime?                   @updatedAt
  updated_by            Int?
  deleted_at            DateTime?                   @prisma.Timestamptz(6)
  deleted_by            Int?
  createdBy             users?                      @relation("charges_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy             users?                      @relation("charges_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy             users?                      @relation("charges_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
  company_charges_pivot company_charges_v2_pivot[]
}

model company_charges_v2_pivot {
  id                Int                 @id @default(autoincrement())
  charge_id         Int?
  company_charge_id Int?
  current_amount    Int                 @default(0)
  next_amount       Int                 @default(0)
  remark            String?
  created_by        Int?
  updated_at        DateTime?           @updatedAt
  updated_by        Int?
  deleted_at        DateTime?           @prisma.Timestamptz(6)
  deleted_by        Int?
  company_charge    company_charges_v2? @relation(fields: [company_charge_id], references: [id], onUpdate: NoAction)
  charges           charges?            @relation(fields: [charge_id], references: [id], onUpdate: NoAction)
  createdBy         users?              @relation("company_charges_v2_pivot_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy         users?              @relation("company_charges_v2_pivot_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy         users?              @relation("company_charges_v2_pivot_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model vehicle_charges {
  id         Int                         @id @default(autoincrement())
  vehicle_id Int?
  category   company_charges_categories?
  name       company_charges_names?
  amount     Float?
  cost       Float?
  remark     String?                     @default("")
  created_at DateTime?                   @default(now()) @prisma.Timestamp
  created_by Int?
  updated_at DateTime?                   @updatedAt
  updated_by Int?
  deleted_at DateTime?                   @prisma.Timestamptz(6)
  deleted_by Int?
  vehicles   vehicles?                   @relation(fields: [vehicle_id], references: [id], onUpdate: NoAction)

  @@unique([vehicle_id, category, name])
}

model container_charges {
  id           Int                         @id @default(autoincrement())
  container_id Int?
  category     company_charges_categories?
  name         company_charges_names?
  amount       Float?
  cost         Float?
  remark       String?                     @default("")
  created_at   DateTime?                   @default(now()) @prisma.Timestamp
  created_by   Int?
  updated_at   DateTime?                   @updatedAt
  updated_by   Int?
  deleted_at   DateTime?                   @prisma.Timestamptz(6)
  deleted_by   Int?
  containers   containers?                 @relation(fields: [container_id], references: [id], onUpdate: NoAction)
}

enum company_charges_categories {
  finance
  clearance
  title
  fedex
  auction
  load
  used_cars
  dispatch
}

enum company_charges_names {
  // Finance
  towing_charge
  halfcut_charge
  shipping_charge
  storage_charge
  custom_hold
  transfer_fee
  wire_fee
  tds_charges
  dispatch_charge
  manheim_purchase
  hybrid_fee
  absorbers_fee
  shortage
  relist_fee
  suv_charges
  sublot
  attestation_fee
  coo_charges
  hybrid_charges
  custom_duty_vat
  vcc
  clearance_charge
  transportation_fee
  office_fee_and_bank
  empty_containers
  inspection_charges
  detention_charges
  auction_storage
  registration_fee
  other_charges

  // Clearance
  local_service_charges
  bill_of_entry
  handling_fee
  unloading_charges
  consignee_charges
  exit_paper

  // Title
  title_charges
  ca_non_repairable_cert_normal
  ca_non_repairable_cert_urgent
  wa_bill_of_sale

  // fedex
  fedex_2day
  fedex_overnight
  fedex_international_standard
  fedex_international_priority
  fed_ex_or_mailing_fee

  // Auction Account
  uae_transfer_fee_for_copart
  uae_transfer_fee_for_iaa
  local_auction_per_deal_with_customer
  broker_fee_for_customer
  local_auction_fee_charges_per_car

  // Load
  wrapping
  dumprid
  hazardous
  additional_video
  additional_photo

  // Used Cars
  storage_fee
  recovery_fee
  sharjah_yard_storage
}

model exchange_rates {
  id                Int                @id @default(autoincrement())
  company_id        Int?
  currency          mix_currency_type?
  rate              Float?
  companies         companies?         @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  created_at        DateTime           @default(now()) @prisma.Timestamp
  created_by        Int?
  deleted_at        DateTime?          @prisma.Timestamptz(6)
  deleted_by        Int?
  updated_at        DateTime?          @updatedAt
  updated_by        Int?
  created_byToUsers users?             @relation("exchange_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updated_byToUsers users?             @relation("exchange_rate_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deleted_byToUsers users?             @relation("exchange_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model shipping_rates_2 {
  id             Int                    @id @default(autoincrement())
  effective_date DateTime?              @prisma.Date
  effective_to   DateTime?              @prisma.Date
  category       String?
  company_id     Int?
  status         shipping_rate_status_2
  email_sent     Boolean                @default(false)
  note           String?
  rate_type      shipping_rate_type_2   @default(complete)
  TDS_amount     Json                   @prisma.Json

  last_email_sent_at DateTime?
  created_at         DateTime  @default(now())
  updated_at         DateTime? @updatedAt
  deleted_at         DateTime?
  last_email_sent_by Int?
  created_by         Int?
  updated_by         Int?
  deleted_by         Int?
  activity_log       Json?     @prisma.Json

  company companies? @relation(fields: [company_id], references: [id], onUpdate: NoAction)

  lastEmailSentBy users? @relation("shipping_rate_last_email_sent_byTousers", fields: [last_email_sent_by], references: [id], onUpdate: NoAction)

  createdBy users? @relation("shipping_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy users? @relation("shipping_rate_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy users? @relation("shipping_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  shipping_rate_destinations shipping_rate_destinations[]
}

model shipping_rate_destinations {
  id               Int @id @default(autoincrement())
  shipping_rate_id Int
  destination_id   Int

  shipping_rate                       shipping_rates_2                      @relation(fields: [shipping_rate_id], references: [id], onDelete: NoAction)
  destination                         destinations                          @relation(fields: [destination_id], references: [id], onDelete: NoAction)
  shipping_rate_destination_locations shipping_rate_destination_locations[]
}

model shipping_rate_destination_locations {
  id                           Int     @id @default(autoincrement())
  shipping_rate_destination_id Int
  location_id                  Int
  archived                     Boolean @default(false)

  shipping_rate_destination                    shipping_rate_destinations                     @relation(fields: [shipping_rate_destination_id], references: [id], onDelete: NoAction)
  location                                     locations                                      @relation(fields: [location_id], references: [id], onDelete: NoAction)
  shipping_rate_destination_location_shiplines shipping_rate_destination_location_shiplines[]
}

model shipping_rate_destination_location_shiplines {
  id                                    Int     @id @default(autoincrement())
  shipping_rate_destination_location_id Int
  shipline_id                           Int?
  rates                                 Json    @prisma.Json
  current_rate_support_documents        Json?   @prisma.Json
  archived                              Boolean @default(false)

  shipping_rate_destination_location shipping_rate_destination_locations @relation(fields: [shipping_rate_destination_location_id], references: [id], onDelete: NoAction)
  shipline                           steamshiplines?                     @relation(fields: [shipline_id], references: [id], onDelete: NoAction)

  created_at DateTime  @default(now())
  updated_at DateTime? @updatedAt
  deleted_at DateTime?
  created_by Int?
  updated_by Int?
  deleted_by Int?

  createdBy users? @relation("shipping_rate_rate_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy users? @relation("shipping_rate_rate_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy users? @relation("shipping_rate_rate_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  activity_log Json? @prisma.Json
}

model kv_store {
  key   String  @id
  value String
  group String?

  created_at DateTime  @default(now())
  updated_at DateTime? @updatedAt

  created_by Int?
  updated_by Int?
  deleted_by Int?

  createdBy users? @relation("kv_store_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedBy users? @relation("kv_store_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedBy users? @relation("kv_store_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

enum shipping_rate_status_2 {
  approved
  archived
}

enum shipping_rate_type_2 {
  complete
  booking
  halfcut
}

model bill_of_loadings {
  id                     Int                               @id @default(autoincrement())
  vessel_id              Int?
  master_bk              String?
  shipline_bl_number     String?
  shipline_bl_link       String?
  status                 bill_loading_status_enum          @default(in_process)
  type                   bill_loading_type_enum?
  qty                    Int?
  cnee                   String?
  invoice_freight_AED    Float?
  invoice_freight_amount Float?
  noc_charge_AED         Float?
  free_days              Int?
  paymentType            bill_loading_payment_type_enum?
  paymentStatus          bill_loading_payment_status_enum? @default(unpaid)
  status_changed_at      DateTime?                         @prisma.Timestamptz(6)
  payment_date           DateTime?                         @prisma.Timestamptz(6)
  created_at             DateTime                          @default(now()) @prisma.Timestamp
  created_by             Int?
  deleted_at             DateTime?                         @prisma.Timestamptz(6)
  deleted_by             Int?
  updated_at             DateTime?                         @updatedAt
  updated_by             Int?
  checked_at             DateTime?
  checked_by             Int?
  attachment             String?
  remarks                String?
  clearance_remarks      String?
  clearance_status       bill_loading_clearance_enum       @default(pending)
  createdByUser          users?                            @relation("billOfLoadingsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser          users?                            @relation("billOfLoadingsUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser          users?                            @relation("billOfLoadingsDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  checkedByUser          users?                            @relation("billOfLoadingsCheckedByUser", fields: [checked_by], references: [id], onUpdate: NoAction)

  vessels              vessels?                  @relation(fields: [vessel_id], references: [id], onUpdate: NoAction)
  bookings             bookings[]                @relation("billOfLoadingToBookings")
  containers           containers[]
  billOfLoadingCharges bill_of_loading_charges[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model bill_of_loading_charges {
  id                    Int                   @id @default(autoincrement())
  bill_of_loading_id    Int
  container_type        container_types
  freight_rate_category freight_rate_category
  rate_name             String
  rate_value            Float
  bill_of_loadings      bill_of_loadings?     @relation(fields: [bill_of_loading_id], references: [id], onUpdate: Cascade)
  deleted_at            DateTime?
}

enum bill_loading_status_enum {
  in_process
  to_be_checked
  done
  pending_issue
}

enum bill_loading_clearance_enum {
  pending
  document
}

enum bill_loading_payment_type_enum {
  prepaid
  collect
}

enum bill_loading_payment_status_enum {
  paid
  unpaid
  approved
}

model transaction_number_sequence {
  id    Int             @id @default(autoincrement())
  key   payment_methods @unique
  name  String
  value Int             @default(1)
}

model dispute_bookings {
  id                Int                    @id @default(autoincrement())
  booking_id        Int
  details           String?
  status            dispute_booking_status @default(pending)
  progress          Int                    @default(0)
  currency          mix_currency_type?
  total_amount      Int?
  to_be_done        String?
  responsible       String?
  charges_type      String[]
  invoice_numbers   String[]
  created_at        DateTime?              @default(now())
  created_by        Int?
  updated_at        DateTime?              @updatedAt
  updated_by        Int?
  deleted_at        DateTime?              @prisma.Timestamp(6)
  deleted_by        Int?
  createdByUser     users?                 @relation("disputeBookingsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser     users?                 @relation("disputeBookingsUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser     users?                 @relation("disputeBookingsDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  bookings          bookings               @relation(fields: [booking_id], references: [id])
  containers        containers[]           @relation("ContainersToDisputeBookings")
  dispute_follow_up dispute_follow_up[]

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

enum dispute_booking_status {
  pending
  in_process
  rejected
  approved
}

model dispute_follow_up {
  id            Int              @id @default(autoincrement())
  dispute_id    Int
  title         String?
  content       String?
  created_at    DateTime?        @default(now())
  created_by    Int?
  updated_at    DateTime?        @updatedAt
  updated_by    Int?
  deleted_at    DateTime?        @prisma.Timestamp(6)
  deleted_by    Int?
  createdByUser users?           @relation("followUpCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?           @relation("followUpUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?           @relation("followUpDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  dispute       dispute_bookings @relation(fields: [dispute_id], references: [id])

  @@index([created_by])
  @@index([deleted_by])
  @@index([updated_by])
}

model bank_accounts {
  id             Int                @id @default(autoincrement())
  bank_name      String?
  account_name   String?
  account_number String?
  ibn            String?
  bic_code       String?
  currency       mix_currency_type?
  country        String?

  created_at                    DateTime                        @default(now()) @prisma.Timestamp
  created_by                    Int?
  deleted_at                    DateTime?                       @prisma.Timestamptz(6)
  deleted_by                    Int?
  updated_at                    DateTime?                       @updatedAt
  updated_by                    Int?
  customer_payment_transactions customer_payment_transactions[] @relation("CustomerPaymentTransactionBankAccount")
  createdByUser                 users?                          @relation("bankAccountCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser                 users?                          @relation("bankAccountUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser                 users?                          @relation("bankAccountDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
  customer_vehicle_invoice      customer_vehicle_invoice[]
}

model customer_transaction_bank_details {
  id               Int       @id @default(autoincrement())
  customer_tran_id Int?
  title            String?
  reference_number String?
  attachment       String?
  attachment_name  String?
  remark           String?
  deposit_date     DateTime?

  created_at                    DateTime                       @default(now()) @prisma.Timestamp
  created_by                    Int?
  deleted_at                    DateTime?                      @prisma.Timestamptz(6)
  deleted_by                    Int?
  updated_at                    DateTime?                      @updatedAt
  updated_by                    Int?
  customer_payment_transactions customer_payment_transactions? @relation(fields: [customer_tran_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "CustomerPaymentTransactionBankDetails")
  createdByUser                 users?                         @relation("bankDetailsCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser                 users?                         @relation("bankDetailsUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser                 users?                         @relation("bankDetailsDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

model profit_loss_sheet {
  id           Int  @id @default(autoincrement())
  container_id Int?

  booking       Decimal? @prisma.Decimal(10, 2)
  loading       Decimal? @prisma.Decimal(10, 2)
  tds_charges   Decimal? @prisma.Decimal(10, 2)
  shipping_rate Decimal? @prisma.Decimal(10, 2)

  towing_cost   Decimal? @prisma.Decimal(10, 2)
  towing_charge Decimal? @prisma.Decimal(10, 2)

  title_cost   Decimal? @prisma.Decimal(10, 2)
  title_charge Decimal? @prisma.Decimal(10, 2)

  dismental_cost   Decimal? @prisma.Decimal(10, 2)
  dismental_charge Decimal? @prisma.Decimal(10, 2)

  gross_pl Decimal? @prisma.Decimal(10, 2)

  shorage_cost   Decimal? @prisma.Decimal(10, 2)
  shorage_charge Decimal? @prisma.Decimal(10, 2)

  other_cost_on_pgl        Decimal? @prisma.Decimal(10, 2)
  other_charge_on_customer Decimal? @prisma.Decimal(10, 2)

  demurage_cost   Decimal? @prisma.Decimal(10, 2)
  inspection_cost Decimal? @prisma.Decimal(10, 2)

  total_invoice Decimal? @prisma.Decimal(10, 2)
  net_pl        Decimal? @prisma.Decimal(10, 2)
  usa_pl        Decimal? @prisma.Decimal(10, 2)
  status        String?

  container     containers? @relation(fields: [container_id], references: [id], onUpdate: NoAction, onDelete: Cascade, name: "PLSheetContainer")
  created_at    DateTime    @default(now()) @prisma.Timestamp
  created_by    Int?
  deleted_at    DateTime?   @prisma.Timestamptz(6)
  deleted_by    Int?
  updated_at    DateTime?   @updatedAt
  updated_by    Int?
  createdByUser users?      @relation("PLSheetCreatedByUser", fields: [created_by], references: [id], onUpdate: NoAction)
  updatedByUser users?      @relation("PLSheetUpdatedByUser", fields: [updated_by], references: [id], onUpdate: NoAction)
  deletedByUser users?      @relation("PLSheetDeletedByUser", fields: [deleted_by], references: [id], onUpdate: NoAction)
}

enum vehicle_comment_type {
  dispatch
  auction
  invoice
  general
  customer
  loading
  damage
  cost
  pickup
  clearance
  finance
  title
  loading_company
}

model vehicle_comments {
  id           Int                  @id @default(autoincrement())
  vehicle_id   Int
  comment_type vehicle_comment_type
  title        String?              @prisma.VarChar(255)
  content      String               @prisma.Text

  // Self-referential relationship for replies
  parent_id      Int?
  parent_comment vehicle_comments?  @relation("CommentReplies", fields: [parent_id], references: [id], onDelete: Cascade)
  replies        vehicle_comments[] @relation("CommentReplies")

  created_at DateTime? @default(now())
  created_by Int?
  updated_at DateTime? @updatedAt
  updated_by Int?
  deleted_at DateTime? @prisma.Timestamptz(6)
  deleted_by Int?

  // Relationships
  vehicle                   vehicles @relation(fields: [vehicle_id], references: [id], onDelete: Cascade)
  users_comments_created_by users?   @relation("vehicle_comments_created_byTousers", fields: [created_by], references: [id], onUpdate: NoAction)
  users_comments_updated_by users?   @relation("vehicle_comments_updated_byTousers", fields: [updated_by], references: [id], onUpdate: NoAction)
  users_comments_deleted_by users?   @relation("vehicle_comments_deleted_byTousers", fields: [deleted_by], references: [id], onUpdate: NoAction)

  @@index([vehicle_id])
  @@index([comment_type])
  @@index([parent_id])
  @@index([created_at])
  @@index([created_by])
  @@index([updated_by])
  @@index([deleted_by])
}
