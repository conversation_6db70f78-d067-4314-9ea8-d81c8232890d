// module.exports = {
//   async redirects() {
//     return [
//       {
//         source: '/',
//         destination: '/',
//         permanent: true
//       }
//     ];
//   }
// };

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      { protocol: 'https', hostname: 'pglsystem.com' },
      { protocol: 'https', hostname: 'latest.pglsystem.com' },
      { protocol: 'https', hostname: 'admin.pglsystem.com' },
      { protocol: 'https', hostname: 'customer.pglsystem.com' },
      { protocol: 'http', hostname: 'localhost' },
    ],
  },
  experimental: {
    // Reduce parallel operations
    // workerThreads: false,
    // cpus: 1,
  },
};

module.exports = nextConfig;
