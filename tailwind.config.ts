import type { Config } from 'tailwindcss';

export default {
  darkMode: ['class'],
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        flash_green: {
          '0%': { backgroundColor: 'hsl(var(--primary) / 0)' }, // Start with yellow background
          '50%': { backgroundColor: 'hsl(var(--primary) / .2)' }, // Start with yellow background
          '100%': { backgroundColor: 'hsl(var(--primary) / 0)' }, // End with no background
        },
        flash_red: {
          '0%': { backgroundColor: 'hsl(var(--destructive) / 0)' }, // Start with yellow background
          '50%': { backgroundColor: 'hsl(var(--destructive) / .2)' }, // Start with yellow background
          '100%': { backgroundColor: 'hsl(var(--destructive) / 0)' }, // End with no background
        },
        flash_foreground: {
          '0%': { backgroundColor: 'hsl(var(--foreground) / 0)' }, // Start with yellow background
          '50%': { backgroundColor: 'hsl(var(--foreground) / .2)' }, // Start with yellow background
          '100%': { backgroundColor: 'hsl(var(--foreground) / 0)' }, // End with no background
        },
      },
      animation: {
        flash_green: 'flash_green 1s ', // 1-second animation
        flash_red: 'flash_red 1s ', // 1-second animation
        flash_foreground: 'flash_foreground 1s ', // 1-second animation
      },
      fontFamily: {
        sans: [
          'Inter',
          'ui-sans-serif',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'Arial',
          'Noto Sans',
          'sans-serif',
        ],
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;
