{"name": "pgl_system", "version": "1.0.0", "title": "", "description": "", "license": "MIT", "author": {"name": "PGL IT Department", "url": "https://peaceglobaltech.com/"}, "private": false, "dependencies": {"@babel/core": "^7.24.9", "@emotion/cache": "^11.13.0", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@hugocxl/react-to-image": "^0.0.9", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@mui/x-data-grid": "^8.0.0", "@mui/x-date-pickers": "^7.23.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.3", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@react-pdf/renderer": "^3.4.4", "ag-grid-community": "^34.0.0", "ag-grid-enterprise": "^34.0.0", "ag-grid-react": "^34.0.0", "apexcharts": "^3.51.0", "axios": "^1.10.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.12", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.6.2", "html-to-image": "^1.11.13", "i18next": "^23.12.2", "jszip": "^3.10.1", "lottie-react": "^2.4.0", "lucide-react": "^0.461.0", "moment": "^2.30.1", "nanoid": "^5.1.5", "next": "15.4.5", "next-i18next": "^15.3.0", "next-themes": "^0.4.4", "nprogress": "^0.2.0", "pm2": "^6.0.8", "pocketbase": "^0.21.3", "react": "19.1.1", "react-apexcharts": "^1.4.1", "react-avatar-editor": "^13.0.2", "react-custom-scrollbars-2": "^4.5.0", "react-day-picker": "8.10.1", "react-dom": "19.1.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-i18next": "^15.0.0", "react-infinite-scroll-component": "^6.1.0", "react-quill-new": "^3.6.0", "react-to-print": "^3.1.1", "react-toastify": "^10.0.5", "react-zoom-pan-pinch": "^3.7.0", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "swiper": "^11.2.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1"}, "scripts": {"dev": "next dev --turbopack", "build": "tsc --noEmitOnError && next build", "start": "next start", "start:default": "pm2 start && pm2 logs", "pm2:kill": "pm2 kill", "pm2:logs": "pm2 logs", "pm2:status": "pm2 status", "export": "next export", "format": "prettier --write src/"}, "devDependencies": {"@types/node": "^20.14.11", "@types/nprogress": "^0.2.3", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "eslint": "^8.0.0", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "next-transpile-modules": "^10.0.1", "postcss": "^8.4.49", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.15", "typescript": "^5.5.4"}, "resolutions": {"@types/react": "19.1.9", "@types/react-dom": "19.1.7", "react-beautiful-dnd": "npm:@hello-pangea/dnd@^18.0.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}