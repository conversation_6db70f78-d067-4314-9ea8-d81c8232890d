import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import axios from '@/lib/axios';
import { Box } from '@mui/material';
import { toast } from 'react-toastify';
import moment from 'moment';

export async function onViewColumnsChange(
  changedColumn,
  action,
  setDownloadColumns,
  downloadColumns,
  tableColumns,
) {
  if (action == 'remove') {
    setDownloadColumns((d) => d.filter((item) => item.name !== changedColumn));
  } else if (action == 'add') {
    let index = tableColumns.findIndex((item) => item.name == changedColumn);
    if (index != -1) {
      let columns = downloadColumns;
      columns.splice(index, 0, tableColumns[index]);
      setDownloadColumns(columns);
    }
  }
}
export const formFormatDate = (date: any) => {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  const year = date?.getFullYear();
  const month = String(date?.getMonth() + 1).padStart(2, '0');
  const day = String(date?.getDate())?.padStart(2, '0');
  return `${year}-${month}-${day}`;
};

type DiffUnit =
  | 'years'
  | 'months'
  | 'weeks'
  | 'days'
  | 'hours'
  | 'minutes'
  | 'seconds';
export const getDateDifference = (
  date1: any,
  date2: any,
  unit: DiffUnit = 'days',
) => {
  const momentDate1 = moment(date1);
  const momentDate2 = moment(date2);

  // Calculate difference in the specified unit (default is days)
  const difference = momentDate2.diff(momentDate1, unit);

  return difference;
};

export const choseColor = (value) => {
  switch (value) {
    case 'on_hand_no_title':
      return '#e66e20';
    case 'on_hand_with_title':
      return '#9932CC';
    case 'shipped':
      return 'green';
    case 'on_the_way':
      return '#B8860B';
    case 'sent':
      return '#0288d1';
    case 'pending':
      // return '#e28c00';
      return '#EC7900';
    case 'approved':
      return 'green';
    case 'active':
      return 'green';
    case 'reviewed':
      return '#0ABAB5';
    case 'archived':
      return '#3F50B5';
    case 'rejected':
      return 'red';
    case 'up_coming':
      return '#e66e19';
    case 'in_process':
      return 'purple';
    case 'applied':
      return 'darkgray';
    case 'cancelled':
      return 'red';
    case 'not_submited':
      return 'lightpink';
    case 'missing':
      return '#818587';
    case 'done':
      return '#14e7c0';
    case 'other':
      return 'white';
    case 'mix':
      return '#B8860B';
    // Trucks/Trailers
    case 'PGL':
      return 'green';
    case 'TGL':
      return '#9932CC';
    case 'truck':
      return 'green';
    case 'trailer':
      return '#9932CC';
    case 'forklift':
      return '#e66e19';
    case 'OMR':
      return '#darkgray';
    case 'AED':
      return '#9932CC';
    case 'bank_approve':
      return '#4dc3ff';
    case 'final_reviewed':
      return '#067975';
    case 'bank_reject':
      return 'red';
    default:
      return 'green';
  }
};

export const choseCheckStatusColor = (value) => {
  switch (value) {
    case 'checked':
      return 'green';
    case 'prechecked':
      return '#3F50B5';
  }
};
export const erd_brd_status = {
  estimate: 'purple',
  actual: '#3498db',
};

export const recordManager = async ({
  data,
  type,
  setTableRecords,
  tableRecords,
  selectedItems,
  setSelectedItems,
  setTotalItems,
  totalItems,
  apiUrl,
  deleteReason = null,
  setDeleteReason = null,
}) => {
  try {
    const dataArray = Array.isArray(data) ? data : [data];
    if (type == 'add') {
      setTableRecords([...dataArray, ...tableRecords]);
    } else if (type == 'update') {
      setTableRecords((tableRecords) =>
        tableRecords.map((row) => {
          const match = dataArray.find((item) => item.id === row.id);
          return match ? match : row;
        }),
      );
    } else if (type == 'delete') {
      let ids = selectedItems.map((item) => item.id);
      const endpoint = `${apiUrl}/${ids}`;

      const config = deleteReason
        ? { data: { deleted_reason: deleteReason } }
        : {};

      const { data } = await axios.delete(endpoint, config);

      if (data.result) {
        const remainingRecords = tableRecords.filter(
          (row) => !ids.includes(row.id),
        );
        setSelectedItems([]);
        setTotalItems(totalItems - ids.length);
        setTableRecords(remainingRecords);
        setDeleteReason('');
        toast.success(data?.message || 'Records deleted successfully!');
      } else {
        toast.warn(data?.message);
      }
    }
  } catch (error) {
    console.error(error);
    toast.error('Something went wrong!');
  }
};

let timer: NodeJS.Timeout | null = null;
let prevent = false;

export const copyORViewFun = ({ getSingleRow, copy, id, display, sx = {} }) => {
  return (
    <AppTooltip title={'Double click to copy clipboard'}>
      <Box
        onClick={() => {
          prevent = false;
          clearTimeout(timer);
          timer = setTimeout(() => {
            if (!prevent) {
              getSingleRow(id);
            }
          }, 250);
        }}
        onDoubleClick={() => {
          clearTimeout(timer);
          prevent = true;
          navigator.clipboard.writeText(copy);
          toast.success('Copy clipboard');
        }}
        style={{
          color: '#2196f3',
          fontWeight: 'bold',
          fontSize: '10px',
          cursor: 'pointer',
        }}
        sx={sx}
      >
        {display}
      </Box>
    </AppTooltip>
  );
};

export const countVehicleCosts = (
  item,
  title_charge_visible = true,
  towing_charge_visible = true,
) => {
  //const towing_cost = item?.towing_cost || 0; // Default to 0 if undefined
  const dismantal_cost = item?.dismantal_cost || 0; // Default to 0 if undefined
  const ship_cost = item?.ship_cost || 0; // Default to 0 if undefined
  const other_cost = item?.other_cost || 0; // Default to 0 if undefined
  const storage_cost = item?.pgl_storage_costs || 0; // Default to 0 if undefined
  const total_vehicle_charges =
    item?.vehicle_charges && item?.vehicle_charges.length > 0
      ? item.vehicle_charges.reduce((acc, charge) => acc + charge.amount, 0)
      : 0;

  let sTitle = 0;
  if (title_charge_visible) {
    sTitle = item?.title_charge || 0;
  }
  let towing_cost = 0;
  if (towing_charge_visible) towing_cost = item?.towing_cost || 0;

  return (
    +towing_cost +
    +dismantal_cost +
    +ship_cost +
    +sTitle +
    +other_cost +
    +storage_cost +
    +total_vehicle_charges
  );
};

export const getInvoicePayments = (payments) => {
  let totalAmountApplied = 0;
  payments?.forEach((payment) => {
    totalAmountApplied += parseFloat(payment.amount_applied);
  });

  return totalAmountApplied;
};

export const setInvoicePayments = (payments) => {
  if (typeof payments === 'number') {
    return payments;
  } else {
    return getInvoicePayments(payments ?? []);
  }
};

export const static_locations = [
  { id: 1, name: 'Savannah, GA' },
  { id: 2, name: 'Houston, TX' },
  { id: 4, name: 'Los Angeles, CA' },
  { id: 5, name: 'New Jersey, NJ' },
  { id: 6, name: 'Baltimore, MD' },
  { id: 9, name: 'Jacksonville, FL' },
  { id: 11, name: 'Chicago' },
];

/* export const yard_locations = [
  {
    id: 2,
    name: 'Aladdin, NJ',
    address: '271 Emmet St. Newark, NJ 07114',
    phone: '************/************',
  },
  {
    id: 4,
    name: 'Honeybee, CA',
    address: '7815 SOMERSET BLVD. PARAMOUNT CA, 90723',
    phone: '************',
  },
  {
    id: 6,
    name: 'SOTBY, NJ',
    address: '221 FOUNDRY ST, Newark, NJ 07105',
    phone: '************ ',
  },
  {
    id: 7,
    name: 'DYNAMIC EXPORT, CA',
    address: '825 Alpha St, Duarte CA, 91010',
    phone: '************',
  },

  {
    id: 16,
    name: 'Platinum Auto Export',
    address: '797 Alpha st, Duarte, CA 91010',
    phone: '(*************',
  },

  {
    id: 20,
    name: 'INDIGO Cars - NJ',
    address: '3 Cass St, Keyport, NJ 07735',
    phone: '************',
  },

  {
    id: 22,
    name: 'Ocean Cargo',
    address: '11237 PHILADELPHIA RD, WHITE MARSH, MD, 21162',
    phone: '************',
  },

  {
    id: 25,
    name: 'PAC LOGISTIC',
    address: '340 W Compton Blvd, Gardena, CA',
    phone: '************',
  },

  {
    id: 26,
    name: 'PGL NJ YARD',
    address: '530 Duncan Ave, Jersey City, NJ 07306',
    phone: '************',
  },
]; */

export const formatCurrency = (amount) => {
  if (Number.isNaN(amount)) `$ 0`;
  const USDollar = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });
  return USDollar.format(amount);
};
