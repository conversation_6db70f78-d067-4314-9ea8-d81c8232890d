import { contextProvider } from '@/contexts/ProfileContext';
import { useContext } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import DashboardIcon from '@mui/icons-material/Dashboard';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
import { DASHBOARD } from '../leftSideMenu/Permissions';

export const HeaderInfoShipmentTrash = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/shipment/all',
      name: 'Shipment',
      icon: <DirectionsBoatIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Trash',
      icon: <RestoreFromTrashIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const shipmentPendingTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'container_number',
    label: 'Container Number',
    align: 'right',
  },
  {
    id: 'booking_id',
    label: 'Booking ID',
    // align: 'right',
  },
  {
    id: 'company',
    label: 'Company',
    wrap: true,
    width: 200,
    pdfWidth: 100,
    sortColumnName: 'companies.name',
    align: 'left',
  },
  {
    id: 'status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'from',
    label: 'From',
  },
  {
    id: 'to',
    label: 'To',
  },
  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];

export const shipmentTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'container_number',
    label: 'Container Number',
    align: 'right',
  },
  {
    id: 'booking_id',
    label: 'Booking ID',
    // align: 'right',
  },
  {
    id: 'company',
    label: 'Company',
    wrap: true,
    width: 200,
    pdfWidth: 100,
    sortColumnName: 'companies.name',
    align: 'left',
  },
  {
    id: 'status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'from',
    label: 'From',
  },
  {
    id: 'to',
    label: 'To',
  },
  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_by_confirm',
    label: 'Confirm Deleted By',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];
