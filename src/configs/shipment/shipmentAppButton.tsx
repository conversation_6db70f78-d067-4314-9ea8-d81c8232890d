import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import { Badge, Box, Button, IconButton } from '@mui/material';
import PrintIcon from '@mui/icons-material/Print';
import DescriptionIcon from '@mui/icons-material/Description';
import ReceiptIcon from '@mui/icons-material/Receipt';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import DocumentScannerIcon from '@mui/icons-material/DocumentScanner';
import axios from '@/lib/axios';
import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange';
import AdfScannerIcon from '@mui/icons-material/AdfScanner';
import JSZip from 'jszip';
import { formatDate } from '@/configs/vehicles/configs';
export const appButton = ({
  selectedItems,
  setPrintForm,
  setOpenConfirm,
  tab,
  mainTab,
  showAll = true,
  CHECK_TO_ANY,
  FINAL_CHECK_TO_ANY,
  STATUS,
  showChangePlStatusButton = false,
  showChangePendingArrivalButton = false,
  handDuplicate,
  fetchOne = (id) => id,
  setPrintData = (data) => data,
  setOpenPrint,
  setOpenTransaction,
  checkTransactionPerm = false,
  setReleaseMulti = (id) => id,
  releaseMulti = false,
  setOpenPlStatusConfirm = null,
  setOpenPendingArrival = null,
}) => {
  const fetchPrintData = async () => {
    try {
      setOpenPrint(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setPrintData(res?.data?.data);
      }
    } catch (error) {
      setOpenPrint(false);
    }
  };
  const fetchPrintDataRelease = async () => {
    try {
      setOpenPrint(true);
      let ids = selectedItems.map((item) => item.id);
      const res = await axios.get(`containers/multiple/${ids}`);
      if (res?.status == 200) {
        setPrintData(res?.data?.data);
      }
    } catch (error) {
      setOpenPrint(false);
    }
  };

  const fetchPrintDataReleaseMulti = async () => {
    try {
      setReleaseMulti(true);
      let ids = selectedItems.map((item) => item.id);
      const res = await axios.get(`containers/release-document/${ids}`);
      const zip = new JSZip();
      res.data.forEach((fileData) => {
        const uint8Array = new Uint8Array(fileData.buffer.data);
        zip.file(fileData.name, uint8Array);
      });
      zip.generateAsync({ type: 'blob' }).then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = blobUrl;
        a.download = `Release Document ${formatDate(new Date())}.zip`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(blobUrl);
      });
      setReleaseMulti(false);
    } catch (error) {
      console.error('Error fetching or processing data:', error);
      setReleaseMulti(false);
    }
  };
  const multiReleaseCompanyIds = [
    158, //UNITED UNSTOPPABLE CAR AUCTION L L C
    577, //UNITED UNSTOPPABLE CAR AUCTION L L C OMAN
    806, //UNITED UNSTOPPABLE CAR AUCTION L L C Poti Georgia
    1078, //Galaxy Worldwide Shipping - Jebel Ali
    1173, //Galaxy Worldwide Shipping - Other destinations
    332, // Noor Al Jabal Used Cars tr LLC
    259, //Noor Al Shams Used Card tr LLC
  ];
  const isMultiReleaseCompany = multiReleaseCompanyIds.includes(
    selectedItems[0].company_id,
  );
  const isSingleSelection = selectedItems.length === 1;
  const isSameCompany = selectedItems.every((item) => {
    if (multiReleaseCompanyIds.includes(selectedItems[0].company_id)) {
      // Treat Jabal and Shams as the same company
      return [332, 259].includes(item.company_id)
        ? true
        : multiReleaseCompanyIds.includes(item.company_id);
    }
    return item.company_id === selectedItems[0].company_id;
  });
  const isSpecificCompany = selectedItems.some((item) =>
    [332, 259].includes(item.company_id),
  );
  const isExactThirteenContainers = selectedItems.length <= 12;

  return selectedItems.length > 0 ? (
    <Box display="flex" alignItems="center" key={'main'}>
      {selectedItems.length > 1 && (
        <AppTooltip key={'release_document'} title={'Release Document Print'}>
          <Button
            onClick={() => fetchPrintDataReleaseMulti()}
            loading={releaseMulti}
            variant="text"
            color="secondary"
            size="small"
          >
            <AdfScannerIcon />
          </Button>
        </AppTooltip>
      )}

      {/* company_id = 158 related to united company and we need to print release document multiple for it */}
      {(isSingleSelection || (isSameCompany && isMultiReleaseCompany)) &&
      tab === 'on_the_way' &&
      (isSingleSelection || !isSpecificCompany || isExactThirteenContainers) ? (
        <AppTooltip key={'release_document'} title={'Release Document Print'}>
          <IconButton
            color="secondary"
            onClick={() => {
              fetchPrintDataRelease();
              setPrintForm('release_document');
            }}
          >
            <DocumentScannerIcon />
          </IconButton>
        </AppTooltip>
      ) : (
        ''
      )}
      {selectedItems.length >= 1 && (
        <>
          {showChangePendingArrivalButton && (
            <AppTooltip title={'Change Pending Arrival'}>
              <Badge
                sx={{ fontSize: '5px' }}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                className="cBadge"
              >
                <IconButton
                  sx={{ color: '#FF9800' }}
                  onClick={() => {
                    setOpenPendingArrival(true);
                  }}
                >
                  <PublishedWithChangesIcon />
                </IconButton>
              </Badge>
            </AppTooltip>
          )}
        </>
      )}
      {selectedItems.length == 1 ? (
        <>
          {showChangePlStatusButton && (
            <AppTooltip title={'Change USA Status'}>
              <Badge
                sx={{ fontSize: '5px' }}
                badgeContent={'USA'}
                color="primary"
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                className="cBadge"
              >
                <IconButton
                  color="success"
                  onClick={() => {
                    setOpenPlStatusConfirm(true);
                  }}
                >
                  <PublishedWithChangesIcon />
                </IconButton>
              </Badge>
            </AppTooltip>
          )}
          {handDuplicate ? (
            <AppTooltip key={'Duplicate_shipment'} title={'Duplicate Shipment'}>
              <IconButton
                color="secondary"
                onClick={() => {
                  handDuplicate();
                }}
              >
                <FileCopyIcon />
              </IconButton>
            </AppTooltip>
          ) : (
            <></>
          )}
          <AppTooltip key={'custom_from'} title={'Custom Form Print '}>
            <IconButton
              color="secondary"
              onClick={() => {
                setPrintForm('custom_from');
                fetchPrintData();
              }}
            >
              <PrintIcon />
            </IconButton>
          </AppTooltip>

          <AppTooltip
            key={'bill_of_loading_print'}
            title={'Bill Of Loading Print'}
          >
            <IconButton
              color="secondary"
              onClick={() => {
                fetchPrintData();
                setPrintForm('bill_of_loading');
              }}
            >
              <DescriptionIcon />
            </IconButton>
          </AppTooltip>
          <AppTooltip key={'dock_receipt'} title={'Dock Receipt Print'}>
            <IconButton
              color="secondary"
              onClick={() => {
                fetchPrintData();
                setPrintForm('dock_receipt');
              }}
            >
              <ReceiptIcon />
            </IconButton>
          </AppTooltip>
        </>
      ) : (
        <></>
      )}
      {changeStatusShow(tab, mainTab) &&
      checkPermissions(
        tab,
        mainTab,
        CHECK_TO_ANY,
        FINAL_CHECK_TO_ANY,
        STATUS,
      ) ? (
        <AppTooltip key={'changeShipmentsStatusAll'} title={'Change Status'}>
          <IconButton color="primary" onClick={() => setOpenConfirm(true)}>
            <PublishedWithChangesIcon />
          </IconButton>
        </AppTooltip>
      ) : (
        ''
      )}
      {selectedItems.length == 1 &&
        !selectedItems[0].transaction_at &&
        checkTransactionPerm && (
          <AppTooltip title={'Add Transaction'}>
            <Badge
              sx={{ fontSize: '5px' }}
              badgeContent={'Tx'}
              color="primary"
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              className="cBadge"
            >
              <IconButton
                color="success"
                onClick={() => {
                  if (setOpenTransaction) {
                    setOpenTransaction(true);
                  }
                }}
              >
                <CurrencyExchangeIcon />
              </IconButton>
            </Badge>
          </AppTooltip>
        )}
    </Box>
  ) : showAll ? (
    <AppTooltip key={'changeShipmentsStatus'} title={'Change Status'}>
      <IconButton color="primary" onClick={() => setOpenConfirm(true)}>
        <PublishedWithChangesIcon />
      </IconButton>
    </AppTooltip>
  ) : (
    <></>
  );
};

const changeStatusShow = (tab: string, mainTab) => {
  switch (tab) {
    case 'all':
      if (mainTab == 'checked' || 'clearance' || 'at_loading') return true;
      else return false;

    case 'draft_check':
      return false;
    case 'title_archive':
      return false;
    case 'pending_archive':
      return false;
    default:
      return true;
  }
};
const checkPermissions = (
  tab,
  mainTab,
  CHECK_TO_ANY,
  FINAL_CHECK_TO_ANY,
  STATUS,
) => {
  if (tab == 'final_checked') {
    return FINAL_CHECK_TO_ANY;
  } else if (mainTab == 'checked') {
    return CHECK_TO_ANY;
  } else {
    return STATUS;
  }
};
