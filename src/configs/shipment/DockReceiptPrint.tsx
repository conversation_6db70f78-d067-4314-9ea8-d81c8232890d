import { Box } from '@mui/system';
import { forwardRef } from 'react';
import {
  Table,
  TableCell,
  TableRow,
  TableBody,
  Typography,
  Grid,
} from '@mui/material';
import Image from 'next/image';
import logo from '@/public/static/images/logo-500.webp';
import { $format, formatDate, handelColor } from '../vehicles/configs';
import Head from 'next/head';
import DockReceiptImage from './DockReceiptImage';

const DockReceiptPrint = forwardRef((props: any, ref: any) => {
  const { data } = props;

  if (!data) {
    return (
      <Box
        ref={ref}
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        No Content to print
      </Box>
    );
  }

  const printStyles = {
    td1: {
      boxSizing: 'border-box',
      border: '1px solid #0a0a0a',
      textAlign: 'left',
      padding: '0 3px',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },
    n_units_load: {
      padding: '0 3px',
      boxSizing: 'border-box',
      border: '1px solid #0a0a0a',
      borderTop: 'none',
      textAlign: 'center',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },
  };

  const a4Width = '21cm';
  const a4Height = '29.7cm';

  const PrintRow = ({ cells, sx = {} }) => (
    <TableRow sx={sx}>
      {cells.map((row, index) => (
        <TableCell
          key={index}
          {...(row.className ? { className: row.className } : {})}
          sx={row.sx || printStyles.td1}
          {...(row.rowSpan ? { rowSpan: row.rowSpan } : {})}
          {...(row.colSpan ? { colSpan: row.colSpan } : {})}
        >
          <Box display="flex" p="2px" sx={{ color: 'black' }}>
            {row.label && (
              <Typography
                sx={{
                  fontSize: row.stylePara ? 14 : 14,
                  fontWeight: row.stylePara ? 'bold' : 'normal',
                }}
              >
                {row.label}
              </Typography>
            )}
            {row.value}
          </Box>
        </TableCell>
      ))}
    </TableRow>
  );

  const vehicleChunks = data?.vehicles?.length
    ? [...Array(Math.ceil(data.vehicles.length / 4)).keys()].map((i) =>
        data.vehicles.slice(i * 4, i * 4 + 4),
      )
    : [[]];

  const totalPages = vehicleChunks.length;

  return (
    <>
      <Head>
        <title>Dock Receipt</title>
      </Head>
      <Box ref={ref}>
        {vehicleChunks.map((vehicleChunk, pageIndex) => (
          <Box
            key={pageIndex}
            width={a4Width}
            minHeight={a4Height}
            p={2}
            id={`print-page-${pageIndex}`}
            sx={{
              background: 'white',
              pageBreakAfter: pageIndex === totalPages - 1 ? 'auto' : 'always',
              pageBreakInside: 'avoid',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              color: 'black',
            }}
          >
            {/* Header */}
            <Box
              sx={{
                fontSize: '18px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                mb: 2,
              }}
            >
              <Image src={logo} height="50" alt="logo" />
              &nbsp;&nbsp;&nbsp; PGL Dock Receipt
            </Box>

            <Table>
              <TableBody>
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'BOOKING#: ',
                      value: `${data?.bookings?.booking_number ?? ''}${
                        data?.booking_suffix ? `-${data?.booking_suffix}` : ''
                      }`,
                      className: 'td1 w-50',
                    },
                    {
                      stylePara: true,
                      label: 'Customer: ',
                      value: data?.companies?.name ?? '',
                      className: 'td1 w-50',
                      colSpan: 2,
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'POL: ',
                      value: data?.bookings?.vessels?.locations?.name ?? '',
                    },
                    {
                      stylePara: true,
                      label: 'POD: ',
                      value: data?.bookings?.destinations?.name ?? '',
                    },
                    {
                      stylePara: true,
                      label: 'CONTAINER#:',
                      value: data?.container_number ?? '',
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'ETD:',
                      value: data?.bookings?.vessels?.etd
                        ? formatDate(data?.bookings?.vessels?.etd)
                        : '',
                    },
                    {
                      stylePara: true,
                      label: 'ETA:',
                      value: data?.bookings?.eta
                        ? formatDate(data?.bookings?.eta)
                        : '',
                    },
                    {
                      stylePara: true,
                      label: 'SEAL NO:',
                      value: data?.seal_number ?? '',
                    },
                  ]}
                />
              </TableBody>
            </Table>

            {/* Vehicle List */}
            <Table>
              <TableBody>
                <PrintRow
                  cells={[
                    {
                      sx: printStyles.n_units_load,
                      value: (
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            width: '100%',
                          }}
                        >
                          {data?.no_units_load}
                        </Box>
                      ),
                      colSpan: 9,
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      sx: {
                        ...printStyles.n_units_load,
                        py: 0,
                        textAlign: 'left',
                        px: 1,
                        pb: 1,
                      },
                      value: (
                        <Box sx={{ mt: 1 }}>
                          <Grid container spacing={0.5}>
                            {vehicleChunk.map((vehicle, i) => (
                              <Grid
                                key={i}
                                sx={{
                                  borderBottom:
                                    i < vehicleChunk.length - 1
                                      ? '1px solid #ccc'
                                      : 'none',
                                  pb: 0.5,
                                }}
                                size={12}
                              >
                                <Box
                                  sx={{
                                    display: 'flex',
                                    gap: 1,
                                    alignItems: 'center',
                                  }}
                                >
                                  <Box sx={{ width: 240, maxWidth: 240 }}>
                                    <DockReceiptImage
                                      cover_photo={vehicle.cover_photo}
                                    />
                                  </Box>
                                  <Box sx={{ color: 'black', width: '100%' }}>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        HAT#
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                      >
                                        {vehicle?.hat_number}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        DESC
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                        fontWeight={600}
                                      >
                                        {`${vehicle.year} ${vehicle.make} ${vehicle?.model} ${vehicle?.color}`}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        LOT#
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                        fontWeight={600}
                                      >
                                        {vehicle.lot_number}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        VIN#
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                      >
                                        <span>{vehicle?.vin.slice(0, -8)}</span>
                                        <b style={{ fontSize: 18 }}>
                                          {vehicle?.vin.slice(-8)}
                                        </b>
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        Note
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                        fontWeight={600}
                                      >
                                        {vehicle?.customer_remark}
                                      </Typography>
                                    </Box>
                                    {data?.company_id !== 408 && (
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          gap: 1,
                                          alignItems: 'center',
                                        }}
                                      >
                                        <Typography
                                          variant="h5"
                                          fontSize={16}
                                          width={60}
                                          fontWeight={600}
                                        >
                                          Price
                                        </Typography>
                                        <Typography
                                          variant={'body1'}
                                          style={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            fontSize: 12,
                                            color: 'white',
                                            backgroundColor: handelColor(
                                              vehicle?.price,
                                            ),
                                            padding: '1px 6px',
                                            borderRadius: 4,
                                          }}
                                        >
                                          {$format(vehicle?.price)}
                                        </Typography>
                                      </Box>
                                    )}
                                    <Box
                                      sx={{
                                        display: 'flex',
                                        gap: 1,
                                        alignItems: 'center',
                                      }}
                                    >
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={60}
                                        fontWeight={600}
                                      >
                                        Com
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                      >
                                        {vehicle?.companies?.name}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </Box>
                              </Grid>
                            ))}
                          </Grid>
                        </Box>
                      ),
                      colSpan: 9,
                    },
                  ]}
                />
              </TableBody>
            </Table>

            {/* Footer with instruction and page number */}
            <Box
              sx={{
                mt: 1,
                minHeight: 60,
                border: '1px solid #000',
                justifyContent: 'space-between',
                alignItems: 'center',
                color: 'red',
                fontSize: 12,
                px: 1,
              }}
            >
              <b>Instruction:&nbsp;</b>
              {data?.loading_instruction || 'No loading instruction provided.'}
            </Box>

            <Box
              sx={{
                flexShrink: 60,
                mt: 1,
                color: 'black',
                fontSize: 12,
                display: 'flex',
                justifyContent: 'flex-end',
                width: '100%',
              }}
            >
              Page {pageIndex + 1} of {totalPages}
            </Box>
          </Box>
        ))}
      </Box>
      <style>
        {`
          @media print {
            @page {
              size: A4;
              margin: 0;
            }
            html, body {
              font-size: 16px;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              background: white;
            }
            #print-page-0, #print-page-1, #print-page-2 {
              page-break-inside: avoid;
              break-inside: avoid;
            }
          }
        `}
      </style>
    </>
  );
});

export default DockReceiptPrint;
