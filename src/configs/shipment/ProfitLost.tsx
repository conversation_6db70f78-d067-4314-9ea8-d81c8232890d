import DashboardIcon from '@mui/icons-material/Dashboard';
import QueryStatsIcon from '@mui/icons-material/QueryStats';

export const HeaderInfo = {
  breadcrumbs: [
    {
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    },
    {
      href: 'false',
      name: 'Profit/Loss Report',
      icon: <QueryStatsIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ],
};

export const tableColumns = function (reportType: ProfitLossReportType) {
  let tableColumns: any = [
    {
      id: 'booking_number',
      label: 'Booking #',
      pdfWidth: 120,
      sortColumnName: 'bookings.booking_number',
      align: 'left',
    },
    {
      id: 'container_number',
      label: 'Container #',
      pdfWidth: 120,
      sortColumnName: 'container_number',
      align: 'left',
    },
    {
      id: 'company',
      label: 'Com',
      pdfWidth: 120,
      width: '20px',
      headerTextAlign: 'left',
      sortColumnName: 'companies.name',
      align: 'left',
    },
    {
      id: 'shipline',
      label: 'Shipline',
      sortColumnName: 'bookings.vessels.steamshiplines.name',
      align: 'left',
    },
    {
      id: 'etd',
      label: 'ETD',
      sortColumnName: 'bookings.vessels.etd',
      align: 'left',
    },
    {
      id: 'eta',
      label: 'ETA',
      sortColumnName: 'bookings.eta',
      align: 'left',
    },
    {
      id: 'pol',
      label: 'POL',
      align: 'left',
      sortColumnName: 'bookings?.vessels?.locations?.name',
    },
    {
      id: 'pod',
      label: 'POD',
      align: 'left',
      sortColumnName: 'bookings?.destinations?.name',
    },
    {
      id: 'yard_location',
      label: 'Yard Location',
    },
    {
      id: 'load_type',
      label: 'Load Type',
    },
    {
      id: 'status',
      label: 'Status',
    },
    {
      id: 'prelim_pl_status',
      label: 'Charge Status',
    },
  ];

  if (
    reportType == ProfitLossReportType.OVERALL ||
    reportType == ProfitLossReportType.USA
  ) {
    tableColumns.push(
      {
        id: 'booking_cost',
        label: 'BKG',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'loading_cost',
        label: 'Ldg',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'shipping_charged',
        label: 'Rate',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'shipping_pl',
        label: 'R-P&L',
        width: '30px',
        isProfit: true,
        headerTextAlign: 'center',
        align: 'right',
        sortable: false,
      },
      {
        id: 'towing_cost',
        label: 'TowCo',
        width: '15px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'towing_charge',
        label: 'TowCh',
        width: '15px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'towing_pl',
        label: 'TowP',
        width: '30px',
        isProfit: true,
        headerTextAlign: 'center',
        align: 'right',
        sortable: false,
      },
      {
        id: 'title_cost',
        label: 'TitCo',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'title_charge',
        label: 'TitCh',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'title_pl',
        label: 'TitPL',
        width: '30px',
        isProfit: true,
        headerTextAlign: 'center',
        align: 'right',
        sortable: false,
      },
      {
        id: 'dismantle_cost',
        label: 'DismtCo',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'dismantle_charge',
        label: 'DismtCh',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'dismantle_pl',
        label: 'DismtPL',
        width: '30px',
        isProfit: true,
        headerTextAlign: 'center',
        align: 'right',
        sortable: false,
      },
      {
        id: 'gross_pl',
        label: 'GrosP&L',
        width: '35px',
        isProfit: true,
        headerTextAlign: 'center',
        align: 'right',
        sortable: false,
      },
      {
        id: 'detention',
        label: 'DET',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'damage_cost',
        label: 'DMG',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'inspection_pol',
        label: 'INSPE',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'others',
        label: 'OTH',
        sortColumnName: 'container_costs.others',
        width: '10px',
        headerTextAlign: 'left',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'booking_tds_cost',
        label: 'TDSCo',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'additional_ch',
        label: 'AddCH',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'additional_co',
        label: 'AddCO',
        width: '15px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'additional_pl',
        label: 'AddPL',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'invoice_status',
        label: 'INV Status',
        sortable: false,
      },
      {
        id: 'invoice_amount',
        label: 'INV',
        sortColumnName: 'invoices.invoice_amount',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'invoice_balance',
        label: 'Bal-L',
        width: '10px',
        headerTextAlign: 'center',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'net_pl',
        label: 'NP&L',
        width: '20px',
        headerTextAlign: 'center',
        isProfit: true,
        maxWidth: '50px',
        align: 'right',
        sortable: false,
      },
      {
        id: 'usa_pl',
        label: 'UsaPL',
        width: '20px',
        headerTextAlign: 'center',
        isProfit: true,
        maxWidth: '50px',
        align: 'right',
        sortable: false,
      },
      {
        id: 'pl_status',
        label: 'Cost Status',
      },
    );
  }

  if (
    reportType === ProfitLossReportType.OVERALL ||
    reportType === ProfitLossReportType.UAE
  ) {
    tableColumns.push(
      {
        id: 'clearance_costs',
        label: 'CLN',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'clearance_costs_ch',
        label: 'CRate',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'clearance_costs_pl',
        label: 'CPL',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'delivery_order',
        label: 'DoCo',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'delivery_order_ch',
        label: 'DoRate',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'delivery_order_pl',
        label: 'DoPL',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'tran_cost',
        label: 'TranC',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'tran_ch',
        label: 'TranCH',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'tran_pl',
        label: 'TranPL',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'gross_pl_uae',
        label: 'UaeGrossPL',
        align: 'right',
        sortable: false,
      },
      {
        id: 'com_bi_co',
        label: 'ComBCo',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'com_bi_ch',
        label: 'ComBCH',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'com_bi_pl',
        label: 'ComBPL',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'invoice_status_uae',
        label: 'INV Status',
        sortable: false,
      },
      {
        id: 'invoiced_uae',
        label: 'INV UAE',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'uae_pl',
        label: 'UaePL',
        isProfit: true,
        align: 'right',
        sortable: false,
      },
      {
        id: 'uae_pl_status',
        label: 'Uae PL Status',
      },
    );
  }

  if (reportType === ProfitLossReportType.OVERALL) {
    tableColumns.push({
      id: 'total_pl_customer',
      label: 'P&L',
      isProfit: true,
      align: 'right',
      sortable: false,
    });
  }
  return tableColumns;
};

export const tableColumnsViewSingle = function () {
  return [
    {
      id: 'vehicle_description',
      label: 'Vehicle Description',
    },
    {
      id: 'vin',
      label: 'Vin Number',
    },
    {
      id: 'dismantal_costs',
      label: 'Dismantle Chg',
    },
    {
      id: 'dismantle_costs',
      label: 'Dismantle Cst',
    },
    {
      id: 'storage_cost',
      label: 'Storage Cst',
    },
    {
      id: 'title_charge',
      label: 'Title Chg',
    },
    {
      id: 'title_cost',
      label: 'Title Cst',
    },
    {
      id: 'towing_cost',
      label: 'Tow Chg',
    },
    {
      id: 'tow_amount',
      label: 'Tow Cst',
    },
    {
      id: 'damage_cost',
      label: 'Damage',
    },
  ];
};

export const mixNewColumns = [
  {
    id: 'tcc',
    label: 'TCC',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'att_insp',
    label: 'Att/Insp',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'clr',
    label: 'Clr-R',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'tcr',
    label: 'TCR',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'clp',
    label: 'CL-PL',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'vat_duty_cost',
    label: 'V&D-Co',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'vat_duty_ch',
    label: 'V&D-Ch',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
  {
    id: 'vat_duty_pl',
    label: 'V&D-PL',
    isProfit: true,
    align: 'right',
    sortable: false,
  },
];

export const filterContentShipments = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return [
    {
      title: 'ID Filtering',
      items: [
        {
          name: 'company_id',
          label: 'Company',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=companies&ids=',
          keyName: 'name',
        },
        {
          name: 'bookings.port_of_discharge',
          label: 'Port of Destination or Port of Discharge ',
          type: 'autocomplete',
          url: '/destinations?id',
          keyName: 'name',
        },
        {
          name: 'bookings.vessels.port_of_loading',
          label: 'Port of Loading ',
          type: 'autocomplete',
          url: '/locations?id',
          keyName: 'name',
        },
      ],
    },
    {
      title: 'Data',
      items: [
        {
          name: 'status',
          label: 'Container Status',
          type: 'checkbox',
          items: [
            'pending',
            'at_loading',
            'on_the_way',
            'arrived',
            'reserved',
            'checked',
            'final_checked',
            'at_the_dock',
            'clearance',
          ],
        },
        {
          name: 'invoice_status',
          label: 'Invoice Status',
          type: 'checkbox',
          items: ['pending', 'open', 'past_due', 'paid'],
        },
        {
          name: 'prelim_pl_status',
          label: 'Preliminary',
          type: 'checkbox',
          items: ['reviewed'],
        },
        {
          name: 'pl_status',
          label: 'USA',
          type: 'checkbox',
          items: ['reviewed'],
        },
        {
          name: 'uae_pl_status',
          label: 'UAE',
          type: 'checkbox',
          items: ['reviewed'],
        },
      ],
    },
    {
      title: 'Date Range',
      items: [
        {
          name: 'bookings.vessels.etd',
          label: 'ETD',
          type: 'date_range',
        },
        {
          name: 'bookings.eta',
          label: 'ETA',
          type: 'date_range',
        },
        {
          name: 'prelim_pl_at',
          label: 'Charge Status Reviewed',
          type: 'date_range',
        },
        {
          name: 'pl_at',
          label: 'Cost Status Reviewed',
          type: 'date_range',
        },
        {
          name: 'uae_pl_at',
          label: 'UAE Status Reviewed',
          type: 'date_range',
        },
      ],
    },
  ];
};

export const profitLossTabs = function (reportType: ProfitLossReportType) {
  if (
    reportType == ProfitLossReportType.USA ||
    reportType == ProfitLossReportType.UAE
  ) {
    if (reportType == ProfitLossReportType.USA) {
      return [
        { value: 'all_usa_full', name: 'All Full' },
        { value: 'all_usa_mix', name: 'All Mix' },
        { value: 'have_book_load_usa', name: 'Pending Full' },
        { value: 'have_book_load_reviewed_usa', name: 'Reviewed Full' },
        { value: 'only_mix_containers_usa', name: 'Pending Mix' },
        { value: 'only_mix_containers_reviewed_usa', name: 'Reviewed Mix' },
        { value: 'before_etd_2025_usa', name: 'Before Etd 2025' },
      ];
    } else {
      return [
        { value: 'all_uae_full', name: 'All Full' },
        { value: 'all_uae_mix', name: 'All Mix' },
        { value: 'have_book_load_uae', name: 'Pending Full' },
        { value: 'have_book_load_reviewed_uae', name: 'Reviewed Full' },
        { value: 'only_mix_containers_uae', name: 'Pending Mix' },
        { value: 'only_mix_containers_reviewed_uae', name: 'Reviewed Mix' },
        { value: 'before_etd_2025_uae', name: 'Before Etd 2025' },
      ];
    }
  }
  return [
    { value: 'overall_full', name: 'All Full' },
    { value: 'overall_mix', name: 'All Mix' },
    { value: 'have_book_load', name: 'Pending Full' },
    { value: 'have_book_load_reviewed', name: 'Reviewed Full' },
    { value: 'only_mix_containers', name: 'Pending Mix' },
    { value: 'only_mix_containers_reviewed', name: 'Reviewed Mix' },
    { value: 'overall_before_etd_2025', name: 'Before Etd 2025' },
  ];
};

export const viewPLProfiles = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const updatePLProfiles = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const clearanceCharges = [
  'port_handling',
  'inspection_charges',
  'doc_attestation',
  'custom_duty',
  'wash_fine_charges',
  'port_storage',
  'demurrage',
  'vcc',
  'detention_charges',
  'terminal_handling_charges',
  'damage_charges',
  'hazardous_handling_import_charges',
  'additional_charges',
  'repairing_cost_charges',
  'local_service_charges',
  'bill_of_entry',
  'vat',
  'handling_fee',
  'unloading',
  'consignee_charges',
  'crune_charges',
  'export_services_fees',
  'other_charges',
  'country_of_origin_charges',
];

export const clearanceCosts = [
  'port_handling_pgl_cost',
  'inspection_pgl_cost',
  'doc_attestation_pgl_cost',
  'terminal_handling_pgl_cost',
  'custom_pgl_cost',
  'wash_find_pgl_cost',
  'port_storage_pgl_cost',
  'additional_pgl_cost',
  'demurrage_pgl_cost',
  'repairing_pgl_cost',
  'vcc_pgl_cost',
  'damage_pgl_cost',
  'detention_pgl_cost',
  'vat_pgl_cost',
  'country_of_origin_pgl_cost',
];

export const mixVatDutyCosts = ['vat_pgl_cost', 'custom_pgl_cost'];

export const mixVatDutyCharges = ['vat', 'custom_duty'];

export const deliveryCharges = ['delivery_order_amount'];

export const deliveryCosts = ['delivery_pgl_cost'];

export const transportationCharges = ['e_token', 'transporter_charges'];

export const transportationCosts = [
  'fuel_pgl_cost',
  'tip_pgl_cost',
  'crane_pgl_cost',
  'token_pgl_cost',
  'charges_on_truck_pgl_cost',
  'taxi_truck_pgl_cost',
];

export const containerOthCosts = ['others', 'demurrage', 'customs_origin'];

export const mixInvoiceCharges = [
  'clearance',
  'freight',
  'tow_amount',
  'vat_and_custom',
];

export const attesInspCharges = ['inspection_charges', 'attestation_fee'];

export const mixShipRateCharges = ['freight'];

export const uaeCurrencyRate = 3.685;

export enum ProfitLossReportType {
  USA = 'usa',
  UAE = 'uae',
  OVERALL = 'overall',
}

export type TabFilterBy = {
  allUsaFull: Boolean;
  allUsaMix: Boolean;
  allUaeFull: Boolean;
  allUaeMix: Boolean;
  haveBookLoadCost: Boolean;
  haveBookLoadCostReviewed: Boolean;
  onlyMixContainers: Boolean;
  onlyMixContainersReviewed: Boolean;
  beforeEtd2025: Boolean;
};
