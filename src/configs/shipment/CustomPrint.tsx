import { Box } from '@mui/system';
import { forwardRef, useState } from 'react';
import {
  Table,
  TableCell,
  TableRow,
  TableBody,
  Typography,
  Link,
  CircularProgress,
  Button,
} from '@mui/material';
import Image from 'next/image';
import logo from '@/public/static/images/logo-500.webp';
import moment from 'moment';
import AutoAwesomeMotionIcon from '@mui/icons-material/AutoAwesomeMotion';
import Head from 'next/head';
import axios from '@/lib/axios';
import LinkIcon from '@mui/icons-material/Link';

const CutomePrint = forwardRef((props: any, ref: any) => {
  const { data } = props;
  const [loading, setLoading] = useState(false);
  const [isPdfLoading, setIsPdfLoading] = useState(false);
  const downloadCombinedTitles = async ({ route }) => {
    try {
      if (route === 'combined-pdf') {
        setIsPdfLoading(true);
      } else {
        setLoading(true);
      }
      const response = await axios.get(`containers/${route}/${data?.id}`, {
        responseType: 'blob',
      });
      setIsPdfLoading(false);
      setLoading(false);
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${data?.id}-combined-titles.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      setIsPdfLoading(false);
      setLoading(false);
    }
  };

  if (data?.length == 0) {
    return (
      <>
        <Head>
          <title>Custom Form</title>
        </Head>
        <Box
          ref={ref}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          No Content to print
        </Box>
      </>
    );
  }
  const printStyles: { [key: string]: any } = {
    table: {
      fontFamily: '"Trebuchet MS", Arial, Helvetica, sans-serif',
      borderSpacing: '0',
      fontSize: '14px',
      width: '21cm',
      color: 'black',
    },
    td1: {
      boxSizing: 'border-box',
      border: '2px solid #0a0a0a',
      textAlign: 'left',
      paddingLeft: '3px',
      padding: '3px',
      paddingRight: '3px',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },
    p: { color: 'black', fontWeight: 'bold' },
  };
  const cDate = (d) =>
    moment(d).isValid() ? moment(d).utc().format('YYYY-MM-DD') : '';
  let weight = 0;
  const sumWeight = (d) => {
    weight = weight + +d;
  };
  let total = 0;
  //@ts-ignore
  const sumMoney = (d) => {
    total = total + +d;
  };
  const a4Width = '21cm';
  const a4Height = '29.7cm';

  const PrintRow = ({ cells }) => (
    <TableRow>
      {cells.map((row, index) => (
        <TableCell
          key={index}
          sx={printStyles.td1}
          rowSpan={row.rowSpan}
          colSpan={row.colSpan}
          style={row.center ? { textAlign: 'center' } : {}}
        >
          <Typography
            sx={printStyles.p}
            style={row.style ? { fontSize: 12 } : {}}
          >
            {row.label}
          </Typography>
          {row?.value}
        </TableCell>
      ))}
    </TableRow>
  );

  return (
    <>
      <Head>
        <title>Custom Form</title>
      </Head>
      <Box
        ref={ref}
        width={a4Width}
        height={a4Height}
        p={5}
        pl={2.6}
        id="print"
        sx={{ background: 'white' }}
      >
        <Box
          sx={{
            fontSize: '18px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
          }}
          mb="30px"
        >
          <Image src={logo} height="40" alt="logo" />
          &nbsp;&nbsp;&nbsp;{' '}
          <Box component="p" sx={{ color: 'black' }}>
            PGL Custom Form
          </Box>
        </Box>
        <Table>
          <TableBody>
            <PrintRow
              cells={[
                {
                  label: 'SHIPPER / EXPORTER',
                  value: (
                    <>
                      {data?.shipping_documents?.shipper_exporter}
                      <br />
                      {data?.shipping_documents?.shipper_street_address ?? ''}
                      <br />
                      {data?.shipping_documents?.shipper_city ?? ''},
                      {data?.shipping_documents?.shipper_state ?? ''},
                      {data?.shipping_documents?.shipper_zip_code ?? ''}
                      <br />
                      {data?.shipping_documents?.shipper_email_address ?? ''}
                      <br />
                      Phone:
                      {data?.shipping_documents?.shipper_phone_number ?? ''}
                      <br />
                      Fax:
                      {data?.shipping_documents?.shipper_fax_number ?? ''}
                    </>
                  ),
                  rowSpan: 3,
                  colSpan: 4,
                },
                {
                  style: true,
                  label: 'BOOKING NUMBER',
                  value: `${data?.bookings?.booking_number}${
                    data?.booking_suffix ? `-${data?.booking_suffix}` : ''
                  }`,
                  colSpan: 2,
                },
                {
                  style: true,
                  label: 'BILL OF LOADING NO.',
                  value: data?.bill_of_loading_number,
                  colSpan: 4,
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  rowSpan: 1,
                  colSpan: 6,
                  label: (
                    <Box component="span" sx={{ fontSize: 13 }}>
                      VESSEL / VOYAGE# / STEAMSHIP LINE / FLAG
                    </Box>
                  ),
                  value: `${data?.bookings?.vessels?.name ?? ''}/${
                    data?.voyage_number ?? ''
                  }#/
                ${data?.bookings?.vessels?.steamshiplines?.name ?? ''}/${
                  data?.flag ?? ''
                }`,
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  colSpan: 2,
                  value: (
                    <>
                      COUNTRY OF ORIGIN
                      <br /> {data?.place_receipt ?? ''}
                    </>
                  ),
                },
                {
                  colSpan: 4,
                  value: (
                    <>
                      PLACE OF RECEIPT
                      <br />
                      {data?.country_origin ?? ''}
                    </>
                  ),
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  value: (
                    <>
                      CONSIGNEE
                      <br />
                      {data?.companies?.consignee ?? ''} <br />
                      {data?.companies?.consignee_street ?? ''}
                      <br />
                      P. O. Box Number:
                      {data?.companies?.consignee_box ?? ''}
                      <br />
                      {data?.companies?.consignee_poc ?? ''},
                      {data?.companies?.consignee_email ?? ''}
                      <br />
                      Phone:
                      {data?.companies?.consignee_phone ?? ''}
                      <br />
                      {data?.companies?.consignee_city ?? ''},
                      {data?.companies?.consignee_zip_code ?? ''},
                      {data?.companies?.consignee_country ?? ''}
                    </>
                  ),

                  rowSpan: 2,
                  colSpan: 4,
                },
                {
                  style: true,
                  label: 'PORT OF LOADING',
                  value: data?.bookings?.vessels?.locations?.name ?? '',
                  colSpan: 2,
                },
                {
                  style: true,
                  label: 'PORT OF DISCHARGE',
                  value: data?.bookings?.destinations?.name,
                  colSpan: 4,
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  style: true,
                  colSpan: 2,
                  label: 'ETD AT PORT OF LOADING',
                  value: cDate(data?.bookings?.vessels?.etd),
                },
                {
                  style: true,
                  colSpan: 4,
                  label: 'ETA AT PORT OF DISCHARGE',
                  value: cDate(data?.bookings?.eta),
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  value: (
                    <>
                      NOTIFY PARTY
                      <br />
                      {data?.companies?.notify_party ?? ''}
                      <br />
                      {data?.companies?.notify_street ?? ''}
                      <br />
                      P. O. Box Number: {data?.companies?.notify_box ?? ''}
                      <br />
                      {data?.companies?.notify_city ?? ''},
                      {data?.companies?.notify_zip ?? ''},
                      {data?.notify_country ?? ''}
                      <br />
                      {data?.companies?.notify_poc ?? ''},
                      {data?.companies?.notify_email ?? ''} - Phone:$
                      {data?.companies?.notify_phone ?? ''}
                    </>
                  ),
                  colSpan: 4,
                },
                {
                  style: true,
                  label: 'EXPORT REFERENCES NUMBER:',
                  value: data?.invoice_number ?? '',
                  colSpan: 6,
                },
              ]}
            />
            <PrintRow
              cells={[
                { colSpan: 1, value: 'MARKS & NUMBERS' },
                {
                  colSpan: 9,
                  center: true,
                  value: `Description of Packages and Goods`,
                },
              ]}
            />

            <PrintRow
              cells={[
                {
                  colSpan: 1,
                  rowSpan: 12,
                  value: (
                    <>
                      CONTAINER NO.: <br />
                      {data?.container_number ?? ''}
                      <br />
                      Container Size/Type:
                      <br />
                      {data?.bookings?.size ?? ''}
                      <br />
                      SEAL Number:
                      <br />
                      {data?.seal_number ?? ''}
                      <br />
                      AES ITN Number:
                      <Box
                        sx={{
                          minWidth: 100,
                          maxWidth: 210,
                          overflowWrap: 'break-word',
                        }}
                      >
                        {data?.aes_itn_number ?? ''}
                      </Box>
                      SCAC Code:
                      <br />
                      {data?.bookings?.vessels?.scac_code ?? ''}
                      <br />
                    </>
                  ),
                },
              ]}
            />

            <PrintRow
              cells={[
                {
                  colSpan: 9,
                  value: (
                    <Typography sx={{ fontSize: '22px' }} align="center">
                      {data?.no_units_load
                        ? data?.no_units_load + ' Listed below'
                        : ''}
                    </Typography>
                  ),
                },
              ]}
            />

            <PrintRow
              cells={[
                { value: `#` },
                { value: `Description` },
                { value: `VIN` },
                { value: `Title Number` },
                { value: `Title State` },
                { value: `Weight KGs` },
                { value: `Value $` },
                { value: `DOC` },
                { value: `A.Link` },
              ]}
            />

            {data?.vehicles?.map((data, index) => (
              <PrintRow
                key={index}
                cells={[
                  { value: index + 1 },
                  {
                    value: `${data?.year ?? ''} ${data?.make ?? ''} ${
                      data?.model ?? ''
                    }`,
                  },
                  { value: data?.vin ?? '' },
                  { center: true, value: data?.title_number ?? '' },
                  { value: data?.title_state ?? '' },
                  {
                    value: (
                      <>
                        {data?.weight ? sumWeight(data.weight) : ''}
                        <Typography style={{ margin: '7px 0px' }}>
                          {data?.weight}
                        </Typography>
                      </>
                    ),
                  },
                  { value: data?.price ?? '' },
                  {
                    center: true,
                    value: (
                      <Link
                        href={data?.vehicle_document_link}
                        target="_blank"
                        color="inherit"
                      >
                        <LinkIcon
                          sx={{
                            color: data?.vehicle_document_link
                              ? '#3f50b5 !important'
                              : 'grey !important',
                          }}
                        />
                      </Link>
                    ),
                  },
                  {
                    center: true,
                    value: (
                      <Link
                        href={data?.auction_invoice}
                        target="_blank"
                        color="inherit"
                      >
                        <LinkIcon
                          sx={{
                            color: data?.auction_invoice
                              ? '#3f50b5 !important'
                              : 'grey !important',
                          }}
                        />
                      </Link>
                    ),
                  },
                ]}
              />
            ))}

            <PrintRow
              cells={[
                { colSpan: 5, value: 'TOTAL KGs & VALUE IN US$' },
                { center: true, value: weight },
                { center: true, value: `$ ${total}` },
                {
                  center: true,
                  value: (
                    <Box
                      sx={{ cursor: 'pointer' }}
                      onClick={
                        !loading
                          ? () => {
                              downloadCombinedTitles({
                                route: 'combined-titles',
                              });
                            }
                          : null
                      }
                    >
                      <Button
                        sx={{ px: 0.5, minWidth: 'unset' }}
                        loading={loading}
                        loadingIndicator={
                          <CircularProgress color="primary" size={16} />
                        }
                      >
                        <AutoAwesomeMotionIcon
                          sx={{
                            color: '#3f50b5',
                            visibility: loading ? 'hidden' : 'visible',
                          }}
                        />
                      </Button>
                    </Box>
                  ),
                },
                {
                  center: true,
                  value: (
                    <Box
                      sx={{ cursor: 'pointer' }}
                      onClick={
                        !isPdfLoading
                          ? () => {
                              downloadCombinedTitles({ route: 'combined-pdf' });
                            }
                          : null
                      }
                    >
                      <Button
                        sx={{ px: 0.5, minWidth: 'unset' }}
                        loading={isPdfLoading}
                        loadingIndicator={
                          <CircularProgress color="primary" size={16} />
                        }
                      >
                        <AutoAwesomeMotionIcon
                          sx={{
                            color: '#3f50b5',
                            visibility: isPdfLoading ? 'hidden' : 'visible',
                          }}
                        />
                      </Button>
                    </Box>
                  ),
                },
              ]}
            />
          </TableBody>
        </Table>
        <style>
          {`
            @media print {
              @page {
                size: A4; /* DIN A4 standard, Europe */
                margin: 0;
              }
              html, body {
                font-size: 12px;
              }
            }
        `}
        </style>
      </Box>
    </>
  );
});
export default CutomePrint;
