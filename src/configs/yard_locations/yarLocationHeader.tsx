import DashboardIcon from '@mui/icons-material/Dashboard';
import * as z from 'zod';
import YardIcon from '@mui/icons-material/Yard';
import { useContext } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '@/configs/leftSideMenu/Permissions';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: 'false',
      name: '',
      icon: <SettingsSuggestIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Yard Locations',
      icon: <YardIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const HeaderInfoLoadingCompanyRates = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: 'false',
      name: '',
      icon: <SettingsSuggestIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Loading Company Rates',
      icon: <YardIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const yardLocationsHeaders = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'name',
    label: 'Name',
  },
  {
    id: 'username',
    label: 'Username',
  },
  {
    id: 'status',
    label: 'Status',
  },
  {
    id: 'location_name',
    label: 'Locations Name',
    sortColumnName: 'locations.name',
  },
  {
    id: 'emails',
    label: 'Emails',
  },
  {
    id: 'created_by',
    label: 'Created By',
  },
  {
    id: 'created_at',
    label: 'Created At',
  },
  {
    id: 'updated_by',
    label: 'Updated By',
  },
  {
    id: 'updated_at',
    label: 'Updated At',
  },
];

export const loadingCompanyRateHeaders = [
  {
    id: 'id',
    label: 'ID',
    align: 'left',
  },
  {
    id: 'name',
    label: 'Name',
  },
  {
    id: 'rate_status',
    label: 'Rate Status',
  },
  {
    id: 'effective_date_from',
    label: 'Effective Date From',
    align: 'left',
  },
  {
    id: 'effective_date_to',
    label: 'Effective Date To',
    align: 'left',
  },
  {
    id: 'rate_40hc_4v',
    label: '40HC 4V',
    align: 'left',
  },
  {
    id: 'rate_40hc_3v',
    label: '40HC 3V',
    align: 'left',
  },
  {
    id: 'rate_40hc_halfcut',
    label: '40HC Halfcut',
    align: 'left',
  },
  {
    id: 'rate_45hc_4v',
    label: '45HC 4V',
    align: 'left',
  },
  {
    id: 'rate_45hc_3v',
    label: '45HC 3V',
    align: 'left',
  },
  {
    id: 'rate_45hc_halfcut',
    label: '45HC Halfcut',
    align: 'left',
  },
];

export const loadingCompanyPendingRateHeaders = [
  {
    id: 'id',
    label: 'ID',
    align: 'left',
  },
  {
    id: 'name',
    label: 'Name',
  },
  {
    id: 'rate_status',
    label: 'Rate Status',
  },
  {
    id: 'rate_40hc_4v',
    label: '40HC 4V',
    align: 'left',
  },
  {
    id: 'rate_40hc_3v',
    label: '40HC 3V',
    align: 'left',
  },
  {
    id: 'rate_40hc_halfcut',
    label: '40HC Halfcut',
    align: 'left',
  },
  {
    id: 'rate_45hc_4v',
    label: '45HC 4V',
    align: 'left',
  },
  {
    id: 'rate_45hc_3v',
    label: '45HC 3V',
    align: 'left',
  },
  {
    id: 'rate_45hc_halfcut',
    label: '45HC Halfcut',
    align: 'left',
  },
];

export const filterContentYardLocations = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'location_id',
        label: 'Location',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=locations&id=',
        keyName: 'name',
      },
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'loginable.status',
        label: 'Account Status',
        type: 'checkbox',
        items: [
          'active',
          'deactive',
          // 'new_register'
        ],
      },
    ],
  },
  {
    title: 'Date Range Filtering',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];

export const filterContentLoadingCompanyRates = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'yard_location.name',
        label: 'Yard Location Name',
        type: 'textfield',
      },
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'status',
        label: 'Rate Status',
        type: 'checkbox',
        items: ['pending', 'active'],
      },
      {
        name: 'rate_40hc_3v',
        label: '40HC 3V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_40hc_4v',
        label: '40HC 4V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_45hc_3v',
        label: '45HC 3V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_45hc_4v',
        label: '45HC 4V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
      {
        name: 'effective_date_from',
        label: 'Effective From',
        type: 'date_range',
      },
      {
        name: 'effective_date_to',
        label: 'Effective To',
        type: 'date_range',
      },
    ],
  },
];

export const filterContentSpecificStatusLoadingCompanyRates = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'yard_location.name',
        label: 'Yard Location Name',
        type: 'textfield',
      },
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
    ],
  },
  {
    title: 'Rate Range',
    items: [
      {
        name: 'rate_40hc_3v',
        label: '40HC 3V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_40hc_4v',
        label: '40HC 4V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_45hc_3v',
        label: '45HC 3V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
      {
        name: 'rate_45hc_4v',
        label: '45HC 4V Rate',
        type: 'number_range',
        min: 'Min Amount',
        max: 'Max Amount',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      // {
      //   name: 'created_at',
      //   label: 'Created At',
      //   type: 'date_range',
      // },
      // {
      //   name: 'updated_at',
      //   label: 'Updated At',
      //   type: 'date_range',
      // },
      {
        name: 'effective_date_from',
        label: 'Effective From',
        type: 'date_range',
      },
      {
        name: 'effective_date_to',
        label: 'Effective To',
        type: 'date_range',
      },
    ],
  },
];

export const schema = z.object({
  name: z.string().refine((e) => e.length > 0, {
    message: 'Name must Not be Empty',
  }),
  location_id: z.number({
    required_error: 'Location is required',
    invalid_type_error: 'Location must be one of the Options',
  }),
  emails: z.array(z.string().email()),
  status: z.enum(['active', 'deactive']),
  bio: z.string().nullable().optional(),
  address_info: z.string().nullable().optional(),
  username: z
    .string()
    .email({ message: 'Invalid email format' })
    .min(1, { message: 'Email is required' }),
  rate_40hc_4v: z.number().nullable().optional(),
  rate_40hc_3v: z.number().nullable().optional(),
  rate_40hc_halfcut: z.number().nullable().optional(),
  rate_45hc_4v: z.number().nullable().optional(),
  rate_45hc_3v: z.number().nullable().optional(),
  rate_45hc_halfcut: z.number().nullable().optional(),
  effective_date_from: z.string().nullable().optional(),
  effective_date_to: z.string().nullable().optional(),
});

export const loadRateSchema = z.object({
  yard_location_id: z.number({
    required_error: 'Yard location is required',
    invalid_type_error: 'Yard location must be one of the Options',
  }),
  rate_40hc_4v: z.number().nullable().optional(),
  rate_40hc_3v: z.number().nullable().optional(),
  rate_40hc_halfcut: z.number().nullable().optional(),
  rate_45hc_4v: z.number().nullable().optional(),
  rate_45hc_3v: z.number().nullable().optional(),
  rate_45hc_halfcut: z.number().nullable().optional(),
  effective_date_from: z.string().nullable().optional(),
  effective_date_to: z.string().nullable().optional(),
});

export const createSchema = schema.extend({
  password: z
    .string()
    .min(6, { message: 'Password should be at least 6 characters long' })
    .optional(),
});

export const updateSchema = schema.extend({
  password: z.nullable(z.string()).optional(),
});
