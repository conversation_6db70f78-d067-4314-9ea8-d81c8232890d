import { contextProvider } from '@/contexts/ProfileContext';
import { useContext } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DashboardIcon from '@mui/icons-material/Dashboard';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
import { DASHBOARD } from '../leftSideMenu/Permissions';

export const HeaderInfoVehicleTrash = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/vehicles/all',
      name: 'Vehicles',
      icon: <DirectionsCarIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Trash',
      icon: <RestoreFromTrashIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const VehiclePendingTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'vehicle_description',
    label: 'Vehicle Description',
    wrap: true,
    sortColumnName: 'year',
    align: 'left',
  },
  {
    id: 'vin',
    label: 'VIN',
    align: 'right',
  },
  {
    id: 'lot_number',
    label: 'LOT #',
    align: 'right',
  },
  {
    id: 'carstate',
    label: 'Status',
    pdfWidth: 80,
    align: 'center',
  },
  {
    id: 'company',
    label: 'Company',
    wrap: true,
    width: 200,
    pdfWidth: 100,
    sortColumnName: 'companies.name',
    align: 'left',
  },
  {
    id: 'customer_remark',
    label: 'Customer Remark',
    pdfWidth: 120,
  },
  {
    id: 'points_of_loading',
    label: 'Point of Loading',
    pdfWidth: 80,
    sortColumnName: 'pol_locations.name',
  },

  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];

export const VehicleTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'vehicle_description',
    label: 'Vehicle Description',
    wrap: true,
    sortColumnName: 'year',
    align: 'left',
  },
  {
    id: 'vin',
    label: 'VIN',
    align: 'right',
  },
  {
    id: 'lot_number',
    label: 'LOT #',
    align: 'right',
  },
  {
    id: 'carstate',
    label: 'Status',
    pdfWidth: 80,
    align: 'center',
  },
  {
    id: 'company',
    label: 'Company',
    wrap: true,
    width: 200,
    pdfWidth: 100,
    sortColumnName: 'companies.name',
    align: 'left',
  },
  {
    id: 'customer_remark',
    label: 'Customer Remark',
    pdfWidth: 120,
  },
  {
    id: 'points_of_loading',
    label: 'Point of Loading',
    pdfWidth: 80,
    sortColumnName: 'pol_locations.name',
  },

  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_by_confirm',
    label: 'Confirm Deleted By',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];
