'use strict';
Object.defineProperty(exports, '__esModule', { value: true });
exports.cost_analysis = void 0;
exports.cost_analysis = [
  {
    id: 'vehicle_description_cost',
    label: 'Vehicle Description',
    wrap: true,
    pdfWidth: 150,
    sortColumnName: 'year',
    align: 'left',
  },
  {
    id: 'vin',
    label: 'Vin',
    pdfWidth: 120,
    align: 'right',
  },
  {
    id: 'company',
    label: 'Company',
    sortColumnName: 'customers.companies.name',
  },
  {
    id: 'container_number_cost',
    label: ' Container #',
    pdfWidth: 90,
    sortColumnName: 'containers.container_number',
    align: 'right',
  },
  {
    id: 'request_for_pickup_date',
    label: 'Tow Request Date',
  },
  {
    id: 'ready_for_pickup_date',
    label: 'Ready to Pickup',
  },
  {
    id: 'vehicle_price',
    label: 'Vehicle Cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.vehicle_price',
    align: 'center',
  },
  {
    id: 'towing_cost',
    label: 'Towing Cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.towing_cost',
    align: 'right',
  },
  {
    id: 'dismantal_cost',
    label: 'Dismental Cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.dismantal_cost',
    align: 'right',
  },
  {
    id: 'ship_cost',
    label: 'Ship Cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.ship_cost',
    align: 'right',
  },
  {
    id: 'pgl_storage_costs',
    label: 'Strg POL',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.pgl_storage_costs',
    align: 'right',
  },
  {
    id: 'title_charge',
    label: 'Title Cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.title_charge',
    align: 'right',
  },
  {
    id: 'dubai_custom_cost',
    label: 'Custom',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.dubai_custom_cost',
    align: 'right',
  },
  {
    id: 'other_cost',
    label: 'Other cost',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.other_cost',
    align: 'right',
  },
  {
    id: 'invoice_description',
    label: 'Description',
    wrap: true,
    pdfWidth: 120,
    sortColumnName: 'vehicle_costs.invoice_description',
  },
  {
    id: 'total',
    label: 'Total',
    pdfWidth: 60,
    sortable: false,
    align: 'right',
  },
  {
    id: 'sales_cost',
    label: 'Sale',
    pdfWidth: 50,
    sortColumnName: 'vehicle_costs.sales_cost',
    align: 'right',
  },
  {
    id: 'profit',
    label: 'Prof/Lose',
    pdfWidth: 60,
    sortable: false,
    align: 'right',
  },
  {
    id: 'percent_profit',
    label: 'Percent profit',
    pdfWidth: 60,
    sortable: false,
    align: 'right',
  },
  {
    id: 'storage_cost',
    label: 'PGL Strg Cst',
    pdfWidth: 60,
    sortColumnName: 'storage_cost',
    align: 'right',
  },
];
//# sourceMappingURL=costAnalysisHeader.js.map
