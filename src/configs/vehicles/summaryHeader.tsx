import { contextProvider } from '@/contexts/ProfileContext';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PlaylistAddCheckCircleIcon from '@mui/icons-material/PlaylistAddCheckCircle';
import { useContext } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../leftSideMenu/Permissions';

// export const HeaderInfoVehicleSummary = {
//   breadcrumbs: [
//     // {
//     //   href: '/',
//     //   name: 'Dashboard',
//     //   icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
//     //   key: '1'
//     // },
//     {
//       href: '/vehicles/all',
//       name: 'Vehicles',
//       icon: <DirectionsCarIcon sx={{ fontSize: '18px' }} />,
//       key: '2',
//     },
//     {
//       href: 'false',
//       name: 'Summary',
//       icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '18px' }} />,
//       key: '3',
//     },
//   ],
// };

export const HeaderInfoVehicleSummary = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/vehicles/all',
      name: 'Vehicles',
      icon: <DirectionsCarIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Summary',
      icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const tableColumnsVehicleSummary = [
  {
    id: 'name',
    label: 'Company',
  },
  {
    id: 'auction_paid_unpaid_pending',
    label: 'Auction',
    sort: (data) => {
      return (
        (data?.auction_paid || 0) +
        (data?.auction_unpaid || 0) +
        (data?.pending_auction || 0)
      );
    },
    align: 'right',
  },
  {
    id: 'pending_on_the_way',
    label: 'Pending On the way',
    align: 'right',
  },
  {
    id: 'on_the_way',
    label: 'On the way',
    align: 'right',
  },
  {
    id: 'on_hand_no_title',
    label: 'No/Title',
    align: 'right',
  },
  {
    id: 'on_hand_with_title',
    label: 'W/Title',
    align: 'right',
  },
  {
    id: 'total_titles',
    label: 'OH Total',
    align: 'right',
  },
  {
    id: 'total_on_the_way_no_with_title',
    label: 'OTW & Titles Total',
    align: 'right',
  },

  {
    id: 'halfcut',
    label: 'HalfCut/Unknown',
    align: 'center',
  },
  // {
  //   id: 'unknown_halfcut',
  //   label: 'Unknown HalfCut',
  //   align: 'right',
  // },
  {
    id: 'on_hand_with_load',
    label: 'OH with Load',
    align: 'right',
  },
  {
    id: 'shipped',
    label: 'Shipped',
    align: 'right',
  },
  {
    id: 'pending',
    label: 'Pending',
    align: 'right',
  },
  {
    id: 'total',
    label: 'Total',
    sortable: false,
    align: 'right',
  },
  {
    id: 'mix',
    label: 'Mix',
    align: 'center',
  },
  {
    id: 'complete',
    label: 'Complete',
    align: 'center',
  },
  {
    id: 'mix_halfcut',
    label: 'Mix Halfcut',
    align: 'center',
  },
  {
    id: 'complete_halfcut',
    label: 'Complete Halfcut',
    align: 'center',
  },
  {
    id: 'notes',
    label: 'Note',
  },
  {
    id: 'dest_name',
    label: 'Destinations',
  },
  {
    id: 'loading_companies',
    label: 'Loading Company',
  },
  {
    id: 'reason',
    label: 'Load Note',
  },
];
