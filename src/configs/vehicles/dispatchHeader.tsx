import { contextProvider } from '@/contexts/ProfileContext';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PlaylistAddCheckCircleIcon from '@mui/icons-material/PlaylistAddCheckCircle';
import { useContext } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../leftSideMenu/Permissions';

export const HeaderInfoDispatch = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/vehicles/dispatch/all',
      name: 'Dispatch',
      icon: <DirectionsCarIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Dispatch',
      icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const dispatchHeader = [
  {
    id: 'lot_number',
    label: 'Lot #',
  },
  {
    id: '',
    label: 'Vehicle Description',
    align: 'right',
  },
  {
    id: 'vin',
    label: 'VIN #',
    align: 'right',
  },
  {
    id: '',
    label: 'Price',
    align: 'right',
  },
  {
    id: '',
    label: 'Age',
    align: 'right',
  },
  {
    id: '',
    label: 'Damage',
    align: 'right',
  },
  {
    id: '',
    label: 'Last Update',
    align: 'right',
  },
];
