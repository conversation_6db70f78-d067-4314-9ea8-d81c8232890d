import { useLocationContext } from '@/contexts/LocationsContext';
import { fuel_type_enum, vehicle_size_enum } from './configs';

// const dispatchOptions = [
//   { name: 'Dispatch', id: 'dispatch' },
//   {
//     name: 'Self Pickup PGL',
//     id: 'self_pickup_delivered_to_pgl',
//   },
//   {
//     name: 'Self Pickup Not PGL',
//     id: 'self_pickup_not_delivered_to_pgl',
//   },
// ];
export const filterContentVehicle = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { map }: any = useLocationContext();
  const locations = map((item) => {
    return {
      name: item.name,
      id: item.id,
    };
  });
  const vehicle_size = vehicle_size_enum.map((item) => ({
    name: item.replace(/_/g, ' ').toUpperCase(),
    id: item.toLowerCase(),
  }));
  return [
    {
      title: 'ID Filtering',
      items: [
        {
          name: 'created_by',
          label: 'Created By',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=users&ids=',
          keyName: 'fullname',
        },
        {
          name: 'users_vehicles_created_byTousers.department_id',
          label: 'Created By Department',
          text: 'department(s)',
          type: 'autocomplete_with_exclude',
          url: '/autoComplete?column=name&modal=departments&ids=',
          keyName: 'name',
          excludeName: 'include_exclude_department',
          excludeLabel: 'Exclude selected departments',
        },
        {
          name: 'updated_by',
          label: 'Updated By',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=users&ids=',
          keyName: 'fullname',
        },
        {
          name: 'users_vehicles_updated_byTousers.department_id',
          label: 'Updated By Department',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=departments&ids=',
          keyName: 'name',
        },
        {
          name: 'customer_id',
          label: 'Customer',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=customers&ids=',
          keyName: 'fullname',
        },
        {
          name: 'company_id',
          label: 'Company',
          text: 'company(s)',
          type: 'autocomplete_with_exclude',
          url: '/autoComplete?column=name&modal=companies&ids=',
          keyName: 'name',
          excludeName: 'include_exclude_company',
          excludeLabel: 'Exclude selected companies',
        },
        {
          name: 'container_id',
          label: 'Container',
          type: 'autocomplete',
          url: '/autoComplete?column=container_number&modal=containers&ids=',
          keyName: 'container_number',
        },
        {
          name: 'yard_location_id',
          label: 'Yard Location',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=yard_locations&ids=',
          keyName: 'name',
        },
        {
          name: 'containers.bookings.port_of_discharge',
          label: 'Destinations',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=destinations&ids=',
          keyName: 'name',
        },
        {
          name: 'account_owner',
          label: 'Account Owner',
          type: 'checkbox',
          items: ['PGL', 'Customer'],
        },
        {
          name: 'load_type',
          label: 'Load Type',
          type: 'checkbox',
          items: ['mix', 'full'],
        },
        {
          name: 'fuel_type',
          label: 'Fuel Type',
          type: 'checkbox',
          items: fuel_type_enum.map((item) => item.toLowerCase()),
        },
        {
          name: 'halfcut_status',
          label: 'Is Halfcut',
          type: 'null_check',
          items: [
            { label: 'Yes', value: 'half_cut' },
            { label: 'No', value: 'completed' },
          ],
        },
        {
          name: 'point_of_loading',
          label: 'Point of Loading',
          type: 'autocomplete',
          url: '',
          options: locations,
          keyName: 'name',
        },
        {
          name: 'vehicle_size',
          label: 'Vehicle size',
          type: 'autocomplete',
          url: '',
          options: vehicle_size,
          keyName: 'name',
        },
      ],
    },
    {
      title: 'Data',
      items: [
        {
          name: 'carstate',
          label: 'Status',
          type: 'checkbox',
          items: [
            'added_by_customer',
            'on_the_way',
            'on_hand_no_title',
            'on_hand_with_title',
            'on_hand_with_load',
            'shipped',
            'pending',
            'archived',
            /* 'at_port',
            'arrived', */
            'auction_paid',
            'auction_unpaid',
            'pending_auction',
            'pending_on_the_way',
          ],
        },
        {
          name: 'eta_status',
          label: 'ETA Status',
          type: 'checkbox',
          items: ['actual', 'estimate'],
        },
        {
          name: 'tax_status',
          label: 'Tax Status',
          type: 'checkbox',
          items: ['No', 'Yes', 'Refund'],
        },
        {
          name: 'inspection',
          label: 'Inspection',
          type: 'checkbox',
          items: ['No', 'Yes'],
        },
        {
          name: 'ach',
          label: 'ACH Payment Approved',
          type: 'checkbox',
          items: ['Yes', 'No', 'Not set'],
        },
        {
          name: 'is_key_present',
          label: 'Is Key present',
          type: 'checkbox',
          items: ['Yes', 'No', 'Not set'],
        },
        {
          name: 'vehicles_with_cover_photos',
          label: 'Vehicles with Cover Photos',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'vehicles_with_photos',
          label: 'Vehicles with Photos',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'vehicles_with_delivery_photos',
          label: 'Vehicles with Delivery Photos',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'receiver_name',
          label: 'Receiver Name',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'vehicles_with_auction_photos',
          label: 'Vehicles with Auction Photos',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'vehicles_with_aes_filling_link',
          label: 'Vehicles with AES Filling Link',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'dispatch_type',
          label: 'Dispatch Type',
          type: 'checkbox',
          items: [
            'dispatch',
            'self_pickup_delivered_to_pgl',
            'self_pickup_not_delivered_to_pgl',
          ],
        },
        {
          name: 'vehicles_with_auction_invoice_link',
          label: 'Vehicles with BOS Link',
          type: 'checkbox',
          items: ['Yes', 'No'],
        },
        {
          name: 'lot_number',
          label: 'Lot Number',
          type: 'textfield',
        },
        {
          name: 'vin',
          label: 'Vin',
          type: 'textfield',
        },
        {
          name: 'price',
          label: 'Price',
          min: 'Start',
          max: 'End',
          type: 'number_range',
        },
        {
          name: 'dispatch_department',
          label: 'For Dispatch Department',
          type: 'checkbox',
          items: ['yes'],
        },
        {
          name: 'is_scrap',
          label: 'Is Scrap',
          type: 'checkbox',
          items: ['yes'],
        },
        {
          name: 'receiver_name',
          label: 'Receiver Name',
          type: 'null_check',
          items: [
            { label: 'Without', value: 'null' },
            { label: 'With', value: 'not_null' },
          ],
        },
        {
          name: 'make',
          label: 'Make',
          type: 'textfield',
        },
        {
          name: 'model',
          label: 'Model',
          type: 'textfield',
        },
        {
          name: 'year',
          label: 'Year',
          type: 'textfield',
        },
      ],
    },
    {
      title: 'Date Range',
      items: [
        {
          name: 'purchased_at',
          label: 'Purchase Date',
          type: 'date_range',
        },
        {
          name: 'created_at',
          label: 'Report Date to PGL',
          type: 'date_range',
        },
        {
          name: 'payment_date',
          label: 'Payment Date',
          type: 'date_range',
        },
        {
          name: 'eta',
          label: 'ETA',
          type: 'date_range',
        },
        {
          name: 'pickup_date',
          label: 'Pick Up Date',
          type: 'date_range',
        },
        {
          name: 'deliver_date',
          label: 'Deliver Date',
          type: 'date_range',
        },
        {
          name: 'updated_at',
          label: 'Updated At',
          type: 'date_range',
        },
      ],
    },
  ];
};

export const filterContentVehicleStatus = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { map }: any = useLocationContext();
  const locations = map((item) => {
    return {
      name: item.name,
      id: item.id,
    };
  });
  return [
    {
      title: 'ID Filtering',
      items: [
        {
          name: 'created_by',
          label: 'Created By',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=users&id=',
          keyName: 'fullname',
        },
        {
          name: 'users_vehicles_created_byTousers.department_id',
          label: 'Created By Department',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=departments&ids=',
          keyName: 'name',
        },
        {
          name: 'updated_by',
          label: 'Updated By',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=users&id=',
          keyName: 'fullname',
        },
        {
          name: 'users_vehicles_updated_byTousers.department_id',
          label: 'Updated By Department',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=departments&ids=',
          keyName: 'name',
        },
        {
          name: 'customer_id',
          label: 'Customer',
          type: 'autocomplete',
          url: '/autoComplete?column=fullname&modal=customers&id=',
          keyName: 'fullname',
        },
        {
          name: 'company_id',
          label: 'Company',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=companies&id=',
          keyName: 'name',
        },
        {
          name: 'container_id',
          label: 'Container',
          type: 'autocomplete',
          url: '/autoComplete?column=container_number&modal=containers&id=',
          keyName: 'container_number',
        },
        {
          name: 'yard_location_id',
          label: 'Yard Location',
          type: 'autocomplete',
          url: '/autoComplete?column=name&modal=yard_locations&id=',
          keyName: 'name',
        },
        {
          name: 'account_owner',
          label: 'Account Owner',
          type: 'checkbox',
          items: ['PGL', 'Customer'],
        },
        {
          name: 'load_type',
          label: 'Load Type',
          type: 'checkbox',
          items: ['mix', 'full'],
        },
        {
          name: 'point_of_loading',
          label: 'Point of Loading',
          type: 'autocomplete',
          url: '',
          options: locations,
          keyName: 'name',
        },
      ],
    },
    {
      title: 'Data',
      items: [
        {
          name: 'lot_number',
          label: 'Lot Number',
          type: 'textfield',
        },
        {
          name: 'vin',
          label: 'Vin',
          type: 'textfield',
        },
        {
          name: 'price',
          label: 'Price',
          min: 'Start',
          max: 'End',
          type: 'number_range',
        },
        {
          name: 'make',
          label: 'Make',
          type: 'textfield',
        },
        {
          name: 'model',
          label: 'Model',
          type: 'textfield',
        },
        {
          name: 'year',
          label: 'Year',
          type: 'textfield',
        },
        {
          name: 'dispatch_department',
          label: 'For Dispatch Department',
          type: 'checkbox',
          items: ['Dispatch Department'],
        },
      ],
    },
    {
      title: 'Date Range',
      items: [
        {
          name: 'purchased_at',
          label: 'Purchase Date',
          type: 'date_range',
        },
        {
          name: 'created_at',
          label: 'Report Date to PGL',
          type: 'date_range',
        },
        {
          name: 'payment_date',
          label: 'Payment Date',
          type: 'date_range',
        },
        {
          name: 'deliver_date',
          label: 'Deliver Date',
          type: 'date_range',
        },
        {
          name: 'updated_at',
          label: 'Updated At',
          type: 'date_range',
        },
      ],
    },
  ];
};

export const filterContentVehicleCostAnalysis = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'users_vehicles_created_byTousers.department_id',
        label: 'Created By Department',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=departments&ids=',
        keyName: 'name',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'users_vehicles_updated_byTousers.department_id',
        label: 'Updated By Department',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=departments&ids=',
        keyName: 'name',
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'price',
        label: 'Price',
        min: 'Start',
        max: 'End',
        type: 'number_range',
      },
      {
        name: 'dispatch_department',
        label: 'For Dispatch Department',
        type: 'checkbox',
        items: ['Dispatch Department'],
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },

      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];

export const filterContentHalfVehicle = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'users_vehicles_created_byTousers.department_id',
        label: 'Created By Department',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=departments&ids=',
        keyName: 'name',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'users_vehicles_updated_byTousers.department_id',
        label: 'Updated By Department',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=departments&ids=',
        keyName: 'name',
      },
      {
        name: 'company_id',
        label: 'Company',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=companies&id=',
        keyName: 'name',
      },
      {
        name: 'load_type',
        label: 'Load Type',
        type: 'checkbox',
        items: ['mix', 'full'],
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'halfcut_status',
        label: 'Half Cut Status',
        type: 'checkbox',
        items: ['ready_to_halfcut', 'ready_to_ship', 'shipped', 'otw_halfcut'],
      },
      {
        name: 'dispatch_department',
        label: 'For Dispatch Department',
        type: 'checkbox',
        items: ['Dispatch Department'],
      },
      {
        name: 'lot_number',
        label: 'Lot Number',
        type: 'textfield',
      },
      {
        name: 'vin',
        label: 'Vin',
        type: 'textfield',
      },

      {
        name: 'make',
        label: 'Make',
        type: 'textfield',
      },
      {
        name: 'model',
        label: 'Model',
        type: 'textfield',
      },
      {
        name: 'year',
        label: 'Year',
        type: 'textfield',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },

      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];

export const filterContentHalfCutVehicleSummary = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'company_id',
        label: 'Company',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=companies&id=',
        keyName: 'name',
      },

      {
        name: 'yard_location_id',
        label: 'Yard Location',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=yardLocations&id=',
        keyName: 'name',
      },
      {
        name: 'point_of_loading',
        label: 'Point of Loading',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=locations&id=',
        keyName: 'name',
      },
      {
        name: 'halfcut_status',
        label: 'Half Cut Status',
        type: 'checkbox',
        items: ['ready_to_halfcut', 'ready_to_ship', 'shipped', 'otw_halfcut'],
      },
      {
        name: 'load_type',
        label: 'Load Type',
        type: 'checkbox',
        items: ['mix', 'full'],
      },
    ],
  },

  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },

      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];
