import { Box } from '@mui/system';
import { forwardRef, useEffect, useState } from 'react';
import {
  Table,
  TableCell,
  TableRow,
  TableBody,
  Grid,
  Typography,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Image from 'next/image';
import logo from '@/public/static/images/logo-500.webp';
import axios from '@/lib/axios';
import { formatDate, formatDateFromNow } from '../vehicles/configs';
import { countVehicleCosts, getInvoicePayments } from '../configs';

const multiInvoicePrint = forwardRef((props: any, ref: any) => {
  const [profile, setProfile] = useState<any>();
  const [isCheck, setIsCheck] = useState(false);

  const CustomTableCell = styled(TableCell)(({ theme }) => ({
    padding: theme.spacing(1), // Adjust the padding value as needed
  }));
  const printStyles: { [key: string]: any } = {
    tableCol: {
      float: 'right',
      marginTop: '-6em',
      border: '1px solid #000',
      color: 'black',
    },
    tableCellHeader: {
      height: '30px',
      background: '#ccc',
      border: '1px solid #000',
      textAlign: 'right',
      color: 'black !important',
    },
    tableCell: {
      height: '30px',
      border: '1px solid #000',
      textAlign: 'right',
      width: '120px',
      color: 'black',
    },
    blueText: {
      color: 'blue',
    },
    tab1Cell: {
      borderTop: 0,
      borderBottomWidth: '2px',
      border: '1px solid #000',
      padding: '3px',
      lineHeight: '1.42857143',
      background: '#ccc',
      color: 'black',
    },
    tab2Cell: {
      height: '30px',
      verticalAlign: 'top',
      border: '1px solid #000',
      textAlign: 'left',
      color: 'black',
    },
  };

  let dueBalance = 0;
  let invTotalAmount = 0;
  let payTotalAmount = 0;
  let totalDiscount = 0;

  useEffect(() => {
    getPGLProfile();
  }, []);

  const getPGLProfile = async () => {
    try {
      await axios.get('pgl-profile/profile').then(({ data }: any) => {
        setProfile(data?.data);
      });
    } catch (e) {
      return e;
    }
  };
  const { data } = props;

  if (!data) {
    return (
      <Box
        ref={ref}
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        No Content to print
      </Box>
    );
  }
  data.forEach((item) => {
    item?.containers?.vehicles?.map((vehicle) => {
      invTotalAmount += countVehicleCosts(
        {
          ...vehicle?.vehicle_costs,
          vehicle_charges: vehicle?.vehicle_charges,
        },
        item?.title_charge_visible ?? true,
        item?.towing_charge_visible ?? true,
      );
    });
    totalDiscount += +item?.discount;
    payTotalAmount += getInvoicePayments(item?.payments) ?? 0;
  });
  dueBalance += invTotalAmount - payTotalAmount;

  return (
    <Box
      ref={ref}
      sx={{ width: '100%', backgroundColor: 'white', color: 'black' }}
      p={2}
    >
      <Box sx={{ display: 'flex', justifyContent: 'end' }}>
        <FormControlLabel
          className="no-print"
          value="top"
          control={
            <Switch color="primary" onChange={() => setIsCheck(!isCheck)} />
          }
          label="Check if You want Bill To"
          labelPlacement="top"
        />
      </Box>
      <Box>
        <Grid container sx={{ display: 'flex', alignItems: 'center' }}>
          <Grid size={4}>
            <Image src={logo} height="70" alt="logo" />
          </Grid>
          <Grid size={4}>
            <Typography fontWeight="bold" mb={1}>
              PEACE GLOBAL LOGISTICS LLC
            </Typography>
            <Typography>
              2824 Tremont Road
              <br />
              Savannah, Georgia, 31405
              <br />
              <EMAIL>
            </Typography>
          </Grid>
          <Grid size={4}>
            <Table>
              <TableBody>
                <TableRow>
                  <CustomTableCell colSpan={2} sx={printStyles.tableCellHeader}>
                    Method Payment:
                  </CustomTableCell>
                  <TableCell sx={printStyles.tableCell}>
                    {data && data[0]?.payment_method}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <CustomTableCell colSpan={2} sx={printStyles.tableCellHeader}>
                    Payment Date:
                  </CustomTableCell>
                  <CustomTableCell sx={printStyles.tableCell}>
                    {isCheck ? '' : new Date().toLocaleDateString('en-US')}
                  </CustomTableCell>
                </TableRow>
                <TableRow>
                  <CustomTableCell colSpan={2} sx={printStyles.tableCellHeader}>
                    Invoice Amount:
                  </CustomTableCell>
                  <CustomTableCell
                    sx={
                      invTotalAmount > 0
                        ? {
                            height: '30px',
                            border: '1px solid#000',
                            textAlign: 'right',
                            width: '120px',
                            color: 'blue',
                          }
                        : {
                            height: '30px',
                            border: '1px solid#000',
                            textAlign: 'right',
                            width: '120px',
                            color: 'black',
                          }
                    }
                  >
                    $ {(invTotalAmount - totalDiscount).toFixed(2)}
                  </CustomTableCell>
                </TableRow>
                <TableRow>
                  <CustomTableCell colSpan={2} sx={printStyles.tableCellHeader}>
                    Payment Received:
                  </CustomTableCell>
                  <CustomTableCell
                    sx={
                      payTotalAmount > 0
                        ? {
                            height: '30px',
                            border: '1px solid#000',
                            textAlign: 'right',
                            width: '120px',
                            color: 'blue',
                          }
                        : {
                            height: '30px',
                            border: '1px solid#000',
                            textAlign: 'right',
                            width: '120px',
                            color: 'black',
                          }
                    }
                  >
                    $ {payTotalAmount.toFixed(2)}
                  </CustomTableCell>
                </TableRow>
              </TableBody>
            </Table>
          </Grid>
        </Grid>
      </Box>
      <br />
      <Table>
        <TableBody>
          <TableRow>
            {isCheck ? (
              <CustomTableCell
                style={{
                  background: '#ccc',
                  border: '1px solid#000',
                  textAlign: 'center',
                  width: '30%',
                  color: 'black',
                }}
              >
                Bill TO:
              </CustomTableCell>
            ) : (
              <CustomTableCell
                style={{
                  background: '#ccc',
                  border: '1px solid#000',
                  textAlign: 'center',
                  width: '30%',
                  color: 'black',
                }}
              >
                Received From:
              </CustomTableCell>
            )}
            <CustomTableCell
              style={
                invTotalAmount > 0
                  ? {
                      border: '1px solid#000',
                      color: 'blue',
                    }
                  : {
                      border: '1px solid#000',
                      color: 'black',
                    }
              }
            >
              &nbsp;&nbsp; {data[0]?.companies?.name}
            </CustomTableCell>
          </TableRow>
          {/* <TableRow>
          <CustomTableCell colSpan={2} className={classes.CustomTableCellHeader}>
            Memo:
          </CustomTableCell>
          <CustomTableCell className={classes.CustomTableCell}>&nbsp;&nbsp;</CustomTableCell>
        </TableRow> */}
        </TableBody>
      </Table>
      <br />
      <Table
        style={{
          backgroundColor: '#fff',
          color: 'black',
          border: '1px solid rgb(14, 12, 12)',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          borderCollapse: 'collapse',
          borderSpacing: '0',
        }}
      >
        <TableBody>
          <TableRow
            style={{
              height: '30px',
              border: '1px black solid',
              background: '#ccc',
              fontWeight: 'bold',
              textAlign: 'center',
              color: 'black',
            }}
          >
            {isCheck ? (
              <CustomTableCell
                colSpan={totalDiscount ? 10 : 9}
                style={{
                  textAlign: 'center',
                  color: 'black',
                }}
              >
                Invoice Report
              </CustomTableCell>
            ) : (
              <CustomTableCell
                colSpan={totalDiscount ? 10 : 9}
                style={{
                  textAlign: 'center',
                  color: 'black',
                }}
              >
                Invoice Report (Items Paid)
              </CustomTableCell>
            )}
          </TableRow>
          <TableRow
            style={{
              display: 'table-row',
              verticalAlign: 'inherit',
              borderColor: 'inherit',
            }}
          >
            <CustomTableCell sx={printStyles.tab1Cell}>No.#</CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Invoice No.
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Cargo Information
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Due Date.
            </CustomTableCell>

            {totalDiscount > 0 && (
              <CustomTableCell sx={printStyles.tab1Cell}>
                Discount
              </CustomTableCell>
            )}

            <CustomTableCell sx={printStyles.tab1Cell}>
              Invoice Amount
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Payment Received
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Balance Due
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>
              Past Due Days
            </CustomTableCell>
            <CustomTableCell sx={printStyles.tab1Cell}>Remarks</CustomTableCell>
          </TableRow>
        </TableBody>
        <TableBody>
          {data.map((invoices, index) => {
            const container = invoices.containers;
            let amount = 0;
            invoices?.containers?.vehicles?.map((vehicle) => {
              amount += countVehicleCosts(
                {
                  ...vehicle?.vehicle_costs,
                  vehicle_charges: vehicle?.vehicle_charges,
                },
                invoices?.title_charge_visible ?? true,
                invoices?.towing_charge_visible ?? true,
              );
            });
            const i = index + 1;

            return (
              <TableRow key={index}>
                <CustomTableCell sx={printStyles.tab2Cell}>{i}</CustomTableCell>
                <CustomTableCell sx={printStyles.tab2Cell}>
                  {invoices.invoice_number}
                </CustomTableCell>
                <CustomTableCell sx={printStyles.tab2Cell}>
                  Container Number: {container?.container_number} <br />
                  Booking Number: {container?.bookings?.booking_number}
                  {container?.booking_suffix
                    ? ' - ' + container?.booking_suffix
                    : ''}
                  <br />
                  Origin: {container?.bookings?.vessels?.locations?.name} <br />
                  To: {container?.bookings?.destinations?.name} <br />
                </CustomTableCell>
                <CustomTableCell sx={printStyles.tab2Cell}>
                  {formatDate(invoices?.invoice_due_date)}
                </CustomTableCell>
                {totalDiscount > 0 && (
                  <CustomTableCell
                    sx={{
                      ...printStyles.tab2Cell,
                      textAlign: 'right',
                      color: 'black',
                    }}
                  >
                    {(Number(invoices.discount) || 0).toFixed(2)}
                  </CustomTableCell>
                )}
                <CustomTableCell
                  sx={{
                    ...printStyles.tab2Cell,
                    textAlign: 'right',
                    color: 'black',
                  }}
                >
                  {amount ? amount.toFixed(2) : '0.00'}
                </CustomTableCell>
                <CustomTableCell
                  style={{}}
                  sx={{
                    ...printStyles.tab2Cell,
                    textAlign: 'right',
                    color:
                      getInvoicePayments(invoices?.payments) > 0
                        ? 'blue'
                        : 'black',
                  }}
                >
                  {getInvoicePayments(invoices?.payments)
                    ? getInvoicePayments(invoices?.payments).toFixed(2)
                    : '0.00'}
                </CustomTableCell>
                <CustomTableCell
                  sx={{
                    ...printStyles.tab2Cell,
                    textAlign: 'right',
                    color: 'black',
                  }}
                >
                  {amount
                    ? (
                        amount -
                        (getInvoicePayments(invoices?.payments) +
                          parseFloat(invoices?.discount ?? 0))
                      ).toFixed(2)
                    : '0'}
                </CustomTableCell>
                <CustomTableCell sx={printStyles.tab2Cell}>
                  {formatDateFromNow(invoices.invoice_due_date)}
                </CustomTableCell>
                <CustomTableCell sx={printStyles.tab2Cell}>
                  {invoices.description || ''}
                </CustomTableCell>
              </TableRow>
            );
          })}
          <TableRow>
            <CustomTableCell
              style={{
                width: '170px',
                background: '#ccc',
                color: 'black',
              }}
              colSpan={4}
            >
              Total Amount:
            </CustomTableCell>
            {totalDiscount > 0 && (
              <CustomTableCell
                style={{
                  textAlign: 'right',
                  border: '1px solid #000',
                  height: '30px',
                  background: '#ccc',
                  color: 'black',
                }}
              >
                ${totalDiscount.toFixed(2)}
              </CustomTableCell>
            )}

            <CustomTableCell
              style={{
                textAlign: 'right',
                border: '1px solid #000',
                height: '30px',
                background: '#ccc',
                color: 'black',
              }}
            >
              ${invTotalAmount.toFixed(2)}
            </CustomTableCell>
            <CustomTableCell
              style={{
                textAlign: 'right',
                border: '1px solid #000',
                height: '30px',
                background: '#ccc',
                // color: data[0]?.amountDue > 0 ? 'blue' : '',
                fontWeight: 'bold',
                color: 'black',
              }}
            >
              ${payTotalAmount.toFixed(2)}
            </CustomTableCell>

            <CustomTableCell
              style={{
                textAlign: 'right',
                border: '1px solid #000',
                height: '30px',
                background: '#ccc',
                color: 'black',
              }}
            >
              ${(dueBalance - totalDiscount).toFixed(2)}
            </CustomTableCell>
            <CustomTableCell
              style={{
                textAlign: 'right',
                border: '1px solid #000',
                height: '30px',
                background: '#ccc',
                color: 'black',
              }}
            ></CustomTableCell>
          </TableRow>
        </TableBody>
      </Table>
      <Box
        sx={{
          background: '#ccc',
          width: '40%',
          margin: '0 auto',
          padding: '1em',
          color: 'black',
        }}
      >
        <b>TOTAL GLOBAL LOGISTICS LLC - Chase Bank:</b>
        <br />
        Bank Name: {profile?.bank_name}
        <br />
        Account Name: {profile?.account_name}
        <br />
        Account #: {profile?.account_number}
        <br />
        Routing#: ACH & Direct deposits *********
        <br />
        Routing#: Wire transfer *********
        <br />
        SWIFT Code: {profile?.swift}
        <br />
        Bank add: 270 Park AVe New Yark, NY, 10172 - USA
        <br />
        Company add: 4701 DUCKER DR BAYTOWN, TX 77520
        <br />
        {/* {profile?.b_street}
        <br />
        {profile?.b_city}, {profile?.b_state}, {profile?.b_zip} -{' '}
        {profile?.b_country}
        <br /> */}
        {/* <Typography variant="body1">Bank Name: {profile?.bank_name}</Typography>
        <Typography variant="body1">
          Account Name: {profile?.account_name}
        </Typography>
        <Typography variant="body1">
          Account #: {profile?.account_number}
        </Typography>
        <Typography variant="body1">ABA Routing: {profile?.aba}</Typography>
        <Typography variant="body1">
          Routing No: Direct deposits and ACH *********
        </Typography>
        <Typography variant="body1">SWIFT Code: {profile?.swift}</Typography>
        <Typography variant="body1">Address: {profile?.b_street}</Typography>
        <Typography variant="body1">
          {profile?.b_city}, {profile?.b_state}, {profile?.b_zip} -{' '}
          {profile?.b_country}
        </Typography> */}
      </Box>
      <style>
        {`
          @media print {
            .no-print
            {
                display: none !important;
            }

            @page {
              size: A4; /* DIN A4 standard, Europe */
              margin: 0;
            }

            html, body {

                font-size: 13px;
            }
          }
        `}
      </style>
    </Box>
  );
});
export default multiInvoicePrint;
