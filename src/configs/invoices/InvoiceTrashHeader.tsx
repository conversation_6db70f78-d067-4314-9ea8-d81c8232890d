import { contextProvider } from '@/contexts/ProfileContext';
import { useContext } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
import { DASHBOARD } from '../leftSideMenu/Permissions';
import InterestsIcon from '@mui/icons-material/Interests';

export const HeaderInfoInvoiceTrash = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/invoices/all',
      name: 'Invoice',
      icon: <InterestsIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Trash',
      icon: <RestoreFromTrashIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const invoicePendingTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'invoice_number',
    label: 'Invoice Number',
    pdfWidth: 120,
    align: 'right',
  },
  {
    id: 'company_name',
    label: 'Company',
    sortColumnName: 'companies.name',
    pdfWidth: 120,
  },
  {
    id: 'purpose',
    label: 'Purpose',
    pdfWidth: 150,
  },
  {
    id: 'container_number',
    label: 'Container #',
    sortColumnName: 'containers.container_number',
    pdfWidth: 120,
    align: 'right',
  },

  {
    id: 'status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'invoice_date',
    label: 'Issue Date',
  },
  {
    id: 'due_date',
    label: 'Due Date',
    sortColumnName: 'containers.bookings.eta',
  },
  {
    id: 'invoice_amount',
    label: 'Invoice Amount',
    align: 'right',
  },
  {
    id: 'payment_received',
    label: 'Payment Received',
    align: 'right',
  },
  {
    id: 'received_date',
    label: 'Received Date',
  },

  {
    id: 'past_due_date',
    label: 'Past Due Days',
    sortColumnName: 'invoice_due_date',
    pdfWidth: 60,
  },
  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];

export const invoiceTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'invoice_number',
    label: 'Invoice Number',
    pdfWidth: 120,
    align: 'right',
  },
  {
    id: 'company_name',
    label: 'Company',
    sortColumnName: 'companies.name',
    pdfWidth: 120,
  },
  {
    id: 'purpose',
    label: 'Purpose',
    pdfWidth: 150,
  },
  {
    id: 'container_number',
    label: 'Container #',
    sortColumnName: 'containers.container_number',
    pdfWidth: 120,
    align: 'right',
  },

  {
    id: 'status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'invoice_date',
    label: 'Issue Date',
  },
  {
    id: 'due_date',
    label: 'Due Date',
    sortColumnName: 'containers.bookings.eta',
  },
  {
    id: 'invoice_amount',
    label: 'Invoice Amount',
    align: 'right',
  },
  {
    id: 'payment_received',
    label: 'Payment Received',
    align: 'right',
  },
  {
    id: 'received_date',
    label: 'Received Date',
  },

  {
    id: 'past_due_date',
    label: 'Past Due Days',
    sortColumnName: 'invoice_due_date',
    pdfWidth: 60,
  },
  {
    id: 'deleted_by',
    label: 'Deleted By',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
  },
  {
    id: 'deleted_by_confirm',
    label: 'Confirm Deleted By',
  },
  {
    id: 'deleted_reason',
    label: 'Deleted Reason',
  },
];
