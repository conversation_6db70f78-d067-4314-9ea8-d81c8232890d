// @ts-ignore
import { contextProvider } from '@/contexts/ProfileContext';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { useContext } from 'react';
import * as z from 'zod';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DAMAGE, DASHBOARD } from '../leftSideMenu/Permissions';
import { CarCrash } from '@mui/icons-material';

export const useDamageHeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: 'false',
      name: 'Damages',
      icon: <CarCrash sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const damagesTabs = [
  { name: 'All', value: 'all' },
  {
    name: 'Process',
    value: 'process',
    children: [
      { name: 'Under Investigation', value: 'under_investigation' },
      { name: 'In Process', value: 'in_process' },
      { name: 'Pending CA', value: 'pending_ca' },
      { name: 'Half Cut', value: 'half_cut' },
      { name: 'Forgotten', value: 'forgotten' },
      { name: 'Unloading', value: 'unloading' },
      { name: 'Dismissed', value: 'dismissed' },
    ],
  },
  { name: 'Initial Review', value: 'initial_review' },
  { name: 'Closed', value: 'closed' },
  {
    name: 'Credit',
    value: 'credit',
    children: [
      { name: 'Pre Credit', value: 'pre_credit' },
      { name: 'Credited', value: 'credited' },
      { name: 'Audit Reviewed', value: 'audit_reviewed' },
    ],
  },
  { name: 'Damage Trash', value: 'damage_trash' },
];

export const viewPermissions = {
  under_investigation: DAMAGE?.UNDER_INVESTIGATION_VIEW,
  pending_ca: DAMAGE?.PENDING_CA_VIEW,
  forgotten: DAMAGE?.FORGOTTEN_VIEW,
  pending: DAMAGE?.IN_PROCESS_VIEW,
  in_process: DAMAGE?.DISMISSED_VIEW,
  pre_credit: DAMAGE?.PRE_CREDIT_VIEW,
  credited: DAMAGE?.CREDITED_VIEW,
  audit_reviewed: DAMAGE?.AUDIT_REVIEWED_VIEW,
  unloading: DAMAGE?.UNLOADING_VIEW,
  half_cut: DAMAGE?.HALF_CUT_VIEW,
  closed: DAMAGE?.CLOSED_VIEW,
  initial_review: DAMAGE?.INITIAL_REVIEW_VIEW,
};

export const damage_type_options = [
  { id: 'Damage', label: 'Damage', color: '#6DA9DF', name: 'Damage' },
  { id: 'Missing', label: 'Missing', color: '#A2C3C8', name: 'Missing' },
  { id: 'Broken', label: 'Broken', color: '#BE7D9F', name: 'Broken' },
  { id: 'Scratch', label: 'Scratch', color: '#D8A8C0', name: 'Scratch' },
  { id: 'Dent', label: 'Dent', color: '#C27AA0', name: 'Dent' },
];

export const pol_options = [
  { id: 'GA', label: 'GA', color: '#4ABBCD' },
  { id: 'TX', label: 'TX', color: '#684EB1' },
  { id: 'CA', label: 'CA', color: '#B55D04' },
  { id: 'NJ', label: 'NJ', color: '#FF8C00' },
  { id: 'MD', label: 'MD', color: '#468191' },
  { id: 'NC', label: 'NC', color: '#FF8C00' },
  { id: 'FL', label: 'FL', color: '#FF8C00' },
];

export const claim_status_options = [
  { id: 'Un_Credited', label: 'Un Credited', color: '#B47F17' },
  { id: 'Credited', label: 'Credited', color: '#20F699' },
];

export const damage_status_options = [
  { id: 'Under_Investigation', label: 'Under Investigation', color: '#FFA500' },
  { id: 'In_process', label: 'In process', color: '#6B9FEA' },
  { id: 'Pending_CA', label: 'Pending CA', color: '#FFD700' },
  { id: 'Forgotten', label: 'Forgotten', color: '#808080' },
  { id: 'Dismissed', label: 'Dismissed', color: '#E55A5F' },
  { id: 'Initial_Review', label: 'Initial Review', color: '#9370DB' },
  { id: 'Pre_Credit', label: 'Pre Credit', color: '#59C079' },
  { id: 'Credited', label: 'Credited', color: '#20F699' },
  { id: 'Closed', label: 'Closed', color: '#20F699' },
  { id: 'UnLoading', label: 'UnLoading', color: '#E55A5F' },
  { id: 'Half_Cut', label: 'Half Cut', color: '#E55A5F' },
];

export const damage_happened_at_options = [
  { id: 'Unknown', label: 'Unknown', color: '#B4A7D5' },
  { id: 'Auction', label: 'Auction', color: '#A1C5E9' },
  { id: 'Destination', label: 'Destination', color: '#E27D66' },
  { id: 'Container', label: 'Container', color: '#Faa521' },
  { id: 'Yard', label: 'Yard', color: '#DB7F6A' },
  { id: 'Dispatch', label: 'Dispatch', color: '#C17BA0' },
  { id: 'Loaded', label: 'Loaded', color: '#6D9EEB ' },
  { id: 'UnLoading', label: 'UnLoading', color: '#6D9EEB ' },
  { id: 'CBP', label: 'CBP', color: '#881337' },
];

export const damage_detail_options = [
  { id: 'All_Over', label: 'All_Over' },
  { id: 'Antenna', label: 'Antenna' },
  { id: 'Back_Fender', label: 'Back Fender' },
  { id: 'Back_Light', label: 'Back Light' },
  { id: 'Bonnet', label: 'Bonnet' },
  { id: 'Battery', label: 'Battery' },
  { id: 'Bumper', label: 'Bumper' },
  { id: 'Catalytic_Converter', label: 'Catalytic Converter' },
  { id: 'Dashboard', label: 'Dashboard' },
  { id: 'Door', label: 'Door' },
  { id: 'Engine', label: 'Engine' },
  { id: 'Fender', label: 'Fender' },
  { id: 'Fog_Light', label: 'Fog Light' },
  { id: 'Front_Bumper', label: 'Front Bumper' },
  { id: 'Front_Door', label: 'Front Door' },
  { id: 'Front_Fender', label: 'Front Fender' },
  { id: 'Front_Grill', label: 'Front Grill' },
  { id: 'Front_windshield', label: 'Front Windshield' },
  { id: 'Glass', label: 'Glass' },
  { id: 'Grill', label: 'Grill' },
  { id: 'Head_Light', label: 'Head Light' },
  { id: 'Key', label: 'Key' },
  { id: 'Light', label: 'Light' },
  { id: 'Mirror', label: 'Mirror' },
  { id: 'Panorama', label: 'Panorama' },
  { id: 'Radar', label: 'Radar' },
  { id: 'Rear_Door', label: 'Rear Door' },
  { id: 'Rear_Glass', label: 'Rear Glass' },
  { id: 'Rear_Windshield', label: 'Rear Windshield' },
  { id: 'Roof', label: 'Roof' },
  { id: 'Roof_Pillar', label: 'Roof Pillar' },
  { id: 'Running_Panel', label: 'Running Panel' },
  { id: 'Seats', label: 'Seats' },
  { id: 'Spoiler', label: 'Spoiler' },
  { id: 'Tire', label: 'Tire' },
  { id: 'Trunk', label: 'Trunk' },
  { id: 'Underneath', label: 'Underneath' },
  { id: 'Wheel', label: 'Wheel' },
  { id: 'Windshield', label: 'Windshield' },
];

export const currency_options = [
  { id: 'USD', label: 'USD' },
  { id: 'AED', label: 'AED' },
  { id: 'OMR', label: 'OMR' },
];

export const damageHeader = [
  {
    id: 'id',
    label: 'ID',
    pdfWidth: 100,
    align: 'center',
  },
  {
    id: 'loaded_by',
    label: 'Loaded By',
    pdfWidth: 250,
  },
  {
    id: 'damage_type',
    label: 'Damage Type',
    pdfWidth: 300,
    align: 'left',
  },
  {
    id: 'email_sent_at',
    label: 'Process Damage At',
    pdfWidth: 350,
  },
  {
    id: 'case_number',
    label: 'Case Number',
    pdfWidth: 300,
  },
  {
    id: 'yards_location',
    label: 'Yard Location',
    pdfWidth: 300,
    maxWidth: 200,
  },
  {
    id: 'damage_details',
    label: 'Details',
    align: 'center',
    maxWidth: 400,
    pdfWidth: 400,
  },
  {
    id: 'auction_photos_link',
    label: 'A.Photo',
    pdfWidth: 300,
  },
  {
    id: 'photo_link',
    label: 'R.Photo',
    pdfWidth: 399,
  },
  {
    id: 'photo_link_loading',
    label: 'L.Photo',
    pdfWidth: 399,
  },

  {
    id: 'inspection_photo',
    label: 'Ins.Photo',
    pdfWidth: 400,
  },

  {
    id: 'damage_happened_at',
    label: 'Happened at',
    pdfWidth: 350,
  },
  {
    id: 'pol_locations',
    label: 'POL',
    pdfWidth: 150,
  },
  {
    id: 'destinations',
    label: 'POD',
    pdfWidth: 150,
  },
  {
    id: 'description',
    label: 'Description',
    minWidth: 460,
    maxWidth: 460,
  },
  {
    id: 'vin',
    label: 'VIN#',
    pdfWidth: 120,
  },

  {
    id: 'lot_number',
    label: 'LOT#',
    pdfWidth: 120,
  },

  {
    id: 'price',
    label: 'V.Price',
    pdfWidth: 130,
  },
  {
    id: 'company',
    label: 'Company',
    pdfWidth: 300,
  },
  {
    id: 'container_number',
    label: 'Container#',
    pdfWidth: 150,
  },
  {
    id: 'case_received_date',
    label: 'Received date',
    pdfWidth: 200,
  },
  {
    id: 'loading_date',
    label: 'Loading date',
    pdfWidth: 200,
  },
  {
    id: 'eta',
    label: 'ETA',
    pdfWidth: 150,
  },
  {
    id: 'etd',
    label: 'ETD',
    pdfWidth: 150,
  },
  {
    id: 'claim',
    label: 'Claim',
    pdfWidth: 150,
  },
  {
    id: 'confirmed',
    label: 'Confirmed',
    pdfWidth: 150,
  },
  {
    id: 'credit',
    label: 'Credited',
    pdfWidth: 150,
  },
  {
    id: 'lc_credit',
    label: 'LC.Credit',
    pdfWidth: 150,
  },
  {
    id: 'credited_at',
    label: 'Credited At',
    pdfWidth: 150,
    align: 'left',
  },
  {
    id: 'credited_by',
    label: 'Credited By',
    pdfWidth: 150,
    align: 'left',
  },
  {
    id: 'audit_reviewed_at',
    label: 'Audit Reviewed At',
    pdfWidth: 150,
    align: 'left',
  },
  {
    id: 'audit_reviewed_by',
    label: 'Audit Reviewed By',
    pdfWidth: 150,
    align: 'left',
  },
  {
    id: 'claim_status',
    label: 'Claim Status',
    pdfWidth: 300,
  },
  {
    id: 'damage_status',
    label: 'Damage Status',
    pdfWidth: 300,
  },

  {
    id: 'remark',
    label: 'Remark',
    pdfWidth: 300,
  },

  // {
  //   id: 'created_by',
  //   label: 'Created By',
  //   pdfWidth: 130,
  // },
  // {
  //   id: 'created_at',
  //   label: 'Created At',
  //   pdfWidth: 130,

  // },
  // {
  //   id: 'updated_by',
  //   label: 'Updated By',
  //   pdfWidth: 120,
  // },
  // {
  //   id: 'updated_at',
  //   label: 'Updated At',
  // },
];

export const filterContentDamages = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'vehicles.containers.loaders.some.id',
        label: 'Loaded By',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=loaders&ids=',
        keyName: 'name',
        multiple: true,
      },

      {
        name: 'vehicles.pol_locations.id',
        label: 'POL',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=locations&ids=',
        keyName: 'name',
        multiple: true,
      },
      {
        name: 'vehicles.containers.bookings.destinations.id',
        label: 'POD',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=destinations&ids=',
        keyName: 'name',
        multiple: true,
      },
      {
        name: 'vehicles.customers.companies.id',
        label: 'Company',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=companies&ids=',
        keyName: 'name',
        multiple: true,
      },
      {
        name: 'vehicles.yard_location_id',
        label: 'Yard Location',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=yard_locations&ids=',
        keyName: 'name',
        multiple: true,
      },
      {
        name: 'vehicles.containers.yard_location_id',
        label: 'Container Yard Location',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=yard_locations&ids=',
        keyName: 'name',
        multiple: true,
      },
      {
        name: 'credited_by',
        label: 'Credited By',
        type: 'autocomplete',
        url: '/users?id',
        keyName: 'fullname',
      },
      {
        name: 'credited_at',
        label: 'Credited Date:',
        type: 'date_range',
      },
    ],
  },

  {
    title: 'Damage Filtering',
    items: [
      {
        name: 'damage_type',
        label: 'Damage Type',
        type: 'autocomplete',
        options: damage_type_options,
        keyName: 'label',
      },
      {
        name: 'damage_details.some.detail',
        label: 'Damage details',
        type: 'autocomplete',
        options: damage_detail_options,
        keyName: 'label',
        multiple: true,
      },
      {
        name: 'damage_happened_at.some.happened_at',
        label: 'damage_happened_at',
        type: 'autocomplete',
        options: damage_happened_at_options,
        keyName: 'label',
        multiple: true,
      },
      {
        name: 'claim_status',
        label: 'Claim Status',
        type: 'autocomplete',
        options: claim_status_options,
        keyName: 'label',
        multiple: true,
      },
      {
        name: 'damage_status',
        label: 'Damage Status',
        type: 'autocomplete',
        options: damage_status_options,
        keyName: 'label',
        multiple: true,
      },
      {
        name: 'vehilce_damages.created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/users?id',
        keyName: 'fullname',
      },
      {
        name: 'vehilce_damages.updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/users?id',
        keyName: 'fullname',
      },
      {
        name: 'case_received_date',
        label: 'Case Received Date:',
        type: 'date_range',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'vehicles.containers.bookings.vessels.etd',
        label: 'ETD',
        type: 'date_range',
      },
      {
        name: 'vehicles.containers.bookings.eta',
        label: 'ETA',
        type: 'date_range',
      },
      {
        name: 'vehicles.containers.loading_date',
        label: 'Loading Date',
        type: 'date_range',
      },
      {
        name: 'created_at',
        label: 'Created Date:',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated Date:',
        type: 'date_range',
      },
    ],
  },
];

export const financeDamageHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'center',
  },
  {
    id: 'damage_type',
    label: 'Type',
    align: 'center',
  },
  {
    id: 'damage_details',
    label: 'Detail',
    align: 'center',
    maxWidth: 300,
  },
  {
    id: 'inspection_photo',
    label: 'Ins.Photo',
    align: 'center',
    // maxWidth: 100,
  },
  {
    id: 'damage_happened_at',
    label: 'Happened At',
    align: 'center',
  },
  {
    id: 'case_number',
    label: 'Case #',
    align: 'center',
  },
  {
    id: 'company',
    label: 'Company',
    align: 'left',
    maxWidth: 200,
  },
  {
    id: 'pol',
    label: 'POL',
    align: 'center',
  },
  {
    id: 'description',
    label: 'Description',
    align: 'left',
  },
  {
    id: 'vin',
    label: 'VIN',
    align: 'left',
  },
  {
    id: 'lot',
    label: 'LOT',
    align: 'left',
  },
  {
    id: 'credited',
    label: 'Credited',
    align: 'left',
  },
];

export const DamageTrashHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'damage_type',
    label: 'Damage Type',
    align: 'left',
  },
  {
    id: 'description',
    label: 'Vehicle Description',
    align: 'left',
  },
  // {
  //   id: 'loaders',
  //   label: 'Loaded By',
  //   align: 'left',
  // },
  {
    id: 'vin',
    label: 'VIN#',
    align: 'left',
  },
  {
    id: 'lot_number',
    label: 'LOT#',
    align: 'left',
  },
  {
    id: 'company',
    label: 'Company',
    align: 'left',
  },
  {
    id: 'pol_locations',
    label: 'POL',
    align: 'left',
  },
  {
    id: 'deleted_by',
    label: 'Deleted By',
    align: 'left',
  },
  {
    id: 'deleted_at',
    label: 'Deleted At',
    align: 'left',
  },
];

export const DamageSchema = z
  .object({
    vehicle_id: z.number({ message: 'Select a vehicle is required' }),
    damage_type: z
      .enum(damage_type_options.map((item) => item.id) as [string, ...string[]])
      .optional()
      .nullable(),
    damage_details: z
      .array(z.object({ detail: z.string() }))
      .optional()
      .nullable(),
    inspection_photo: z
      .string()
      .optional()
      .nullable()
      .refine(
        (val) =>
          val === null || val === '' || z.string().url().safeParse(val).success,
        {
          message: 'Invalid URL format',
        },
      ),
    case_received_date: z
      .union([z.string(), z.coerce.date()])
      .optional()
      .nullable(),

    credited_at: z.union([z.string(), z.coerce.date()]).optional().nullable(),
    damage_happened_at: z
      .array(z.object({ happened_at: z.string() }))
      .optional()
      .nullable(),

    claim: z
      .union([z.string(), z.number()])
      .optional()
      .transform((val) => (typeof val === 'string' ? parseFloat(val) : val)),
    claim_currency: z
      .enum(currency_options.map((item) => item.id) as [string, ...string[]])
      .optional()
      .nullable(),
    confirmed: z
      .union([z.string(), z.number()])
      .transform((val) => (typeof val === 'string' ? parseFloat(val) : val))
      .optional()
      .default(0),

    confirmed_currency: z
      .enum(currency_options.map((item) => item.id) as [string, ...string[]])
      .optional()
      .nullable(),
    credit: z
      .union([z.string(), z.number()])
      .optional()
      .transform((val) => (typeof val === 'string' ? parseFloat(val) : val)),
    credit_currency: z
      .enum(currency_options.map((item) => item.id) as [string, ...string[]])
      .optional()
      .nullable(),
    lc_credit: z
      .union([z.string(), z.number()])
      .optional()
      .transform((val) => (typeof val === 'string' ? parseFloat(val) : val)),
    lc_credit_currency: z
      .enum(currency_options.map((item) => item.id) as [string, ...string[]])
      .optional()
      .nullable(),
    remark: z.string().optional().nullable(),
    claim_status: z
      .enum(
        claim_status_options.map((item) => item.id) as [string, ...string[]],
      )
      .optional()
      .nullable(),
    damage_status: z
      .enum(
        damage_status_options.map((item) => item.id) as [string, ...string[]],
      )
      .optional()
      .nullable(),
    email_sent_at: z.union([z.string(), z.coerce.date()]).optional().nullable(),
  })
  .refine(
    (data) => {
      // check if claim  , credit or confirmed have values
      const hasClaim = data.claim !== 0;
      const hasCredit = data.credit !== 0;
      const hasConfirmed = data.confirmed !== 0;

      if (hasClaim || hasCredit || hasConfirmed) {
        const claimCurrencyValid = hasClaim
          ? data.claim_currency !== null
          : true;
        const creditCurrencyValid = hasCredit
          ? data.credit_currency !== null
          : true;
        const ConfirmedCurrencyValid = hasConfirmed
          ? data.confirmed_currency !== null
          : true;

        return (
          claimCurrencyValid && creditCurrencyValid && ConfirmedCurrencyValid
        );
      }
      return true;
    },
    {
      message:
        'Currency must be selected if claim, credit, or confirmed have values',
      path: ['claim_currency', 'credit_currency', 'confirmed_currency'], // Specify the paths for error messages
    },
  );

export const getDamageHeadersByTab = (activeTab: string) => {
  // For these tabs, exclude both email_sent_at and case_number columns
  const tabsToHideEmailAndCase = [
    'under_investigation',
    'forgotten',
    'dismissed',
    'closed',
  ];

  if (tabsToHideEmailAndCase.includes(activeTab)) {
    return damageHeader.filter(
      (header) => header.id !== 'email_sent_at' && header.id !== 'case_number',
    );
  }

  // For credited tab, show only essential columns for table view
  if (activeTab === 'credited') {
    return damageHeader.filter((header) =>
      [
        'id',
        'case_number',
        'damage_type',
        'description',
        'vin',
        'lot_number',
        'company',
        'credit',
        'credited_at',
        'credited_by',
        'damage_status',
      ].includes(header.id),
    );
  }

  // For audit_reviewed tab, show similar columns as credited but include audit fields
  if (activeTab === 'audit_reviewed') {
    return damageHeader.filter((header) =>
      [
        'id',
        'case_number',
        'damage_type',
        'description',
        'vin',
        'lot_number',
        'company',
        'credit',
        'credited_at',
        'credited_by',
        'audit_reviewed_at',
        'audit_reviewed_by',
        'damage_status',
      ].includes(header.id),
    );
  }

  // For all other tabs, return the default headers
  return damageHeader;
};
