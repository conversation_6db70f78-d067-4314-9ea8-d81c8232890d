import DashboardIcon from '@mui/icons-material/Dashboard';
import * as z from 'zod';
import SailingIcon from '@mui/icons-material/Sailing';
import { useContext } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../leftSideMenu/Permissions';
import BookIcon from '@mui/icons-material/Book';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: '/booking/all',
      name: 'Booking MGT',
      icon: <BookIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
    {
      href: 'false',
      name: 'Vessels',
      icon: <SailingIcon sx={{ fontSize: '18px' }} />,
      key: '3',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const VesselTabs = [
  {
    name: 'Up Coming',
    value: 'up_coming',
  },
  {
    name: 'In Progress',
    value: 'in_process',
  },
  {
    name: 'To Be Finalized',
    value: 'to_be_finalized',
  },
  {
    name: 'Archived',
    value: 'archived',
  },
];

export const vessels_status = [
  'up_coming',
  'in_process',
  'to_be_finalized',
  'archived',
];

export const vesselsHeaders = [
  {
    id: 'id',
    label: 'ID',
    pdfWidth: 50,
    align: 'right',
  },
  {
    id: 'name',
    label: 'name',
    pdfWidth: 170,
  },

  {
    id: 'si_cut_off',
    label: 'SI Cut Off',
  },
  {
    id: 'etd',
    label: 'ETD',
  },
  {
    id: 'pol',
    label: 'POL',
    sortColumnName: 'locations.name',
    pdfWidth: 100,
  },
  {
    id: 'terminals',
    label: 'Terminals',
    sortColumnName: 'terminals.name',
    pdfWidth: 100,
  },
  {
    id: 'steamshiplines',
    label: 'Steamline',
    sortColumnName: 'steamshiplines.name',
  },
  {
    id: 'brd',
    label: 'BRD',
  },
  {
    id: 'erd',
    label: 'ERD',
  },
  {
    id: 'destination_eta',
    label: 'Destination (ETA)',
  },
  {
    id: 'chasis_no',
    label: 'Chasis',
    align: 'right',
  },
  {
    id: 'vessel_status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'avg_load',
    label: 'Avg Load',
    align: 'right',
  },
  {
    id: 'w_load',
    label: 'W Load',
    align: 'right',
  },
  {
    id: 'status_changed_at',
    label: 'Age of Status',
    sortColumnName: 'status_changed_at',
    align: 'right',
  },
];

export const filterContentVessel = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&&modal=users&id=',
        keyName: 'fullname',
      },

      {
        name: 'cancelled_by',
        label: 'Cancelled By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'reviewed_by',
        label: 'Reviewed By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'port_of_loading',
        label: 'Port of Loading ',
        type: 'autocomplete',
        url: '/autoComplete?column=name&&modal=locations&id=',
        keyName: 'name',
      },
      {
        name: 'steamshipline_id',
        label: 'Steamship Line ',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=steamshiplines&id=',
        keyName: 'name',
      },
      {
        name: 'container_status',
        label: 'Shipment Status',
        type: 'checkbox',
        items: ['at_the_dock', 'checked', 'final_checked', 'on_the_way'],
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'name',
        label: 'Vessels Name',
        type: 'textfield',
      },
      {
        name: 'vessel_status',
        label: 'Status',
        type: 'checkbox',
        items: ['up_coming', 'in_process', 'to_be_finalized', 'archived'],
      },
      {
        name: 'avg_load',
        label: 'avg load',
        min: 'Start',
        max: 'End',
        type: 'number_range',
      },
      {
        name: 'chasis_no',
        label: 'Chasis Number',
        min: 'Start',
        max: 'End',
        type: 'number_range',
      },
      {
        name: 'w_load',
        label: 'W Load',
        min: 'Start',
        max: 'End',
        type: 'number_range',
      },
      {
        name: 'erd_status',
        label: 'ERD Status',
        type: 'checkbox',
        items: ['estimate', 'actual'],
      },
      {
        name: 'brd_status',
        label: 'BRD Status',
        type: 'checkbox',
        items: ['estimate', 'actual'],
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
      {
        name: 'etd',
        label: 'ETD',
        type: 'date_range',
      },
      {
        name: 'reviewed_at',
        label: 'Reviewed At',
        type: 'date_range',
      },

      {
        name: 'erd',
        label: 'ERD',
        type: 'date_range',
      },
      {
        name: 'brd',
        label: 'BRD',
        type: 'date_range',
      },
      /* {
        name: 'brd_bkg',
        label: 'BRD BKG',
        type: 'date_range',
      }, */
    ],
  },
];

export const schema = z.object({
  name: z
    .string({
      required_error: 'Name is required',
      invalid_type_error: 'Name must be a string',
    })
    .refine((e) => e.length > 0, {
      message: 'Name must Not be Empty',
    }),
  port_of_loading: z.number({
    required_error: 'POL (Port of Loading) is required',
    invalid_type_error: 'POL (Port of Loading) must be one of the Options',
  }),
  terminal_id: z.union([z.number(), z.null(), z.undefined()]),
  etd: z.nullable(z.coerce.date()),
  etd_status: z.enum(['estimate', 'actual']),
  si_cut_off: z.nullable(z.coerce.date()),
  brd: z.nullable(z.coerce.date()),
  brd_status: z.enum(['estimate', 'actual']),
  erd: z.nullable(z.coerce.date()),
  erd_status: z.enum(['estimate', 'actual']),
  steamshipline_id: z.number({
    required_error: 'Steamshipline is required',
    invalid_type_error: 'Steamshipline must be one of the Options',
  }),

  w_load: z.nullable(
    z.number().nonnegative({
      message: 'W Load must be a positive number',
    }),
  ),
  avg_load: z
    .nullable(
      z.number().nonnegative({
        message: 'Avg Load must be a positive number',
      }),
    )
    .optional(),
  chasis_no: z
    .number({
      required_error: 'Chasis No is required',
      invalid_type_error: 'Chasis No must be a number',
    })
    .nullable(),
  scac_code: z.nullable(
    z.string({
      required_error: 'Scac Code is required',
      invalid_type_error: 'Scac Code be a string',
    }),
  ),
  vessel_status: z.enum([
    'up_coming',
    'in_process',
    'to_be_finalized',
    'archived',
  ]),
  eta: z
    .array(
      z.object({
        destination_id: z.number(),
        destination_name: z.string(),
        eta: z.coerce.date(),
      }),
    )
    .optional(),
  booking_targets: z
    .array(
      z.object({
        destination_id: z.number(),
        target: z.number(),
      }),
    )
    .optional(),
});

export const vesselStatus = [
  'up_coming',
  'in_process',
  'to_be_finalized',
  'archived',
];

export const brd_erd_status = ['estimate', 'actual'];
