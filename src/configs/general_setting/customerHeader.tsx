import { contextProvider } from '@/contexts/ProfileContext';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import { useContext } from 'react';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { z } from 'zod';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../leftSideMenu/Permissions';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: 'false',
      name: 'Customers',
      icon: <PeopleAltIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dash<PERSON>',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const customersHeaders = [
  {
    id: 'id',
    label: 'ID',
    pdfWidth: 40,
    align: 'right',
  },
  {
    id: 'photo',
    label: 'Photo',
    sortable: false,
    pdfWidth: 50,
  },

  {
    id: 'fullname',
    label: 'Customer Name',
  },

  {
    id: 'company_name',
    label: 'Company',
    sortColumnName: 'companies.name',
  },

  {
    id: 'phone',
    label: 'Phone',
  },
  {
    id: 'email',
    label: 'Email Address',
    pdfWidth: 120,
  },
  {
    id: 'address',
    label: 'Physical Address',
  },
  {
    id: 'status',
    label: 'Status',
    sortColumnName: 'loginable.status',
    pdfWidth: 50,
    align: 'center',
  },
  {
    id: 'login_link',
    label: 'Login',
    align: 'center',
  },
  {
    id: 'created_at',
    label: 'Created At',
  },
  {
    id: 'created_by',
    label: 'Created By',
  },
  {
    id: 'updated_at',
    label: 'Updated At',
  },
  {
    id: 'updated_by',
    label: 'Updated By',
  },
];

export const customer_status = ['active', 'deactive'];
export const customer_type = ['direct', 'indirect'];
export const schema = z.object({
  username: z.string().min(1, { message: 'Username is required' }),
  company_id: z.number().min(1, { message: 'Company ID is required' }),
  fullname: z.string().min(1, { message: 'Full name is required' }),
  email: z
    .string()
    .email({ message: 'Invalid email format' })
    .min(1, { message: 'Email is required' }),
  gender: z.enum(['male', 'female']),
  status: z.enum(['active', 'deactive']),
  phone: z.string().nonempty(),

  secondary_phone: z.nullable(z.string()).optional(),
  secondary_email: z
    .string()
    .optional()
    .nullable()
    .refine((value) => !value || value.length > 0, {
      message: 'Invalid secondary email format',
    })
    .refine((value) => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value), {
      message: 'Invalid secondary email format',
    }),

  address: z.nullable(z.string()),
  bio: z.nullable(z.string().min(1, { message: 'Bio is required' })),
  photo: z.nullable(z.any().nullable()),
});

export const createSchema = schema.extend({
  password: z
    .string()
    .min(8, { message: 'Password should be at least 8 characters long' })
    .optional(),
});

export const updateSchema = schema.extend({
  password: z.nullable(z.string()).optional(),
});

export const fieldsObject = {
  username: '',
  company_id: '',
  fullname: '',
  email: '',
  password: null,
  since_date: '',
  gender: 'male',
  status: '',
  phone: '',
  secondary_phone: null,
  secondary_email: null,
  // country: '',
  // company_city: '',
  // zip_code: null,
  address: null,
  bio: null,
  // note: null,
  photo: null,
  // loading_instruction: ''
};

export const filterContentCustomers = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'companies.destination_id',
        label: 'Port of Destination',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=destinations&id=',
        keyName: 'name',
      },

      {
        name: 'inactive_customers',
        label: 'Inactive Customer',
        type: 'date_range',
      },

      {
        name: 'active_customers',
        label: 'Active Customer',
        type: 'date_range',
      },
    ],
  },
  {
    title: 'Data',
    items: [
      {
        name: 'fullname',
        label: 'Customer Name',
        type: 'textfield',
      },
      {
        name: 'phone',
        label: 'Phone Number',
        type: 'textfield',
      },
      {
        name: 'secondary_email',
        label: 'Email Address',
        type: 'textfield',
      },
      {
        name: 'address',
        label: 'Physical Address',
        type: 'textfield',
      },
      {
        name: 'loginable.status',
        label: 'Acount Status',
        type: 'checkbox',
        items: ['Enable', 'Disable'],
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];
