import { useContext } from 'react';
import { DASHBOARD } from '../leftSideMenu/Permissions';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import DashboardIcon from '@mui/icons-material/Dashboard';
import * as z from 'zod';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  let breadcrumbs = [
    {
      href: 'false',
      name: 'Towing Rates',
      icon: <TrendingUpIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }
  return breadcrumbs;
};

export const towingRateTabs = [
  { name: 'All', value: 'all' },
  { name: 'Pending', value: 'pending' },
  { name: 'Active', value: 'active' },
  { name: 'In Active', value: 'inactive' },
];

export const towingRatesHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'state_name',
    label: 'State Name',
  },

  {
    id: 'branch_name',
    label: 'Branch Name',
  },

  {
    id: 'city_name',
    label: 'City Name',
  },
  {
    id: 'location_name',
    label: 'Location Name',
  },
  {
    id: 'towing',
    label: 'Towing Rate',
    align: 'right',
  },
  {
    id: 'half_location',
    label: 'Half Cut Location',
  },
  {
    id: 'half_rate',
    label: 'Half Cut Rate',
    align: 'right',
  },
  {
    id: 'status',
    label: 'status',
  },
  {
    id: 'created_at',
    label: 'Created At',
  },
  {
    id: 'created_by',
    label: 'Created By',
  },
  {
    id: 'updated_at',
    label: 'Updated At',
  },
];

export const towingRateSchema = z.object({
  stateId: z.number({
    required_error: 'State is required',
    invalid_type_error: 'State must be one of the Options',
  }),
  branchId: z.number({
    required_error: 'State is required',
    invalid_type_error: 'State must be one of the Options',
  }),
  loading_city_id: z.number({
    required_error: 'City is required',
    invalid_type_error: 'City must be one of the Options',
  }),

  towing: z
    .number({
      required_error: 'Towing is required',
      invalid_type_error: 'Towing must be a number',
    })
    .refine((val) => val >= 0, {
      message: 'Towing must be a positive number.',
    })
    .nullable(),
  location_id: z.number({
    required_error: 'Location is required',
    invalid_type_error: 'Location must be one of the Options',
  }),
  half_location_id: z
    .union([z.number(), z.string().nullable()])
    .transform((value) => {
      return value === '' ? null : value;
    }),
  half_rate: z.nullable(z.number()),
});

export const filterContentTowingRates = [
  {
    title: 'Data',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'stateId',
        label: 'States',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=loading_states&ids=',
        keyName: 'name',
      },
      {
        name: 'branchId',
        label: 'Branches',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=loading_branches&ids=',
        keyName: 'name',
      },
      {
        name: 'loading_city_id',
        label: 'Cities',
        type: 'autocomplete',
        url: '/autoComplete?column=city_name&modal=loading_cities&ids=',
        keyName: 'city_name',
      },
      {
        name: 'location_id',
        label: 'Locations',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=locations&ids=',
        keyName: 'name',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];
