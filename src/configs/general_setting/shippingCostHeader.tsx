import { useContext } from 'react';
import { DASHBOARD } from '../leftSideMenu/Permissions';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import PaidIcon from '@mui/icons-material/Paid';
import DashboardIcon from '@mui/icons-material/Dashboard';
import * as z from 'zod';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  let breadcrumbs = [
    {
      href: 'false',
      name: 'Mix Shipping Rates',
      icon: <PaidIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }
  return breadcrumbs;
};

export const filterContentShippingCost = [
  {
    title: 'Data',
    items: [
      {
        name: 'status',
        label: 'Status',
        type: 'checkbox',
        items: ['pending', 'approved', 'archived'],
      },
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'loading_cities.loading_states.parent.id',
        label: 'States',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=loading_states&ids=',
        keyName: 'name',
      },
      {
        name: 'loading_cities.branch_id',
        label: 'Branches',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=loading_branches&ids=',
        keyName: 'name',
      },
      {
        name: 'loading_city_id',
        label: 'Cities',
        type: 'autocomplete',
        url: '/autoComplete?column=city_name&modal=loading_cities&ids=',
        keyName: 'city_name',
      },
      {
        name: 'location_id',
        label: 'Locations',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=locations&ids=',
        keyName: 'name',
      },
      {
        name: 'destination_id',
        label: 'Destination',
        type: 'autocomplete',
        url: '/destinations/mixShippingDestination?',
        keyName: 'name',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
      {
        name: 'effective_date',
        label: 'Effective Date',
        type: 'date_range',
      },
    ],
  },
];

export const shippingCostHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'state_name',
    label: 'State Name',
  },

  {
    id: 'branch_name',
    label: 'Branch Name',
  },

  {
    id: 'city_name',
    label: 'City Name',
  },
  {
    id: 'location_name',
    label: 'Location Name',
  },
  {
    id: 'destination_name',
    label: 'Destination',
  },
  {
    id: 'status',
    label: 'Status',
    align: 'center',
  },
  {
    id: 'towing',
    label: 'Towing Cost',
    align: 'right',
  },
  {
    id: 'shipping',
    label: 'Shipping Cost',
    align: 'right',
  },
  {
    id: 'clearance',
    label: 'Clearance Cost',
    align: 'right',
  },
  {
    id: 'TDS_charges',
    label: 'TDS Charges',
    align: 'right',
  },
  {
    id: 'tax_duty',
    label: 'TAX & DUTY',
    align: 'right',
  },
  {
    id: 'profit',
    label: 'Profits',
    align: 'right',
  },
  {
    id: 'total',
    label: 'Total',
    align: 'right',
  },
  {
    id: 'effective_date',
    label: 'Effective Date',
  },
  {
    id: 'created_at',
    label: 'Created At',
  },
  {
    id: 'created_by',
    label: 'Created By',
  },
];

export const schema = z.object({
  stateId: z.number({
    required_error: 'State is required',
    invalid_type_error: 'State must be one of the Options',
  }),
  branchId: z.number({
    required_error: 'State is required',
    invalid_type_error: 'State must be one of the Options',
  }),
  loading_city_id: z.number({
    required_error: 'City is required',
    invalid_type_error: 'City must be one of the Options',
  }),

  // towing: z
  //   .number({
  //     required_error: 'Towing is required',
  //     invalid_type_error: 'Towing must be a number',
  //   })
  //   .refine((val) => val >= 0, {
  //     message: 'Towing must be a positive number.',
  //   })
  //   .nullable(),
  shipping: z
    .number({
      required_error: 'Shipping is required',
      invalid_type_error: 'Shipping must be a number',
    })
    .refine((val) => val > 0, {
      message: 'Shipping must be greater than zero',
    }),
  clearance: z
    .number({
      required_error: 'Clearance is required',
      invalid_type_error: 'Clearance must be a number',
    })
    .refine((val) => val >= 0, {
      message: 'Clearance must be a positive number.',
    })
    .nullable(),
  TDS_charges: z
    .number({
      required_error: 'TDS charges is required',
      invalid_type_error: 'TDS charges must be a number',
    })
    .refine((val) => val >= 0, {
      message: 'TDS charges must be a positive number.',
    })
    .nullable(),
  profit: z
    .number({
      required_error: 'Profit amount is required',
      invalid_type_error: 'Profit amount must be a number',
    })
    .refine((val) => val >= 0, {
      message: 'Profit amount must be a positive number.',
    })
    .nullable(),
  location_id: z.number({
    required_error: 'Location is required',
    invalid_type_error: 'Location must be one of the Options',
  }),
  destination_id: z.number({
    required_error: 'Destination is required',
    invalid_type_error: 'Destination must be one of the Options',
  }),
  company_id: z.number().nullable(),
  update_for_same_pol_pod: z.boolean(),
  //effective_date: z.coerce.date(),
});

/* export const sendMail = z.object({
  subject: z.string().min(1, { message: 'Subject is required' }),
  body: z.string().min(1, { message: 'Body is required' }),
  company_ids: z.number({
    required_error: 'Company is required',
    invalid_type_error: 'Company must be one of the Options',
  }),
}); */

export const offerSchema = z.object({
  stateId: z.number({
    required_error: 'State is required',
    invalid_type_error: 'State must be one of the Options',
  }),
  branchId: z.number({
    required_error: 'Branch is required',
    invalid_type_error: 'Branch must be one of the Options',
  }),
  loading_city_id: z.number({
    required_error: 'City is required',
    invalid_type_error: 'City must be one of the Options',
  }),
  location_id: z.number({
    required_error: 'Location is required',
    invalid_type_error: 'Location must be one of the Options',
  }),
  towing: z
    .number({
      required_error: 'Towing is required',
      invalid_type_error: 'Towing must be a number',
    })
    .refine((val) => val > 0, {
      message: 'Towing must be greater than zero',
    }),
  shipping: z
    .number({
      required_error: 'Shipping is required',
      invalid_type_error: 'Shipping must be a number',
    })
    .refine((val) => val > 0, {
      message: 'Shipping must be greater than zero',
    }),
  clearance: z
    .number({
      required_error: 'Clearance is required',
      invalid_type_error: 'Clearance must be a number',
    })
    .refine((val) => val > 0, {
      message: 'Clearance must be greater than zero',
    }),
  TDS_charges: z
    .number({
      required_error: 'TDS charges is required',
      invalid_type_error: 'TDS charges must be a number',
    })
    .refine((val) => val > 0, {
      message: 'TDS charges must be greater than zero',
    }),
  company_id: z.number({
    required_error: 'Company is required',
    invalid_type_error: 'Company must be one of the Options',
  }),
  effective_date: z.coerce.date(),
});

export const states = [
  { id: 'average', name: 'Average' },
  { id: 'regular', name: 'Regular' },
  { id: 'spot', name: 'Spot' },
  { id: 'custom-percentage', name: 'Percent' },
  { id: 'shipline', name: 'Shipline' },
  { id: 'custom', name: 'Custom' },
];
