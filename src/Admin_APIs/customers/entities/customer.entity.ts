import { ApiProperty } from '@nestjs/swagger';
import {
  customer_shipping_rate_type,
  customer_type,
  customers,
  gender,
} from '@prisma/client';
import { Loginable } from './loginable.entity';
import { Optional } from '@nestjs/common';
import { JsonValue } from '@prisma/client/runtime/library';

export class Customer implements customers {
  constructor(partial: Partial<customers>) {
    Object.assign(this, partial);
  }

  @ApiProperty()
  id: number;

  @ApiProperty()
  fullname: string;

  @ApiProperty()
  company_id: number;

  @ApiProperty()
  address: string;

  @ApiProperty()
  phone: string;

  @ApiProperty()
  gender: gender;

  @ApiProperty()
  photo: string;

  @ApiProperty()
  bio: string;

  @ApiProperty()
  since_date: Date;

  @ApiProperty()
  secondary_email: string;

  @ApiProperty()
  secondary_phone: string;

  @ApiProperty()
  preferred_com_way: string;

  @ApiProperty()
  company_city: string;

  @ApiProperty()
  zip_code: string;

  @ApiProperty()
  country: string;

  @ApiProperty()
  consignee: string;

  @ApiProperty()
  consignee_street: string;

  @ApiProperty()
  consignee_box: string;

  @ApiProperty()
  consignee_city: string;

  @ApiProperty()
  consignee_zip_code: string;

  @ApiProperty()
  consignee_country: string;

  @ApiProperty()
  consignee_phone: string;

  @ApiProperty()
  consignee_email: string;

  @ApiProperty()
  consignee_fax: string;

  @ApiProperty()
  consignee_poc: string;

  @ApiProperty()
  notify_party: string;

  @ApiProperty()
  notify_street: string;

  @ApiProperty()
  notify_box: string;

  @ApiProperty()
  notify_city: string;

  @ApiProperty()
  notify_state: string;

  @ApiProperty()
  notify_zip: string;

  @ApiProperty()
  notify_country: string;

  @ApiProperty()
  notify_phone: string;

  @ApiProperty()
  notify_email: string;

  @ApiProperty()
  notify_fax: string;

  @ApiProperty()
  notify_poc: string;

  @ApiProperty()
  note: string;

  @ApiProperty()
  lang: string;

  @ApiProperty()
  loading_instruction: string;

  @ApiProperty()
  customer_type: customer_type;
  @ApiProperty()
  @Optional()
  customer_shipping_rate_type: customer_shipping_rate_type;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  created_by: number;

  @ApiProperty()
  updated_at: Date;

  @ApiProperty()
  updated_by: number;

  @ApiProperty()
  deleted_at: Date;

  @ApiProperty()
  deleted_by: number;

  @ApiProperty({ type: () => Loginable })
  loginable?: Loginable;

  @ApiProperty()
  settings: JsonValue;
}
