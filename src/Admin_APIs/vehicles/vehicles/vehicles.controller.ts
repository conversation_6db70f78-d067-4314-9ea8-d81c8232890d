import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  ParseArrayPipe,
  Patch,
  Post,
  Put,
  Query,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import {
  addStorageDto,
  addToArrivedDto,
  addToInventoryDto,
  addToPGLUDto,
  addVehicleTransaction,
  changeVehiclesHalfCutStatus,
  changeVehiclesState,
  CreateLoadAddVehiclesToContainer,
  CreateVehicleDto,
  downloadInventoryExcel,
  emailInventoryExcel,
  filterHalfCut,
  filterInventory,
  filterVehicles,
  HalfCutSummaryDto,
  UpdateVehicleDto,
  UploadVehicleFileDto,
  VehiclesIdsDto,
  vehiclesToContainer,
  vehiclesCountBasedOnLocation,
  UploadVehicleImageDto,
  SimplificationDto,
  vehicleSummaryDto,
  addTitleTrackingDto,
} from './dto/vehicles.dto';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import PermissionGuard from 'src/Commons/guards/permissions.guard';
import { AdminGuard } from 'src/Commons/guards/jwt.guard';
import { User } from 'src/Commons/decorators/user.decorator';
import {
  SearchPaginateDto,
  TitleDateDto,
} from 'src/Commons/dto/searchPaginateDto.dto';
import { UpdateUniqueValidator } from 'src/Commons/validators/updateUnique.validator';
import { VehiclesService } from './vehicles.service';
import { carstate } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { Response } from 'express';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { getFileStream } from 'src/Commons/services/file-service';
import { Cron } from '@nestjs/schedule';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import {
  uploadToMinio,
  getPreSignedUploadUrl,
  deleteVehicleImagesFromMinio,
} from 'src/Commons/services/file-service';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import { buildCreatedAtFilter } from './helpers/createdAtDateTimeFilter.helper';
@Controller('vehicles')
@ApiTags('Vechicles')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class VehiclesController {
  constructor(
    private readonly vehiclesService: VehiclesService,
    @InjectRedis() private readonly redis: Redis,
    private readonly prisma: PrismaService,
  ) {}

  @Post()
  @UseGuards(PermissionGuard('create_vehicles'))
  async create(@Body() dto: CreateVehicleDto, @User() user: any) {
    return await this.vehiclesService.create(dto, user);
  }

  @Post('/image')
  @UseGuards(PermissionGuard('create_vehicles'))
  @UseInterceptors(FilesInterceptor('cover'))
  @ApiConsumes('multipart/form-data')
  async uploadImage(
    @Body() dto: UploadVehicleFileDto,
    @User() user: any,
    @UploadedFiles() cover: Express.Multer.File,
  ) {
    return await this.vehiclesService.uploadImage(dto, user, cover);
  }

  @Get('/image')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getVehileImage(@Query() query, @Res() res: Response) {
    const stream = await getFileStream(query?.key, { isProtected: false });
    stream.pipe(res);
  }

  @Delete('/image/:id')
  @UseGuards(PermissionGuard('update_vehicles'))
  async deleteVehileImage(@Param('id') id: string, @User() user: any) {
    return await this.vehiclesService.deleteVehileImage(+id, user);
  }

  @Get()
  @UseGuards(PermissionGuard('view_vehicles'))
  async findAll(@Query() query: filterVehicles, @User() user: any) {
    // Parse filterData if it's a string
    let filterData = query.filterData;
    if (typeof filterData === 'string') {
      try {
        filterData = JSON.parse(filterData);
      } catch (e) {
        filterData = {};
      }
    }

    // Build created_at filter
    const createdAtFilter = buildCreatedAtFilter(filterData);

    query._createdAtFilter = createdAtFilter;

    return await this.vehiclesService.findAll(
      query,
      user.userId,
      user.loginable_id,
    );
  }

  @Get('get_specific_vehicles')
  @UseGuards(PermissionGuard('view_vehicles'))
  async findSpecificVehicles(@Query() query: any) {
    return await this.vehiclesService.findSpecificVehicles(query.id);
  }

  @Get('inventory')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getInventory(@Query() query: filterInventory) {
    return await this.vehiclesService.getInventory(query);
  }

  @Get('dispatch')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getDispatch(@Query() query: any) {
    return await this.vehiclesService.getDispatch(query);
  }

  @Get('halfCut')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getHalfCut(@Query() query: filterHalfCut) {
    return await this.vehiclesService.getHalfCut(query);
  }

  @Get('halfCut/summary')
  @UseGuards(PermissionGuard('view_vehicles'))
  async halfCutSummary(@Query() query: HalfCutSummaryDto) {
    return await this.vehiclesService.getHalfCutSummary(query);
  }

  @Get('towCostReport')
  async getTowCostReport(@Query() query: SearchPaginateDto) {
    return await this.vehiclesService.getTowCostReport(query);
  }

  @Get('costAnalysis')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getCostAnalysis(@Query() query: filterVehicles) {
    return await this.vehiclesService.getCostAnalysis(query);
  }

  @Get('dateLines')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getDateLines(@Query() query: filterVehicles) {
    return await this.vehiclesService.getDateLines(query);
  }

  @Get('summary')
  @UseGuards(PermissionGuard('vehicle_view_summary'))
  async getSummary(@Query() query: vehicleSummaryDto) {
    return await this.vehiclesService.getSummary(query);
  }

  @Get('vehicle-images')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getVehicleImages(@Query() query) {
    return await this.vehiclesService.getVehicleImages(+query.id, query.type);
  }

  @Get('storage-link')
  @UseGuards(PermissionGuard('view_vehicles'))
  async getStorageLink(@Query() query) {
    const { signedUrl, fileKey } = await getPreSignedUploadUrl(
      query.type == 'warehouse'
        ? `uploads/vehicles/images/${query?.id}/${query?.size}`
        : `uploads/auction/images/${query?.id}/${query?.size}`,
      query.fileName,
      query.date,
      query.uuid,
    );
    return { signedUrl, fileKey, result: true };
  }

  @Post('upload-images')
  @UseGuards(PermissionGuard('upload_vehicle_image'))
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadVehicleImages(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: UploadVehicleImageDto,
    @User() user: any,
  ) {
    const { key } = await uploadToMinio(
      file,
      dto.destination,
      { isProtected: false, isResizeEnabled: true },
      'vehicle',
    );

    return await this.vehiclesService.storeVehicleImages(
      dto,
      `/${process.env.MINIO_BUCKET}/${key}`,
      user,
    );
  }

  @Delete('delete-image')
  @UseGuards(PermissionGuard('delete_vehicle_image'))
  async deleteVehicleImage(@Query() query, @User() user: any) {
    await this.vehiclesService.deleteVehicleImage(
      query?.ids,
      query?.type,
      user,
    );
    try {
      const results = await Promise.all(
        query.fileKeys.map((fileKey) => deleteVehicleImagesFromMinio(fileKey)),
      );
      return results;
    } catch (error) {
      console.error('Error during deletion:', error);
      throw error;
    }
  }

  @Get('count')
  async count() {
    const count = await this.vehiclesService.getTotal({
      deleted_at: null,
      deleted_by: null,
      carstate: {
        in: [
          carstate.on_the_way,
          carstate.on_hand_no_title,
          carstate.on_hand_with_title,
          carstate.shipped,
          carstate.pending,
        ],
      },
    });
    return { data: { count }, result: true };
  }

  @Get('vehicles-count-based-on-location')
  async getWithTitleCountBasedOnLocation(
    @Query() query: vehiclesCountBasedOnLocation,
  ) {
    return await this.vehiclesService.getVehiclesCountBasedOnLocation(query);
  }

  @Get('gatePassPrint/:id')
  @UseGuards(PermissionGuard('view_vehicles'))
  async GatePass(@Param('id') id: string) {
    return await this.vehiclesService.gatePassPrint(+id);
  }

  @Get('trash')
  @UseGuards(PermissionGuard('trash_view_vehicles'))
  async findTrash(@Query() query: SearchPaginateDto) {
    return await this.vehiclesService.findTrash(query);
  }

  @Get('pending-trash')
  @UseGuards(PermissionGuard('pending_trash_view_vehicles'))
  async findPendingTrash(@Query() query: SearchPaginateDto) {
    return await this.vehiclesService.findPendingTrash(query);
  }

  @Get('company-port-on-hand/:ids')
  // @UseGuards(PermissionGuard('view_vehicles'))
  async companyPort(@Param('ids', ParseArrayPipe) ids: string[]) {
    const temId = ids.map(Number);
    return await this.vehiclesService.companyPort(temId);
  }

  @Get(':id')
  @UseGuards(PermissionGuard('view_vehicles'))
  async findOne(@Param('id') id: string) {
    return await this.vehiclesService.findOne(+id);
  }

  @Patch('changeHalfcutStatus')
  @UseGuards(PermissionGuard('change_halfcut_status_vehicles'))
  async changeHalfcutStatus(
    @Body() query: changeVehiclesHalfCutStatus,
    @User() user: any,
  ) {
    return await this.vehiclesService.changeHalfCutStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch('addVehicleToContainer')
  @UseGuards(PermissionGuard('add_to_container_vehicles'))
  async addVehicleToContainer(
    @Body() query: vehiclesToContainer,
    @User() user: any,
  ) {
    return await this.vehiclesService.addVehiclesToContainer(
      query,
      user.loginable_id,
    );
  }

  @Patch('createLoadAddToContainer')
  @UseGuards(PermissionGuard('add_to_container_vehicles'))
  async createLoadAddToContainer(
    @Body() query: CreateLoadAddVehiclesToContainer,
    @User() user: any,
  ) {
    return await this.vehiclesService.createLoadAddToContainer(
      query,
      user.loginable_id,
    );
  }

  @Patch('addVehicleTransaction')
  @UseGuards(PermissionGuard('create_vehicles_transaction'))
  async addVehicleTransaction(
    @Body() query: addVehicleTransaction,
    @User() user: any,
  ) {
    return await this.vehiclesService.addVehicleTransaction(
      query,
      user.loginable_id,
    );
  }

  @Patch('addStorage/:id')
  @UseGuards(PermissionGuard('add_storage_reports'))
  async addStorage(
    @Param('id') id: string,
    @User() user: any,
    @Body() query: addStorageDto,
  ) {
    return await this.vehiclesService.addStorage(+id, user.loginable_id, query);
  }

  @Patch('gatePass/:id')
  @UseGuards(PermissionGuard('create_gatepass_for_pgl_vehicles'))
  async gatePass(@Param('id') id: string, @User() user: any) {
    return await this.vehiclesService.gatePass(+id, user.loginable_id);
  }

  @Patch('changeVehiclesState')
  @UseGuards(PermissionGuard('change_status_vehicles'))
  async changeVehicleState(
    @Body() query: changeVehiclesState,
    @User() user: any,
  ) {
    const data = await this.vehiclesService.changeVehiclesState(query, user);
    if (data.count > 0)
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    else return { result: false, message: 'Update failed.', data };
  }

  @Patch(':id')
  @UseGuards(PermissionGuard('update_vehicles'))
  async update(
    @Param('id') id: string,
    @Body()
    dto: UpdateVehicleDto,
    @User() user: any,
  ) {
    await UpdateUniqueValidator(+id, 'vehicles', {
      vin: dto.vin,
    });
    return await this.vehiclesService.update(+id, dto, user);
  }

  @Delete(':ids')
  @UseGuards(PermissionGuard('delete_vehicles'))
  async remove(
    @Param('ids', ParseArrayPipe) ids: number[],
    @Body() body: { deleted_reason?: string },
    @User() user: any,
  ) {
    const veh = await this.prisma.pgl_used_cars
      .findMany({
        where: {
          vehicle_id: { in: ids.map(Number) },
          deleted_at: null,
          deleted_by: null,
        },
      })
      .finally(() => this.prisma.$disconnect());

    if (veh.length > 0)
      return {
        result: false,
        message: 'Vehicle is related to PGLU, Can not be deleted.',
      };
    else
      return await this.vehiclesService.softDeleteOne(
        ids,
        user.loginable_id,
        body.deleted_reason,
      );
  }

  @Put('trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_vehicles'))
  async restore(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    const data = await this.vehiclesService.restore(ids, user.loginable_id);
    return { data, result: true };
  }

  @Put('pending-trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_pending_trash_vehicles'))
  async restorePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    const data = await this.vehiclesService.restorePendingTrash(
      ids,
      user.loginable_id,
    );
    return { data, result: true };
  }

  @Put('pending-trash/delete/:ids')
  @UseGuards(PermissionGuard('delete_pending_trash_vehicles'))
  async deletePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    const data = await this.vehiclesService.deletePendingTrash(
      ids,
      user.loginable_id,
    );
    return { data, result: true };
  }

  /* @Delete('trash/forceDelete/:ids')
  @UseGuards(PermissionGuard('force_delete_vehicles'))
  async forceDelete(@Param('ids', ParseArrayPipe) ids: number[]) {
    const data = await this.vehiclesService.forceDeleteOne(ids);
    return { data, result: true };
  } */

  @Post('addToInventory')
  async addToInventory(@Body() dto: addToInventoryDto, @User() user: any) {
    return await this.vehiclesService.addToInventory(dto, user.loginable_id);
  }

  @Post('addTitleTracking')
  async addToDone(@Body() dto: addTitleTrackingDto) {
    return await this.vehiclesService.addTitleTracking(dto);
  }

  @Post('addToArrived')
  async addToArrived(@Body() dto: addToArrivedDto) {
    return await this.vehiclesService.addToArrived(dto);
  }

  @Post('addToPGLU')
  async addToPGLU(@Body() dto: addToPGLUDto, @User() user: any) {
    const data = await this.vehiclesService.addToPGLU(dto, user.loginable_id);
    if (data.result) {
      return { result: data.result, data };
    } else {
      throw new HttpException(data, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('addToUnitedTradingCars')
  @UseGuards(PermissionGuard('add_to_united_trading_vehicles'))
  async addToUnitedTradingCars(@Body() dto: VehiclesIdsDto) {
    const data = await this.vehiclesService.addToUnitedTradingCars(dto);
    if (data.result) {
      return { result: data.result, data };
    } else {
      throw new HttpException(data, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch('auctionPaidToOnTheWay/:id')
  @UseGuards(PermissionGuard('auction_paid_vehicles'))
  async auctionPaidToOnTheWay(@Param('id') id: string, @User() user: any) {
    return this.vehiclesService.auctionPaidToOnTheWay(+id, user.loginable_id);
  }

  @Patch('updateTitleReceiveDate/:id')
  async updateTitleReceiveDate(
    @Param('id') id: string,
    @Body() dto: TitleDateDto,
    @User() user: any,
  ) {
    return await this.vehiclesService.updateTitleReceiveDate(
      +id,
      dto,
      user.loginable_id,
    );
  }
  @Patch('updatedLastTitleFollowUpDate/:id')
  async updatedLastTitleFollowUpDate(
    @Param('id') id: string,
    @Body() dto: TitleDateDto,
    @User() user: any,
  ) {
    return await this.vehiclesService.updatedLastTitleFollowUpDate(
      +id,
      dto,
      user.loginable_id,
    );
  }

  @Patch('updatTrackNumber/:id')
  async updatedSimplificationTrack(
    @Param('id') id: string,
    @Body() dto: SimplificationDto,
    @User() user: any,
  ) {
    return await this.vehiclesService.updatedSimplificationTrack(
      +id,
      dto,
      user.loginable_id,
    );
  }

  @Post('download-inventory-excel')
  async downloadInventoryExcel(
    @Body() dto: downloadInventoryExcel,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const excelBuffer =
        await this.vehiclesService.downloadInventoryExcel(dto);
      res.header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.header('Content-Disposition', 'attachment; filename=inventory.xlsx');
      res.send(excelBuffer);
    } catch (error) {
      console.error('Error generating Excel:', error);

      res.status(500).send('Internal Server Error');
    }
  }

  @Post('download-inventory-pdf')
  async downloadInventoryPDF(
    @Body() dto: downloadInventoryExcel,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const Pdf = await this.vehiclesService.downloadInventoryPDFOrImage(
        dto,
        'pdf',
      );
      res.header('Content-Type', 'application/pdf');
      res.header('Content-Disposition', 'attachment; filename="inventory.pdf"');
      res.send(Pdf);
    } catch (error) {
      console.error('Error generating Pdf:', error);
      res.status(500).send('Internal Server Error');
    }
  }

  @Post('download-inventory-image')
  async downloadInventoryImage(
    @Body() dto: downloadInventoryExcel,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const Pdf = await this.vehiclesService.downloadInventoryPDFOrImage(
        dto,
        'image',
      );
      res.header('Content-Type', 'image/png');
      res.header('Content-Disposition', 'attachment; filename="inventory.png"');
      res.send(Pdf);
    } catch (error) {
      console.error('Error generating Image:', error);
      res.status(500).send('Internal Server Error');
    }
  }

  @Post('email-inventory-excel')
  async emailInventoryExcel(@Body() dto: emailInventoryExcel) {
    return await this.vehiclesService.sendEmailInventoryExcel(dto);
  }

  @Get('google-drive/download-images')
  async downloadGoogleDriveImages(
    @Query() query: string,
    @User() user: any,
    @Res() res: Response,
  ) {
    const zipFilePath = await this.vehiclesService.downloadGoogleDriveImages(
      query,
      user,
    );
    res.setHeader('Content-Disposition', 'attachment; filename=images.zip');
    res.setHeader('Content-Type', 'application/zip');
    res.sendFile(zipFilePath, { root: '.' });
    try {
      setTimeout(() => {
        if (existsSync(zipFilePath)) {
          unlink(zipFilePath);
        }
      }, 5000);
    } catch (unlinkError) {
      console.error('Error deleting file:', unlinkError);
    }
  }

  @Cron('0 5 * * *')
  async updateTitleTrackingStatus() {
    const lockKey = `update_title_tracking_status_lock`;
    const lockTtl = 10 * 60;
    const hasLock = await this.acquireLock(lockKey, lockTtl);

    if (hasLock) {
      try {
        const today = new Date();
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() - 2);

        await this.prisma.vehicles.updateMany({
          where: {
            title_tracking_status: null,
            containers: {
              bookings: {
                port_of_discharge: {
                  in: [
                    22, // POTI, Georgia
                    14, // Klaipeda, LT
                  ],
                },
                vessels: {
                  etd: {
                    lte: targetDate,
                  },
                },
              },
            },
          },
          data: {
            title_tracking_status: 'in_process',
            title_tracking_status_updated_at: new Date(),
          },
        });
      } catch (error) {
        console.error(error);
      } finally {
        this.releaseLock(lockKey);
      }
    }
  }

  async acquireLock(key: string, ttlSeconds: number): Promise<boolean> {
    const result = await this.redis.set(key, 'locked', 'EX', ttlSeconds, 'NX');
    return result === 'OK';
  }

  releaseLock(key: string): void {
    this.redis.del(key);
  }
}
