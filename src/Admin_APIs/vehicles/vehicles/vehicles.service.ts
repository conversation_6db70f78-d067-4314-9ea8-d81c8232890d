import * as <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import axios from 'axios';
import {
  addStorageDto,
  addToPGLUDto,
  addVehicleTransaction,
  changeVehiclesHalfCutStatus,
  changeVehiclesState,
  CreateLoadAddVehiclesToContainer,
  CreateVehicleDto,
  downloadInventoryExcel,
  emailInventoryExcel,
  filterHalfCut,
  filterInventory,
  filterVehicles,
  HalfCutSummaryDto,
  UpdateVehicleDto,
  UploadVehicleFileDto,
  VehicleChargesDTO,
  vehiclesCountBasedOnLocation,
  vehiclesToContainer,
  vehicleSummaryDto,
  UploadVehicleImageDto,
  SimplificationDto,
  addToInventoryDto,
  addTitleTrackingDto,
  addToArrivedDto,
} from './dto/vehicles.dto';
import { CommonFields } from 'src/Commons/services/common.fields';
import {
  $Enums,
  carstate,
  container_status,
  pl_status,
  tracking_status,
  vehicles_halfcut_status_enum,
  container_aes_status,
  container_title_status,
} from '@prisma/client';
import {
  SearchPaginateDto,
  TitleDateDto,
} from 'src/Commons/dto/searchPaginateDto.dto';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { ordersBy } from 'src/Commons/helpers/ordersby.helper';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import { carStateFour } from 'src/Commons/enums/enums';
import { email } from 'src/Commons/helpers/email.helper';
import statics from 'src/Commons/configs/statics';
import * as moment from 'moment';
import * as momentTimeZone from 'moment-timezone';
import { check_permissions } from 'src/Commons/helpers/check_permissions.helper';
import { DbService } from 'src/prisma/prisma.service';
import * as ExcelJS from 'exceljs';
import { Buffer } from 'buffer';
import * as fsPromises from 'fs/promises';
import * as nodemailer from 'nodemailer';
import * as mailgunTransport from 'nodemailer-mailgun-transport';
import { readFileSync } from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import { add_tracking } from 'src/Commons/helpers/tracking.helper';
import {
  deleteFromMinio,
  uploadToMinio,
} from 'src/Commons/services/file-service';
import { generateImage, generatePdf } from 'src/Commons/helpers/generatePDF';
import { VehiclesIdsDto } from 'src/LoadingCompany_APIs/vehicles/dto/vehicles.dto';
import { createLog } from 'src/Commons/helpers/log_functions.helper';
import { CheckPermissionsService } from 'src/Admin_APIs/users/check-permissions.service';
import { StorageService } from '../storage_reports/storage.service';
import { paymentSelect } from 'src/Commons/helpers/customerPaymentTransactions.helper';
import { GoogleApisService } from 'src/Commons/services/google-drive/google-apis.service';
import { google } from 'googleapis';
import * as fs from 'fs/promises';
import { vehicle_images_logs } from 'src/Admin_APIs/activity_log/models/vehicle_images_logs';
import { addAndRemoveToShipment } from 'src/Admin_APIs/activity_log/models/vehicles_log';

import reactEmail from 'src/Commons/helpers/reactEmail.helper';
import AddVehiclesAndChangeStatusEmailTemplate from 'emails/Others/AddVehiclesAndChangeStatusEmailTemplate';
import GatePassEmailTemplate from 'emails/Others/GatePassEmailTemplate';

const create_update_delete_by = CommonFields([
  'users_vehicles_created_byTousers',
  'users_vehicles_updated_byTousers',
  'users_vehicles_deleted_byTousers',
]);

const create_update_by = CommonFields([
  'users_vehicles_created_byTousers',
  'users_vehicles_updated_byTousers',
]);

const transaction_by = CommonFields(['users_vehicles_transaction_byTousers']);

const searchColumns = [
  'int@@id',
  'vin',
  'lot_number',
  'year',
  'model',
  'make',
  'color',
  'weight',
  'int@@price',
  'int@@tax_amount',
  'date@@deliver_date',
  'date@@purchased_at',
  'vehicle_towings.int@@tow_amount',
  'vehicle_towings.towing_company',
  'customers.companies.name',
  'containers.container_number',
  'containers.bookings.booking_number',
  'containers.bookings.parent.booking_number',
  'vehicle_costs.invoice_description',
  'buyer_number',
  'yards_location.name',
  'customer_remark',
  'auction_remark',
  'title_state',
  'title_status',
  'trn',
  'title_delivery_location',
  'title_status_step_two',
  'pol_locations.name',
  // 'auction_city',
  'auction_name',
  'hat_number',
  'title_number',
  'title_tracking_number',
];

const titleStepOne = [
  'LIEN PAPER',
  'TITLE WITH LIEN',
  'MAIL TO BUYER',
  'TRN# WILL BE ASSIGNED BY FEDEX',
  'TITLE PENDING',
  'PICKED BY DRIVER',
  'BILL OF SALE',
  'INSTRUCTION REQUEST',
  'HALFCUT REQUEST',
  'REQUEST FROM ACCOUNT',
  'TITLE AVAILABLE IN AUTCTION DRIVER SHOULD PICKED UP',
  'TITLE AVAILABLE IN AUCTION',
  'DUPLICATE FROM AUCTION',
  'BILL OF SALE FROM AUCTION',
  'CUSTOM',
];

const excludeCompanyIdsDoNotSendEmail = [
  1078, // Galaxy Worldwide Shipping - Jebel Ali
  1173, // Galaxy Worldwide Shipping - Other destinations
];

const is_send: boolean = process.env.SEND_MAIL === 'true';
@Injectable()
export class VehiclesService {
  private prisma: any;
  private prisma2: typeof DbService.prisma;
  constructor(
    private readonly db: DbService,
    private readonly checkPermissionsService: CheckPermissionsService,
    private readonly storageService: StorageService,
    private readonly googleApisService: GoogleApisService,
  ) {
    this.prisma = DbService.prisma;
    this.prisma2 = DbService.prisma;
  }

  create = async (dto: CreateVehicleDto, loginable: any) => {
    const created_by: number = loginable.loginable_id;
    const perm = await check_permissions(
      loginable.userId,
      'auction_unpaid_vehicles',
    );

    const DataEntryUser = await this.prisma.users.findFirst({
      where: {
        id: loginable.loginable_id,
        department_id: 3,
      },
      select: {
        id: true,
        fullname: true,
      },
    });

    let dispatch_type = 'dispatch';
    if (DataEntryUser) {
      dispatch_type = 'self_pickup_delivered_to_pgl';
    }

    // if (dto.point_of_loading == 6) {
    //   dto.yard_location_id = 22;
    // }
    if (dto.vehicle_charges.length == 0) {
      delete dto.vehicle_charges;
    }
    const complete = Object.assign({}, dto);
    delete dto.towing_company;
    delete dto.tow_amount;
    delete dto.towed_from;
    delete dto.dismantal_cost;
    delete dto.ship_cost;
    delete dto.pgl_storage_costs;
    delete dto.title_charge;
    delete dto.dubai_custom_cost;
    delete dto.other_cost;
    delete dto.sales_cost;
    delete dto.towing_cost;
    delete dto.vehicle_price;
    delete dto.invoice_description;
    delete dto.add_information;
    if (!dto.request_for_pickup_date) {
      dto.request_for_pickup_date = dto.payment_date_to_pgl;
    }

    if (!dto.ready_for_pickup_date) {
      dto.ready_for_pickup_date =
        dto.payment_date_to_pgl ??
        (new Date(dto.payment_date) > new Date(dto.request_for_pickup_date)
          ? dto.payment_date
          : dto.request_for_pickup_date);
    }

    const data = {
      ...dto,
      ...(perm ? { carstate: carstate.auction_unpaid } : {}),
      weight: dto.weight.toString(),
      created_by,
      dispatch_type,
      status_changed_at: new Date(),
      updated_by: created_by,
      vehicle_costs: {
        create: {
          dismantal_cost: complete.dismantal_cost,
          ship_cost: complete.ship_cost,
          pgl_storage_costs: complete.pgl_storage_costs,
          title_charge: complete.title_charge,
          dubai_custom_cost: complete.dubai_custom_cost,
          other_cost: complete.other_cost,
          sales_cost: complete.sales_cost,
          towing_cost: complete.towing_cost,
          vehicle_price: complete.vehicle_price,
          invoice_description: complete.invoice_description,
          add_information: complete.add_information,
          created_by,
          updated_by: created_by,
        },
      },
    };

    if (data.carstate == carstate.auction_unpaid && dto.payment_date != null) {
      if (await check_permissions(loginable.userId, 'auction_paid_vehicles')) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        data.carstate = carstate.auction_paid;
      }
    }

    if (complete.tow_amount || complete.towing_company || complete.towed_from) {
      data['vehicle_towings'] = {
        create: {
          tow_amount: complete.tow_amount,
          towing_company: complete.towing_company,
          towed_from: complete.towed_from,
          created_by,
          updated_by: created_by,
        },
      };
    }
    const charges = complete.vehicle_charges;
    // if (complete.vehicle_charges) {
    //   data['vehicle_charges'] = {
    //     create: charges.map((ch: any) => ({
    //       name: ch.name,
    //       amount: ch.amount,
    //       cost: ch.cost,
    //       remark: ch.remark,
    //     }))
    //   };
    // }

    try {
      const result = await this.prisma.vehicles
        .create({
          data:
            complete.vehicle_charges?.length > 0
              ? {
                  ...data,
                  vehicle_charges: {
                    create: charges.map((ch: any) => ({
                      name: ch.name,
                      amount: ch.amount,
                      cost: ch.cost,
                      remark: ch.remark,
                      category: ch.category,
                      created_at: new Date(),
                      created_by: loginable.loginable_id,
                    })),
                  },
                }
              : data,
          include: { vehicle_costs: true, vehicle_towings: true },
        })
        .finally(() => this.prisma.$disconnect());
      add_tracking(
        [result?.id],
        perm ? tracking_status.auction_unpaid : tracking_status.on_the_way,
      );
      // Add vehicle to pglu if the company name was united unstaoppable car auction LLC and color = #FFFFFF
      const company = await this.prisma.companies
        .findUnique({
          where: { id: dto.company_id },
          select: { is_belong_to_used_car: true },
        })
        .finally(() => this.prisma.$disconnect());
      if (company?.is_belong_to_used_car) {
        const puc = await this.prisma.pgl_used_cars
          .create({
            data: {
              vehicle_id: result?.id,
              selling_status: 2,
              selling_status_color: '#FFFFFF',
              created_by,
              updated_by: created_by,
            },
            select: { id: true },
          })
          .finally(() => this.prisma.$disconnect());

        await this.prisma.pgl_used_car_costs
          .create({
            data: {
              pgl_used_car_id: puc.id,
              ten_percent_vat_duty: (10 * dto.price) / 100,
              clearance: 300,
              created_by,
              updated_by: created_by,
            },
          })
          .finally(() => this.prisma.$disconnect());
      }
      // End PGLU section
      const res: any = await this.findOne(result?.id);

      this.sendMail([res], 'add_vehicles');

      return { result: true, data: res };
    } catch (err) {
      catch_response(err);
    }
  };

  async uploadImage(
    dto: UploadVehicleFileDto,
    loginable: any,
    cover: Express.Multer.File,
  ) {
    try {
      const vehicle = await this.findOne(dto?.id);

      if (vehicle?.cover_photo) {
        await deleteFromMinio(vehicle?.cover_photo, { isProtected: false });
      }

      const destination = 'uploads/vehicles/cover_photo';
      const uploadedFile = await uploadToMinio(cover[0], destination, {
        isProtected: false,
      });

      return this.prisma.vehicles
        .updateMany({
          where: {
            id: dto.id,
          },
          data: {
            cover_photo: `${uploadedFile.bucket}/${uploadedFile.key}`,
            updated_by: loginable.loginable_id,
          },
        })
        .then((data) => {
          if (data.count > 0)
            return {
              result: true,
              message: `${data.count} records have been updated!`,
            };
          else
            return { result: false, message: 'No records have been updated.' };
        })
        .catch((err) => catch_response(err));
    } catch (error) {
      catch_response(error);
    }
  }

  async storeVehicleImages(dto: UploadVehicleImageDto, fileKey, loginable) {
    const url = fileKey.replace(/\/1024\//, '/250/');
    const model =
      dto.type === 'warehouse'
        ? this.prisma.vehicle_images
        : this.prisma.vehicle_auction_images;
    await model
      .create({
        data: {
          vehicle_id: Number(dto?.id),
          name: dto?.fileName,
          size: 250,
          url: url,
          created_by: loginable.loginable_id,
        },
      })
      .finally(() => this.prisma.$disconnect());
  }

  async deleteVehileImage(id, loginable: any) {
    try {
      const vehicle = await this.findOne(id);

      if (vehicle?.cover_photo) {
        await deleteFromMinio(vehicle?.cover_photo, { isProtected: false });
      }
      return this.prisma.vehicles
        .updateMany({
          where: {
            id: id,
          },
          data: {
            cover_photo: null,
            updated_by: loginable.loginable_id,
          },
        })
        .then((data) => {
          if (data.count > 0)
            return {
              result: true,
              message: `${data.count} records have been updated!`,
            };
          else
            return { result: false, message: 'No records have been updated.' };
        })
        .catch((err) => catch_response(err));
    } catch (error) {
      catch_response(error);
    }
  }

  downloadGoogleDriveImages = async (query: any, user: any) => {
    const token = await this.googleApisService.findToken(user.loginable_id);

    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({
      access_token: token['access_token'],
      refresh_token: token['refresh_token'],
    });
    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    const folderId = query.folderId;
    const res = await drive.files.list({
      q: `mimeType contains 'image/' and '${folderId}' in parents`,
      fields: 'files(id, name)',
      pageSize: 100,
      spaces: 'drive',
      supportsAllDrives: true,
      corpora: 'allDrives',
      includeItemsFromAllDrives: true,
    });

    const files = res.data.files;
    const zip = new JSZip();

    for (const file of files) {
      const fileId = file.id;
      const fileName = file.name;
      const fileRes = await drive.files.get(
        { fileId, alt: 'media' },
        { responseType: 'stream' },
      );
      const chunks = [];
      fileRes.data.on('data', (chunk) => {
        chunks.push(chunk);
      });
      await new Promise((resolve, reject) => {
        fileRes.data.on('end', resolve);
        fileRes.data.on('error', reject);
      });
      const buffer = Buffer.concat(chunks);
      zip.file(fileName, buffer);
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    const zipFilePath = 'images.zip';
    await fs.writeFile(zipFilePath, zipBuffer);

    return zipFilePath;
  };

  findSpecificVehicles = async (vehicleIds: number[]) => {
    try {
      const convertedVehicleIds = vehicleIds.map((id) => Number(id));
      const [data] = await Promise.all([
        this.prisma.vehicles.findMany({
          where: {
            id: { in: convertedVehicleIds },
          },
          include: {
            pgl_used_cars: { select: { id: true } },
            yard_inventories: { select: { id: true } },
            payments: paymentSelect(),
            vehicle_towings: {
              select: {
                towing_company: true,
                tow_amount: true,
              },
            },
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_damages: {
              select: {
                id: true,
              },
              where: {
                deleted_at: null,
              },
            },
            _count: {
              select: {
                vehicle_images: {
                  where: { size: 250 }, // optional filter for count too
                },
                vehicle_auction_images: {
                  where: { size: 250 },
                },
              },
            },
            companies: { select: { name: true } },
            customers: {
              select: {
                companies: {
                  select: {
                    name: true,
                    destinations: {
                      select: {
                        name: true,
                        id: true,
                      },
                    },
                  },
                },
              },
            },
            containers: {
              select: {
                aes_filling_link: true,
                id: true,
                status: true,
                container_number: true,
                loading_date: true,
                booking_suffix: true,
                bookings: {
                  select: {
                    parent_id: true,
                    booking_number: true,
                    destinations: {
                      select: { id: true, name: true },
                    },
                    parent: {
                      select: {
                        booking_number: true,
                        eta: true,
                        id: true,
                      },
                    },
                    eta: true,
                    vessels: { select: { etd: true } },
                  },
                },
              },
            },
            pol_locations: { select: { name: true } },
            // loading_cities: { select: { city_name: true } },
            destinations: { select: { id: true, name: true } },
            yards_location: { select: { name: true, id: true } },
            ...transaction_by,
            ...create_update_by,
          },
        }),
      ]);

      return { result: true, data };
    } catch (err) {
      return catch_response(err);
    } finally {
      await this.prisma.$disconnect();
    }
  };

  findAll = async (
    query: filterVehicles,
    login_id: number,
    user_id: number,
  ) => {
    const offset = query.page;
    const limit = query.per_page;
    const carState: any = query.state;
    const dispatch = JSON.parse(query.filterData);

    const searchOptions =
      query.searchOptions && JSON.parse(query.searchOptions.replace(/'/g, '"'));
    const isExport = query.isExport;
    const exportTotal = query.exportTotal;
    const exportType = query.exportType;

    let on_hand_with_load;
    let shipped;
    if (dispatch.carstate) {
      on_hand_with_load = dispatch.carstate.includes('on_hand_with_load');
      shipped = dispatch.carstate.includes('shipped');
      if (on_hand_with_load) {
        if (dispatch.carstate.length > 1) {
          dispatch.carstate = dispatch.carstate.filter(
            (state) => state !== 'on_hand_with_load',
          );
        } else {
          delete dispatch.carstate;
        }
        query.filterData = JSON.stringify(dispatch);
      }
    }

    if (isExport) {
      createLog({
        log_name: 'pgl',
        description: 'Export',
        subject_type: 'User',
        subject_id: user_id,
        causer_type: 'User',
        causer_id: user_id,
        properties: { exportTotal: exportTotal, exportType: exportType },
      });
    }

    const searchBy = searchOptions?.length > 0 ? searchOptions : searchColumns;

    let where: any = await searchFilter(query, searchBy);

    // Add created_at filter if it exists
    if (query._createdAtFilter) {
      where = {
        ...where,
        ...query._createdAtFilter,
      };
    }

    // Remove any receiver_name conditions added by searchFilter
    if (where['AND']) {
      where['AND'] = where['AND'].filter(
        (condition) => !condition.hasOwnProperty('receiver_name'),
      );
      if (where['AND'].length === 0) delete where['AND'];
    }
    if (where.hasOwnProperty('receiver_name')) {
      delete where.receiver_name;
    }

    const orderBy = await ordersBy(query);

    let hasMatch = [];
    if (query.search && query?.search?.length >= 6) {
      hasMatch = await this.prisma2.vehicles.findMany({
        where: {
          OR: [
            { vin: { contains: query.search } },
            { lot_number: { contains: query.search } },
            { containers: { container_number: { contains: query.search } } },
          ],
        },
        select: { id: true },
      });
    }

    const permissions =
      await this.checkPermissionsService.getUsersPermissionsByGroupNames(
        user_id,
        ['vehicles'],
      );

    const restrictions =
      this.checkPermissionsService.getVehiclesRestrictions(permissions);

    if (restrictions) {
      const newRest = {
        OR: [
          ...restrictions.OR,
          hasMatch.length ? { id: { in: hasMatch.map((e) => e.id) } } : {},
        ],
      };
      if (where['AND']) where['AND'].push(newRest);
      else where['AND'] = [newRest];
    }

    if (dispatch?.dispatch_department?.length > 0) {
      where['OR'] = [
        { payment_date: { not: null } },
        { date_posted_in_central_dispatch: { not: null } },
        { request_for_pickup_date: { not: null } },
      ];
    }

    if (dispatch?.is_scrap?.length > 0) {
      where['AND'] = [{ is_scrap: true }];
    }

    if (dispatch?.receiver_name?.length > 0) {
      if (dispatch?.receiver_name == 'null') {
        where['AND'] = { OR: [{ receiver_name: null }, { receiver_name: '' }] };
      }
      if (dispatch?.receiver_name == 'not_null') {
        where['AND'] = {
          AND: [
            { receiver_name: { not: null } },
            { receiver_name: { not: '' } },
          ],
        };
      }
    }

    if (dispatch?.is_key_present?.length > 0) {
      const keyPresent = dispatch?.is_key_present;
      const kQ = [
        keyPresent.includes('Yes') ? { is_key_present: true } : null,
        keyPresent.includes('No') ? { is_key_present: false } : null,
        keyPresent.includes('Not set') ? { is_key_present: null } : null,
      ].filter((item) => item != null);
      where['AND'] = where['AND']
        ? [...where['AND'], { OR: kQ }]
        : [{ OR: kQ }];
    }

    if (dispatch?.ach?.length > 0) {
      const achPaymentApproved = dispatch?.ach;
      const result = [
        achPaymentApproved.includes('Yes') ? { ach: true } : null,
        achPaymentApproved.includes('No') ? { ach: false } : null,
        achPaymentApproved.includes('Not set') ? { ach: null } : null,
      ].filter((item) => item != null);
      where['AND'] = where['AND']
        ? [...where['AND'], { OR: result }]
        : [{ OR: result }];
    }

    if (dispatch?.vehicles_with_cover_photos?.length > 0) {
      const vehiclesWithCoverPhotos = dispatch?.vehicles_with_cover_photos;
      const vwcpQ = [
        vehiclesWithCoverPhotos.includes('Yes')
          ? { cover_photo: { not: null } }
          : null,
        vehiclesWithCoverPhotos.includes('No') ? { cover_photo: null } : null,
      ].filter((item) => item != null);
      where['AND'] = where['AND']
        ? [...where['AND'], { OR: vwcpQ }]
        : [{ OR: vwcpQ }];
    }

    if (dispatch?.vehicles_with_photos?.length > 0) {
      const vehiclesWithPhotos = dispatch?.vehicles_with_photos;
      if (
        vehiclesWithPhotos.includes('No') &&
        vehiclesWithPhotos.includes('Yes')
      ) {
        where['AND'] = where['AND'];
      } else {
        const vwpQ = [
          vehiclesWithPhotos.includes('Yes')
            ? {
                OR: [
                  {
                    AND: [
                      { photo_link: { not: null } },
                      { photo_link: { not: '' } },
                    ],
                  },
                  {
                    vehicle_images: {
                      some: {},
                    },
                  },
                ],
              }
            : null,
          vehiclesWithPhotos.includes('No')
            ? {
                photo_link: null,
                vehicle_images: {
                  none: {},
                },
              }
            : null,
        ].filter((item) => item != null);
        where['AND'] = where['AND'] ? [...where['AND'], ...vwpQ] : vwpQ;
      }
    }

    if (dispatch?.vehicles_with_auction_photos?.length > 0) {
      const vehiclesWithAuctionPhotos = dispatch?.vehicles_with_auction_photos;
      if (
        vehiclesWithAuctionPhotos.includes('No') &&
        vehiclesWithAuctionPhotos.includes('Yes')
      ) {
        where['AND'] = where['AND'];
      } else {
        const vwapQ = [
          vehiclesWithAuctionPhotos.includes('Yes')
            ? {
                OR: [
                  {
                    AND: [
                      { auction_photos_link: { not: null } },
                      { auction_photos_link: { not: '' } },
                    ],
                  },
                  {
                    vehicle_auction_images: {
                      some: {},
                    },
                  },
                ],
              }
            : null,
          vehiclesWithAuctionPhotos.includes('No')
            ? {
                auction_photos_link: null,
                vehicle_auction_images: {
                  none: {},
                },
              }
            : null,
        ].filter((item) => item != null);
        where['AND'] = where['AND'] ? [...where['AND'], ...vwapQ] : vwapQ;
      }
    }

    if (dispatch?.vehicles_with_aes_filling_link?.length > 0) {
      const vehiclesWithAesFillingLink =
        dispatch?.vehicles_with_aes_filling_link;
      const vaesQ = [
        vehiclesWithAesFillingLink.includes('Yes')
          ? { containers: { aes_filling_link: { not: null } } }
          : null,
        vehiclesWithAesFillingLink.includes('No')
          ? { containers: { aes_filling_link: null } }
          : null,
      ].filter((item) => item != null);
      where['AND'] = where['AND']
        ? [...where['AND'], { OR: vaesQ }]
        : [{ OR: vaesQ }];
    }

    // Filter By Auction Invoice Link
    if (dispatch?.vehicles_with_auction_invoice_link?.length > 0) {
      const vehicles_with_auction_invoice_link =
        dispatch?.vehicles_with_auction_invoice_link;
      const vaesQ = [
        vehicles_with_auction_invoice_link.includes('Yes')
          ? { auction_invoice: { not: null } }
          : null,
        vehicles_with_auction_invoice_link.includes('No')
          ? { auction_invoice: null }
          : null,
      ].filter((item) => item != null);
      where['AND'] = where['AND']
        ? [...where['AND'], { OR: vaesQ }]
        : [{ OR: vaesQ }];
    }

    // Filter by receiver_name
    if (dispatch?.receiver_name?.length > 0) {
      const receiverCondition = dispatch.receiver_name.includes('Yes')
        ? { receiver_name: { not: null } }
        : dispatch.receiver_name.includes('No')
          ? { receiver_name: null }
          : null;
      if (receiverCondition) {
        where['AND'] = where['AND']
          ? [...where['AND'], receiverCondition]
          : [receiverCondition];
        console.log('Receiver condition added:', receiverCondition);
      }
    }

    // Filter by carState if it's set to 'in_process' or 'done'
    const today = new Date();
    const twoDaysAgo = new Date(today);
    twoDaysAgo.setDate(today.getDate() - 2); // Subtract 2 days from today
    const twoDaysAgoISO = twoDaysAgo.toISOString();

    if (carState) {
      if (
        carState === 'in_process' ||
        carState === 'send' ||
        carState === 'arrived'
      ) {
        where['title_tracking_status'] = carState;

        if (carState === 'in_process') {
          where['containers'] = {
            is: {
              container_number: { not: null },
              bookings: {
                is: {
                  vessels: {
                    is: {
                      etd: { lte: twoDaysAgoISO }, // Dates two days ago or earlier
                    },
                  },
                },
              },
            },
          };
        }
      } else if (carState === 'on_the_way_auction') {
        where['carstate'] = {
          in: [
            carstate.on_the_way,
            carstate.auction_paid,
            carstate.auction_unpaid,
          ],
        };
      } else {
        where['carstate'] = carState;
      }
    }

    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    if (query.search) {
      const [bookingNumber, bookingSuffix] = query.search.split('-');
      if (bookingSuffix) {
        delete where.OR;
        where['AND'] = [
          {
            containers: {
              bookings: {
                booking_number: {
                  contains: bookingNumber.trim(),
                  mode: 'insensitive',
                },
              },
            },
          },
          {
            containers: {
              booking_suffix: {
                contains: bookingSuffix.trim(),
                mode: 'insensitive',
              },
            },
          },
        ];
      }
    }

    if (on_hand_with_load) {
      const temp = where['AND']?.find((item) => {
        return Object.keys(item).includes('carstate');
      });
      where = {
        ...where,
        AND: [
          ...(where['AND']
            ? where['AND']?.filter((item) => {
                return !Object.keys(item).includes('carstate');
              })
            : []),
          {
            OR: [
              temp,
              {
                carstate: carstate.shipped,
                containers: { container_number: null },
              },
            ].filter((item) => item),
          },
        ],
      };
    } else if (shipped) {
      const temp = where['AND']?.find((item) => {
        return Object.keys(item).includes('carstate');
      });
      where = {
        ...where,
        AND: [
          ...(where['AND']
            ? where['AND']?.filter((item) => {
                return !Object.keys(item).includes('carstate');
              })
            : []),
          {
            OR: [
              {
                carstate: {
                  in: temp.carstate.in.filter(
                    (item) => item !== carstate.shipped,
                  ),
                },
              },
              {
                carstate: carstate.shipped,
                containers: {
                  container_number: {
                    not: null,
                  },
                },
              },
            ],
          },
        ],
      };
    }

    if (carState === 'on_hand_with_load') {
      where = {
        ...where,
        ...{
          carstate: carstate.shipped,
          containers: { container_number: null },
        },
      };
    }
    if (carState === 'shipped') {
      where = {
        ...where,
        ...{
          carstate: carstate.shipped,
          containers: {
            container_number: {
              not: null, // Ensure container_number is not null
            },
          },
        },
      };
    }
    const companiesDestinationId = dispatch.OR?.find(
      (condition) => condition['companies.destination_id'],
    )?.['companies.destination_id'];

    if (companiesDestinationId) {
      where = {
        ...where,
        OR: [
          { destination_id: dispatch?.OR[0].destination_id }, // Match destination_id = 12
          {
            destination_id: null,
            companies: { destination_id: companiesDestinationId },
          }, // Match destination_id = NULL with companies.destination_id = 12
        ],
      };
    }

    if (dispatch.eta_status) {
      where['containers'] = {
        bookings: {
          eta_status: {
            in: dispatch.eta_status,
          },
        },
      };
    }

    try {
      const [total, data] = await Promise.all([
        this.prisma.vehicles.count({
          where,
        }),
        this.prisma2.vehicles.findMany({
          where,
          ...paginate,
          orderBy,
          include: {
            pgl_used_cars: { select: { id: true } },
            yard_inventories: { select: { id: true } },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            payments: paymentSelect(),
            vehicle_towings: {
              select: {
                towing_company: true,
                tow_amount: true,
              },
            },
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_damages: {
              select: {
                id: true,
              },
              where: {
                deleted_at: null,
              },
            },
            _count: {
              select: {
                vehicle_images: {
                  where: { size: 250 }, // optional filter for count too
                },
                vehicle_auction_images: {
                  where: { size: 250 },
                },
              },
            },
            companies: { select: { name: true } },
            customers: {
              select: {
                companies: {
                  select: {
                    name: true,
                    destinations: {
                      select: {
                        name: true,
                        id: true,
                      },
                    },
                  },
                },
              },
            },
            containers: {
              select: {
                aes_filling_link: true,
                id: true,
                status: true,
                container_number: true,
                loading_date: true,
                booking_suffix: true,
                bookings: {
                  select: {
                    parent_id: true,
                    booking_number: true,
                    destinations: {
                      select: { id: true, name: true },
                    },
                    parent: {
                      select: {
                        booking_number: true,
                        eta: true,
                        id: true,
                      },
                    },
                    eta: true,
                    vessels: { select: { etd: true } },
                  },
                },
              },
            },
            pol_locations: { select: { name: true } },
            loading_cities: { select: { city_name: true } },
            auction_cities: {
              select: {
                city_name: true,
                loading_states: {
                  select: {
                    parent: {
                      select: {
                        name: true,
                      },
                    },
                  },
                },
              },
            },
            destinations: { select: { id: true, name: true } },
            yards_location: {
              select: {
                name: true,
                id: true,
                address_info: true,
              },
            },
            ...transaction_by,
            ...create_update_by,
          },
        }),
      ]);

      return { result: true, page: offset, per_page: limit, total, data };
    } catch (err) {
      return catch_response(err);
    } finally {
      await this.prisma.$disconnect();
    }
  };

  async getDispatch(query: any) {
    try {
      const { page = 1, per_page = 20 } = query;
      const where = await searchFilter(query, searchColumns);
      // const orderBy = await ordersBy(query);

      const paginate = {
        skip: (page - 1) * per_page,
        take: per_page,
      };

      const [total, data] = await Promise.all([
        this.prisma.vehicle_dispatches.count({ where }),
        this.prisma.vehicle_dispatches.findMany({
          where: { deleted_at: null },
          include: {
            vehicle: {
              select: {
                id: true,
                vin: true,
                lot_number: true,

                make: true,
                model: true,
                year: true,
                companies: {
                  select: { id: true, name: true },
                },
              },
            },
            created_by_user: {
              select: { id: true, fullname: true },
            },
          },
          // ...paginate,
          // orderBy,
        }),
      ]);

      return {
        result: true,
        page,
        per_page,
        total,
        data,
        total_pages: Math.ceil(total / per_page),
      };
    } catch (error) {
      throw catch_response(error);
    } finally {
      await this.prisma.$disconnect();
    }
  }

  getInventory = async (query: filterInventory) => {
    const offset = query.page;
    const limit = query.per_page;
    const locationId = query.locationId;
    const dispatch = JSON.parse(query.filterData);
    const where = await searchFilter(query, searchColumns);
    const orderBy = await ordersBy(query);
    if (locationId) where['point_of_loading'] = locationId;

    where['carstate'] = {
      in: [carstate.on_hand_no_title, carstate.on_hand_with_title],
    };

    if (dispatch?.dispatch_department?.length > 0) {
      where['OR'] = [
        { payment_date: { not: null } },
        { date_posted_in_central_dispatch: { not: null } },
        { request_for_pickup_date: { not: null } },
      ];
    }

    const property = {
      where,
      orderBy,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        hat_number: true,
        photo_link: true,
        carstate: true,
        vin: true,
        lot_number: true,
        customer_remark: true,
        halfcut_status: true,
        deliver_date: true,
        yards_location: { select: { id: true, name: true } },
        customers: {
          select: { companies: { select: { name: true } } },
        },
        containers: {
          select: {
            container_number: true,
          },
        },
        destinations: { select: { name: true } },
        pol_locations: { select: { id: true, name: true } },
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  changeHalfCutStatus = (
    query: changeVehiclesHalfCutStatus,
    updated_by: number,
  ) =>
    this.prisma.vehicles
      .updateMany({
        where: {
          id: { in: query.vehiclesIds },
          halfcut_status: vehicles_halfcut_status_enum.half_cut,
        },
        data: {
          halfcut_status: query.vehicleHalfCutStatus,
          updated_by,
        },
      })
      .then((data) => {
        if (data.count > 0)
          return {
            result: true,
            message: `${data.count} records have been updated!`,
          };
        else return { result: false, message: 'No records have been updated.' };
      })
      .catch((err) => catch_response(err));

  getHalfCut = async (query: filterHalfCut) => {
    const offset = query.page;
    const limit = query.per_page;
    const halfcutStatus = query.halfcutStatus;
    const locationId = query.locationId;
    const where = await searchFilter(query, searchColumns);
    const orderBy = await ordersBy(query);
    if (locationId) where['point_of_loading'] = locationId;
    if (halfcutStatus) where['halfcut_status'] = halfcutStatus;
    else where['halfcut_status'] = vehicles_halfcut_status_enum.half_cut;

    const property = {
      where,
      orderBy,
      select: {
        id: true,
        carstate: true,
        year: true,
        model: true,
        color: true,
        make: true,
        photo_link: true,
        hat_number: true,
        vin: true,
        lot_number: true,
        customer_remark: true,
        halfcut_status: true,
        deliver_date: true,
        containers: {
          select: {
            booking_suffix: true,
            bookings: {
              select: { booking_number: true },
            },
            container_number: true,
            companies: { select: { name: true } },
          },
        },
        pol_locations: { select: { name: true } },
        point_of_loading: true,
        destinations: { select: { name: true } },
        yard_location: true,
        purchased_at: true,
      },
    };
    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getTowCostReport = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const where = await searchFilter(query, searchColumns);
    where['carstate'] = {
      notIn: [carstate.auction_paid, carstate.auction_unpaid],
    };
    const orderBy = await ordersBy(query);
    const property = {
      where,
      orderBy,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        vin: true,
        deliver_date: true,
        auction_name: true,
        auction_city: true,
        auction_cities: {
          select: {
            city_name: true,
            loading_states: {
              select: {
                parent: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
        customers: {
          select: {
            companies: { select: { name: true } },
          },
        },
        vehicle_costs: {
          select: {
            towing_cost: true,
          },
        },
        containers: {
          select: {
            container_number: true,
          },
        },
        pol_locations: { select: { name: true } },
        yards_location: { select: { name: true } },
        destinations: { select: { name: true } },
        vehicle_towings: {
          select: {
            tow_amount: true,
          },
        },
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getCostAnalysis = async (query: filterVehicles) => {
    const offset = query.page;
    const limit = query.per_page;
    const dispatch = JSON.parse(query.filterData);
    const where = await searchFilter(query, searchColumns);

    if (dispatch?.dispatch_department?.length > 0) {
      where['OR'] = [
        { payment_date: { not: null } },
        { date_posted_in_central_dispatch: { not: null } },
        { request_for_pickup_date: { not: null } },
      ];
    }

    const orderBy = await ordersBy(query);
    const property: any = {
      where,
      orderBy,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        vin: true,
        payment_date: true,
        payment_date_to_pgl: true,
        request_for_pickup_date: true,
        ready_for_pickup_date: true,
        companies: {
          select: {
            name: true,
          },
        },
        storage_cost: true,
        customers: {
          select: {
            companies: { select: { name: true } },
          },
        },
        containers: {
          select: {
            container_number: true,
          },
        },
        vehicle_costs: true,
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getActiveVehiclesCountBasedOnDestionation = async (
    locationId,
    yard_location_id,
  ) => {
    try {
      const destiations = await this.prisma2.destinations.findMany({
        select: { id: true, name: true },
      });

      const destsQ = destiations.map(
        (d) =>
          `SUM(CASE WHEN (vehicles.destination_id = ${d.id} OR companies.destination_id = ${d.id})
        THEN 1 ELSE 0 END)::int AS dest_${d.id}`,
      );

      const q = `
      SELECT ${destsQ} FROM vehicles
        LEFT JOIN companies ON vehicles.company_id = companies.id
        WHERE vehicles.carstate IN (
          '${carstate.on_the_way}',
          '${carstate.on_hand_with_title}',
          '${carstate.on_hand_no_title}'
        ) ${locationId ? `AND vehicles.point_of_loading = ${locationId}` : ''}
          ${
            yard_location_id
              ? `AND vehicles.yard_location_id = ${yard_location_id}`
              : ''
          } AND vehicles.deleted_at IS NULL
        `;

      const res = await this.db.runRaw(q);
      const dests = [];
      Object.entries(res[0]).forEach(([key, value]) => {
        if (value) {
          const dest = destiations.find(
            (d) => d.id == +key.replace('dest_', ''),
          );
          dests.push({ destination: dest, count: value });
        }
      });
      return dests;
    } catch (error) {
      return catch_response(error);
    }
  };

  getSummary = async (query: vehicleSummaryDto) => {
    const page = query.page;
    const limit = query.per_page;
    const forInventory = query.inventory;
    const order_column = 'on_the_way';
    const order = query.order;
    const offset = (page - 1) * limit;
    let paginate = ` ORDER BY id ASC `;
    if (order_column && order) paginate = ` ORDER BY ${order_column} ${order}`;

    if (limit > 0) {
      paginate += `
      LIMIT ${limit}
      OFFSET ${offset} `;
    }
    let conds = `companies.deleted_at IS NULL `;
    let loading_comp = ``;
    let note_comp = ``;
    let reason_comp = ``;

    // const filterQuery = query.state
    //   ? ` AND SUM(CASE WHEN carstate IN ('${carstate.on_the_way}',
    //   '${carstate.on_hand_no_title}', '${carstate.on_hand_with_title}',
    //   '${carstate.auction_paid}', '${carstate.auction_unpaid}')THEN 1 ELSE 0 END)::int > 0
    //   AND SUM(CASE WHEN carstate = '${carstate.shipped}' THEN 1 ELSE 0 END)::int > 0`
    //   : '';

    if (query.locationId) {
      conds += ` AND vehicles.point_of_loading = ${query.locationId}`;
      loading_comp = ` AND yl.location_id = ${query.locationId}`;
      note_comp = ` AND location_id = ${query.locationId}`;
      reason_comp = ` AND location_id = ${query.locationId}`;
    }

    if (query.yard_location_id)
      conds += ` AND vehicles.yard_location_id = '${query.yard_location_id}'`;

    let having = ``;
    if (query.state == carStateFour.active_customer) {
      having = ` HAVING  SUM(CASE WHEN carstate  IN ('${carStateFour.on_the_way}','${carStateFour.on_hand_no_title}','${carStateFour.on_hand_with_title}','${carStateFour.pending}','${carStateFour.auction_paid}','${carStateFour.auction_uppaid}') THEN 1 ELSE 0 END)::int > 0 `;
    } else if (query.state == carStateFour.auction) {
      having = ` HAVING  SUM(CASE WHEN carstate  IN ('${carStateFour.auction_paid}','${carStateFour.auction_uppaid}') THEN 1 ELSE 0 END)::int > 0 `;
    } else if (query.state === carStateFour.half_cut) {
      having = ` HAVING  SUM(CASE WHEN vehicles.halfcut_status = '${carStateFour.half_cut}'
      AND carstate IN ('${carstate.on_hand_with_title}', '${carstate.on_hand_no_title}')
      THEN 1 ELSE 0 END)::int > 0 `;
    } else if (query.state) {
      having = ` HAVING SUM(CASE WHEN carstate = '${query.state}' THEN 1 ELSE 0 END)::int > 0 `;
    }

    if (query.search)
      conds += query.exactMatch
        ? ` AND companies.name='${query.search}'`
        : ` AND companies.name ILIKE '%${query.search}%' `;

    const commonQuery = (loadType) => {
      return `SUM(CASE WHEN carstate = '${carstate.on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.on_the_way},
      SUM(CASE WHEN carstate = '${carstate.pending_on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.pending_on_the_way},
      SUM(COALESCE(CASE WHEN carstate = '${carstate.on_hand_no_title}' AND halfcut_status in ('${vehicles_halfcut_status_enum.completed}', '${vehicles_halfcut_status_enum.unknown}') THEN 1 ELSE 0 END, 0))::int AS ${carstate.on_hand_no_title},
      SUM(COALESCE(CASE WHEN carstate = '${carstate.on_hand_with_title}' AND (halfcut_status in ('${vehicles_halfcut_status_enum.completed}', '${vehicles_halfcut_status_enum.unknown}') OR halfcut_status IS NULL) THEN 1 ELSE 0 END, 0))::int AS ${carstate.on_hand_with_title},
      SUM(CASE WHEN carstate = '${carstate.shipped}' AND containers.container_number IS NOT NULL THEN 1 ELSE 0 END)::int AS ${carstate.shipped},
      SUM(CASE WHEN carstate = '${carstate.shipped}' AND containers.container_number IS NULL THEN 1 ELSE 0 END)::int AS on_hand_with_load,
      SUM(CASE WHEN carstate = '${carstate.pending}' THEN 1 ELSE 0 END)::int AS ${carstate.pending},
      SUM(CASE WHEN carstate = '${carstate.auction_paid}' THEN 1 ELSE 0 END)::int AS ${carstate.auction_paid},
      SUM(CASE WHEN carstate = '${carstate.auction_unpaid}' THEN 1 ELSE 0 END)::int AS ${carstate.auction_unpaid},
      SUM(CASE WHEN carstate = '${carstate.pending_auction}' THEN 1 ELSE 0 END)::int AS ${carstate.pending_auction},
      SUM(CASE WHEN DATE(vehicles.deliver_date::timestamp) <= CURRENT_DATE - INTERVAL '10 days' AND carstate = '${carstate.on_hand_with_title}' THEN 1 ELSE 0 END)::int AS delivery_alert,
      SUM(CASE WHEN halfcut_status = '${vehicles_halfcut_status_enum.unknown}' AND carstate IN ('${carstate.on_hand_with_title}','${carstate.on_hand_no_title}','${carstate.on_the_way}' ) THEN 1 ELSE 0 END)::int AS unknown_halfcut,
      SUM(CASE WHEN halfcut_status = '${vehicles_halfcut_status_enum.half_cut}' AND carstate IN ('${carstate.on_hand_with_title}','${carstate.on_hand_no_title}' ) THEN 1 ELSE 0 END)::int AS ${carStateFour.half_cut}
      FROM companies
      LEFT JOIN vehicles ON vehicles.company_id = companies.id and vehicles.deleted_by is null
      LEFT JOIN destinations ON companies.destination_id = destinations.id
      LEFT JOIN containers ON vehicles.container_id = containers.id
      WHERE carstate IN ('${carstate.on_the_way}','${carstate.pending_on_the_way}','${carstate.pending_auction}','${carstate.on_hand_no_title}','${carstate.on_hand_with_title}','${carstate.shipped}','${carstate.pending}','${carstate.auction_paid}','${carstate.auction_unpaid}') AND (vehicles.deleted_at IS NULL  ${loadType}) AND ${conds}`;
    };

    const commonQuery2 = (loadType) => {
      return `SUM(CASE WHEN carstate = '${carstate.on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.on_the_way},
       SUM(CASE WHEN carstate = '${carstate.pending_on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.pending_on_the_way},
      SUM(COALESCE(CASE WHEN carstate = '${carstate.on_hand_no_title}' AND is_scrap = true THEN 1 ELSE 0 END, 0))::int AS ${carstate.on_hand_no_title},
      SUM(COALESCE(CASE WHEN carstate = '${carstate.on_hand_with_title}' AND (is_scrap = true OR halfcut_status IS NULL) THEN 1 ELSE 0 END, 0))::int AS ${carstate.on_hand_with_title},
      SUM(CASE WHEN carstate = '${carstate.shipped}' AND containers.container_number IS NOT NULL THEN 1 ELSE 0 END)::int AS ${carstate.shipped},
      SUM(CASE WHEN carstate = '${carstate.shipped}' AND containers.container_number IS NULL THEN 1 ELSE 0 END)::int AS on_hand_with_load,
      SUM(CASE WHEN carstate = '${carstate.pending}' THEN 1 ELSE 0 END)::int AS ${carstate.pending},
      SUM(CASE WHEN carstate = '${carstate.auction_paid}' THEN 1 ELSE 0 END)::int AS ${carstate.auction_paid},
      SUM(CASE WHEN carstate = '${carstate.auction_unpaid}' THEN 1 ELSE 0 END)::int AS ${carstate.auction_unpaid},
      SUM(CASE WHEN carstate = '${carstate.pending_auction}' THEN 1 ELSE 0 END)::int AS ${carstate.pending_auction},
      SUM(CASE WHEN DATE(vehicles.deliver_date::timestamp) <= CURRENT_DATE - INTERVAL '10 days' AND carstate = '${carstate.on_hand_with_title}' THEN 1 ELSE 0 END)::int AS delivery_alert,
      SUM(CASE WHEN halfcut_status = '${vehicles_halfcut_status_enum.unknown}' AND carstate IN ('${carstate.on_hand_with_title}','${carstate.on_hand_no_title}','${carstate.on_the_way}') THEN 1 ELSE 0 END)::int AS unknown_halfcut,
      SUM(CASE WHEN halfcut_status = '${vehicles_halfcut_status_enum.half_cut}' AND carstate IN ('${carstate.on_hand_with_title}','${carstate.on_hand_no_title}' ) THEN 1 ELSE 0 END)::int AS ${carStateFour.half_cut}
      FROM companies
      LEFT JOIN vehicles ON vehicles.company_id = companies.id and vehicles.deleted_by is null
      LEFT JOIN destinations ON companies.destination_id = destinations.id
      LEFT JOIN containers ON vehicles.container_id = containers.id
      WHERE carstate IN ('${carstate.on_the_way}','${carstate.pending_on_the_way}','${carstate.pending_auction}','${carstate.on_hand_no_title}','${carstate.on_hand_with_title}','${carstate.shipped}','${carstate.pending}','${carstate.auction_paid}','${carstate.auction_unpaid}') AND (vehicles.deleted_at IS NULL  ${loadType}) AND vehicles.load_type = 'mix' AND ${conds}`;
    };

    const q2 = `MIN(deliver_date) FILTER (WHERE carstate = '${
      carstate.on_hand_with_title
    }') AS min_deliver_date_with_title,
    MIN(deliver_date) FILTER (WHERE carstate = '${
      carstate.auction_paid && carstate.auction_unpaid
    }') AS min_deliver_date_auction,
    MIN(deliver_date) FILTER (WHERE carstate = '${
      carstate.on_the_way
    }') AS min_deliver_date_on_the_way,
    MIN(deliver_date) FILTER (WHERE carstate = '${
      carstate.pending_on_the_way
    }') AS min_deliver_date_pending_on_the_way,
    MIN(deliver_date) FILTER (WHERE carstate = '${
      carstate.on_hand_no_title
    }') AS min_deliver_date_no_title`;

    const q = ` SELECT companies.id, companies.mix, companies.mix_halfcut,companies.complete, companies.complete_halfcut, companies.name, companies.join_date, companies.destination_id, companies.tier_level, destinations.name AS dest_name,
    (SELECT STRING_AGG(DISTINCT TRIM(note),', ') FROM pglsystem.company_notes WHERE company_id=companies.id ${note_comp}) AS notes,
    (SELECT STRING_AGG(RIGHT(locations.name,2) || ' * ' || TRIM(reason), ', ') FROM pglsystem.company_notes LEFT JOIN pglsystem.locations on locations.id=company_notes.location_id WHERE company_id=companies.id ${reason_comp} ) AS reason,
    (
      SELECT STRING_AGG(yl.name, ' * ') AS yard_name
      FROM pglsystem."_company_loading"
      LEFT JOIN pglsystem.yard_locations yl ON yl.id = "B"
      WHERE "A"=companies.id ${loading_comp}
    ) AS Loading_companies,
    MAX(deliver_date) as deliver_date,
    ${q2},
    ${commonQuery(
      `${
        forInventory
          ? ''
          : " AND ( vehicles.load_type = 'full' OR vehicles.load_type IS NULL OR companies.destination_id NOT IN(12,24,22))"
      }`,
    )} GROUP BY companies.id, companies.name, companies.destination_id, destinations.name ${having}`;
    try {
      const [data, totalCount, total, activeVehicles] = await Promise.all([
        this.db.runRaw(q + paginate),
        this.db.runRaw(`SELECT ${commonQuery(``)}`),
        this.db.runRaw(`SELECT COUNT(*)::int AS all FROM (${q}) AS sub`),
        this.getActiveVehiclesCountBasedOnDestionation(
          query.locationId,
          query.yard_location_id,
        ),
      ]);
      let mixData = [];
      if (!forInventory) {
        const [mixOman, mixPoti, mixUae, mixHulfcutUae, unitedUaeScrap] =
          await Promise.all([
            this.db.runRaw(
              `SELECT ${q2}, ${commonQuery(
                `AND ( vehicles.load_type = 'mix'  AND (vehicles.destination_id = 24 OR (vehicles.destination_id IS NULL AND companies.destination_id = 24)))`,
              )}`,
            ),
            this.db.runRaw(
              `SELECT ${q2}, ${commonQuery(
                `AND ( vehicles.load_type = 'mix'  AND (vehicles.destination_id = 22 OR (vehicles.destination_id IS NULL AND companies.destination_id = 22)))`,
              )}`,
            ),
            this.db.runRaw(
              `SELECT ${q2}, ${commonQuery(
                `AND ( vehicles.load_type = 'mix' AND vehicles.is_scrap = false AND halfcut_status = 'completed' AND (vehicles.destination_id = 12 OR (vehicles.destination_id IS NULL AND companies.destination_id = 12 )))`,
              )}`,
            ),
            this.db.runRaw(
              `SELECT ${q2}, ${commonQuery(
                `AND ( vehicles.load_type = 'mix' AND halfcut_status = 'half_cut' AND (vehicles.destination_id = 12 OR (vehicles.destination_id IS NULL AND companies.destination_id = 12 )))`,
              )}`,
            ),
            this.db.runRaw(
              `SELECT ${q2}, ${commonQuery2(
                `AND (is_scrap = true AND (vehicles.destination_id = 12 OR (vehicles.destination_id IS NULL AND companies.destination_id = 12 )))`,
              )}`,
            ),
          ]);

        mixData = [
          {
            ...mixPoti?.[0],
            name: 'MIX POTI',
            destination_id: 22,
            dest_name: 'POTI, Georgia',
            id: 'poti',
          },
          {
            ...mixOman?.[0],
            name: 'MIX OMAN',
            destination_id: 24,
            dest_name: 'SALALAH, OM',
            id: 'oman',
          },
          {
            ...mixUae?.[0],
            name: 'MIX UAE',
            destination_id: 12,
            dest_name: 'JEBEL ALI, UAE',
            id: 'uae',
          },
        ];
        if (query.locationId != 1) {
          mixData = [
            ...mixData,
            {
              ...mixHulfcutUae?.[0],
              name: 'MIX HALFCUT UAE',
              destination_id: 12,
              dest_name: 'JEBEL ALI, UAE',
              id: 'halfcut-uae',
            },
          ];
        }
        mixData = [
          ...mixData,
          {
            ...unitedUaeScrap?.[0],
            name: 'United UAE Scrap',
            destination_id: 12,
            dest_name: 'JEBEL ALI, UAE',
            id: 'uae-scrap',
          },
        ];
      }

      const p = limit > 0 ? { page, per_page: limit } : {};
      return {
        result: true,
        ...p,
        total: total[0].all,
        data: {
          totalData: totalCount[0],
          data: [...data, ...mixData],
          activeVehicles,
        },
      };
    } catch (err) {
      catch_response(err);
    }
  };

  getHalfCutSummary = async (query: HalfCutSummaryDto) => {
    const page = query.page;
    const limit = query.per_page;
    const offset = (page - 1) * limit;
    const order_column = query.column == 'id' ? 'company_id' : query.column;
    const order = query.order;
    let where = ` ve.halfcut_status = 'half_cut' `;
    /* if (query.halfcutStatus)
      where = ` ve.halfcut_status = '${query.halfcutStatus}' `; */

    if (query.locationId)
      where += ` AND ve.point_of_loading = ${query.locationId}`;

    let paginate = ` ORDER BY company_id DESC`;
    if (order && order_column) paginate = ` ORDER BY ${order_column} ${order}`;

    if (limit > 0) {
      paginate += `
      LIMIT ${limit}
      OFFSET ${offset} `;
    }
    let having = ``;
    if (query.halfcutStatus)
      having = ` HAVING SUM(CASE WHEN ve.halfcut_status = '${query.halfcutStatus}' THEN 1 ELSE 0 END)::int > 0 `;

    // const commonQuery = `
    // SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.not_ready_to_halfcut}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.not_ready_to_halfcut},
    // SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.otw_halfcut}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.otw_halfcut},
    // SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.ready_to_halfcut}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.ready_to_halfcut},
    // SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.ready_to_ship}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.ready_to_ship},
    // SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.shipped}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.shipped},

    // SUM(CASE WHEN ve.carstate = '${carstate.on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.on_the_way},
    // SUM(CASE WHEN ve.carstate = '${carstate.on_hand_no_title}' THEN 1 ELSE 0 END)::int AS ${carstate.on_hand_no_title},
    // SUM(CASE WHEN ve.carstate = '${carstate.on_hand_with_title}' THEN 1 ELSE 0 END)::int AS ${carstate.on_hand_with_title},
    // SUM(CASE WHEN ve.carstate = '${carstate.shipped}' THEN 1 ELSE 0 END)::int AS ${carstate.shipped}_veh,
    // SUM(CASE WHEN ve.carstate = '${carstate.pending}' THEN 1 ELSE 0 END)::int AS ${carstate.pending}

    // FROM companies com
    // LEFT JOIN vehicles ve ON ve.company_id = com.id AND ve.deleted_at IS NULL
    // WHERE com.deleted_at IS NULL AND ${where}`;

    const commonQuery = `
    SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.half_cut}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.half_cut},
    SUM(CASE WHEN ve.halfcut_status = '${vehicles_halfcut_status_enum.completed}' THEN 1 ELSE 0 END)::int AS ${vehicles_halfcut_status_enum.completed},

    SUM(CASE WHEN ve.carstate = '${carstate.on_the_way}' THEN 1 ELSE 0 END)::int AS ${carstate.on_the_way},
    SUM(CASE WHEN ve.carstate = '${carstate.on_hand_no_title}' THEN 1 ELSE 0 END)::int AS ${carstate.on_hand_no_title},
    SUM(CASE WHEN ve.carstate = '${carstate.on_hand_with_title}' THEN 1 ELSE 0 END)::int AS ${carstate.on_hand_with_title},
    SUM(CASE WHEN ve.carstate = '${carstate.shipped}' THEN 1 ELSE 0 END)::int AS ${carstate.shipped}_veh,
    SUM(CASE WHEN ve.carstate = '${carstate.pending}' THEN 1 ELSE 0 END)::int AS ${carstate.pending}

    FROM companies com
    LEFT JOIN vehicles ve ON ve.company_id = com.id AND ve.deleted_at IS NULL
    WHERE com.deleted_at IS NULL AND ${where}`;

    const q = `SELECT com.id AS company_id, com.name AS company_name, (SELECT STRING_AGG(DISTINCT TRIM(note),', ')
    FROM company_notes WHERE company_id=com.id) AS note, ${commonQuery} GROUP BY com.id ${having}`;

    try {
      const data = await this.db.runRaw(q + paginate);
      const td = await this.db.runRaw(`SELECT ${commonQuery}`);
      const t = await this.db.runRaw(
        `SELECT COUNT(*)::int AS all FROM (${q}) AS sub`,
      );
      const p = limit > 0 ? { page, per_page: limit } : {};
      return {
        result: true,
        ...p,
        total: t[0].all,
        data: { totalData: td[0], data },
      };
    } catch (err) {
      catch_response(err);
    }
  };

  getDateLines = async (query: filterVehicles) => {
    const offset = query.page;
    const limit = query.per_page;
    const dispatch = JSON.parse(query.filterData);
    const where = await searchFilter(query, searchColumns);
    const orderBy = await ordersBy(query);
    const property = {
      where,
      orderBy,
      select: {
        id: true,
        vin: true,
        year: true,
        make: true,
        model: true,
        color: true,
        customers: {
          select: { id: true, companies: { select: { name: true, id: true } } },
        },
        company_id: true,
        point_of_loading: true,
        lot_number: true,
        purchased_at: true,
        payment_date: true,
        payment_date_to_pgl: true,
        date_posted_in_central_dispatch: true,
        pickup_date: true,
        request_for_pickup_date: true,
        ready_for_pickup_date: true,
        deliver_date: true,
        created_at: true,
        ...create_update_by,
      },
    };
    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    if (dispatch?.dispatch_department?.length > 0) {
      where['OR'] = [
        { payment_date: { not: null } },
        { date_posted_in_central_dispatch: { not: null } },
        { request_for_pickup_date: { not: null } },
      ];
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findOne = async (id: number) => {
    try {
      const res = await this.prisma.vehicles.findUnique({
        where: { id },
        include: {
          pgl_used_cars: { select: { id: true } },
          yard_inventories: { select: { id: true } },
          vehicle_towings: {
            select: {
              id: true,
              tow_amount: true,
              towing_company: true,
              towed_from: true,
            },
          },
          companies: {
            select: {
              name: true,
            },
          },
          vehicle_costs: true,
          vehicle_charges: {
            where: {
              deleted_at: null,
            },
          },
          vehicle_images: {
            where: { size: 250 },
            select: {
              id,
              vehicle_id: true,
              name: true,
              url: true,
              size: true,
            },
          },
          vehicle_storages: {
            orderBy: { id: 'desc' },
            select: {
              id: true,
              cost: true,
              date: true,
            },
          },
          payments: {
            select: {
              id: true,
              amount_applied: true,
              type: true,
            },
          },
          customers: {
            select: {
              id: true,
              fullname: true,
              companies: {
                select: {
                  name: true,
                  id: true,
                  destinations: { select: { id: true, name: true } },
                },
              },
            },
          },
          destinations: { select: { id: true, name: true } },
          containers: {
            select: {
              id: true,
              container_number: true,
              loading_date: true,
              booking_suffix: true,
              bookings: {
                select: {
                  id: true,
                  booking_number: true,
                  eta: true,
                  vessels: { select: { etd: true } },
                },
              },
              invoices: {
                where: {
                  deleted_at: null,
                },
                select: {
                  id: true,
                  status: true,
                },
              },
              pl_status: true,
              uae_pl_status: true,
              prelim_pl_status: true,
            },
          },
          pol_locations: { select: { id: true, name: true } },
          loading_cities: { select: { city_name: true } },
          auction_cities: {
            select: {
              city_name: true,
              loading_states: {
                select: {
                  parent: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          yards_location: { select: { id: true, name: true } },
          ...create_update_by,
        },
      });
      const mix_vehicle = await this.prisma.mix_shipping_vehicles.findFirst({
        where: { vehicle_id: id },
      });

      if (mix_vehicle) {
        res.is_mix_vehicle = true;
        res.mix_vehicle_id = mix_vehicle.id;
      }

      return res;
    } catch (err) {
      catch_response(err);
    } finally {
      await this.prisma.$disconnect();
    }
  };

  getVehicleImages = async (id, type) => {
    const model =
      type === 'warehouse'
        ? this.prisma.vehicle_images
        : this.prisma.vehicle_auction_images;
    const created_by =
      type === 'warehouse'
        ? 'users_vehicle_image_created_by'
        : 'users_vehicle_auction_image_created_by';
    return await model
      .findMany({
        where: { vehicle_id: id, size: 250 },
        include: {
          [created_by]: {
            select: {
              id: true,
              fullname: true,
            },
          },
        },
      })
      .then((res) => {
        return res;
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  deleteVehicleImage = async (ids: number[], type: string, user) => {
    const imagesId = ids.map((id) => Number(id));

    await vehicle_images_logs('Vehicle', imagesId, user);
    const model =
      type == 'warehouse'
        ? this.prisma.vehicle_images
        : this.prisma.vehicle_auction_images;

    return await model
      .deleteMany({
        where: {
          id: {
            in: imagesId,
          },
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  private findMany = (ids: number[]) =>
    this.prisma.vehicles
      .findMany({
        where: { id: { in: ids.map(Number) } },
        include: {
          vehicle_towings: {
            select: {
              id: true,
              tow_amount: true,
              towing_company: true,
              towed_from: true,
            },
          },
          vehicle_costs: true,
          customers: {
            select: {
              id: true,
              fullname: true,
              loginable: { select: { email: true } }, /// Email is added for sending email to customer.
              companies: { select: { name: true, id: true } },
            },
          },
          destinations: { select: { id: true, name: true } },
          containers: {
            select: {
              id: true,
              container_number: true,
              bookings: {
                select: {
                  id: true,
                  booking_number: true,
                  eta: true,
                  vessels: { select: { etd: true } },
                },
              },
            },
          },
          pol_locations: { select: { id: true, name: true } },
          yards_location: { select: { id: true, name: true } },
          yard: { select: { id: true, name: true } },
          users_vehicles_gate_passed_byTousers: { select: { fullname: true } },
          ...create_update_by,
        },
      })
      .then((data) => data)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  canUpdateCharges = async (vehicle) => {
    // Check if the vehicle has no containers or the containers have no invoices
    if (!vehicle.containers || vehicle.containers.invoices.length === 0) {
      return true;
    }

    // Check if any invoice has a 'pending' status
    if (
      vehicle.containers.invoices.some(
        (invoice) => !['open', 'past_due', 'paid'].includes(invoice.status),
      )
    ) {
      return true;
    }

    return false;
  };

  canUpdatePGLCosts = async (vehicle) => {
    // Check if the vehicle has no containers or the containers have no invoices
    if (this.canUpdateCharges(vehicle)) {
      return true;
    }

    // Check if any P/L report is not 'reviewed' status
    return vehicle.containers.prelim_pl_status != 'reviewed';
  };

  hasVehicleCostsChanged = (vehicle, dto: UpdateVehicleDto) => {
    const vehicleCostsKeys = [
      'vehicle_price',
      'other_cost',
      'towing_cost',
      'dismantal_cost',
      'sales_cost',
      'ship_cost',
      'pgl_storage_costs',
      'title_charge',
      'dubai_custom_cost',
      'add_information',
    ];

    const hasChargesChanged = vehicleCostsKeys.some(
      (key) => (vehicle?.vehicle_costs?.[key] ?? null) !== (dto?.[key] ?? null),
    );

    return hasChargesChanged;
  };

  hasVehiclePGLCostsChanged = (vehicle, dto: UpdateVehicleDto) => {
    const otherKeys = [
      'dismantle_costs',
      'title_cost',
      'storage_cost',
      'other_costs',
    ];

    const hasChargesChanged =
      otherKeys.some((key) => vehicle[key] !== dto[key]) ||
      vehicle?.vehicle_towings?.tow_amount !== dto.tow_amount;

    return hasChargesChanged;
  };

  update = async (id: number, dto: UpdateVehicleDto, user: any) => {
    const updated_by = user.loginable_id;
    const vehicle = await this.prisma2.vehicles
      .findFirst({
        where: { id: id },
        include: {
          vehicle_towings: true,
          companies: true,
          containers: { include: { invoices: true } },
          vehicle_costs: true,
          pgl_used_cars: { where: { deleted_at: null, deleted_by: null } },
          vehicle_storages: true,
          mix_shipping_vehicles: {
            include: {
              mix_shipping_vehicle_charges: {
                where: {
                  deleted_at: null,
                },
              },
            },
          },
        },
      })
      .finally(() => this.prisma.$disconnect());

    // handle mix_shipping_vehicle_charges
    // const is_mix_vehicle = dto.is_mix_vehicle;
    // if (is_mix_vehicle) {
    //   const delete_auction_storage =
    //     dto.pgl_storage_costs === 0 && dto.storage_cost === 0;

    //   const existingMixCharges =
    //     await this.prisma.mix_shipping_vehicle_charges.findMany({
    //       where: {
    //         mix_vehicle_id: dto.mix_vehicle_id,
    //         ...(delete_auction_storage
    //           ? {}
    //           : { name: { not: 'auction_storage' } }),
    //         deleted_at: null,
    //       },
    //     });

    //   const updatedChargeNames = dto.mix_vehicle_charges.map((c) => c.name);

    //   const chargesToDelete = existingMixCharges.filter(
    //     (charge) => !updatedChargeNames.includes(charge.name),
    //   );

    //   // soft deletes mix_shipping_vehicle_charges
    //   if (chargesToDelete.length > 0) {
    //     await this.prisma.mix_shipping_vehicle_charges
    //       .updateMany({
    //         where: {
    //           id: { in: chargesToDelete.map((c) => c.id) },
    //           deleted_at: null,
    //         },
    //         data: {
    //           deleted_at: new Date(),
    //           deleted_by: user.loginable_id,
    //         },
    //       })
    //       .catch((err) => catch_response(err))
    //       .finally(() => this.prisma.$disconnect());
    //   }

    //   // handle mix_vehicle auction_storage charge
    //   if (!delete_auction_storage) {
    //     await handleMixVehicleCharge({
    //       mix_vehicle_id: dto.mix_vehicle_id,
    //       chargeName: 'auction_storage',
    //       value: dto.pgl_storage_costs,
    //       cost_value: dto.storage_cost,
    //       user: user.loginable_id,
    //       prisma: this.prisma,
    //     });
    //   }

    //   // handle remaining mix_vehicle_charges
    //   for (const mixVehicleCharge of dto.mix_vehicle_charges) {
    //     await handleMixVehicleCharge({
    //       mix_vehicle_id: dto.mix_vehicle_id,
    //       chargeName: mixVehicleCharge.name,
    //       value: mixVehicleCharge.value,
    //       cost_value: mixVehicleCharge.cost_value,
    //       user: user.loginable_id,
    //       prisma: this.prisma,
    //     });
    //   }

    //   async function handleMixVehicleCharge({
    //     mix_vehicle_id,
    //     chargeName,
    //     value,
    //     cost_value,
    //     user,
    //     prisma,
    //   }) {
    //     // check for existing record including soft-deleted records
    //     const existing = await prisma.mix_shipping_vehicle_charges.findFirst({
    //       where: {
    //         mix_vehicle_id,
    //         name: chargeName,
    //       },
    //       orderBy: { created_at: 'desc' },
    //     });

    //     // update or revive mix_vehicle_charge
    //     if (existing) {
    //       await prisma.mix_shipping_vehicle_charges
    //         .update({
    //           where: { id: existing.id },
    //           data: {
    //             value,
    //             cost_value,
    //             updated_by: user,
    //             deleted_at: null,
    //             deleted_by: null,
    //           },
    //         })
    //         .finally(() => prisma.$disconnect());
    //     } else {
    //       // create new mix_vehicle_charge when no existing records found
    //       await prisma.mix_shipping_vehicle_charges
    //         .create({
    //           data: {
    //             mix_vehicle_id,
    //             name: chargeName,
    //             value,
    //             cost_value,
    //             created_by: user,
    //           },
    //         })
    //         .finally(() => prisma.$disconnect());
    //     }
    //   }
    // }

    // handle mix_shipping_vehicle_charges
    const is_mix_vehicle = dto.is_mix_vehicle;
    const old_pgl_storage_costs = vehicle?.vehicle_costs?.pgl_storage_costs;

    if (is_mix_vehicle) {
      const mix_vehicle_id = vehicle.mix_shipping_vehicles[0].id;

      await this.handleMixVehicleCharge({
        mix_vehicle_id: mix_vehicle_id,
        chargeName: 'auction_storage',
        value: dto.pgl_storage_costs,
        cost_value: dto.storage_cost,
        user: user.loginable_id,
        old_value: old_pgl_storage_costs,
      });
    }

    const hasChargesChanged = this.hasVehicleCostsChanged(vehicle, dto);

    if (hasChargesChanged) {
      const isChargesUpdatable = await this.canUpdateCharges(vehicle);
      if (!isChargesUpdatable)
        throw new HttpException(
          {
            result: false,
            message:
              "This vehicle's invoice status is open, paid, or passed due, though this vehicles charges can't be modified.",
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
    }

    if (
      vehicle?.carstate == 'on_hand_with_title' &&
      vehicle?.companies?.name !== 'unknown' &&
      dto.load_type == null
    ) {
      throw new HttpException(
        {
          result: false,
          message: 'Please select a load type!',
          status: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const hasPGLCostsChanged = this.hasVehiclePGLCostsChanged(vehicle, dto);

    if (hasPGLCostsChanged) {
      const isChargesUpdatable = await this.canUpdatePGLCosts(vehicle);
      if (!isChargesUpdatable)
        throw new HttpException(
          {
            result: false,
            message:
              "This vehicle's invoice status is open, paid, or passed due, or P/L reports statuses are reviewed, though this vehicles costs can't be modified.",
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
    }

    if (
      vehicle?.companies?.is_belong_to_used_car &&
      vehicle?.company_id != dto.company_id &&
      vehicle?.pgl_used_cars?.length > 0
    ) {
      const pglu_company = await this.prisma.companies
        .findFirst({
          where: { id: dto.company_id },
          select: { is_belong_to_used_car: true },
        })
        .finally(() => this.prisma.$disconnect());
      if (!pglu_company.is_belong_to_used_car) {
        throw new HttpException(
          {
            result: false,
            message:
              'This vehicle is related to PGLU, and before changing the company, please ask the PGLU team to remove it from their system. Than change the companys.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    /// Due Error (company_id, container_id constraints) In Update Vehicles, Code Commented.
    /* if (vehicle?.company_id != dto.company_id) {
      const mix_sh_veh = await this.prisma.mix_shipping_vehicles.findFirst({
        where: { vehicles: { id: vehicle?.id } },
        include: {
          mix_shipping_invoices: true,
        },
      });

      // updating company name as well incase we had mix_shipping_invoices
      if (mix_sh_veh?.mix_shipping_invoices) {
        await this.prisma.mix_shipping_invoices.update({
          where: { id: mix_sh_veh?.mix_shipping_invoices.id },
          data: { company_id: dto.company_id },
        });
      }
    } */

    if (vehicle?.containers?.pl_status === pl_status.reviewed) {
      const vehicleCostFieldNames = [
        'dismantal_cost',
        'ship_cost',
        'pgl_storage_costs',
        'title_charge',
        'dubai_custom_cost',
        'other_cost',
        'sales_cost',
        'towing_cost',
        'vehicle_price',
      ];

      // const vehiclesFieldNames = [
      //   'dismantle_costs',
      //   'title_cost',
      //   'storage_cost',
      //   'other_costs',
      // ];

      // const vehicleTowingFieldNames = ['tow_amount'];
      const vehicleCosts = vehicle.vehicle_costs ?? {};
      // const vehicleTowing = vehicle.vehicle_towings ?? {};

      const hasVehicleCostsFieldChanged = vehicleCostFieldNames.some(
        (fieldName) => {
          return vehicleCosts[fieldName] !== dto[fieldName];
        },
      );

      // const hasVehiclesFieldsChanged = vehiclesFieldNames.some((fieldName) => {
      //   return vehicle[fieldName] !== dto[fieldName];
      // });

      // const hasVehicleTowingFieldsChanged = vehicleTowingFieldNames.some(
      //   (fieldName) => {
      //     return vehicleTowing[fieldName] !== dto[fieldName];
      //   },
      // );

      if (
        hasVehicleCostsFieldChanged
        // || hasVehiclesFieldsChanged ||
        // hasVehicleTowingFieldsChanged
      ) {
        throw new HttpException(
          {
            result: false,
            message:
              'Reviewed Container Vehicle Costs/Charges can not be modified.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const d = Object.assign({}, dto);
    delete dto.towing_company;
    delete dto.tow_amount;
    delete dto.towed_from;
    delete dto.dismantal_cost;
    delete dto.ship_cost;
    delete dto.pgl_storage_costs;
    delete dto.title_charge;
    delete dto.dubai_custom_cost;
    delete dto.other_cost;
    delete dto.sales_cost;
    delete dto.towing_cost;
    delete dto.vehicle_price;
    delete dto.invoice_description;
    delete dto.add_information;
    delete dto.is_mix_vehicle;
    delete dto.mix_vehicle_id;
    delete dto.mix_vehicle_charges;

    if (!dto.request_for_pickup_date) {
      dto.request_for_pickup_date = dto.payment_date_to_pgl ?? dto.payment_date;
    }

    if (!dto.ready_for_pickup_date_editable) {
      dto.ready_for_pickup_date =
        dto.payment_date_to_pgl ??
        (new Date(dto.payment_date) > new Date(dto.request_for_pickup_date)
          ? dto.payment_date
          : dto.request_for_pickup_date);
    }

    if (
      moment(dto.ready_for_pickup_date).format('YYYY-MM-DD') !==
      moment(vehicle.ready_for_pickup_date).format('YYYY-MM-DD')
    ) {
      await this.calculateAndChargeStorage(vehicle, updated_by);
    }

    const vehicles: any = await this.prisma.vehicles
      .findUnique({
        where: { id },
        include: {
          vehicle_charges: {
            where: {
              deleted_at: null,
            },
          },
        },
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

    const updatedMap = new Map<string, VehicleChargesDTO>();
    for (const ch of d.vehicle_charges) {
      updatedMap.set(`${ch.category}__${ch.name}`, ch);
    }

    const mergedCharges: VehicleChargesDTO[] = [];
    for (const dbCharge of vehicles.vehicle_charges) {
      const key = `${dbCharge.category}__${dbCharge.name}`;
      if (updatedMap.has(key)) {
        mergedCharges.push(updatedMap.get(key)!);
        updatedMap.delete(key);
      } else {
        mergedCharges.push({ ...dbCharge, deleted: true });
      }
    }

    for (const ch of updatedMap.values()) {
      mergedCharges.push(ch);
    }

    const data = {
      ...dto,
      inspection: dto.inspection,
      inspector_name: dto.inspector_name,
      weight: dto.weight.toString(),
      updated_by,
      vehicle_costs: {
        upsert: {
          create: {
            dismantal_cost: d.dismantal_cost,
            ship_cost: d.ship_cost,
            pgl_storage_costs: d.pgl_storage_costs,
            title_charge: d.title_charge,
            dubai_custom_cost: d.dubai_custom_cost,
            other_cost: d.other_cost,
            sales_cost: d.sales_cost,
            towing_cost: d.towing_cost,
            vehicle_price: d.vehicle_price,
            invoice_description: d.invoice_description,
            add_information: d.add_information,
            created_by: updated_by, // or user.loginable_id
          },
          update: {
            dismantal_cost: d.dismantal_cost,
            ship_cost: d.ship_cost,
            pgl_storage_costs: d.pgl_storage_costs,
            title_charge: d.title_charge,
            dubai_custom_cost: d.dubai_custom_cost,
            other_cost: d.other_cost,
            sales_cost: d.sales_cost,
            towing_cost: d.towing_cost,
            vehicle_price: d.vehicle_price,
            invoice_description: d.invoice_description,
            add_information: d.add_information,
            updated_by,
          },
        },
      },
      vehicle_charges: {
        upsert: mergedCharges?.map((ch: any) => {
          const updatedOrDeleted =
            ch.deleted && ch.deleted
              ? {
                  deleted_at: new Date(),
                  deleted_by: user.loginable_id,
                }
              : {
                  updated_at: new Date(),
                  updated_by: user.loginable_id,
                  deleted_at: null,
                  deleted_by: null,
                };
          return {
            where: {
              vehicle_id_category_name: {
                vehicle_id: id,
                name: ch.name,
                category: ch.category ? ch.category : '',
              },
            },
            create: {
              name: ch.name,
              amount: ch.amount,
              cost: ch.cost,
              remark: ch.remark,
              category: ch.category,
              created_at: new Date(),
              created_by: user.loginable_id,
            },
            update: {
              name: ch.name,
              amount: ch.amount,
              cost: ch.cost,
              remark: ch.remark,
              category: ch.category,
              ...updatedOrDeleted,
            },
          };
        }),
      },
    };

    if (vehicle.carstate == carstate.auction_unpaid && d.payment_date) {
      if (await check_permissions(user.userId, 'auction_unpaid_vehicles')) {
        data['carstate'] = carstate.auction_paid;
        data['moved_to_paid_date'] = d.payment_date;
      } else throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }

    if (vehicle.carstate == carstate.auction_paid && !d.payment_date)
      data['carstate'] = carstate.auction_unpaid;

    if (
      (d.tow_amount && d.tow_amount != undefined) ||
      (d.towing_company && d.towing_company != undefined) ||
      (d.towed_from && d.towed_from != undefined)
    ) {
      data['vehicle_towings'] = vehicle.vehicle_towings
        ? {
            update: {
              tow_amount: d.tow_amount,
              towing_company: d.towing_company,
              towed_from: d.towed_from,
              updated_by,
            },
          }
        : {
            create: {
              tow_amount: d.tow_amount,
              towing_company: d.towing_company,
              towed_from: d.towed_from,
              created_by: updated_by,
            },
          };
    }
    try {
      const result = await this.prisma.vehicles
        .update({
          where: { id },
          data,
          include: {
            vehicle_costs: true,
            vehicle_towings: true,
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
              select: {
                id: true,
                name: true,
                amount: true,
                cost: true,
                remark: true,
              },
            },
          },
        })
        .finally(() => this.prisma.$disconnect());

      if ('carstate' in data) {
        add_tracking([result?.id], data.carstate as tracking_status);
      }

      ///---This is for update the Number Of Units In Containers ---///
      if (
        vehicle.container_id &&
        vehicle.halfcut_status != dto.halfcut_status
      ) {
        const [vehicles, container] = await Promise.all([
          this.prisma.vehicles.findMany({
            where: { container_id: vehicle.container_id },
          }),
          this.prisma.containers.findFirst({
            where: { id: vehicle.container_id },
            select: {
              bookings: { select: { size: true } },
            },
          }),
        ]).finally(() => this.prisma.$disconnect());

        const size = container?.bookings?.size;
        let complete = 0;
        let half = 0;

        vehicles.forEach((vehicle) => {
          if (vehicle?.halfcut_status === 'completed') complete += 1;
          if (vehicle?.halfcut_status === 'half_cut') half += 1;
        });

        let no_units_load = `${complete} USED AUTO | `;

        if (half > 0 && complete > 0) {
          no_units_load += `ONE LOTS OF USED AUTO PARTS | ${size}`;
        } else if (half > 0 && complete == 0) {
          no_units_load = `ONE LOTS OF USED AUTO PARTS | ${size}`;
        } else {
          no_units_load += size;
        }

        await this.prisma.containers
          .update({
            where: {
              id: vehicle.container_id,
            },
            data: {
              no_units_load,
            },
          })
          .finally(() => this.prisma.$disconnect());
      }
      // Add vehicle to pglu if the company name was united unstaoppable car auction LLC and color = #FFFFFF
      const company = await this.prisma.companies
        .findUnique({
          where: { id: dto.company_id },
          select: { is_belong_to_used_car: true },
        })
        .finally(() => this.prisma.$disconnect());
      if (company?.is_belong_to_used_car) {
        const existingPuc = await this.prisma.pgl_used_cars
          .findFirst({
            where: { vehicle_id: id },
            select: { id: true },
          })
          .finally(() => this.prisma.$disconnect());
        if (existingPuc == null) {
          const puc = await this.prisma.pgl_used_cars
            .create({
              data: {
                vehicle_id: result?.id,
                selling_status: 2,
                selling_status_color: '#FFFFFF',
                created_by: updated_by,
                updated_by,
              },
              select: { id: true },
            })
            .finally(() => this.prisma.$disconnect());

          await this.prisma.pgl_used_car_costs
            .create({
              data: {
                pgl_used_car_id: puc.id,
                ten_percent_vat_duty: (10 * dto.price) / 100,
                clearance: 300,
                created_by: updated_by,
                updated_by,
              },
            })
            .finally(() => this.prisma.$disconnect());
        } else {
          await this.prisma.pgl_used_cars
            .update({
              where: { id: existingPuc.id },
              data: {
                deleted_at: null,
                deleted_by: null,
                pgl_used_car_costs: {
                  update: { deleted_at: null, deleted_by: null },
                },
              },
            })
            .finally(() => this.prisma.$disconnect());
        }
      }
      // End PGLU section

      const data1 = await this.findOne(result?.id);
      return { result: true, data: data1 };
    } catch (err) {
      catch_response(err);
    }
  };
  handleMixVehicleCharge = async ({
    mix_vehicle_id,
    chargeName,
    value,
    cost_value,
    user,
    old_value,
  }) => {
    if (chargeName === 'auction_storage') {
      // Check for an existing active 'auction_storage' charge
      const existingActive =
        await this.prisma.mix_shipping_vehicle_charges.findFirst({
          where: {
            mix_vehicle_id: mix_vehicle_id,
            name: chargeName,
          },
        });

      const valid =
        existingActive?.value === 0 || existingActive?.value === old_value;
      if (existingActive && valid) {
        // Revive the charge if it was soft-deleted and update values
        await this.prisma.mix_shipping_vehicle_charges
          .update({
            where: { id: existingActive.id },
            data: {
              value: value,
              cost_value: cost_value,
              updated_by: user,
              updated_at: new Date(),
              deleted_at: null,
              deleted_by: null,
            },
          })
          .finally(() => this.prisma.$disconnect());
      } else if (!existingActive && value > 0) {
        // Create a new charge record
        await this.prisma.mix_shipping_vehicle_charges
          .create({
            data: {
              mix_vehicle_id: mix_vehicle_id,
              name: chargeName,
              value: value,
              cost_value: cost_value,
              created_by: user,
            },
          })
          .finally(() => this.prisma.$disconnect());
      }
    }

    // Proceed with existing logic for other charges or when update is needed
  };

  gatePass = async (id: number, gate_passed_by: number) => {
    const t = await this.prisma.vehicles
      .findFirst({
        where: {
          id,
          gate_passed_by: { not: null },
          gate_passed_at: { not: null },
        },
        select: {
          vin: true,
          users_vehicles_gate_passed_byTousers: { select: { fullname: true } },
          gate_passed_at: true,
        },
      })
      .finally(() => this.prisma.$disconnect());

    if (t) {
      return {
        result: false,
        message: `Info! Vehicle with vin(${
          t.vin
        }) has been existed from our yard by ${
          t.users_vehicles_gate_passed_byTousers.fullname
        } at ${moment(t.gate_passed_at).utc().format('YYYY-MM-DD h:mm A')}`,
      };
    } else {
      const data = await this.prisma.vehicles
        .update({
          where: { id },
          data: { gate_passed_by, gate_passed_at: new Date() },
          // include: {
          //   yard: { select: { name: true } },
          //   users_vehicles_gate_passed_byTousers: {
          //     select: { fullname: true },
          //   },
          //   customers: {
          //     select: {
          //       fullname: true,
          //     },
          //   },
          // },
        })
        .finally(() => this.prisma.$disconnect());
      // await email(
      //   // statics.VEHICLE_GATE_PASSED_EMAIL,
      //   '<EMAIL>',
      //   `Gate Pass of (PGL) Car ${id}`,
      //   data,
      //   'gate_pass_vehicles',
      // );

      await reactEmail(
        statics.VEHICLE_GATE_PASSED_EMAIL,
        // '<EMAIL>',
        `Gate Pass of (PGL) Car ${id}`,
        { data: data },
        GatePassEmailTemplate,
        null,
        {
          from: process.env.MAILGUN_FROM,
          // cc: ['<EMAIL>'],
        },
      );

      ////// please don not remove this code when it gave conflicted ///////
      const data2 = await this.prisma.vehicles.findFirst({
        where: {
          id,
        },
        select: {
          id: true,
          model: true,
          color: true,
          make: true,
          year: true,
          gate_passed_at: true,
          vin: true,
          lot_number: true,
          users_vehicles_gate_passed_byTousers: {
            select: { fullname: true },
          },
          customers: { select: { fullname: true } },
          yard: { select: { name: true } },
          yard_inventories: {
            select: { hat_number: true, yard: { select: { name: true } } },
          },
        },
      });
      return { result: true, data: data2 };
    }
  };

  gatePassPrint = (id: number) =>
    this.prisma.vehicles
      .findFirst({
        where: {
          id,
          gate_passed_at: { not: null },
          gate_passed_by: { not: null },
        },
        select: {
          id: true,
          model: true,
          color: true,
          make: true,
          year: true,
          gate_passed_at: true,
          vin: true,
          lot_number: true,
          users_vehicles_gate_passed_byTousers: { select: { fullname: true } },
          customers: { select: { fullname: true } },
          yard: { select: { name: true } },
          yard_inventories: {
            select: { hat_number: true, yard: { select: { name: true } } },
          },
        },
      })
      .then((data) => ({ result: data ? true : false, data }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  addStorage = async (id: number, updated_by: number, query: addStorageDto) => {
    try {
      const vehicle = await this.prisma.vehicles.findFirst({
        where: { id: id },
        include: {
          vehicle_storages: true,
          vehicle_costs: true,
          mix_shipping_vehicles: {
            include: {
              mix_shipping_vehicle_charges: {
                where: {
                  deleted_at: null,
                },
              },
            },
          },
        },
      });
      const newVehicleStorages = query.storage_values.filter(
        (item) => !item.id,
      );

      const updatedVehicleStorages = query.storage_values.filter(
        (item) => item.id,
      );

      const deletedVehicleStorages = vehicle.vehicle_storages
        .filter(
          (item) =>
            updatedVehicleStorages.find((e) => e.id === item.id) == null,
        )
        .map((item) => item.id);

      await this.prisma2.vehicles.update({
        where: { id },
        data: {
          vehicle_storages: {
            deleteMany: {
              id: { in: deletedVehicleStorages },
            },
            createMany: {
              data: newVehicleStorages.map((item) => ({
                date: new Date(item.date),
                cost: item.cost,
                created_by: updated_by,
              })),
            },
            updateMany: updatedVehicleStorages.map((item) => ({
              where: { id: item.id },
              data: {
                date: new Date(item.date),
                cost: item.cost,
              },
            })),
          },
        },
        include: { vehicle_costs: true, vehicle_towings: true },
      });

      await this.calculateAndChargeStorage(vehicle, updated_by);

      const data1 = await this.findOne(vehicle?.id);

      return { result: true, data: data1 };
    } catch (err) {
      catch_response(err);
    }
  };

  calculateAndChargeStorage = async (vehicle, updated_by) => {
    const storageCalc = await this.storageService.getStorageReportsVehicles({
      filterData: JSON.stringify({ id: vehicle?.id }),
      exactMatch: false,
      search: '',
      searchOptions: null,
      column: null,
      order: null,
      per_page: 1,
      page: 1,
    });
    if (storageCalc?.total) {
      const storageValues = storageCalc.data[0];
      await this.prisma2.vehicles.update({
        where: { id: vehicle?.id },
        data: {
          storage_cost: storageValues.total_storage,
          vehicle_costs: {
            update: {
              pgl_storage_costs: storageValues.total_storage_on_customer,
            },
          },
          updated_by,
        },
      });

      const mix_vehicle = vehicle?.mix_shipping_vehicles[0];
      if (mix_vehicle) {
        const storage =
          await this.prisma.mix_shipping_vehicle_charges.findFirst({
            where: { mix_vehicle_id: mix_vehicle.id, name: 'auction_storage' },
          });
        const oldValue = vehicle?.vehicle_costs?.pgl_storage_costs;

        const valid = storage?.value === 0 || storage?.value === oldValue;
        if (storage && valid) {
          await this.prisma.mix_shipping_vehicle_charges
            .update({
              where: { id: storage.id, name: 'auction_storage' },
              data: {
                value: storageValues?.total_storage_on_customer,
                updated_by: updated_by,
                updated_at: new Date(),
              },
            })
            .finally(() => this.prisma.$disconnect());
        } else if (!storage && storageValues?.total_storage_on_customer > 0) {
          await this.prisma.mix_shipping_vehicle_charges
            .create({
              data: {
                mix_vehicle_id: mix_vehicle.id,
                name: 'auction_storage',
                value: storageValues.total_storage_on_customer,
                created_by: updated_by,
                created_at: new Date(),
              },
            })
            .finally(() => this.prisma.$disconnect());
        }
      }
    }
  };

  // addVehiclesToContainer = async (
  //   query: vehiclesToContainer,
  //   updated_by: number,
  // ) => {
  //   if (query.yard > 0) {
  //     await this.prisma.containers
  //       .update({
  //         where: {
  //           id: query.containerId,
  //         },
  //         data: {
  //           yard_location_id: query.yard,
  //         },
  //       })
  //       .then((result) => result)
  //       .catch((e: any) => catch_response(e));
  //   }

  //   return this.prisma.vehicles
  //     .updateMany({
  //       where: { id: { in: query.vehiclesIds } },
  //       data: {
  //         container_id: query.containerId,
  //         carstate: carstate.shipped,
  //         updated_by,
  //       },
  //     })
  //     .then(async (r) => {
  //       await this.updateContainerSizeUnite(query.containerId);
  //       add_tracking(query.vehiclesIds, tracking_status.shipped);
  //       return {
  //         result: r.count > 0,
  //         message:
  //           r.count > 0
  //             ? `${r.count} records have been updated!`
  //             : 'Update failed.',
  //       };
  //     })
  //     .catch((err) => catch_response(err))
  //     .finally(() => this.prisma.$disconnect());
  // };

  addVehiclesToContainer = async (
    query: vehiclesToContainer,
    updated_by: number,
  ) => {
    if (query.yard > 0) {
      await this.prisma.containers
        .update({
          where: {
            id: query.containerId,
          },
          data: {
            yard_location_id: query.yard,
          },
        })
        .then((result) => result)
        .catch((e: any) => catch_response(e));
    }
    const container = await this.prisma.containers.findUnique({
      where: {
        id: query.containerId,
      },
      include: {
        bookings: true,
      },
    });
    const vehicle = await this.prisma.vehicles.findMany({
      where: { id: { in: query.vehiclesIds } },
      select: {
        vin: true,
        lot_number: true,
      },
    });
    const vehicleList = vehicle
      .map((vehicle) => {
        const vinText = vehicle.vin ? `${vehicle.vin}` : '';
        return `${vinText}`;
      })
      .join(', ');
    const newDescription = `\n[${vehicleList}] in ${query.containerId}`;
    newDescription.concat(` in [${query.containerId}]`);
    await this.prisma.containers
      .update({
        where: {
          id: query.containerId,
        },
        data: {
          title_status: container.title_status
            ? container_title_status.replace
            : null,
          aes_status: container.aes_status ? container_aes_status.amend : null,
          bookings: {
            update: {
              ...(container.bookings?.si === 'submitted' ||
              container.bookings?.si === 'confirmed' ||
              container.booking?.si === 'amend_si'
                ? {
                    description: {
                      set: container.bookings.description + newDescription,
                    },
                    si: 'amend_si',
                  }
                : {}),
            },
          },
        },
      })
      .then((result) => {
        return result;
      })
      .catch((e: any) => catch_response(e));

    return this.prisma.vehicles
      .updateMany({
        where: { id: { in: query.vehiclesIds } },
        data: {
          container_id: query.containerId,
          carstate: carstate.shipped,
          updated_by,
        },
      })
      .then(async (r) => {
        // await this.updateContainerSizeUnite(query.containerId);
        const [vehicles, container] = await Promise.all([
          this.prisma.vehicles.findMany({
            where: { container_id: query.containerId },
          }),
          this.prisma.containers.findFirst({
            where: { id: query.containerId },
            select: {
              bookings: { select: { size: true } },
              container_number: true,
            },
          }),
        ]).finally(() => this.prisma.$disconnect());
        add_tracking(
          query.vehiclesIds,
          container?.container_number
            ? tracking_status.shipped
            : tracking_status.on_hand_with_load,
        );

        const size = container?.bookings?.size;
        let complete = 0;
        let half = 0;

        vehicles.forEach((vehicle) => {
          if (vehicle?.halfcut_status === 'completed') complete += 1;
          if (vehicle?.halfcut_status === 'half_cut') half += 1;
        });

        let no_units_load = `${complete} USED AUTO | `;

        if (half > 0 && complete > 0) {
          no_units_load += `ONE LOTS OF USED AUTO PARTS | ${size}`;
        } else if (half > 0 && complete == 0) {
          no_units_load = `ONE LOTS OF USED AUTO PARTS | ${size}`;
        } else {
          no_units_load += size;
        }

        await this.prisma.containers.update({
          where: {
            id: query.containerId,
          },
          data: {
            no_units_load,
          },
        });
        await addAndRemoveToShipment(
          vehicles,
          query.vehiclesIds,
          updated_by,
          this.prisma,
          'add to container',
        );

        return {
          result: r.count > 0,
          message:
            r.count > 0
              ? `${r.count} records have been updated!`
              : 'Update failed.',
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };
  // private updateContainerSizeUnite = async (container_id: number) => {
  //   const vehicles = await this.prisma.vehicles.count({
  //     where: { container_id },
  //   });
  //   const conts = await this.prisma.containers.findUnique({
  //     where: { id: container_id },
  //     select: { no_units_load: true },
  //   });

  //   await this.prisma.containers.update({
  //     where: { id: container_id },
  //     data: {
  //       no_units_load: `${vehicles} ${conts.no_units_load.trim().substring(1)}`,
  //     },
  //   });
  // };

  changeVehiclesState = async (query: changeVehiclesState, user: any) => {
    const updated_by = user.loginable_id;
    const { vehiclesIds, vehicleState } = query;

    // Check for vehicles with empty or unknown halfcut_status
    const statesToValidateHalfcut = [
      carstate.on_hand_with_title,
      carstate.on_the_way,
      carstate.on_hand_no_title,
      carstate.pending_on_the_way,
      carstate.pending,
    ];

    if (statesToValidateHalfcut.includes(vehicleState as any)) {
      const vehiclesWithInvalidHalfcut = await this.prisma.vehicles.findMany({
        where: {
          id: { in: vehiclesIds },
          OR: [
            { halfcut_status: null },
            { halfcut_status: vehicles_halfcut_status_enum.unknown },
          ],
        },
        select: {
          id: true,
          vin: true,
          halfcut_status: true,
        },
      });

      if (vehiclesWithInvalidHalfcut.length > 0) {
        throw new HttpException(
          {
            result: false,
            message: 'Failed! The vehicle halfcut status is empty or unknown.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    type ContainerGroup = {
      [containerId: string]: {
        vehicles: any[];
        booking: any;
      };
    };

    const prisma: typeof DbService.prisma = this.prisma;

    const vehiclesToUpdate = await prisma.vehicles.findMany({
      where: {
        id: { in: vehiclesIds },
        carstate: {
          in: [carstate.shipped, carstate.on_the_way, carstate.auction_unpaid],
        },
      },
      include: {
        containers: {
          select: {
            id: true,
            title_status: true,
            aes_status: true,
            bookings: {
              select: {
                id: true,
                si: true,
                description: true,
              },
            },
          },
        },
      },
    });

    // Group vehicles by container to handle all removals together
    const containerGroups = vehiclesToUpdate.reduce<ContainerGroup>(
      (acc, vehicle) => {
        const containerId = vehicle.containers?.id;
        if (containerId) {
          if (!acc[containerId]) {
            acc[containerId] = {
              vehicles: [],
              booking: vehicle.containers.bookings,
            };
          }
          acc[containerId].vehicles.push(vehicle);
        }
        return acc;
      },
      {},
    );

    const updateOperations = Object.entries(containerGroups)
      .map(([containerId, group]) => {
        const { vehicles, booking } = group;
        if (!booking) return [];

        // Create description with all VINs being removed
        const vehicleList = vehicles
          .map((vehicle) => `${vehicle.vin || ''}`)
          .join(', ');
        const newDescription = `[${vehicleList}] out ${containerId}`;

        const updateBooking = prisma.bookings.update({
          where: { id: booking.id },
          data: {
            ...(booking.si === 'submitted' ||
            booking.si === 'confirmed' ||
            booking.si === 'amend_si'
              ? {
                  description: {
                    set: booking.description
                      ? `${booking.description}, \n${newDescription}`
                      : newDescription,
                  },
                  si: 'amend_si',
                }
              : {}),
          },
        });

        const updateContainer = prisma.containers.update({
          where: { id: parseInt(containerId) }, // Convert string key to number
          data: {
            title_status: vehicles[0].containers.title_status
              ? container_title_status.replace
              : null,
            aes_status: vehicles[0].containers.aes_status
              ? container_aes_status.amend
              : null,
          },
        });

        return [updateBooking, updateContainer];
      })
      .flat();

    await prisma.$transaction(updateOperations);

    const newYork = momentTimeZone.tz(new Date(), 'America/New_York');
    const losAngeles = newYork.clone().tz('America/Los_Angeles');
    const losAngelesDate = new Date(losAngeles.format('YYYY-MM-DD'));

    const shippedVehicles = vehiclesToUpdate
      .filter((vehicle) => vehicle.carstate === carstate.shipped)
      .map((vehicle) => vehicle.id);

    const onTheWayVehicles = vehiclesToUpdate
      .filter((vehicle) => vehicle.carstate === carstate.on_the_way)
      .map((vehicle) => vehicle.id);

    const auctionUnpaidVehicles = vehiclesToUpdate.filter(
      (vehicle) => vehicle.carstate === carstate.auction_unpaid,
    );

    if (
      vehicleState === carstate.on_hand_with_title ||
      vehicleState === carstate.on_hand_no_title
    ) {
      await prisma.vehicles.updateMany({
        where: { id: { in: onTheWayVehicles } },
        data: { deliver_date: losAngelesDate },
      });
    }

    if (
      shippedVehicles.length > 0 &&
      !(await check_permissions(user.userId, 'shipped_to_any'))
    ) {
      return { count: 0 };
    }

    if (shippedVehicles.length > 0) {
      const vehicles = await this.prisma.vehicles.findMany({
        where: { id: { in: shippedVehicles } },
      });

      await this.prisma.vehicles.updateMany({
        where: { id: { in: shippedVehicles } },
        data: { container_id: null },
      });

      await addAndRemoveToShipment(
        vehicles,
        shippedVehicles,
        user.loginable_id,
        this.prisma,
        'remove from container',
      );
    }

    if (vehicleState === carstate.on_hand_no_title) {
      await prisma.vehicles.updateMany({
        where: { id: { in: vehiclesIds } },
        data: { is_title_exist: false },
      });
    }

    if (
      vehicleState === carstate.on_hand_with_title ||
      vehicleState === carstate.shipped
    ) {
      await prisma.vehicles.updateMany({
        where: { id: { in: vehiclesIds } },
        data: { is_title_exist: true },
      });
    }

    if (
      vehicleState === carstate.auction_paid &&
      auctionUnpaidVehicles.length > 0
    ) {
      for (const v of auctionUnpaidVehicles) {
        await prisma.vehicles.update({
          where: { id: v.id },
          data: {
            payment_date: v.payment_date ?? losAngelesDate,
            request_for_pickup_date:
              v.request_for_pickup_date ?? losAngelesDate,
            moved_to_paid_date: losAngelesDate,
          },
        });
      }
    }

    if (vehicleState === carstate.on_the_way) {
      const auctionPaidVehicles = await prisma.vehicles.findMany({
        where: {
          id: { in: vehiclesIds },
          carstate: carstate.auction_paid,
          OR: [
            { halfcut_status: null },
            { halfcut_status: vehicles_halfcut_status_enum.unknown },
          ],
        },
      });

      if (auctionPaidVehicles.length > 0) {
        throw new HttpException(
          {
            result: false,
            message: 'The selected vehicles halfcut status is not valid.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const result = await prisma.vehicles.updateMany({
      where: { id: { in: vehiclesIds } },
      data: {
        carstate: vehicleState,
        updated_by,
        status_changed_at: new Date(),
      },
    });

    await prisma.$disconnect();

    add_tracking(vehiclesIds, vehicleState as tracking_status);

    const data = await this.findMany(vehiclesIds);
    this.sendMail(data, 'change_status_vehicles');

    return result;
  };

  softDeleteOne = (
    ids: number[],
    deleted_by: number,
    deleted_reason?: string,
  ) =>
    this.prisma.vehicles
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          deleted_at: new Date(),
          deleted_by,
          deleted_reason,
          isPendingTrash: true,
        },
      })
      .then((data) => {
        if (data.count > 0)
          return {
            result: true,
            message: `${data.count} records have been removed!`,
          };
        else return { result: false, message: 'No records have been removed.' };
      })
      .catch()
      .finally(() => this.prisma.$disconnect());

  getTotal = (where: object = {}) =>
    this.prisma.vehicles
      .count({ where })
      .then((data: any) => data)
      .catch((err: any) => catch_response(err))
      .finally(async () => await this.prisma.$disconnect());

  getVehiclesCountBasedOnLocation = async (
    query: vehiclesCountBasedOnLocation,
  ) => {
    const { company_id, state } = query;
    try {
      // Fetch vehicle counts grouped by location and location names in one go
      const data = await this.prisma2.vehicles.groupBy({
        by: ['point_of_loading'],
        where: {
          company_id: company_id,
          carstate: state as $Enums.carstate,
          deleted_at: null,
          halfcut_status: 'completed',
        },
        _count: {
          id: true,
        },
      });

      // Extract point_of_loading IDs
      const pointOfLoadingIds = data.map((item) => item.point_of_loading);

      // Fetch location names for those IDs
      const locations = await this.prisma2.locations.findMany({
        where: {
          id: { in: pointOfLoadingIds },
        },
        select: {
          id: true,
          name: true,
        },
      });

      // Create a map for quick location lookup
      const locationMap = new Map(locations.map((loc) => [loc.id, loc.name]));

      // Map the grouped data to include location names
      const result = data.map((item) => ({
        location: locationMap.get(item.point_of_loading) || 'Unknown Location',
        count: item._count.id,
      }));

      return { result: true, data: result };
    } catch (error) {
      console.error('Error fetching vehicle counts:', error);
      return { result: false, error: 'Error fetching vehicle counts' };
    }
  };

  /*  forceDeleteOne = (ids: number[]) => {
    const idn = ids.map(Number);
    return this.prisma.vehicles.deleteMany({
      where: {
        AND: [
          { id: { in: idn } },
          { deleted_at: { not: null }, deleted_by: { not: null } },
        ],
      },
    });
  }; */

  findTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const w = await searchFilter(query, searchColumns);
    const where = {
      ...w,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: false,
    };
    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany({
        where,
        ...paginate,
        orderBy: await ordersBy(query),
        select: {
          id: true,
          year: true,
          make: true,
          model: true,
          color: true,
          vin: true,
          lot_number: true,
          carstate: true,
          company_id: true,
          companies: { select: { id: true, name: true } },
          customer_remark: true,
          point_of_loading: true,
          pol_locations: { select: { id: true, name: true } },
          deleted_at: true,
          deleted_reason: true,
          users_vehicles_deleted_by_confirmTousers: {
            select: {
              id: true,
              fullname: true,
            },
          },
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findPendingTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const w = await searchFilter(query, searchColumns);
    const where = {
      ...w,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: true,
    };
    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    return await Promise.all([
      this.prisma.vehicles.count({ where }),
      this.prisma.vehicles.findMany({
        where,
        ...paginate,
        orderBy: await ordersBy(query),
        select: {
          id: true,
          year: true,
          make: true,
          model: true,
          color: true,
          vin: true,
          lot_number: true,
          carstate: true,
          company_id: true,
          companies: { select: { id: true, name: true } },
          customer_remark: true,
          point_of_loading: true,
          pol_locations: { select: { id: true, name: true } },
          deleted_at: true,
          deleted_reason: true,
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getTrashTotal = () =>
    this.prisma.vehicles
      .count({
        where: { deleted_at: { not: null }, deleted_by: { not: null } },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  restore = (ids: number[], updated_by: number) =>
    this.prisma.vehicles
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: { deleted_at: null, deleted_by: null, updated_by },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  restorePendingTrash = (ids: number[], updated_by: number) =>
    this.prisma.vehicles
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by,
          isPendingTrash: false,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  deletePendingTrash = (ids: number[], deleted_by_confirm: number) =>
    this.prisma.vehicles
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          isPendingTrash: false,
          deleted_by_confirm,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  addToInventory = async (dto: addToInventoryDto, created_by: number) => {
    const check = await this.prisma.vehicles
      .findMany({
        where: { id: { in: dto.vehiclesIds }, container_id: dto.containerId },
        select: { id: true },
      })
      .finally(() => this.prisma.$disconnect());

    if (check.length == dto.vehiclesIds.length) {
      await this.prisma.vehicles
        .updateMany({
          where: { id: { in: dto.vehiclesIds } },
          data: { yard_id: dto.yard_id },
        })
        .finally(() => this.prisma.$disconnect());
      const data = dto.vehiclesIds.map((e) => ({
        vehicle_id: e,
        yard_id: dto.yard_id,
        date_in: dto.date_in,
        storage_amount: dto.storage_amount,
        comments: dto.comments,
        created_by,
        updated_by: created_by,
      }));
      const result = await this.prisma.yard_inventories
        .createMany({ data })
        .finally(() => this.prisma.$disconnect());
      return { result: true, message: `${result.count} records, inserted.` };
    } else {
      return {
        result: false,
        message:
          'Vehicles do not have container, In case of multiple vehicles they are not in same container.',
      };
    }
  };

  addTitleTracking = async (dto: addTitleTrackingDto) => {
    const {
      vehicleId,
      titleTrackingDate,
      titleTrackingArrivalDate,
      titleTrackingNumber,
    } = dto;
    for (const id of vehicleId) {
      const vehicle = await this.prisma2.vehicles.findUnique({
        where: { id },
      });

      if (!vehicle) {
        throw new NotFoundException(`Vehicle with ID ${id} not found`);
      }

      if (vehicle.title_tracking_status === 'send') {
        throw new BadRequestException(
          `The Vehicle with ID ${id} is already added`,
        );
      }

      if (!titleTrackingNumber) {
        throw new BadRequestException(`Please add the Title Tracking Number`);
      }

      await this.prisma2.vehicles
        .update({
          where: { id },
          data: {
            title_tracking_status: 'send',
            title_tracking_date: new Date(titleTrackingDate),
            title_tracking_arrival_date: new Date(titleTrackingArrivalDate),
            title_tracking_number: titleTrackingNumber,
          },
        })
        .finally(() => this.prisma2.$disconnect());
      return {
        result: true,
        message: 'Added To Done Successfully',
      };
    }
  };

  addToArrived = async (dto: addToArrivedDto) => {
    const { vehicleIds } = dto;

    const vehicles = await this.prisma2.vehicles.findMany({
      where: {
        id: { in: vehicleIds },
      },
    });

    if (vehicles.length !== vehicleIds.length) {
      const foundIds = vehicles.map((v) => v.id);
      const missingIds = vehicleIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Vehicles with IDs ${missingIds.join(', ')} not found`,
      );
    }

    const alreadyArrived = vehicles.filter(
      (v) => v.title_tracking_status === 'arrived',
    );

    if (alreadyArrived.length > 0) {
      const idsAlreadyArrived = alreadyArrived.map((v) => v.id);
      throw new BadRequestException(
        `Vehicles with IDs ${idsAlreadyArrived.join(', ')} are already marked as arrived`,
      );
    }

    await this.prisma2.vehicles.updateMany({
      where: {
        id: { in: vehicleIds },
      },
      data: {
        title_tracking_status: 'arrived',
      },
    });

    await this.prisma2.$disconnect();

    return {
      result: true,
      message: 'Vehicles marked as arrived successfully',
    };
  };

  addToPGLU = async (dto: addToPGLUDto, created_by: number) => {
    // Add vehicle to pglu if the company name was united unstaoppable car auction LLC and color = #FFFFFF
    const vehicle = await this.prisma.vehicles
      .findFirst({
        where: { id: dto.vehicle_id },
      })
      .finally(() => this.prisma.$disconnect());
    const company = await this.prisma.companies
      .findFirst({
        where: { id: vehicle.company_id },
        select: { is_belong_to_used_car: true },
      })
      .finally(() => this.prisma.$disconnect());
    if (company?.is_belong_to_used_car) {
      const puc = await this.prisma.pgl_used_cars
        .create({
          data: {
            vehicle_id: vehicle?.id,
            selling_status: 2,
            selling_status_color: '#FFFFFF',
            created_by,
            updated_by: created_by,
          },
          select: { id: true },
        })
        .finally(() => this.prisma.$disconnect());

      await this.prisma.pgl_used_car_costs
        .create({
          data: {
            pgl_used_car_id: puc.id,
            ten_percent_vat_duty: (10 * vehicle.price) / 100,
            clearance: 300,
            created_by,
            updated_by: created_by,
          },
        })
        .finally(() => this.prisma.$disconnect());
      return {
        result: true,
        message: 'Vehicle added to pglu successfully.',
      };
    } else
      return {
        result: false,
        message: 'Only vehicle with company of pglu can be added into pglu',
      };
  };

  addToUnitedTradingCars = async (dto: VehiclesIdsDto) => {
    // Add vehicle to pglu if the company name was united unstaoppable car auction LLC and color = #FFFFFF
    const vehicles = await this.prisma.vehicles
      .findMany({
        where: { id: { in: dto.vehiclesIds } },
        select: {
          id: true,
          vin: true,
          lot_number: true,
          year: true,
          make: true,
          model: true,
          color: true,
          weight: true,
          price: true,
          tax_amount: true,
          title_number: true,
          hat_number: true,
          photo_link: true,
          vehicle_description: true,
        },
      })
      .finally(() => this.prisma.$disconnect());

    const res = await axios
      .post(
        `${process.env.AUCTION_API_URL}/api/vehicles/public-store`,
        { vehicles: vehicles },
        {
          headers: {
            'api-key': process.env.PUBLIC_API_KEY,
          },
        },
      )
      .then((res) => res)
      .catch((err) => err.response);
    if (res.status !== 200) {
      throw new HttpException(
        {
          result: false,
          message: JSON.stringify(res.data),
          status: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    await this.prisma.vehicles.updateMany({
      where: { id: { in: vehicles.map((e) => e.id) } },
      data: { is_in_united_trading: true },
    });
    return {
      result: true,
      message: vehicles,
      auction_result: res.data,
    };
  };
  private sendMail = async (data: any[], temp: string) => {
    if (!is_send) return;
    for (const e of data) {
      if (
        !excludeCompanyIdsDoNotSendEmail.includes(e.company_id) ||
        (excludeCompanyIdsDoNotSendEmail.includes(e.company_id) &&
          temp !== 'change_status_vehicles' &&
          temp !== 'add_vehicles')
      ) {
        const state = this.transformString(e?.carstate);
        await reactEmail(
          // '<EMAIL>',
          e?.customers?.loginable?.email,
          ` ${e?.vin ?? ''} ${e?.color ?? ''} ${e?.year ?? ''} ${e?.make ?? ''} ${e?.model ?? ''} ${state ?? ''} to ${e?.pol_locations?.name ?? ''}`,
          { data: e, temp: temp },
          AddVehiclesAndChangeStatusEmailTemplate,
          null,
          {
            from: process.env.MAILGUN_FROM,
            // cc: ['<EMAIL>'],
          },
        );
        // await email(
        //   '<EMAIL>',
        //   // e?.customers?.loginable?.email,
        //   `${e?.vin} ${e?.color} ${e?.year} ${e?.make} ${e?.model} ${state} to ${e?.pol_locations?.name}`,
        //   e,
        //   temp,
        // );
      }
    }
  };

  private transformString(input: string): string {
    if (input && input != undefined && input != '') {
      const words = input.split('_');
      const capitalizedWords = words.map((word) => {
        return word.charAt(0).toUpperCase() + word.slice(1);
      });
      return capitalizedWords.join(' ');
    } else return input;
  }

  /**
   * Move vehicle from auction paid to on the way status.
   */
  auctionPaidToOnTheWay = async (id: number, updated_by: number) =>
    this.prisma.vehicles
      .updateMany({
        where: { id, carstate: carstate.auction_paid },
        data: { carstate: carstate.on_the_way, updated_by },
      })
      .then((r) => {
        add_tracking([id], tracking_status.on_the_way);
        return {
          result: r.count > 0,
          message: r
            ? 'Vehicle moved to On The Way status successfully.'
            : 'Vehicle move failed, Please try again later.',
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  updateTitleReceiveDate = (
    id: number,
    dto: TitleDateDto,
    updated_by: number,
  ) =>
    this.prisma.vehicles
      .update({
        where: { id },
        data: { title_receive_date: dto.date_param, updated_by },
      })
      .then((r) => ({ result: r ? true : false, data: r }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);

  updatedLastTitleFollowUpDate = (
    id: number,
    dto: TitleDateDto,
    updated_by: number,
  ) =>
    this.prisma.vehicles
      .update({
        where: { id },
        data: { last_title_follow_up_date: dto.date_param, updated_by },
      })
      .then((r) => ({ result: r ? true : false, data: r }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);

  updatedSimplificationTrack = (
    id: number,
    dto: SimplificationDto,
    updated_by: number,
  ) =>
    this.prisma.vehicles
      .update({
        where: { id },
        data: {
          title_delivery_location: dto.title_delivery_location,
          title_status: dto.title_status,
          last_title_follow_up_date: dto.last_title_follow_up_date,
          title_receive_date: dto.title_receive_date,
          trn: dto.trn,
          title_status_step_two: dto.title_status_step_two,
          updated_by,
        },
      })
      .then((r) => ({ result: r ? true : false, data: r }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);

  createLoadAddToContainer = async (
    dto: CreateLoadAddVehiclesToContainer,
    userId: number,
  ) => {
    const selected_booking = await this.prisma.bookings.findUnique({
      where: { id: dto.booking_id },
      include: {
        _count: {
          select: {
            containers: {
              where: { deleted_at: null },
            },
          },
        },
        vessels: {
          select: {
            locations: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });
    if (selected_booking) {
      const allowed_bookings = [
        'GAJEBBK',
        'TXJEBBK',
        'NJJEBBK',
        'MDJEBBK',
        'CAJEBBK',
        'PotiBK',
        'PotiTXBK',
        'PotiHAZ',
        'CASALALAHBK',
        'CAPOTIBK',
        'MDJEBBK HAZ',
        'GAJEBBK HAZ',
        'NJJEBBK HAZ',
        'TXJEBBK HAZ',
        'CAJEBBK HAZ',
        'PotiTXBKHAZ',
        'HAZTXJEBBK',
      ];
      if (!allowed_bookings.includes(selected_booking.booking_number)) {
        if (selected_booking._count.containers >= selected_booking.qty) {
          throw new HttpException(
            {
              result: false,
              message: `The number of containers (${selected_booking._count.containers}) has reached or exceeded the booking quantity (${selected_booking.qty}).`,
              status: HttpStatus.BAD_REQUEST,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    return await this.prisma.containers
      .create({
        data: {
          container_number: dto.container_number,
          container_number_assigned_at: dto.container_number
            ? new Date()
            : null,
          pin_out: dto.container_number,
          loading_date: new Date(dto.loading_date),
          actions: dto.actions,
          no_units_load: dto.no_units_load,
          company_id: dto.company_id,
          clearance_invoice_link: dto.clearance_invoice_link,
          ingate: dto.ingate,
          shipping_document_id: dto.shipping_document_id,
          booking_id: dto.booking_id,
          booking_suffix: dto.booking_suffix,
          aes_itn_number: dto.aes_itn_number,
          tracking_contatiner: dto.tracking_contatiner,
          container_id_update_date: dto.container_id_update_date,
          bill_of_loading_number: dto.bill_of_loading_number,
          seal_number: dto.seal_number,
          invoice_number: dto.invoice_number,
          amount: dto.amount,
          photo_link: dto.photo_link,
          ingate_date: dto.ingate_date,
          pull_date: dto.pull_date,
          ingate_driver_bonus: dto.ingate_driver_bonus,
          pull_driver_bonus: dto.pull_driver_bonus,
          ingate_driver_notes: dto.ingate_driver_notes,
          pull_driver_notes: dto.pull_driver_notes,
          pin_in: dto.pin_in,
          status_changed_at: new Date(),
          loading_instruction: dto.loading_instruction,
          invisible_for_customer: dto.invisible_for_customer,
          yard_location_id: dto.yard_location_id,
          status: container_status.at_loading,
          documentation_instruction: dto.documentation_instruction,
          created_by: userId,
          updated_by: userId,
        },
      })
      .then(async (cont: any) => {
        await add_tracking([cont?.id], tracking_status.pending, false);
        // Generate and update invoice number
        const locationName = selected_booking.vessels?.locations?.name ?? '';
        const invoiceNumber = `PGL${locationName.slice(-2)}${cont?.id}`;
        await this.prisma.containers.update({
          where: { id: cont?.id },
          data: { invoice_number: invoiceNumber },
        });

        return await this.prisma.vehicles
          .updateMany({
            where: { id: { in: dto.vehiclesIds } },
            data: {
              container_id: cont.id,
              carstate: carstate.shipped,
              status_changed_at: new Date(),
              updated_by: userId,
            },
          })
          .then(async (res) => {
            // await this.updateContainerSizeUnite(cont?.id);
            if (res.count > 0) {
              add_tracking(
                dto.vehiclesIds,
                dto.container_number
                  ? tracking_status.shipped
                  : tracking_status.on_hand_with_load,
              );
              const vehicles = await this.prisma.vehicles.findMany({
                where: { id: { in: dto.vehiclesIds } },
              });
              await addAndRemoveToShipment(
                vehicles,
                dto.vehiclesIds,
                userId,
                this.prisma,
                'add to container',
              );
              return {
                result: true,
                message: `${res.count} records have been updated!`,
              };
            } else return { result: false, message: 'Update failed.' };
          })
          .finally(() => this.prisma.$disconnect());
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  capitalizeText = (text) => {
    text = text.replace(/_/g, ' ');
    const words = text.split(' ');
    for (let i = 0; i < words.length; i++) {
      words[i] = words[i].charAt(0).toUpperCase() + words[i].slice(1);
    }
    return words.join(' ');
  };

  capitalize = (status, pickup_status) => {
    if (status == 'on_the_way')
      return `On The Way${
        pickup_status ? ' | ' + this.capitalizeText(pickup_status) : ''
      }`;
    else if (status == 'on_hand_no_title') return 'On Hand W/ No Title';
    else if (status == 'on_hand_with_title') return 'On Hand W/ Title';
  };

  ////// Excel inventory
  async downloadInventoryExcel(dto): Promise<Buffer> {
    const { company_id, note, state } = dto;
    let where;
    if (
      state == 'on_the_way' ||
      state == 'on_hand_no_title' ||
      state == 'on_hand_with_title'
    ) {
      where = {
        company_id: company_id,
        carstate: state,
        deleted_at: null,
      };
    } else {
      where = {
        company_id: company_id,
        carstate: {
          in: ['on_the_way', 'on_hand_with_title', 'on_hand_no_title'],
        },
        deleted_at: null,
      };
    }
    const vehicles = await this.prisma.vehicles.findMany({
      where: where,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        deliver_date: true,
        customer_remark: true,
        vin: true,
        lot_number: true,
        trn: true,
        carstate: true,
        point_of_loading: true,
        pol_locations: { select: { name: true } },
        photo_link: true,
        companies: {
          select: {
            name: true,
          },
        },
        title_status: true,
        title_receive_date: true,
        pickup_status: true,
        dispatch_remark: true,
      },
      orderBy: [
        {
          carstate: 'desc',
        },
        {
          pol_locations: {
            name: 'desc',
          },
        },
      ],
    });

    // Extract vehicle IDs
    const vehicleIds = vehicles.map((vehicle) => vehicle.point_of_loading);

    // Fetch filtered company notes based on the vehicle IDs and point_of_loading
    const loadNote = await this.prisma.company_notes.findMany({
      where: {
        location_id: {
          in: vehicleIds,
        },
        company_id: company_id,
        deleted_at: null,
      },
      select: {
        id: true,
        location_id: true,
        locations: { select: { name: true } },
        reason: true,
      },
    });

    // Associate filtered company notes with vehicles
    const vehiclesWithFilteredCompanyNotes = vehicles.map((vehicle) => {
      const matchingNotes = loadNote.filter(
        (note) => note.location_id === vehicle.point_of_loading,
      );
      return {
        ...vehicle,
        companies: { ...vehicle.companies, company_notes: matchingNotes },
      };
    });

    const commentShow = (vehicle) => {
      if (vehicle.carstate == 'on_hand_no_title') {
        return vehicle.title_status;
      }
      if (vehicle.carstate == 'on_the_way') {
        return vehicle?.dispatch_remark;
      }
      if (vehicle.carstate == 'on_hand_with_title') {
        return vehicle?.companies?.company_notes[0]?.reason;
      }
    };
    //////////////// excel data //////////////////////
    const excelData = vehiclesWithFilteredCompanyNotes.map(
      (vehicle, index) => ({
        ID: index + 1,
        ...(state === 'on_the_way'
          ? {}
          : { customerComment: commentShow(vehicle) }),
        vehicleDescription: `${vehicle.year ?? ''} ${vehicle.make ?? ''} ${
          vehicle.model ?? ''
        } ${vehicle.color ?? ''}`,
        VINNumber: vehicle.vin,
        lot: vehicle.lot_number,
        trn: vehicle.trn ?? '',
        currentStatus: this.capitalize(vehicle.carstate, vehicle.pickup_status),
        POL: vehicle.pol_locations.name,
        deliveryDate:
          vehicle.carstate == 'on_the_way'
            ? ' '
            : state == 'on_hand_no_title'
              ? vehicle.title_receive_date
              : vehicle.deliver_date,
        // photoLink: vehicle.photo_link,
      }),
    );

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('PEACE GLOBAL LOGISTICS');
    worksheet.pageSetup.paperSize = 9;
    worksheet.columns = [
      { header: 'ID', key: 'ID', width: 5 },
      // Add the conditional column
      ...(state == 'on_the_way'
        ? []
        : [{ header: 'Customer Comment', key: 'customerComment', width: 45 }]),
      { header: 'Vehicle Description', key: 'vehicleDescription', width: 50 },
      { header: 'VIN Number', key: 'VINNumber', width: 25 },
      { header: 'Lot No', key: 'lot', width: 12 },
      { header: 'TRN#', key: 'trn', width: 25 },
      { header: 'Current Status', key: 'currentStatus', width: 30 },
      { header: 'POL', key: 'POL', width: 20 },
      {
        header:
          state === 'on_hand_no_title' ? 'Title Receive ' : 'Delivery Date',
        key: 'deliveryDate',
        width: 15,
      },
      // { header: 'Photo Link', key: 'photoLink', width: 30 },
    ];
    if (state == 'on_hand_with_title') {
      worksheet.getRow(1).height = 140;
    } else {
      worksheet.getRow(1).height = 90;
    }

    worksheet.mergeCells('A1:I1');
    const headerImageBuffer: Buffer = await fsPromises.readFile(
      './images/inventory.jpg',
    );
    const headerImage = workbook.addImage({
      buffer: headerImageBuffer,
      extension: 'jpeg',
    });
    const headerImageBuffer2: Buffer = await fsPromises.readFile(
      './images/With_title.jpg',
    );
    const headerImage2 = workbook.addImage({
      buffer: headerImageBuffer2,
      extension: 'jpeg',
    });
    const headerImageBuffer3: Buffer = await fsPromises.readFile(
      './images/inventoryNoTitle.jpg',
    );
    const headerImage3 = workbook.addImage({
      buffer: headerImageBuffer3,
      extension: 'jpeg',
    });
    if (state == 'on_hand_with_title') {
      worksheet.addImage(headerImage2, 'A1:H1');
    } else if (state == 'on_hand_no_title') {
      worksheet.addImage(headerImage3, 'A1:I1');
    } else {
      worksheet.addImage(headerImage, 'A1:H1');
    }
    ////////////////// header /////////////////////
    worksheet.getRow(2).height = 20;
    worksheet.mergeCells('A2:I2');
    const cell = worksheet.getCell('A2:H2');
    cell.value = `${vehicles[0]?.companies.name}`;
    cell.font = { bold: true, size: 15 };
    cell.alignment = { horizontal: 'center', vertical: 'middle' };

    let headerNames = [
      { name: 'A3', value: 'ID' },
      { name: 'B3', value: 'Comment' },
      { name: 'C3', value: 'Vehicle Description' },
      { name: 'D3', value: 'VIN Number' },
      { name: 'E3', value: 'Lot No' },
      { name: 'F3', value: 'TRN#' },
      { name: 'G3', value: 'Current Status' },
      { name: 'H3', value: 'POL' },
      {
        name: 'I3',
        value: state == 'on_hand_no_title' ? 'Title Receive' : 'Delivery Date',
      },
      // { name: 'I3', value: 'Photo Link' },
    ];

    if (state == 'on_the_way') {
      headerNames = [
        { name: 'A3', value: 'ID' },

        { name: 'B3', value: 'Vehicle Description' },
        { name: 'C3', value: 'VIN Number' },
        { name: 'D3', value: 'Lot No' },
        { name: 'E3', value: 'Current Status' },
        { name: 'F3', value: 'POL' },
        {
          name: 'G3',
          value:
            state == 'on_hand_no_title' ? 'Title Receive' : 'Delivery Date',
        },
        // { name: 'I3', value: 'Photo Link' },
      ];
    }

    headerNames.forEach(({ name, value }) => {
      worksheet.getCell(name).value = value;
    });
    headerNames.forEach(({ name }) => {
      worksheet.getCell(name).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '595959' },
      };
      worksheet.getCell(name).font = {
        color: {
          argb: 'FFFFFF',
        },
        bold: true,
      };
    });

    const tempExcel = excelData.filter(
      (item) => item.currentStatus == 'On Hand W/ Title',
    );

    // return tempExcel;
    let start = 4;
    let previousPOL = '';
    let endRow = start;

    tempExcel.forEach((item, index) => {
      if (item.POL !== previousPOL) {
        if (endRow > start) {
          worksheet.mergeCells(`B${start}:B${endRow}`);
          const cell = worksheet.getCell(`B${start}`);
          cell.value =
            tempExcel[start - 4].customerComment == 'No Comment'
              ? ' '
              : tempExcel[start - 4].customerComment;
          cell.alignment = { wrapText: true };
          cell.numFmt = '0%';
        }
        start = index + 4;
        endRow = start;
        previousPOL = item.POL;
      } else {
        endRow = index + 4;
      }
      worksheet.addRow(item);
    });

    if (endRow > start) {
      worksheet.mergeCells(`B${start}:B${endRow}`);
      const cell = worksheet.getCell(`B${start}`);
      cell.value =
        tempExcel[start - 4].customerComment == 'No Comment'
          ? ' '
          : tempExcel[start - 4].customerComment;
      cell.alignment = { wrapText: true };
      cell.numFmt = '0%';
    }

    const tempExcel2 = excelData.filter(
      (item) => item.currentStatus != 'On Hand W/ Title',
    );
    worksheet.addRows(tempExcel2);

    const currentStatusColumn = worksheet.getColumn('F');
    currentStatusColumn.eachCell({ includeEmpty: true }, (cell) => {
      const currentStatusValue = cell.text;
      if (
        currentStatusValue === 'On The Way' ||
        currentStatusValue === 'On The Way | Picked Up' ||
        currentStatusValue === 'On The Way | Delivered'
      ) {
        cell.font = {
          color: { argb: '8B0000' }, // Dark red
        };
      } else if (currentStatusValue === 'On Hand W/ No Title') {
        cell.font = {
          color: { argb: 'FF0000' }, // Red
        };
      }
    });

    worksheet.columns.forEach((column, index) => {
      const isColumnAorB = index === 1 || index === 2; // A is 1, B is 2
      column.alignment = {
        horizontal: isColumnAorB ? 'left' : 'center',
        vertical: 'middle',
      };
    });

    ///// note
    const noteRowNumber = worksheet.rowCount + 1;
    worksheet.getRow(noteRowNumber).height = 40;
    worksheet.mergeCells(`B${noteRowNumber}:B${noteRowNumber}`);
    const noteCell = worksheet.getCell(`B${noteRowNumber}:H${noteRowNumber}`);
    noteCell.value = 'Note';
    noteCell.font = { bold: true };
    noteCell.alignment = { horizontal: 'center', vertical: 'middle' };

    worksheet.mergeCells(`C${noteRowNumber}:I${noteRowNumber}`);
    const noteValueCell = worksheet.getCell(
      `C${noteRowNumber}:I${noteRowNumber}`,
    );
    if (note) {
      noteValueCell.value = `${
        note.replace(/<\/?[^>]+(>|$)/g, '').replace('&nbsp;', ' ') ?? ''
      }`;
    }
    noteValueCell.font = { bold: true };
    noteValueCell.alignment = { horizontal: 'left', vertical: 'middle' };

    /// footer
    // const lastDataRowNumber = worksheet.rowCount + 1;
    // worksheet.getRow(lastDataRowNumber).height = 75;
    // worksheet.mergeCells(`A${lastDataRowNumber}:H${lastDataRowNumber}`);
    // const footerImageBuffer: Buffer = await fsPromises.readFile(
    //   './images/inventory_footer.jpg',
    // );
    // const footerImage = workbook.addImage({
    //   buffer: footerImageBuffer,
    //   extension: 'jpeg',
    // });
    // worksheet.addImage(
    //   footerImage,
    //   `A${lastDataRowNumber}:H${lastDataRowNumber}`,
    // );
    // worksheet.eachRow((row) => {
    //   row.eachCell((cell) => {
    //     cell.border = {
    //       top: { style: 'thin' },
    //       left: { style: 'thin' },
    //       bottom: { style: 'thin' },
    //       right: { style: 'thin' },
    //     };
    //   });
    // });

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer as Buffer;
  }

  vehicleInventoryTable = async (
    vehiclesForTable,
    companyName,
    headerImage,
    state,
    note,
  ) => {
    const temp = readFileSync(
      path.join(
        __dirname,
        `../../../../email_templates/inventory_template.hbs`,
      ),
      'utf8',
    );

    let indexTable = 0;
    const table = async () => `
    <table class="custom-table">
        <tr style="font-size: 12px;font-weight: 500;text-transform:uppercase">
            <th >ID</th>
            ${state == 'on_the_way' ? '' : '<th >Comment</th>'}
            <th >Description</th>
            <th >VIN Number</th>
            <th >Lot No</th>
            ${state == 'on_hand_no_title' ? '<th >TRN#</th>' : ''}
            <th >Current Status</th>
            <th >POL</th>
          ${
            state == 'on_hand_no_title'
              ? '<th >Title Receive Date</th>'
              : '<th >Delivery Date</th>'
          }
        </tr>
        ${(
          await Promise.all(
            vehiclesForTable.map((item) => {
              return item.vehicles
                .map((row, index) => {
                  indexTable++;
                  return `<tr>
                      <td style="text-align:left">${indexTable}</td>
                        ${
                          state == 'on_the_way'
                            ? ''
                            : index == 0
                              ? `<td style="text-transform:uppercase;text-align:left" rowspan="${
                                  item?.vehicles?.length
                                }">
                                ${item?.note == 'No Comment' ? '' : item?.note}
                              </td>`
                              : ''
                        }
                      <td style="text-align:left">${row?.year ?? ''} ${
                        row?.make ?? ''
                      } ${row?.model ?? ''} ${row?.color ?? ''}</td>
                      <td style="text-align:left">${row?.vin}</td>
                      <td style="text-align:left">${row?.lot_number ?? ''}</td>
                      ${
                        row?.carstate == 'on_hand_no_title'
                          ? '<td style="text-align:left">' +
                            (row?.trn ?? '') +
                            '</td>'
                          : ''
                      }
                      <td style="text-align:left; ${
                        row?.carstate == 'on_the_way'
                          ? 'color:#8B0000;'
                          : row?.carstate == 'on_hand_no_title'
                            ? 'color:red;'
                            : 'color:inherit;'
                      }">${this.capitalize(
                        row?.carstate,
                        row?.pickup_status,
                      )}</td>
                      <td style="text-align:left">${
                        row?.pol_locations?.name
                      }</td>
                      <td style="text-align:left">${
                        row?.carstate == 'on_the_way'
                          ? ''
                          : state == 'on_hand_no_title'
                            ? row.title_receive_date
                              ? moment(row.title_receive_date ?? '').format(
                                  'YYYY-MM-DD',
                                )
                              : ''
                            : moment(row.deliver_date ?? '').format(
                                'YYYY-MM-DD',
                              )
                      }</td>
                      </tr>`;
                })
                .join(' ');
            }),
          )
        ).join(' ')}
        <tr>
          <td colspan="2" style="font-weight: 700; text-align: center;">Note</td>
          <td colspan="100">${note ?? ''}</td>
        </tr>
      </table>
    `;

    const compiledTemp = Handlebars.compile(temp);
    const awaitTable = await table();
    const html = compiledTemp({
      table: awaitTable,
      companyName: companyName ?? '',
      headerImage,
    });

    return html;
  };

  ////// Email inventory
  async sendEmailInventoryExcel(dto: emailInventoryExcel) {
    const { company_id, note, state } = dto;
    let where;
    if (
      state == 'on_the_way' ||
      state == 'on_hand_no_title' ||
      state == 'on_hand_with_title'
    ) {
      where = {
        company_id: company_id,
        carstate: state,
        deleted_at: null,
      };
    } else {
      where = {
        company_id: company_id,
        carstate: {
          in: ['on_the_way', 'on_hand_with_title', 'on_hand_no_title'],
        },
        deleted_at: null,
      };
    }
    const vehicles = await this.prisma.vehicles.findMany({
      where: where,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        deliver_date: true,
        customer_remark: true,
        vin: true,
        lot_number: true,
        carstate: true,
        pol_locations: { select: { name: true } },
        photo_link: true,
        companies: { select: { name: true } },
        title_status: true,
        pickup_status: true,
        dispatch_remark: true,
        title_receive_date: true,
      },
      orderBy: [
        {
          carstate: 'desc',
        },
        {
          pol_locations: {
            name: 'asc',
          },
        },
      ],
    });
    const loadNote = await this.prisma.company_notes.findMany({
      where: {
        company_id: company_id,
        deleted_at: null,
      },
      select: {
        locations: { select: { name: true } },
        reason: true,
      },
    });

    const tempVehicles = vehicles.filter(
      (item) => item.carstate != 'on_hand_with_title',
    );

    const arrangedNotesWith = [];
    tempVehicles.forEach((item) => {
      if (item.carstate == 'on_hand_no_title') {
        arrangedNotesWith.push({
          note: item?.title_status ?? '',
          vehicles: [item],
        });
      } else {
        arrangedNotesWith.push({
          note: item?.dispatch_remark ?? '',
          vehicles: [item],
        });
      }
    });

    const groupedVehicles = {};
    vehicles.forEach((vehicle) => {
      const locationName = vehicle.pol_locations.name;
      if (!groupedVehicles[locationName]) {
        groupedVehicles[locationName] = [];
      }
      groupedVehicles[locationName].push(vehicle);
    });

    // Create arrangedNotes array with vehicles grouped by pol_locations.name
    const arrangedNotes = [];
    loadNote.forEach((note) => {
      const locationName = note.locations.name;
      if (groupedVehicles[locationName]) {
        const vehiclesWithTitles = groupedVehicles[locationName].filter(
          (vehicle) => vehicle.carstate === 'on_hand_with_title',
        );
        if (note.reason != null) {
          arrangedNotes.push({
            note: note.reason,
            vehicles: vehiclesWithTitles,
          });
        }
        delete groupedVehicles[locationName];
      }
    });
    Object.keys(groupedVehicles).forEach((locationName) => {
      arrangedNotes.push({
        note: '',
        vehicles: groupedVehicles[locationName],
      });
    });
    const vehiclesForTable = arrangedNotes.concat(arrangedNotesWith);
    const toEmail = await this.prisma.customers.findMany({
      where: { company_id: company_id },
      select: {
        loginable: { select: { email: true } },
        companies: { select: { name: true } },
      },
    });
    const emailArray = toEmail.map((item) => item.loginable.email);
    const emailString = emailArray.join(',');
    const file = (await this.downloadInventoryExcel({
      company_id: company_id,
      state: state,
    })) as Buffer;

    let transporter: nodemailer.Transporter;
    const html = await this.vehicleInventoryTable(
      vehiclesForTable,
      vehicles[0]?.companies?.name,
      state == 'on_hand_with_title'
        ? 'https://latest-api.pglsystem.com/images/With_title.jpg'
        : state == 'on_hand_no_title'
          ? 'https://latest-api.pglsystem.com/images/inventoryNoTitle.jpg'
          : 'https://latest-api.pglsystem.com/images/inventory.jpg',
      state,
      note,
    );

    // eslint-disable-next-line prefer-const
    transporter = nodemailer.createTransport(
      mailgunTransport({
        auth: {
          api_key: process.env.MAILGUN_API_KEY,
          domain: process.env.MAILGUN_DOMAIN,
        },
      }),
    );

    await transporter
      .sendMail({
        from: '<EMAIL>',
        to: emailString,
        // to: '<EMAIL>',
        // to: '<EMAIL>',
        // cc: '<EMAIL>',
        subject:
          'Weekly Inventory Cars for ' +
          (vehicles[0]?.companies?.name ??
            vehiclesForTable[0]?.vehicles[0]?.companies?.name),
        html:
          vehicles.length > 100
            ? note
              ? note
              : `<p>Our goal is to provide fast loading services. If you can help us in obtaining titles for your cars without title or have any other questions, please don't hesitate to reach out!</p>`
            : html,
        attachments: [
          {
            filename: `${
              'Weekly Inventory Cars for ' +
              (vehicles[0]?.companies?.name ??
                vehiclesForTable[0]?.vehicles[0]?.companies?.name)
            }.xlsx`,
            content: file,
          },
          {
            filename: `${
              'Weekly Inventory Cars for ' +
              (vehicles[0]?.companies?.name ??
                vehiclesForTable[0]?.vehicles[0]?.companies?.name)
            }.pdf`,
            content: await generatePdf(
              html,
              {
                top: '0',
                bottom: '0',
                right: '0',
                left: '0',
              },
              true,
            ),
          },
        ],
      })
      .then((result) => ({
        result: result ? true : false,
        data: { vehicles, loadNote },
      }))

      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);
  }

  ////// PDF inventory
  async downloadInventoryPDFOrImage(dto: downloadInventoryExcel, type = 'pdf') {
    const { company_id, state, note } = dto;
    let where;
    if (
      state == 'on_the_way' ||
      state == 'on_hand_no_title' ||
      state == 'on_hand_with_title'
    ) {
      where = {
        company_id: company_id,
        carstate: state,
        deleted_at: null,
      };
    } else {
      where = {
        company_id: company_id,
        carstate: {
          in: ['on_the_way', 'on_hand_with_title', 'on_hand_no_title'],
        },
        deleted_at: null,
      };
    }
    const vehicles = await this.prisma2.vehicles.findMany({
      where: where,
      select: {
        id: true,
        make: true,
        model: true,
        year: true,
        color: true,
        deliver_date: true,
        customer_remark: true,
        vin: true,
        lot_number: true,
        carstate: true,
        pol_locations: { select: { name: true } },
        photo_link: true,
        companies: { select: { name: true } },
        title_status: true,
        trn: true,
        title_delivery_location: true,
        pickup_status: true,
        dispatch_remark: true,
        title_receive_date: true,
      },
      orderBy: [
        {
          carstate: 'desc',
        },
        {
          pol_locations: {
            name: 'asc',
          },
        },
      ],
    });
    const loadNote = await this.prisma.company_notes.findMany({
      where: {
        company_id: company_id,
        deleted_at: null,
      },
      select: {
        locations: { select: { name: true } },
        reason: true,
      },
    });

    const tempVehicles = vehicles.filter(
      (item) => item.carstate != 'on_hand_with_title',
    );

    const arrangedNotesWith = [];
    tempVehicles.forEach((item) => {
      if (item.carstate == 'on_hand_no_title') {
        arrangedNotesWith.push({
          note:
            item?.title_status &&
            titleStepOne.includes(item?.title_status) &&
            item?.title_status !== 'CUSTOM' &&
            (item?.trn || item?.title_delivery_location)
              ? `${item?.trn} <br /> ${item?.title_delivery_location}`
              : item?.title_status,
          vehicles: [item],
        });
      } else {
        arrangedNotesWith.push({
          note: item?.dispatch_remark ?? '',
          vehicles: [item],
        });
      }
    });

    const groupedVehicles = {};
    vehicles.forEach((vehicle) => {
      const locationName = vehicle.pol_locations.name;
      if (!groupedVehicles[locationName]) {
        groupedVehicles[locationName] = [];
      }
      groupedVehicles[locationName].push(vehicle);
    });

    // Create arrangedNotes array with vehicles grouped by pol_locations.name
    const arrangedNotes = [];
    loadNote.forEach((note) => {
      const locationName = note.locations.name;
      if (groupedVehicles[locationName]) {
        const vehiclesWithTitles = groupedVehicles[locationName].filter(
          (vehicle) => vehicle.carstate === 'on_hand_with_title',
        );
        arrangedNotes.push({
          note: note.reason,
          vehicles: vehiclesWithTitles,
        });
        delete groupedVehicles[locationName];
      }
    });

    Object.keys(groupedVehicles).forEach((locationName) => {
      arrangedNotes.push({
        note: '',
        vehicles: groupedVehicles[locationName],
      });
    });

    const vehiclesForTable =
      arrangedNotesWith.length > 0 ? arrangedNotesWith : arrangedNotes;

    const html = await this.vehicleInventoryTable(
      vehiclesForTable,
      vehicles[0]?.companies?.name,
      state == 'on_hand_with_title'
        ? 'https://latest-api.pglsystem.com/images/With_title.jpg'
        : state == 'on_hand_no_title'
          ? 'https://latest-api.pglsystem.com/images/inventoryNoTitle.jpg'
          : 'https://latest-api.pglsystem.com/images/inventory.jpg',
      state,
      note,
    );
    if (type === 'pdf') {
      return await generatePdf(
        html,
        {
          top: '0',
          bottom: '0',
          right: '0',
          left: '0',
        },
        true,
      );
    } else if (type === 'image') {
      return await generateImage(html, true);
    }
  }

  async companyPort(ids: number[]) {
    const queryRawId = `select company_notes.id, company_notes.location_id, company_notes.reason,
                        locations.name, companies.id as companyID, companies.name as companyName
                        from vehicles inner join companies on companies.id = vehicles.company_id
                        left join company_notes on company_notes.company_id = companies.id
                        left join locations on locations.id = vehicles.point_of_loading
                        where vehicles.carstate = 'on_hand_with_title' and company_notes.location_id = locations.id
                        and companies.id in (${ids}) GROUP BY company_notes.location_id, locations.name, company_notes.reason, companies.id, companies.name, company_notes.id;`;
    const groupedData = await this.prisma.$queryRawUnsafe(queryRawId);
    const resultData = Object.values(groupedData);
    return { result: true, data: resultData };
  }

  addVehicleTransaction = async (
    query: addVehicleTransaction,
    transaction_by: number,
  ) => {
    const { transactionNo, vehicle_id } = query;
    const vehicle = await this.prisma.vehicles.findFirst({
      where: { id: vehicle_id },
    });

    if (vehicle) {
      const data = await this.prisma.vehicles.update({
        where: { id: vehicle_id },
        data: {
          transaction_no: transactionNo,
          transaction_at: new Date(),
          transaction_by,
        },
      });
      return {
        result: true,
        message: `Transaction Added Successfully!`,
        data: data,
      };
    } else {
      return {
        result: false,
        message: 'There is no Vehicle Found. Contact IT',
      };
    }
  };
}
