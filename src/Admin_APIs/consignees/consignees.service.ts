import { Injectable } from '@nestjs/common';
import { CreateConsigneeDto, UpdateConsigneeDto } from './dto/consignees.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { catch_response } from 'src/Commons/helpers/response.helper';

@Injectable()
export class ConsigneesService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateConsigneeDto, created_by: number) {
    return this.prisma.company_consignees.create({
      data: { ...dto, created_by },
      include: { company: true },
    });
  }

  async findAll(params: {
    page?: number;
    per_page?: number;
    search?: string;
    filterData?: any;
  }) {
    const { page = 1, per_page = 50, search, filterData } = params;
    const skip = (page - 1) * per_page;

    const where: any = {
      deleted_at: null,
    };

    if (search) {
      where.OR = [
        { consignee: { contains: search, mode: 'insensitive' } },
        { consignee_city: { contains: search, mode: 'insensitive' } },
        { consignee_country: { contains: search, mode: 'insensitive' } },
        { company: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (filterData?.company_id) {
      where.company_id = Number(filterData.company_id);
    }
    if (filterData?.created_by) {
      where.created_by = Number(filterData.created_by);
    }
    if (filterData?.updated_by) {
      where.updated_by = Number(filterData.updated_by);
    }
    if (filterData?.deleted_by) {
      where.deleted_by = Number(filterData.deleted_by);
    }

    if (filterData?.created_at?.from || filterData?.created_at?.to) {
      where.created_at = {};
      if (filterData.created_at.from) {
        where.created_at.gte = new Date(filterData.created_at.from);
      }
      if (filterData.created_at.to) {
        const toDate = new Date(filterData.created_at.to);
        toDate.setHours(23, 59, 59, 999);
        where.created_at.lte = toDate;
      }
    }

    if (filterData?.updated_at?.from || filterData?.updated_at?.to) {
      where.updated_at = {};
      if (filterData.updated_at.from) {
        where.updated_at.gte = new Date(filterData.updated_at.from);
      }
      if (filterData.updated_at.to) {
        const toDate = new Date(filterData.updated_at.to);
        toDate.setHours(23, 59, 59, 999);
        where.updated_at.lte = toDate;
      }
    }

    const [data, total] = await Promise.all([
      this.prisma.company_consignees.findMany({
        where,
        skip,
        take: per_page,
        include: {
          company: true,
          createdByUser: true,
          updatedByUser: true,
          deletedByUser: true,
        },
        orderBy: { id: 'desc' },
      }),
      this.prisma.company_consignees.count({ where }),
    ]);

    return { data, total };
  }

  async findOne(id: number) {
    return this.prisma.company_consignees.findFirst({
      where: { id, deleted_at: null }, // ensure not deleted
      include: { company: true },
    });
  }

  async update(id: number, dto: UpdateConsigneeDto) {
    return this.prisma.company_consignees.update({
      where: { id },
      data: dto,
      include: { company: true },
    });
  }

  softDeleteOne = (ids: number[], deleted_by: number) =>
    this.prisma.company_consignees
      .updateMany({
        where: { id: { in: ids.map(Number) } },
        data: { deleted_at: new Date(), deleted_by },
      })
      .then((data) => ({
        result: data.count > 0,
        message: `${data.count} record(s) deleted successfully.`,
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
}
