import { BadRequestException, Injectable } from '@nestjs/common';
import {
  CreateExitPaperDto,
  SummaryExitPaperDto,
  UpdateExitPaperDto,
  filterExitPapers,
} from './dto/exit_papers.dto';
import { CommonFields } from 'src/Commons/services/common.fields';
import { PaginateDto } from 'src/Commons/dto/searchPaginateDto.dto';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { ordersBy } from 'src/Commons/helpers/ordersby.helper';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import { DbService } from 'src/prisma/prisma.service';
import { exit_papers_enum } from 'src/Commons/enums/enums';
import { createLog } from 'src/Commons/helpers/log_functions.helper';

const create_update_delete_by = CommonFields([
  'users_exit_papers_created_byTousers',
  'users_exit_papers_updated_byTousers',
  'users_exit_papers_deleted_byTousers',
]);

const create_update_by = CommonFields([
  'users_exit_papers_created_byTousers',
  'users_exit_papers_updated_byTousers',
]);

const searchColumns = [
  'declaration_number',
  'claim_number',
  'date@@issue_date',
  'many@@exit_papers_vehicles.int@@vat',
  'many@@exit_papers_vehicles.int@@custom_duty',
  'many@@exit_papers_vehicles.vehicles.int@@price',
  'many@@exit_papers_vehicles.vehicles.vin',
  'many@@exit_papers_vehicles.vehicles.year',
  'many@@exit_papers_vehicles.vehicles.make',
  'many@@exit_papers_vehicles.vehicles.model',
  'many@@exit_papers_vehicles.vehicles.color',
  'many@@exit_papers_vehicles.vehicles.containers.container_number',
  'many@@exit_papers_vehicles.custom_container_number',
  'many@@exit_papers_vehicles.custom_vin',
  'many@@exit_papers_vehicles.custom_company_name',
  'many@@exit_papers_vehicles.custom_vehicle_description',
  'many@@exit_papers_vehicles.int@@custom_vehicle_price',
];

const include = {
  ...create_update_by,
  exit_papers_vehicles: {
    include: {
      vehicles: {
        select: {
          vin: true,
          make: true,
          model: true,
          color: true,
          year: true,
          price: true,
          companies: {
            select: {
              id: true,
              name: true,
              exchange_rates: {
                select: {
                  currency: true,
                  rate: true,
                  created_at: true,
                },
              },
            },
          },
          containers: {
            select: {
              id: true,
              container_number: true,
              clear_logs: {
                // where: { deleted_at: null },
                select: {
                  id: true,
                  clearing_company: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

// START: Query for exit paper credits
const includeExitPaperCredits = {
  customerPaymentTransaction: {
    where: { deleted_at: null },
    include: {
      customer_credits: {
        include: {
          vehicle: {
            select: {
              id: true,
              vin: true,
              lot_number: true,
            },
          },
        },
      },
      exitPapers: {
        where: {
          deleted_at: null,
        },
        select: {
          id: true,
          service_charge: true,
          exit_papers_vehicles: {
            where: {
              deleted_at: null,
            },
            include: {
              vehicles: {
                select: {
                  id: true,
                  lot_number: true,
                  vin: true,
                },
              },
            },
          },
          customerPaymentTransaction: {
            where: { deleted_at: null },
            select: {
              id: true,
              customer_credits: {
                where: { deleted_at: null },
                include: {
                  vehicle: {
                    select: {
                      id: true,
                      vin: true,
                      lot_number: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
// END: Query for exit paper credits

@Injectable()
export class ExitPapersService {
  private prisma: typeof DbService.prisma;
  constructor(private readonly db: DbService) {
    this.prisma = DbService.prisma;
  }

  findAll = async (query: filterExitPapers, user_id: number) => {
    const offset = query.page;
    const limit = query.per_page;
    const tab: string = query.tab;
    const where = await searchFilter(query, searchColumns);
    const dispatch = JSON.parse(query.filterData);
    const isExport = query.isExport;
    const exportTotal = query.exportTotal;
    const exportType = query.exportType;

    if (isExport) {
      createLog({
        log_name: 'pgl',
        description: 'Export',
        subject_type: 'User',
        subject_id: user_id,
        causer_type: 'User',
        causer_id: user_id,
        properties: { exportTotal: exportTotal, exportType: exportType },
      });
    }

    if (
      tab &&
      Object.values(exit_papers_enum).includes(tab as exit_papers_enum)
    ) {
      where['status'] = tab;
    } else if (tab === 'mukhasa') {
      where['is_mukhasa'] = true;
    } else if (tab === 'credit_given_to_customer') {
      where['is_credit_given'] = true;
      where['status'] = exit_papers_enum?.credit_given_to_customer;
    }

    if (dispatch?.is_mukhasa?.length > 0) {
      const isMukhasa = dispatch?.is_mukhasa;
      const mukhasa = [
        isMukhasa.includes('Yes') ? { is_mukhasa: true } : null,
        isMukhasa.includes('No') ? { is_mukhasa: false } : null,
      ].filter((item) => item != null);
      if (mukhasa.length > 0) {
        where['AND'] = where['AND']
          ? [{ OR: mukhasa }]
          : [...where['AND'], { OR: mukhasa }];
      }
    }

    if (dispatch?.is_credit_given_to_customer?.length > 0) {
      const isCreditGiven = dispatch?.is_credit_given_to_customer;
      const greditGiven = [
        isCreditGiven.includes('Yes') ? { is_credit_given: true } : null,
        isCreditGiven.includes('No') ? { is_credit_given: false } : null,
      ].filter((item) => item != null);
      if (greditGiven.length > 0) {
        where['AND'] = where['AND']
          ? [{ OR: greditGiven }]
          : [...where['AND'], { OR: greditGiven }];
      }
    }

    return await Promise.all([
      this.prisma.exit_papers.count({ where }),
      this.prisma.exit_papers.findMany({
        skip: (offset - 1) * limit,
        take: limit,
        where,
        orderBy: [{ [query.column]: query.order }],
        include: { ...include, ...includeExitPaperCredits },
      }),
    ])
      .then(([total, data]) => ({ page: offset, per_page: limit, total, data }))
      .catch((err) => console.log(err))
      .finally(() => this.prisma.$disconnect());
  };

  create = async (dtos: CreateExitPaperDto, userId: any) => {
    const { vehicles, custom_vehicles, ...dto } = dtos;
    const vins = custom_vehicles.map((item) => item.c_vin);

    const existingVins = await this.prisma.exit_papers_vehicles.findMany({
      where: { custom_vin: { in: vins } },
    });

    if (existingVins.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicle with the same VIN already exist!',
        error: 'Bad Request',
      });
    }

    const ids = vehicles.map((item) => item.vehicle_id);
    const idCounts = ids.reduce(
      (acc, id) => {
        acc[id] = (acc[id] || 0) + 1;
        return acc;
      },
      {} as { [key: string]: number },
    );

    if (Object.values(idCounts).some((count) => count > 1)) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'You added duplicate vehicles!',
        error: 'Bad Request',
      });
    }

    const vehiclesData = await this.prisma.vehicles.findMany({
      where: { id: { in: ids } },
      select: {
        id: true,
        companies: { select: { id: true } },
        containers: { select: { id: true, container_number: true } },
      },
    });

    const containerNumbers = vehiclesData.map(
      (item) => item.containers?.container_number,
    );
    const companyIds = vehiclesData.map((item) => item.companies?.id);
    const containerIds = vehiclesData.map((item) => item.containers?.id);

    if (containerNumbers.some((number) => !number)) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicles must have a container number!',
      });
    }

    if (new Set(containerIds).size > 1) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicles are not in the same container!',
        error: 'Bad Request',
      });
    }

    if (new Set(companyIds).size > 1) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicles do not have the same company!',
        error: 'Bad Request',
      });
    }

    const vehiclesExits = await this.prisma.exit_papers_vehicles.findFirst({
      where: { vehicle_id: { in: ids }, deleted_at: null },
    });

    if (vehiclesExits) {
      throw new BadRequestException({
        statusCode: 400,
        message:
          ids.length > 1
            ? 'Exit paper already exists for one or more of these vehicles!'
            : 'Exit paper already exists for this vehicle!',
      });
    }

    const vehicleData = vehicles.map((item) => ({
      vehicle_id: item.vehicle_id,
      vat: item.vat,
      custom_duty: item.custom_duty,
      currency: item.currency,
      claim_amount: item.claim_amount,
      custom_vin: null,
      custom_company_name: null,
      custom_vehicle_description: null,
      custom_vehicle_price: null,
      custom_container_number: null,
    }));

    const customVehicleData = custom_vehicles.map((item) => ({
      vehicle_id: null,
      vat: item.c_vat,
      custom_duty: item.c_custom_duty,
      currency: item.c_currency,
      custom_vin: item.c_vin,
      claim_amount: item.c_claim_amount,
      custom_company_name: item.c_company_name,
      custom_vehicle_description: item.c_vehicle_description,
      custom_vehicle_price: item.c_vehicle_price,
      custom_container_number: item.c_container_number,
    }));

    const exitPapersVehiclesData = [...vehicleData, ...customVehicleData];

    try {
      const createdExitPaper = await this.prisma.exit_papers.create({
        data: {
          ...dto,
          service_charge: dto.service_charge,
          status: dto.is_credit_given
            ? exit_papers_enum?.credit_given_to_customer
            : exit_papers_enum?.credit_not_given_to_customer,
          exit_papers_vehicles: {
            createMany: {
              data: exitPapersVehiclesData,
            },
          },
          created_by: userId,
          updated_by: userId,
        },
        include: include,
      });

      return { result: true, data: createdExitPaper };
    } catch (error) {
      console.error(error);
      throw new Error('Error creating exit paper');
    } finally {
      this.prisma.$disconnect();
    }
  };

  update = async (id: number, dtos: UpdateExitPaperDto, userId: any) => {
    // START: Fields for exit paper credits
    const customerPaymentTransactionId = dtos.customerPaymentTransactionId;
    const customer_credits_total_amount = dtos?.amount;
    const customer_credits = dtos?.customer_credits;
    const credits_service_charge = dtos.credits_service_charge;
    const credits_currency = dtos.currency;
    delete dtos.customerPaymentTransactionId;
    delete dtos.amount;
    delete dtos.customer_credits;
    delete dtos.credits_service_charge;
    delete dtos.currency;
    const hasCustomerCredits =
      typeof customerPaymentTransactionId === 'number' &&
      customerPaymentTransactionId > 0;
    // END: Fields for exit paper credits

    const { vehicles, custom_vehicles, ...dto } = dtos;
    const ids = vehicles.map((item) => item.vehicle_id);

    const idCounts = ids.reduce(
      (acc, id) => {
        acc[id] = (acc[id] || 0) + 1;
        return acc;
      },
      {} as { [key: string]: number },
    );

    if (Object.values(idCounts).some((count) => count > 1)) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'You added duplicate vehicles!',
        error: 'Bad Request',
      });
    }

    const vehiclesData = await this.prisma.vehicles.findMany({
      where: { id: { in: ids } },
      select: {
        id: true,
        companies: { select: { id: true } },
        containers: { select: { id: true } },
      },
    });

    const vehiclesExits = await this.prisma.exit_papers_vehicles.findFirst({
      where: {
        vehicle_id: { in: ids },
        deleted_at: null,
        exit_paper_id: { not: id },
      },
    });

    if (vehiclesExits) {
      throw new BadRequestException({
        statusCode: 400,
        message:
          ids.length > 1
            ? 'Exit paper already exists for one or more of these vehicles!'
            : 'Exit paper already exists for this vehicle!',
      });
    }

    const containerIds = vehiclesData.map((v) => v.containers?.id);
    const uniqueContainerIds = new Set(containerIds);
    if (uniqueContainerIds.size > 1) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicles are not in the same container!',
        error: 'Bad Request',
      });
    }

    const companyIds = vehiclesData.map((v) => v.companies?.id);
    const uniqueCompanyIds = new Set(companyIds);
    if (uniqueCompanyIds.size > 1) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicles do not have the same company!',
        error: 'Bad Request',
      });
    }

    const inputVehicleIds = vehicles.map((item) => item.vehicle_id);
    const inputCustomVins = custom_vehicles.map((item) => item.c_vin);

    const existingVehicles = await this.prisma.exit_papers_vehicles.findMany({
      where: { exit_paper_id: id, deleted_at: null },
      select: { vehicle_id: true, custom_vin: true },
    });

    const existingVehicleIds = existingVehicles
      .filter((v) => v.vehicle_id !== null)
      .map((v) => v.vehicle_id);

    const existingCustomVins = existingVehicles
      .filter((v) => v.custom_vin !== null)
      .map((v) => v.custom_vin);
    const vehiclesToAdd = vehicles.filter(
      (item) => !existingVehicleIds.includes(item.vehicle_id),
    );

    const vehiclesToUpdate = vehicles.filter((item) =>
      existingVehicleIds.includes(item.vehicle_id),
    );

    const vehiclesToDelete = existingVehicleIds.filter(
      (id) => !inputVehicleIds.includes(id),
    );

    const customVehiclesToAdd = custom_vehicles.filter(
      (item) => !existingCustomVins.includes(item.c_vin),
    );

    const customVins = customVehiclesToAdd.map((item) => item.c_vin);
    const duplicateCustomVins = await this.prisma.exit_papers_vehicles.findMany(
      {
        where: { custom_vin: { in: customVins } },
      },
    );

    if (duplicateCustomVins.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Vehicle with the same VIN already exist!',
        error: 'Bad Request',
      });
    }

    const customVehiclesToUpdate = custom_vehicles.filter((item) =>
      existingCustomVins.includes(item.c_vin),
    );

    const customVehiclesToDelete = existingCustomVins.filter(
      (vin) => !inputCustomVins.includes(vin),
    );

    const exitPapersVehiclesData = [
      ...vehiclesToUpdate.map((item) => ({
        where: { vehicle_id: item.vehicle_id },
        data: {
          vat: item.vat,
          custom_duty: item.custom_duty,
          custom_vin: null,
          currency: item.currency,
          claim_amount: item.claim_amount,
          custom_company_name: null,
          custom_vehicle_description: null,
          custom_vehicle_price: null,
          custom_container_number: null,
        },
      })),
      ...customVehiclesToUpdate.map((item) => ({
        where: { custom_vin: item.c_vin },
        data: {
          vat: item.c_vat,
          custom_duty: item.c_custom_duty ?? 0,
          claim_amount: item.c_claim_amount,
          custom_vin: item.c_vin,
          currency: item.c_currency,
          custom_company_name: item.c_company_name,
          custom_vehicle_description: item.c_vehicle_description,
          custom_vehicle_price: item.c_vehicle_price,
          custom_container_number: item.c_container_number,
        },
      })),
    ];

    const vehiclesToCreate = [
      ...vehiclesToAdd.map((item) => ({
        exit_paper_id: id,
        vehicle_id: item.vehicle_id,
        vat: item.vat,
        custom_duty: item.custom_duty,
        currency: item.currency,
        custom_vin: null,
        claim_amount: item.claim_amount,
        custom_company_name: null,
        custom_vehicle_description: null,
        custom_vehicle_price: null,
        custom_container_number: null,
      })),
      ...customVehiclesToAdd.map((item) => ({
        exit_paper_id: id,
        vehicle_id: null,
        vat: item.c_vat,
        custom_duty: item.c_custom_duty,
        custom_vin: item.c_vin,
        currency: item.c_currency,
        claim_amount: item.c_claim_amount,
        custom_company_name: item.c_company_name,
        custom_vehicle_description: item.c_vehicle_description,
        custom_vehicle_price: item.c_vehicle_price,
        custom_container_number: item.c_container_number,
      })),
    ];

    try {
      const updatedExitPaper = await this.prisma.$transaction(async (tx) => {
        if (vehiclesToDelete.length) {
          await tx.exit_papers_vehicles.deleteMany({
            where: { vehicle_id: { in: vehiclesToDelete } },
          });
        }
        if (customVehiclesToDelete.length) {
          await tx.exit_papers_vehicles.deleteMany({
            where: { custom_vin: { in: customVehiclesToDelete } },
          });
        }

        for (const updateData of exitPapersVehiclesData) {
          await tx.exit_papers_vehicles.updateMany(updateData);
        }

        if (vehiclesToCreate.length) {
          await tx.exit_papers_vehicles.createMany({
            data: vehiclesToCreate,
          });
        }

        // START: Handle exit paper credits
        if (hasCustomerCredits) {
          // --- START: Handle existing customer_credits ---
          const existingPayments =
            await tx.customer_payment_transactions.findUnique({
              where: { id: customerPaymentTransactionId },
              include: {
                customer_credits: {},
              },
            });

          const existingCreditMap = new Map<
            number,
            (typeof existingPayments.customer_credits)[0]
          >();
          for (const credit of existingPayments.customer_credits) {
            existingCreditMap.set(credit.id, credit);
          }

          const incomingCredits = customer_credits ?? [];

          const incomingIds = incomingCredits.map((c) => +c.id).filter(Boolean);
          const existingIds = existingPayments.customer_credits
            .filter((c) => c.deleted_at === null)
            .map((c) => c.id);

          const deletedIds = existingIds.filter(
            (id) => !incomingIds.includes(id),
          );

          // Soft-delete removed credits
          if (deletedIds.length > 0) {
            await this.prisma.customer_credits.updateMany({
              where: { id: { in: deletedIds } },
              data: {
                deleted_at: new Date(),
                deleted_by: userId,
              },
            });
          }

          // --- END: Handle existing customer_credits ---

          // Update each credit manually (instead of using upsert)
          for (const credit of incomingCredits) {
            const creditId = credit.id ? +credit.id : undefined;

            if (creditId && existingCreditMap.has(creditId)) {
              // Restore and update soft-deleted credit or just update active one
              await tx.customer_credits.update({
                where: { id: creditId },
                data: {
                  vehicle_id: credit.vehicle_id,
                  container_id: credit.container_id,
                  amount: +credit.amount,
                  remark: credit.remark,
                  updated_at: new Date(),
                  updated_by: userId,
                  deleted_at: null, // Restore if it was soft-deleted
                  deleted_by: null,
                },
              });
            } else {
              // Create new credit
              await tx.customer_credits.create({
                data: {
                  customer_payment_transactionId: customerPaymentTransactionId,
                  vehicle_id: credit.vehicle_id,
                  container_id: credit.container_id,
                  amount: +credit.amount,
                  remark: credit.remark,
                  created_at: new Date(),
                  created_by: userId,
                },
              });
            }
          }

          // Update the payment transaction
          await tx.customer_payment_transactions.update({
            where: {
              id: customerPaymentTransactionId,
            },
            data: {
              amount: customer_credits_total_amount,
              service_charge: credits_service_charge,
              currency: credits_currency,
              updated_at: new Date(),
              updated_by: userId,
            },
          });
        }
        // END: Handle exit paper credits

        return await tx.exit_papers.update({
          where: { id },
          data: {
            ...dto,
            service_charge: dto.service_charge,
            status:
              !hasCustomerCredits &&
              [
                exit_papers_enum?.credit_not_given_to_customer,
                exit_papers_enum?.credit_given_to_customer,
              ].includes(dto.status)
                ? dto.is_credit_given
                  ? exit_papers_enum?.credit_given_to_customer
                  : exit_papers_enum?.credit_not_given_to_customer
                : dto.status,
            created_by: userId,
            updated_by: userId,
          },
          include: include,
        });
      });

      return { result: true, data: updatedExitPaper };
    } catch (error) {
      console.error(error);
      throw new Error('Error updating exit paper');
    } finally {
      await this.prisma.$disconnect();
    }
  };

  softDelete = async (ids: number[], deleted_by: number) => {
    const transaction = await this.prisma.$transaction([
      this.prisma.exit_papers.updateMany({
        where: { id: { in: ids.map(Number) } },
        data: {
          deleted_at: new Date(),
          deleted_by,
        },
      }),

      this.prisma.exit_papers_vehicles.updateMany({
        where: { exit_paper_id: { in: ids.map(Number) } },
        data: {
          deleted_at: new Date(),
          deleted_by,
        },
      }),
    ]);

    return transaction;
  };

  getTrash = async (query: PaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const orderBy = await ordersBy(query);

    const totalCount = await this.prisma.exit_papers.count({
      where: {
        deleted_at: { not: null },
      },
    });

    const records = await this.prisma.exit_papers
      .findMany({
        skip: (offset - 1) * limit,
        take: limit,
        orderBy,
        where: {
          deleted_at: { not: null },
        },
        include: {
          ...create_update_delete_by,
          ...include,
        },
      })
      .finally(() => this.prisma.$disconnect());
    return {
      total: totalCount,
      records,
    };
  };

  restore = async (ids: number[], updated_by: number) => {
    const idn = ids.map(Number);
    await this.prisma.exit_papers
      .updateMany({
        where: {
          id: { in: idn },
        },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  changeExitPaperStatus = async (query: any, user: any) => {
    const { exitPapersIds, exitPaperStatus } = query;
    try {
      const data = await this.prisma.exit_papers.updateMany({
        where: { id: { in: exitPapersIds } },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by: user.loginable_id,
          status: exitPaperStatus,
        },
      });
      if (data.count > 0) {
        return {
          result: true,
          message: `${data.count} records have been updated!`,
        };
      } else {
        return {
          result: false,
          message: 'No records were updated. Please check the provided IDs.',
        };
      }
    } catch (error) {
      console.error('Error updating records:', error);
      return {
        result: false,
        message: 'An error occurred during the update.',
        error,
      };
    }
  };

  getOne = async (id: number) => {
    return await this.prisma.exit_papers
      .findUnique({
        where: { id },
        include: {
          ...create_update_delete_by,
          ...include,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findOne = async (id: number) => {
    return await this.prisma.vehicles
      .findUnique({
        where: { id },
        select: {
          price: true,
          year: true,
          make: true,
          model: true,
          color: true,
          mix_shipping_vehicles: {
            where: { vehicle_id: id },
            select: {
              vat_and_custom: true,
            },
          },
          containers: {
            select: {
              id: true,
              container_number: true,
              clear_logs: {
                select: {
                  log_invoices: {
                    select: { invoice_date: true },
                  },
                },
              },
            },
          },
          companies: {
            select: {
              name: true,
            },
          },
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getSummary = async (query: SummaryExitPaperDto) => {
    const {
      page,
      per_page: limit,
      order = 'ASC',
      status,
      search,
      exactMatch,
    } = query;
    const orderColumn = 'id';
    const offset = (page - 1) * limit;

    const paginate = `
    ORDER BY ${orderColumn} ${order}
    ${limit > 0 ? `LIMIT ${limit} OFFSET ${offset}` : ''}`;

    const conditions = [
      `exit_papers.deleted_at IS NULL`,
      status ? `exit_papers.status = '${status}'` : null,
      search
        ? exactMatch
          ? `companies.name = '${search}'`
          : `companies.name ILIKE '%${search}%'`
        : null,
    ]
      .filter(Boolean)
      .join(' AND ');

    const summaryQuery = `
    SELECT
      companies.id,
      companies.name,
      ${this.getCommonQuery()}
    FROM
      companies
    LEFT JOIN
      vehicles ON vehicles.company_id = companies.id
    LEFT JOIN
      exit_papers_vehicles ON exit_papers_vehicles.vehicle_id = vehicles.id
    LEFT JOIN
      exit_papers ON exit_papers.id = exit_papers_vehicles.exit_paper_id
    WHERE ${conditions}
    GROUP BY companies.id, companies.name
    ${paginate}`;

    const totalDataQuery = `
    SELECT ${this.getCommonQuery()}
    FROM exit_papers
    WHERE exit_papers.deleted_at IS NULL`;

    const totalCountQuery = `
    SELECT COUNT(*)::int AS total
    FROM (${summaryQuery}) AS sub`;

    try {
      const [data, totalCount, totalData] = await Promise.all([
        this.db.runRaw(summaryQuery),
        this.db.runRaw(totalCountQuery),
        this.db.runRaw(totalDataQuery),
      ]);

      const totalDataObject = totalData?.[0] || {};

      return {
        data,
        totalData: totalDataObject,
        total: totalCount?.[0]?.total || 0,
        page,
        per_page: limit,
      };
    } catch (error) {
      console.error('Error fetching exit paper summary:', error);
      throw new Error('Failed to fetch exit paper summary.');
    }
  };

  private getCommonQuery() {
    return `
    SUM(CASE WHEN exit_papers.status = 'document_recieved' THEN 1 ELSE 0 END)::int AS document_received_count,
    SUM(CASE WHEN exit_papers.status = 'submitted_to_custom' THEN 1 ELSE 0 END)::int AS submitted_to_custom_count,
    SUM(CASE WHEN exit_papers.status = 'recieved_from_custom' THEN 1 ELSE 0 END)::int AS received_from_custom_count,
    SUM(CASE WHEN exit_papers.status = 'credit_not_given_to_customer' THEN 1 ELSE 0 END)::int AS credit_not_given_to_customer_count,
    SUM(CASE WHEN exit_papers.status = 'credit_given_to_customer' THEN 1 ELSE 0 END)::int AS credit_given_to_customer_count,
    SUM(CASE WHEN exit_papers.status = 'suspended' THEN 1 ELSE 0 END)::int AS suspended_count,
    SUM(CASE WHEN exit_papers.status = 'rejected' THEN 1 ELSE 0 END)::int AS rejected_count`;
  }
}
