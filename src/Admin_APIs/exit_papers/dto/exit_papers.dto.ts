import { ApiProperty, PartialType } from '@nestjs/swagger';
import { exit_paper_status, mix_currency_type } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsDecimal,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import {
  SearchPaginateDto,
  WithoutFilterPaginateDto,
} from 'src/Commons/dto/searchPaginateDto.dto';
import { exit_papers_enum } from 'src/Commons/enums/enums';
import { toDate, trim } from 'src/Commons/helpers/cast.helper';
import { customer_credits_form_dto } from '../../customer_payment_transactions/dto/customer-payment-transactions.dto';

export class filterExitPapers extends SearchPaginateDto {
  @IsString()
  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => trim(value))
  tab?: exit_paper_status;

  @ApiProperty({ required: false })
  @IsOptional()
  isExport?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  exportTotal?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  exportType?: string;
}

export class ExitPapersIdsDto {
  @ApiProperty({ type: [Number], required: true })
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value == 'object' && value.length > 1) return value.map(Number);
    else return [+value];
  })
  @IsNumber({ allowNaN: false }, { each: true })
  @ArrayMinSize(1)
  vehiclesIds: number[];
}

export class changeExitPaperStatus extends ExitPapersIdsDto {
  @IsString()
  @ApiProperty()
  @IsEnum(exit_paper_status)
  @Transform(({ value }) => trim(value))
  ExitPaperStatus?: exit_paper_status;
}

export class VehicleItemDto {
  @IsNumber()
  @IsOptional()
  vehicle_id: number;

  @IsNumber()
  @IsNotEmpty()
  vat: number;

  @IsNumber()
  @IsOptional()
  custom_duty?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => trim(value))
  @IsEnum(mix_currency_type)
  currency: mix_currency_type;

  @IsString()
  @IsOptional()
  custom_vin: string;

  @IsNumber()
  @IsOptional()
  claim_amount: number;
}

export class CustomVehicleItemDto {
  @IsString()
  c_vin: string;

  @IsString()
  @IsOptional()
  c_company_name?: string;

  @IsString()
  @IsOptional()
  c_vehicle_description?: string;

  @IsNumber()
  @IsOptional()
  c_vat: number;

  @IsNumber()
  @IsOptional()
  c_custom_duty?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => trim(value))
  @IsEnum(mix_currency_type)
  c_currency: mix_currency_type;

  @IsNumber()
  @IsOptional()
  c_vehicle_price: number;

  @IsString()
  @IsOptional()
  c_container_number?: string;

  @IsNumber()
  @IsOptional()
  c_claim_amount: number;
}
export class CreateExitPaperDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.issue_date && o.issue_date != '')
  @Transform(({ value }) => toDate(value))
  @Type(() => Date)
  @IsDate()
  issue_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.declaration_number != null)
  @Transform(({ value }) => trim(value))
  @IsString()
  declaration_number?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.declaration_date && o.declaration_date != '')
  @Transform(({ value }) => toDate(value))
  @Type(() => Date)
  @IsDate()
  declaration_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.claim_number != null)
  @Transform(({ value }) => trim(value))
  @IsString()
  claim_number?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.claim_date && o.claim_date != '')
  @Transform(({ value }) => toDate(value))
  @Type(() => Date)
  @IsDate()
  claim_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.request_no != null)
  @Transform(({ value }) => trim(value))
  @IsString()
  request_no?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trim(value))
  @ValidateIf((o) => o.status != '')
  @IsEnum(exit_papers_enum)
  status?: exit_papers_enum;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  is_credit_given?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  is_mukhasa?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.prove != null)
  @Transform(({ value }) => trim(value))
  @IsString()
  prove?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.description != null)
  @Transform(({ value }) => trim(value))
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.expire_date && o.expire_date != '')
  @Transform(({ value }) => toDate(value))
  @Type(() => Date)
  @IsDate()
  expired_date?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @ValidateIf((o) => o.service_charge != null)
  service_charge?: number;

  @ApiProperty({ type: [VehicleItemDto], required: false })
  @IsOptional()
  @ValidateIf((o) => o.vehicles && o.vehicles.length > 0)
  @Type(() => VehicleItemDto)
  vehicles?: VehicleItemDto[];

  @ApiProperty({ type: [CustomVehicleItemDto], required: false })
  @IsOptional()
  @ValidateIf((o) => o.custom_vehicles && o.custom_vehicles.length > 0)
  @Type(() => CustomVehicleItemDto)
  custom_vehicles?: CustomVehicleItemDto[];
}

export class UpdateExitPaperDto extends PartialType(CreateExitPaperDto) {
  // START: Fields for Exit Paper Credits
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => +value)
  customerPaymentTransactionId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => Number(value).toFixed(3))
  @IsDecimal()
  amount?: number;

  @ApiProperty({ required: false })
  @Transform(({ value }) => trim(value))
  @IsEnum(mix_currency_type)
  currency: mix_currency_type;

  @ApiProperty({ required: false })
  @IsOptional()
  customer_credits?: customer_credits_form_dto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => Number(value).toFixed(3))
  @IsDecimal()
  credits_service_charge?: number;
  // END: Fields for Exit Paper Credits
}

export class SummaryExitPaperDto extends WithoutFilterPaginateDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.status != null)
  @IsString()
  @Transform(({ value }) => trim(value))
  status?: exit_paper_status;
}
