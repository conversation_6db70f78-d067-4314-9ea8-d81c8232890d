import {
  BadRequestException,
  Controller,
  Get,
  NotFoundException,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminGuard } from 'src/Commons/guards/jwt.guard';
import { AutoCompleteDto, UniquenessDto } from './dto/uniqueness.dto';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { DbService, PrismaService } from 'src/prisma/prisma.service';
import { user_status } from '@prisma/client';
import { formatFetchInvoices } from 'src/Commons/helpers/customerPaymentTransactions.helper';

@ApiTags('Uniqueness')
@Controller('uniqueness')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class UniquenessController {
  constructor(private readonly prisma: PrismaService) {}

  @Get()
  async uniqueness(@Query() query: UniquenessDto) {
    const { column, data, modal, vessel_id, id } = query;

    if (!data || !modal || !column) {
      throw new BadRequestException('Missing required parameters');
    }

    try {
      const model = this.prisma[modal];
      if (!model || typeof model.findFirst !== 'function') {
        throw new NotFoundException(`Model ${modal} does not exist`);
      }

      // Handle value conversion
      const value = column.toLowerCase() === 'date' 
        ? new Date(data).toISOString() 
        : data.toString();

      // Special case for seal_number with vessel_id
      if (column === 'seal_number' && vessel_id) {
        const where: any = {
          [column]: data,
          bookings: {
            vessel_id: vessel_id
          }
        };
        
        if (id) {
          where.NOT = { id };
        }

        const found = await this.prisma.containers.findFirst({ where });
        return { status: Boolean(found) };
      }

      // General case for other fields
      const where: any = {
        [column]: value ? { equals: value } : null
      };

      if (id) {
        where.NOT = { id };
      }

      const found = await model.findFirst({ where });
      return { status: Boolean(found) };

    } catch (error) {
      return { 
        status: false, 
        error: error.message || 'An error occurred' 
      };
    } finally {
      await this.prisma.$disconnect();
    }
  }
}

///////////// -----Auto Complete Controller -----//////////
@ApiTags('Auto Complete')
@Controller('autoComplete')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class AutoCompleteController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly db: DbService,
  ) {}

  @Get()
  async autoComplete(@Query() query: AutoCompleteDto) {
    const {
      column,
      modal,
      ids,
      id,
      per_page,
      isSpecialRate,
      destination_id,
      destination_ids,
      parentHierarchyCheck,
    } = query;
    let name = query[query.column];
    name = name?.trim();
    //const destinationExists = `AND destination_id = ${destination_id}`
    const exist: any = await this.prisma
      .$queryRawUnsafe(
        `SELECT column_name FROM information_schema.columns WHERE table_name = '${modal}' AND column_name = '${column}'`,
      )
      .finally(() => this.prisma.$disconnect());
    if (
      exist.length > 0 ||
      modal == 'loading_branches' ||
      modal == 'mix_companies' ||
      modal == 'complete_not_special_companies'
    ) {
      let model = this.prisma[modal];
      const where = { deleted_at: null, deleted_by: null };
      if (modal == 'bookings') {
        where['status'] = {
          notIn: ['applied'], // put your actual statuses here
        };
      }
      if (modal == 'companies') {
        where['OR'] = [
          {
            customers: {
              some: { loginable: { status: user_status.active } },
            },
          },
          {
            customers: {
              none: {},
            },
          },
        ];
        if (isSpecialRate) {
          where['is_special_rate'] = true;
        }
        if (parentHierarchyCheck) {
          let searchedRec = null;
          if (id && !name) {
            searchedRec = await this.prisma.companies.findUnique({
              where: { id },
              select: { id: true, name: true },
            });
          }
          const q = `SELECT DISTINCT ON ("name") id, ${column} FROM companies
                WHERE ${name ? `"name" ILIKE '%${name}%' AND` : ''}
                parent_id = id
                ${id ? `AND "id" != '${id}'` : ''}
                limit 20`;
          const [data] = await Promise.all([this.db.runRaw(q)]);
          if (searchedRec) data.push(searchedRec);
          return { result: true, data };
        }
      }
      if (modal == 'loading_branches') model = this.prisma.loading_states;
      if (modal == 'mix_companies') {
        model = this.prisma.companies;
        where['destination_id'] = {
          in: destination_id ? [destination_id] : [12, 24, 22],
        };
        where['mix'] = true;
      }

      if (modal == 'complete_not_special_companies') {
        model = this.prisma.companies;
        where['complete'] = true;
        where['is_special_rate'] = false;
        where['destination_id'] = {
          in: destination_ids.split(',').map(Number),
        };
        where['customers'] = {
          some: { loginable: { status: user_status.active } },
        };
      }

      if (name) {
        if (modal === 'bank_accounts') {
          where['OR'] = [
            { bank_name: { contains: name, mode: 'insensitive' } },
            { account_name: { contains: name, mode: 'insensitive' } },
            { account_number: { contains: name, mode: 'insensitive' } },
            { ibn: { contains: name, mode: 'insensitive' } },
          ];
        } else if (modal === 'loading_cities') {
          where['OR'] = [
            { city_name: { contains: name, mode: 'insensitive' } },
            {
              loading_states: {
                OR: [
                  { name: { contains: name, mode: 'insensitive' } },
                  { parent: { name: { contains: name, mode: 'insensitive' } } },
                ],
              },
            },
          ];
        } else {
          where[column] = { contains: name, mode: 'insensitive' };
        }
      }
      if (ids) where['id'] = { in: ids.split(',').map(Number) };
      else if (id) where['id'] = { in: [id] };

      if (modal == 'shipping_documents') where['active'] = true;
      if (modal == 'loading_branches') where['parentId'] = { not: null };
      if (modal == 'loading_states') where['parentId'] = null;
      const select = { id: true, [column]: true };

      if (modal == 'bank_accounts') {
        select['bank_name'] = true;
        select['account_name'] = true;
        select['account_number'] = true;
        select['currency'] = true;
      }
      if (modal == 'companies') {
        select['mix'] = true;
        select['complete_halfcut'] = true;
        select['complete'] = true;
        select['mix_halfcut'] = true;
        select['scrap'] = true;
        select['destination_id'] = true;
      }
      if (modal == 'loaders' || modal == 'drivers') where['status'] = 'active';
      if (modal == 'vessels') {
        select['locations'] = {
          select: {
            id: true,
            name: true,
          },
        };
      }
      if (modal == 'loading_cities') {
        select['loading_states'] = {
          select: {
            name: true,
            parent: {
              select: {
                name: true,
              },
            },
          },
        };
      }
      if (modal == 'bookings') {
        select['party'] = true;
      }

      try {
        // this.prisma.vessels.findMany({
        //   select: {
        //     locations: {
        //       select: {
        //         id: true,
        //         name: true,
        //       },
        //     },
        //   },
        // });
        let data = await model
          .findMany({
            where,
            skip: 0,
            take: per_page,
            select,
          })
          .finally(() => this.prisma.$disconnect());
        if (modal === 'loading_cities')
          data = this.modifyNameForLoadingCities(data);
        if (modal == 'vessels') data = this.modifyNameForVessel(data);
        if (modal === 'bank_accounts') {
          data = data?.map((op) => {
            const parts = [op.bank_name, op.account_name];
            if (op.account_number) parts.push(op.account_number);
            parts.push(`(${op.currency})`);

            return {
              id: op.id,
              bank_name: parts.filter(Boolean).join(' | '),
            };
          });
        }
        if (modal === 'bookings') {
          data = data?.map((op) => {
            return {
              id: op.id,
              booking_number: op.booking_number,
              booking_parties: op.party,
            };
          });
        }
        if (id || ids) {
          const tempId = ids ? ids.split(',').map(Number) : [id];
          const whereClause = {
            deleted_at: null,
            deleted_by: null,
            id: { notIn: tempId },
          };
          if (isSpecialRate) whereClause['is_special_rate'] = true;

          let data2 = await model
            .findMany({
              where: whereClause,
              skip: 0,
              take: per_page,
              select,
            })
            .finally(() => this.prisma.$disconnect());

          if (modal == 'vessels') data2 = this.modifyNameForVessel(data2);
          if (modal === 'loading_cities')
            data2 = this.modifyNameForLoadingCities(data2);
          if (modal === 'bank_accounts') {
            data2 = data2?.map((op) => {
              const parts = [op.bank_name, op.account_name];
              if (op.account_number) parts.push(op.account_number);
              parts.push(`(${op.currency})`);
              return {
                id: op.id,
                bank_name: parts.filter(Boolean).join(' | '),
              };
            });
          }
          if (modal === 'bookings') {
            data2 = data2?.map((op) => {
              return {
                id: op.id,
                booking_number: op.booking_number,
                booking_parties: op.party,
              };
            });
          }
          const mergedData = [...data, ...data2];
          return { result: true, data: mergedData };
        } else {
          return { result: true, data };
        }
      } catch (err) {
        catch_response(err);
      }
    } else return { result: false, message: 'Column does not exist.' };
  }

  modifyNameForVessel(data) {
    return data.map((item) => ({
      name: item.name + ' (' + item.locations.name.split(',')?.[1].trim() + ')',
      id: item.id,
    }));
  }
  modifyNameForLoadingCities(data) {
    return data.map((item) => ({
      city_name: `${item.city_name} | ${item.loading_states.name} | ${item.loading_states.parent.name}`,
      id: item.id,
    }));
  }
  @Get('autoCompleteMulti')
  async autoCompleteMulti(@Query() query: AutoCompleteDto) {
    const { column, modal, ids, id, pol } = query;

    const exist: any = await this.prisma.$queryRawUnsafe(
      `SELECT column_name FROM information_schema.columns WHERE table_name = '${modal}' AND column_name IN (${column
        .map((column) => `'${column}'`)
        .join(', ')})`,
    );

    if (exist.length > 0) {
      const model = this.prisma[modal];
      const where = { deleted_at: null, deleted_by: null };
      if (query.name) {
        const SearchValue = { contains: query.name, mode: 'insensitive' };
        where[column[0]] = SearchValue;
      }
      if (pol) where['port_of_loading'] = pol;
      if (ids) where['id'] = { in: ids.split(',').map(Number) };
      else if (id) where['id'] = { in: [id] };

      if (modal == 'shipping_documents') where['active'] = true;

      try {
        const data = await model.findMany({
          where,
          skip: 0,
          take: 20,
          select: {
            id: true,
            ...column.reduce((acc, column) => ({ ...acc, [column]: true }), {}),
          },
          // select: { id: true, [column[0]]: true },
        });
        this.prisma.$disconnect();

        if (id || ids) {
          const tempId = ids ? ids.split(',').map(Number) : [id];
          const data2 = await model.findMany({
            where: {
              deleted_at: null,
              deleted_by: null,
              id: { notIn: tempId },
            },
            skip: 0,
            take: 20,
            select: {
              id: true,
              ...column.reduce(
                (acc, column) => ({ ...acc, [column]: true }),
                {},
              ),
            },
          });
          this.prisma.$disconnect();
          return { result: true, data: [...data, ...data2] };
        }

        return { result: true, data };
      } catch (err) {
        this.prisma.$disconnect();
        catch_response(err);
      }
    } else {
      return { result: false, message: 'One or more columns do not exist.' };
    }
  }

  @Get('drivers')
  async getDrivers() {
    try {
      return await this.prisma.drivers.findMany({
        select: { id: true, name: true },
      });
    } catch (error) {
      catch_response(error);
    }
  }
  @Get('loaders')
  async getLoaders() {
    try {
      return await this.prisma.loaders.findMany({
        select: { id: true, name: true },
      });
    } catch (error) {
      catch_response(error);
    }
  }
  @Get('paymentInvoices')
  async getMixClearanceInvoices(@Query() query: AutoCompleteDto) {
    const { column, modal, invoice_number, per_page, customer, ids } = query;
    let select: any = { id: true };
    const where: any = { deleted_at: null, deleted_by: null };
    const model = this.prisma[modal];
    const orConditions = [];
    const andConditions = [];
    if (invoice_number) {
      if (
        modal != 'invoices' &&
        modal != 'vehicles' &&
        modal != 'mix_shipping_vehicles'
      ) {
        const extractIdFromString = (str) => {
          const match = str.match(/\d+/);
          return match ? parseInt(match[0], 10) : null;
        };
        if (invoice_number.includes('PGL')) {
          const id = extractIdFromString(invoice_number);
          if (id) {
            where['id'] = { equals: id };
          }
        }
      }
      if (modal === 'vehicles') {
        orConditions.push(
          {
            vin: {
              contains: invoice_number,
              mode: 'insensitive',
            },
          },
          {
            lot_number: {
              contains: invoice_number,
              mode: 'insensitive',
            },
          },
        );
      }
      if (modal == 'mix_shipping_vehicles') {
        orConditions.push(
          {
            vehicles: {
              vin: {
                contains: invoice_number,
                mode: 'insensitive',
              },
            },
          },
          {
            vehicles: {
              lot_number: {
                contains: invoice_number,
                mode: 'insensitive',
              },
            },
          },
        );
      }
    }

    if (
      modal != 'mix_shipping_vehicles' &&
      modal != 'invoices' &&
      modal != 'delivery_charge_invoice' &&
      modal != 'vehicles'
    ) {
      if (customer) {
        if (modal == 'clearance_combine_booking_invocies') {
          where['containers'] = {
            company_id: { equals: customer },
          };
        } else {
          where['clear_logs'] = {
            company_id: customer,
          };
          if (invoice_number && !invoice_number.includes('PGL')) {
            orConditions.push({
              clear_logs: {
                OR: [
                  {
                    container_number: {
                      contains: invoice_number,
                      mode: 'insensitive',
                    },
                  },
                  {
                    containers: {
                      container_number: {
                        contains: invoice_number,
                        mode: 'insensitive',
                      },
                    },
                  },
                ],
              },
            });
          }
        }
      }
    }

    if (
      modal == 'mix_shipping_vehicles' ||
      modal == 'delivery_charge_invoice' ||
      modal == 'invoices' ||
      modal == 'vehicles'
    ) {
      if (customer) {
        const company = await this.prisma.companies.findUnique({
          where: { id: customer },
          select: {
            parent_id: true,
            child_company: {
              select: {
                id: true,
              },
            },
          },
        });
        const child_ids =
          company?.child_company?.map((child) => child.id) || [];

        if (company?.parent_id && !child_ids.includes(company.parent_id)) {
          child_ids.push(company.parent_id);
        }

        // Also add current customer if not in list
        if (!child_ids.includes(customer)) {
          child_ids.push(customer);
        }
        if (modal == 'mix_shipping_vehicles') {
          where['mix_shipping_invoices'] = {
            company_id: { in: child_ids },
          };
        } else if (modal == 'invoices') {
          if (invoice_number) {
            andConditions.push({
              invoice_number: {
                contains: invoice_number,
                mode: 'insensitive',
              },
            });
          }
          andConditions.push({
            OR: [{ company_id: { in: child_ids } }],
          });
        } else if (modal == 'vehicles') {
          where['company_id'] = { in: child_ids };
        } else {
          where['company_id'] = { equals: customer };
        }
      }
    }
    const container_number_modals = [
      'invoices',
      'clearance_combine_booking_invocies',
    ];
    if (container_number_modals.includes(modal)) {
      select = {
        id: true,
        // [column]: true,
        containers: {
          select: { container_number: true },
        },
      };
      if (modal === 'invoices') {
        select = {
          ...select,
          [column]: true,
        };
      }
    }

    if (modal == 'vehicles') {
      select = { id: true, vin: true, lot_number: true, price: true };
      where['price'] = { gt: 0 };
    }
    // if (
    //   modal == 'log_invoices' &&
    //   invoice_number &&
    //   !invoice_number.includes('PGL')
    // ) {
    //   select = {
    //     id: true,
    //     clear_logs: {
    //       select: {
    //         container_number: true,
    //         containers: {
    //           select: {
    //             container_number: true,
    //           },
    //         },
    //       },
    //     },
    //   };
    // }
    const container_number_with_log_modals = [
      'log_invoices',
      'exit_claim_charge',
      'single_vcc',
      'detention_charge',
    ];
    if (container_number_with_log_modals.includes(modal)) {
      select = {
        id: true,
        clear_logs: {
          select: {
            container_number: true,
            containers: {
              select: {
                container_number: true,
              },
            },
          },
        },
      };
    }
    if (modal == 'mix_shipping_vehicles') {
      select = {
        id: true,
        vehicles: { select: { id: true, vin: true, lot_number: true } },
      };
      delete where.deleted_at;
      delete where.deleted_by;
    }
    if (ids) {
      andConditions.push({ id: { notIn: ids.split(',').map(Number) } });
    }
    if (orConditions.length > 0) {
      where['OR'] = orConditions;
    }
    if (andConditions.length > 0) {
      where['AND'] = andConditions;
    }
    try {
      let data = await model
        .findMany({
          where,
          skip: 0,
          take: per_page,
          select,
          orderBy: {
            id: 'desc',
          },
        })
        .finally(() => this.prisma.$disconnect());

      data = await formatFetchInvoices(modal, data);
      if (ids) {
        const idArray = ids.split(',').map(Number);
        let prioritizedData = await model
          .findMany({
            where: { id: { in: idArray } },
            skip: 0,
            take: idArray.length,
            select,
            orderBy: {
              id: 'desc',
            },
          })
          .finally(() => this.prisma.$disconnect());

        prioritizedData = await formatFetchInvoices(modal, prioritizedData);
        data = [...prioritizedData, ...data].sort((a, b) => b.id - a.id);
      }
      return { result: true, data };
    } catch (err) {
      catch_response(err);
    }
  }

  @Get('shipmentCompany')
  async shipmentCompany(@Query() query: AutoCompleteDto) {
    const { column, modal, ids, id, per_page, shipementId } = query;
    const name = query[query.column];

    if (modal !== 'companies') {
      return {
        result: false,
        message: 'Invalid modal. Only "companies" is supported.',
      };
    }

    // Base where clause
    const where = {
      deleted_at: null,
      deleted_by: null,
      OR: [
        {
          customers: {
            some: { loginable: { status: user_status.active } },
          },
        },
        {
          customers: {
            none: {},
          },
        },
      ],
    };

    if (name) {
      where[column] = { contains: name, mode: 'insensitive' };
    }

    if (ids) {
      where['id'] = { in: ids.split(',').map(Number) };
    } else if (id) {
      where['id'] = { in: [id] };
    }

    // Handle shipmentId logic
    if (shipementId) {
      const shipment = await this.prisma.containers.findFirst({
        where: { id: +shipementId },
        select: {
          vehicles: {
            select: {
              id: true,
              load_type: true,
              company_id: true,
              is_scrap: true,
            },
          },
          bookings: {
            select: {
              port_of_discharge: true,
            },
          },
        },
      });

      if (shipment && shipment.vehicles.length > 0) {
        const vehicles = shipment.vehicles;
        const portOfDischarge = shipment.bookings?.port_of_discharge;

        const hasScrap = vehicles.some((item) => item.is_scrap === true);
        const allCompanyIds = vehicles.map((v) => v.company_id);

        let additionalCompanyId: number;

        if (portOfDischarge === 24) {
          additionalCompanyId = 577;
        } else if (portOfDischarge === 22) {
          additionalCompanyId = 806;
        } else {
          additionalCompanyId = 158;
        }

        const companyIds = [
          ...new Set([...allCompanyIds, additionalCompanyId]),
        ];
        if (hasScrap) companyIds.push(1062);

        where['id'] = { in: companyIds };
      }
    }

    // Select fields
    const select = {
      id: true,
      [column]: true,
      mix: true,
      complete_halfcut: true,
      complete: true,
      mix_halfcut: true,
      scrap: true,
      destination_id: true,
    };
    // UNITED UAE SCRAP
    try {
      // Fetch companies
      const data = await this.prisma.companies.findMany({
        where,
        skip: 0,
        take: per_page,
        select,
      });

      // Sort data if ID is provided
      if (id) {
        data.sort((a, b) => (a.id === id ? -1 : b.id === id ? 1 : 0));
      }

      // Return the filtered companies
      return { result: true, data };
    } catch (err) {
      catch_response(err);
    }
  }
}
