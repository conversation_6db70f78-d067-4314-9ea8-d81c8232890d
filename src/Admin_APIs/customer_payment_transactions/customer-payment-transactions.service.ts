import { Injectable } from '@nestjs/common';
import { DbService, PrismaService } from 'src/prisma/prisma.service'; // Adjust path as necessary
import {
  approveRevertAllPaymentsDto,
  approveRevertSinglePaymentDto,
  CreateCustomerPaymentTransactionDto,
  customerPaymentTransactionIds,
  fetchInvoiceDto,
  InvoiceStatus,
  paymentAllocationsDto,
  paymentCardsDto,
  paymentDto,
  CustomerCreditsDto,
  GetVinLotDto,
  GetContainerNumberDto,
  CustomerPaymentSearchPaginateDto,
} from './dto/customer-payment-transactions.dto';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { CommonFields } from 'src/Commons/services/common.fields';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import {
  clearance_combine_booking_invoice_status,
  mix_currency_type,
  invoice_status,
  payment_status,
  payment_state_options,
  payment_types,
  payment_methods,
  Prisma,
} from '@prisma/client';
import {
  clearanceInvoiceSelect,
  deliveryInvoiceSelect,
  detentionInvoiceSelect,
  exitClaimInvoiceSelect,
  singleCstPaymentHelper,
  getLogInvoiceAmount,
  getMixVehicleDiscounts,
  getMixVehicleInvoiceAmount,
  getShipmentInvoiceAmount,
  logInvoiceInclude,
  mixVehicleSelect,
  shipmentInvoiceSelect,
  vccInvoiceSelect,
  vehicleSelect,
  customer_payments_transactions_permissions,
  creditPaymentTypes,
  generatePaymentEmailSubject,
  getRelatedDeptsEmailFromAndCc,
  isAuctionVehicleUnpaidToSendEmail,
} from 'src/Commons/helpers/customerPaymentTransactions.helper';
import {
  deleteFromMinio,
  getPreSignedDownloadUrl,
  uploadToMinio,
} from 'src/Commons/services/file-service';
import reactEmail from 'src/Commons/helpers/reactEmail.helper';
import CustomerEmailTemplateForApprovedPayments from '../../../emails/customer-payment/customerEmailTemplateForApprovedPayments';
// import CustomerEmailTemplateForPendingPayments from '../../../emails/customer-payment/customerEmailTemplateForPendingPayments';
import CustomerEmailAttachment from '../../../emails/customer-payment/customerEmailAttachment';
import { render } from '@react-email/components';
import { generatePdf } from '../../Commons/helpers/generatePDF';
import CustomerStatementTemplate from '../../../emails/customer-payment/customerStatementTemplate';
import { check_permissions } from 'src/Commons/helpers/check_permissions.helper';
import AuctionPaymentEmailTemplate from '../../../emails/customer-payment/AuctionPaymentEmailTemplate';
import CreateBankApprovalEmailTemplate from 'emails/customer-payment/CreateBankApproval';
import PaymentRejectionEmailTemplate from 'emails/customer-payment/paymentRejectionTemplate';
import customerVoucherTemplate from 'emails/customer-payment/customerVoucherTemplate';

const create_update_delete_by = CommonFields([
  'created_byToUsers',
  'updated_byToUsers',
  'deleted_byToUsers',
]);

const searchColumns = [
  'remark',
  'transaction_number',
  'many@@payments.shipmentInvoice.invoice_number',
  'many@@payments.shipmentInvoice.containers.container_number',
  'companies.name',
  'many@@payments.shipmentInvoice.containers.bookings.booking_number',
  'many@@payments.shipmentInvoice.containers.bookings.parent.booking_number',
  'many@@payments.vehicle.vin',
  'many@@payments.vehicle.lot_number',
  'many@@payments.mixShippingVehicle.vehicles.vin',
  'many@@payments.mixShippingVehicle.vehicles.lot_number',
  'many@@exitPapers.many@@exit_papers_vehicles.vehicles.vin',
  'many@@exitPapers.many@@exit_papers_vehicles.vehicles.lot_number',
  'many@@customer_transaction_bank_details.reference_number',
  'many@@customer_credits.vehicle.vin',
  'many@@customer_credits.vehicle.lot_number',
  'many@@customer_credits.container.container_number',
];
/**
 * This code defines various search columns for different types of searches in customer payments.
 *
 * The `searchColumns` array is used for general searches and includes fields like transaction number,
 * invoice number, container number, company name, vehicle VIN, and lot number.
 *
 * Specific search columns are defined for different types of invoices and charges:
 * - `logInvoiceSearchColumns`: For log invoice searches.
 * - `singleVCCSearchColumns`: For single VCC searches.
 * - `exitClaimSearchColumns`: For exit claim charge searches.
 * - `detentionSearchColumns`: For detention charge searches.
 * - `clearanceInvoiceSearchColumns`: For clearance invoice searches.
 * - `deliveryChargeInvoiceSearchColumns`: For delivery charge invoice searches.
 * - `mixShippingsearchColumns`: For mix shipping invoice searches.
 *
 * The `extractIdFromString` function extracts the first number found in a string and returns it as an integer.
 *
 * The `getSearchColumnAndSearch` function determines the appropriate search columns based on the search string.
 * It checks for specific prefixes (e.g., 'PGLC', 'PGLMS') and assigns the corresponding search columns.
 * If no specific prefix is found, it defaults to the general `searchColumns`.
 */
const logInvoiceSearchColumns = ['many@@payments.logInvoice.int@@id'];
const singleVCCSearchColumns = ['many@@payments.singleVcc.int@@id'];
const exitClaimSearchColumns = ['many@@payments.exitClaimCharge.int@@id'];
const detentionSearchColumns = ['many@@payments.detentionCharge.int@@id'];
const clearanceInvoiceSearchColumns = [
  'many@@payments.detentionCharge.int@@id',
];
const deliveryChargeInvoiceSearchColumns = [
  'many@@payments.deliveryChargeInvoice.int@@id',
];
const mixShippingsearchColumns = [
  'many@@payments.mixShippingVehicle.mix_shipping_invoices.int@@id',
];
const excludedValues = [
  'mukhasa',
  'buyer_fee_discount',
  'damage_credit',
  'demurrage_credit',
  'storage_credit',
  'exit_paper_credit',
  'online_payment',
  'refund_of_suspended',
  'extra_payment_copart_iaa',
  'loading_amendment_credit',
  'missing_loading_credit',
  'customer_purchased_vehicle_removed',
  'clearance_credit',
  'shortage_section',
  'other_credit',
  'delivery_order_credit',
  'commission',
  'discount_credit',
  'sale_tax_credit',
  'port_storage_and_demurrage_credit',
  'mailing_fee_credit',
  'relist_fee_credit',
];

const extractIdFromString = (str) => {
  const match = str.match(/\d+/);
  return match ? parseInt(match[0], 10) : null;
};

const getSearchColumnAndSearch = (search, searchOptions) => {
  if (searchOptions?.length) {
    return { columns: searchOptions, search };
  }
  const extractedId = `${extractIdFromString(search)}`;
  if (search.includes('PGLC')) {
    return {
      columns: [...logInvoiceSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLMS')) {
    return {
      columns: [...mixShippingsearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLV')) {
    return {
      columns: [...singleVCCSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLE')) {
    return {
      columns: [...exitClaimSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLD') && !search.includes('PGLDMG')) {
    return {
      columns: [...detentionSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLDO')) {
    return {
      columns: [...deliveryChargeInvoiceSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else if (search.includes('PGLCB')) {
    return {
      columns: [...clearanceInvoiceSearchColumns, ...searchColumns],
      search: extractedId,
    };
  } else {
    return { columns: searchColumns, search };
  }
};

const getFileNameFromUrl = (url: string) => {
  return url.split('/').pop();
};

const bankApprovalRequiredMethods = [
  'sales_tax',
  'wire',
  'check',
  'zelle',
  'online_payment',
  'delivery_order',
  'kabul_cash',
  'hawala',
];

@Injectable()
export class CustomerPaymentTransactionService {
  private prisma: typeof DbService.prisma;

  constructor(
    private p: PrismaService,
    private readonly db: DbService,
  ) {
    this.prisma = DbService.prisma;
  }

  private async checkDuplicateReferenceNumbers(
    referenceNumbers: string[],
    excludeTransactionId?: number,
  ) {
    if (!referenceNumbers.length) return [];

    const whereClause: any = {
      reference_number: { in: referenceNumbers },
      deleted_at: null,
    };
    if (excludeTransactionId) {
      whereClause.customer_tran_id = { not: excludeTransactionId };
    }

    const existingRefs =
      await this.prisma.customer_transaction_bank_details.findMany({
        where: whereClause,
        select: { reference_number: true },
      });

    const existingRefNumbers = existingRefs.map((ref) => ref.reference_number);
    const duplicateInSubmission = referenceNumbers.filter((item, index) => {
      return referenceNumbers.indexOf(item) !== index;
    });

    const duplicates = Array.from(
      new Set([...existingRefNumbers, ...duplicateInSubmission]),
    );
    return duplicates;
  }

  async create(
    attachments: Array<Express.Multer.File>,
    dto: CreateCustomerPaymentTransactionDto,
    created_by: number,
  ) {
    const txn_attachments = attachments.filter((op) =>
      op.fieldname.includes('[transaction]'),
    );
    const bank_attachments = attachments.filter(
      (op) => !op.fieldname.includes('[transaction]'),
    );
    try {
      const payments: any =
        await this.setPaymentObjectAndRemainingAmountFlag(dto);
      const payment_cards: any = Object.values(
        Object.assign({}, dto.payment_cards),
      );
      const pmd = dto.payment_method.toLowerCase() as payment_methods;
      const handleTransactionResult =
        await this.handleTransactionForCreateServices(pmd, dto);

      if (handleTransactionResult.status !== 200) {
        return handleTransactionResult;
      }

      const bank_details = dto.customer_transaction_bank_details ?? [];

      const referenceNumbers = bank_details
        .map((item: any) => item.reference_number?.trim())
        .filter(Boolean); // remove null/undefined/empty strings

      const duplicateRefs =
        await this.checkDuplicateReferenceNumbers(referenceNumbers);

      if (duplicateRefs.length) {
        return {
          status: 400,
          result: false,
          message: `Duplicate reference numbers detected: ${duplicateRefs.join(', ')}`,
        };
      }

      const createdPayment =
        await this.prisma.customer_payment_transactions.create({
          data: {
            payment_method: dto.payment_method,
            amount: dto.amount,
            bank_id: dto.bank_id,
            transaction_number: dto.transaction_number,
            transaction_fee: dto.transaction_fee,
            inapplicable_amount: dto.inapplicable_amount,
            remark: dto.remark,
            link: dto.link,
            payment_date: dto.payment_date,
            amount_applied:
              this.recalculate_amount_applied(payments, dto.exchange_rate) ??
              dto.amount_applied,
            currency: dto.currency,
            exchange_rate: dto.exchange_rate,
            customer_id: dto.customer_id,
            company_id: dto.company_id,
            state: dto.state,
            attachments: {
              create: await this.uploadAttachmentsAndPrepare(
                txn_attachments,
                dto.deposit_date,
              ),
            },
            // payment_cards: dto.payment_cards
            //   ? JSON.stringify(dto.payment_cards)
            //   : null,
            payment_cards: {
              create: payment_cards.map((payment: paymentCardsDto) => ({
                type: payment.type,
                limit: +payment.limit,
                short_amount: +payment.short_amount,
                exchange_rate: +payment.exchange_rate,
                created_at: new Date(),
                created_by,
              })),
            },
            customer_transaction_bank_details: {
              create: await Promise.all(
                bank_details.map(async (detail: any) => {
                  let attachment = null;
                  if (bank_attachments.length) {
                    attachment = await this.uploadCommentAttachment(
                      bank_attachments.find(
                        (op) =>
                          op.fieldname ===
                          `attachment[${detail.reference_number}][0]`,
                      ),
                    );
                  }
                  return {
                    title: detail.title,
                    reference_number: detail.reference_number,
                    remark: detail.remark,
                    deposit_date: detail.deposit_date,
                    attachment,
                    attachment_name: attachment
                      ? getFileNameFromUrl(attachment)
                      : null,
                    created_by,
                  };
                }),
              ),
            },
            payments: {
              create: payments.map((payment: paymentDto) => ({
                amount_applied: parseFloat(String(payment.amount_applied)),
                transaction_number: payment.transaction_number,
                type: payment.type,
                auction_category_id: +payment.auction_category_id,
                payment_date: payment.payment_date,
                exchange_rate: +payment.exchange_rate,
                payment_remark: payment.payment_remark,
                mix_shipping_vehicle_id: +payment.mix_shipping_vehicle_id,
                shipment_invoice_id: +payment.shipment_invoice_id,
                clearance_invoice_id: +payment.clearance_invoice_id,
                log_invoice_id: +payment.log_invoice_id,
                single_vcc_id: +payment.single_vcc_id,
                exit_claim_charge_id: +payment.exit_claim_charge_id,
                detention_charge_id: +payment.detention_charge_id,
                delivery_charge_invoice_id: +payment.delivery_charge_invoice_id,
                vehicle_id: +payment.vehicle_id,
                show_remaining_amount: payment.show_remaining_amount,
                created_at: new Date(),
                created_by,
                payment_allocations: {
                  create: Object.values(
                    Object.assign({}, payment.payment_allocations),
                  ).map((m: paymentAllocationsDto) => ({
                    amount: m.amount,
                    type: m.type,
                    created_at: new Date(),
                    created_by,
                  })),
                },
              })),
            },
            created_at: new Date(),
            created_by,
          },
          include: {
            companies: { select: { name: true } },
            customer: { select: { fullname: true } },
            bank_accounts: {
              select: {
                id: true,
                bank_name: true,
                account_name: true,
                account_number: true,
                ibn: true,
                bic_code: true,
                currency: true,
                country: true,
              },
            },
            payments: {
              select: {
                id: true,
                amount_applied: true,
                type: true,
                state: true,
                exchange_rate: true,
                payment_remark: true,
                payment_date: true,
                auction_category_id: true,
                transaction_number: true,

                // Include all potential conditional fields
                shipment_invoice_id: true,
                mix_shipping_vehicle_id: true,
                clearance_invoice_id: true,
                single_vcc_id: true,
                log_invoice_id: true,
                delivery_charge_invoice_id: true,
                exit_claim_charge_id: true,
                detention_charge_id: true,
                vehicle_id: true,
              },
            },
            payment_cards: {
              where: { deleted_at: null },
              select: {
                id: true,
                limit: true,
                short_amount: true,
                type: true,
                exchange_rate: true,
              },
            },
            customer_transaction_bank_details: {
              where: { deleted_at: null },
              select: {
                id: true,
                title: true,
                reference_number: true,
                remark: true,
                deposit_date: true,
                attachment: true,
                attachment_name: true,
              },
            },
            ...create_update_delete_by,
          },
        });

      const hasAuctionPayment =
        payments.filter((payment) => payment.type === 'auction').length > 0;

      if (
        hasAuctionPayment &&
        (dto.payment_method === 'cash' ||
          dto.payment_method === 'buyer_fee_discount' ||
          dto.payment_method === 'wire' ||
          dto.payment_method === 'check')
      ) {
        // const auctionPaymentRecord = (await this.findOne(createdPayment?.id))
        //   .data;
        /* await reactEmail(
          '<EMAIL>',
          `Auction Payment Notification | ${dto.payment_method}`,
          auctionPaymentRecord,
          AuctionPaymentEmailTemplate,
          null,
          {
            from: process.env.MAILGUN_FROM,
            cc: ['<EMAIL>', '<EMAIL>'],
          },
        );*/
      }

      // const { email } = await this.prisma.loginables.findUnique({
      //   where: { customer_id: dto?.customer_id },
      //   select: { email: true },
      // });
      //
      // await reactEmail(
      //   email,
      //   'Payment Notification',
      //   dto,
      //   CustomerEmailTemplateForPendingPayments,
      // );

      if (bankApprovalRequiredMethods.includes(dto.payment_method)) {
        const { email } = await this.prisma.loginables.findUnique({
          where: { customer_id: dto?.customer_id },
          select: { email: true },
        });

        await reactEmail(
          email,
          generatePaymentEmailSubject(createdPayment),
          {
            ...createdPayment,
            action: 'creation',
          },
          CreateBankApprovalEmailTemplate,
          null,
          {
            from: '<EMAIL>',
            cc: ['<EMAIL>'],
          },
        );
      }

      return { result: true, data: createdPayment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async createAttachmentRecord(dto: any) {
    try {
      const createdAttachment =
        await this.prisma.customer_payment_attachment.create({
          data: {
            customer_payment_transactionId: +dto.customer_payment_transactionId,
            name: dto.name,
            url: dto.url,
            deposit_date: dto.deposit_date,
          },
        });

      return { result: true, data: createdAttachment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async findAll(
    query: CustomerPaymentSearchPaginateDto,
    state: string,
    paymentType: string,
    userId: any,
    isCredit: boolean = false,
  ) {
    const offset = query.page;
    const limit = query.per_page;
    const transactionNumberFlagSearch = query.transactionNumber;
    const searchOptions =
      query.searchOptions && JSON.parse(query.searchOptions.replace(/'/g, '"'));

    const columnsAndSearch = getSearchColumnAndSearch(
      query.search,
      searchOptions,
    );
    const searchBy = columnsAndSearch.columns;
    query.search = columnsAndSearch.search;
    let where = await searchFilter(
      query,
      query.search.includes('PGLPN') || transactionNumberFlagSearch
        ? []
        : searchBy,
      false,
      undefined,
      'payments',
      state,
    );

    const orderBy: any[] = [{ id: 'desc' }];
    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = +limit;
    }
    const paymentCondition = isCredit
      ? `payment_method IN (${creditPaymentTypes.map((p) => `'${p}'`).join(', ')}) AND`
      : `payment_method NOT IN (${creditPaymentTypes.map((p) => `'${p}'`).join(', ')}) AND`;
    if (state === 'remaining-summary') {
      const companyQuerySearch = query.search
        ? `AND c.name LIKE '%${query.search}%'`
        : '';
      const totalRecords = await this.prisma.$queryRawUnsafe<
        [{ count: number }]
      >(`SELECT COUNT(DISTINCT c.id) AS count
          FROM customer_payment_transactions t
          JOIN companies c ON c.id = t.company_id
          WHERE ${paymentCondition}
            t.amount > 0
            AND (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) > 0.009
            AND t.deleted_at IS NULL ${companyQuerySearch}
        `);
      const remainingPaymentsSummary = await this.prisma.$queryRawUnsafe<
        {
          company_id: number;
          company_name: string;

          total_amount_aed: number;
          total_amount_applied_aed: number;
          total_transaction_fee_aed: number;
          total_inapplicable_amount_aed: number;
          remaining_balance_aed: number;

          total_amount_usd: number;
          total_amount_applied_usd: number;
          total_transaction_fee_usd: number;
          total_inapplicable_amount_usd: number;
          remaining_balance_usd: number;

          total_amount_omr: number;
          total_amount_applied_omr: number;
          total_transaction_fee_omr: number;
          total_inapplicable_amount_omr: number;
          remaining_balance_omr: number;

          total_amount_gel: number;
          total_amount_applied_gel: number;
          total_transaction_fee_gel: number;
          total_inapplicable_amount_gel: number;
          remaining_balance_gel: number;

          pending_total_amount_aed: number;
          pending_total_amount_applied_aed: number;
          pending_total_transaction_fee_aed: number;
          pending_total_inapplicable_amount_aed: number;
          pending_remaining_balance_aed: number;

          pending_total_amount_usd: number;
          pending_total_amount_applied_usd: number;
          pending_total_transaction_fee_usd: number;
          pending_total_inapplicable_amount_usd: number;
          pending_remaining_balance_usd: number;

          pending_total_amount_omr: number;
          pending_total_amount_applied_omr: number;
          pending_total_transaction_fee_omr: number;
          pending_total_inapplicable_amount_omr: number;
          pending_remaining_balance_omr: number;

          pending_total_amount_gel: number;
          pending_total_amount_applied_gel: number;
          pending_total_transaction_fee_gel: number;
          pending_total_inapplicable_amount_gel: number;
          pending_remaining_balance_gel: number;

          reviewed_total_amount_aed: number;
          reviewed_total_amount_applied_aed: number;
          reviewed_total_transaction_fee_aed: number;
          reviewed_total_inapplicable_amount_aed: number;
          reviewed_remaining_balance_aed: number;

          reviewed_total_amount_usd: number;
          reviewed_total_amount_applied_usd: number;
          reviewed_total_transaction_fee_usd: number;
          reviewed_total_inapplicable_amount_usd: number;
          reviewed_remaining_balance_usd: number;

          reviewed_total_amount_omr: number;
          reviewed_total_amount_applied_omr: number;
          reviewed_total_transaction_fee_omr: number;
          reviewed_total_inapplicable_amount_omr: number;
          reviewed_remaining_balance_omr: number;

          reviewed_total_amount_gel: number;
          reviewed_total_amount_applied_gel: number;
          reviewed_total_transaction_fee_gel: number;
          reviewed_total_inapplicable_amount_gel: number;
          reviewed_remaining_balance_gel: number;

          final_reviewed_total_amount_aed: number;
          final_reviewed_total_amount_applied_aed: number;
          final_reviewed_total_transaction_fee_aed: number;
          final_reviewed_total_inapplicable_amount_aed: number;
          final_reviewed_remaining_balance_aed: number;

          final_reviewed_total_amount_usd: number;
          final_reviewed_total_amount_applied_usd: number;
          final_reviewed_total_transaction_fee_usd: number;
          final_reviewed_total_inapplicable_amount_usd: number;
          final_reviewed_remaining_balance_usd: number;

          final_reviewed_total_amount_omr: number;
          final_reviewed_total_amount_applied_omr: number;
          final_reviewed_total_transaction_fee_omr: number;
          final_reviewed_total_inapplicable_amount_omr: number;
          final_reviewed_remaining_balance_omr: number;

          final_reviewed_total_amount_gel: number;
          final_reviewed_total_amount_applied_gel: number;
          final_reviewed_total_transaction_fee_gel: number;
          final_reviewed_total_inapplicable_amount_gel: number;
          final_reviewed_remaining_balance_gel: number;

          approved_total_amount_aed: number;
          approved_total_amount_applied_aed: number;
          approved_total_transaction_fee_aed: number;
          approved_total_inapplicable_amount_aed: number;
          approved_remaining_balance_aed: number;

          approved_total_amount_usd: number;
          approved_total_amount_applied_usd: number;
          approved_total_transaction_fee_usd: number;
          approved_total_inapplicable_amount_usd: number;
          approved_remaining_balance_usd: number;

          approved_total_amount_omr: number;
          approved_total_amount_applied_omr: number;
          approved_total_transaction_fee_omr: number;
          approved_total_inapplicable_amount_omr: number;
          approved_remaining_balance_omr: number;

          approved_total_amount_gel: number;
          approved_total_amount_applied_gel: number;
          approved_total_transaction_fee_gel: number;
          approved_total_inapplicable_amount_gel: number;
          approved_remaining_balance_gel: number;
        }[]
      >(`
        SELECT
          c.id AS company_id,
          c.name AS company_name,

          -- === TOTALS PER CURRENCY ===
          SUM(CASE WHEN t.currency = 'AED' THEN t.amount ELSE 0 END) AS total_amount_aed,
          SUM(CASE WHEN t.currency = 'AED' THEN t.amount_applied ELSE 0 END) AS total_amount_applied_aed,
          SUM(CASE WHEN t.currency = 'AED' THEN t.transaction_fee ELSE 0 END) AS total_transaction_fee_aed,
          SUM(CASE WHEN t.currency = 'AED' THEN t.inapplicable_amount ELSE 0 END) AS total_inapplicable_amount_aed,
          SUM(CASE WHEN t.currency = 'AED' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS remaining_balance_aed,

          SUM(CASE WHEN t.currency = '$' THEN t.amount ELSE 0 END) AS total_amount_usd,
          SUM(CASE WHEN t.currency = '$' THEN t.amount_applied ELSE 0 END) AS total_amount_applied_usd,
          SUM(CASE WHEN t.currency = '$' THEN t.transaction_fee ELSE 0 END) AS total_transaction_fee_usd,
          SUM(CASE WHEN t.currency = '$' THEN t.inapplicable_amount ELSE 0 END) AS total_inapplicable_amount_usd,
          SUM(CASE WHEN t.currency = '$' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS remaining_balance_usd,

          SUM(CASE WHEN t.currency = 'OMR' THEN t.amount ELSE 0 END) AS total_amount_omr,
          SUM(CASE WHEN t.currency = 'OMR' THEN t.amount_applied ELSE 0 END) AS total_amount_applied_omr,
          SUM(CASE WHEN t.currency = 'OMR' THEN t.transaction_fee ELSE 0 END) AS total_transaction_fee_omr,
          SUM(CASE WHEN t.currency = 'OMR' THEN t.inapplicable_amount ELSE 0 END) AS total_inapplicable_amount_omr,
          SUM(CASE WHEN t.currency = 'OMR' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS remaining_balance_omr,

          SUM(CASE WHEN t.currency = 'GEL' THEN t.amount ELSE 0 END) AS total_amount_gel,
          SUM(CASE WHEN t.currency = 'GEL' THEN t.amount_applied ELSE 0 END) AS total_amount_applied_gel,
          SUM(CASE WHEN t.currency = 'GEL' THEN t.transaction_fee ELSE 0 END) AS total_transaction_fee_gel,
          SUM(CASE WHEN t.currency = 'GEL' THEN t.inapplicable_amount ELSE 0 END) AS total_inapplicable_amount_gel,
          SUM(CASE WHEN t.currency = 'GEL' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS remaining_balance_gel,

          -- === PENDING ===
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'AED' THEN t.amount ELSE 0 END) AS pending_total_amount_aed,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'AED' THEN t.amount_applied ELSE 0 END) AS pending_total_amount_applied_aed,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'AED' THEN t.transaction_fee ELSE 0 END) AS pending_total_transaction_fee_aed,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'AED' THEN t.inapplicable_amount ELSE 0 END) AS pending_total_inapplicable_amount_aed,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'AED' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS pending_remaining_balance_aed,

          SUM(CASE WHEN t.state = 'pending' AND t.currency = '$' THEN t.amount ELSE 0 END) AS pending_total_amount_usd,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = '$' THEN t.amount_applied ELSE 0 END) AS pending_total_amount_applied_usd,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = '$' THEN t.transaction_fee ELSE 0 END) AS pending_total_transaction_fee_usd,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = '$' THEN t.inapplicable_amount ELSE 0 END) AS pending_total_inapplicable_amount_usd,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = '$' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS pending_remaining_balance_usd,

          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'OMR' THEN t.amount ELSE 0 END) AS pending_total_amount_omr,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'OMR' THEN t.amount_applied ELSE 0 END) AS pending_total_amount_applied_omr,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'OMR' THEN t.transaction_fee ELSE 0 END) AS pending_total_transaction_fee_omr,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'OMR' THEN t.inapplicable_amount ELSE 0 END) AS pending_total_inapplicable_amount_omr,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'OMR' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS pending_remaining_balance_omr,

          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'GEL' THEN t.amount ELSE 0 END) AS pending_total_amount_gel,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'GEL' THEN t.amount_applied ELSE 0 END) AS pending_total_amount_applied_gel,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'GEL' THEN t.transaction_fee ELSE 0 END) AS pending_total_transaction_fee_gel,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'GEL' THEN t.inapplicable_amount ELSE 0 END) AS pending_total_inapplicable_amount_gel,
          SUM(CASE WHEN t.state = 'pending' AND t.currency = 'GEL' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS pending_remaining_balance_gel,

            -- === REVIEWED ===
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'AED' THEN t.amount ELSE 0 END) AS reviewed_total_amount_aed,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'AED' THEN t.amount_applied ELSE 0 END) AS reviewed_total_amount_applied_aed,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'AED' THEN t.transaction_fee ELSE 0 END) AS reviewed_total_transaction_fee_aed,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'AED' THEN t.inapplicable_amount ELSE 0 END) AS reviewed_total_inapplicable_amount_aed,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'AED' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS reviewed_remaining_balance_aed,

          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = '$' THEN t.amount ELSE 0 END) AS reviewed_total_amount_usd,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = '$' THEN t.amount_applied ELSE 0 END) AS reviewed_total_amount_applied_usd,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = '$' THEN t.transaction_fee ELSE 0 END) AS reviewed_total_transaction_fee_usd,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = '$' THEN t.inapplicable_amount ELSE 0 END) AS reviewed_total_inapplicable_amount_usd,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = '$' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS reviewed_remaining_balance_usd,

          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'OMR' THEN t.amount ELSE 0 END) AS reviewed_total_amount_omr,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'OMR' THEN t.amount_applied ELSE 0 END) AS reviewed_total_amount_applied_omr,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'OMR' THEN t.transaction_fee ELSE 0 END) AS reviewed_total_transaction_fee_omr,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'OMR' THEN t.inapplicable_amount ELSE 0 END) AS reviewed_total_inapplicable_amount_omr,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'OMR' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS reviewed_remaining_balance_omr,

          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'GEL' THEN t.amount ELSE 0 END) AS reviewed_total_amount_gel,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'GEL' THEN t.amount_applied ELSE 0 END) AS reviewed_total_amount_applied_gel,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'GEL' THEN t.transaction_fee ELSE 0 END) AS reviewed_total_transaction_fee_gel,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'GEL' THEN t.inapplicable_amount ELSE 0 END) AS reviewed_total_inapplicable_amount_gel,
          SUM(CASE WHEN t.state = 'reviewed' AND t.currency = 'GEL' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS reviewed_remaining_balance_gel,

              -- === FINAL_REVIEWED ===
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'AED' THEN t.amount ELSE 0 END) AS final_reviewed_total_amount_aed,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'AED' THEN t.amount_applied ELSE 0 END) AS final_reviewed_total_amount_applied_aed,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'AED' THEN t.transaction_fee ELSE 0 END) AS final_reviewed_total_transaction_fee_aed,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'AED' THEN t.inapplicable_amount ELSE 0 END) AS final_reviewed_total_inapplicable_amount_aed,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'AED' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS final_reviewed_remaining_balance_aed,

          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = '$' THEN t.amount ELSE 0 END) AS final_reviewed_total_amount_usd,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = '$' THEN t.amount_applied ELSE 0 END) AS final_reviewed_total_amount_applied_usd,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = '$' THEN t.transaction_fee ELSE 0 END) AS final_reviewed_total_transaction_fee_usd,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = '$' THEN t.inapplicable_amount ELSE 0 END) AS final_reviewed_total_inapplicable_amount_usd,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = '$' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS final_reviewed_remaining_balance_usd,

          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'OMR' THEN t.amount ELSE 0 END) AS final_reviewed_total_amount_omr,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'OMR' THEN t.amount_applied ELSE 0 END) AS final_reviewed_total_amount_applied_omr,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'OMR' THEN t.transaction_fee ELSE 0 END) AS final_reviewed_total_transaction_fee_omr,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'OMR' THEN t.inapplicable_amount ELSE 0 END) AS final_reviewed_total_inapplicable_amount_omr,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'OMR' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS final_reviewed_remaining_balance_omr,

          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'GEL' THEN t.amount ELSE 0 END) AS final_reviewed_total_amount_gel,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'GEL' THEN t.amount_applied ELSE 0 END) AS final_reviewed_total_amount_applied_gel,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'GEL' THEN t.transaction_fee ELSE 0 END) AS final_reviewed_total_transaction_fee_gel,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'GEL' THEN t.inapplicable_amount ELSE 0 END) AS final_reviewed_total_inapplicable_amount_gel,
          SUM(CASE WHEN t.state = 'final_reviewed' AND t.currency = 'GEL' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS final_reviewed_remaining_balance_gel,


            -- === APPROVED ===
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'AED' THEN t.amount ELSE 0 END) AS approved_total_amount_aed,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'AED' THEN t.amount_applied ELSE 0 END) AS approved_total_amount_applied_aed,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'AED' THEN t.transaction_fee ELSE 0 END) AS approved_total_transaction_fee_aed,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'AED' THEN t.inapplicable_amount ELSE 0 END) AS approved_total_inapplicable_amount_aed,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'AED' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS approved_remaining_balance_aed,

          SUM(CASE WHEN t.state = 'approved' AND t.currency = '$' THEN t.amount ELSE 0 END) AS approved_total_amount_usd,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = '$' THEN t.amount_applied ELSE 0 END) AS approved_total_amount_applied_usd,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = '$' THEN t.transaction_fee ELSE 0 END) AS approved_total_transaction_fee_usd,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = '$' THEN t.inapplicable_amount ELSE 0 END) AS approved_total_inapplicable_amount_usd,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = '$' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS approved_remaining_balance_usd,

          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'OMR' THEN t.amount ELSE 0 END) AS approved_total_amount_omr,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'OMR' THEN t.amount_applied ELSE 0 END) AS approved_total_amount_applied_omr,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'OMR' THEN t.transaction_fee ELSE 0 END) AS approved_total_transaction_fee_omr,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'OMR' THEN t.inapplicable_amount ELSE 0 END) AS approved_total_inapplicable_amount_omr,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'OMR' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS approved_remaining_balance_omr,

          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'GEL' THEN t.amount ELSE 0 END) AS approved_total_amount_gel,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'GEL' THEN t.amount_applied ELSE 0 END) AS approved_total_amount_applied_gel,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'GEL' THEN t.transaction_fee ELSE 0 END) AS approved_total_transaction_fee_gel,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'GEL' THEN t.inapplicable_amount ELSE 0 END) AS approved_total_inapplicable_amount_gel,
          SUM(CASE WHEN t.state = 'approved' AND t.currency = 'GEL' THEN (t.amount - t.amount_applied - t.transaction_fee - t.inapplicable_amount) ELSE 0 END) AS approved_remaining_balance_gel


        FROM customer_payment_transactions t
        JOIN companies c ON c.id = t.company_id
        WHERE ${paymentCondition}
          t.amount > 0
          AND (t.amount - t.amount_applied - t.transaction_fee) > 0.009
          AND t.deleted_at IS NULL ${companyQuerySearch}
        GROUP BY c.id, c.name
        LIMIT ${limit} OFFSET ${(offset - 1) * limit}
      `);
      function convertBigInt(value: any) {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }
      return {
        data: remainingPaymentsSummary,
        total: convertBigInt(totalRecords[0]?.count),
      };
    }
    if (
      state !== 'all' &&
      state !== '' &&
      state !== 'trash' &&
      state !== 'remaining'
    )
      where['state'] = state;
    if (state === 'trash') {
      where = {
        ...where,
        deleted_at: { not: null },
        deleted_by: { not: null },
      };
    }

    where['before_march_15_check'] = false;
    if (state === 'before_march_15') {
      where['before_march_15_check'] = true;
      where['state'] = payment_state_options.pending;
    }

    if (state === 'all') delete where['before_march_15_check'];

    if (query.search.includes('PGLPN')) {
      const id = extractIdFromString(query.search);
      if (id) where['id'] = id;
    }

    if (transactionNumberFlagSearch && !query.search.includes('PGLPN')) {
      where['transaction_number'] = {
        contains: query.search,
        mode: 'insensitive',
      };
    }

    const allowedPaymentTypes = [];
    const hasOnlyViewAllPayments = await check_permissions(
      userId,
      'only_view_all_payments',
    );
    const hasApproveCustomerPayments = await check_permissions(
      userId,
      'approve_customer_payments',
    );
    for (const {
      type,
      permission,
    } of customer_payments_transactions_permissions) {
      const hasPermission = await check_permissions(userId, permission);
      if (
        hasPermission ||
        hasOnlyViewAllPayments ||
        hasApproveCustomerPayments
      ) {
        allowedPaymentTypes.push(type);
      }
    }
    const isRemaining = state === 'remaining' && !where['id'];
    if (isRemaining) {
      const typeJoins =
        allowedPaymentTypes.length > 0
          ? `LEFT JOIN payments p ON p.customer_tran_id = cpt.id AND p.deleted_at IS NULL
            LEFT JOIN payment_cards pc ON pc.customer_tran_id = cpt.id AND pc.deleted_at IS NULL`
          : '';
      const filteredTypes =
        paymentType && allowedPaymentTypes.includes(paymentType)
          ? [paymentType]
          : allowedPaymentTypes;

      const sqlInList = filteredTypes.map((t) => `'${t}'`).join(', ');

      const typeCondition =
        filteredTypes.length > 0
          ? `AND (
        (p.type IN (${sqlInList}))
        OR
        (pc.type IN (${sqlInList}))
      )`
          : '';
      const emptyPaymentCondition =
        !paymentType && allowedPaymentTypes.length === 0
          ? `AND (
        NOT EXISTS (
          SELECT 1 FROM payments p WHERE p.customer_tran_id = cpt.id AND p.deleted_at IS NULL
        ) AND
        NOT EXISTS (
          SELECT 1 FROM payment_cards pc WHERE pc.customer_tran_id = cpt.id AND pc.deleted_at IS NULL
        )
     )`
          : '';
      const rawQuery = `SELECT DISTINCT cpt.id
          FROM customer_payment_transactions cpt
          ${typeJoins}
          WHERE ${paymentCondition}
            cpt.amount > 0
            AND (cpt.amount - cpt.amount_applied - cpt.transaction_fee - cpt.inapplicable_amount) > 0.009
            AND cpt.deleted_at IS NULL AND cpt.before_march_15_check = false
            ${typeCondition}
            ${emptyPaymentCondition}
            `;
      const remainingPayments =
        await this.prisma.$queryRawUnsafe<{ id: number }[]>(rawQuery);
      const remainingIds = remainingPayments.map((payment) => payment.id);
      where['id'] = { in: remainingIds };
    }

    if (!isRemaining && !transactionNumberFlagSearch) {
      const paymentTypeFilters = [];

      paymentTypeFilters.push(
        {
          payments: {
            some: {
              type: {
                in:
                  paymentType && allowedPaymentTypes.includes(paymentType)
                    ? [paymentType]
                    : allowedPaymentTypes,
              },
              deleted_at: null,
            },
          },
        },
        {
          payment_cards: {
            some: {
              type: {
                in:
                  paymentType && allowedPaymentTypes.includes(paymentType)
                    ? [paymentType]
                    : allowedPaymentTypes,
              },
              deleted_at: null,
            },
          },
        },

        !paymentType && {
          AND: [
            {
              payments: {
                none: {
                  deleted_at: null,
                },
              },
            },
            {
              payment_cards: {
                none: {
                  deleted_at: null,
                },
              },
            },
          ],
        },
      );

      where['AND'] = [...(where['AND'] || []), { OR: paymentTypeFilters }];
    }

    // delete where['OR'];
    // if (isCredit) {
    //   if (where.AND) {
    //     where['AND'].push({ payment_method: { in: creditPaymentTypes } });
    //   } else {
    //     where['AND'] = [{ payment_method: { in: creditPaymentTypes } }];
    //   }
    // } else {
    //   where['AND'].push({
    //     payment_method: { not: { in: creditPaymentTypes } },
    //   });
    // }

    // Ensure where.AND exists
    where['AND'] = where['AND'] || [];

    if (!isRemaining) {
      if (isCredit) {
        where['AND'].push({ payment_method: { in: creditPaymentTypes } });
      } else {
        where['AND'].push({
          payment_method: { not: { in: creditPaymentTypes } },
        });
      }
    }

    where.AND = where.AND.map((andCond) => {
      // if no OR, return as is
      if (!andCond.OR) return andCond;

      // filter OR items to exclude any 'in' array containing 'non_allocated'
      const filteredOR = andCond.OR.filter((orItem) => {
        if (!orItem) return false;

        if (orItem.payments?.some?.type?.in?.includes('non_allocated')) {
          return false;
        }
        if (orItem.payment_cards?.some?.type?.in?.includes('non_allocated')) {
          return false;
        }
        return true; // keep others
      });

      // If after filtering, OR is empty, remove the whole AND condition by returning null
      if (filteredOR.length === 0) return null;

      // otherwise, return with updated OR
      return { ...andCond, OR: filteredOR };
    }).filter(Boolean);

    return await Promise.all([
      this.prisma.customer_payment_transactions.count({ where }),
      this.prisma.customer_payment_transactions.findMany({
        ...paginate,
        where,
        orderBy,
        include: {
          companies: { select: { name: true } },
          customer: { select: { fullname: true } },
          attachments: true,
          transactions: {
            select: {
              id: true,
              category: {
                select: { name: true },
              },
            },
          },
          bank_accounts: {
            select: {
              id: true,
              bank_name: true,
              account_name: true,
              account_number: true,
              ibn: true,
              bic_code: true,
              currency: true,
              country: true,
            },
          },
          customer_transaction_bank_details: {
            where: { deleted_at: null },
          },
          payments: {
            select: {
              id: true,
              amount_applied: true,
              type: true,
              state: true,
              exchange_rate: true,
              payment_remark: true,
              payment_date: true,
              auction_category_id: true,
            },
            where: { deleted_at: null },
          },
          payment_cards: {
            where: { deleted_at: null },
            select: {
              id: true,
              limit: true,
              short_amount: true,
              type: true,
              exchange_rate: true,
            },
          },
          exitPapers: {
            select: {
              id: true,
            },
            where: {
              deleted_at: null,
            },
          },
          ...create_update_delete_by,
        },
      }),
    ])
      .then(async ([total, data]) => {
        if (
          (state === payment_state_options.pending ||
            state === payment_state_options.reviewed ||
            state === payment_state_options.final_reviewed) &&
          !query.search &&
          Object.keys(query.filterData).length !== 0
        ) {
          const w: any = {
            deleted_at: null,
            state: { not: state },
          };
          w.AND = [
            {
              payments: {
                some: {
                  state,
                  deleted_at: null,
                  type: {
                    in:
                      paymentType && allowedPaymentTypes.includes(paymentType)
                        ? [paymentType]
                        : allowedPaymentTypes,
                  },
                },
              },
            },
          ];
          if (!isCredit) {
            w.AND.push({ payment_method: { not: { in: creditPaymentTypes } } });
          } else {
            w.AND.push({ payment_method: { in: creditPaymentTypes } });
          }
          w['before_march_15_check'] = false;
          // Fetch other CPTs with payments state based pending or reviewed
          const otherCptsWithPayments =
            await this.prisma.customer_payment_transactions.findMany({
              where: w,
              include: {
                companies: { select: { name: true } },
                customer: { select: { fullname: true } },
                attachments: true,
                transactions: {
                  select: {
                    id: true,
                    category: {
                      select: { name: true },
                    },
                  },
                },
                payments: {
                  select: {
                    id: true,
                    amount_applied: true,
                    type: true,
                    state: true,
                    exchange_rate: true,
                    payment_remark: true,
                    payment_date: true,
                    auction_category_id: true,
                  },
                  where: { deleted_at: null },
                },
                ...create_update_delete_by,
              },
            });

          const mergedTotal = total + otherCptsWithPayments.length;
          const mergedData = [...data, ...otherCptsWithPayments];

          if (limit > 0) {
            return {
              result: true,
              page: offset,
              per_page: limit,
              total: mergedTotal,
              data: mergedData,
            };
          } else {
            return { result: true, total: mergedTotal, data: mergedData };
          }
        } else {
          if (limit > 0) {
            return { result: true, page: offset, per_page: limit, total, data };
          } else {
            return { result: true, total, data };
          }
        }
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  }

  async findOne(id: number) {
    try {
      const payments =
        await this.prisma.customer_payment_transactions.findUnique({
          where: { id },
          include: {
            companies: { select: { name: true } },
            customer: { select: { fullname: true } },
            attachments: true,
            transactions: {
              select: {
                id: true,
                category: {
                  select: { name: true },
                },
              },
            },
            bank_accounts: {
              where: { deleted_at: null },
              select: {
                id: true,
                bank_name: true,
                account_name: true,
                account_number: true,
                ibn: true,
                bic_code: true,
                currency: true,
                country: true,
              },
            },
            customer_transaction_bank_details: {
              where: { deleted_at: null },
            },
            payments: {
              select: {
                id: true,
                amount_applied: true,
                type: true,
                state: true,
                exchange_rate: true,
                payment_remark: true,
                payment_date: true,
                auction_category_id: true,
                transaction_number: true,
                show_remaining_amount: true,
                mixShippingVehicle: mixVehicleSelect(),
                logInvoice: logInvoiceInclude(),
                singleVcc: vccInvoiceSelect(),
                exitClaimCharge: exitClaimInvoiceSelect(),
                detentionCharge: detentionInvoiceSelect(),
                deliveryChargeInvoice: deliveryInvoiceSelect(),
                shipmentInvoice: shipmentInvoiceSelect(),
                clearanceInvoice: clearanceInvoiceSelect(),
                vehicle: vehicleSelect(),
                payment_allocations: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
              where: { deleted_at: null },
            },
            payment_cards: {
              where: { deleted_at: null },
              select: {
                id: true,
                limit: true,
                short_amount: true,
                type: true,
                exchange_rate: true,
              },
            },
            customer_credits: {
              where: {
                deleted_at: null,
              },
              include: {
                vehicle: {
                  select: {
                    id: true,
                    vin: true,
                    lot_number: true,
                  },
                },
                container: {
                  select: {
                    id: true,
                    container_number: true,
                  },
                },
              },
            },
            exitPapers: {
              where: {
                deleted_at: null,
              },
              include: {
                exit_papers_vehicles: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    vehicles: {
                      select: {
                        id: true,
                        lot_number: true,
                        vin: true,
                      },
                    },
                  },
                },
              },
            },
            ...create_update_delete_by,
          },
        });

      if (payments?.payments) {
        payments.payments = singleCstPaymentHelper(payments);
      }
      return { result: true, data: payments };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }
  recalculate_amount_applied(payments: any[], rate: number) {
    const all_amounts_usd = payments
      ?.map((op) => ({
        applied: op.amount_applied / op.exchange_rate,
      }))
      ?.reduce((acc, curr) => (acc += curr.applied), 0);
    return (all_amounts_usd * Number(rate)).toFixed(3);
  }
  async update(
    id: number,
    attachments: Array<Express.Multer.File>,
    deletedAttachments: string[],
    dto: CreateCustomerPaymentTransactionDto,
    updated_by: number,
  ) {
    try {
      const txn_attachments = attachments.filter((op) =>
        op.fieldname.includes('[transaction]'),
      );
      const bank_attachments = attachments.filter(
        (op) => !op.fieldname.includes('[transaction]'),
      );
      const payments: any =
        await this.setPaymentObjectAndRemainingAmountFlag(dto);
      const payment_cards: any = Object.values(
        Object.assign({}, dto.payment_cards),
      );
      const pmd = dto.payment_method.toLowerCase() as payment_methods;
      const handleTransactionResult =
        await this.handleTransactionForUpdateServices(pmd, dto, id);

      if (handleTransactionResult.status !== 200) {
        return handleTransactionResult;
      }

      const existingPayments =
        await this.prisma.customer_payment_transactions.findUnique({
          where: { id },
          include: {
            attachments: true,
            payments: {
              select: {
                id: true,
                type: true,
                payment_allocations: {
                  select: { id: true },
                  where: { deleted_at: null },
                },
              },
              where: { deleted_at: null },
            },
            payment_cards: {
              select: { id: true },
              where: { deleted_at: null },
            },
          },
        });
      // if (existingPayments.state === payment_state_options.approved) {
      //   return {
      //     status: 400,
      //     result: false,
      //     message: `Customer Payment can not be updated once approved.`,
      //   };
      // }

      const existingPaymentIds = existingPayments.payments.map((p) => +p.id);
      const updatedPaymentIds = payments.map((p) => +p.id);
      const deletedPaymentIds = existingPaymentIds.filter(
        (id) => !updatedPaymentIds.includes(id),
      );

      const existingAllocationIds = existingPayments.payments.flatMap((p) =>
        p.payment_allocations.map((a) => +a.id),
      );

      const updatedAllocationIds = payments?.flatMap(
        (p) =>
          p.payment_allocations &&
          Object.values(p.payment_allocations)
            .map((a: any) => +a.id)
            .filter((id) => id !== undefined),
      );

      const deletedAllocationIds = existingAllocationIds.filter(
        (id) => !updatedAllocationIds.includes(id),
      );

      await this.prisma.payment_allocations.updateMany({
        where: { id: { in: deletedAllocationIds } },
        data: {
          deleted_at: new Date(),
          deleted_by: updated_by,
        },
      });
      const existingPaymentCardIds = existingPayments.payment_cards.map(
        (p) => +p.id,
      );

      const updatedPaymentCardIds = payment_cards.map((p) => +p.rec_id);

      const deletedPaymentCardIds = existingPaymentCardIds.filter(
        (id) => !updatedPaymentCardIds.includes(id),
      );

      const detailsNo =
        await this.prisma.customer_transaction_bank_details.aggregate({
          _max: { id: true },
        });
      const lastPayment = await this.prisma.payments.aggregate({
        _max: { id: true },
      });
      const lastPaymentCard = await this.prisma.payment_cards.aggregate({
        _max: { id: true },
      });
      const lastPaymentAllot = await this.prisma.payment_allocations.aggregate({
        _max: { id: true },
      });
      let bank_details_number: number = detailsNo._max.id;
      let PaymentNo: number = lastPayment._max.id;
      let PaymentCardNo: number = lastPaymentCard._max.id;
      let PaymentAllotNo: number = lastPaymentAllot._max.id;

      for (const attachment of deletedAttachments) {
        await deleteFromMinio(attachment, {
          isProtected: true,
        });
      }
      const depositeDate = dto.deposit_date ? new Date(dto.deposit_date) : null;
      delete dto.deposit_date;
      const bank_details = dto.customer_transaction_bank_details ?? [];
      const existingRecords =
        await this.prisma.customer_transaction_bank_details.findMany({
          where: {
            customer_tran_id: +id,
          },
        });

      const newReferenceNumbers = bank_details
        .map((item: any) => item.reference_number?.trim())
        .filter(Boolean);

      const duplicates = await this.checkDuplicateReferenceNumbers(
        newReferenceNumbers,
        +id,
      );

      if (duplicates.length) {
        return {
          status: 400,
          result: false,
          message: `Reference numbers already in use or duplicated: ${duplicates.join(', ')}`,
        };
      }
      const detailsToDelete =
        await this.prisma.customer_transaction_bank_details.findMany({
          where: {
            customer_tran_id: +id,
            id: {
              notIn: bank_details
                .map((op) => op.id)
                .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
                .filter((id) => !isNaN(id)),
            },
          },
        });
      const changedRecords = existingRecords
        .filter((frontendRecord) => {
          const dbRecord = bank_details.find(
            (db) => +db.id === +frontendRecord.id,
          );
          return dbRecord && dbRecord.attachment !== frontendRecord.attachment;
        })
        .map((frontendRecord) => ({
          id: frontendRecord.id,
          oldAttachment: frontendRecord.attachment,
          newAttachment: bank_details.find((db) => db.id === frontendRecord.id)
            ?.attachment,
        }));
      for (const attachment of changedRecords) {
        if (attachment.oldAttachment) {
          await deleteFromMinio(attachment.oldAttachment, {
            isProtected: true,
          });
        }
      }
      for (const attachment of detailsToDelete) {
        if (attachment.attachment) {
          await deleteFromMinio(attachment.attachment, {
            isProtected: true,
          });
        }
      }

      const updatedPayment =
        await this.prisma.customer_payment_transactions.update({
          where: { id: +id },
          data: {
            ...dto,
            amount_applied:
              this.recalculate_amount_applied(payments, dto.exchange_rate) ??
              dto.amount_applied,
            attachments: {
              create: await this.uploadAttachmentsAndPrepare(
                txn_attachments,
                depositeDate,
              ),
              deleteMany: {
                url: {
                  in: deletedAttachments,
                },
              },
            },
            // customer_transaction_bank_details: {
            //   upsert: bank_details.map((detail) => {
            //     bank_details_number++;
            //     return {
            //       update: {
            //         title: detail.title,
            //         reference_number: detail.reference_number,
            //         remark: detail.remark,
            //         deposit_date: detail.deposit_date,
            //         attachment: detail.attachment,
            //         updated_at: new Date(),
            //         updated_by,
            //       },
            //       create: {
            //         title: detail.title,
            //         reference_number: detail.reference_number,
            //         remark: detail.remark,
            //         attachment: detail.attachment,
            //         created_at: new Date(),
            //         created_by: updated_by,
            //       },
            //       where: { id: +detail.id ? +detail.id : bank_details_number },
            //     };
            //   }),
            //   updateMany: {
            //     where: { id: { in: detailsToDelete.map((op) => op.id) } },
            //     data: {
            //       deleted_at: new Date(),
            //       deleted_by: updated_by,
            //     },
            //   },
            // },
            customer_transaction_bank_details: {
              upsert: await Promise.all(
                bank_details.map(async (detail) => {
                  bank_details_number++;
                  const att = bank_attachments.find(
                    (op) =>
                      op.fieldname ===
                      `attachment[${detail.reference_number}][0]`,
                  );
                  const attachment = detail.attachment
                    ? detail.attachment
                    : att
                      ? await this.uploadCommentAttachment(att)
                      : null;
                  return {
                    update: {
                      title: detail.title,
                      reference_number: detail.reference_number,
                      remark: detail.remark,
                      deposit_date: detail.deposit_date,
                      attachment,
                      attachment_name: detail.attachment_name
                        ? detail.attachment_name
                        : attachment
                          ? getFileNameFromUrl(attachment)
                          : null,
                      updated_at: new Date(),
                      updated_by,
                    },
                    create: {
                      title: detail.title,
                      reference_number: detail.reference_number,
                      remark: detail.remark,
                      deposit_date: detail.deposit_date,
                      attachment,
                      attachment_name: attachment
                        ? getFileNameFromUrl(attachment)
                        : null,
                      created_at: new Date(),
                      created_by: updated_by,
                    },
                    where: {
                      id: +detail.id ? +detail.id : bank_details_number,
                    },
                  };
                }),
              ),
              updateMany: {
                where: { id: { in: detailsToDelete.map((op) => op.id) } },
                data: {
                  deleted_at: new Date(),
                  deleted_by: updated_by,
                },
              },
            },
            payments: {
              upsert: payments.map((payment: paymentDto) => {
                PaymentNo++;
                return {
                  update: {
                    amount_applied: parseFloat(String(payment.amount_applied)),
                    type: payment.type,
                    transaction_number: payment.transaction_number,
                    auction_category_id: +payment.auction_category_id,
                    payment_date: payment.payment_date,
                    state: payment.state,
                    exchange_rate: +payment.exchange_rate,
                    payment_remark: payment.payment_remark,
                    mix_shipping_vehicle_id: +payment.mix_shipping_vehicle_id,
                    shipment_invoice_id: +payment.shipment_invoice_id,
                    clearance_invoice_id: +payment.clearance_invoice_id,
                    log_invoice_id: +payment.log_invoice_id,
                    single_vcc_id: +payment.single_vcc_id,
                    exit_claim_charge_id: +payment.exit_claim_charge_id,
                    detention_charge_id: +payment.detention_charge_id,
                    delivery_charge_invoice_id:
                      +payment.delivery_charge_invoice_id,
                    vehicle_id: +payment.vehicle_id,
                    show_remaining_amount: payment.show_remaining_amount,
                    payment_allocations: {
                      upsert: Object.values(
                        Object.assign({}, payment.payment_allocations),
                      ).map((m: paymentAllocationsDto) => {
                        PaymentAllotNo++;
                        return {
                          update: {
                            amount: m.amount,
                            type: m.type,
                            updated_at: new Date(),
                            updated_by,
                          },
                          create: {
                            amount: m.amount,
                            type: m.type,
                            created_at: new Date(),
                            created_by: updated_by,
                          },
                          where: { id: +m.id ? +m.id : PaymentAllotNo },
                        };
                      }),
                      // updateMany: {
                      //   where: { id: { in: deletedAllocationIds } },
                      //   data: {
                      //     deleted_at: new Date(),
                      //     deleted_by: updated_by,
                      //   },
                      // },
                    },
                    updated_at: new Date(),
                    updated_by,
                  },
                  create: {
                    amount_applied: parseFloat(String(payment.amount_applied)),
                    type: payment.type,
                    transaction_number: payment.transaction_number,
                    auction_category_id: +payment.auction_category_id,
                    payment_date: payment.payment_date,
                    exchange_rate: +payment.exchange_rate,
                    state: payment.state,
                    payment_remark: payment.payment_remark,
                    mix_shipping_vehicle_id: +payment.mix_shipping_vehicle_id,
                    shipment_invoice_id: +payment.shipment_invoice_id,
                    clearance_invoice_id: +payment.clearance_invoice_id,
                    log_invoice_id: +payment.log_invoice_id,
                    single_vcc_id: +payment.single_vcc_id,
                    exit_claim_charge_id: +payment.exit_claim_charge_id,
                    detention_charge_id: +payment.detention_charge_id,
                    delivery_charge_invoice_id:
                      +payment.delivery_charge_invoice_id,
                    vehicle_id: +payment.vehicle_id,
                    show_remaining_amount: payment.show_remaining_amount,
                    payment_allocations: {
                      create: Object.values(
                        Object.assign({}, payment.payment_allocations),
                      ).map((m: paymentAllocationsDto) => ({
                        amount: m.amount,
                        type: m.type,
                        created_by: updated_by,
                      })),
                    },
                    created_at: new Date(),
                    created_by: updated_by,
                  },
                  where: { id: +payment.id ? +payment.id : PaymentNo },
                };
              }),
              updateMany: {
                where: { id: { in: deletedPaymentIds } },
                data: {
                  deleted_at: new Date(),
                  deleted_by: updated_by,
                  show_remaining_amount: false,
                },
              },
            },
            payment_cards: {
              upsert: payment_cards.map((payment: paymentCardsDto) => {
                PaymentCardNo++;
                return {
                  update: {
                    type: payment.type,
                    exchange_rate: +payment.exchange_rate,
                    limit: +payment.limit,
                    short_amount: +payment.short_amount,
                    updated_at: new Date(),
                    updated_by,
                  },
                  create: {
                    type: payment.type,
                    exchange_rate: +payment.exchange_rate,
                    limit: +payment.limit,
                    short_amount: +payment.short_amount,
                    created_at: new Date(),
                    created_by: updated_by,
                  },
                  where: {
                    id: +payment.rec_id ? +payment.rec_id : PaymentCardNo,
                  },
                };
              }),
              updateMany: {
                where: { id: { in: deletedPaymentCardIds } },
                data: {
                  deleted_at: new Date(),
                  deleted_by: updated_by,
                },
              },
            },
            updated_at: new Date(),
            updated_by,
          },
          include: {
            companies: { select: { name: true } },
            customer: { select: { fullname: true } },
            attachments: true,
            transactions: {
              select: {
                id: true,
                category: {
                  select: { name: true },
                },
              },
            },
            bank_accounts: {
              select: {
                id: true,
                bank_name: true,
                account_name: true,
                account_number: true,
                ibn: true,
                bic_code: true,
                currency: true,
                country: true,
              },
            },
            payments: {
              select: {
                id: true,
                amount_applied: true,
                type: true,
                state: true,
                exchange_rate: true,
                payment_remark: true,
                payment_date: true,
                auction_category_id: true,
                transaction_number: true,

                // Include all potential conditional fields
                shipment_invoice_id: true,
                mix_shipping_vehicle_id: true,
                clearance_invoice_id: true,
                single_vcc_id: true,
                log_invoice_id: true,
                delivery_charge_invoice_id: true,
                exit_claim_charge_id: true,
                detention_charge_id: true,
                vehicle_id: true,
              },
              where: { deleted_at: null },
            },
            payment_cards: {
              where: { deleted_at: null },
              select: {
                id: true,
                limit: true,
                short_amount: true,
                type: true,
                exchange_rate: true,
              },
            },
            customer_transaction_bank_details: {
              where: { deleted_at: null },
              select: {
                id: true,
                title: true,
                reference_number: true,
                remark: true,
                deposit_date: true,
                attachment: true,
                attachment_name: true,
              },
            },
            ...create_update_delete_by,
          },
        });

      const existingAuctionPaymentIds = existingPayments.payments
        .filter((payment) => payment.type === 'auction')
        .map((payment) => payment.id.toString());

      const newAuctionPaymentIds = payments
        .filter((payment) => payment.type === 'auction')
        .map((payment) => payment.id.toString());

      const uniqueExistingIds: string[] = existingAuctionPaymentIds.filter(
        (id: string) => !newAuctionPaymentIds.includes(id),
      );

      const uniqueNewIds: string[] = newAuctionPaymentIds.filter(
        (id: string) => !existingAuctionPaymentIds.includes(id),
      );

      const uniqueIds: string[] = [...uniqueExistingIds, ...uniqueNewIds];

      if (
        uniqueIds.length > 0 &&
        (dto.payment_method === 'cash' ||
          dto.payment_method === 'buyer_fee_discount' ||
          dto.payment_method === 'wire' ||
          dto.payment_method === 'check' ||
          dto.payment_method === 'cashbook' ||
          dto.payment_method === 'kabul_cash')
      ) {
        const auctionPaymentRecord = (await this.findOne(updatedPayment?.id))
          .data;

        // --- START: find auction category name
        const auctionPaymentIndex = auctionPaymentRecord?.payments.findIndex(
          (payment) => payment.type === 'auction',
        );
        let category: { name: string };
        if (auctionPaymentRecord?.payments.length > 0) {
          category = await this.prisma.categories.findUnique({
            where: {
              id: auctionPaymentRecord?.payments[auctionPaymentIndex]
                ?.auction_category_id,
            },
            select: { name: true },
          });
        } else if (
          auctionPaymentRecord?.transactions[auctionPaymentIndex]?.category !==
          undefined
        ) {
          category =
            auctionPaymentRecord?.transactions[auctionPaymentIndex]?.category;
        } else {
          const deletedCustomerPaymentTransaction =
            await this.prisma.customer_payment_transactions.findUnique({
              where: { id: updatedPayment?.id },
              include: {
                payments: {
                  orderBy: { created_at: 'desc' },
                  take: 1,
                },
              },
            });
          const deletedAuctionPaymentIndex =
            deletedCustomerPaymentTransaction?.payments.findIndex(
              (payment) => payment.type === 'auction',
            );
          const deleted_auction_category_id =
            deletedCustomerPaymentTransaction?.payments[
              deletedAuctionPaymentIndex
            ]?.auction_category_id;

          category = await this.prisma.categories.findUnique({
            where: {
              id: deleted_auction_category_id,
            },
            select: { name: true },
          });
        }
        // --- END: find auction category name

        await reactEmail(
          '<EMAIL>',
          `${auctionPaymentRecord?.customer?.fullname} - ${category?.name} ${auctionPaymentRecord?.amount} ${dto.currency} Auction Payment`,
          auctionPaymentRecord,
          AuctionPaymentEmailTemplate,
          null,
          {
            from: '<EMAIL>',
            cc: ['<EMAIL>'],
          },
        );
      }

      return { result: true, data: updatedPayment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  restore = async (ids: number[], updated_by: number) => {
    try {
      const result = await this.prisma.$transaction(async (prisma) => {
        // Fetch payments to get their internal IDs
        const payments = await prisma.payments.findMany({
          where: { customer_tran_id: { in: ids.map(Number) } },
          select: { id: true },
        });

        // Restore payments
        await prisma.payments.updateMany({
          where: { customer_tran_id: { in: ids.map(Number) } },
          data: { deleted_at: null, deleted_by: null },
        });

        // Restore related payment allocations
        await prisma.payment_allocations.updateMany({
          where: { payment_id: { in: payments.map((p) => Number(p.id)) } },
          data: { deleted_at: null, deleted_by: null },
        });

        // Restore related customer credits
        await prisma.customer_credits.updateMany({
          where: { customer_payment_transactionId: { in: ids.map(Number) } },
          data: { deleted_at: null, deleted_by: null },
        });

        // Restore and fetch customer payment transactions using update per record
        const updatedTransactions = await Promise.all(
          ids.map((id) =>
            prisma.customer_payment_transactions.update({
              where: { id: Number(id) },
              data: { deleted_at: null, deleted_by: null, updated_by },
              select: {
                id: true,
                payment_method: true,
                exitPapers: { select: { id: true } },
                deleted_at: true,
                deleted_by: true,
              },
            }),
          ),
        );

        // For any exit_paper_credit, update exit_papers' status to credit_given_to_customer
        for (const tx of updatedTransactions) {
          if (
            tx.payment_method === 'exit_paper_credit' &&
            tx.exitPapers.length > 0
          ) {
            await prisma.exit_papers.updateMany({
              where: { id: { in: tx.exitPapers.map((e) => e.id) } },
              data: {
                deleted_at: null,
                deleted_by: null,
                updated_by: updated_by,
                status: 'credit_given_to_customer',
                is_credit_given: true,
              },
            });
          }
        }

        return {
          result: updatedTransactions.length > 0,
          data: updatedTransactions,
        };
      });

      return result;
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  };

  async approveRevertSinglePayment(data: approveRevertSinglePaymentDto) {
    try {
      // const payment: any = this.fetchPayment(id);
      // this.handleInvoiceStatus(payment, InvoiceStatus.paid);
      const updatedPayment = await this.prisma.payments.updateMany({
        where: { id: data.id },
        data: {
          state: data.state,
        },
      });
      return { result: true, data: updatedPayment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async customerPaymentStateUpdate(
    da: approveRevertSinglePaymentDto,
    user_id: number,
  ) {
    try {
      const updatedPayment =
        await this.prisma.customer_payment_transactions.update({
          where: { id: da.id },
          data: {
            state: da.state,
            updated_by: user_id,
          },
        });

      const { data } = await this.findOne(da.id);
      const { email } = await this.prisma.loginables.findUnique({
        where: { customer_id: data?.customer_id },
        select: { email: true },
      });

      if (
        da.state === payment_state_options.reviewed &&
        data.before_march_15_check
      ) {
        await this.prisma.customer_payment_transactions.update({
          where: { id: da.id },
          data: {
            before_march_15_check: false,
          },
        });
      }

      if (
        da.state === payment_state_options.final_reviewed &&
        !da.isRevert &&
        isAuctionVehicleUnpaidToSendEmail(data)
      ) {
        await reactEmail(
          email,
          generatePaymentEmailSubject(data),
          data,
          CustomerEmailTemplateForApprovedPayments,
          CustomerEmailAttachment,
          getRelatedDeptsEmailFromAndCc(data),
        );
      }

      if (
        da.state === payment_state_options.pending &&
        bankApprovalRequiredMethods.includes(data.payment_method)
      ) {
        await reactEmail(
          email,
          generatePaymentEmailSubject(data),
          {
            ...data,
            action: 'bankApprove',
          },
          CreateBankApprovalEmailTemplate,
          null,
          {
            from: '<EMAIL>',
            cc: ['<EMAIL>'],
          },
        );
      }

      if (da.isRevert && da.state !== payment_state_options.bank_approve) {
        await this.prisma.payments.updateMany({
          where: { customer_tran_id: da.id, deleted_at: null },
          data: {
            state: da.state,
          },
        });
      }
      return { result: true, data: updatedPayment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }
  async rejectCustomerPayment(da: any, user_id: number) {
    try {
      const rejectedPayment =
        await this.prisma.customer_payment_transactions.update({
          where: { id: da.id },
          data: {
            state: da.state,
            rejection_reason: da.rejection_reason,
            amount_applied: 0,
          },
          include: {
            attachments: true,
          },
        });
      await this.prisma.payments.updateMany({
        where: {
          customer_tran_id: da.id,
        },
        data: { deleted_at: new Date(), deleted_by: user_id },
      });
      const rejectAttachment = rejectedPayment?.attachments?.find(
        (op) => op.name === 'bank_reject',
      );
      const minioStorage = process.env.MINIO_ENDPOINT;
      const { data } = await this.findOne(da.id);
      const { email } = await this.prisma.loginables.findUnique({
        where: { customer_id: data?.customer_id },
        select: { email: true },
      });
      if (data && email) {
        await reactEmail(
          email,
          'Payment Got Rejected',
          data,
          PaymentRejectionEmailTemplate,
          `${minioStorage}/${rejectAttachment.url}`,
          // CustomerEmailAttachment,
          getRelatedDeptsEmailFromAndCc(data),
          // {
          //   from: process.env.MAILGUN_FROM,
          //   cc: ['<EMAIL>'],
          // },
        );
      }

      return { result: true, data: rejectedPayment };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  softDelete = async (ids: number[], deleted_by: number) => {
    try {
      const numericIds = ids.map((i) => Number(i));
      const now = new Date();

      // Run all of your Prisma reads + writes in one DB transaction
      const { results } = await this.prisma.$transaction(
        async (prisma) => {
          // 1) Load payments
          const payments = await prisma.payments.findMany({
            where: { customer_tran_id: { in: numericIds } },
            select: { id: true },
          });

          // 2) Load bank‐detail attachments to delete
          // const filesToDelete =
          //   await prisma.customer_transaction_bank_details.findMany({
          //     where: { customer_tran_id: { in: numericIds } },
          //     select: { attachment: true },
          //   });

          // 3) Soft‐delete the bank‐details
          await prisma.customer_transaction_bank_details.updateMany({
            where: { customer_tran_id: { in: numericIds } },
            data: { deleted_at: now, deleted_by },
          });

          // 4) Soft‐delete payments
          await prisma.payments.updateMany({
            where: { customer_tran_id: { in: numericIds } },
            data: { deleted_at: now, deleted_by },
          });

          // 5) Soft‐delete payment_allocations
          await prisma.payment_allocations.updateMany({
            where: { payment_id: { in: payments.map((p) => p.id) } },
            data: { deleted_at: now, deleted_by },
          });

          // 6) Soft‐delete the customer_payment_transactions
          await prisma.customer_payment_transactions.updateMany({
            where: { id: { in: numericIds } },
            data: { deleted_at: now, deleted_by },
          });

          // 7) Soft-delete removed credits
          await this.prisma.customer_credits.updateMany({
            where: { customer_payment_transactionId: { in: numericIds } },
            data: {
              deleted_at: now,
              deleted_by,
            },
          });

          // 8) Re‑fetch the transactions so we know which exitPapers credit state to undo
          const results = await Promise.all(
            numericIds.map((id) =>
              prisma.customer_payment_transactions.update({
                where: { id },
                data: {}, // already soft‑deleted, so no data change
                select: {
                  id: true,
                  payment_method: true,
                  exitPapers: { select: { id: true } },
                },
              }),
            ),
          );

          // 9) For any exit_paper_credit, update exit_papers' status to credit_not_given_to_customer
          for (const tx of results) {
            if (
              tx.payment_method === 'exit_paper_credit' &&
              tx.exitPapers.length > 0
            ) {
              await prisma.exit_papers.updateMany({
                where: { id: { in: tx.exitPapers.map((e) => e.id) } },
                data: {
                  deleted_at: null,
                  deleted_by: null,
                  updated_by: deleted_by,
                  status: 'credit_not_given_to_customer',
                  is_credit_given: false,
                },
              });
            }
          }

          return { results };
        },
        { timeout: 60_000 },
      );

      // Only after the DB transaction has committed do we remove from Minio
      // (uncomment and then export filesToDelete variable alongside results in the above function for it to work)
      // await Promise.all(
      //   filesToDelete.map((f) =>
      //     deleteFromMinio(f.attachment, { isProtected: true }),
      //   ),
      // );

      return { result: !!results, data: results };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  };

  async approveRevertAllPayments(
    id: number,
    data: approveRevertAllPaymentsDto,
  ) {
    try {
      const { type, state } = data;
      // const payments = await this.prisma.payments.findMany({
      //   where: { customer_tran_id: id, type: type },
      // });
      // payments.forEach((payment) => {
      //   this.handleInvoiceStatus(
      //     payment,
      //     approveAll ? InvoiceStatus.paid : InvoiceStatus.pending,
      //   );
      // });

      await this.prisma.payments.updateMany({
        where: { customer_tran_id: id, type: type, deleted_at: null },
        data: {
          state,
        },
      });
      return this.findOne(id);
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async handleInvoiceStatus(payment: any, invoiceStatus: InvoiceStatus) {
    const updatedPayments = await Promise.all(
      [
        (async () => {
          if (payment.shipment_invoice_id) {
            const shipmentInvoice = await this.fetchShipmentInvoice([
              +payment.shipment_invoice_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              shipmentInvoice[0].payments,
            );
            const discount = shipmentInvoice[0].discount || 0;
            const invoiceTotal =
              getShipmentInvoiceAmount(
                shipmentInvoice[0].containers,
                shipmentInvoice[0].title_charge_visible,
                shipmentInvoice[0].title_charge_visible,
              ) - discount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.invoices.update({
                where: { id: +payment.shipment_invoice_id },
                data: {
                  status:
                    invoiceStatus === InvoiceStatus.paid
                      ? invoice_status.paid
                      : invoice_status.pending,
                },
              });
            }
          }

          if (payment.mix_shipping_vehicle_id) {
            const mixVehicle = await this.fetchMixVehicle([
              +payment.mix_shipping_vehicle_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              mixVehicle.payments,
            );
            const invoiceTotal =
              getMixVehicleInvoiceAmount(
                mixVehicle,
                mixVehicle?.mix_shipping_invoices?.type,
              ) - getMixVehicleDiscounts(mixVehicle);

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.mix_shipping_vehicles.update({
                where: { id: +payment.mix_shipping_vehicle_id },
                data: {
                  payment_status:
                    invoiceStatus === InvoiceStatus.paid
                      ? payment_status.paid
                      : payment_status.pending,
                },
              });
            } else {
              await this.prisma.mix_shipping_vehicles.update({
                where: { id: +payment.mix_shipping_vehicle_id },
                data: {
                  payment_status:
                    invoiceStatus === InvoiceStatus.paid
                      ? payment_status.partial
                      : payment_status.pending,
                },
              });
            }
          }

          if (payment.clearance_invoice_id) {
            const clearanceInvoice = await this.fetchClearanceInvoice([
              +payment.clearance_invoice_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              clearanceInvoice[0]?.payments,
            );
            const invoiceTotal = clearanceInvoice[0]?.invoice_amount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal.toNumber(),
              )
            ) {
              await this.prisma.clearance_combine_booking_invocies.update({
                where: { id: +payment.clearance_invoice_id },
                data: {
                  status:
                    invoiceStatus === InvoiceStatus.paid
                      ? clearance_combine_booking_invoice_status.paid
                      : clearance_combine_booking_invoice_status.pending,
                },
              });
            }
          }

          if (payment.log_invoice_id) {
            const logInvoice = await this.fetchLogInvoice([
              +payment.log_invoice_id,
            ]);
            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              logInvoice[0].payments,
            );
            const discount = logInvoice[0].discount || 0;
            const invoiceTotal = getLogInvoiceAmount(logInvoice[0]) - discount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.log_invoices.update({
                where: { id: +payment.log_invoice_id },
                data: {
                  status: invoiceStatus === InvoiceStatus.paid ? '4' : '2',
                },
              });
            }
          }

          if (payment.single_vcc_id) {
            const vccInvoice = await this.fetchVccInvoice([
              +payment.single_vcc_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              vccInvoice[0].payments,
            );
            const discount = vccInvoice[0].discount || 0;
            const invoiceTotal = vccInvoice[0].vcc - discount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.single_vcc.update({
                where: { id: +payment.single_vcc_id },
                data: { status: invoiceStatus === InvoiceStatus.paid ? 4 : 2 },
              });
            }
          }

          if (payment.exit_claim_charge_id) {
            const exitClaimInvoice = await this.fetchExitClaimInvoice([
              +payment.exit_claim_charge_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              exitClaimInvoice[0].payments,
            );
            const discount = exitClaimInvoice[0].discount || 0;
            const invoiceTotal = exitClaimInvoice[0].exit_charges - discount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.exit_claim_charge.update({
                where: { id: +payment.exit_claim_charge_id },
                data: { status: invoiceStatus === InvoiceStatus.paid ? 4 : 2 },
              });
            }
          }

          if (payment.detention_charge_id) {
            const detentionInvoice = await this.fetchDetentionInvoice([
              +payment.detention_charge_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              detentionInvoice[0].payments,
            );
            const discount = detentionInvoice[0].discount || 0;
            const invoiceTotal =
              detentionInvoice[0].detention_charges - discount;

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.detention_charge.update({
                where: { id: +payment.detention_charge_id },
                data: { status: invoiceStatus === InvoiceStatus.paid ? 4 : 2 },
              });
            }
          }

          if (payment.delivery_charge_invoice_id) {
            const deliveryInvoice = await this.fetchDeliveryInvoice([
              +payment.delivery_charge_invoice_id,
            ]);

            const totalPaymentAmount = this.fetchInvoicePaymentAmount(
              deliveryInvoice[0].payments,
            );
            const invoiceTotal = parseFloat(
              deliveryInvoice[0].delivery_charges,
            );

            if (
              this.hasInvoiceAmountAppliedFull(
                payment.amount_applied,
                totalPaymentAmount,
                invoiceTotal,
              )
            ) {
              await this.prisma.detention_charge.update({
                where: { id: +payment.detention_charge_id },
                data: { status: invoiceStatus === InvoiceStatus.paid ? 4 : 2 },
              });
            }
          }
          return null;
        })(),
      ].filter(Boolean),
    );

    return updatedPayments;
  }

  fetchInvoicePaymentAmount(payments: any): number {
    if (payments && payments.length > 0) {
      return payments.reduce(
        (
          acc: number,
          curr: {
            amount_applied: number;
            customer_payment_transactions: {
              currency: mix_currency_type;
              exchange_rate: number;
            };
          },
        ) => {
          let amount = curr.amount_applied;
          if (
            curr?.customer_payment_transactions?.currency !==
              mix_currency_type.USD &&
            curr?.customer_payment_transactions?.exchange_rate
          ) {
            amount /= curr.customer_payment_transactions.exchange_rate;
          }
          return acc + amount;
        },
        0,
      );
    }

    return 0;
  }

  hasInvoiceAmountAppliedFull(
    paymentAmountApplied: number,
    totalPaymentAmount: number,
    invoiceTotal: number,
  ): boolean {
    return paymentAmountApplied + totalPaymentAmount >= invoiceTotal;
  }

  async fetchMixVehicle(ids: number[]): Promise<any> {
    return await this.prisma.mix_shipping_vehicles.findMany({
      where: { id: { in: ids.map(Number) } },
      ...mixVehicleSelect(),
    });
  }

  async fetchClearanceInvoice(ids: number[]): Promise<any> {
    return await this.prisma.clearance_combine_booking_invocies.findMany({
      where: { id: { in: ids.map(Number) } },
      ...clearanceInvoiceSelect(),
    });
  }

  async fetchLogInvoice(ids: number[]): Promise<any> {
    return await this.prisma.log_invoices.findMany({
      where: { id: { in: ids.map(Number) } },
      ...logInvoiceInclude(),
    });
  }

  async fetchVccInvoice(ids: number[]): Promise<any> {
    return await this.prisma.single_vcc.findMany({
      where: { id: { in: ids.map(Number) } },
      ...vccInvoiceSelect(),
    });
  }

  async fetchExitClaimInvoice(ids: number[]): Promise<any> {
    return await this.prisma.exit_claim_charge.findMany({
      where: { id: { in: ids.map(Number) } },
      ...exitClaimInvoiceSelect(),
    });
  }

  async fetchDetentionInvoice(ids: number[]): Promise<any> {
    return await this.prisma.detention_charge.findMany({
      where: { id: { in: ids.map(Number) } },
      ...detentionInvoiceSelect(),
    });
  }

  async fetchDeliveryInvoice(ids: number[]): Promise<any> {
    return await this.prisma.delivery_charge_invoice.findMany({
      where: { id: { in: ids.map(Number) } },
      ...deliveryInvoiceSelect(),
    });
  }

  async fetchShipmentInvoice(ids: number[]): Promise<any> {
    return await this.prisma.invoices.findMany({
      where: { id: { in: ids.map(Number) } },
      ...shipmentInvoiceSelect(),
    });
  }

  async fetchAuctionVehicle(ids: number[]): Promise<any> {
    return await this.prisma.vehicles.findMany({
      where: { id: { in: ids.map(Number) } },
      ...vehicleSelect(),
    });
  }

  async fetchPayment(id: number): Promise<any> {
    return await this.prisma.payments.findUnique({
      where: { id },
    });
  }

  downloadInvoice = async (invoiceName: string) => {
    return await getPreSignedDownloadUrl(
      `uploads/customer_payments/${invoiceName}`,
      { isProtected: true },
    );
  };

  uploadAttachmentsAndPrepare = async (
    attachments: Array<Express.Multer.File>,
    depositeDate: Date | null,
  ) => {
    const uploadedAttachments = [];
    const allowedMimeTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];
    for (const attachment of attachments) {
      if (!allowedMimeTypes.includes(attachment?.mimetype)) {
        throw new Error('Only PDF, PNG, JPG, and JPEG files are allowed!');
      }
      const destination = 'uploads/customer_payments';
      const uploadedFile = await uploadToMinio(attachment, destination, {
        isProtected: true,
        isResizeEnabled: false,
      });

      uploadedAttachments.push({
        url: `${uploadedFile.bucket}/${uploadedFile.key}`,
        name: uploadedFile.key.slice(uploadedFile.key.lastIndexOf('/') + 1),
        deposit_date: depositeDate,
      });
    }

    return uploadedAttachments;
  };

  uploadCommentAttachment = async (attachment: Express.Multer.File) => {
    const allowedMimeTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];
    if (!allowedMimeTypes.includes(attachment?.mimetype)) {
      throw new Error('Only PDF, PNG, JPG, and JPEG files are allowed!');
    }
    const destination = 'uploads/customer_payments';
    const uploadedFile = await uploadToMinio(attachment, destination, {
      isProtected: true,
      isResizeEnabled: false,
    });
    return `${uploadedFile.bucket}/${uploadedFile.key}`;
  };

  async fetchInvoice(dto: fetchInvoiceDto) {
    try {
      let data: any = [];
      if (dto.type == payment_types.shipment && dto?.ids) {
        data = await this.fetchShipmentInvoice(dto?.ids);
      }

      if (dto.type == payment_types.single_vcc && dto?.ids) {
        data = await this.fetchVccInvoice(dto?.ids);
      }
      if (dto.type == payment_types.clearance && dto?.ids) {
        data = await this.fetchClearanceInvoice(dto?.ids);
      }
      if (dto.type == payment_types.clear_log && dto?.ids) {
        data = await this.fetchLogInvoice(dto?.ids);
      }
      if (dto.type == payment_types.delivery_charge && dto?.ids) {
        data = await this.fetchDeliveryInvoice(dto?.ids);
      }
      if (dto.type == payment_types.exit_claim_charge && dto?.ids) {
        data = await this.fetchExitClaimInvoice(dto?.ids);
      }
      if (dto.type == payment_types.detention_charge && dto?.ids) {
        data = await this.fetchDetentionInvoice(dto?.ids);
      }
      if (dto.type == payment_types.mix && dto?.ids) {
        data = await this.fetchMixVehicle(dto?.ids);
      }
      if (dto.type == payment_types.auction && dto?.ids) {
        data = await this.fetchAuctionVehicle(dto?.ids);
      }
      return { result: true, data };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async generateStatement(customerPaymentIds: number[]) {
    const customerPayments: any = [];

    for (const id of customerPaymentIds) {
      const { data } = await this.findOne(id);
      customerPayments.push(data);
    }

    try {
      const CustomerStatementHtml = await render(
        CustomerStatementTemplate({ customerPayments }),
      );
      const customerStatementPdf = await generatePdf(CustomerStatementHtml);
      return { result: true, data: customerStatementPdf };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async generateVoucher(customerPaymentIds: number[], user: any) {
    const customerPayments: any = [];

    for (const id of customerPaymentIds) {
      const { data } = await this.findOne(id);
      customerPayments.push(data);
    }

    const username = await this.prisma.users.findUnique({
      where: {
        id: user.loginable_id,
      },
      select: {
        fullname: true,
      },
    });
    try {
      const CustomerStatementHtml = await render(
        customerVoucherTemplate({ customerPayments }, username),
      );
      const customerStatementPdf = await generatePdf(CustomerStatementHtml);
      return { result: true, data: customerStatementPdf };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  async generateTransactionNumber(paymentMethod: payment_methods) {
    const transactionSequence =
      await this.prisma.transaction_number_sequence.findUnique({
        where: { key: paymentMethod },
      });

    if (!transactionSequence) {
      return {
        status: 400,
        result: false,
        message: `Transaction sequence not found for ${paymentMethod}`,
      };
    }

    return {
      status: 200,
      result: true,
      transaction_number: `${transactionSequence.name}${String(transactionSequence.value).padStart(5, '0')}`,
    };
  }

  removeMarch15Check = async (query: customerPaymentTransactionIds) => {
    const { cptIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: cptIds },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          before_march_15_check: false,
        },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'No Customer payment transactions found.',
      };
  };

  getAll = (params: any) =>
    this.prisma.customer_payment_transactions
      .findMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  editMany = (params: any) =>
    this.prisma.customer_payment_transactions
      .updateMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  private async upsertCredits(
    client: any,
    attachments: Express.Multer.File[],
    dto: CustomerCreditsDto,
    created_by: number,
  ) {
    const customerCredits = dto.customer_credits || [];

    // Use the passed-in client to create everything
    const createdPayment = await client.customer_payment_transactions.create({
      data: {
        payment_method: dto.payment_method,
        amount: dto.amount,
        service_charge: dto.service_charge,
        transaction_number: dto.transaction_number,
        remark: dto.remark,
        link: dto.link,
        credit_date: dto.credit_date,
        currency: dto.currency,
        exchange_rate: dto.exchange_rate,
        customer_id: dto.customer_id,
        company_id: dto.company_id,
        state: dto.state,
        attachments: {
          create: await this.uploadAttachmentsAndPrepare(attachments, null),
        },
        customer_credits: {
          create: customerCredits.map((details) => ({
            vehicle_id: +details.vehicle_id,
            container_id: +details.container_id,
            amount: +details.amount,
            remark: details.remark,
          })),
        },
        ...(dto.exitPapersIds?.length
          ? {
              exitPapers: {
                connect: dto.exitPapersIds.map((id) => ({ id })),
              },
            }
          : {}),
        created_at: new Date(),
        created_by,
      },
      include: {
        companies: { select: { name: true } },
        customer: { select: { fullname: true } },
        exitPapers: true,
        ...create_update_delete_by,
      },
    });

    // If this was an exit_paper_credit, update the exit paper status
    if (
      dto.payment_method === 'exit_paper_credit' &&
      dto.exitPaperStatus &&
      dto.exitPapersIds?.length > 0
    ) {
      await client.exit_papers.updateMany({
        where: { id: { in: dto.exitPapersIds } },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by: created_by,
          status: dto.exitPaperStatus as any,
          is_credit_given: dto.is_credit_given,
        },
      });
    }

    return { result: true, data: createdPayment };
  }

  async createCredits(
    attachments: Express.Multer.File[],
    dto: CustomerCreditsDto,
    created_by: number,
    tx?: any,
  ) {
    try {
      if (tx) {
        // already in a transaction: just call core logic
        return await this.upsertCredits(tx, attachments, dto, created_by);
      } else {
        const pmd = dto.payment_method.toLowerCase() as payment_methods;
        const handleTransactionResult =
          await this.handleTransactionForCreateServices(pmd, dto);

        if (handleTransactionResult.status !== 200) {
          return handleTransactionResult;
        }

        // standalone: wrap in its own transaction
        return await this.prisma.$transaction(
          async (prismaTx) => {
            return await this.upsertCredits(
              prismaTx,
              attachments,
              dto,
              created_by,
            );
          },
          { timeout: 60_000 },
        );
      }
    } catch (e) {
      catch_response(e);
    } finally {
      if (!tx) {
        // only disconnect when this was the top‐level transaction
        await this.prisma.$disconnect();
      }
    }
  }

  async updateCredits(
    id: number,
    attachments: Array<Express.Multer.File>,
    deletedAttachments: string[],
    dto: CustomerCreditsDto,
    updated_by: number,
  ) {
    const pmd = dto.payment_method.toLowerCase() as payment_methods;
    const handleTransactionResult =
      await this.handleTransactionForUpdateServices(pmd, dto, id);

    if (handleTransactionResult.status !== 200) {
      return handleTransactionResult;
    }

    const existingPayments =
      await this.prisma.customer_payment_transactions.findUnique({
        where: { id },
        include: {
          attachments: true,
          payments: {
            select: {
              id: true,
              type: true,
            },
            where: { deleted_at: null },
          },
          customer_credits: true,
        },
      });

    // --- START: Handle existing customer_credits ---
    const existingCreditIds = existingPayments.customer_credits.map(
      (c) => c.id,
    );
    const incomingCredits = dto.customer_credits ?? [];
    const updatedCreditIds = incomingCredits.map((c) => +c.id).filter(Boolean);
    const deletedCreditIds = existingCreditIds.filter(
      (cid) => !updatedCreditIds.includes(cid),
    );

    // Soft-delete removed credits
    if (deletedCreditIds.length) {
      await this.prisma.customer_credits.updateMany({
        where: { id: { in: deletedCreditIds } },
        data: {
          deleted_at: new Date(),
          deleted_by: updated_by,
        },
      });
    }
    // --- END: Handle existing customer_credits ---

    // --- START: Handle existing Attachments ---
    for (const attachment of deletedAttachments) {
      await deleteFromMinio(attachment, {
        isProtected: true,
      });
    }
    // --- END: Handle existing Attachments ---

    try {
      const existingPayments =
        await this.prisma.customer_payment_transactions.update({
          where: { id: +id },
          data: {
            ...dto,
            attachments: {
              create: await this.uploadAttachmentsAndPrepare(attachments, null),
              deleteMany: {
                url: {
                  in: deletedAttachments,
                },
              },
            },
            customer_credits: {
              upsert: (dto.customer_credits ?? []).map((credit) => ({
                where: { id: credit.id ? +credit.id : -1 },
                update: {
                  vehicle_id: +credit.vehicle_id,
                  container_id: +credit.container_id,
                  amount: +credit.amount,
                  remark: credit.remark,
                  updated_at: new Date(),
                  updated_by,
                },
                create: {
                  vehicle_id: +credit.vehicle_id,
                  container_id: +credit.container_id,
                  amount: +credit.amount,
                  remark: credit.remark,
                  created_at: new Date(),
                  created_by: updated_by,
                },
              })),
            },
            updated_at: new Date(),
            updated_by,
          },
          include: {
            companies: { select: { name: true } },
            customer: { select: { fullname: true } },
            attachments: true,
            transactions: {
              select: {
                id: true,
                category: {
                  select: { name: true },
                },
              },
            },
            ...create_update_delete_by,
          },
        });

      return { result: true, data: existingPayments };
    } catch (e) {
      catch_response(e);
    } finally {
      this.prisma.$disconnect();
    }
  }

  handleTransactionForCreateServices = async (
    pmd: payment_methods,
    dto: CreateCustomerPaymentTransactionDto | CustomerCreditsDto,
  ) => {
    if (!excludedValues.includes(pmd)) {
      if (!dto.transaction_number || dto.transaction_number.trim() === '') {
        return {
          status: 400,
          result: false,
          message: `Transaction Number is required`,
        };
      }
      const exists = await this.prisma.customer_payment_transactions.findFirst({
        where: {
          deleted_at: null,
          AND: [
            { transaction_number: { not: null } },
            { transaction_number: dto.transaction_number.trim() },
            { transaction_number: { not: '' } },
          ],
        },
      });
      if (exists) {
        return {
          status: 400,
          result: false,
          message: `Transaction Number ${dto.transaction_number} already exists`,
        };
      }
    } else {
      const transactionSequence =
        await this.prisma.transaction_number_sequence.findUnique({
          where: { key: pmd },
        });

      if (!transactionSequence) {
        return {
          status: 400,
          result: false,
          message: `Transaction sequence not found for ${pmd}`,
        };
      }
      dto.transaction_number = `${transactionSequence.name}${String(transactionSequence.value).padStart(5, '0')}`;
      await this.prisma.transaction_number_sequence.update({
        where: { key: pmd },
        data: { value: transactionSequence.value + 1 },
      });
    }

    return {
      status: 200,
      result: true,
      message: `Success!`,
    };
  };

  handleTransactionForUpdateServices = async (
    pmd: payment_methods,
    dto: CreateCustomerPaymentTransactionDto | CustomerCreditsDto,
    id: number,
  ) => {
    if (!excludedValues.includes(pmd)) {
      if (!dto.transaction_number || dto.transaction_number.trim() === '') {
        return {
          status: 400,
          result: false,
          message: `Transaction Number is required`,
        };
      }
      const exists = await this.prisma.customer_payment_transactions.findFirst({
        where: {
          id: { not: { equals: id } },
          deleted_at: null,
          AND: [
            { transaction_number: { not: null } },
            { transaction_number: dto.transaction_number.trim() },
            { transaction_number: { not: '' } },
          ],
        },
      });
      if (exists) {
        return {
          status: 400,
          result: false,
          message: `Transaction Number ${dto.transaction_number} already exists`,
        };
      }
    } else {
      const trn = await this.prisma.customer_payment_transactions.findFirst({
        where: {
          id: id,
        },
        select: {
          transaction_number: true,
        },
      });
      if (
        !dto.transaction_number ||
        dto.transaction_number.trim() === '' ||
        trn.transaction_number != dto.transaction_number
      ) {
        const transactionSequence =
          await this.prisma.transaction_number_sequence.findUnique({
            where: { key: pmd },
          });

        if (!transactionSequence) {
          return {
            status: 400,
            result: false,
            message: `Transaction sequence not found for ${pmd}`,
          };
        }
        dto.transaction_number = `${transactionSequence.name}${String(transactionSequence.value).padStart(5, '0')}`;
        await this.prisma.transaction_number_sequence.update({
          where: { key: pmd },
          data: { value: transactionSequence.value + 1 },
        });
      }
    }

    return {
      status: 200,
      result: true,
      message: `Success!`,
    };
  };

  // async getVinLot(query: GetVinLotDto) {
  //   try {
  //     const { company_id, search, payment_method } = query;

  //     const whereFilter: any = {
  //       company_id,
  //       deleted_at: null,
  //       ...(search
  //         ? {
  //             OR: [
  //               { vin: { contains: search, mode: 'insensitive' } },
  //               { lot_number: { contains: search, mode: 'insensitive' } },
  //             ],
  //           }
  //         : {}),
  //     };

  //     if (payment_method) {
  //       whereFilter.customer_credits = {
  //         none: {
  //           deleted_at: null,
  //           vehicle_id: { not: null },
  //           customer_payment_transaction: {
  //             payment_method: payment_method,
  //           },
  //         },
  //       };
  //     }

  //     const vehicles = await this.prisma.vehicles.findMany({
  //       where: whereFilter,
  //       take: 50,
  //       select: {
  //         id: true,
  //         vin: true,
  //         lot_number: true,
  //       },
  //     });

  //     return {
  //       result: true,
  //       data: vehicles,
  //     };
  //   } catch (error) {
  //     console.error('Error fetching vehicles:', error);
  //     return {
  //       result: false,
  //       error: 'Failed to fetch vehicles',
  //     };
  //   }
  // }

  async getVinLot(query: GetVinLotDto) {
    const { company_id, search, payment_method } = query;

    const searchParam = search ? `%${search}%` : null;
    const result = await this.db.runRaw(`
      SELECT v.id, v.vin, v.lot_number
      FROM vehicles v
      LEFT JOIN containers c ON c.id = v.container_id
      WHERE v.deleted_at IS NULL
        AND (
          v.company_id = ${company_id}
          ${payment_method === payment_methods.clearance_credit ? `OR c.company_id = ${company_id}` : ''}
        )
        ${
          searchParam
            ? `AND (
            v.vin ILIKE '${searchParam}' OR
            v.lot_number ILIKE '${searchParam}'
          )`
            : ''
        }
        ${
          payment_method
            ? `AND NOT EXISTS (
            SELECT 1
            FROM customer_credits cc
            JOIN customer_payment_transactions cpt
              ON cpt.id = cc."customer_payment_transactionId"
            WHERE cc.vehicle_id = v.id
              AND cc.deleted_at IS NULL
              AND cpt.payment_method = '${payment_method}'
          )
        `
            : ''
        }
      LIMIT 50;
    `);

    return {
      result: true,
      data: result,
    };
  }

  async getContainerNumber(query: GetContainerNumberDto) {
    try {
      const { company_id, search, payment_method } = query;

      const whereFilter: any = {
        company_id,
        deleted_at: null,
        ...(search
          ? { container_number: { contains: search.toUpperCase() } }
          : {}),
      };

      if (payment_method) {
        whereFilter.customer_credits = {
          none: {
            deleted_at: null,
            container_id: { not: null },
            customer_payment_transaction: {
              payment_method: payment_method,
            },
          },
        };
      }

      const containers = await this.prisma.containers.findMany({
        where: whereFilter,
        take: 50,
        select: {
          id: true,
          container_number: true,
        },
      });

      return {
        result: true,
        data: containers,
      };
    } catch (error) {
      console.error('Error fetching containers:', error);
      return {
        result: false,
        error: 'Failed to fetch containers',
      };
    }
  }

  async setPaymentObjectAndRemainingAmountFlag(cpt: any) {
    if (!cpt || !Array.isArray(cpt.payments)) {
      return [];
    }

    const amount = Number(cpt.amount || 0);
    const applied = Number(cpt.amount_applied || 0);
    const fee = Number(cpt.transaction_fee || 0);
    const inapplicable = Number(cpt.inapplicable_amount || 0);

    const remaining = amount - applied - fee - inapplicable;

    let payments = cpt.payments.map((p: any) => ({
      ...p,
      show_remaining_amount: false,
    }));

    if (remaining > 0.009) {
      const mixPayments = payments.filter((p) => p.type === 'mix');
      if (mixPayments.length > 0) {
        const lastMixId = mixPayments[mixPayments.length - 1].id;
        payments = payments.map((p) => ({
          ...p,
          show_remaining_amount: p.id === lastMixId,
        }));
      }
    }
    return Object.values(Object.assign({}, payments));
  }
}
