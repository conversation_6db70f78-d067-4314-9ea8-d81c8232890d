import { Module } from '@nestjs/common';
import { ShipmentCostsCController } from 'src/Customer_APIs/mix-shipment-rates/shipment-cost.controller';
import { MixShippingRateService } from './mix-shipping-rates.service';
import { BullModule } from '@nestjs/bull';
import { MixShippingRateProccessor } from './mix-shipping-rates.processor';
import { EventGateWay } from 'src/websocket/event.gateway';
import { FirebaseService } from 'src/firebase/firebase.service';
import { MixShippingRateController } from './mix-shipping-rates.controller';
import { LocationsModule } from '../locations/locations.module';
import { CacheModule } from '@nestjs/cache-manager';
import { RedisClientOptions } from 'redis';
import * as redisStore from 'cache-manager-redis-store';


@Module({
  imports: [
    BullModule.registerQueue({
      name: 'mix_shipping_rate_email',
    }),
    LocationsModule,
    CacheModule.register<RedisClientOptions>({
      store: redisStore,
      url: `redis://:${process.env.REDIS_PASSWORD}@${process.env.REDIS_HOST
        }:${+process.env.REDIS_PORT}/0`,
      ttl: 600, // 10 minutes
      max: 100000,
    }),
  ],
  controllers: [MixShippingRateController, ShipmentCostsCController],
  providers: [
    MixShippingRateService,
    MixShippingRateProccessor,
    EventGateWay,
    FirebaseService,
  ],
})
export class MixShippingRateModule { }
