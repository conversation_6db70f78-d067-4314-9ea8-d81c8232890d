import { BadRequestException, Injectable } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { PrismaClient } from '@prisma/client';
import { Transform, Type, instanceToPlain } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsString,
  Min,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
  Validate,
  IsOptional,
  ValidateIf,
  IsDate,
  IsArray,
  IsEnum,
} from 'class-validator';
import * as moment from 'moment';
import { SearchPaginateDto } from 'src/Commons/dto/searchPaginateDto.dto';
import { toBoolean, toDate } from 'src/Commons/helpers/cast.helper';
import { ArrayForeignKeysExist } from 'src/Commons/validators/arrayForeignkey.validator';
import { ForeignKeyExist } from 'src/Commons/validators/foreignKey.validator';

export class FindMixShippingRateDto extends SearchPaginateDto {
  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;
  @ApiProperty({ required: false })
  @IsString()
  @Transform(({ value }) => value.trim())
  status?: string
}

export function BranchExist(
  branch: boolean,
  fieldName: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: BranchIdExistConstraint,
      constraints: [branch, fieldName],
    });
  };
}

@ValidatorConstraint({ name: 'isForeignKeyExist', async: true })
@Injectable()
export class BranchIdExistConstraint implements ValidatorConstraintInterface {
  async validate(value: string, args: ValidationArguments) {
    const prisma = new PrismaClient();
    const branch = args.constraints[0];
    const fieldName = args.constraints[1];
    const where =
      fieldName == 'id'
        ? { id: +value }
        : { [fieldName]: { equals: value, mode: 'insensitive' } };

    where['parentId'] = branch ? { not: null } : null;
    if (value) {
      const branch = await prisma.loading_states.findFirst({ where });

      if (fieldName == 'id') {
        if (!branch)
          throw new BadRequestException(
            `${args.property} ${value} does not exists`,
          );
        else return true;
      } else {
        if (branch) throw new BadRequestException(`${value} already exists`);
        else return true;
      }
    }
  }
}

@ValidatorConstraint({ name: 'city_name', async: true })
@Injectable()
export class CheckCityName implements ValidatorConstraintInterface {
  async validate(value: string, args: ValidationArguments): Promise<boolean> {
    const dto = instanceToPlain(args.object as Record<string, unknown>);
    const prisma = new PrismaClient();
    const loadCity = await prisma.loading_cities.findFirst({
      where: {
        city_name: { equals: dto.city_name, mode: 'insensitive' },
        branch_id: dto.branchId,
      },
    });
    return !loadCity;
  }
  defaultMessage() {
    return '$property $value is already exist';
  }
}

export class ApproveMixShippingRateDto {
  @IsNotEmpty()
  @ApiProperty({ required: true })
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  destination_id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf((o) => o.date_in && o.date_in != '')
  @Transform(({ value }) => toDate(value))
  @Type(() => Date)
  @IsDate()
  effectiveDate?: Date;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  status: string;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsNotEmpty()
  isEmailSent = false;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  subject: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  body: string;
}

export class RejectMixShippingRateDto {
  @ApiProperty({ required: true })
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map((val) => +val) : [+value],
  )
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;
}

export class DuplicateMixShippingRateDto {
  @ApiProperty({ required: true })
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map((val) => +val) : [+value],
  )
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;

  @ApiProperty({ required: false })
  @ValidateIf(
    (o) =>
      o.duplicate_from_general_to_company &&
      o.duplicate_from_general_to_company != '',
  )
  @IsBoolean()
  duplicate_from_general_to_company: boolean;
}

export class updateCostFieldsDto {
  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  @Min(0)
  value: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  @BranchExist(false, 'name')
  field: string;
}

export class CreateLoadCitiesDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @BranchExist(true, 'id')
  branchId: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  @Validate(CheckCityName)
  city_name: string;
}

export class UpdateLoadCitiesDto extends CreateLoadCitiesDto { }

export class CreateMixShippingRatesDto {
  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  shipping: number;

  @ApiProperty({ required: false, default: 0 })
  @IsNumber()
  @IsOptional()
  clearance?: number;

  @ApiProperty({ required: false, default: 0 })
  @IsNumber()
  @IsOptional()
  TDS_charges?: number;

  @ApiProperty({ required: false, default: 0 })
  @IsNumber()
  @IsOptional()
  profit?: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('locations', 'id')
  location_id: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('loading_cities', 'id')
  loading_city_id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateIf(
    (o) =>
      o.destination_id && o.destination_id != '' && o.destination_id != null,
  )
  @IsNumber()
  @ForeignKeyExist('destinations', 'id')
  destination_id?: number;

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => (value ? Boolean(value) : null))
  @IsBoolean()
  update_for_same_pol_pod: boolean;
}

export class CreateLoadStateDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  @BranchExist(false, 'name')
  name: string;
}

export class EffectiveDateDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @Transform(({ value }) => new Date(moment(value).format('YYYY-MM-DD')))
  @IsDate()
  @Type(() => Date)
  effective_date: Date;
}

export class CreateLoadBranchDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  // @BranchExist(true, 'name')
  name: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @BranchExist(false, 'id')
  parentId: number;
}

export class StateIdDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('loading_states', 'id')
  stateId: number;
}

export class BranchIdDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @BranchExist(true, 'id')
  branchId: number;
}

export class CalculationDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('destinations', 'id')
  destination_id: number;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('loading_cities', 'id')
  loading_city_id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => +value)
  vehicle_price: number;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsNotEmpty()
  full_size_SUVs = false;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsNotEmpty()
  manheim_adesa = false;

  @ApiProperty({ default: false })
  @IsBoolean()
  @IsNotEmpty()
  major_accident = false;

  /* @ApiProperty({ default: false })
  @IsBoolean()
  @IsNotEmpty()
  more_than_10000_AED: boolean = false; */
}

export class CityIdDTo {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => +value)
  @ForeignKeyExist('loading_cities', 'id')
  loading_city_id: number;
}

export class SendMailDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  subject: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  body: string;

  @ApiProperty({ type: [Number], required: false })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    if (typeof value == 'object' && value.length > 1) return value.map(Number);
    else return [+value];
  })
  @IsNumber({ allowNaN: false }, { each: true })
  @ArrayForeignKeysExist('companies')
  company_ids: number[];

  @ApiProperty({ required: false })
  @ValidateIf((o) => o.destination_id && o.destination_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  destination_id: number;

  @ApiProperty({ required: false, default: false })
  @Transform(({ value }) => toBoolean(value))
  @IsBoolean()
  @IsOptional()
  all_customers: boolean;

  @ApiProperty({ required: false, default: false })
  @Transform(({ value }) => toBoolean(value))
  @IsBoolean()
  @IsOptional()
  is_special: boolean;
}

export enum Currency {
  USD = 'USD',
  AED = 'AED',
  OMR = 'OMR',
  GEL = 'GEL',
}

export class DownloadPdfDto {
  @ApiProperty({ required: false })
  @ValidateIf((o) => o.company_id && o.company_id != '')
  @IsNumber()
  @Transform(({ value }) => (value ? +value : null))
  company_id: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(Currency)
  @Transform(({ value }) => value.trim())
  currency?: Currency;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => +value)
  exchangeRate?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  selectedColumns?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  note?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  customNote?: string;
}
