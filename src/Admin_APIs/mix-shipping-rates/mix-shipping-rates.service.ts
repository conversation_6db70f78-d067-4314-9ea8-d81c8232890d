import { BadRequestException, Injectable } from '@nestjs/common';
import {
  CreateLoadBranchDto,
  CreateLoadCitiesDto,
  CreateMixShippingRatesDto,
  CreateLoadStateDto,
  DuplicateMixShippingRateDto,
  FindMixShippingRateDto,
  SendMailDto,
  updateCostFieldsDto,
  ApproveMixShippingRateDto,
  RejectMixShippingRateDto,
} from './dto/mix-shipping-rates.dto';
import { CommonFields } from '../../Commons/services/common.fields';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import { DbService } from 'src/prisma/prisma.service';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { mix_shipping_rate_status_enum, Prisma } from '@prisma/client';
import * as xlsx from 'xlsx';
import { Workbook } from 'exceljs';
import { LocationsService } from '../locations/locations.service';
import { UpsertCustomPNLsDtoArray } from '../rate-analysis/shipping-rates/dto/ra-shipping-rates.dto';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

const create_update_by = CommonFields([
  'mix_shipping_rates_created_by',
  'mix_shipping_rates_updated_by',
]);

const searchColumns = [
  // 'int@@towing',
  'int@@shipping',
  'int@@clearance',
  'int@@TDS_charges',
  'locations.name',
  'destinations.name',
  'loading_cities.city_name',
  'loading_cities.loading_states.name',
  'loading_cities.loading_states.parent.name',
];

@Injectable()
export class MixShippingRateService {
  private prisma: typeof DbService.prisma;
  constructor(
    @InjectQueue('mix_shipping_rate_email') private queue: Queue,
    private readonly locationService: LocationsService,
    private readonly db: DbService,
    @InjectRedis() private readonly redis: Redis,
  ) {
    this.prisma = DbService.prisma;
  }
  private deleted = { deleted_at: null, deleted_by: null };

  createState = (dto: CreateLoadStateDto, created_by: number) =>
    this.prisma.loading_states
      .create({
        data: {
          ...dto,
          created_by,
          updated_by: created_by,
        },
        select: { id: true, name: true },
      })
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  createBranch = (dto: CreateLoadBranchDto, created_by: number) =>
    this.prisma.loading_states
      .create({
        data: {
          ...dto,
          created_by,
          updated_by: created_by,
        },
        select: { id: true, name: true },
      })
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  createCity = (dto: CreateLoadCitiesDto, created_by: number) =>
    this.prisma.loading_cities
      .create({
        data: {
          city_name: dto.city_name,
          branch_id: dto.branchId,
          created_by,
          updated_by: created_by,
        },
      })
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getStates = () =>
    this.prisma.loading_states
      .findMany({
        select: { id: true, name: true },
        orderBy: { name: 'asc' },
        where: {
          parentId: null,
          ...this.deleted,
        },
      })
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getBranches = (stateId: number) =>
    this.prisma.loading_states
      .findMany({
        where: { parentId: stateId, ...this.deleted },
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      })
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getCities = (branchId: number) =>
    this.prisma
      .$queryRawUnsafe(
        `SELECT id, city_name AS name FROM loading_cities WHERE branch_id = ${branchId} AND deleted_at IS NULL AND deleted_by IS NULL ORDER BY city_name ASC`,
      )
      .then((data: any) => ({ result: data ? true : false, data }))
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  create = async (dto: CreateMixShippingRatesDto, created_by: number) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { update_for_same_pol_pod, ...dt } = dto;
    const res = await this.prisma.mix_shipping_rates
      .findFirst({
        where: {
          location_id: dto.location_id,
          loading_city_id: dto.loading_city_id,
          destination_id: dto.destination_id,
          status: mix_shipping_rate_status_enum.pending,
          company_id: dto.company_id ?? null,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
    if (res)
      throw new BadRequestException({
        statusCode: 400,
        message:
          'Rate already exist in specific location and city with pending status.',
        error: 'Bad Request',
      });
    return await this.prisma.mix_shipping_rates
      .create({
        data: {
          ...dt,
          status: mix_shipping_rate_status_enum.pending,
          created_by,
          updated_by: created_by,
        },
        select: {
          id: true,
          shipping: true,
          clearance: true,
          TDS_charges: true,
          profit: true,
          location_id: true,
          loading_city_id: true,
          destination_id: true,
          loading_cities: {
            select: {
              id: true,
              city_name: true,
              loading_states: {
                where: { parentId: { not: null } },
                select: {
                  id: true,
                  name: true,
                  parent: {
                    select: { id: true, name: true },
                    where: { parentId: null },
                  },
                },
              },
            },
          },
          locations: { select: { name: true, id: true } },
          destinations: { select: { name: true } },
          status: true,
          updated_at: true,
          created_at: true,
          ...create_update_by,
        },
      })
      .then(async (data) => {
        const towingRate = await this.prisma.towing_rates.findFirst({
          where: {
            location_id: data?.location_id,
            loading_city_id: data?.loading_city_id,
          },
        });
        return {
          result: data ? true : false,
          data: { ...data, towing: towingRate ? towingRate?.towing : 0 },
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);
  };

  findAll = async (
    query: FindMixShippingRateDto,
    destination_id: number,
    for_company = false,
  ) => {
    const filterData = JSON.parse(query.filterData.replace(/'/g, '"'));
    let archived = false;
    filterData?.status?.map((status) => {
      if (status == 'archived') {
        archived = true;
      }
    });
    const offset = query.page;
    const limit = query.per_page;
    const status = query.status
    const where = await searchFilter(query, searchColumns);
    if (destination_id) where['destination_id'] = destination_id;
    const paginate: any = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }
    if (status) where['status'] = status
    // Create the status array based on the 'archived' flag
    const statusArray: mix_shipping_rate_status_enum[] = [
      mix_shipping_rate_status_enum.approved,
      mix_shipping_rate_status_enum.pending,
      mix_shipping_rate_status_enum.sent,
    ];

    // Add 'archived' status if the flag is true
    if (archived) {
      statusArray.push(mix_shipping_rate_status_enum.archived);
    }

    const where2 = {
      AND: [
        where,
        {
          status: for_company
            ? mix_shipping_rate_status_enum.approved
            : {
              in: statusArray,
            },
          company_id: query.company_id ?? null,
        },
      ],
    };

    return await Promise.all([
      this.prisma.mix_shipping_rates.count({ where: where2 }),
      this.prisma.mix_shipping_rates.findMany({
        ...paginate,
        orderBy: { id: 'desc' },
        where: where2,
        select: {
          id: true,
          // towing: true, // should be removed from db also
          shipping: true,
          clearance: true,
          TDS_charges: true,
          profit: true,
          location_id: true,
          loading_city_id: true,
          effective_date: true,
          destination_id: true,
          loading_cities: {
            select: {
              id: true,
              city_name: true,
              loading_states: {
                where: { parentId: { not: null } },
                select: {
                  id: true,
                  name: true,
                  parent: {
                    select: { id: true, name: true },
                    where: { parentId: null },
                  },
                },
              },
            },
          },
          locations: { select: { name: true, id: true } },
          destinations: { select: { name: true } },
          status: true,
          updated_at: true,
          created_at: true,
          ...create_update_by,
        },
      }),
    ])
      .then(async ([total, data]) => {
        const towingRates = await this.prisma.towing_rates.findMany({
          where: {
            OR: data.map((item) => ({
              location_id: item.location_id,
              loading_city_id: item.loading_city_id,
            })),
          },
          select: {
            id: true,
            location_id: true,
            loading_city_id: true,
            towing: true,
          },
        });

        return {
          result: true,
          page: offset,
          per_page: limit,
          total: total,
          data: data
            .filter(
              (item) =>
                item.location_id !== this.locationService.staticLocation.id,
            )
            .map((item) => {
              const towingRate = towingRates.find(
                (rate) =>
                  rate.location_id === item.location_id &&
                  rate.loading_city_id === item.loading_city_id,
              );
              return {
                ...item,
                towing: towingRate ? towingRate?.towing : 0,
              };
            }),
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);
  };

  approveMixShippingRate = async (
    dto: ApproveMixShippingRateDto,
    updated_by: number,
  ) => {
    const pendingRates = await this.prisma.mix_shipping_rates.findMany({
      where: {
        status:
          dto.status == 'approved'
            ? mix_shipping_rate_status_enum.sent
            : mix_shipping_rate_status_enum.pending,
        company_id: dto.company_id ?? null,
        destination_id: dto.destination_id,
      },
    });

    if (pendingRates.length == 0) {
      throw new BadRequestException({
        statusCode: 400,
        message:
          dto.status == 'approved'
            ? 'There is no sent rate to approved.'
            : 'There is no pending rate to sent.',
        error: 'Bad Request',
      });
    } else {
      await Promise.all([
        await this.prisma.mix_shipping_rates.updateMany({
          where: {
            status: mix_shipping_rate_status_enum.approved,
            company_id: dto.company_id ?? null,
            destination_id: dto.destination_id,
          },
          data: {
            status: mix_shipping_rate_status_enum.archived,
            updated_by,
          },
        }),
        await this.prisma.mix_shipping_rates.updateMany({
          where: {
            status:
              dto.status == 'approved'
                ? mix_shipping_rate_status_enum.sent
                : mix_shipping_rate_status_enum.pending,
            company_id: dto.company_id ?? null,
            destination_id: dto.destination_id,
          },
          data: {
            status:
              dto.status == 'approved'
                ? mix_shipping_rate_status_enum.approved
                : mix_shipping_rate_status_enum.sent,
            effective_date: dto.effectiveDate,
            updated_by,
          },
        }),
      ]);
      const data = {
        body: dto.body,
        subject: dto.subject,
        all_customers: false,
        company_ids: [dto.company_id],
        is_special: true,
        destination_id: dto.destination_id,
      };
      if (
        (dto.isEmailSent && dto.status == 'approved') ||
        dto.status == 'sent'
      ) {
        this.sendMail(data, updated_by);
      }
    }
    this.prisma.$disconnect();
    return {
      result: true,
    };
  };

  rejectMixShippingRate = async (
    dto: RejectMixShippingRateDto,
    updated_by: number,
  ) => {
    try {
      await Promise.all([
        await this.prisma.mix_shipping_rates.updateMany({
          where: {
            id: { in: dto.ids },
            status: mix_shipping_rate_status_enum.pending,
            company_id: dto.company_id ?? null,
          },
          data: {
            status: mix_shipping_rate_status_enum.rejected,
            updated_by,
          },
        }),
      ]);

      this.prisma.$disconnect();
      return {
        result: true,
      };
    } catch (error) {
      return catch_response(error);
    }
  };

  duplicateMixShippingRate = async (
    dto: DuplicateMixShippingRateDto,
    created_by: number,
  ) => {
    const dataToDuplicate = await this.prisma.mix_shipping_rates.findMany({
      where: {
        AND: [
          {
            status: mix_shipping_rate_status_enum.approved,
            id: { in: dto.ids },
            company_id: dto.company_id ?? null,
            deleted_at: null,
          },
        ],
      },
      select: {
        id: true,
        towing: true,
        shipping: true,
        clearance: true,
        TDS_charges: true,
        profit: true,
        location_id: true,
        loading_city_id: true,
        effective_date: true,
        destination_id: true,
        status: true,
      },
    });

    const existingRecords = await this.prisma.mix_shipping_rates.findMany({
      where: {
        status: mix_shipping_rate_status_enum.pending,
        deleted_at: null,
        company_id: dto.company_id ?? null,
        OR: dataToDuplicate.map((item) => ({
          location_id: item.location_id,
          loading_city_id: item.loading_city_id,
          destination_id: item.destination_id,
        })),
      },
    });

    const dataToActuallyDuplicate = dataToDuplicate.filter((item) => {
      const index = existingRecords.findIndex(
        (record) =>
          record.loading_city_id === item.loading_city_id &&
          record.location_id === item.location_id &&
          record.destination_id === item.destination_id,
      );
      return index === -1;
    });

    const duplicatedData = dataToActuallyDuplicate.map((item) => ({
      ...item,
      id: undefined,
      status: mix_shipping_rate_status_enum.pending,
      created_by,
      company_id: dto.company_id ?? null,
      effective_date: null,
    }));

    if (duplicatedData.length > 0) {
      await this.prisma.mix_shipping_rates.createMany({
        data: duplicatedData,
      });
    } else {
      throw new BadRequestException({
        statusCode: 400,
        message: 'There is already data with pending status.',
        error: 'Bad Request',
      });
    }

    return { result: true, message: 'Data duplicated successfully' };
  };

  duplicateMixShippingRateForCompany = async (
    dto: DuplicateMixShippingRateDto,
    created_by: number,
  ) => {
    const company = await this.prisma.companies.findUnique({
      where: { id: dto.company_id },
      select: { id: true, destination_id: true },
    });
    const dataToDuplicate = await this.prisma.mix_shipping_rates.findMany({
      where: {
        AND: [
          {
            status: mix_shipping_rate_status_enum.approved,
            id: { in: dto.ids },
            deleted_at: null,
            destination_id: company?.destination_id,
          },
        ],
      },
      select: {
        id: true,
        towing: true,
        shipping: true,
        clearance: true,
        TDS_charges: true,
        profit: true,
        location_id: true,
        loading_city_id: true,
        effective_date: true,
        destination_id: true,
        status: true,
      },
    });

    const existingRecords = await this.prisma.mix_shipping_rates.findMany({
      where: {
        status: mix_shipping_rate_status_enum.pending,
        deleted_at: null,
        company_id: dto.company_id,
        OR: dataToDuplicate.map((item) => ({
          location_id: item.location_id,
          loading_city_id: item.loading_city_id,
          destination_id: item.destination_id,
        })),
      },
    });

    const dataToActuallyDuplicate = dataToDuplicate.filter((item) => {
      const index = existingRecords.findIndex(
        (record) =>
          record.loading_city_id === item.loading_city_id &&
          record.location_id === item.location_id &&
          record.destination_id === item.destination_id,
      );
      return index === -1;
    });

    const duplicatedData = dataToActuallyDuplicate.map((item) => ({
      ...item,
      id: undefined,
      status: mix_shipping_rate_status_enum.pending,
      created_by,
      company_id: dto.company_id ?? null,
      effective_date: null,
    }));

    if (duplicatedData.length > 0) {
      await this.prisma.mix_shipping_rates.createMany({
        data: duplicatedData,
      });
    } else {
      throw new BadRequestException({
        statusCode: 400,
        message: 'There is already data with pending status.',
        error: 'Bad Request',
      });
    }

    return { result: true, message: 'Data duplicated successfully' };
  };

  async updateCostFields(
    id: number,
    dto: updateCostFieldsDto,
    updated_by: number,
  ) {
    const dataToUpdate: any = {
      [dto.field]: dto.value,
      updated_by,
    };

    await this.prisma.mix_shipping_rates.update({
      where: { id },
      data: dataToUpdate,
      select: {
        id: true,
        towing: true,
        shipping: true,
        clearance: true,
        TDS_charges: true,
        profit: true,
      },
    });
    return await this.findOne(id);
  }

  findAllCustomer = async (query: FindMixShippingRateDto) => {
    const { destination_id, is_mix_special_rate } = await this.prisma.companies
      .findUnique({
        where: { id: query.company_id },
        select: { destination_id: true, is_mix_special_rate: true },
      })
      .finally(() => this.prisma.$disconnect());
    if (!is_mix_special_rate)
      return await this.findAll(
        {
          ...query,
          company_id: null,
        },
        destination_id,
        true,
      );
    return await this.findAll(query, destination_id, true);
  };

  findOne = (id: number) =>
    this.prisma.mix_shipping_rates
      .findUnique({
        where: { id },
        select: {
          id: true,
          // towing: true,
          shipping: true,
          clearance: true,
          TDS_charges: true,
          profit: true,
          location_id: true,
          loading_city_id: true,
          effective_date: true,
          destination_id: true,
          loading_cities: {
            select: {
              id: true,
              city_name: true,
              loading_states: {
                where: { parentId: { not: null } },
                select: {
                  id: true,
                  name: true,
                  parent: {
                    select: { id: true, name: true },
                    where: { parentId: null },
                  },
                },
              },
            },
          },
          locations: { select: { name: true } },
          destinations: { select: { name: true } },
          status: true,
          updated_at: true,
          created_at: true,
          ...create_update_by,
        },
      })
      .then(async (data) => {
        const towingRate = await this.prisma.towing_rates.findFirst({
          where: {
            location_id: data?.location_id,
            loading_city_id: data?.loading_city_id,
          },
        });
        return {
          result: {
            ...data,
            towing: towingRate?.towing ?? 0,
          },
          data,
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);

  update = async (
    id: number,
    dto: CreateMixShippingRatesDto,
    updated_by: number,
  ) => {
    const res = await this.prisma.mix_shipping_rates
      .findFirst({
        where: {
          id: { not: { equals: id } },
          location_id: dto.location_id,
          loading_city_id: dto.loading_city_id,
          destination_id: dto.destination_id,
          company_id: dto.company_id ?? null,
          status: mix_shipping_rate_status_enum.pending,
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
    if (res)
      throw new BadRequestException({
        statusCode: 400,
        message: 'Rate already exist in specific location and city.',
        error: 'Bad Request',
      });

    if (dto.update_for_same_pol_pod) {
      await this.updateForSamePolPod(id, dto, updated_by);
    }

    return await this.prisma.mix_shipping_rates
      .update({
        where: { id },
        data: {
          shipping: dto.shipping,
          clearance: dto.clearance,
          TDS_charges: dto.TDS_charges,
          profit: dto.profit,
          location_id: dto.location_id,
          loading_city_id: dto.loading_city_id,
          destination_id: dto.destination_id,
          company_id: dto.company_id,
          updated_by,
        },
        select: {
          id: true,
          shipping: true,
          clearance: true,
          TDS_charges: true,
          profit: true,
          location_id: true,
          loading_city_id: true,
          effective_date: true,
          destination_id: true,
          loading_cities: {
            select: {
              id: true,
              city_name: true,
              loading_states: {
                where: { parentId: { not: null } },
                select: {
                  id: true,
                  name: true,
                  parent: {
                    select: { id: true, name: true },
                    where: { parentId: null },
                  },
                },
              },
            },
          },
          locations: { select: { name: true } },
          destinations: { select: { name: true } },
          status: true,
          updated_at: true,
          created_at: true,
          ...create_update_by,
        },
      })
      .then(async (data) => {
        const towingRate = await this.prisma.towing_rates.findFirst({
          where: {
            location_id: data?.location_id,
            loading_city_id: data?.loading_city_id,
          },
        });

        return {
          result: data ? true : false,
          data: { ...data, towing: towingRate?.towing ?? 0 },
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);
  };

  updateForSamePolPod = async (
    id: number,
    dto: CreateMixShippingRatesDto,
    updated_by: number,
  ) => {
    const record = await this.prisma.mix_shipping_rates.findFirst({
      where: {
        id: id,
      },
    });

    const updatePromises: Prisma.PrismaPromise<Prisma.BatchPayload>[] = [];

    if (record.shipping != dto.shipping) {
      updatePromises.push(
        this.prisma.mix_shipping_rates.updateMany({
          where: {
            location_id: record?.location_id,
            destination_id: record?.destination_id,
            // shipping: record.shipping,
            company_id: record.company_id,
            status: mix_shipping_rate_status_enum.pending,
          },
          data: {
            shipping: dto.shipping,
            updated_by,
          },
        }),
      );
    }
    if (record.clearance != dto.clearance) {
      updatePromises.push(
        this.prisma.mix_shipping_rates.updateMany({
          where: {
            location_id: record?.location_id,
            destination_id: record?.destination_id,
            // clearance: record.clearance,
            company_id: record.company_id,
            status: mix_shipping_rate_status_enum.pending,
          },
          data: {
            clearance: dto.clearance,
            updated_by,
          },
        }),
      );
    }
    if (record.clearance != dto.clearance) {
      updatePromises.push(
        this.prisma.mix_shipping_rates.updateMany({
          where: {
            location_id: record?.location_id,
            destination_id: record?.destination_id,
            // TDS_charges: record.TDS_charges,
            company_id: record.company_id,
            status: mix_shipping_rate_status_enum.pending,
          },
          data: {
            TDS_charges: dto.TDS_charges,
            updated_by,
          },
        }),
      );
    }
    if (record.profit != dto.profit) {
      updatePromises.push(
        this.prisma.mix_shipping_rates.updateMany({
          where: {
            location_id: record?.location_id,
            destination_id: record?.destination_id,
            // profit: record.profit,
            company_id: record.company_id,
            status: mix_shipping_rate_status_enum.pending,
          },
          data: {
            profit: dto.profit,
            updated_by,
          },
        }),
      );
    }

    await Promise.all(updatePromises);
  };

  softDeleteOne = (ids: number[], deleted_by: number) =>
    this.prisma.mix_shipping_rates
      .updateMany({
        where: { id: { in: ids.map(Number) } },
        data: { deleted_at: new Date(), deleted_by },
      })
      .then((data) => ({
        result: data.count > 0,
        message: `${data.count} records deleted successfully.`,
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect);

  restore = (id: number, updated_by: number) =>
    this.prisma.mix_shipping_rates.update({
      where: { id },
      data: { deleted_at: null, deleted_by: null, updated_by },
    });

  generateShipmentRates = async (destination_id: number, company_id?: number) =>
    await this.prisma.mix_shipping_rates
      .findMany({
        orderBy: [
          {
            loading_cities: { loading_states: { parent: { id: 'asc' } } },
          },
          { location_id: 'asc' },
        ],
        where: {
          destination_id,
          ...this.deleted,
          status: mix_shipping_rate_status_enum.approved,
          company_id: company_id ?? null,
        },
        select: {
          shipping: true,
          clearance: true,
          TDS_charges: true,
          profit: true,
          loading_city_id: true,
          location_id: true,
          effective_date: true,
          companies: { select: { name: true } },
          loading_cities: {
            select: {
              city_name: true,
              loading_states: {
                select: {
                  name: true,
                  parent: { select: { name: true }, where: { parentId: null } },
                },
                where: { parentId: { not: null } },
              },
            },
          },
          locations: { select: { name: true } },
          destinations: { select: { name: true } },
        },
      })
      .then(async (res) => {
        const towingRates = await this.prisma.towing_rates.findMany({
          where: {
            OR: res.map((item) => ({
              location_id: item.location_id,
              loading_city_id: item.loading_city_id,
            })),
          },
          select: {
            id: true,
            location_id: true,
            loading_city_id: true,
            towing: true,
          },
        });
        return res
          .filter(
            (item) =>
              item.location_id !== this.locationService.staticLocation.id,
          )
          .map((item) => {
            const towingRate = towingRates.find(
              (rate) =>
                rate.location_id === item.location_id &&
                rate.loading_city_id === item.loading_city_id,
            );
            return {
              ...item,
              towing: towingRate?.towing ?? 0,
            };
          });
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  generateShipmentRatesCustomer = async (
    user: any,
    destination_id,
    is_mix_special_rate,
  ) => {
    return await this.generateShipmentRates(
      destination_id,
      is_mix_special_rate ? user.company_id : null,
    );
  };

  sendMail = async (dto: SendMailDto, sender_id: number) => {
    const where = {
      mix: true,
      deleted_at: null,
      deleted_by: null,
      destination_id: dto.destination_id,
    };
    if (dto.all_customers === false) where['id'] = { in: dto.company_ids };

    try {
      const data = await this.prisma.loginables.findMany({
        where: {
          customer: {
            deleted_at: null,
            deleted_by: null,
            companies: {
              is_mix_special_rate: dto.is_special,
              ...where,
            },
          },
        },
        select: {
          email: true,
          customer_id: true,
          customer: {
            select: { companies: { select: { id: true, name: true } } },
          },
        },
      });

      await this.sendMailQueue(data, dto, dto.destination_id, sender_id);

      return { result: true };
    } catch (err) {
      catch_response(err);
    }
  };

  sendMailQueue = async (
    customers: any,
    dto: any,
    destination_id: number,
    sender_id: number,
  ) => {
    try {
      this.queue.add(
        'mix_shipping_rate_email',
        {
          customers,
          dto,
          destination_id,
          sender_id,
        },
        {
          attempts: 2,
          timeout: 100000, // Adjust the timeout based on your needs
        },
      );
    } catch (err) {
      console.log(err);
    }
  };

  uploadExcel = async (
    file,
    companyId: string,
    destinationId: string,
    user_id,
  ) => {
    if (!file) {
      throw new Error('File not uploaded.');
    }
    // Check if file is an Excel file
    const validMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];

    if (!validMimeTypes.includes(file.mimetype)) {
      throw new Error('Invalid file type. Only Excel files are allowed.');
    }

    // Process the Excel file
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheetData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    let successRow = 0;
    for (const row of sheetData) {
      const res = await this.prisma.mix_shipping_rates
        .findFirst({
          where: {
            location_id: row['Location Id'],
            loading_city_id: row['City ID'],
            destination_id: Number(destinationId),
            status: mix_shipping_rate_status_enum.pending,
            company_id: Number(companyId) ?? null,
          },
        })
        .then((res) => res)
        .catch((err) => catch_response(err))
        .finally(() => this.prisma.$disconnect());

      const towing = await this.prisma.towing_rates
        .findFirst({
          where: {
            loading_city_id: row['City ID'],
            location_id: row['Location Id'],
          },
        })
        .then((data) => ({ result: data ? true : false }))
        .catch((err) => catch_response(err))
        .finally(() => this.prisma.$disconnect());

      if (res || !towing) continue;

      await this.prisma.mix_shipping_rates.create({
        data: {
          towing: row['Towing'] ? row['Towing'] : null, // Example field
          shipping: row['Shipping'] ? row['Shipping'] : null, // Example field
          clearance: row['Clearance'] ? row['Clearance'] : null, // Example field
          TDS_charges: row['TDS Charges'] ? row['TDS Charges'] : null, // Example field
          profit: row['Profit'] ? row['Profit'] : null, // Example field
          location_id: row['Location Id'] ? row['Location Id'] : null, // Example field
          loading_city_id: row['City ID'] ? row['City ID'] : null, // Example field
          created_by: user_id,
          destination_id: Number(destinationId),
          status: 'pending',
          company_id: companyId ? Number(companyId) : null,
        },
      });
      successRow++;
    }

    return {
      message: 'File processed successfully',
      successRow,
    };
  };

  //  downloadMixShipping
  downloadMixShipping = async (id) => {
    const res = await this.prisma.mix_shipping_rates.findMany({
      where: {
        destination_id: id,
        NOT: { status: 'archived' },
        deleted_by: null,
      },
      select: {
        towing: true,
        shipping: true,
        clearance: true,
        TDS_charges: true,
        profit: true,
        location_id: true,
        loading_city_id: true,
        destination_id: true,
        locations: { select: { name: true } },
        destinations: { select: { name: true } },
        loading_cities: {
          select: {
            id: true,
            city_name: true,
            loading_states: {
              where: { parentId: { not: null } },
              select: {
                id: true,
                name: true,
                parent: {
                  select: { id: true, name: true },
                  where: { parentId: null },
                },
              },
            },
          },
        },
      },
    });

    // Create a new ExcelJS workbook and worksheet
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Report');
    // Transform data to include location and destination names
    const transformedData = res.map((item) => ({
      state_name: item.loading_cities?.loading_states.parent.name,
      branch_name: item.loading_cities?.loading_states.name,
      loading_city_name: item.loading_cities?.city_name || 'N/A',
      location_name: item.locations?.name || 'N/A', // Replace location_id with location name
      destination_name: item.destinations?.name || 'N/A', // Replace destination_id with destination name
      towing: item.towing,
      shipping: item.shipping,
      clearance: item.clearance,
      TDS_charges: item.TDS_charges,
      profit: item.profit,
      loading_city_id: item.loading_city_id,
      location_id: item.location_id,
    }));

    // Add header row
    worksheet.columns = [
      { header: 'State Name', key: 'state_name', width: 15 },
      { header: 'Branch Name', key: 'branch_name', width: 15 },
      { header: 'City Name', key: 'loading_city_name', width: 20 },
      { header: 'Location Name', key: 'location_name', width: 15 },
      { header: 'Destination', key: 'destination_name', width: 15 },
      { header: 'Towing', key: 'towing', width: 15 },
      { header: 'Shipping', key: 'shipping', width: 15 },
      { header: 'Clearance', key: 'clearance', width: 15 },
      { header: 'TDS Charges', key: 'TDS_charges', width: 15 },
      { header: 'Profit', key: 'profit', width: 15 },
      { header: 'City ID', key: 'loading_city_id', width: 20 },
      { header: 'Location Id', key: 'location_id', width: 20 },
    ];

    // Define border style using the correct type
    const borderStyle = {
      top: { style: 'thin' as const },
      left: { style: 'thin' as const },
      bottom: { style: 'thin' as const },
      right: { style: 'thin' as const },
    };

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF595959' }, // Background color
      };
      cell.font = {
        color: { argb: 'FFFFFFFF' }, // Font color
        bold: true, // Make the header bold
      };
      cell.alignment = { vertical: 'middle', horizontal: 'center' }; // Center align header
      cell.border = borderStyle; // Apply border to header cells
    });

    // Add data rows and apply styles
    transformedData.forEach((row) => {
      const newRow = worksheet.addRow(row);

      // Apply middle alignment and border to all cells in data rows
      newRow.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.border = borderStyle; // Apply border to each cell
      });
    });

    // Apply border to all cells in the worksheet
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.border = borderStyle; // Apply border to each cell
      });
    });

    // Generate buffer from workbook
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  };
  getCurrentMonthRange = () => {
    const now = new Date();
    const firstDay = `${now.getFullYear()}-${now.getMonth() + 1}-1`;
    const lastDay = `${now.getFullYear()}-${now.getMonth() + 1}-${new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()}`;
    return { firstDay, lastDay };
  };
  async getSpline(destination_id: number) {


    return await Promise.all([
      await this.getPNLOfCurrentMonth(destination_id),
      this.prisma.steamshiplines.findMany({
        where: {
          deleted_at: null,
          deleted_by: null,
        },
        select: {
          id: true,
          name: true,
          shiplines_booking_freight_rates: {
            where: {
              status: 'active',
              deleted_at: null,
              deleted_by: null,
            },
            select: {
              freight_rates: {
                where: {
                  point_of_destination_id: destination_id,
                  deleted_at: null,
                },
                select: {
                  id: true,
                  point_of_destination_id: true,
                  point_of_loading_id: true,
                  locations: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                  rate_types: {
                    where: {
                      deleted_at: null,
                      container_type: "hc40"
                    },
                    select: {
                      id: true,
                      container_type: true,
                      freight_rate_category: true,
                      rate_name: true,
                      rate_value: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),
      this.prisma.destinations.findMany({
        where: { id: destination_id },
        select: {
          id: true,
          name: true,

        },
      }),
      this.prisma.locations.findMany({
        where: { deleted_at: null },
        select: {
          id: true,
          name: true,
          container_loading_cost: true,
        },
      }),
      this.getCustomPNLs()
    ])
      .then(([PNL, shiplines, destinations, loading_costs, customPNL]) => {
        return {
          result: true,
          PNL,
          shiplines,
          destinations,
          loading_costs,
          customPNL: customPNL.result ? customPNL.data : {}
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  }
  getPNLOfCurrentMonth = async (destination_id) => {
    const { firstDay, lastDay } = this.getCurrentMonthRange();

    return await this.getPNLBetween(
      { startDate: firstDay, endDate: lastDay },
      { destination_id },
    );
  };

  getPNLBetween = async (
    { startDate, endDate }: { startDate: string; endDate: string },
    { destination_id }: { destination_id: number },
  ) => {
    // Container sizes array
    const containerSizes = ['40 HC'];

    // Generate SQL for size-specific averages and quantities dynamically
    const sizeSpecificSQL = containerSizes
      .map(
        (size) => `
        -- Averages for REGULAR and SPOT
        SUM(CASE WHEN b.type = 'REGULAR' AND b.size = '${size}' THEN b.cost * b.qty ELSE 0 END)::int /
        NULLIF(SUM(CASE WHEN b.type = 'REGULAR' AND b.size = '${size}' THEN b.qty ELSE 0 END), 0)::int AS regular_rate_${size.replace(
          ' ',
          '',
        )}_average,
        SUM(CASE WHEN b.type = 'SPOT' AND b.size = '${size}' AND b.cost > 10 THEN b.cost * b.qty ELSE 0 END)::int /
        NULLIF(SUM(CASE WHEN b.type = 'SPOT' AND b.size = '${size}' AND b.cost > 10 THEN b.qty ELSE 0 END), 0)::int AS spot_rate_${size.replace(
          ' ',
          '',
        )}_average,

        -- Quantities for REGULAR and SPOT
        SUM(CASE WHEN b.type = 'REGULAR' AND b.size = '${size}' THEN b.qty ELSE 0 END)::int AS regular_rate_${size.replace(
          ' ',
          '',
        )}_qty,
        SUM(CASE WHEN b.type = 'SPOT' AND b.size = '${size}' AND b.cost > 10 THEN b.qty ELSE 0 END)::int AS spot_rate_${size.replace(
          ' ',
          '',
        )}_qty`,
      )
      .join(',\n');

    // Main SQL query
    const query = `
    SELECT
        v.port_of_loading AS location_id,
        v.steamshipline_id AS shipline_id,
        -- Regular and Spot averages
        SUM(CASE WHEN b.type = 'REGULAR' THEN b.cost * b.qty ELSE 0 END)::int /
        NULLIF(SUM(CASE WHEN b.type = 'REGULAR' THEN b.qty ELSE 0 END), 0)::int AS regular_average,
        SUM(CASE WHEN b.type = 'SPOT' AND b.cost > 10 THEN b.cost * b.qty ELSE 0 END)::int /
        NULLIF(SUM(CASE WHEN b.type = 'SPOT' AND b.cost > 10 THEN b.qty ELSE 0 END), 0)::int AS spot_average,

        -- Size-specific averages and quantities
        ${sizeSpecificSQL},

        -- Total quantities
        SUM(CASE WHEN b.type = 'REGULAR' THEN b.qty ELSE 0 END)::int AS regular_qty,
        SUM(CASE WHEN b.type = 'SPOT' AND b.cost > 10 THEN b.qty ELSE 0 END)::int AS spot_qty,
        SUM(CASE WHEN b.cost > 10 THEN b.qty ELSE 0 END)::int AS total_qty
    FROM vessels v
    LEFT JOIN bookings b ON b.vessel_id = v.id
    WHERE v.etd BETWEEN '${startDate}' AND '${endDate}'
      AND b.port_of_discharge = ${destination_id}
      AND b.type IN ('REGULAR', 'SPOT')
    GROUP BY v.port_of_loading, v.steamshipline_id;
    `;

    // Execute the query with parameters
    return await this.db.runRaw(query);
  };

  getCustomPNLs = async () => {
    try {
      const data = await this.prisma.kv_store.findMany({
        where: { group: 'mix_shipping_rates_custom_pnl' },
        select: {
          key: true,
          value: true,
        },
      });

      const pnls = data.reduce(
        (acc, item) => {
          acc[item.key] = Number(item.value);
          return acc;
        },
        {} as Record<string, number>,
      );

      return { result: true, data: pnls };
    } catch (error) {
      catch_response(error);
    }
  };

  setCustomPNLs = async (
    query: UpsertCustomPNLsDtoArray,
    created_by: number,
  ) => {
    try {
      const queries = query.updates.map(
        async ({ key, value }) =>
          await this.prisma.kv_store.upsert({
            where: { key },
            create: {
              key,
              value: `${value}`,
              group: 'mix_shipping_rates_custom_pnl',
              created_by,
            },
            update: {
              value: `${value}`,
              updated_by: created_by,
            },
            select: {
              key: true,
              value: true,
            },
          }),
      );

      const results = await Promise.all(queries);
      await this.redis.del(`/api/mix-shipping-rates/custom-pnls`);
      return { result: true, data: results };
    } catch (error) {
      catch_response(error);
    }
  };
}
