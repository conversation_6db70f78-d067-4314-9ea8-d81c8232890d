import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseArrayPipe,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from 'src/Commons/decorators/user.decorator';
import { AdminGuard } from 'src/Commons/guards/jwt.guard';
import PermissionGuard from 'src/Commons/guards/permissions.guard';
import {
  CreateLoadBranchDto,
  CreateLoadCitiesDto,
  CreateMixShippingRatesDto,
  CreateLoadStateDto,
  DuplicateMixShippingRateDto,
  FindMixShippingRateDto,
  SendMailDto,
  updateCostFieldsDto,
  DownloadPdfDto,
  ApproveMixShippingRateDto,
  RejectMixShippingRateDto,
} from 'src/Admin_APIs/mix-shipping-rates/dto/mix-shipping-rates.dto';
import { Response } from 'express';
import { generatePdf } from 'src/Commons/helpers/generatePDF';
import mix_shipping_rates from 'src/Commons/pdf_templates/mix_shipping_rates_template';
import { MixShippingRateService } from './mix-shipping-rates.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpsertCustomPNLsDtoArray } from '../rate-analysis/shipping-rates/dto/ra-shipping-rates.dto';
import { CacheInterceptor } from '@nestjs/cache-manager';

@Controller('mix-shipping-rates')
@ApiTags('Mix Shipping Rates')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class MixShippingRateController {
  constructor(private readonly loadingCostsService: MixShippingRateService) { }

  @Post()
  @UseGuards(PermissionGuard('create_shipping_rates'))
  async create(@Body() dto: CreateMixShippingRatesDto, @User() user: any) {
    return await this.loadingCostsService.create(dto, user.loginable_id);
  }

  @Post('createState')
  //@UseGuards(PermissionGuard('create_shipping_rates'))
  async createState(@Body() dto: CreateLoadStateDto, @User() user: any) {
    return await this.loadingCostsService.createState(dto, user.loginable_id);
  }

  @Post('createBranch')
  //@UseGuards(PermissionGuard('create_shipping_rates'))
  async createBranch(@Body() dto: CreateLoadBranchDto, @User() user: any) {
    return await this.loadingCostsService.createBranch(dto, user.loginable_id);
  }

  @Post('createCity')
  //@UseGuards(PermissionGuard('create_shipping_rates'))
  async createCity(@Body() dto: CreateLoadCitiesDto, @User() user: any) {
    return await this.loadingCostsService.createCity(dto, user.loginable_id);
  }

  @Post('sendMail')
  @UseGuards(PermissionGuard('send_email'))
  async sendMail(@Body() dto: SendMailDto, @User() user: any) {
    return await this.loadingCostsService.sendMail(dto, user.loginable_id);
  }

  @Get('states')
  async getAllStates() {
    return await this.loadingCostsService.getStates();
  }

  @Get('branches/:stateId')
  async getAllBranches(@Param('stateId') stateId: string) {
    return await this.loadingCostsService.getBranches(+stateId);
  }

  @Get('cities/:branchId')
  async getAllCities(@Param('branchId') branchId: string) {
    return await this.loadingCostsService.getCities(+branchId);
  }

  @Get('find/:destinationId')
  @UseGuards(PermissionGuard('view_shipping_rates'))
  async findAll(
    @Param('destinationId') destinationId: string,
    @Query() query: FindMixShippingRateDto,
  ) {
    return await this.loadingCostsService.findAll(query, +destinationId);
  }

  @Get("shipline-location")
  @UseInterceptors(CacheInterceptor)
  async getCurrentMonthRange(@Query() query: { destination_id: number }) {
    return await this.loadingCostsService.getSpline(+query.destination_id)
  }

  @Put('custom-pnls')
  async setCustomPNLS(@Body() query: UpsertCustomPNLsDtoArray, @User() user: any) {
    return await this.loadingCostsService.setCustomPNLs(query, user.loginable_id,)
  }
  @Patch('approve')
  async approveMixShippingRate(
    @Body() dto: ApproveMixShippingRateDto,
    @User() user: any,
  ) {
    return await this.loadingCostsService.approveMixShippingRate(
      dto,
      user.loginable_id,
    );
  }

  @Patch('reject')
  async rejectMixShippingRate(
    @Body() dto: RejectMixShippingRateDto,
    @User() user: any,
  ) {
    return await this.loadingCostsService.rejectMixShippingRate(
      dto,
      user.loginable_id,
    );
  }

  @Post('duplicate-mix-shipping-rates')
  async duplicateMixShippingRate(
    @Body() dto: DuplicateMixShippingRateDto,
    @User() user: any,
  ) {
    return dto.duplicate_from_general_to_company
      ? await this.loadingCostsService.duplicateMixShippingRateForCompany(
        dto,
        user.loginable_id,
      )
      : await this.loadingCostsService.duplicateMixShippingRate(
        dto,
        user.loginable_id,
      );
  }

  @Patch('update-cost-fields/:id')
  async updateCostFields(
    @Param('id') id: string,
    @Body() dto: updateCostFieldsDto,
    @User() user: any,
  ) {
    return await this.loadingCostsService.updateCostFields(
      +id,
      dto,
      user.loginable_id,
    );
  }

  @Get('shipmentRatePdf/:destinationId')
  async generateShipmentRates(
    @Param('destinationId') destinationId: string,
    @Query() query: DownloadPdfDto,
    @Res() res: Response,
  ) {
    const result = await this.loadingCostsService.generateShipmentRates(
      +destinationId,
      query.company_id ?? null,
    );
    const template = await mix_shipping_rates(result, query.selectedColumns, {
      currency: query.currency,
      exchangeRate: query.exchangeRate,
      note: query.note,
      customNote: query.customNote,
    });
    const pdf = await generatePdf(template, {
      top: '30px',
      bottom: '0px',
      right: '0px',
      left: '0px',
    });
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="shipping-rates.pdf"',
    );
    res.send(pdf);
  }

  @Get(':id')
  @UseGuards(PermissionGuard('view_shipping_rates'))
  async findOne(@Param('id') id: string) {
    return await this.loadingCostsService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(PermissionGuard('update_shipping_rates'))
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: CreateMixShippingRatesDto,
    @User() user: any,
  ) {
    return await this.loadingCostsService.update(id, dto, user.loginable_id);
  }

  @Delete(':ids')
  @UseGuards(PermissionGuard('delete_shipping_rates'))
  async remove(@Param('ids', ParseArrayPipe) ids: number[], @User() user: any) {
    return await this.loadingCostsService.softDeleteOne(ids, user.loginable_id);
  }

  // upload excel
  @Post('upload-excel')
  @UseGuards(PermissionGuard('create_shipping_rates'))
  @UseInterceptors(
    FileInterceptor('file', { limits: { fileSize: 10 * 1024 * 1024 } }),
  )
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
    @Body('destinationId') destinationId: string,
    @Body('company_id') companyId: string,
    @User() user: any,
  ) {
    return await this.loadingCostsService.uploadExcel(
      file,
      companyId,
      destinationId,
      user.loginable_id,
    );
  }

  @Get('excel/download/:id')
  async downloadMixShippingRates(
    @Param('id') id: number,
    @Res() res: Response,
  ) {
    try {
      const excelBuffer =
        await this.loadingCostsService.downloadMixShipping(id);
      res.header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.header('Content-Disposition', 'attachment; filename=inventory.xlsx');
      res.send(excelBuffer);
    } catch (error) {
      console.error('Error generating Excel:', error);

      res.status(500).send('Internal Server Error');
    }
  }
}
