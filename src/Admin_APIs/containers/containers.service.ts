import {
  driver_status,
  pl_status,
  // rate_apply_date_enum,
  tracking_status,
  Prisma,
} from '@prisma/client';
import * as Handlebars from 'handlebars';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as moment from 'moment';
import * as puppeteer from 'puppeteer';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import {
  SearchPaginateDto,
  WithoutFilterPaginateDto,
  atToContainerDto,
  dateDto,
} from 'src/Commons/dto/searchPaginateDto.dto';
import { CommonFields } from 'src/Commons/services/common.fields';
import { container_status } from '@prisma/client';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { ordersBy } from 'src/Commons/helpers/ordersby.helper';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import {
  ContainerSummary,
  CreateContainerDto,
  UpdateContainerDto,
  changeContainerStatus,
  changeContainerStatusIds,
  changeProfitLossStatus,
  clearanceInvoiceLinkDto,
  filterContainer,
  changeUaeProfitLossStatus,
  changeBalanceDifference,
  changePrelimProfitLossStatus,
  addContainerTransaction,
  ContainerProfitLoss,
  UpdateTitleAESStatus,
  UpdatePullIngateDateDto,
  UpdateContainerDriverDto,
  ContainerChargesDTO,
  UploadContainerImageDto,
  ContainerReport,
} from './dto/container.dto';
import { DbService } from 'src/prisma/prisma.service';
import { check_permissions } from 'src/Commons/helpers/check_permissions.helper';
import customerShipmentBillOfLoading from 'src/Commons/pdf_templates/c_shipment_bill_of_loading';
import { EventGateWay } from 'src/websocket/event.gateway';
import { FirebaseService } from 'src/firebase/firebase.service';
import { generatePdf } from 'src/Commons/helpers/generatePDF';
import { add_tracking } from 'src/Commons/helpers/tracking.helper';
import { UploadVehicleFileDto } from '../vehicles/vehicles/dto/vehicles.dto';
import {
  deleteFromMinio,
  uploadToMinio,
} from 'src/Commons/services/file-service';
import ReleaseDocument from 'src/Commons/pdf_templates/relaseDocumentPdf';
import { GoogleApisService } from 'src/Commons/services/google-drive/google-apis.service';
import { PDFDocument } from 'pdf-lib';
import * as sharp from 'sharp';
import { createLog } from 'src/Commons/helpers/log_functions.helper';
import { CheckPermissionsService } from '../users/check-permissions.service';
import { paymentSelect } from 'src/Commons/helpers/customerPaymentTransactions.helper';
import { google } from 'googleapis';
import * as fs from 'fs/promises';
import * as JSZip from 'jszip';
import { container_images } from 'src/Admin_APIs/activity_log/models/vehicle_images_logs';
import { UpdateCostsDto } from './dto/update-costs.dto';
import reactEmail from 'src/Commons/helpers/reactEmail.helper';
import ShipmentInventoryEmailTemplate from 'emails/Shimpment/ShipmentInventoryEmailTemplate';
import ShipmentArrivalNoticeEmailTemplate from 'emails/Shimpment/ShipmentArrivalNoticeEmailTemplate';
import ShipmentLoadingPlaneEmailTemplate from 'emails/Shimpment/ShipmentLoadingPlaneEmailTemplate';

const is_send: boolean = process.env.SEND_MAIL === 'true';

const create_update_delete_by = CommonFields([
  'users_containers_created_byTousers',
  'users_containers_updated_byTousers',
  'users_containers_deleted_byTousers',
]);
const create_update_by = CommonFields([
  'users_containers_created_byTousers',
  'users_containers_updated_byTousers',
]);

const transaction_by = CommonFields(['users_containers_transaction_byTousers']);
const BATCH_SIZE = 10;

const getDateRange = (date, type) => {
  const resultDate = new Date(date);
  if (type === 'start') {
    resultDate.setHours(0, 0, 0, 0);
  } else if (type === 'end') {
    resultDate.setHours(23, 59, 59, 999);
  }
  return resultDate.toISOString();
};

const searchColumns = [
  'container_number',
  'aes_itn_number',
  'tracking_contatiner',
  'bill_of_loading_number',
  'seal_number',
  'invoice_number',
  'int@@amount',
  'date@@loading_date',
  'bookings.booking_number',
  'bookings.date@@eta',
  'companies.name',
  'companies.many@@customers.fullname',
  'actions',
  'yards_location.name',
  'bookings.vessels.name',
  'bookings.parent.booking_number',
  'bookings.vessels.date@@brd',
  'bookings.vessels.date@@erd',
  'bookings.vessels.date@@si_cut_off',
  'bookings.vessels.date@@etd',
  // 'loaders.name',
  'inGateDriver.name',
  'pullDriver.name',
  'date@@ingate_date',
  'date@@pull_date',
];
@Injectable()
export class ContainersService {
  private prisma: typeof DbService.prisma;
  constructor(
    private readonly db: DbService,
    private readonly socketGateway: EventGateWay,
    private readonly firebaseService: FirebaseService,
    private readonly googleApisService: GoogleApisService,
    private readonly checkPermissionsService: CheckPermissionsService,
  ) {
    this.prisma = DbService.prisma;
  }

  create = async (dto: CreateContainerDto, created_by: number) => {
    // Check for duplicate AES ITN number
    if (dto.aes_itn_number) {
      const existingContainer = await this.prisma.containers.findFirst({
        where: { aes_itn_number: dto.aes_itn_number },
      });

      if (existingContainer) {
        throw new HttpException(
          {
            result: false,
            message: 'AES ITN Number already exists.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (dto.booking_suffix) {
      const existingContainer = await this.prisma.containers.findFirst({
        where: {
          booking_suffix: dto.booking_suffix,
          booking_id: dto.booking_id,
          deleted_at: null,
        },
      });

      if (existingContainer) {
        throw new HttpException(
          {
            result: false,
            message: 'Booking suffix already exists.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const containerCosts: any = Object.values(
      Object.assign({}, dto.container_costs),
    );
    const uniqueContainerCosts = this.removeDuplicateCost(containerCosts);
    if (dto.booking_id && dto.container_number != '') {
      const isSplitContainer = await this.isSplitContainer(
        dto.container_number,
        dto.booking_id,
      );

      if (isSplitContainer) {
        throw new HttpException(
          {
            result: false,
            message: 'Split Containers not Allowed.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    const selected_booking = await this.prisma.bookings.findUnique({
      where: { id: dto.booking_id },
      include: {
        _count: {
          select: {
            containers: {
              where: { deleted_at: null },
            },
          },
        },
        vessels: {
          select: {
            locations: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });
    if (selected_booking) {
      const allowed_bookings = [
        'GAJEBBK',
        'TXJEBBK',
        'NJJEBBK',
        'MDJEBBK',
        'CAJEBBK',
        'PotiBK',
        'PotiTXBK',
        'PotiHAZ',
        'CASALALAHBK',
        'CAPOTIBK',
        'MDJEBBK HAZ',
        'GAJEBBK HAZ',
        'NJJEBBK HAZ',
        'TXJEBBK HAZ',
        'CAJEBBK HAZ',
        'PotiTXBKHAZ',
        'HAZTXJEBBK',
      ];
      if (!allowed_bookings.includes(selected_booking.booking_number)) {
        if (selected_booking._count.containers >= selected_booking.qty) {
          throw new HttpException(
            {
              result: false,
              message: `The number of containers (${selected_booking._count.containers}) has reached or exceeded the booking quantity (${selected_booking.qty}).`,
              status: HttpStatus.BAD_REQUEST,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    }

    return this.prisma.containers
      .create({
        data: {
          container_number: dto.container_number,
          pin_out: dto.container_number,
          loading_date: new Date(dto.loading_date),
          actions: dto.actions,
          no_units_load: dto.no_units_load,
          company_id: dto.company_id,
          clearance_invoice_link: dto.clearance_invoice_link,
          ingate: dto.ingate,
          shipping_document_id: dto.shipping_document_id,
          booking_id: dto.booking_id,
          booking_suffix: dto.booking_suffix,
          aes_itn_number: dto.aes_itn_number,
          tracking_contatiner: dto.tracking_contatiner,
          container_id_update_date: dto.container_id_update_date,
          bill_of_loading_number: dto.bill_of_loading_number,
          seal_number: dto.seal_number,
          invoice_number: dto.invoice_number,
          amount: dto.amount,
          photo_link: dto.photo_link,
          pin_in: dto.pin_in,
          loading_instruction: dto.loading_instruction,
          documentation_instruction: dto.documentation_instruction,
          // invisible_for_customer: dto.invisible_for_customer,
          yard_location_id: dto.yard_location_id,
          title_status: dto.title_status,
          aes_status: dto.aes_status,
          ingate_date: dto.ingate_date,
          pull_date: dto.pull_date,
          ingate_driver_id: dto.ingate_driver,
          ingate_driver_bonus: dto.ingate_driver_bonus,
          pull_driver_id: dto.pull_driver,
          pull_driver_bonus: dto.pull_driver_bonus,

          pull_driver_notes: dto.pull_driver_notes,
          ingate_driver_notes: dto.ingate_driver_notes,

          status_changed_at: new Date(),
          loaders: {
            connect: dto.loaders.map((item) => ({ id: item })),
          },
          loader_remark: dto.loader_remark,
          load_combination_type: dto.load_combination_type,
          container_costs: {
            create: uniqueContainerCosts.map((n) => ({
              name: n.name,
              value: parseFloat(n.value),
              description: n.description || null,
              created_by: created_by,
            })),
          },
          aes_filling_link: dto.aes_filling_link,
          created_by,
          updated_by: created_by,
          of_loading_video: dto.of_loading_video,
          of_loading_photo: dto.of_loading_photo,
          container_charges: {
            createMany: {
              data: dto.container_charges?.map((charge) => ({
                name: charge.name,
                amount: +charge.amount,
                cost: +charge.cost,
                remark: charge.remark,
                category: charge.category,
                created_at: new Date(),
                created_by: created_by,
              })),
            },
          },
        },
        include: {
          container_costs: {
            select: { name: true, value: true, description: true },
          },
        },
      })
      .then(async (r) => {
        // Generate and update invoice number
        const locationName = selected_booking.vessels?.locations?.name ?? '';
        const invoiceNumber = `PGL${locationName.slice(-2)}${r?.id}`;
        await this.prisma.containers.update({
          where: { id: r?.id },
          data: { invoice_number: invoiceNumber },
        });

        add_tracking([r?.id], container_status.at_loading, false);

        return this.findOne(r.id);
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  async uploadImage(
    dto: UploadVehicleFileDto,
    loginable: any,
    cover: Express.Multer.File,
  ) {
    try {
      const { data } = await this.findOne(dto?.id);

      if (data?.cover_photo) {
        await deleteFromMinio(data?.cover_photo, { isProtected: false });
      }

      const destination = 'uploads/containers/cover_photo';
      const uploadedFile = await uploadToMinio(cover[0], destination, {
        isProtected: false,
      });
      return this.prisma.containers
        .updateMany({
          where: {
            id: dto.id,
          },
          data: {
            cover_photo: `${uploadedFile.bucket}/${uploadedFile.key}`,
            updated_by: loginable.loginable_id,
          },
        })
        .then((data) => {
          if (data.count > 0)
            return {
              result: true,
              message: `${data.count} records have been updated!`,
            };
          else
            return { result: false, message: 'No records have been updated.' };
        })
        .catch((err) => catch_response(err));
    } catch (error) {
      catch_response(error);
    }
  }

  async deleteContainerImage(id, loginable: any) {
    try {
      const { data } = await this.findOne(id);

      if (data?.cover_photo) {
        await deleteFromMinio(data?.cover_photo, { isProtected: false });
      }
      return this.prisma.containers
        .updateMany({
          where: {
            id: id,
          },
          data: {
            cover_photo: null,
            updated_by: loginable.loginable_id,
          },
        })
        .then((data) => {
          if (data.count > 0)
            return {
              result: true,
              message: `${data.count} records have been updated!`,
            };
          else
            return { result: false, message: 'No records have been updated.' };
        })
        .catch((err) => catch_response(err));
    } catch (error) {
      catch_response(error);
    }
  }

  updateLoadingDate = async (id: number, dto: dateDto, loginable: any) => {
    return this.prisma.containers
      .update({
        where: { id },
        data: {
          updated_by: loginable.loginable_id,
          loading_date: dto.date_param,
        },
      })
      .then((data: any) => {
        return data;
      })
      .catch((err: any) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  /**
   * Filter Params:
   *  {
   *     "container_number":"21lkj45",
   *     "created_at": {"from":"2023-07-31T19:30:00.000Z", "to":"2023-08-30T19:30:00.000Z"},
   *     "bookings.int@@cost":"100",
   *     "bookings.vessels.etd": {"from":"2023-07-31T19:30:00.000Z", "to":"2023-08-30T19:30:00.000Z"}
   *     "bookings.vessels.int@@port_of_loading": "12354"
   * }
   */
  async findAll(query: filterContainer, login_id: number, user_id: number) {
    console.log('container query', query);
    const offset = query.page;
    const limit = query.per_page;
    const status = query.status;
    const locationId = query.locationId;
    const yard_location_id = query.yard_location_id;
    const searchOptions =
      query.searchOptions && JSON.parse(query.searchOptions.replace(/'/g, '"'));
    const searchBy = searchOptions?.length > 0 ? searchOptions : searchColumns;

    const where: any = await searchFilter(query, searchBy);
    const isExport = query.isExport;
    const exportTotal = query.exportTotal;
    const exportType = query.exportType;

    // Handle date ranges for full-day coverage
    if (query.filterData) {
      const filterData = JSON.parse(query.filterData);

      if (filterData['bookings.vessels.si_cut_off']) {
        const { from, to } = filterData['bookings.vessels.si_cut_off'];
        const start = from ? new Date(from) : undefined;
        const end = to ? new Date(to + 'T23:59:59.999Z') : undefined;

        where.bookings = {
          ...where.bookings,
          vessels: {
            ...(where.bookings?.vessels || {}),
            si_cut_off: {
              ...(start && { gte: start }),
              ...(end && { lte: end }),
            },
          },
        };
      }
    }
    // END OF NEW FILTERS

    if (isExport) {
      createLog({
        log_name: 'pgl',
        description: 'Export',
        subject_type: 'User',
        subject_id: user_id,
        causer_type: 'User',
        causer_id: user_id,
        properties: { exportTotal: exportTotal, exportType: exportType },
      });
    }

    let hasMatch = [];
    if (query.search && query?.search?.length >= 6) {
      hasMatch = await this.prisma.containers.findMany({
        where: {
          OR: [
            { container_number: { contains: query.search } },
            { bookings: { booking_number: { contains: query.search } } },
          ],
        },
        select: { id: true },
      });
    }

    const permissions =
      await this.checkPermissionsService.getUsersPermissionsByGroupNames(
        user_id,
        ['containers'],
      );

    const restrictions =
      this.checkPermissionsService.getContainersRestrictions(permissions);

    if (restrictions) {
      const newRest = {
        OR: [
          ...restrictions.OR,
          hasMatch.length ? { id: { in: hasMatch.map((e) => e.id) } } : {},
        ],
      };
      if (where['AND']) {
        where['AND'].push(newRest);
      } else {
        where['AND'] = [newRest];
      }
    }

    // const orderBy = await ordersBy(query);
    const orderBy: any = {
      id: 'desc',
    };

    if (query.search) {
      const [bookingNumber, bookingSuffix] = query.search.split('-');
      if (bookingSuffix && !isNaN(+bookingSuffix)) {
        where['OR'] = [
          ...where['OR'],
          {
            AND: [
              {
                bookings: {
                  booking_number: {
                    contains: bookingNumber.trim(),
                    mode: 'insensitive',
                  },
                },
              },
              {
                booking_suffix: {
                  contains: bookingSuffix.trim(),
                  mode: 'insensitive',
                },
              },
            ],
          },
        ];
      }
    }

    if (query.mix_container || query.full_container) {
      if (!where.AND) {
        where.AND = [];
      }
      if (query.mix_container) {
        where.AND.push({
          mix_shipping_invoices: {
            some: {
              id: {
                gt: 0,
              },
              deleted_at: null,
            },
          },
        });
      }
      if (query.full_container) {
        where.AND.push({
          mix_shipping_invoices: {
            none: {},
          },
        });
      }
    }

    where['status'] = status ? status : { not: container_status.clearance };
    if (yard_location_id) where['yard_location_id'] = yard_location_id;

    if (locationId)
      where['bookings'] = { vessels: { port_of_loading: locationId } };

    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    } else {
      // this is the maximum number of records that can be returned
      paginate['take'] = 32000;
    }

    if (query.pending_arrival) {
      where['pending_arrival'] = true;
    }

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany({
        where,
        ...paginate,
        orderBy,
        select: {
          id: true,
          yard_location_id: true,
          company_id: true,
          photo_link: true,
          container_number: true,
          seal_number: true,
          container_number_assigned_at: true,
          booking_suffix: true,
          booking_id: true,
          pin_in: true,
          pin_out: true,
          actions: true,
          no_units_load: true,
          status: true,
          status_changed_at: true,
          title_status: true,
          aes_status: true,
          // aes_itn_number: true,
          aes_itn_number: true,
          loading_date: true,
          tracking_contatiner: true,
          invoice_number: true,
          clearance_invoice_link: true,
          created_at: true,
          updated_at: true,
          loadplan_sent_at: true,
          ingate_date: true,
          pending_arrival: true,

          pull_date: true,
          arrival_notice_sent_at: true,
          shipment_type_approved: true,
          cover_photo: true,
          transaction_no: true,
          transaction_at: true,
          aes_filling_link: true,
          of_loading_video: true,
          of_loading_photo: true,
          bill_of_loadings: { select: { master_bk: true } },
          vehicles: {
            select: {
              customer_id: true,
              halfcut_status: true,
              vehicle_costs: {
                select: {
                  towing_cost: true,
                  dismantal_cost: true,
                  ship_cost: true,
                  title_charge: true,
                  other_cost: true,
                  pgl_storage_costs: true,
                },
              },
              vehicle_charges: {
                where: {
                  deleted_at: null,
                },
              },
              vehicle_towings: {
                select: { tow_amount: true },
              },
            },
          },
          bookings: {
            select: {
              free_days: true,
              id: true,
              size: true,
              booking_number: true,
              eta: true,
              si: true,
              cost: true,
              port_of_discharge: true,
              type: true,
              parent_id: true,
              parent: {
                select: {
                  booking_number: true,
                  id: true,
                },
              },
              destinations: { select: { id: true, name: true } },
              vessels: {
                select: {
                  id: true,
                  etd: true,
                  brd: true,
                  brd_status: true,
                  si_cut_off: true,
                  name: true,
                  steamshiplines: { select: { name: true } },
                  locations: { select: { id: true, name: true } },
                  port_of_loading: true,
                },
              },
            },
          },
          companies: {
            select: {
              name: true,
              join_date: true,
              customers: {
                select: {
                  fullname: true,
                  secondary_email: true,
                  loginable: { select: { email: true } },
                },
              },
            },
          },
          yards_location: { select: { name: true } },
          invoices: {
            select: {
              invoice_amount: true,
            },
          },
          mix_shipping_invoices: {
            select: {
              container_id: true,
              type: true,
            },
          },
          _count: {
            select: {
              containers_images: {
                where: { size: 250 }, // optional filter for count too
              },
            },
          },
          container_costs: { select: { id: true, name: true, value: true } },
          mix_shipping_containers: { select: { id: true } },
          clear_logs: { select: { id: true } },
          pullDriver: { select: { id: true, name: true } },
          inGateDriver: { select: { id: true, name: true } },
          ingate_driver_bonus: true,
          pull_driver_bonus: true,
          ingate_driver_notes: true,
          pull_driver_notes: true,
          ...transaction_by,
          loaders: { select: { id: true, name: true } },
          loader_remark: true,
          load_combination_type: true,
          container_charges: {
            where: {
              deleted_at: null,
            },
          },
          ...create_update_by,
        },
      }),
    ])
      .then(async ([total, data]) => {
        // add logic for removing columns of on_hand_with_load
        // const filteredData = data.map((op) => {
        //   if (!op.container_number) {
        //     if (op.bookings && op.bookings.vessels) {
        //       op.bookings.vessels.etd = null;
        //       op.bookings.vessels.name = null;
        //     }
        //     if (op.bookings) {
        //       op.bookings.eta = null;
        //     }
        //   }
        //   return op;
        // });
        // end logic for removing columns of on_hand_with_load

        const dataWithTruckingCost = await this.addContainersTruckingCost(data);

        if (limit > 0)
          return {
            result: true,
            page: offset,
            per_page: limit,
            total,
            data: dataWithTruckingCost,
          };
        else
          return {
            result: true,
            total,
            data: dataWithTruckingCost,
          };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  }

  changePendingArrivalStatus = async (ids, query, userId, loginable_id) => {
    const numericIds = ids?.ids?.map((id) => Number(id));
    await this.prisma.containers
      .updateMany({
        where: { id: { in: numericIds } },
        data: {
          pending_arrival: false,
          updated_by: loginable_id,
          updated_at: new Date(),
        },
      })
      .catch((err) => catch_response(err));

    return await this.findAll(query, userId, loginable_id);
  };

  getContainerImages = async (id) => {
    return await this.prisma.containers_images
      .findMany({
        where: { container_id: id, size: 250 },
        include: {
          users_container_image_created_by: {
            select: {
              id: true,
              fullname: true,
            },
          },
        },
      })
      .then((res) => {
        return res;
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  async storeContainerImages(dto: UploadContainerImageDto, fileKey, loginable) {
    const url = fileKey.replace(/\/1024\//, '/250/');
    await this.prisma.containers_images
      .create({
        data: {
          container_id: Number(dto?.id),
          name: dto?.fileName,
          size: 250,
          url: url,
          created_by: loginable.loginable_id,
        },
      })
      .finally(() => this.prisma.$disconnect());
  }

  deleteContainerImages = async (ids: number[], user) => {
    const imagesId = ids.map((id) => Number(id));
    await container_images('Shipment', imagesId, user);
    return await this.prisma.containers_images
      .deleteMany({
        where: {
          id: {
            in: imagesId,
          },
        },
      })
      .then((res) => res)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  async findSpecificVehicles(vehicleIds: number[]) {
    const convertedVehicleIds = vehicleIds.map((id) => Number(id));
    return await Promise.all([
      this.prisma.containers.findMany({
        where: {
          id: { in: convertedVehicleIds },
        },
        select: {
          id: true,
          yard_location_id: true,
          company_id: true,
          photo_link: true,
          container_number: true,
          booking_suffix: true,
          booking_id: true,
          pin_in: true,
          pin_out: true,
          actions: true,
          no_units_load: true,
          status: true,
          status_changed_at: true,
          title_status: true,
          aes_status: true,
          aes_itn_number: true,
          loading_date: true,
          tracking_contatiner: true,
          invoice_number: true,
          clearance_invoice_link: true,
          created_at: true,
          updated_at: true,
          loadplan_sent_at: true,
          ingate_date: true,

          pull_date: true,
          arrival_notice_sent_at: true,
          shipment_type_approved: true,
          cover_photo: true,
          transaction_no: true,
          transaction_at: true,
          aes_filling_link: true,
          of_loading_video: true,
          of_loading_photo: true,
          vehicles: {
            select: {
              customer_id: true,
              halfcut_status: true,
              vehicle_costs: {
                select: {
                  towing_cost: true,
                  dismantal_cost: true,
                  ship_cost: true,
                  title_charge: true,
                  other_cost: true,
                  pgl_storage_costs: true,
                },
              },
              vehicle_towings: {
                select: { tow_amount: true },
              },
            },
          },
          bookings: {
            select: {
              free_days: true,
              id: true,
              size: true,
              booking_number: true,
              eta: true,
              si: true,
              cost: true,
              port_of_discharge: true,
              type: true,
              parent_id: true,
              parent: {
                select: {
                  booking_number: true,
                  id: true,
                },
              },
              destinations: { select: { id: true, name: true } },
              vessels: {
                select: {
                  id: true,
                  etd: true,
                  brd: true,
                  brd_status: true,
                  si_cut_off: true,
                  name: true,
                  steamshiplines: { select: { name: true } },
                  locations: { select: { id: true, name: true } },
                  port_of_loading: true,
                },
              },
            },
          },
          companies: {
            select: {
              name: true,
              join_date: true,
              customers: {
                select: {
                  fullname: true,
                  secondary_email: true,
                  loginable: { select: { email: true } },
                },
              },
            },
          },
          yards_location: { select: { name: true } },
          invoices: {
            select: {
              invoice_amount: true,
            },
          },
          mix_shipping_invoices: {
            select: {
              container_id: true,
              type: true,
            },
          },
          _count: {
            select: {
              containers_images: {
                where: { size: 250 }, // optional filter for count too
              },
            },
          },
          container_costs: { select: { id: true, name: true, value: true } },
          mix_shipping_containers: { select: { id: true } },
          clear_logs: { select: { id: true } },
          pullDriver: { select: { id: true, name: true } },
          inGateDriver: { select: { id: true, name: true } },
          ingate_driver_bonus: true,
          pull_driver_bonus: true,
          ingate_driver_notes: true,
          pull_driver_notes: true,
          loaders: { select: { id: true, name: true } },
          loader_remark: true,
          load_combination_type: true,
        },
      }),
    ])
      .then(async ([data]) => {
        const dataWithTruckingCost = await this.addContainersTruckingCost(data);

        return {
          result: true,
          data: dataWithTruckingCost,
        };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  }

  downloadGoogleDriveImages = async (query: any, user: any) => {
    const token = await this.googleApisService.findToken(user.loginable_id);

    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({
      access_token: token['access_token'],
      refresh_token: token['refresh_token'],
    });
    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    const folderId = query.folderId;
    const res = await drive.files.list({
      q: `mimeType contains 'image/' and '${folderId}' in parents`,
      fields: 'files(id, name)',
      pageSize: 100,
      spaces: 'drive',
      supportsAllDrives: true,
      corpora: 'allDrives',
      includeItemsFromAllDrives: true,
    });

    const files = res.data.files;
    const zip = new JSZip();

    for (const file of files) {
      const fileId = file.id;
      const fileName = file.name;
      const fileRes = await drive.files.get(
        { fileId, alt: 'media' },
        { responseType: 'stream' },
      );
      const chunks = [];
      fileRes.data.on('data', (chunk) => {
        chunks.push(chunk);
      });
      await new Promise((resolve, reject) => {
        fileRes.data.on('end', resolve);
        fileRes.data.on('error', reject);
      });
      const buffer = Buffer.concat(chunks);
      zip.file(fileName, buffer);
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    const zipFilePath = 'images.zip';
    await fs.writeFile(zipFilePath, zipBuffer);

    return zipFilePath;
  };

  addContainersTruckingCost = async (data) => {
    try {
      if (data.length == 0) return data;
      const q = `
          SELECT
            containers.id,
            containers.pull_driver_bonus,
            pull.amount as pull_amount,
            containers.ingate_driver_bonus,
            ingate.amount as ingate_amount
          FROM containers
            LEFT JOIN (
              SELECT
                drivers.id,
                driver_amounts_per_move.effective_from as effective_from,
                driver_amounts_per_move.effective_to as effective_to,
                (driver_amounts_per_move.amount)::float as amount
              FROM drivers
                LEFT JOIN driver_amounts_per_move ON drivers.id = driver_amounts_per_move.driver_id
                AND driver_amounts_per_move.deleted_at IS NULL
              GROUP BY
                drivers.id,
                driver_amounts_per_move.effective_from,
                driver_amounts_per_move.effective_to,
                driver_amounts_per_move.amount
            ) AS pull
              ON containers.pull_driver_id = pull.id
              AND containers.pull_date BETWEEN pull.effective_from
              AND COALESCE(pull.effective_to, TIMESTAMP 'infinity')
            LEFT JOIN (
              SELECT
                drivers.id,
                driver_amounts_per_move.effective_from as effective_from,
                driver_amounts_per_move.effective_to as effective_to,
                (driver_amounts_per_move.amount)::float as amount
              FROM drivers
                LEFT JOIN driver_amounts_per_move ON drivers.id = driver_amounts_per_move.driver_id
                AND driver_amounts_per_move.deleted_at IS NULL
              GROUP BY
                drivers.id,
                driver_amounts_per_move.effective_from,
                driver_amounts_per_move.effective_to, driver_amounts_per_move.amount
            ) AS ingate
              ON containers.ingate_driver_id = ingate.id
              AND containers.ingate_date BETWEEN ingate.effective_from
              AND COALESCE(ingate.effective_to, TIMESTAMP 'infinity')
          WHERE containers.id IN (${data.map((item) => item.id).join(',')})
        `;
      const res = await this.db.runRaw(q);
      return data.map((item) => {
        const truckingCost = res.find((cost) => cost.id == item.id);
        return {
          ...item,
          trucking_cost: truckingCost,
        };
      });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return data;
    }
  };

  async getDraftCheck(query: SearchPaginateDto) {
    const offset = query.page;
    const limit = query.per_page;
    const where = await searchFilter(query, searchColumns);
    // const orderBy = await ordersBy(query);
    const orderBy = {
      id: 'desc',
    };

    const property: any = {
      where,
      orderBy,
      select: {
        id: true,
        aes_itn_number: true,
        seal_number: true,
        amount: true,
        loadplan_sent_at: true,
        documentation_instruction: true,
        companies: { select: { name: true } },
        bill_of_loadings: { select: { master_bk: true } },
        bookings: {
          select: {
            booking_number: true,
            size: true,
            port_of_discharge: true,
          },
        },
        container_number: true,
        booking_suffix: true,
        status: true,
        vehicles: {
          select: {
            vin: true,
            year: true,
            make: true,
            model: true,
            color: true,
            weight: true,
            price: true,
            receiver_name: true,
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            companies: {
              select: { name: true },
            },
            vehicle_towings: {
              select: { tow_amount: true },
            },
          },
        },
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  }

  getArchive = async (
    query: WithoutFilterPaginateDto,
    location: number,
    title = false,
  ) => {
    const offset: number = query.page;
    const limit: number = query.per_page;

    const where = await searchFilter(query, searchColumns);
    const orderBy = await ordersBy(query);
    if (title) where['bookings'] = { vessels: { port_of_loading: location } };
    where['status'] = {
      in: [container_status.at_loading, container_status.pending],
    };
    const property = {
      where,
      orderBy,
      select: {
        bookings: {
          select: {
            booking_number: true,
          },
        },
        container_number: true,
        booking_suffix: true,
        aes_itn_number: true,
        actions: true,
        invoice_number: true,
        companies: { select: { name: true } },
        id: true,
        vehicles: { select: { vin: true } },
        bill_of_loadings: { select: { master_bk: true } },
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

    /* try {
      const total = await this.getTotal({ where });
      const data = await this.getAll(property);
      if (limit > 0)
        return { result: true, page: offset, per_page: limit, total, data };
      else return { result: true, total, data };
    } catch (err) {
      catch_response(err);
    } */
  };

  getAtLoadingAtDock = (query: atToContainerDto) =>
    this.prisma.containers
      .findMany({
        orderBy: { id: 'desc' },
        where: {
          deleted_at: null,
          deleted_by: null,
          status: {
            in: [
              container_status.pending,
              container_status.at_loading,
              container_status.at_the_dock,
            ],
          },
          AND: [
            {
              /*  bookings: {
                booking_number: { not: null },
              }, */
              bookings: {
                booking_number: { not: null },
                //port_of_discharge: query.vehicles_destination,
                vessels: {
                  port_of_loading: query.point_of_loading,
                },
              },
            },
            /* {
              OR: [
                {
                  bookings: {
                    port_of_discharge: query.vehicles_destination,
                  },
                },
                {
                  bookings: {
                    vessels: {
                      port_of_loading: query.point_of_loading,
                    },
                  },
                },
              ],
            }, */
          ],
        },
        select: {
          id: true,
          container_number: true,
          booking_suffix: true,
          no_units_load: true,
          yards_location: { select: { id: true, name: true } },
          companies: { select: { id: true, name: true } },
          bill_of_loadings: { select: { master_bk: true } },
          bookings: {
            select: {
              id: true,
              booking_number: true,
              size: true,
              destinations: { select: { id: true, name: true } },
              vessels: {
                select: {
                  name: true,
                  port_of_loading: true,
                  locations: { select: { id: true, name: true } },
                },
              },
            },
          },
        },
      })
      .then((r) => ({ result: r ? true : false, data: r }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getSubmitSI = async (query: WithoutFilterPaginateDto) => {
    const todayDate = new Date().toISOString().slice(0, 10);
    const offset = query.page;
    const limit = query.per_page;
    const where = await searchFilter(query, searchColumns);
    const orderBy = await ordersBy(query);
    where['status'] = {
      in: [
        container_status.at_loading,
        container_status.reserved,
        container_status.checked,
        container_status.final_checked,
        container_status.at_the_dock,
      ],
    };
    where['bookings'] = { vessels: { si_cut_off: new Date(todayDate) } };
    const property = {
      where,
      orderBy,
      select: {
        bookings: {
          select: {
            booking_number: true,
            size: true,
            vessels: {
              select: {
                name: true,
                etd: true,
                erd: true,
                locations: { select: { id: true, name: true } },
              },
            },
            destinations: {
              where: { deleted_at: null },
              select: { name: true },
            },
          },
        },
        container_number: true,
        vehicles: {
          select: {
            yards_location: { select: { name: true } },
            note: true,
            pol_locations: { select: { name: true } },
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            vehicle_towings: {
              select: { tow_amount: true },
            },
          },
        },
        companies: { select: { name: true } },
        no_units_load: true,
        loading_date: true,
        invoice_number: true,
        bill_of_loadings: { select: { master_bk: true } },
      },
    };
    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }
    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  async getReport(params: ContainerReport) {
    const months: string[] = [];
    const monthConditions: string[] = [];
    const filteDate = {
      ingate: 'con.ingate_date',
      pol_date: 'b.eta',
      pod_date: 'vess.etd',
    };
    for (let i = 0; i < 12; i++) {
      const date = new Date();
      date.setDate(1);
      date.setMonth(date.getMonth() - i);

      const label = date
        .toLocaleString('en-US', {
          month: 'short',
          year: 'numeric',
        })
        .toLowerCase()
        .replace(' ', '_'); // e.g. jun_2025

      const monthStart = `DATE_TRUNC('month', CURRENT_DATE - INTERVAL '${i} month')`;
      const monthEnd = `DATE_TRUNC('month', CURRENT_DATE - INTERVAL '${i} month') + INTERVAL '1 month' - INTERVAL '1 second'`;

      const clause = `SUM(CASE
      WHEN ${filteDate[params.selectedFilterDate]} >= ${monthStart} AND
           ${filteDate[params.selectedFilterDate]} <= ${monthEnd}
      THEN 1 ELSE 0 END)::int AS ${label}`;

      const condition = `${label} > 0`;

      months.push(clause);
      monthConditions.push(condition);
    }

    const filters: string[] = [];
    if (params.filterData?.company_id?.length) {
      const ids = params.filterData.company_id.map(Number).join(',');
      filters.push(`com.id IN (${ids})`);
    }

    if (params.filterData?.location_id?.length) {
      const ids = params.filterData.location_id.map(Number).join(',');
      filters.push(`vess.port_of_loading IN (${ids})`);
    }

    if (params.filterData?.destination_id?.length) {
      const ids = params.filterData.destination_id.map(Number).join(',');
      filters.push(`b.port_of_discharge IN (${ids})`);
    }
    const filter = filters.length ? `AND ${filters.join(' AND ')}` : undefined;

    const shipmentCompany = `
    WITH raw_data AS (
      SELECT
        com.id,
        com.name,
        ${months.join(',\n      ')}
      FROM companies com
      LEFT JOIN containers con ON con.company_id = com.id AND con.deleted_at IS NULL
      JOIN bookings          b   ON b.id = con.booking_id
      JOIN vessels            vess on b.vessel_id =vess.id
      WHERE com.deleted_at IS NULL ${params.search ? `AND com.name ILIKE '%' || '${params.search}' || '%'` : ''}
            ${filter ? filter : ''}
      GROUP BY com.id, com.name
    )
    SELECT * FROM raw_data
    WHERE ${monthConditions.join(' OR ')};
  `.trim();
    const shipmentPod = `	SELECT *
            FROM (
             SELECT
              	d.id  ,
              	d.name ,
               ${months.join(',\n      ')}
            FROM destinations         d
            JOIN bookings             b    ON b.port_of_discharge    = d.id
            JOIN containers          con   ON b.id = con.booking_id
            JOIN vessels            vess on b.vessel_id =vess.id
          WHERE b.deleted_at IS NULL ${params.search ? `AND d.name ILIKE '%' || '${params.search}' || '%'` : ''}
            AND con.deleted_at IS NULL
            AND b.created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 month'
            GROUP BY d.id, d.name
            ) pivoted
          WHERE ${monthConditions.join(' OR ')}
          ORDER BY name;`;

    const shpimentPol = `SELECT *
            FROM (
             SELECT
              loc.id  ,
              loc.name ,
               ${months.join(',\n      ')}
            FROM locations           loc
            JOIN vessels          vess   ON vess.port_of_loading    = loc.id
            JOIN bookings         b     ON b.vessel_id    = vess.id
            JOIN containers          con   ON b.id = con.booking_id

          WHERE b.deleted_at IS NULL  ${params.search ? `AND loc.name ILIKE '%' || '${params.search}' || '%'` : ''}

            AND con.deleted_at IS NULL
            AND b.created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 month'
            GROUP BY loc.id, loc.name
            ) pivoted
          WHERE ${monthConditions.join(' OR ')}
          ORDER BY name;`.trim();

    const vehicleCompany = `
          SELECT *
            FROM (
             SELECT
              vc.id   AS vehicle_company_id,
              vc.name AS name,
               ${months.join(',\n        ')}
            FROM vehicles            v
            JOIN containers          con   ON v.container_id = con.id
            JOIN companies           vc  ON v.company_id   = vc.id
            JOIN companies           com  ON con.company_id   = com.id
            JOIN bookings 			b    on con.booking_id =b.id
            JOIN vessels            vess on b.vessel_id =vess.id
          WHERE v.deleted_at IS NULL
             ${filter ? filter : 'AND com.id IN (806, 577,158)'}
             ${params.search ? `AND vc.name ILIKE '%' || '${params.search}' || '%'` : ''}
            AND con.deleted_at IS NULL
            AND v.created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 month'
            GROUP BY vc.id, vc.name
            ) pivoted
          WHERE ${monthConditions.join(' OR ')}
          ORDER BY name;
          `.trim();

    const vehiclePod = `	SELECT *
            FROM (
             SELECT
              d.id  ,
              d.name ,
             ${months.join(',\n      ')}
            FROM vehicles            v
            JOIN containers          con   ON v.container_id = con.id
            JOIN destinations          d  ON v.destination_id   = d.id
            JOIN bookings 			   b on con.booking_id =b.id
            JOIN vessels            vess on b.vessel_id =vess.id

          WHERE v.deleted_at IS NULL
            AND con.deleted_at IS NULL
            AND v.created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 month'
            GROUP BY d.id, d.name
            ) pivoted
          WHERE ${monthConditions.join(' OR ')}
          ORDER BY name;`.trim();
    const vehilcePol = `
          SELECT *
            FROM (
             SELECT
              loc.id  ,
              loc.name,
               ${months.join(',\n      ')}
            FROM vehicles            v
            JOIN containers          con   ON v.container_id = con.id
            JOIN locations          loc  ON v.point_of_loading    = loc.id
            JOIN bookings 			   b on con.booking_id =b.id
            JOIN vessels            vess on b.vessel_id =vess.id

          WHERE v.deleted_at IS NULL
            AND con.deleted_at IS NULL
            AND v.created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 month'
            GROUP BY loc.id, loc.name
            ) pivoted
             WHERE ${monthConditions.join(' OR ')}
          ORDER BY name;`.trim();

    let excuteQuery = shipmentCompany;
    if (params.status === 'shipment' && params.selectedFilter === 'company') {
      excuteQuery = shipmentCompany;
    } else if (
      params.status === 'shipment' &&
      params.selectedFilter === 'pod'
    ) {
      excuteQuery = shipmentPod;
    } else if (
      params.status === 'shipment' &&
      params.selectedFilter === 'pol'
    ) {
      excuteQuery = shpimentPol;
    } else if (
      params.status === 'vehicle' &&
      params.selectedFilter === 'company'
    ) {
      excuteQuery = vehicleCompany;
    } else if (params.status === 'vehicle' && params.selectedFilter === 'pod') {
      excuteQuery = vehiclePod;
    } else if (params.status === 'vehicle' && params.selectedFilter === 'pol') {
      excuteQuery = vehilcePol;
    }
    try {
      const data = await this.db.runRaw(excuteQuery);
      return {
        result: true,
        data,
        total: data?.length,
      };
    } catch (err) {
      catch_response(err);
    }
  }

  getSummary = async (query: ContainerSummary) => {
    const {
      page,
      per_page: limit,
      search,
      locationId,
      status,
      order = 'ASC',
      filterData: filter,
      arrivalNotice,
      arrivalNoticeDays,
      arrivalNoticeType,
    } = query;

    const offset = (page - 1) * limit;
    const order_column = 'on_the_way';

    let conds = search ? ` AND companies.name ILIKE '%${search}%'` : '';
    let destinationCond = '';

    if (locationId) {
      conds += ` AND vessels.port_of_loading = ${locationId} `;
      destinationCond += ` AND vessels.port_of_loading = ${locationId} `;
    }

    const addFilterCondition = (field, values) => {
      if (values && values.length) {
        const joinedValues = values.map((val) => `'${val}'`).join(',');
        conds += ` AND ${field} IN (${joinedValues})`;
        destinationCond += ` AND ${field} IN (${joinedValues})`;
      }
    };

    if (filter) {
      addFilterCondition('con.company_id', filter.company_id);
      addFilterCondition('con.status', filter.status);
      addFilterCondition('con.booking_id', filter.booking_id);
      addFilterCondition('con.created_by', filter.created_by);
      addFilterCondition('con.updated_by', filter.updated_by);
      addFilterCondition('con.ingate_driver_id', filter.ingate_driver_id);
      addFilterCondition(
        'bookings.port_of_discharge',
        filter.port_of_discharge,
      );
      addFilterCondition('vessels.steamshipline_id', filter.steamshipline_id);
      addFilterCondition('con.pull_driver_id', filter.pull_driver_id);
      addFilterCondition('con.yard_location_id', filter.yard_location_id);
      addFilterCondition('vessels.id', filter.vessel_id);

      const addDateCondition = (field, dateRange) => {
        if (dateRange) {
          if (dateRange.from) {
            const fromDate = getDateRange(dateRange.from, 'start');
            conds += ` AND DATE(${field}) >= '${fromDate}'`;
            destinationCond += ` AND DATE(${field}) >= '${fromDate}'`;
          }
          if (dateRange.to) {
            const toDate = getDateRange(dateRange.to, 'end');
            conds += ` AND DATE(${field}) <= '${toDate}'`;
            destinationCond += ` AND DATE(${field}) <= '${toDate}'`;
          }
        }
      };

      addDateCondition('con.loading_date', filter.loading_date);
      addDateCondition('vessels.etd', filter.etd_date);
      addDateCondition('con.ingate_date', filter.ingate_date);
      addDateCondition('bookings.eta', filter.eta_date);
      addDateCondition('con.pull_date', filter.pull_date);
      addDateCondition('con.created_at', filter.created_at);
      addDateCondition('con.updated_at', filter.updated_at);
      addDateCondition('vessels.si_cut_off', filter.si_cut_off);

      if (filter.ingate) {
        conds += ` AND con.ingate = ${filter.ingate}`;
        destinationCond += ` AND con.ingate = ${filter.ingate}`;
      }
    }

    if (arrivalNotice) {
      const today = new Date();
      const nextDays = new Date(today);
      nextDays.setDate(today.getDate() + arrivalNoticeDays);
      const formattedToday = today.toISOString().slice(0, 10);
      const formattedNextDays = nextDays.toISOString().slice(0, 10);
      conds += ` AND DATE(bookings.eta) BETWEEN '${arrivalNoticeDays > 0 ? formattedToday : formattedNextDays}' AND '${arrivalNoticeDays > 0 ? formattedNextDays : formattedToday}'`;
      if (arrivalNoticeType === 'sent') {
        conds += ` AND con.arrival_notice_sent_at IS NOT NULL`;
      } else if (arrivalNoticeType === 'not_sent') {
        conds += ` AND con.arrival_notice_sent_at IS NULL`;
      }
    }

    const having = status
      ? ` HAVING SUM(CASE WHEN con.status = '${status}' THEN 1 ELSE 0 END)::int > 0 `
      : '';

    const paginate = ` ORDER BY ${order_column} ${order} LIMIT ${limit} OFFSET ${offset}`;

    const locations = [
      { id: 1, name: 'ga' },
      { id: 2, name: 'tx' },
      { id: 4, name: 'ca' },
      { id: 5, name: 'nj' },
      { id: 6, name: 'md' },
      { id: 7, name: 'nc' },
      { id: 9, name: 'fl' },
      { id: 10, name: 'halfcut_tx' },
    ];

    const ports = locations
      .map(
        (row) =>
          `SUM(CASE WHEN vessels.port_of_loading = ${row.id} THEN 1 ELSE 0 END)::int AS ${row.name}`,
      )
      .join(',');

    const commonQuery = `
    SUM(CASE WHEN con.status = '${container_status.final_checked}' THEN 1 ELSE 0 END)::int AS ${container_status.final_checked},
    SUM(CASE WHEN con.status = '${container_status.pending}' THEN 1 ELSE 0 END)::int AS ${container_status.pending},
    SUM(CASE WHEN con.status = '${container_status.checked}' THEN 1 ELSE 0 END)::int AS ${container_status.checked},
    SUM(CASE WHEN con.status = '${container_status.at_the_dock}' THEN 1 ELSE 0 END)::int AS ${container_status.at_the_dock},
    SUM(CASE WHEN con.status = '${container_status.at_loading}' THEN 1 ELSE 0 END)::int AS ${container_status.at_loading},
    SUM(CASE WHEN con.status = '${container_status.on_the_way}' THEN 1 ELSE 0 END)::int AS ${container_status.on_the_way},
    SUM(CASE WHEN con.status = '${container_status.arrived}' THEN 1 ELSE 0 END)::int AS ${container_status.arrived},
    SUM(CASE WHEN con.status = '${container_status.cbp_inspection}' THEN 1 ELSE 0 END)::int AS ${container_status.cbp_inspection},
    ${ports}
    FROM companies
    LEFT JOIN containers con ON con.company_id = companies.id AND con.deleted_at IS NULL
    LEFT JOIN bookings ON con.booking_id = bookings.id AND bookings.deleted_at IS NULL
    LEFT JOIN vessels ON vessels.id = bookings.vessel_id AND vessels.deleted_at IS NULL
    WHERE companies.deleted_at IS NULL ${conds}
    `;

    const q = `SELECT companies.id, companies.name, companies.destination_id, ${commonQuery} GROUP BY companies.id ${having}`;

    const destination = `
    SELECT destinations.name, COUNT(*)::int AS count FROM containers con
    LEFT JOIN bookings ON con.booking_id = bookings.id AND bookings.deleted_at IS NULL
    LEFT JOIN destinations ON bookings.port_of_discharge = destinations.id AND destinations.deleted_at IS NULL
    LEFT JOIN vessels ON bookings.vessel_id = vessels.id AND vessels.deleted_at IS NULL
    WHERE destinations.name IS NOT NULL AND con.deleted_at IS NULL ${destinationCond} GROUP BY destinations.name;
    `;

    try {
      const data = await this.db.runRaw(q + paginate);
      const td = await this.db.runRaw(`SELECT ${commonQuery}`);
      const t = await this.db.runRaw(
        `SELECT COUNT(*)::int AS all FROM (${q}) AS sub`,
      );
      const d = await this.db.runRaw(destination);
      const p = limit > 0 ? { page, per_page: limit } : {};

      return {
        result: true,
        ...p,
        total: t[0].all,
        dest: d,
        data: { totalData: td[0], data },
      };
    } catch (err) {
      catch_response(err);
    }
  };

  getprofitLoss = async (query: ContainerProfitLoss) => {
    const limit = query.per_page;
    const offset = query.page;
    const filterData = JSON.parse(query.filterData);
    const filters: any = [];
    if (filterData?.invoice_status) {
      filters.push({
        invoices: {
          some: {
            status: {
              in: filterData?.invoice_status,
            },
          },
        },
      });

      delete filterData.invoice_status;
      query.filterData = JSON.stringify(filterData);
    }

    const where = await searchFilter(query, [
      'container_number',
      'companies.name',
      'bookings.booking_number',
      'bookings.parent.booking_number',
    ]);

    const filterNoneClearance: any = {
      clear_logs: {
        some: {
          id: {
            gt: 0,
          },
          deleted_at: null,
        },
      },
    };

    if (filters.length > 0) {
      if (!where.AND) {
        where.AND = [];
      }
      where.AND = [...where.AND, ...filters];
    }

    const orderBy = await ordersBy(query);

    if (query.search) {
      const [bookingNumber, bookingSuffix] = query.search.split('-');
      if (bookingSuffix && !isNaN(+bookingSuffix)) {
        delete where.OR;
        where['AND'] = [
          {
            bookings: {
              booking_number: {
                contains: bookingNumber.trim(),
                mode: 'insensitive',
              },
            },
          },
          {
            booking_suffix: {
              contains: bookingSuffix.trim(),
              mode: 'insensitive',
            },
          },
        ];
      }
    }

    if (query.beforeEtd2025) {
      where['etd_2025_check'] = {
        not: true,
      };
    } else {
      if (query.haveBookLoadCost || query.onlyMixContainers) {
        if (!where.AND) {
          where.AND = [];
        }
        if (query.haveBookLoadCost) {
          where.AND.push(
            {
              bookings: {
                cost: {
                  gt: 0,
                },
              },
            },
            {
              container_costs: {
                some: {
                  name: 'loading_cost',
                  value: {
                    gt: 0,
                  },
                },
              },
            },
            {
              vehicles: {
                some: {
                  vehicle_costs: {
                    ship_cost: {
                      gt: 0,
                    },
                  },
                },
              },
            },
            {
              mix_shipping_invoices: {
                none: {},
              },
            },
          );

          if (query.filterNoneClearance) {
            where.AND.push(filterNoneClearance);
          }
        }
        if (query.onlyMixContainers) {
          if (!where.AND) {
            where.AND = [];
          }
          where.AND.push({
            mix_shipping_invoices: {
              some: {
                id: {
                  gt: 0,
                },
                deleted_at: null,
              },
            },
          });
        }
        if (query.haveBookLoadCostReviewed || query.onlyMixContainersReviewed) {
          if (query.uae || query.usa) {
            if (query.usa) {
              where.AND.push({
                pl_status: 'reviewed',
              });
            } else {
              where.AND.push({
                uae_pl_status: 'reviewed',
              });
            }
          } else {
            where.AND.push({
              pl_status: 'reviewed',
              uae_pl_status: 'reviewed',
            });
          }
          if (query.filterNoneClearance) {
            where.AND.push(filterNoneClearance);
          }
        } else {
          if (query.uae || query.usa) {
            if (query.usa) {
              where.AND.push({
                pl_status: 'pending',
              });
            } else {
              where.AND.push({
                uae_pl_status: 'pending',
              });
            }
          } else {
            where.AND.push({
              uae_pl_status: 'pending',
            });
          }
        }
      }
      if (
        query.allUsaFull ||
        query.allUsaMix ||
        query.allUaeFull ||
        query.allUaeMix
      ) {
        if (!where.AND) {
          where.AND = [];
        }
        if (query.allUsaFull || query.allUaeFull) {
          where.AND.push({
            mix_shipping_invoices: {
              none: {},
            },
          });
        }
        if (query.allUsaMix || query.allUaeMix) {
          where.AND.push({
            mix_shipping_invoices: {
              some: {
                id: {
                  gt: 0,
                },
                deleted_at: null,
              },
            },
          });
        }
      }
      where['etd_2025_check'] = true;
    }

    const property = {
      where,
      orderBy,
      select: {
        id: true,
        container_number: true,
        yards_location: true,
        status: true,
        pl_status: true,
        uae_pl_status: true,
        pl_balance_diff: true,
        prelim_pl_status: true,
        booking_suffix: true,
        companies: {
          select: {
            name: true,
          },
        },
        container_costs: {
          select: {
            name: true,
            value: true,
          },
        },
        invoices: {
          select: {
            invoice_amount: true,
            payment_received: true,
            discount: true,
            status: true,
            title_charge_visible: true,
            towing_charge_visible: true,
            payments: paymentSelect(),
          },
        },
        vehicles: {
          select: {
            storage_cost: true,
            load_type: true,
            title_cost: true,
            dismantle_costs: true,
            damage_cost: true,
            other_costs: true,
            vehicle_costs: {
              select: {
                dismantal_cost: true,
                pgl_storage_costs: true,
                title_charge: true,
                towing_cost: true,
                ship_cost: true,
                other_cost: true,
              },
            },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            vehicle_towings: {
              select: {
                tow_amount: true,
              },
            },
          },
        },
        bookings: {
          select: {
            id: true,
            eta: true,
            cost: true,
            port_of_discharge: true,
            booking_number: true,
            tds_cost: true,
            parent_id: true,
            parent: {
              select: {
                booking_number: true,
                id: true,
              },
            },
            destinations: {
              select: {
                name: true,
              },
            },
            vessels: {
              select: {
                etd: true,
                port_of_loading: true,
                locations: { select: { name: true } },
                steamshiplines: { select: { name: true } },
              },
            },
          },
        },
        mix_shipping_invoices: {
          select: {
            container_id: true,
            type: true,
            exchange_rate: true,
            mix_shipping_vehicles: {
              select: {
                clearance: true,
                freight: true,
                tow_amount: true,
                other_charges: true,
                payment_amount: true,
                vat_and_custom: true,
                vat_and_custom_cost: true,
                mix_shipping_vehicle_charges: {
                  select: {
                    name: true,
                    value: true,
                    cost_value: true,
                    deleted_at: true,
                  },
                },
                vehicles: {
                  select: {
                    storage_cost: true,
                    title_cost: true,
                    dismantle_costs: true,
                    damage_cost: true,
                    other_costs: true,
                    vehicle_costs: {
                      select: {
                        dismantal_cost: true,
                        pgl_storage_costs: true,
                        title_charge: true,
                        towing_cost: true,
                        ship_cost: true,
                        other_cost: true,
                      },
                    },
                    vehicle_charges: {
                      where: {
                        deleted_at: null,
                      },
                    },
                    vehicle_towings: {
                      select: {
                        tow_amount: true,
                      },
                    },
                  },
                },
                payments: paymentSelect(),
              },
            },
          },
        },
        delivery_charge_invoice_details: {
          select: {
            delivery_charge_invoice: {
              select: {
                delivery_charges: true,
                delivery_charge_invoice_details: {
                  select: {
                    delivery_charge_invoice_id: true,
                  },
                },
              },
            },
          },
        },
        clear_logs: {
          include: {
            log_invoices: true,
            single_vcc: {
              select: {
                vcc: true,
                discount: true,
              },
            },
            exit_claim_charge: {
              select: {
                exit_charges: true,
                discount: true,
              },
            },
            detention_charge: {
              select: {
                detention_charges: true,
                discount: true,
              },
            },
          },
        },
        clearance_combine_booking_invocies: {
          select: {
            cost: true,
            payment_received: true,
            invoice_amount: true,
            payments: paymentSelect(),
          },
        },
      },
    };

    if (limit > 0) {
      property['skip'] = (offset - 1) * limit;
      property['take'] = limit;
    }

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany(property),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  async updateCosts(id: number, dto: UpdateCostsDto, updated_by: number) {
    const container = await this.prisma.containers.findUnique({
      where: { id },
      include: {
        bookings: true,
        container_costs: true,
      },
    });

    if (!container) return null;

    if (dto.booking_cost && dto.booking_id) {
      await this.prisma.bookings.update({
        where: { id: container.booking_id },
        data: { cost: dto.booking_cost, updated_by: updated_by },
      });
    }

    if (dto.loading_cost && container.container_costs.length) {
      await this.editOne({
        where: { id: id },
        data: {
          updated_by: updated_by,
          container_costs: {
            upsert: [
              {
                where: {
                  container_id_name: {
                    container_id: id,
                    name: 'loading_cost',
                  },
                },
                update: {
                  value: dto.loading_cost,
                  updated_by: updated_by,
                },
                create: {
                  name: 'loading_cost',
                  value: dto.loading_cost,
                  created_by: updated_by,
                },
              },
            ],
          },
        },
        include: {
          container_costs: {
            select: {
              name: true,
              value: true,
            },
          },
        },
      });
    }

    const updatedContainer = await this.prisma.containers.findUnique({
      where: { id },
      include: {
        bookings: true,
        container_costs: true,
      },
    });

    return updatedContainer;
  }

  findOne = async (id: number) => {
    const r: any = await this.findMultiple(!Number.isNaN(id) ? [id] : []);
    return { result: r.data.length > 0, data: r.data[0] };
  };

  findMultiple = (ids: number[]) =>
    this.prisma.containers
      .findMany({
        where: { id: { in: Array.isArray(ids) ? ids.map(Number) : [] } },
        include: {
          ...create_update_by,
          shipping_documents: true,
          loadplan_sent_byToUser: { select: { fullname: true } },
          bookings: {
            include: {
              parent: { select: { id: true, booking_number: true } },
              destinations: { select: { id: true, name: true } },
              vessels: {
                include: {
                  steamshiplines: { select: { id: true, name: true } },
                  locations: { select: { id: true, name: true } },
                },
              },
            },
          },
          companies: {
            select: {
              name: true,
              consignee: true,
              consignee_street: true,
              consignee_box: true,
              consignee_poc: true,
              consignee_email: true,
              consignee_phone: true,
              consignee_city: true,
              consignee_zip_code: true,
              consignee_country: true,
              notify_party: true,
              notify_street: true,
              notify_box: true,
              notify_city: true,
              notify_zip: true,
              notify_country: true,
              notify_poc: true,
              notify_email: true,
              notify_phone: true,
              customers: { select: { fullname: true } },
            },
          },
          vehicles: {
            orderBy: {
              id: 'desc',
            },
            include: {
              companies: {
                select: {
                  name: true,
                },
              },
              vehicle_costs: {
                select: {
                  dismantal_cost: true,
                  pgl_storage_costs: true,
                  title_charge: true,
                  towing_cost: true,
                  ship_cost: true,
                  other_cost: true,
                },
              },
              vehicle_charges: {
                where: {
                  deleted_at: null,
                },
              },
              vehicle_towings: {
                select: {
                  tow_amount: true,
                },
              },
            },
          },
          yards_location: { select: { name: true, emails: true } },
          invoices: {
            select: {
              id: true,
              invoice_amount: true,
            },
          },
          container_costs: {
            select: { id: true, name: true, value: true, description: true },
          },
          pullDriver: { select: { id: true, name: true } },
          inGateDriver: { select: { id: true, name: true } },
          loaders: { select: { id: true, name: true } },
          container_charges: {
            where: {
              deleted_at: null,
            },
          },
        },
      })
      .then(async (data) => ({
        result: data.length > 0,
        data: await this.addContainersTruckingCost(data),
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  update = async (id: number, dto: UpdateContainerDto, updated_by: number) => {
    const containerCosts: any = Object.values(
      Object.assign({}, dto.container_costs),
    );
    const uniqueContainerCosts = this.removeDuplicateCost(containerCosts);
    const containerFind: any = await this.findMultiple([id]);
    const container = containerFind.result ? containerFind.data[0] : null;

    // const containerForBookingSuffix = await this.prisma.containers.findMany({
    //   where: {
    //     booking_id: dto.prev_booking_id,
    //     booking_suffix: {
    //       gt: dto.booking_suffix_gt,
    //       not: dto.booking_suffix_gt,
    //     },
    //   },
    // });
    // console.log(
    //   'container',
    //   containerForBookingSuffix,
    //   dto.prev_booking_id,
    //   dto.booking_suffix_gt,
    // );

    if (container.pl_status === pl_status.reviewed) {
      const existingContainerCosts = container.container_costs.reduce(
        (acc: any, cost: any) => {
          acc[cost.name] = cost.value;
          return acc;
        },
        {},
      );

      const hasCostsChanged = uniqueContainerCosts.some((cost: any) => {
        const existingValue = existingContainerCosts[cost.name];
        return (
          existingValue === undefined ||
          parseFloat(cost.value) !== existingValue
        );
      });

      if (hasCostsChanged) {
        throw new HttpException(
          {
            result: false,
            message: 'Reviewed Container costs can not be modified.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (dto.booking_id && dto.container_number != '') {
      const isSplitContainer = await this.isSplitContainer(
        dto.container_number,
        dto.booking_id,
        id,
      );

      if (isSplitContainer) {
        throw new HttpException(
          {
            result: false,
            message: 'Split Containers not Allowed.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    delete dto.container_costs;

    const containerBeforeUpdate = await this.prisma.containers.findUnique({
      where: { id },
    });

    if (dto.booking_suffix) {
      const existingContainer = await this.prisma.containers.findFirst({
        where: {
          booking_suffix: dto.booking_suffix,
          booking_id: dto.booking_id,
          deleted_at: null,
        },
        include: {
          bookings: {
            include: {
              vessels: {
                select: { etd: true },
              },
            },
          },
        },
      });

      const date1 = moment(existingContainer?.bookings?.vessels?.etd);
      const date2 = moment('2024-11-15');

      if (
        existingContainer &&
        existingContainer.id !== containerBeforeUpdate.id &&
        date1.isAfter(date2)
      ) {
        throw new HttpException(
          {
            result: false,
            message: 'Booking suffix already exists.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (containerBeforeUpdate.booking_id != dto.booking_id) {
      const selected_booking = await this.prisma.bookings.findUnique({
        where: { id: dto.booking_id },
        include: {
          _count: {
            select: {
              containers: {
                where: { deleted_at: null },
              },
            },
          },
        },
      });
      if (selected_booking) {
        const allowed_bookings = [
          'GAJEBBK',
          'TXJEBBK',
          'NJJEBBK',
          'MDJEBBK',
          'CAJEBBK',
          'PotiBK',
          'PotiTXBK',
          'PotiHAZ',
          'CASALALAHBK',
          'CAPOTIBK',
          'MDJEBBK HAZ',
          'GAJEBBK HAZ',
          'NJJEBBK HAZ',
          'TXJEBBK HAZ',
          'CAJEBBK HAZ',
          'PotiTXBKHAZ',
          'HAZTXJEBBK',
        ];
        if (!allowed_bookings.includes(selected_booking.booking_number)) {
          if (selected_booking._count.containers >= selected_booking.qty) {
            throw new HttpException(
              {
                result: false,
                message: `The number of containers (${selected_booking._count.containers}) has reached or exceeded the booking quantity (${selected_booking.qty}).`,
                status: HttpStatus.BAD_REQUEST,
              },
              HttpStatus.BAD_REQUEST,
            );
          }
        }
      }
    }

    const maxIdResult = await this.prisma.container_charges.aggregate({
      _max: { id: true },
    });

    let maxChargeId: number = maxIdResult._max.id;
    const containers: any = await this.prisma.containers
      .findUnique({
        where: { id },
        include: {
          container_charges: true,
        },
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

    const updatedMap = new Map<string, ContainerChargesDTO>();
    for (const ch of dto.container_charges) {
      updatedMap.set(`${ch.category}__${ch.name}`, ch);
    }

    const mergedCharges: ContainerChargesDTO[] = [];

    for (const dbCharge of containers.container_charges) {
      const key = `${dbCharge.category}__${dbCharge.name}`;
      if (updatedMap.has(key)) {
        mergedCharges.push(updatedMap.get(key)!);
        updatedMap.delete(key);
      } else {
        mergedCharges.push({ ...dbCharge, deleted: true });
      }
    }

    for (const ch of updatedMap.values()) {
      mergedCharges.push(ch);
    }
    try {
      const result = await this.editOne({
        where: { id },
        data: {
          actions: dto.actions,
          aes_itn_number: dto.aes_itn_number,
          amount: dto.amount,
          bill_of_loading_number: dto.bill_of_loading_number,
          booking_id: dto.booking_id,
          booking_suffix: dto.booking_suffix,
          clearance_invoice_link: dto.clearance_invoice_link,
          company_id: dto.company_id,
          container_id_update_date: dto.container_id_update_date,
          container_number: dto.container_number,
          ingate: dto.ingate,
          // invisible_for_customer: dto.invisible_for_customer,
          invoice_number: dto.invoice_number,
          loading_date: new Date(dto.loading_date),
          loading_instruction: dto.loading_instruction,
          documentation_instruction: dto.documentation_instruction,
          no_units_load: dto.no_units_load,
          photo_link: dto.photo_link,
          pin_in: dto.pin_in,
          pin_out: dto.pin_out,
          seal_number: dto.seal_number,
          shipping_document_id: dto.shipping_document_id,
          tracking_contatiner: dto.tracking_contatiner,
          ingate_date: dto.ingate_date,

          pull_date: dto.pull_date,
          ingate_driver_id: dto.ingate_driver,
          ingate_driver_bonus: dto.ingate_driver_bonus,
          pull_driver_id: dto.pull_driver,
          pull_driver_bonus: dto.pull_driver_bonus,
          pull_driver_notes: dto.pull_driver_notes,
          ingate_driver_notes: dto.ingate_driver_notes,
          title_status: dto.title_status ?? null,
          aes_status: dto.aes_status ?? null,
          container_number_assigned_at:
            dto.container_number === null
              ? null
              : dto.container_number !=
                    containerBeforeUpdate?.container_number ||
                  containerBeforeUpdate?.container_number == null
                ? new Date()
                : containerBeforeUpdate.container_number_assigned_at,
          loaders: {
            set: dto.loaders?.map((item) => ({ id: item })),
          },
          loader_remark: dto.loader_remark,
          load_combination_type: dto.load_combination_type,
          shipment_type_approved: dto.shipment_type_approved,
          container_costs: {
            upsert: uniqueContainerCosts.map((n) => {
              const parsedValue = parseFloat(n?.value || 0);
              return {
                update: {
                  name: n.name,
                  value: parsedValue,
                  description: n.description,
                  updated_by,
                },
                create: {
                  name: n.name,
                  value: parsedValue,
                  description: n.description,
                  updated_by,
                },
                where: { id: n.id ?? 0 },
              };
            }),
          },
          yard_location_id: dto.yard_location_id,
          aes_filling_link: dto.aes_filling_link,
          updated_by,
          of_loading_video: dto.of_loading_video,
          of_loading_photo: dto.of_loading_photo,
          container_charges: {
            upsert: mergedCharges?.map(
              (charge: ContainerChargesDTO & { deleted?: boolean }) => {
                maxChargeId++;
                const updatedOrDeleted =
                  charge.deleted && charge.deleted
                    ? {
                        deleted_at: new Date(),
                        deleted_by: updated_by,
                      }
                    : {
                        updated_at: new Date(),
                        updated_by: updated_by,
                      };
                return {
                  update: {
                    name: charge.name,
                    amount: +charge.amount,
                    cost: +charge.cost,
                    remark: charge.remark,
                    category: charge.category,
                    ...updatedOrDeleted,
                  },
                  create: {
                    name: charge.name,
                    amount: +charge.amount,
                    cost: +charge.cost,
                    remark: charge.remark,
                    category: charge.category,
                    created_at: new Date(),
                    created_by: updated_by,
                  },
                  where: { id: charge.id ? charge.id : maxChargeId },
                };
              },
            ),
          },
        },
        include: {
          container_costs: {
            select: { name: true, value: true, description: true },
          },
          container_charges: {
            where: {
              deleted_at: null,
            },
            select: {
              name: true,
              amount: true,
              cost: true,
              remark: true,
            },
          },
        },
      });

      if (dto.container_number) {
        await this.prisma.vehicles
          .findMany({ where: { container_id: id }, select: { id: true } })
          .then((res) =>
            add_tracking(
              res?.map((o) => o.id),
              tracking_status.shipped,
            ),
          )
          .catch((err) => catch_response(err))
          .finally(() => this.prisma.$disconnect());
      }

      //if (dto.status) add_tracking([result?.id], dto.status, false);
      return await this.findOne(result.id);
    } catch (err) {
      catch_response(err);
    }
  };

  updateInvoiceLink = async (
    id: number,
    dto: clearanceInvoiceLinkDto,
    updated_by: number,
  ) =>
    this.prisma.containers
      .update({
        where: { id },
        data: {
          clearance_invoice_link: dto.clearance_invoice_link,
          updated_by,
        },
        select: {
          id: true,
          clearance_invoice_link: true,
          updated_at: true,
          updated_by: true,
        },
      })
      .then((r) => r)
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect());

  updatePullIngateDate = async (
    id: number,
    dto: UpdatePullIngateDateDto,
    updated_by: number,
  ) => {
    return this.prisma.containers
      .update({
        where: { id },
        data: {
          pull_date: dto.pull_date,
          ingate_date: dto.ingate_date,
          updated_by,
        },
        select: {
          id: true,
          ingate_date: true,
          pull_date: true,
          updated_at: true,
          updated_by: true,
        },
      })
      .then((r) => r)
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect());
  };

  softDeleteOne = (
    ids: number[],
    deleted_by: number,
    deleted_reason?: string,
  ) =>
    this.prisma.containers
      .updateMany({
        where: { id: { in: ids.map(Number) }, vehicles: { none: {} } },
        data: {
          deleted_at: new Date(),
          deleted_by,
          deleted_reason,
          isPendingTrash: true,
        },
      })
      .then((r) => ({ result: r ? true : false, data: r }))
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect());

  getTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const searchFilterWhere = await searchFilter(query, searchColumns);
    const where = {
      ...searchFilterWhere,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: false,
    };

    const orderBy = await ordersBy(query);

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany({
        skip: (offset - 1) * limit,
        take: limit,
        orderBy,
        where,
        select: {
          id: true,
          container_number: true,
          booking_id: true,
          booking_suffix: true,
          status: true,
          companies: { select: { id: true, name: true } },
          bookings: {
            select: {
              id: true,
              booking_number: true,
              destinations: { select: { id: true, name: true } },
              vessels: {
                select: {
                  id: true,
                  locations: { select: { id: true, name: true } },
                },
              },
            },
          },
          deleted_at: true,
          deleted_reason: true,
          users_containers_deleted_by_confirmTousers: {
            select: {
              id: true,
              fullname: true,
            },
          },
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => ({
        result: true,
        page: offset,
        per_page: limit,
        total,
        data,
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getPendingTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const searchFilterWhere = await searchFilter(query, searchColumns);
    const where = {
      ...searchFilterWhere,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: true,
    };

    const orderBy = await ordersBy(query);

    return await Promise.all([
      this.prisma.containers.count({ where }),
      this.prisma.containers.findMany({
        skip: (offset - 1) * limit,
        take: limit,
        orderBy,
        where,
        select: {
          id: true,
          container_number: true,
          booking_id: true,
          booking_suffix: true,
          status: true,
          companies: { select: { id: true, name: true } },
          bookings: {
            select: {
              id: true,
              booking_number: true,
              destinations: { select: { id: true, name: true } },
              vessels: {
                select: {
                  id: true,
                  locations: { select: { id: true, name: true } },
                },
              },
            },
          },
          deleted_at: true,
          deleted_reason: true,
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => ({
        result: true,
        page: offset,
        per_page: limit,
        total,
        data,
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  restore = (ids: number[], updated_by: number) =>
    this.prisma.containers
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: { deleted_at: null, deleted_by: null, updated_by },
      })
      .then((data) => ({ return: data ? true : false, data }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  restorePendingTrash = (ids: number[], updated_by: number) =>
    this.prisma.containers
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by,
          isPendingTrash: false,
        },
      })
      .then((data) => ({ return: data ? true : false, data }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  deletePendingTrash = (ids: number[], deleted_by_confirm: number) =>
    this.prisma.containers
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          isPendingTrash: false,
          deleted_by_confirm,
        },
      })
      .then((data) => ({ return: data ? true : false, data }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getTotal = (where: any) =>
    this.prisma.containers
      .count(where)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => this.prisma.$disconnect());

  getAll = (params: any) =>
    this.prisma.containers
      .findMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  editOne = (params: any) =>
    this.prisma.containers
      .update(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  editMany = (params: any) =>
    this.prisma.containers
      .updateMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  changeCheckToAny = async (
    query: changeContainerStatus,
    updated_by: number,
  ) => {
    const include = {
      bookings: {
        include: {
          vessels: true,
        },
      },
      vehicles: true,
    };
    const check = await this.getAll({
      where: {
        id: { in: query.containerIds },
        status: container_status.checked,
      },
      include: ['on_the_way', 'checked', 'final_checked'].includes(
        query.containerStatus,
      )
        ? include
        : {},
    });

    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          status: query.containerStatus,
          updated_by,
          status_changed_at: new Date(),
        },
      });
      // if (
      //   ['on_the_way', 'checked', 'final_checked'].includes(
      //     query.containerStatus,
      //   )
      // ) {
      //   check.forEach(async (container) => {
      //     await this.generateInvoice(container, updated_by);
      //   });
      // }
      add_tracking(await check.map((o) => o.id), query.containerStatus, false);
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in check status.',
      };
  };

  changeFinalCheckToAny = async (
    query: changeContainerStatus,
    updated_by: number,
  ) => {
    const include = {
      bookings: {
        include: {
          vessels: true,
        },
      },
      vehicles: true,
    };
    const check = await this.getAll({
      where: {
        id: { in: query.containerIds },
        status: container_status.final_checked,
      },
      include: ['on_the_way', 'checked', 'final_checked'].includes(
        query.containerStatus,
      )
        ? include
        : {},
    });

    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          status: query.containerStatus,
          updated_by,
          status_changed_at: new Date(),
        },
      });

      add_tracking(await check.map((o) => o.id), query.containerStatus, false);
      // if (
      //   ['on_the_way', 'checked', 'final_checked'].includes(
      //     query.containerStatus,
      //   )
      // ) {
      //   check.forEach(async (container) => {
      //     await this.generateInvoice(container, updated_by);
      //   });
      // }
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in final check status.',
      };
  };

  changeAnyToClearance = async (
    query: changeContainerStatusIds,
    updated_by: number,
  ) => {
    const check = await this.getAll({
      where: {
        id: { in: query.containerIds },
        status: { not: container_status.clearance },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          status: container_status.clearance,
          updated_by,
          status_changed_at: new Date(),
        },
      });
      add_tracking(
        await check.map((o) => o.id),
        container_status.clearance,
        false,
      );
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers.',
      };
  };

  changeContainerStatus = async (
    query: changeContainerStatus,
    updated_by: number,
  ) => {
    const { containerStatus, containerIds } = query;
    let onTheWayCheck = true;
    if (containerStatus == container_status.clearance) {
      if (await check_permissions(updated_by, 'to_clearance')) {
        await this.changeAnyToClearance(query, updated_by);
      } else {
        throw new HttpException(
          {
            result: false,
            message: 'Can not change status to clearance',
            status: HttpStatus.NOT_FOUND,
          },
          HttpStatus.NOT_FOUND,
        );
      }
    }
    const include = {
      bookings: {
        include: {
          vessels: true,
        },
      },
      vehicles: true,
    };
    const check = await this.getAll({
      where: {
        id: { in: containerIds },
        // the ids are already in the checked status
        // status: {
        //   notIn: [container_status.final_checked, container_status.checked],
        // },
      },
      include: ['on_the_way', 'checked', 'final_checked'].includes(
        containerStatus,
      )
        ? include
        : {},
    });

    if (containerStatus == container_status.on_the_way) {
      onTheWayCheck = await this.hasBookingCostSet(containerIds);
      if (!onTheWayCheck) {
        throw new HttpException(
          {
            result: false,
            message: 'Booking Cost not Set for this/some container/s.',
            status: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          status: containerStatus,
          updated_by,
          status_changed_at: new Date(),
        },
      });

      add_tracking(await check.map((o) => o.id), containerStatus, false);
      // if (
      //   ['on_the_way', 'checked', 'final_checked'].includes(containerStatus)
      // ) {
      //   check.forEach(async (container) => {
      //     await this.generateInvoice(container, updated_by);
      //   });
      // }
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific status.',
      };
  };

  updateTitleAESStatus = async (
    id,
    dto: UpdateTitleAESStatus,
    updated_by: number,
  ) =>
    await this.prisma.containers
      .update({
        where: { id },
        data: {
          title_status: dto.title_status,
          aes_status: dto.aes_status,
          updated_by,
        },
      })
      .then((res: any) => ({ result: res ? true : false, data: res }))
      .catch((e: any) => catch_response(e))
      .finally(() => this.prisma.$disconnect());

  changeProfitLossStatus = async (
    query: changeProfitLossStatus,
    updated_by: number,
  ) => {
    const { plStatus, containerIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: containerIds },
      },
    });

    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: { pl_status: plStatus, pl_at: new Date(), updated_by },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific Profit Loss status.',
      };
  };

  changeUaeProfitLossStatus = async (
    query: changeUaeProfitLossStatus,
    updated_by: number,
  ) => {
    const { uaePlStatus, containerIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: containerIds },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: { uae_pl_status: uaePlStatus, uae_pl_at: new Date(), updated_by },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific Profit Loss status.',
      };
  };

  changePrelimProfitLossStatus = async (
    query: changePrelimProfitLossStatus,
    updated_by: number,
  ) => {
    const { prelimPlStatus, containerIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: containerIds },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          prelim_pl_status: prelimPlStatus,
          prelim_pl_at: new Date(),
          updated_by,
        },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific Profit Loss status.',
      };
  };

  removeEtd2025Check = async (query: changeContainerStatusIds) => {
    const { containerIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: containerIds },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: {
          etd_2025_check: true,
        },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific Profit Loss status.',
      };
  };

  changeBalanceDifference = async (
    query: changeBalanceDifference,
    updated_by: number,
  ) => {
    const { balanceDiff, containerIds } = query;

    const check = await this.getAll({
      where: {
        id: { in: containerIds },
      },
    });
    if (check.length > 0) {
      const data = await this.editMany({
        where: { id: { in: check.map((o) => o.id) } },
        data: { pl_balance_diff: balanceDiff, updated_by },
      });
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    } else
      return {
        result: false,
        message: 'There is no containers in specific Profit Loss status.',
      };
  };

  hasBookingCostSet = async (ids: number[]): Promise<boolean> => {
    const containersWithBookings = await this.getAll({
      where: { id: { in: ids } },
      select: {
        id: true,
        bookings: { select: { id: true, eta: true, cost: true } },
      },
    });

    return containersWithBookings.every(
      (container) => container.bookings?.cost > 0,
    );
  };

  getYardLocation = (id: number) =>
    this.prisma.bookings
      .findMany({
        where: { id },
        select: {
          id: true,
          size: true,
          port_of_discharge: true,
          vessel_id: true,
          destinations: { select: { name: true } },
          party: true,
          vessels: {
            select: {
              locations: {
                select: {
                  id: true,
                  name: true,
                  container_loading_cost: true,
                  yards_location: { select: { id: true, name: true } },
                },
              },
            },
          },
        },
      })
      .then((r) => r)
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect);

  getDrivers = (id: number) =>
    this.prisma.bookings
      .findUnique({
        where: {
          id,
        },
        select: {
          vessels: {
            select: {
              locations: {
                select: {
                  id: true,
                  drivers: {
                    where: { status: driver_status.active },
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      })
      .then((r) => r?.vessels?.locations?.drivers)
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect);

  getLoaders = (id: number) =>
    this.prisma.loaders
      .findMany({
        where: {
          OR: [
            {
              portOfLoading: {
                vessels: {
                  some: {
                    bookings: {
                      some: {
                        id,
                      },
                    },
                  },
                },
              },
            },
            { name: { contains: 'Unknown' } },
          ],
        },
      })
      .then((r) => r)
      .catch((err) => catch_response(err))
      .finally(async () => this.prisma.$disconnect);

  hasLoadingTitleCostMissing = async (id: number) => {
    let check = false;
    let message = '';
    const containerFind: any = await this.findMultiple([id]);
    const container = containerFind.result ? containerFind.data[0] : null;
    if (container) {
      // Check for missing 'loading_cost' or its value not greater than 0
      const loadingCost = container.container_costs.find(
        (cost) => cost.name === 'loading_cost',
      );
      const loadingCostMissing = !loadingCost || loadingCost.value <= 0;

      if (loadingCostMissing) {
        message = 'Container Loading Cost missing';
      }
      for (const vehicle of container.vehicles) {
        const titleCharge = vehicle.vehicle_costs.title_charge;
        const titleCost = vehicle.title_cost;

        if (titleCharge > 0 && titleCost <= 0) {
          check = true;
          if (message != '') {
            message = message + ' and some Vehicle Title Cost missing.';
          } else {
            message = 'Some Vehicle Title Cost missing.';
          }
          break;
        }
      }
      check = check || loadingCostMissing;
    }
    return { result: check, message: message };
  };

  getMaxBookingSuffix = (id: number) =>
    this.prisma.containers
      .aggregate({
        _max: { booking_suffix: true },
        where: { booking_id: id },
      })
      .then((r) => {
        const bs = r._max.booking_suffix;
        return bs && bs !== '' && Number.isInteger(+bs) ? +bs + 1 : 1;
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  getMaxBookingSuffix2 = async (id: number) => {
    try {
      const containers = await this.prisma.containers.findMany({
        where: { booking_id: id, deleted_at: null },
        select: {
          booking_suffix: true,
        },
      });

      // Step 1: Extract numeric values and sort them

      const suffixNumbers = containers
        .map((item) => Number(item.booking_suffix))
        .filter((num) => !isNaN(num) && num !== null && num > 0) // Filters out NaN, null, and non-positive numbers
        .sort((a, b) => a - b);

      let suffix = suffixNumbers[0] !== 1 ? 1 : suffixNumbers[0]; // Default to 1 if no valid suffix found

      // Find missing numbers
      if (suffixNumbers[0] == 1) {
        const max = suffixNumbers[suffixNumbers.length - 1] || 1; // Default to 1 if no valid suffix found
        const missingNumbers = Array.from(
          { length: max - suffix + 1 },
          (_, i) => suffix + i,
        ).filter((num) => !suffixNumbers.includes(num));

        suffix = missingNumbers.length > 0 ? missingNumbers[0] : max + 1;
      }
      return suffix;
    } catch (error) {
      console.log('Error retrieving container count:', error);
      throw error;
    }
  };

  getContainerSuffix = async (container_number: string) => {
    const prefix = container_number.split('-')[0];
    try {
      const containers = await this.prisma.containers.findMany({
        where: {
          container_number: {
            startsWith: prefix,
          },
          deleted_at: null,
        },
      });
      return containers.length;
    } catch (error) {
      console.log('Error retrieving container count:', error);
      throw error;
    }
  };

  findMany = (container_id: number) =>
    this.getAll({
      where: { id: container_id },
      select: {
        id: true,
        no_units_load: true,
        invoice_number: true,
        cover_photo: true,
        invoices: {
          select: {
            purpose: true,
          },
        },
        companies: {
          select: {
            name: true,
            id: true,
          },
        },
        vehicles: {
          select: {
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            vehicle_towings: { select: { tow_amount: true } },
          },
        },
        bookings: {
          select: {
            id: true,
            eta: true,
          },
        },
      },
    });

  validate = async (field: string, value: string, idValue: number) => {
    const check = await this.prisma.containers.findFirst({
      where: {
        id: { not: idValue },
        [field]: value,
        deleted_at: null,
        deleted_by: null,
      },
    });
    if (check) {
      this.prisma.$disconnect();
      throw new BadRequestException(`${value} ${field} is already exist. `);
    } else {
      this.prisma.$disconnect();
      return true;
    }
  };

  async sendLoadingPlan(
    body: {
      ids: string;
      subject: string;
      description: string;
      emails: string[];
    },
    sent_by: number,
    files: any,
  ) {
    try {
      const ids = JSON.parse(body.ids);
      if (ids.length == 0) {
        return;
      }
      return await this.prisma.$transaction(async () => {
        await this.prisma.containers.updateMany({
          where: { id: { in: ids } },
          data: {
            loadplan_sent_at: new Date(),
            loadplan_sent_by: sent_by,
          },
        });

        const containers = await this.prisma.containers.findMany({
          where: { id: { in: ids } },
          select: {
            company_id: true,
            booking_suffix: true,
            yards_location: { select: { name: true, emails: true } },
            container_number: true,
            loadplan_sent_at: true,
            bookings: {
              select: {
                booking_number: true,
                size: true,
                destinations: { select: { id: true, name: true } },
              },
            },
            vehicles: {
              select: {
                year: true,
                make: true,
                model: true,
                vin: true,
                color: true,
                lot_number: true,
                price: true,
              },
            },
          },
        });
        this.sendLoadPlanMail(containers, body, files);
        return containers;
      });
    } catch (error) {
      catch_response(error);
    }
  }

  async sendLoadPlanMail(containers, body, files = []) {
    if (!body.emails) {
      return;
    }
    if (!is_send) return;
    try {
      if (!body.emails) {
        return;
      }
      await reactEmail(
        body.emails,
        // '<EMAIL>',
        body.subject,
        { containers: containers, description: body.description },
        ShipmentLoadingPlaneEmailTemplate,
        null,
        {
          from: 'Loading & <NAME_EMAIL>',
          cc: ['<EMAIL>'],
          attachments: files.map((file) => {
            return {
              filename: file.originalname,
              content: file.buffer,
            };
          }),
        },
      );
      return true;
    } catch (error) {
      console.log('error', error);
      throw new HttpException(error, 500);
    }
  }

  async getInventories(query: {
    company_id: string;
    from: string;
    to: string;
  }) {
    try {
      return await this.prisma.containers.findMany({
        where: {
          company_id: +query.company_id,
          ingate_date: {
            gte: new Date(query.from),
            lte: new Date(query.to),
          },
        },
        select: {
          id: true,
          company_id: true,
          booking_suffix: true,
          photo_link: true,
          shipping_documents: true,
          yards_location: { select: { name: true, emails: true } },
          container_number: true,
          bookings: {
            select: {
              booking_number: true,
              size: true,
              eta: true,
              vessels: {
                select: {
                  etd: true,
                  locations: {
                    select: { id: true, name: true },
                  },
                  steamshiplines: { select: { id: true, name: true } },
                },
              },
              destinations: { select: { id: true, name: true } },
            },
          },
          vehicles: {
            select: {
              year: true,
              make: true,
              model: true,
              vin: true,
              color: true,
              lot_number: true,
              weight: true,
              receiver_name: true,
            },
          },
          companies: true,
        },
      });
    } catch (error) {
      return catch_response(error);
    }
  }

  async sendInventories(
    body: {
      company_id: string;
      from: string;
      to: string;
      subject: string;
      description: string;
    },
    files: any,
  ) {
    try {
      let emails: any = await this.prisma.loginables.findMany({
        where: {
          customer: {
            companies: { id: +body.company_id },
          },
        },
        select: {
          email: true,
        },
      });
      const containers: any = await this.getInventories(body);

      if (containers.length > 0) {
        emails = emails.map((row) => row.email);
        body['emails'] = emails.join(',');
        await this.sendInventoryMail(containers, body, files);
        return { result: true };
      }
      return { result: false, message: 'no container found!' };
    } catch (error) {
      return catch_response(error);
    }
  }

  async sendInventoryMail(containers, body, files = []) {
    try {
      const containersClone = [...containers];
      const bolFiles = await this.getBolPdf(containersClone);
      const attachedFiles = files.map((file) => {
        return {
          filename: file.originalname,
          content: file.buffer,
        };
      });
      await reactEmail(
        body.emails,
        body.subject,
        { containers: containers, description: body.description },

        ShipmentInventoryEmailTemplate,
        null,
        {
          from: 'Loading & <NAME_EMAIL>',
          cc: ['<EMAIL>'],
          attachments: [...attachedFiles, ...bolFiles],
        },
      );

      return true;
    } catch (error) {
      throw new HttpException(error, 500);
    }
  }

  async getArrivalNoticeContainers(container_ids) {
    const containersRes = await this.prisma.containers.findMany({
      where: {
        id: { in: container_ids },
      },
      select: {
        id: true,
        company_id: true,
        booking_suffix: true,
        container_number: true,
        bookings: {
          select: {
            booking_number: true,
            eta: true,
            vessels: {
              select: {
                etd: true,
                locations: {
                  select: { id: true, name: true },
                },
                steamshiplines: { select: { id: true, name: true } },
              },
            },
            destinations: { select: { id: true, name: true } },
            parent_id: true,
            parent: {
              select: {
                booking_number: true,
                id: true,
              },
            },
          },
        },
        companies: {
          select: { name: true },
        },
      },
    });
    return containersRes;
  }

  async sendArrivalNotices(
    body: {
      container_ids: number[];
      company_id: number;
      subject: string;
      description: string;
      free_days: {
        id: number;
        value: number;
      }[];
      cc: string[];
      emails: string[];
    },
    loginable: any,
  ) {
    try {
      const customers = await this.prisma.loginables.findMany({
        where: {
          customer: {
            companies: { id: +body.company_id },
          },
        },
        select: {
          email: true,
          customer_id: true,
        },
      });

      const containersRes = await this.getArrivalNoticeContainers(
        body.container_ids,
      );

      if (containersRes.length > 0) {
        // const emails = customers.map((row) => row.email);
        const ids = customers.map((row) => row.customer_id);
        // body['emails'] = emails.join(',');
        body['ids'] = ids;
        await this.sendArrivalNoticeMailAndNotification(
          containersRes.map((item) => {
            return {
              ...item,
              free_days: body.free_days.find((item2) => item2.id == item.id)
                ?.value, // adding free days to container object
            };
          }),
          body,
        );
        await this.prisma.containers.updateMany({
          where: {
            id: { in: body.container_ids },
          },
          data: {
            arrival_notice_sent_at: new Date(),
            arrival_notice_sent_by: loginable.loginable_id,
            updated_by: loginable.loginable_id,
          },
        });
        return { result: true };
      }
      return { result: false, message: 'No container found!' };
    } catch (error) {
      return catch_response(error);
    }
  }

  groupByConsecutive = (arr, check) => {
    const result = [];
    let currentGroup = null;
    for (let i = 0; i < arr.length; i++) {
      if (!currentGroup || !check(currentGroup.item, arr[i])) {
        currentGroup = {
          item: arr[i],
          firstIndex: i,
          length: 1,
        };
        result.push(currentGroup);
      } else {
        currentGroup.length++;
      }
    }
    return result;
  };

  getBookingNumber(item) {
    return item?.bookings?.parent
      ? item?.bookings?.parent.booking_number
      : item?.bookings?.booking_number;
  }

  sortContainersBasedOnBookingNumber = (cns) => {
    const containers = JSON.parse(JSON.stringify(cns));
    containers.sort((item1, item2) => {
      if (this.getBookingNumber(item1) > this.getBookingNumber(item2))
        return -1;
      if (this.getBookingNumber(item1) < this.getBookingNumber(item2)) return 1;
      return 0;
    });
    return containers;
  };

  getRows = (cns) => {
    const containers = this.sortContainersBasedOnBookingNumber(cns);
    const groupedContainersLocation = this.groupByConsecutive(
      containers,
      (item1, item2) =>
        item1.bookings.vessels.locations.name ==
        item2.bookings.vessels.locations.name,
    );
    const groupedContainersCompany = this.groupByConsecutive(
      containers,
      (item1, item2) => item1.companies.name == item2.companies.name,
    );
    const groupedContainersEta = this.groupByConsecutive(
      containers,
      (item1, item2) =>
        moment(item1.bookings.eta).format('dddd, MMMM D, YYYY') ==
        moment(item2.bookings.eta).format('dddd, MMMM D, YYYY'),
    );
    const groupedContainersFreeDays = this.groupByConsecutive(
      containers,
      (item1, item2) => item1.free_days == item2.free_days,
    );
    const groupedBookingNumbers = this.groupByConsecutive(
      containers,
      (item1, item2) =>
        this.getBookingNumber(item1) == this.getBookingNumber(item2),
    );

    const td = `color: rgba(0, 0, 0, 0.7); font-family: 'Roboto'; font-size: 13px;
      font-style: normal; font-weight: 400; line-height: normal;
      border-bottom: 1px solid #d9d9d9; text-align: left; padding: 12px;
      padding-left: 14px; padding-right: 14px; border: 1px solid #d9d9d9;
      text-align: center; `;

    return containers
      .map((item, index) => {
        const itemGroupedLocation = groupedContainersLocation.find(
          (el) => el.firstIndex == index,
        );
        const itemGroupedCompany = groupedContainersCompany.find(
          (el) => el.firstIndex == index,
        );
        const itemGroupedEta = groupedContainersEta.find(
          (el) => el.firstIndex == index,
        );
        const itemGroupedFreeDays = groupedContainersFreeDays.find(
          (el) => el.firstIndex == index,
        );
        const itemGroupedBookingNumbers = groupedBookingNumbers.find(
          (el) => el.firstIndex == index,
        );
        const locationCell = itemGroupedLocation
          ? `<td style="${td}" rowspan="${itemGroupedLocation.length}">${item.bookings.vessels.locations.name}</td>`
          : '';
        const companyCell = itemGroupedCompany
          ? `<td style="${td}" rowspan="${itemGroupedCompany.length}">${item.companies.name}</td>`
          : '';
        const etaCell = itemGroupedEta
          ? `<td style="${td}" rowspan="${itemGroupedEta.length}">${moment(
              item.bookings.eta,
            ).format('dddd, MMMM D, YYYY')}</td>`
          : '';
        const freeDaysCell = itemGroupedFreeDays
          ? `<td style="${td}" rowspan="${itemGroupedFreeDays.length}">${item.free_days}</td>`
          : '';
        const bookingNoCell = itemGroupedBookingNumbers
          ? `<td style="${td}" rowspan="${
              itemGroupedBookingNumbers.length
            }">${this.getBookingNumber(item)}</td>`
          : '';

        return `
      <tr>
      <td style="${td}">${index + 1}</td>
      ${bookingNoCell}
      <td style="${td}"> ${item.container_number ?? ''}</td>
      ${companyCell}
      ${locationCell}
      ${etaCell}
      ${freeDaysCell}
    </tr>`;
      })
      .join('');
  };

  async sendArrivalNoticeMailAndNotification(containers, body) {
    if (!is_send) return;
    try {
      const company = await this.prisma.companies.findUnique({
        where: { id: body.company_id },
        select: { id: true, name: true, destination_id: true },
      });

      const imageSrc =
        company.destination_id === 22
          ? 'https://latest-api.pglsystem.com/images/ar_notoce_georgia.png'
          : 'https://latest-api.pglsystem.com/images/ar_notice.png';

      await reactEmail(
        body.emails,
        // '<EMAIL>',
        body.subject,
        {
          imageSrc: imageSrc,
          body: body,
          rowsHtml: this.getRows(containers),
        },
        ShipmentArrivalNoticeEmailTemplate,
        null,
        {
          from: '<EMAIL>',
          cc: ['<EMAIL>', '<EMAIL>', ...body.cc],
        },
      );

      for (let i = 0; i < body.ids.length; i++) {
        const id = body.ids[i];
        const notificationData = {
          receiver_id: id,
          title: body.subject,
          description: body.description,
          data: { containers },
          notification_type: 'arrival_notice',
        };

        const res = await this.prisma.customer_notifications.create({
          data: notificationData,
        });

        await Promise.all([
          this.socketGateway.customerNotificationEvent(res),
          this.firebaseService.sendFirebaseNotification(
            res,
            '',
            `Dear Valued Customer, We are delighted to let you know that the subject container (s) have been scheduled to arrive at the final destination on estimated ${moment(
              containers[0].bookings.eta,
            ).format(
              'dddd, MMMM D, YYYY',
            )} at destination "arrival is subject to change".`,
          ),
        ]);
      }

      return false;
    } catch (error) {
      throw new HttpException(error, 500);
    }
  }

  async downloadArrivalNoticePDF(body: {
    container_ids: number[];
    company_id: number;
    subject: string;
    description: string;
    free_days: {
      id: number;
      value: number;
    }[];
  }) {
    const container_ids = body.container_ids.map((item) => +item);

    try {
      const containers = await this.getArrivalNoticeContainers(
        container_ids,
      ).then((res) =>
        res.map((item) => {
          return {
            ...item,
            free_days: body.free_days.find((item2) => item2.id == item.id)
              ?.value,
          };
        }),
      );
      const company = await this.prisma.companies.findUnique({
        where: { id: body.company_id },
        select: { id: true, name: true, destination_id: true },
      });

      const temp = readFileSync(
        join(
          __dirname,
          `../../../email_templates/arrival_notice_email_template.hbs`,
        ),
        'utf8',
      );

      const compiledTemp = Handlebars.compile(temp);
      const imageSrc =
        company.destination_id === 22
          ? 'https://latest-api.pglsystem.com/images/ar_notoce_georgia.png'
          : 'https://latest-api.pglsystem.com/images/ar_notice.png';

      const html = compiledTemp({
        containers,
        rows: this.getRows(containers),
        body,
        imageSrc,
      });

      const pdf = await generatePdf(html, {
        top: '0',
        bottom: '0',
        right: '0',
        left: '0',
      });
      return pdf;
    } catch (error) {
      return catch_response(error);
    }
  }

  async getBolPdf(containers) {
    try {
      const temp_containers = containers;
      const files = [];
      const browser: puppeteer.Browser = await puppeteer.launch({
        args: [
          '--disable-setup-sandbox',
          '--no-sandbox',
          '--single-process',
          '--no-zygote',
        ],
        headless: true,
      });
      const page = await browser.newPage();

      const generateFiles = async () => {
        if (temp_containers.length > 0) {
          const htmlTemplate = await customerShipmentBillOfLoading(
            temp_containers[0],
          );
          await page.setContent(htmlTemplate);

          const pdfArray: Uint8Array = await page.pdf({
            format: 'A4',
            printBackground: true,
          });

          const pdfBuffer: Buffer = Buffer.from(pdfArray);
          files.push({
            filename: temp_containers[0].container_number + '.pdf',
            content: pdfBuffer,
          });
          temp_containers.shift();
          await generateFiles();
        }
      };
      await generateFiles();
      await browser.close();

      return files;
    } catch (error) {
      throw new HttpException(error, 500);
    }
  }

  isSplitContainer = async (
    containerNum: string,
    bookingId: number,
    containerId?: number,
  ) => {
    const booking = await this.prisma.$queryRawUnsafe(
      `SELECT ETA
      FROM BOOKINGS
      WHERE ID = ${bookingId};`,
    );

    const result: any = await this.prisma.$queryRawUnsafe(
      `SELECT CONTAINERS.ID,
          BOOKINGS.ETA,
          CONTAINERS.CONTAINER_NUMBER
        FROM CONTAINERS
        INNER JOIN BOOKINGS ON BOOKINGS.ID = CONTAINERS.BOOKING_ID
        WHERE LEFT(CONTAINERS.CONTAINER_NUMBER,10) = LEFT('${containerNum}',10)
        AND CONTAINERS.CREATED_AT > DATE '2024-01-01' AND CONTAINERS.deleted_at is null`,
    );

    if (result && result.length > 0) {
      if (containerId) {
        const hasMatchingETAUp = result.some(
          (record) =>
            record.eta.toISOString() === booking[0].eta.toISOString() &&
            record.id != containerId,
        );
        return hasMatchingETAUp;
      } else {
        const hasMatchingETA = result.some(
          (record) => record.eta.toISOString() === booking[0].eta.toISOString(),
        );
        return hasMatchingETA;
      }
    } else {
      return false;
    }
  };

  addContainerTransaction = async (
    query: addContainerTransaction,
    transaction_by: number,
  ) => {
    const { transactionNo, container_id } = query;
    const container = await this.findOne(container_id);

    if (container) {
      const data = await this.prisma.containers.update({
        where: { id: container_id },
        data: {
          transaction_no: transactionNo,
          transaction_at: new Date(),
          transaction_by,
        },
      });
      return {
        result: true,
        message: `Transaction Added Successfully!`,
        data: data,
      };
    } else {
      return {
        result: false,
        message: 'There is no Container Found. Contact IT',
      };
    }
  };

  // generateMultiReleaseDoc = async (ids: number[]) => {
  //   const data = await this.findMultiple(ids);
  //   const arrayOfData = data.data;
  //   const files = await Promise.all(
  //     arrayOfData.map(async (item) => {
  //       const html = ReleaseDocument(item);
  //       const buffer = await generatePdf(html);
  //       return {
  //         name: `${item.container_number}-${item.companies.name}.pdf`,
  //         buffer: buffer,
  //       };
  //     }),
  //   );
  //   return files;
  // };

  generateMultiReleaseDoc = async (ids: number[]) => {
    const data: any = await this.findMultiple(ids);
    const arrayOfData = data.data;
    const files = [];

    for (let i = 0; i < arrayOfData.length; i += BATCH_SIZE) {
      const batch = arrayOfData.slice(i, i + BATCH_SIZE);
      const batchFiles = await Promise.all(
        batch.map(async (item) => {
          const html = ReleaseDocument(item);
          const buffer = await generatePdf(html);
          return {
            name: `${item.container_number}-${item.companies.name}.pdf`,
            buffer: buffer,
          };
        }),
      );
      files.push(...batchFiles);
    }

    return files;
  };

  generateBatchPDF = async (batch: any[]) => {
    if (!batch || batch.length === 0) {
      throw new Error('Empty batch provided to generateBatchPDF');
    }

    const containersData = batch.map((container) => ({
      container_number: container.container_number || '',
      bookings: {
        size: container.bookings?.size || '',
        vessels: {
          scac_code: container.bookings?.vessels?.scac_code || '',
        },
      },
      seal_number: container.seal_number || '',
      no_units_load: container.no_units_load || '',
      vehicles:
        container.vehicles?.map((vehicle) => ({
          id: vehicle.id || 0,
          year: vehicle.year || '',
          make: vehicle.make || '',
          model: vehicle.model || '',
          color: vehicle.color || '',
          vin: vehicle.vin || '',
          weight: vehicle.weight || 0,
          halfcut_status: vehicle.halfcut_status || '',
        })) || [],
      aes_itn_number: container.aes_itn_number || '',
      measurement: container.measurement || '',
    }));

    const firstContainer = batch[0];
    const companyName = firstContainer.companies?.name || 'unknown';
    const containerNumber = firstContainer.container_number || 'unknown';

    const data = {
      ...firstContainer,
      containers: containersData,
    };

    const html = ReleaseDocument(data);
    const buffer = await generatePdf(html);

    const fileName = `${containerNumber}-${companyName}-batch.pdf`;

    return {
      name: fileName,
      buffer,
    };
  };

  generateSinglePdfBaseOnCompany = async (ids: number[]) => {
    try {
      const originalContainers: any = await this.findMultiple(ids);
      const originalData = originalContainers.data;

      if (
        !originalData ||
        !Array.isArray(originalData) ||
        originalData.length === 0
      ) {
        throw new Error('No container data found');
      }
      const trackedIds = originalData
        .map((c) => c.release_doc_tracking)
        .filter((id) => id !== null);

      const allIdsToFetch = new Set(ids);

      if (trackedIds.length > 0) {
        const relatedTrackedContainers = await this.prisma.containers.findMany({
          where: {
            release_doc_tracking: { in: trackedIds },
          },
        });

        for (const container of relatedTrackedContainers) {
          allIdsToFetch.add(container.id);
        }
      }
      const allContainersData = await this.findMultiple([...allIdsToFetch]);

      if (!allContainersData || !allContainersData.data) {
        throw new Error('Failed to retrieve containers.');
      }

      const arrayOfData = allContainersData?.data;
      const containersByCompany = arrayOfData.reduce((acc, item) => {
        const companyId = item.company_id || 'unknown';
        if (!acc[companyId]) acc[companyId] = [];
        acc[companyId].push(item);
        return acc;
      }, {});

      const files = [];

      for (const companyId in containersByCompany) {
        const companyContainers = containersByCompany[companyId];

        const groupedByTracking = companyContainers.reduce((acc, container) => {
          const key = container.release_doc_tracking ?? 'untracked';
          if (!acc[key]) acc[key] = [];
          acc[key].push(container);
          return acc;
        }, {});

        for (const key in groupedByTracking) {
          const group = groupedByTracking[key];

          if (key === 'untracked') {
            for (let i = 0; i < group.length; i += 3) {
              const batch = group.slice(i, i + 3);
              if (batch.length > 0) {
                const trackingId = batch[0].id;
                await this.prisma.containers.updateMany({
                  where: { id: { in: batch.map((b) => b.id) } },
                  data: { release_doc_tracking: trackingId },
                });

                const file = await this.generateBatchPDF(batch);
                files.push(file);
              }
            }
          } else {
            const subgroups = [];
            for (let i = 0; i < group.length; i += 3) {
              subgroups.push(group.slice(i, i + 3));
            }

            for (const subgroup of subgroups) {
              const file = await this.generateBatchPDF(subgroup);
              files.push(file);
            }
          }
        }
      }

      return files;
    } catch (error) {
      console.error('Error generating PDFs:', error);
      throw new Error('Failed to generate PDF documents');
    }
  };
  extractDriveId = (url) => {
    const pattern = /(?:\/file\/d\/|id=)([^/&]+)/;

    // Search for the pattern in the URL
    const match = url.match(pattern);

    // If a match is found, return the extracted ID
    if (match) {
      return match[1];
    } else {
      return null;
    }
  };

  generateCombinedTitles = async (id: number, user_id: number) => {
    try {
      const [container, token] = await Promise.all([
        await this.prisma.containers.findUnique({
          where: { id: id },
          select: {
            vehicles: {
              select: { id: true, vehicle_document_link: true, vin: true },
            },
          },
        }),
        await this.googleApisService.findToken(user_id),
      ]);
      if (!container) return null;
      const fileIds = container.vehicles
        .map((vehicle) => {
          const url = vehicle?.vehicle_document_link;
          if (!url) return null;
          return this.extractDriveId(url);
        })
        .filter((item) => item != null);

      const fileObjects = await Promise.all(
        fileIds.map(async (fileId) => {
          const blob: any = await this.googleApisService.getFile(fileId, token);
          const arrayBuffer = await blob.arrayBuffer();

          return {
            buffer: arrayBuffer,
            mimeType: blob.type,
          };
        }),
      );

      const buffer = await this.mergePDFs(fileObjects);
      return { buffer };
    } catch (error) {
      console.log(error);
      return null;
    }
  };
  generateCombinedPDF = async (id: number, user_id: number) => {
    try {
      const [container, token] = await Promise.all([
        await this.prisma.containers.findUnique({
          where: { id: id },
          select: {
            vehicles: {
              select: { id: true, auction_invoice: true, vin: true },
            },
          },
        }),
        await this.googleApisService.findToken(user_id),
      ]);
      if (!container) return null;
      const fileIds = container.vehicles
        .map((vehicle) => {
          const url = vehicle?.auction_invoice;
          if (!url) return null;
          return this.extractDriveId(url);
        })
        .filter((item) => item != null);

      const fileObjects = await Promise.all(
        fileIds.map(async (fileId) => {
          const blob: any = await this.googleApisService.getFile(fileId, token);
          const arrayBuffer = await blob.arrayBuffer();

          return {
            buffer: arrayBuffer,
            mimeType: blob.type,
          };
        }),
      );

      const buffer = await this.mergePDFs(fileObjects);
      return { buffer };
    } catch (error) {
      return error;
    }
  };

  mergePDFs = async (fileObjects) => {
    const PDFMerger = await import('pdf-merger-js');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    const merger = new PDFMerger();
    await Promise.all(
      fileObjects.map(async ({ buffer, mimeType }) => {
        if (mimeType == 'application/pdf') {
          return await merger.add(buffer);
        }
        if (mimeType.includes('image')) {
          const imageFile = await this.createPdfFromImage(buffer);
          return await merger.add(await imageFile.arrayBuffer());
        }
      }),
    );
    await merger.setMetadata({
      producer: 'PGL',
      author: 'PGL',
      creator: 'PGL',
      title: 'Combined titles',
    });

    const mergedPdfBuffer = await merger.saveAsBuffer();
    return mergedPdfBuffer;
  };

  createPdfFromImage = async (imgBuffer) => {
    const imageBuffer = await sharp(imgBuffer)
      .resize({ width: 612, height: 792, fit: 'inside' })
      .toBuffer();

    const pdfDoc = await PDFDocument.create();

    const page = pdfDoc.addPage([612, 792]);

    const pngImage = await pdfDoc.embedPng(imageBuffer);
    page.drawImage(pngImage, {
      x: 0,
      y: 0,
      width: 612,
      height: 792,
    });
    const buffer = await pdfDoc.save();
    return new File([buffer], 'example.pdf');
  };

  // generateInvoice = async (
  //   dto: {
  //     id: number;
  //     booking_id: number;
  //     invoice_number: any;
  //     company_id: number;
  //     no_units_load: string;
  //     loading_date: Date | null;
  //     ingate_date: Date | null;
  //     bookings: {
  //       vessel_id: number;
  //       eta: any;
  //       port_of_discharge: number;
  //       size: string;
  //       vessels: {
  //         port_of_loading: number;
  //         steamshipline_id: number;
  //         etd: Date | null;
  //       };
  //     };
  //     vehicles: {
  //       id: number;
  //       loading_city_id: number;
  //       vin: string;
  //       load_type: string;
  //       halfcut_status: string;
  //     }[];
  //   },
  //   created_by: number,
  // ) => {
  //   try {
  //     // return await this.prisma.$transaction(async () => {
  //     const sizeMapping = {
  //       '20 Standard': 'rate_20ft',
  //       '40 HC': 'rate_40hc',
  //       '45 HC': 'rate_45hc',
  //     };

  //     const {
  //       company_id,
  //       bookings,
  //       id,
  //       invoice_number,
  //       no_units_load,
  //       vehicles,
  //       ingate_date,
  //       loading_date,
  //     } = dto;

  //     const {
  //       vessels: { port_of_loading, steamshipline_id, etd },
  //       port_of_discharge,
  //       eta,
  //       size,
  //     } = bookings;

  //     const invoice = await this.prisma.invoices.findFirst({
  //       where: {
  //         container_id: id,
  //         invoice_number: invoice_number,
  //       },
  //     });

  //     if (invoice) {
  //       return false;
  //     }

  //     let applyDate = etd;

  //     const company = await this.prisma.companies.findUnique({
  //       where: { id: company_id },
  //       select: { id: true, rate_apply_date: true },
  //     });

  //     switch (company.rate_apply_date) {
  //       case rate_apply_date_enum.loading_date:
  //         applyDate = loading_date;
  //         break;
  //       case rate_apply_date_enum.ingate_date:
  //         applyDate = ingate_date;
  //         break;
  //       case rate_apply_date_enum.purchase_date:
  //       case rate_apply_date_enum.etd_date:
  //       default:
  //         applyDate = etd;
  //         break;
  //     }

  //     const allFull = vehicles.every((item) => item.load_type === 'full');
  //     if (!allFull || !invoice_number) {
  //       return false;
  //     }

  //     const shippingRate = await this.prisma.shipping_rates_2.findFirst({
  //       select: { id: true },
  //       orderBy: { id: 'desc' },
  //       where: {
  //         company_id: company_id,
  //         rate_type: 'complete',
  //         effective_date: {
  //           lte: applyDate,
  //         },
  //         OR: [
  //           {
  //             effective_to: {
  //               gt: applyDate,
  //             },
  //           },
  //           {
  //             effective_to: null,
  //           },
  //         ],
  //       },
  //     });

  //     let shippingRateId = shippingRate?.id;

  //     if (!shippingRate?.id) {
  //       const generalShippingRate =
  //         await this.prisma.shipping_rates_2.findFirst({
  //           select: { id: true },
  //           orderBy: { id: 'desc' },
  //           where: {
  //             company_id: null,
  //             rate_type: 'complete',
  //             effective_date: {
  //               lte: applyDate,
  //             },
  //             OR: [
  //               {
  //                 effective_to: {
  //                   gt: applyDate,
  //                 },
  //               },
  //               {
  //                 effective_to: null,
  //               },
  //             ],
  //           },
  //         });

  //       shippingRateId = generalShippingRate?.id;
  //     }

  //     if (shippingRateId) {
  //       const shippingRateShipline =
  //         await this.prisma.shipping_rate_destination_location_shiplines.findMany(
  //           {
  //             where: {
  //               OR: [
  //                 {
  //                   shipline_id: steamshipline_id,
  //                 },
  //                 { shipline_id: null },
  //               ],
  //               archived: false,
  //               shipping_rate_destination_location: {
  //                 location_id: port_of_loading,
  //                 archived: false,
  //                 shipping_rate_destination: {
  //                   destination_id: port_of_discharge,
  //                   shipping_rate: {
  //                     id: shippingRateId,
  //                   },
  //                 },
  //               },
  //             },
  //             select: {
  //               id: true,
  //               rates: true,
  //               shipline_id: true,
  //             },
  //             orderBy: {
  //               shipping_rate_destination_location: {
  //                 shipping_rate_destination: {
  //                   shipping_rate: { effective_date: 'asc' },
  //                 },
  //               },
  //             },
  //           },
  //         );

  //       let shiplineRate = null,
  //         targetRate = 0;

  //       if (shippingRateShipline.length == 1) {
  //         shiplineRate = shippingRateShipline[0];
  //       } else if (shippingRateShipline.length > 1) {
  //         shiplineRate = shippingRateShipline.find(
  //           (item) => item.shipline_id == steamshipline_id,
  //         );
  //       }

  //       if (shiplineRate && shiplineRate?.rates?.[sizeMapping[size]]) {
  //         targetRate = shiplineRate?.rates[sizeMapping[size]]?.current;
  //       }

  //       if (vehicles.length > 0 && targetRate > 0) {
  //         const cost_per_vehicle = (targetRate / vehicles.length).toFixed(2);
  //         const vehicle_ids = vehicles.map((row) => row.id);
  //         await this.prisma.vehicle_costs.updateMany({
  //           where: {
  //             vehicle_id: { in: vehicle_ids },
  //           },
  //           data: {
  //             ship_cost: parseFloat(cost_per_vehicle),
  //           },
  //         });
  //       }

  //       await this.prisma.invoices.create({
  //         data: {
  //           status: 'auto_generated',
  //           company_id: company_id,
  //           invoice_number: invoice_number,
  //           invoice_date: new Date(),
  //           purpose: no_units_load,
  //           invoice_due_date: new Date(eta),
  //           container_id: id,
  //           status_changed_at: new Date(),
  //           created_by,
  //         },
  //       });
  //     }

  //     await Promise.all(
  //       vehicles.map(async (v) => {
  //         const towing_rate = await this.prisma.towing_rates.findFirst({
  //           where: {
  //             location_id: port_of_loading,
  //             loading_city_id: v.loading_city_id,
  //           },
  //         });
  //         if (towing_rate) {
  //           await this.prisma.vehicle_costs.update({
  //             where: {
  //               vehicle_id: v.id,
  //               OR: [
  //                 {
  //                   towing_cost: 0,
  //                 },
  //                 {
  //                   towing_cost: null,
  //                 },
  //               ],
  //             },
  //             data: {
  //               towing_cost: towing_rate.towing,
  //             },
  //           });
  //         }
  //       }),
  //     );

  //     this.prisma.$disconnect();
  //     // });
  //   } catch (error) {
  //     this.prisma.$disconnect();
  //     console.log('invoice generation error', error);
  //   }
  // };

  // Check added to tackle something duplicate objects were coming, not every time.

  removeDuplicateCost = (arr) => {
    const seen = new Set();
    return arr.filter((item) => {
      if (seen.has(item.name)) {
        return false;
      } else {
        seen.add(item.name);
        return true;
      }
    });
  };

  async updateContainerDriver(
    updateContainerDriverDto: UpdateContainerDriverDto,
  ) {
    const { containerId, driverId, driverType } = updateContainerDriverDto;

    try {
      const data =
        driverType === 'pull'
          ? { pull_driver_id: driverId }
          : { ingate_driver_id: driverId };
      const include =
        driverType === 'pull' ? { pullDriver: true } : { inGateDriver: true };

      const updatedContainer = await this.prisma.containers.update({
        where: { id: containerId },
        data,
        include,
      });

      return { result: true, data: updatedContainer };
    } catch (error) {
      throw error; // Handle error appropriately
    } finally {
      this.prisma.$disconnect();
    }
  }

  async generalSearch(query: { search: string }) {
    const { search } = query;

    if (!search) {
      return {
        containers: [],
        vehicles: [],
        invoices: [],
        totalRecords: 0,
      };
    }

    // Search containers by container number, booking number, or invoice number
    const containerSearch = this.prisma.containers.findMany({
      where: {
        OR: [
          { container_number: search },
          {
            bookings: {
              booking_number: search,
            },
          },
          { invoice_number: search },
        ],
      },
      select: {
        id: true,
        aes_itn_number: true,
        seal_number: true,
        amount: true,
        loadplan_sent_at: true,
        companies: { select: { name: true } },
        bookings: {
          select: {
            booking_number: true,
            size: true,
          },
        },
        container_number: true,
        booking_suffix: true,
        status: true,
        vehicles: {
          select: {
            vin: true,
            year: true,
            make: true,
            model: true,
            color: true,
            weight: true,
            price: true,
            vehicle_costs: {
              select: {
                towing_cost: true,
                dismantal_cost: true,
                ship_cost: true,
                title_charge: true,
                other_cost: true,
                pgl_storage_costs: true,
              },
            },
            vehicle_charges: {
              where: {
                deleted_at: null,
              },
            },
            companies: {
              select: { name: true },
            },
            vehicle_towings: {
              select: { tow_amount: true },
            },
          },
        },
      },
    });

    // Search vehicles by VIN or lot number
    const vehicleSearch = this.prisma.vehicles.findMany({
      where: {
        OR: [
          { vin: search },
          { lot_number: search },
          {
            containers: {
              container_number: search,
            },
          },
        ],
      },
      include: {
        pgl_used_cars: { select: { id: true } },
        yard_inventories: { select: { id: true } },
        vehicle_towings: {
          select: {
            towing_company: true,
            tow_amount: true,
          },
        },
        vehicle_costs: {
          select: {
            towing_cost: true,
            dismantal_cost: true,
            ship_cost: true,
            title_charge: true,
            other_cost: true,
            pgl_storage_costs: true,
          },
        },
        vehicle_charges: {
          where: {
            deleted_at: null,
          },
        },
        payments: paymentSelect(),
        companies: { select: { name: true } },
        customers: {
          select: {
            companies: {
              select: {
                name: true,
                destinations: {
                  select: {
                    name: true,
                    id: true,
                  },
                },
              },
            },
          },
        },
        containers: {
          select: {
            id: true,
            container_number: true,
            loading_date: true,
            booking_suffix: true,
            bookings: {
              select: {
                parent_id: true,
                booking_number: true,
                parent: {
                  select: {
                    booking_number: true,
                    id: true,
                  },
                },
                eta: true,
                vessels: { select: { etd: true } },
              },
            },
          },
        },
        pol_locations: { select: { name: true } },
        destinations: { select: { name: true } },
        yards_location: { select: { name: true, id: true } },
      },
    });

    // Search invoices by invoice number or container number
    const invoiceSearch = this.prisma.invoices.findMany({
      where: {
        OR: [
          { invoice_number: search },
          {
            containers: {
              container_number: search,
            },
          },
        ],
      },
      include: {
        containers: {
          select: {
            id: true,
            container_number: true,
            status: true,
            vehicles: {
              select: {
                vehicle_costs: {
                  select: {
                    towing_cost: true,
                    dismantal_cost: true,
                    ship_cost: true,
                    title_charge: true,
                    other_cost: true,
                    pgl_storage_costs: true,
                  },
                },
                vehicle_charges: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
            },
            bookings: {
              select: {
                booking_number: true,
                vessels: {
                  select: { id: true, etd: true },
                },
                parent: {
                  select: {
                    booking_number: true,
                    id: true,
                  },
                },
              },
            },
          },
        },
        companies: { select: { name: true } },
      },
    });

    try {
      const [containers, vehicles, invoices] = await Promise.all([
        containerSearch,
        vehicleSearch,
        invoiceSearch,
      ]);

      const totalContainers = containers.length;
      const totalVehicles = vehicles.length;
      const totalInvoices = invoices.length;
      const totalRecords = totalContainers + totalVehicles + totalInvoices;

      return { containers, vehicles, invoices, totalRecords };
    } catch (err) {
      console.error('Error during search:', err);
      throw new Error('Error during search');
    } finally {
      await this.prisma.$disconnect();
    }
  }
}
