import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Put,
  NotFoundException,
  ParseArrayPipe,
  UseInterceptors,
  UploadedFiles,
  Res,
  UploadedFile,
} from '@nestjs/common';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import {
  SearchPaginateDto,
  TabWithoutFilterPaginateDto,
  atToContainerDto,
  dateDto,
} from 'src/Commons/dto/searchPaginateDto.dto';
import PermissionGuard from 'src/Commons/guards/permissions.guard';
import { User } from 'src/Commons/decorators/user.decorator';
import { AdminGuard } from 'src/Commons/guards/jwt.guard';
import { ContainersService } from 'src/Admin_APIs/containers/containers.service';
import { LocationsService } from '../locations/locations.service';
import { container_status } from '@prisma/client';
import {
  ContainerSummary,
  CreateContainerDto,
  UpdateContainerDto,
  changeContainerStatus,
  changeContainerStatusIds,
  changeProfitLossStatus,
  clearanceInvoiceLinkDto,
  filterContainer,
  changeUaeProfitLossStatus,
  changeBalanceDifference,
  changePrelimProfitLossStatus,
  addContainerTransaction,
  UpdateTitleAESStatus,
  UpdatePullIngateDateDto,
  ContainerProfitLoss,
  UpdateContainerDriverDto,
  UploadContainerImageDto,
  ContainerReport,
} from './dto/container.dto';
import { DbService } from 'src/prisma/prisma.service';
import {
  getPreSignedUploadUrl,
  deleteVehicleImagesFromMinio,
  uploadToMinio,
} from 'src/Commons/services/file-service';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import PlAccessGuard from 'src/Commons/guards/placcess.guard';
import { generatePdf } from 'src/Commons/helpers/generatePDF';
import { Response } from 'express';
import shipmentInventoryTemplate from 'src/Commons/pdf_templates/shipment_inventory_pdf_template';
import { getFileStream } from 'src/Commons/services/file-service';
import { UploadVehicleFileDto } from '../vehicles/vehicles/dto/vehicles.dto';
import { Readable } from 'stream';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { existsSync } from 'fs';
import { unlink } from 'fs/promises';
import { Cron } from '@nestjs/schedule';
import * as dayjs from 'dayjs';
import { UpdateCostsDto } from './dto/update-costs.dto';

@Controller('containers')
@ApiTags('Containers')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class ContainersController {
  private prisma: any;
  constructor(
    private readonly containersService: ContainersService,
    private locationService: LocationsService,
  ) {
    this.prisma = DbService.prisma;
  }

  @Get('pull_drivers')
  getDestination() {
    try {
      return this.prisma.drivers
        .findMany({
          select: { name: true, id: true },
        })
        .then((data) => ({ result: data ? true : false, data }))
        .finally(() => this.prisma.$disconnect());
    } catch (error) {
      catch_response(error);
    }
  }

  @Post()
  @UseGuards(PermissionGuard('create_containers'))
  async create(@Body() dto: CreateContainerDto, @User() user: any) {
    return await this.containersService.create(dto, user.loginable_id);
  }

  @Post('/image')
  @UseGuards(PermissionGuard('create_containers'))
  @UseInterceptors(FilesInterceptor('cover'))
  @ApiConsumes('multipart/form-data')
  async uploadImage(
    @Body() dto: UploadVehicleFileDto,
    @User() user: any,
    @UploadedFiles() cover: Express.Multer.File,
  ) {
    return await this.containersService.uploadImage(dto, user, cover);
  }

  @Get('/image')
  @UseGuards(PermissionGuard('view_containers'))
  async getContainerImage(@Query() query, @Res() res: Response) {
    const stream = await getFileStream(query?.key, { isProtected: false });
    stream.pipe(res);
  }

  @Delete('/image/:id')
  @UseGuards(PermissionGuard('create_containers'))
  async deleteContainerImage(@Param('id') id: string, @User() user: any) {
    return await this.containersService.deleteContainerImage(+id, user);
  }

  @Patch('updateLoadingDate/:id')
  @UseGuards(PermissionGuard('update_containers'))
  async updateLoadingDate(
    @Param('id') id: string,
    @Body() dto: dateDto,
    @User() user: any,
  ) {
    return await this.containersService.updateLoadingDate(
      +id,
      dto,
      user.loginable_id,
    );
  }

  @Get()
  @UseGuards(PermissionGuard('view_containers'))
  async findAll(@Query() query: filterContainer, @User() user: any) {
    return await this.containersService.findAll(
      query,
      user.userId,
      user.loginable_id,
    );
  }

  @Get('containers-images')
  @UseGuards(PermissionGuard('view_containers'))
  async getVehicleImages(@Query() query) {
    return await this.containersService.getContainerImages(+query.id);
  }
  // storage-link

  @Get('storage-link')
  @UseGuards(PermissionGuard('view_containers'))
  async getStorageLink(@Query() query) {
    const { signedUrl, fileKey } = await getPreSignedUploadUrl(
      `uploads/containers/images/${query?.id}/${query?.size}`,
      query.fileName,
      query.date,
      query.uuid,
    );
    return { signedUrl, fileKey, result: true };
  }

  @Post('upload-images')
  @UseGuards(PermissionGuard('upload_vehicle_image'))
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadVehicleImages(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: UploadContainerImageDto,
    @User() user: any,
  ) {
    const { key } = await uploadToMinio(
      file,
      dto.destination,
      { isProtected: false, isResizeEnabled: true },
      'vehicle',
    );

    return await this.containersService.storeContainerImages(
      dto,
      `/${process.env.MINIO_BUCKET}/${key}`,
      user,
    );
  }

  @Delete('delete-image')
  @UseGuards(PermissionGuard('delete_containers'))
  async deleteVehicleImage(@Query() query, @User() user: any) {
    await this.containersService.deleteContainerImages(query?.ids, user);
    try {
      const results = await Promise.all(
        query.fileKeys.map((fileKey) => deleteVehicleImagesFromMinio(fileKey)),
      );
      return results;
    } catch (error) {
      throw error;
    }
  }

  @Get('get_specific_vehicles')
  @UseGuards(PermissionGuard('view_containers'))
  async findSpecificVehicles(@Query() query: any) {
    return await this.containersService.findSpecificVehicles(query.id);
  }

  @Get('google-drive/download-images')
  async downloadGoogleDriveImages(
    @Query() query: string,
    @User() user: any,
    @Res() res: Response,
  ) {
    const zipFilePath = await this.containersService.downloadGoogleDriveImages(
      query,
      user,
    );
    res.setHeader('Content-Disposition', 'attachment; filename=images.zip');
    res.setHeader('Content-Type', 'application/zip');
    res.sendFile(zipFilePath, { root: '.' });
    try {
      setTimeout(() => {
        if (existsSync(zipFilePath)) {
          unlink(zipFilePath);
        }
      }, 5000);
    } catch (unlinkError) {
      console.error('Error deleting file:', unlinkError);
    }
  }

  @Get('general-search')
  async generalSearch(@Query() query: filterContainer) {
    return await this.containersService.generalSearch(query);
  }

  @Get('atLoadingAtDock')
  async getAtloadingDock(@Query() query: atToContainerDto) {
    return await this.containersService.getAtLoadingAtDock(query);
  }

  @Get('draftCheck')
  @UseGuards(PermissionGuard('draft_check_view'))
  async getDraftCheck(@Query() query: SearchPaginateDto) {
    return await this.containersService.getDraftCheck(query);
  }

  @Get('pendingArival')
  @UseGuards(PermissionGuard('view_containers'))
  async getCommingSoon(@Query() query: filterContainer, @User() user: any) {
    return await this.containersService.findAll(
      query,
      user.userId,
      user.loginable_id,
    );
  }

  @Get('submitSI')
  @UseGuards(PermissionGuard('view_containers'))
  async getSubmitSI(@Query() query: TabWithoutFilterPaginateDto) {
    return await this.containersService.getSubmitSI(query);
  }

  @Get('pendingArchived')
  @UseGuards(PermissionGuard('view_containers'))
  async getArchived(@Query() query: TabWithoutFilterPaginateDto) {
    return await this.containersService.getArchive(query, 0);
  }

  @Patch('changePendingArrivalStatus')
  @UseGuards(PermissionGuard('view_containers'))
  async changePendingArrivalStatus(
    @Query() query: filterContainer,
    @Body() ids: string,
    @User() user: any,
  ) {
    return await this.containersService.changePendingArrivalStatus(
      ids,
      query,
      user.userId,
      user.loginable_id,
    );
  }

  @Get('titleArchive')
  @UseGuards(PermissionGuard('view_containers'))
  async getTitleArchive(@Query() query: TabWithoutFilterPaginateDto) {
    const location = await this.locationService.getOne('Savannah, GA');
    return await this.containersService.getArchive(query, location.id, true);
  }

  @Get('summary')
  @UseGuards(PermissionGuard('shipment_view_summary'))
  async getSummary(@Query() query: ContainerSummary) {
    const data = await this.containersService.getSummary(query);
    return { result: true, ...data };
  }
  @Get('report')
  @UseGuards(PermissionGuard('view_shipment_reports'))
  async getReport(@Query() query: ContainerReport) {
    const data = await this.containersService.getReport(query);
    return { result: true, ...data };
  }
  @Get('profitLoss')
  @UseGuards(PlAccessGuard())
  async getprofitLoss(@Query() query: ContainerProfitLoss) {
    const data = await this.containersService.getprofitLoss(query);
    return { result: true, ...data };
  }

  @Patch('updateCosts/:id')
  async updateCosts(
    @Param('id') id: number,
    @Body() dto: UpdateCostsDto,
    @User() user: any,
  ) {
    const result = await this.containersService.updateCosts(
      id,
      dto,
      user?.loginable_id,
    );
    if (!result) throw new NotFoundException('Container not found');

    return {
      result: true,
      data: result,
    };
  }

  @Get('count')
  async count() {
    const count = await this.containersService.getTotal({
      deleted_at: null,
      deleted_by: null,
      status: {
        in: [
          container_status.at_loading,
          container_status.on_the_way,
          container_status.arrived,
          container_status.pending,
          container_status.at_the_dock,
          container_status.checked,
          container_status.final_checked,
          container_status.cbp_inspection,
        ],
      },
    });
    return { data: { count }, result: true };
  }

  @Get('trash')
  @UseGuards(PermissionGuard('trash_view_containers'))
  async getTrash(@Query() query: SearchPaginateDto) {
    return await this.containersService.getTrash(query);
  }

  @Get('pending-trash')
  @UseGuards(PermissionGuard('pending_trash_view_containers'))
  async getPendingTrash(@Query() query: SearchPaginateDto) {
    return await this.containersService.getPendingTrash(query);
  }

  @Get('multiple/:ids')
  @UseGuards(PermissionGuard('view_containers'))
  async viewMultiple(@Param('ids', ParseArrayPipe) ids: number[]) {
    return await this.containersService.findMultiple(ids);
  }
  @Get('release-base-company/:ids')
  @UseGuards(PermissionGuard('view_containers'))
  async viewBaseCompany(@Param('ids', ParseArrayPipe) ids: number[]) {
    return await this.containersService.generateSinglePdfBaseOnCompany(ids);
  }

  @Get('release-document/:ids')
  @UseGuards(PermissionGuard('view_containers'))
  async generateMultiReleaseDoc(@Param('ids', ParseArrayPipe) ids: number[]) {
    return await this.containersService.generateMultiReleaseDoc(ids);
  }
  @Get('combined-titles/:id')
  @UseGuards(PermissionGuard('view_containers'))
  async generateCombinedTitles(
    @Param('id') container_id: number,
    @User() user: any,
    @Res() res: Response,
  ) {
    const mergedPdf = await this.containersService.generateCombinedTitles(
      container_id,
      user.loginable_id,
    );
    if (!mergedPdf) {
      res.status(404).json({ result: false, message: 'Not found!' });
      return;
    }

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${container_id}-combined-titles.pdf"`,
    );

    res.setHeader('Content-Type', 'application/pdf');
    const fileStream = new Readable();
    fileStream.push(mergedPdf?.buffer);
    fileStream.push(null);
    fileStream.pipe(res);
    return;
  }
  @Get('combined-pdf/:id')
  @UseGuards(PermissionGuard('view_containers'))
  async generateCombinedPDF(
    @Param('id') container_id: number,
    @User() user: any,
    @Res() res: Response,
  ) {
    const mergedPdf = await this.containersService.generateCombinedPDF(
      container_id,
      user.loginable_id,
    );
    if (!mergedPdf) {
      res.status(404).json({ result: false, message: 'Not found!' });
      return;
    }

    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${container_id}-combined-titles.pdf"`,
    );

    res.setHeader('Content-Type', 'application/pdf');
    const fileStream = new Readable();
    fileStream.push(mergedPdf?.buffer);
    fileStream.push(null);
    fileStream.pipe(res);
    return;
  }

  @Get('inventories')
  @UseGuards(PermissionGuard('view_containers'))
  async getInventories(
    @Query() query: { company_id: string; from: string; to: string },
  ) {
    return await this.containersService.getInventories(query);
  }

  @Post('inventories')
  @UseGuards(PermissionGuard('view_containers'))
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  async sendInventories(
    @Body()
    body: {
      company_id: string;
      from: string;
      to: string;
      subject: string;
      description: string;
    },
    @UploadedFiles() files,
  ) {
    return await this.containersService.sendInventories(body, files);
  }

  @Post('arrival-notices')
  async sendArrivalNotices(
    @Body()
    body: {
      company_id: number;
      container_ids: number[];
      subject: string;
      description: string;
      free_days: {
        id: number;
        value: number;
      }[];
      cc: string[];
      emails: string[];
    },
    @User() user: any,
  ) {
    return await this.containersService.sendArrivalNotices(body, user);
  }

  @Post('arrival-notices/get-pdf')
  async getArrivalNoticePDF(
    @Res() res,
    @Body()
    body: {
      company_id: number;
      container_ids: number[];
      subject: string;
      description: string;
      free_days: {
        id: number;
        value: number;
      }[];
    },
  ) {
    const pdf = await this.containersService.downloadArrivalNoticePDF(body);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="shipping-rates.pdf"',
    );
    res.send(pdf);
  }

  @Get('download-inventories')
  @UseGuards(PermissionGuard('view_containers'))
  async downloadShippingRate(
    @Res() res: Response,
    @Query() query: { company_id: string; from: string; to: string },
  ) {
    const data = await this.containersService.getInventories(query);
    const htmlTemplmate = await shipmentInventoryTemplate(data, query);
    const pdf = await generatePdf(htmlTemplmate, null, true);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="shipment-inventory.pdf"',
    );
    res.send(pdf);
  }

  @Get(':id')
  @UseGuards(PermissionGuard('view_containers'))
  async findOne(@Param('id') id: string) {
    return await this.containersService.findOne(+id);
  }

  @Get('yardLocation/:bookingId')
  async getYardLocation(@Param('bookingId') bookingId: string) {
    // const booking_suffix = await this.containersService.getMaxBookingSuffix(
    //   +bookingId,
    // );
    const bookingSuffix2 =
      await this.containersService.getMaxBookingSuffix2(+bookingId);
    // const bs = bookingSuffix2 || '';
    const data = await this.containersService.getYardLocation(+bookingId);
    // if (!data) {
    //   throw new NotFoundException('The yard locations do not exist.');
    // }
    // const booking_suffix =
    //   bs && bs !== '' && Number.isInteger(+bs) ? +bs + 1 : 1;
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    data[0].booking_suffix = bookingSuffix2;
    return { result: true, data: data };
  }

  @Get('drivers/:bookingId')
  async getDrivers(@Param('bookingId') bookingId: string) {
    const data = await this.containersService.getDrivers(+bookingId);
    return { result: true, data: data };
  }

  @Get('loaders/:bookingId')
  async getLoaders(@Param('bookingId') bookingId: string) {
    const data = await this.containersService.getLoaders(+bookingId);
    return { result: true, data: data };
  }

  @Get('checkContainerNumberSuffix/:containerNumber')
  async getContainerNumberSuffix(
    @Param('containerNumber') containerNumber: string,
  ) {
    const bookingSuffix =
      await this.containersService.getContainerSuffix(containerNumber);
    const bs: number | string = bookingSuffix || '';
    const booking_suffix =
      bs && bs !== '' && Number.isInteger(+bs) ? +bs + 1 : 1;

    return { result: true, data: booking_suffix };
  }

  @Patch('clearanceInvoiceLink/:id')
  @UseGuards(PermissionGuard('check_to_any'))
  async updateInvoiceLink(
    @Param('id') id: string,
    @Body() dto: clearanceInvoiceLinkDto,
    @User() user: any,
  ) {
    const data = await this.containersService.updateInvoiceLink(
      +id,
      dto,
      user.loginable_id,
    );
    return { result: true, data };
  }
  @Patch('update-pull-ingate-date/:id')
  @UseGuards(PermissionGuard('update_containers'))
  async updatePullIngateDate(
    @Param('id') id: string,
    @Body() dto: UpdatePullIngateDateDto,
    @User() user: any,
  ) {
    const data = await this.containersService.updatePullIngateDate(
      +id,
      dto,
      user.loginable_id,
    );
    return { result: true, data };
  }

  @Patch('changeCheckToAny')
  @UseGuards(PermissionGuard('check_to_any'))
  async changeCheckToAny(
    @Body() query: changeContainerStatus,
    @User() user: any,
  ) {
    return await this.containersService.changeCheckToAny(
      query,
      user.loginable_id,
    );
  }

  /*  @Patch('addRemark')
  @UseGuards(PermissionGuard('update_containers'))
  async addRemark(@Body() query: addRemarkDto, @User() user: any) {
    return await this.containersService.addRemark(query, user.loginable_id);
  } */

  @Patch('changeFinalCheckToAny')
  @UseGuards(PermissionGuard('final_check_to_any'))
  async changeFinalCheckToAny(
    @Body() query: changeContainerStatus,
    @User() user: any,
  ) {
    return await this.containersService.changeFinalCheckToAny(
      query,
      user.loginable_id,
    );
  }

  @Patch('changeAnyToClearance')
  @UseGuards(PermissionGuard('to_clearance'))
  async changeAnyToClearance(
    @Body() query: changeContainerStatusIds,
    @User() user: any,
  ) {
    return await this.containersService.changeAnyToClearance(
      query,
      user.loginable_id,
    );
  }

  @Patch('changeContainerStatus')
  @UseGuards(PermissionGuard('change_status_containers'))
  async changeContainerStatus(
    @Body() query: changeContainerStatus,
    @User() user: any,
  ) {
    return await this.containersService.changeContainerStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch('updateTitleAESStatus/:id')
  async updateTitleAESStatus(
    @Param('id') id: string,
    @Body() query: UpdateTitleAESStatus,
    @User() user: any,
  ) {
    return await this.containersService.updateTitleAESStatus(
      +id,
      query,
      user.loginable_id,
    );
  }

  @Patch('changeProfitLossStatus')
  @UseGuards(PlAccessGuard())
  async changeProfitLossStatus(
    @Body() query: changeProfitLossStatus,
    @User() user: any,
  ) {
    return await this.containersService.changeProfitLossStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch('changeUaeProfitLossStatus')
  @UseGuards(PlAccessGuard())
  async changeUaeProfitLossStatus(
    @Body() query: changeUaeProfitLossStatus,
    @User() user: any,
  ) {
    return await this.containersService.changeUaeProfitLossStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch('changePrelimProfitLossStatus')
  @UseGuards(PlAccessGuard())
  async changePrelimProfitLossStatus(
    @Body() query: changePrelimProfitLossStatus,
    @User() user: any,
  ) {
    return await this.containersService.changePrelimProfitLossStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch('removeEtd2025Check')
  @UseGuards(PlAccessGuard())
  async removeEtd2025Check(@Body() query: changeContainerStatusIds) {
    return await this.containersService.removeEtd2025Check(query);
  }

  @Patch('changeBalanceDifference')
  @UseGuards(PlAccessGuard())
  async changeBalanceDifference(
    @Body() query: changeBalanceDifference,
    @User() user: any,
  ) {
    return await this.containersService.changeBalanceDifference(
      query,
      user.loginable_id,
    );
  }

  @Patch('addContainerTransaction')
  @UseGuards(PermissionGuard('create_containers_transaction'))
  async addContainerTransaction(
    @Body() query: addContainerTransaction,
    @User() user: any,
  ) {
    return await this.containersService.addContainerTransaction(
      query,
      user.loginable_id,
    );
  }

  @Patch(':id')
  @UseGuards(PermissionGuard('update_containers'))
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateContainerDto,
    @User() user: any,
  ) {
    if (dto.container_number)
      await this.containersService.validate(
        'container_number',
        dto.container_number,
        +id,
      );

    return await this.containersService.update(+id, dto, user.loginable_id);
  }

  @Delete(':ids')
  @UseGuards(PermissionGuard('delete_containers'))
  async remove(
    @Param('ids', ParseArrayPipe) rawIds: string[],
    @Body() body: { deleted_reason?: string },
    @User() user: any,
  ) {
    const ids = rawIds.map((id) => parseInt(id, 10));
    const containersWithVehicles = await this.prisma.containers.findMany({
      where: {
        AND: [
          { id: { in: ids } },
          {
            OR: [
              {
                vehicles: { some: {} },
                invoices: { some: {} },
                mix_shipping_containers: { some: {} },
                mix_shipping_invoices: { some: {} },
                clear_logs: { some: {} },
              },
            ],
          },
        ],
      },
    });
    if (containersWithVehicles.length > 0) {
      throw new NotFoundException(
        'Cannot delete containers associated with other modules.',
      );
    }
    return await this.containersService.softDeleteOne(
      ids,
      user.loginable_id,
      body.deleted_reason,
    );
  }

  @Put('trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_containers'))
  async restore(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.containersService.restore(ids, user.loginable_id);
  }

  @Put('pending-trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_pending_trash_containers'))
  async restorePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.containersService.restorePendingTrash(
      ids,
      user.loginable_id,
    );
  }

  @Put('pending-trash/delete/:ids')
  @UseGuards(PermissionGuard('delete_pending_trash_containers'))
  async deletePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.containersService.deletePendingTrash(
      ids,
      user.loginable_id,
    );
  }

  /* @Delete('trash/forceDelete/:ids')
  @UseGuards(PermissionGuard('force_delete_containers'))
  async forceDelete(@Param('ids', ParseArrayPipe) ids: number[]) {
    const data = await this.containersService.forceDeleteOne(ids);
    if (data.count) return { data, result: true };
    else return { result: false, message: 'Delete Failed.' };
  } */

  @Get('ETA/:containerId')
  async findMany(@Param('containerId') containerId: string) {
    const data = await this.containersService.findMany(+containerId);
    if (!data) {
      throw new NotFoundException('The container ETA does not exist.');
    }
    return { result: true, data };
  }

  @Post('send-loading-plan')
  @UseGuards(PermissionGuard('send_load_plan'))
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  async sendLoadingPlan(
    @Body()
    body: {
      ids: string;
      subject: string;
      description: string;
      emails: string[];
    },
    @User() user: any,
    @UploadedFiles() files,
  ) {
    return await this.containersService.sendLoadingPlan(
      body,
      user.loginable_id,
      files,
    );
  }
  @Put('updateDriver')
  async updateContainerDriver(
    @Body() updateContainerDriverDto: UpdateContainerDriverDto,
  ) {
    try {
      const result = await this.containersService.updateContainerDriver(
        updateContainerDriverDto,
      );
      return result;
    } catch (error) {
      return { result: false, message: error.message }; // Handle error response
    }
  }

  @Cron('0 */8 * * *') // ✅ Runs every 8 hours (00:00, 08:00, 16:00)
  async handleClearLoadNote() {
    await this.prisma.containers.updateMany({
      where: {
        bookings: {
          eta: {
            lte: dayjs(new Date()).add(15, 'day').toDate(),
            gte: new Date(),
          },
        },
        pending_arrival: null,
      },
      data: {
        pending_arrival: true,
      },
    });
  }
}
