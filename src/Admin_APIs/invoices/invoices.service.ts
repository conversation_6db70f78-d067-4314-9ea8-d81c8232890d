import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import {
  CreateInvoiceDto,
  UpdateInvoiceDto,
  changeInvoicesIds,
  changeInvoicesStatus,
  filterInvoices,
} from './dto/invoice.dto';
import { SearchPaginateDto } from 'src/Commons/dto/searchPaginateDto.dto';
import { CommonFields } from 'src/Commons/services/common.fields';
import { invoice_status, pl_status, prelim_pl_status } from '@prisma/client';
import { catch_response } from 'src/Commons/helpers/response.helper';
import { ordersBy } from 'src/Commons/helpers/ordersby.helper';
import { searchFilter } from 'src/Commons/helpers/searchFilter.helper';
import { DbService } from 'src/prisma/prisma.service';
import { ContainersService } from '../containers/containers.service';
import { createLog } from 'src/Commons/helpers/log_functions.helper';
import { CheckPermissionsService } from '../users/check-permissions.service';
import { paymentSelect } from 'src/Commons/helpers/customerPaymentTransactions.helper';
import { Cron } from '@nestjs/schedule';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

const create_update_delete_by = CommonFields([
  'users_invoices_created_byTousers',
  'users_invoices_updated_byTousers',
  'users_invoices_deleted_byTousers',
]);
const create_update_by = CommonFields([
  'users_invoices_created_byTousers',
  'users_invoices_updated_byTousers',
]);

const searchColumns = [
  'invoice_number',
  'purpose',
  'payment_method',
  'description',
  'int@@invoice_amount',
  'evidence_proof',
  'date@@invoice_date',
  'date@@invoice_due_date',
  'containers.container_number',
  'companies.name',
  'containers.bookings.booking_number',
  'containers.bookings.parent.booking_number',
];

@Injectable()
export class InvoicesService {
  private prisma: typeof DbService.prisma;
  private readonly logger = new Logger(InvoicesService.name);

  constructor(
    private readonly containersService: ContainersService,
    private readonly checkPermissionsService: CheckPermissionsService,
    private readonly db: DbService,
    @InjectRedis() private readonly redis: Redis,
  ) {
    this.prisma = DbService.prisma;
  }

  create = async (dto: CreateInvoiceDto, created_by: number) => {
    const loadingTitleCostMissingCheck =
      await this.containersService.hasLoadingTitleCostMissing(dto.container_id);
    if (loadingTitleCostMissingCheck.result) {
      throw new HttpException(
        {
          result: false,
          message: loadingTitleCostMissingCheck.message,
          status: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.prisma.invoices
      .create({
        data: {
          company_id: dto.company_id,
          invoice_number: dto.invoice_number,
          invoice_date: new Date(dto.invoice_date),
          purpose: dto.purpose,
          invoice_due_date: new Date(dto.invoice_due_date),
          container_id: dto.container_id,
          invoice_amount: dto.invoice_amount,
          discount: dto.discount,
          payment_received: dto.payment_received,
          payment_method: dto.payment_method,
          description: dto.description,
          evidence_proof: dto.evidence_proof,
          title_charge_visible: dto.title_charge_visible,
          towing_charge_visible: dto.towing_charge_visible,
          created_by,
          status_changed_at: new Date(),
          updated_by: created_by,
        },
      })
      .then((res) => this.findOne(res?.id))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findOne = async (id: number) => {
    try {
      const data = await this.prisma.invoices.findUnique({
        where: { id },
        include: {
          users_invoices_checked_byTousers: { select: { fullname: true } },
          users_invoices_cleared_byTousers: { select: { fullname: true } },
          users_invoices_rejected_byTousers: {
            select: {
              id: true,
              fullname: true,
              departments: { select: { name: true } },
            },
          },
          containers: {
            select: {
              id: true,
              container_number: true,
              pl_status: true,
              vehicles: {
                select: {
                  id: true,
                  vin: true,
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                },
              },
              bookings: {
                select: {
                  id: true,
                  booking_number: true,
                  vessels: {
                    select: {
                      id: true,
                      name: true,
                      steamshiplines: { select: { name: true } },
                      vessel_status: true,
                      locations: { select: { id: true, name: true } },
                    },
                  },
                  destinations: {
                    select: { id: true, name: true, order: true },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: {
            select: { id: true, name: true, is_belong_to_used_car: true },
          },
          ...create_update_by,
        },
      });

      return { result: !!data, data };
    } catch (e) {
      catch_response(e);
    } finally {
      await this.prisma.$disconnect();
    }
  };

  getInvoiceReport = async (
    query: filterInvoices,
    login_id: number,
    user_id: number,
    customerId?: number,
  ) => {
    const offset = query.page;
    const limit = +query.per_page;
    const searchOptions =
      query.searchOptions && JSON.parse(query.searchOptions.replace(/'/g, '"'));
    const searchBy = searchOptions?.length > 0 ? searchOptions : searchColumns;
    const where = await searchFilter(query, searchBy);
    const isExport = String(query.isExport);
    const exportTotal = query.exportTotal;
    const exportType = query.exportType;

    if (isExport == 'true') {
      createLog({
        log_name: 'pgl',
        description: 'Export',
        subject_type: 'User',
        subject_id: user_id,
        causer_type: 'User',
        causer_id: user_id,
        properties: { exportTotal: exportTotal, exportType: exportType },
      });
    }

    if (query.search) {
      await this.prisma.invoices.findMany({
        where: {
          OR: [
            { invoice_number: query.search },
            { containers: { container_number: query.search } },
          ],
        },
        select: { id: true },
      });
    }

    const orderBy: any[] = [{ id: 'desc' }];
    if (customerId)
      where['companies'] = { customers: { some: { id: customerId } } };

    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    const today = new Date();
    const fortyFiveDaysAgo = new Date(today);
    fortyFiveDaysAgo.setDate(today.getDate() - 45);

    const pastDueDays = JSON.parse(query.filterData)?.past_due_days;
    if (pastDueDays) {
      const today = new Date();
      today.setDate(today.getDate() - pastDueDays);
      if (where['AND']) {
        where['AND'].push({ invoice_due_date: { lte: today } });
      } else {
        where['AND'] = [{ invoice_due_date: { lte: today } }];
      }
    }
    const filterData = JSON.parse(query.filterData);
    const statusCondition =
      filterData.status && filterData.status.length > 0
        ? { status: { in: filterData.status } } // Use 'in' to match any of the statuses
        : { status: 'open' };

    if (!where.AND) {
      where.AND = [];
    }

    where.AND.push({
      invoice_due_date: {
        lt: fortyFiveDaysAgo,
      },
      ...statusCondition,
    });
    return await Promise.all([
      this.prisma.invoices.count({ where }),

      isExport == 'false'
        ? this.prisma.invoices.findMany({
            ...paginate,
            where,
            orderBy,
            include: {
              containers: {
                select: {
                  id: true,
                  container_number: true,
                  status: true,
                  vehicles: {
                    select: {
                      vehicle_costs: {
                        select: {
                          towing_cost: true,
                          dismantal_cost: true,
                          ship_cost: true,
                          title_charge: true,
                          other_cost: true,
                          pgl_storage_costs: true,
                        },
                      },
                      vehicle_charges: {
                        where: {
                          deleted_at: null,
                        },
                      },
                    },
                  },
                  bookings: {
                    select: {
                      booking_number: true,
                      vessels: {
                        select: { id: true, etd: true },
                      },
                      parent: {
                        select: {
                          booking_number: true,
                          id: true,
                        },
                      },
                    },
                  },
                },
              },
              companies: { select: { name: true } },
              ...create_update_by,
            },
          })
        : this.prisma.invoices.findMany({
            where,
            orderBy,
            include: {
              containers: {
                select: {
                  id: true,
                  container_number: true,
                  status: true,
                  vehicles: {
                    select: {
                      vehicle_costs: {
                        select: {
                          towing_cost: true,
                          dismantal_cost: true,
                          ship_cost: true,
                          title_charge: true,
                          other_cost: true,
                          pgl_storage_costs: true,
                        },
                      },
                      vehicle_charges: {
                        where: {
                          deleted_at: null,
                        },
                      },
                    },
                  },
                  bookings: {
                    select: {
                      booking_number: true,
                      vessels: {
                        select: { id: true, etd: true },
                      },
                      parent: {
                        select: {
                          booking_number: true,
                          id: true,
                        },
                      },
                    },
                  },
                },
              },
              companies: { select: { name: true } },
              ...create_update_by,
            },
          }),
    ])
      .then(([total, data]) => {
        data.forEach((item) => {
          const dueDate: any = new Date(item?.invoice_due_date);
          const currentDate: any = new Date();

          const timeDifference = currentDate - dueDate; // in milliseconds
          const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));
          item['age'] = String(daysDifference) + ' days';
          item['balance_due'] = 0;
        });
        if (limit > 0)
          return {
            result: true,
            page: offset,
            per_page: limit,
            total,
            data,
          };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findAll = async (
    query: filterInvoices,
    login_id: number,
    user_id: number,
    customerId?: number,
  ) => {
    const offset = query.page;
    const limit = query.per_page;
    const invoiceStatus = query.status;
    const searchOptions =
      query.searchOptions && JSON.parse(query.searchOptions.replace(/'/g, '"'));
    const searchBy = searchOptions?.length > 0 ? searchOptions : searchColumns;

    // Parse the JSON data from query
    let isRejected = false;
    const filterData = JSON.parse(query.filterData);
    if (filterData?.status?.includes('rejected')) {
      isRejected = true;
    }
    // Check if "status" is an array and remove "rejected"
    if (isRejected && Array.isArray(filterData.status)) {
      filterData.status = filterData.status.filter(
        (status) => status !== 'rejected',
      );

      // Remove `status` key if the array is empty after filtering
      if (filterData.status.length === 0) {
        delete filterData.status;
      }
    }

    // Update the original query data if you need the modified filterData for further use
    query.filterData = JSON.stringify(filterData);

    // Now pass the modified query to searchFilter
    const where = await searchFilter(query, searchBy);
    const isExport = query.isExport;
    const exportTotal = query.exportTotal;
    const exportType = query.exportType;

    if (isExport) {
      createLog({
        log_name: 'pgl',
        description: 'Export',
        subject_type: 'User',
        subject_id: user_id,
        causer_type: 'User',
        causer_id: user_id,
        properties: { exportTotal: exportTotal, exportType: exportType },
      });
    }

    let hasMatch = [];
    if (query.search) {
      hasMatch = await this.prisma.invoices.findMany({
        where: {
          OR: [
            { invoice_number: query.search },
            { containers: { container_number: query.search } },
          ],
        },
        select: { id: true },
      });
    }

    const permissions =
      await this.checkPermissionsService.getUsersPermissionsByGroupNames(
        user_id,
        ['invoices'],
      );

    const restrictions =
      this.checkPermissionsService.getInvoicesRestrictions(permissions);

    if (restrictions) {
      const newRest = {
        OR: [
          ...restrictions.OR,
          hasMatch.length ? { id: { in: hasMatch.map((e) => e.id) } } : {},
        ],
      };
      if (where['AND']) {
        where['AND'].push(newRest);
      } else {
        where['AND'] = [newRest];
      }
    }

    if (isRejected) {
      if (where['AND']) {
        where['AND'].push({ rejected_at: { not: null } });
      } else {
        where['AND'] = [{ rejected_at: { not: null } }];
      }
    }

    const pastDueDays = filterData?.past_due_days;
    if (pastDueDays) {
      const today = new Date();
      today.setDate(today.getDate() - pastDueDays);
      if (where['AND']) {
        where['AND'].push({ invoice_due_date: { lte: today } });
      } else {
        where['AND'] = [{ invoice_due_date: { lte: today } }];
      }
    }

    let orderBy: any[] = [{ id: 'desc' }];
    if (invoiceStatus) where['status'] = invoiceStatus;
    if (customerId)
      where['companies'] = { customers: { some: { id: customerId } } };

    if (invoiceStatus === 'open')
      orderBy = [{ move_to_open_date: 'desc' }, { invoice_date: 'desc' }];

    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    // where['containers'] = {
    //   deleted_at: null,
    //   mix_shipping_invoices: {
    //     some: {
    //       deleted_at: null,
    //     },
    //   },
    // };

    if (!where.AND) {
      where.AND = [];
    }
    where.AND.push({
      OR: [
        {
          containers: {
            deleted_at: null,
            mix_shipping_invoices: {
              none: {},
            },
          },
        },
        {
          containers: {
            mix_shipping_invoices: {
              some: {
                deleted_at: {
                  not: null,
                },
              },
            },
          },
        },
      ],
    });

    // Add zero-balance logic only if `filterData.due_balance` exists
    if (filterData.due_balance) {
      const companyIds = filterData.company_id;
      const rawQuery = () =>
        `
            WITH invoice_costs AS (
        SELECT
          invoices.id AS invoice_id,
          SUM(
                COALESCE(vehicle_costs.dismantal_cost, 0) +
                COALESCE(vehicle_costs.ship_cost, 0) +
                CASE WHEN invoices.title_charge_visible THEN COALESCE(vehicle_costs.title_charge, 0) ELSE 0 END +
                CASE WHEN invoices.towing_charge_visible THEN COALESCE(vehicle_costs.towing_cost, 0) ELSE 0 END +
                COALESCE(vehicle_costs.pgl_storage_costs, 0) +
                COALESCE(vehicle_costs.other_cost, 0) +
                COALESCE(
                        (
                            SELECT SUM(CAST(vehicle_charges.amount AS DOUBLE PRECISION))
                            FROM vehicle_charges
                            WHERE vehicle_charges.vehicle_id = vehicles.id
                             AND vehicle_charges.deleted_at IS NULL
                        ),
                        0
                )
            ) AS amt
        FROM invoices
        LEFT JOIN containers ON containers.id = invoices.container_id
        LEFT JOIN vehicles ON vehicles.container_id = containers.id
        LEFT JOIN vehicle_costs ON vehicles.id = vehicle_costs.vehicle_id
        LEFT JOIN companies comp ON comp.id = invoices.company_id
        WHERE containers.deleted_at IS NULL
        ${companyIds ? `AND comp.id IN (${companyIds})` : ''}
          AND vehicles.deleted_at IS NULL
        GROUP BY invoices.id
      ),
      invoice_payments AS (
        SELECT
          payments.shipment_invoice_id AS invoice_id,
          SUM(payments.amount_applied) AS pay
        FROM payments
        WHERE payments.deleted_at IS NULL
        GROUP BY payments.shipment_invoice_id
      )
      SELECT
        i.id,
        i.invoice_number,
        COALESCE(ic.amt, 0) AS amt,
        COALESCE(ip.pay, 0) AS pay,
        COALESCE(i.discount, 0) AS discount
      FROM invoices i
      LEFT JOIN invoice_costs ic ON ic.invoice_id = i.id
      LEFT JOIN invoice_payments ip ON ip.invoice_id = i.id
      LEFT JOIN containers c ON c.id = i.container_id
      LEFT JOIN bookings b ON b.id = c.booking_id
      LEFT JOIN companies com ON com.id = i.company_id
      WHERE c.deleted_at IS NULL
        ${companyIds ? `AND com.id IN (${companyIds})` : ''}
        ${restrictions && restrictions.sql ? restrictions.sql : ''}
        AND ic.amt > 0
        ${invoiceStatus ? `AND i.status = '${invoiceStatus}'` : ''}
        AND (
          COALESCE(ic.amt, 0) - (COALESCE(ip.pay, 0) + COALESCE(i.discount, 0)) <= 1
        );
      `;
      const response = await this.db.runRaw(rawQuery());

      where.AND.push({
        id: {
          in: response.map((invoice) => invoice.id),
        },
      });

      // Delete `due_balance` from `filterData` to prevent it from being used again
      delete filterData.due_balance;
      // Remove the `due_balance` condition from the `where` clause if it exists
      if (where.AND) {
        where.AND = where.AND.filter((condition) => !condition.due_balance);
      }
    }

    return await Promise.all([
      this.prisma.invoices.count({ where }),
      this.prisma.invoices.findMany({
        ...paginate,
        where,
        orderBy,
        include: {
          containers: {
            select: {
              id: true,
              container_number: true,
              status: true,
              vehicles: {
                select: {
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                },
              },
              bookings: {
                select: {
                  booking_number: true,
                  vessels: {
                    select: {
                      id: true,
                      etd: true,
                      etd_status: true,
                      steamshiplines: { select: { name: true } },
                    },
                  },
                  parent: {
                    select: {
                      booking_number: true,
                      id: true,
                    },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: { select: { name: true } },
          ...create_update_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  findPrintData = (id: number) =>
    this.prisma.invoices
      .findUnique({
        where: { id: id },
        include: {
          users_invoices_checked_byTousers: { select: { fullname: true } },
          users_invoices_cleared_byTousers: { select: { fullname: true } },
          containers: {
            include: {
              bookings: {
                include: {
                  vessels: {
                    include: {
                      locations: { select: { id: true, name: true } },
                    },
                  },
                  destinations: { select: { id: true, name: true } },
                },
              },
              vehicles: {
                include: {
                  vehicle_costs: true,
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: true,
          ...create_update_by,
        },
      })
      .then((data) => ({ result: data ? true : false, data }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  findMultiPrintData = (ids: number[]) =>
    this.prisma.invoices
      .findMany({
        where: { id: { in: ids.map(Number) } },
        include: {
          containers: {
            select: {
              id: true,
              vehicles: {
                select: {
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                },
              },

              container_number: true,
              booking_suffix: true,
              bookings: {
                select: {
                  id: true,
                  booking_number: true,
                  vessels: {
                    select: {
                      locations: { select: { id: true, name: true } },
                    },
                  },
                  destinations: { select: { id: true, name: true } },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: {
            select: {
              name: true,
            },
          },
        },
      })
      .then((data) => data)
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  update = (id: number, dto: UpdateInvoiceDto, updated_by: number) => {
    try {
      return this.prisma.invoices
        .update({
          where: { id: +id },
          data: { ...dto, updated_by },
        })
        .then(async (res) => this.findOne(res?.id))
        .catch((err) => catch_response(err))
        .finally(() => this.prisma.$disconnect());
    } catch (err) {
      console.log('Error:', err);
    }
  };

  getTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const searchFilterTrash = await searchFilter(query, searchColumns);
    const where = {
      ...searchFilterTrash,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: false,
    };
    const orderBy = await ordersBy(query);
    return await Promise.all([
      this.prisma.invoices.count({ where }),
      this.prisma.invoices.findMany({
        skip: (offset - 1) * limit,
        take: limit,
        orderBy,
        where,
        include: {
          users_invoices_checked_byTousers: { select: { fullname: true } },
          users_invoices_cleared_byTousers: { select: { fullname: true } },
          containers: {
            select: {
              id: true,
              container_number: true,
              vehicles: {
                select: {
                  customer_id: true,
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                  vehicle_towings: {
                    select: { tow_amount: true },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: { select: { id: true, name: true } },
          users_invoices_deleted_by_confirmTousers: {
            select: {
              id: true,
              fullname: true,
            },
          },
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getPendingTrash = async (query: SearchPaginateDto) => {
    const offset = query.page;
    const limit = query.per_page;
    const searchFilterTrash = await searchFilter(query, searchColumns);
    const where = {
      ...searchFilterTrash,
      deleted_at: { not: null },
      deleted_by: { not: null },
      isPendingTrash: true,
    };
    const orderBy = await ordersBy(query);
    return await Promise.all([
      this.prisma.invoices.count({ where }),
      this.prisma.invoices.findMany({
        skip: (offset - 1) * limit,
        take: limit,
        orderBy,
        where,
        include: {
          users_invoices_checked_byTousers: { select: { fullname: true } },
          users_invoices_cleared_byTousers: { select: { fullname: true } },
          containers: {
            select: {
              id: true,
              container_number: true,
              vehicles: {
                select: {
                  customer_id: true,
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                  vehicle_towings: {
                    select: { tow_amount: true },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: { select: { id: true, name: true } },
          ...create_update_delete_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  getMixed = async (query: filterInvoices, customerId?: number) => {
    const offset = query.page;
    const limit = query.per_page;
    const invoiceStatus = query.status;
    const where = await searchFilter(query, searchColumns);

    where.AND = [];
    where.AND.push({
      containers: {
        mix_shipping_invoices: {
          some: {
            id: {
              gt: 0,
            },
            deleted_at: null,
          },
        },
        deleted_at: null,
      },
    });
    let orderBy: any[] = [{ id: 'desc' }];
    if (invoiceStatus) where['status'] = invoiceStatus;
    if (customerId)
      where['companies'] = { customers: { some: { id: customerId } } };

    if (invoiceStatus == invoice_status.open)
      orderBy = [{ move_to_open_date: 'desc' }, { invoice_date: 'desc' }];

    const paginate = {};
    if (limit > 0) {
      paginate['skip'] = (offset - 1) * limit;
      paginate['take'] = limit;
    }

    return await Promise.all([
      this.prisma.invoices.count({ where }),
      this.prisma.invoices.findMany({
        ...paginate,
        where,
        orderBy,
        include: {
          containers: {
            select: {
              id: true,
              container_number: true,
              status: true,
              mix_shipping_invoices: {
                select: {
                  id: true,
                  inv_due_date: true,
                },
              },
              vehicles: {
                select: {
                  vehicle_costs: {
                    select: {
                      towing_cost: true,
                      dismantal_cost: true,
                      ship_cost: true,
                      title_charge: true,
                      other_cost: true,
                      pgl_storage_costs: true,
                    },
                  },
                  vehicle_charges: {
                    where: {
                      deleted_at: null,
                    },
                  },
                },
              },
            },
          },
          payments: paymentSelect(),
          companies: { select: { name: true } },
          ...create_update_by,
        },
      }),
    ])
      .then(([total, data]) => {
        if (limit > 0)
          return { result: true, page: offset, per_page: limit, total, data };
        else return { result: true, total, data };
      })
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  };

  restore = (ids: number[], updated_by: number) =>
    this.prisma.invoices
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: { deleted_at: null, deleted_by: null, updated_by },
      })
      .then((res) => ({
        result: res.count > 0 ? true : false,
        message: 'You have restore one record',
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  restorePendingTrash = (ids: number[], updated_by: number) =>
    this.prisma.invoices
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          deleted_at: null,
          deleted_by: null,
          updated_by,
          isPendingTrash: false,
        },
      })
      .then((res) => ({
        result: res.count > 0 ? true : false,
        message: 'You have restore one record',
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  deletePendingTrash = (ids: number[], deleted_by_confirm: number) =>
    this.prisma.invoices
      .updateMany({
        where: {
          id: { in: ids.map(Number) },
        },
        data: {
          isPendingTrash: false,
          deleted_by_confirm,
        },
      })
      .then((res) => ({
        result: res.count > 0 ? true : false,
        message: 'You have restore one record',
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());

  softDeleteOne = (
    ids: number[],
    deleted_by: number,
    deleted_reason?: string,
  ) =>
    this.prisma.invoices
      .updateMany({
        where: { id: { in: ids.map(Number) } },
        data: {
          deleted_at: new Date(),
          deleted_by,
          deleted_reason,
          isPendingTrash: true,
        },
      })
      .then((res) => ({
        result: res.count > 0 ? true : false,
        message: 'You have delete one record',
      }))
      .catch((err) => catch_response(err))
      .finally(() => this.prisma.$disconnect());
  /* forceDeleteOne = (ids: number[]) => {
    const idn = ids.map(Number);
    return prisma.invoices.deleteMany({
      where: {
        AND: [
          { id: { in: idn } },
          { deleted_at: { not: null }, deleted_by: { not: null } },
        ],
      },
    });
  }; */

  changePendingToOpen = async (
    query: changeInvoicesIds,
    updated_by: number,
  ) => {
    const data = await this.editMany({
      where: {
        id: { in: query.invoicesIds },
        status:
          query.activeTab == 'auto_generated'
            ? invoice_status.auto_generated
            : invoice_status.pending,
      },
      data: {
        status:
          query.activeTab == 'auto_generated'
            ? invoice_status.pending
            : invoice_status.final_review,
        updated_by,
        status_changed_at: new Date(),
        // move_to_open_date: new Date(),
      },
    });
    if (data.count > 0)
      return {
        result: true,
        message: `${data.count} records have been updated!`,
      };
    else return { result: false, message: 'No records have been updated.' };
  };

  // invoiceFinalReview = async (query: changeInvoicesIds, updated_by: number) => {
  //   const check = await this.getAll({
  //     where: {
  //       id: { in: query.invoicesIds },
  //       status: invoice_status.final_review,
  //     },
  //   });

  //   if (check.length > 0) {
  //     const data = await this.editMany({
  //       where: {
  //         id: { in: check.map((o) => o.id) },
  //         status: invoice_status.final_review,
  //       },
  //       data:
  //         query.actionType === 'reject'
  //           ? {
  //               status: invoice_status.pending,
  //               updated_by,
  //               rejected_by: updated_by,
  //               rejected_at: new Date(),
  //               status_changed_at: new Date(),
  //               rejected_reason: query.rejected_reason,
  //             }
  //           : {
  //               status: invoice_status.open,
  //               updated_by,
  //               status_changed_at: new Date(),
  //               move_to_open_date: new Date(),
  //               containers: {
  //                 pl_status: pl_status.reviewed,
  //               },
  //             },
  //     });
  //     if (data.count > 0)
  //       return {
  //         result: true,
  //         message: `${data.count} records have been updated!`,
  //       };
  //     else return { result: false, message: 'No records have been updated.' };
  //   } else
  //     return {
  //       result: false,
  //       message: 'There is no invoices in pending state.',
  //     };
  // };

  invoiceFinalReview = async (query: changeInvoicesIds, updated_by: number) => {
    const check = await this.getAll({
      where: {
        id: { in: query.invoicesIds },
        status: invoice_status.final_review,
      },
      include: {
        containers: {
          select: {
            id: true,
            container_number: true,
            status: true,
            bookings: {
              select: {
                booking_number: true,
                vessels: {
                  select: {
                    id: true,
                    etd: true,
                    etd_status: true,
                    steamshiplines: { select: { name: true } },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (check.length === 0) {
      return {
        result: false,
        message: 'There is no invoices in pending state.',
      };
    }
    const isBookkingApproved = check?.some(
      (el) => el.containers.bookings.vessels.etd_status !== 'actual',
    );
    if (isBookkingApproved) {
      return {
        result: false,
        message: 'The booking etd should mark as actual.',
      };
    }

    const data = await this.editMany({
      where: {
        id: { in: check.map((o) => o.id) },
        status: invoice_status.final_review,
      },
      data:
        query.actionType === 'reject'
          ? {
              status: invoice_status.pending,
              updated_by,
              rejected_by: updated_by,
              rejected_at: new Date(),
              status_changed_at: new Date(),
              rejected_reason: query.rejected_reason,
            }
          : {
              status: invoice_status.open,
              updated_by,
              status_changed_at: new Date(),
              move_to_open_date: new Date(),
            },
    });

    if (query.actionType !== 'reject') {
      const containerIds = check
        .map((inv: any) => inv.container_id)
        .filter((id: number): id is number => id !== null);

      if (containerIds.length > 0) {
        await this.prisma.containers.updateMany({
          where: {
            id: { in: containerIds },
          },
          data: {
            prelim_pl_status: prelim_pl_status.reviewed,
          },
        });
      }
    }

    return data.count > 0
      ? {
          result: true,
          message: `${data.count} records have been updated!`,
        }
      : {
          result: false,
          message: 'No records have been updated.',
        };
  };

  changeOpenDueToPaid = async (
    query: changeInvoicesIds,
    updated_by: number,
  ) => {
    const check = await this.getAll({
      where: {
        id: { in: query.invoicesIds },
        status: { in: [invoice_status.open, invoice_status.past_due] },
        containers: { vehicles: { some: {} } },
      },
    });

    if (check.length > 0) {
      const data = await this.editMany({
        where: {
          id: { in: check.map((o) => o.id) },
          status: { in: [invoice_status.open, invoice_status.past_due] },
        },
        data: {
          status: invoice_status.paid,
          updated_by,
          status_changed_at: new Date(),
        },
      });
      if (data.count > 0)
        return {
          result: true,
          message: `${data.count} records have been updated!`,
        };
      else return { result: false, message: 'No records have been updated.' };
    } else
      throw new BadRequestException({
        statusCode: 400,
        message:
          'There is no invoices in open or past due state, Or no vehicles.',
        error: 'Bad Request',
      });
  };

  // changeInvoicesStatus = async (
  //   query: changeInvoicesStatus,
  //   updated_by: number,
  // ) => {
  //   const { invoicesIds, invoiceStatus } = query;

  //   const check = await this.getAll({
  //     where: {
  //       id: { in: invoicesIds },
  //       status: { notIn: [invoice_status.pending] },
  //     },
  //   });
  //   if (check.length > 0) {
  //     const ids = check
  //       .map((o) => {
  //         if (
  //           (o.status == invoice_status.open &&
  //             invoiceStatus != invoice_status.open) ||
  //           (o.status == invoice_status.past_due &&
  //             invoiceStatus != invoice_status.past_due) ||
  //           (o.status == invoice_status.irrecoverable_debt &&
  //             invoiceStatus != invoice_status.irrecoverable_debt) ||
  //           o.status == invoice_status.paid
  //         )
  //           return o.id;
  //       })
  //       .filter(Boolean);

  //     const data = await this.editMany({
  //       where: {
  //         id: { in: ids },
  //         status: { not: invoice_status.pending },
  //       },
  //       data: {
  //         status: invoiceStatus == 'initial_review' ? 'pending' : invoiceStatus,
  //         updated_by,
  //         status_changed_at: new Date(),
  //       },
  //     });

  //     if (data.count > 0)
  //       return {
  //         result: true,
  //         message: `${data.count} records have been updated!`,
  //       };
  //     else return { result: false, message: 'No records have been updated.' };
  //   } else
  //     return {
  //       result: false,
  //       message: 'There is no invoice in specific status.',
  //     };
  // };

  changeInvoicesStatus = async (
    query: changeInvoicesStatus,
    updated_by: number,
  ) => {
    const { invoicesIds, invoiceStatus } = query;

    const check = await this.getAll({
      where: {
        id: { in: invoicesIds },
        status: { notIn: [invoice_status.pending] },
      },
    });

    if (check.length === 0) {
      return {
        result: false,
        message: 'There is no invoice in specific status.',
      };
    }

    const ids = check
      .map((o) => {
        if (
          (o.status == invoice_status.open &&
            invoiceStatus != invoice_status.open) ||
          (o.status == invoice_status.past_due &&
            invoiceStatus != invoice_status.past_due) ||
          (o.status == invoice_status.irrecoverable_debt &&
            invoiceStatus != invoice_status.irrecoverable_debt) ||
          o.status == invoice_status.paid
        ) {
          return o.id;
        }
      })
      .filter(Boolean);

    const data = await this.editMany({
      where: {
        id: { in: ids },
        status: { not: invoice_status.pending },
      },
      data: {
        status:
          invoiceStatus === 'initial_review'
            ? invoice_status.pending
            : invoiceStatus,
        updated_by,
        status_changed_at: new Date(),
      },
    });

    // If invoiceStatus is 'initial_review', update container.pl_status to pending
    if (invoiceStatus === 'initial_review') {
      const containerIds = check
        .filter((o) => ids.includes(o.id) && o.container_id != null)
        .map((o) => o.container_id as number);

      if (containerIds.length > 0) {
        await this.prisma.containers.updateMany({
          where: {
            id: { in: containerIds },
          },
          data: {
            prelim_pl_status: prelim_pl_status.pending,
            pl_status: pl_status.pending,
          },
        });
      }
    }

    return data.count > 0
      ? {
          result: true,
          message: `${data.count} records have been updated!`,
        }
      : {
          result: false,
          message: 'No records have been updated.',
        };
  };

  getTotal = (where: any) =>
    this.prisma.invoices
      .count({ where })
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => this.prisma.$disconnect());

  getAll = (params: any) =>
    this.prisma.invoices
      .findMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  editMany = (params: any) =>
    this.prisma.invoices
      .updateMany(params)
      .then((result: any) => result)
      .catch((e: any) => catch_response(e))
      .finally(async () => await this.prisma.$disconnect());

  // @Cron('*/15 * * * * *')
  @Cron('30 20 * * *')
  async handleOverDurInvoices() {
    const lockKey = `change_invoices_status_to_past_due_lock`;
    const lockTtl = 10 * 60;
    const hasLock = await this.acquireLock(lockKey, lockTtl);
    if (hasLock) {
      try {
        const q = `
            select
              inv.id
            from invoices inv
            left join companies cm on cm.id = inv.company_id
            left join containers cn on cn.id = inv.container_id
            left join bookings bk on bk.id = cn.booking_id
            where
              inv.status = 'open'
            and
              (EXTRACT(EPOCH FROM bk.eta::timestamp)::bigint + (COALESCE(cm.days_limit, 0) * 86400)::bigint)::bigint < EXTRACT(EPOCH FROM NOW())::bigint
            and
              bk.eta_status = 'actual'`;
        const pastDueInvoices = await this.db.runRaw(q);

        const result = await this.prisma.invoices.updateMany({
          where: { id: { in: pastDueInvoices.map((inv) => inv.id) } },
          data: { status: 'past_due', updated_by: 681 },
        });

        this.logger.log(
          `${result.count} invoices updated moved to past_due status.`,
        );
      } catch (error) {
        this.logger.error(error);
      }
    }
  }

  async acquireLock(key: string, ttlSeconds: number): Promise<boolean> {
    const result = await this.redis.set(key, 'locked', 'EX', ttlSeconds, 'NX');
    return result === 'OK';
  }

  releaseLock(key: string): void {
    this.redis.del(key);
  }
}
