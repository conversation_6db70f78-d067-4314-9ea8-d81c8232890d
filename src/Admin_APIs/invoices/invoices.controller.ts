import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  NotFoundException,
  ParseArrayPipe,
  Res,
} from '@nestjs/common';

import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminGuard } from 'src/Commons/guards/jwt.guard';
import { User } from 'src/Commons/decorators/user.decorator';
import { SearchPaginateDto } from 'src/Commons/dto/searchPaginateDto.dto';
import PermissionGuard from 'src/Commons/guards/permissions.guard';
import {
  CreateInvoiceDto,
  UpdateInvoiceDto,
  changeInvoicesIds,
  changeInvoicesStatus,
  filterInvoices,
} from 'src/Admin_APIs/invoices/dto/invoice.dto';
import { InvoicesService } from './invoices.service';
import { OpenInvoicesService } from './open_invoices.service';
import { Response } from 'express';
@Controller('invoices')
@ApiTags('Invoices')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class InvoicesController {
  constructor(private readonly invoicesService: InvoicesService) {}

  @Post()
  @UseGuards(PermissionGuard('create_invoices'))
  async create(@Body() dto: CreateInvoiceDto, @User() user: any) {
    return await this.invoicesService.create(dto, user.loginable_id);
  }

  @Get()
  @UseGuards(PermissionGuard('view_invoices'))
  async findAll(@Query() query: filterInvoices, @User() user: any) {
    return await this.invoicesService.findAll(
      query,
      user.userId,
      user.loginable_id,
    );
  }

  @Get('count')
  async count() {
    const count = await this.invoicesService.getTotal({
      deleted_at: null,
      deleted_by: null,
    });
    return { data: { count }, result: true };
  }

  @Get('trash')
  @UseGuards(PermissionGuard('trash_view_invoices'))
  async getTrash(@Query() query: SearchPaginateDto) {
    return await this.invoicesService.getTrash(query);
  }

  @Get('pending-trash')
  @UseGuards(PermissionGuard('pending_trash_view_invoices'))
  async getPendingTrash(@Query() query: SearchPaginateDto) {
    return await this.invoicesService.getPendingTrash(query);
  }

  @Get('mix')
  @UseGuards(PermissionGuard('view_invoices'))
  async getMixed(@Query() query: filterInvoices) {
    return await this.invoicesService.getMixed(query);
  }

  @Get(':id')
  @UseGuards(PermissionGuard('view_invoices'))
  async findOne(@Param('id') id: string) {
    return await this.invoicesService.findOne(+id);
  }

  @Get('multiPrint/:ids')
  @UseGuards(PermissionGuard('view_invoices'))
  async multiPrintData(@Param('ids', ParseArrayPipe) ids: number[]) {
    const data = await this.invoicesService.findMultiPrintData(ids);
    if (!data) {
      throw new NotFoundException('The locations does not exist.');
    }
    return { result: true, data };
  }

  @Get('print/:id')
  @UseGuards(PermissionGuard('view_invoices'))
  async printData(@Param('id') id: number) {
    return await this.invoicesService.findPrintData(id);
  }

  @Patch('changeOpenDueToPaid')
  @UseGuards(PermissionGuard('open_due_to_paid'))
  async changeOpenDueToPaid(
    @Body() query: changeInvoicesIds,
    @User() user: any,
  ) {
    return await this.invoicesService.changeOpenDueToPaid(
      query,
      user.loginable_id,
    );
  }

  @Patch('changePendingToOpen')
  @UseGuards(PermissionGuard('invoice_initial_review'))
  async changePendingToOpen(
    @Body() query: changeInvoicesIds,
    @User() user: any,
  ) {
    return await this.invoicesService.changePendingToOpen(
      query,
      user.loginable_id,
    );
  }

  @Patch('invoiceFinalReview')
  @UseGuards(PermissionGuard('invoice_final_review'))
  async invoiceFinalReview(
    @Body() query: changeInvoicesIds,
    @User() user: any,
  ) {
    return await this.invoicesService.invoiceFinalReview(
      query,
      user.loginable_id,
    );
  }

  @Patch('changeInvoicesStatus')
  @UseGuards(PermissionGuard('change_status_invoices'))
  async changeInvoicesStatus(
    @Body() query: changeInvoicesStatus,
    @User() user: any,
  ) {
    return await this.invoicesService.changeInvoicesStatus(
      query,
      user.loginable_id,
    );
  }

  @Patch(':id')
  @UseGuards(PermissionGuard('update_invoices'))
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateInvoiceDto,
    @User() user: any,
  ) {
    return await this.invoicesService.update(+id, dto, user.loginable_id);
  }

  @Delete(':ids')
  @UseGuards(PermissionGuard('delete_invoices'))
  async remove(
    @Param('ids', ParseArrayPipe) ids: number[],
    @Body() body: { deleted_reason?: string },
    @User() user: any,
  ) {
    return await this.invoicesService.softDeleteOne(
      ids,
      user.loginable_id,
      body.deleted_reason,
    );
  }

  @Put('trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_invoices'))
  async restore(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.invoicesService.restore(ids, user.loginable_id);
  }

  @Put('pending-trash/restore/:ids')
  @UseGuards(PermissionGuard('restore_pending_trash_invoices'))
  async restorePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.invoicesService.restorePendingTrash(
      ids,
      user.loginable_id,
    );
  }

  @Put('pending-trash/delete/:ids')
  @UseGuards(PermissionGuard('delete_pending_trash_invoices'))
  async deletePendingTrash(
    @Param('ids', ParseArrayPipe) ids: number[],
    @User() user: any,
  ) {
    return await this.invoicesService.deletePendingTrash(
      ids,
      user.loginable_id,
    );
  }

  /* @Delete('trash/forceDelete/:ids')
  @UseGuards(PermissionGuard('force_delete_invoices'))
  async forceDelete(@Param('ids', ParseArrayPipe) ids: number[]) {
    const data = await this.invoicesService.forceDeleteOne(ids);
    if (data.count) return { data, result: true };
    else return { result: false, message: 'Delete Failed.' };
  } */
}

// admin panel -> invoices -> statements
@Controller('open-invoices')
@ApiTags('Open Invoices')
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class OpenInvoicesController {
  constructor(private readonly openInvoiceService: OpenInvoicesService) {}
  @Get()
  @UseGuards(PermissionGuard('view_invoices'))
  async findAll(@Query() query: filterInvoices) {
    return await this.openInvoiceService.findAll(query);
  }
  @Get('print')
  @UseGuards(PermissionGuard('view_invoices'))
  async printInvoices(
    @Query() query: { invoices: { id: number; invoice_type: string } },
  ) {
    return await this.openInvoiceService.printInvoices(query);
  }

  @Post('multiPrint')
  @UseGuards(PermissionGuard('view_invoices'))
  async multiPrintInvoices(
    @Body()
    query: {
      invoices: { id: number; invoice_type: string; company_id: number }[];
    },
  ) {
    return await this.openInvoiceService.multiPrintInvoices(query.invoices);
  }

  @Post('download-statement-inventory-pdf')
  @UseGuards(PermissionGuard('view_invoices'))
  async DownloadStatementInventoryReport(
    @Body()
    query,
    @Res() res: Response,
  ) {
    try {
      const Pdf =
        await this.openInvoiceService.DownloadStatementInventoryReport(query);
      res.header('Content-Type', 'application/pdf');
      res.header('Content-Disposition', 'attachment; filename="inventory.pdf"');
      res.send(Pdf);
    } catch (error) {
      console.error('Error generating Pdf:', error);
      res.status(500).send('Internal Server Error');
    }
  }

  @Get('credit_limit_report')
  @UseGuards(PermissionGuard('view_invoices_reports'))
  async getCreditLimitReport(@Query() query: any) {
    return await this.openInvoiceService.creditLimitReport(query);
  }

  @Get('customer_due_summary')
  @UseGuards(PermissionGuard('view_invoices_reports'))
  async getCustomerDueReport(@Query() query: any) {
    return await this.openInvoiceService.customerDueSummeryReport(query);
  }
  @Get('due_type_summary')
  @UseGuards(PermissionGuard('view_invoices_reports'))
  async getDueTypeReport(@Query() query: any) {
    return await this.openInvoiceService.dueTypeSummeryReport(query);
  }
}
