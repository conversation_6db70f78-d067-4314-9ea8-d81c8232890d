'use server';
import axios from '@/lib/axios-server';
import { cookies } from 'next/headers';
import { notFound, redirect } from 'next/navigation';
import { NetworkError } from './network-error-enum';
import ServerErorr from '@/components/custom-pages/server-error';
import Unauthorized from '@/components/custom-pages/unauthorized';

export const getTokenAndKey = async () => {
  const allCookies = await cookies();
  const token = allCookies.get('token');
  const userKey = allCookies.get('userKey');
  return { token: token?.value, key: userKey?.value };
};

export const getProfile = async () => {
  try {
    const { token, key } = await getTokenAndKey();
    const response = await axios.get(`auth/profile`, {
      params: { key },
      headers: { Authorization: `Bearer ${token}` },
    });

    return { result: true, data: response.data.data };
  } catch (error) {
    if (error?.code == 'ECONNREFUSED') {
      return { result: false, code: NetworkError.CONNECTION_REFUSED };
    }
    if (error?.code == 'ERR_BAD_REQUEST') {
      const status = error?.response?.status;
      if (status == 401) {
        return { result: false, code: NetworkError.UNAUTHORIZED };
      }
      if (status == 403) {
        return { result: false, code: NetworkError.FORBIDDEN };
      }
      if (status == 404) {
        return { result: false, code: NetworkError.NOT_FOUND };
      }
    }
  }
};

export const hasPagePermission = async ({
  permission = '',
}: {
  permission?: string;
}) => {
  const { token, key } = await getTokenAndKey();

  try {
    if (!token) {
      return redirect('/auth/signin');
    }

    const { data } = await axios.get(`auth/has-permission`, {
      params: { permission, key },
      headers: { Authorization: `Bearer ${token}` },
    });

    const result = data?.result ?? false;
    return { result, code: !result ? NetworkError.UNAUTHORIZED : undefined };
  } catch (error) {
    if (error?.code == 'ECONNREFUSED') {
      return { result: false, code: NetworkError.CONNECTION_REFUSED };
    }
    if (error?.code == 'ERR_BAD_REQUEST') {
      const status = error?.response?.status;
      if (status == 401) {
        return { result: false, code: NetworkError.UNAUTHORIZED };
      }
    }
  }
};

export const networkErrorHandler = async ({
  result,
  code,
}: {
  result: boolean;
  code: NetworkError;
}) => {
  if (!result) {
    switch (code) {
      case NetworkError.CONNECTION_REFUSED:
        return <ServerErorr />;
      case NetworkError.UNAUTHORIZED:
        return <Unauthorized />;
      case NetworkError.FORBIDDEN:
        return redirect('/auth/signin');
      case NetworkError.NOT_FOUND:
        return notFound();
    }
    return redirect('/auth/signin');
  }
};
