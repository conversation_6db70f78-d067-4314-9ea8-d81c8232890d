import axios from '@/lib/axios';

export const getCompanies = async (
  search,
  category,
  isFilter: boolean = false,
  ids: string[] = [],
) => {
  try {
    const { data } = await axios.get(`charges/company-autocomplete`, {
      params: { search, category, isFilter, ids: ids },
    });
    return data.data.map((item) => ({
      value: `${item.id}`,
      label: item.name,
    }));
  } catch (error) {
    return null;
  }
};

export const getDestination = async (search, ids: string[] = []) => {
  try {
    const { data } = await axios.get(
      `/autoComplete?column=name&modal=destinations&ids=${ids}`,
      {
        params: {
          name: search,
        },
      },
    );
    return data.data.map((item) => ({
      value: `${item.id}`,
      label: item.name,
    }));
  } catch (error) {
    return null;
  }
};

export const companiesAutoComplete = async (search, ids: string[] = []) => {
  try {
    const { data } = await axios.get(
      `/autoComplete?column=name&modal=companies&ids=${ids}`,
      {
        params: {
          name: search,
        },
      },
    );
    return data.data.map((item) => ({
      value: `${item.id}`,
      label: item.name,
    }));
  } catch (error) {
    return null;
  }
};
