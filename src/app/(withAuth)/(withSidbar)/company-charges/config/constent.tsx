import { format } from 'date-fns';

export const chargesCategory = [
  'finance',
  'clearance',
  'title',
  'fedex',
  'auction',
  'load',
  'used_cars',
  'dispatch',
];

export type PageProps = {
  params: Promise<any>;
  searchParams: any;
};
export type fetchType = {
  page: number;
  per_page: number;
  search?: string;
  filterData?: string;
  status: string;
};

type returnType = {
  id: number;
  chargeName: string;
  amount: number;
  remark: string;
};
export function transformCharges(
  chargeObj: Record<string, { amount: number; remark: string }>,
): returnType[] {
  return Object.entries(chargeObj).map(([key, value]) => {
    const [idStr, nameWithSuffix] = key.split('@');
    const id = parseInt(idStr, 10);
    const chargeName = nameWithSuffix ? nameWithSuffix.split('.')[0] : key;
    return {
      id,
      chargeName,
      amount: value.amount,
      remark: value.remark,
    };
  });
}

export const formatDate = (dateString) => {
  return dateString ? format(new Date(dateString), 'yyyy-MM-dd') : '';
};

export const processPivotEntry = (pivot, chargeMap, companyData) => {
  const chargeName = chargeMap.get(pivot.charge_id);
  if (!chargeName) return;

  companyData[`${chargeName}.id`] = pivot.id;
  companyData[`${chargeName}.current_amount`] = pivot.current_amount;
  companyData[`${chargeName}.next_amount`] = pivot.next_amount;
  companyData[`${chargeName}.remark`] = pivot.remark;
};
