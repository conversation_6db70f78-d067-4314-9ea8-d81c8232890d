'use client';

import {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useState,
} from 'react';
type Charge = {
  amount?: number;
  remark?: string;
  company_id: number;
  key?: string;
  company?: string;
};

type ChargesContextProps = {
  selectedItems: string[];
  setSelectItems: Dispatch<SetStateAction<string[]>>;
  isDelete: boolean;
  setIsDelete: Dispatch<SetStateAction<boolean>>;
  chargeObj: Record<string, Charge>;
  setChargeObj: Dispatch<SetStateAction<any>>;
  addCharge: ({}: Charge) => void;
};
export const ChargesContext = createContext<ChargesContextProps>({
  selectedItems: [],
  setSelectItems: () => {},
  isDelete: false,
  setIsDelete: () => {},
  setChargeObj: () => {},
  chargeObj: {},
  addCharge: () => {},
});
const ChargesProvider = ({ children }: { children: React.ReactNode }) => {
  const [selectedItems, setSelectItems] = useState<string[]>([]);
  const [isDelete, setIsDelete] = useState<boolean>(false);
  const [chargeObj, setChargeObj] = useState<any>({});

  const addCharge = ({ company_id, amount, remark, key, company }: Charge) => {
    const newCharge: Charge = { company_id };
    if (amount !== undefined) newCharge.amount = amount;
    if (remark !== undefined) newCharge.remark = remark;
    setChargeObj((prev) => ({
      ...prev,
      [key]: { amount, remark, company_id, company },
    }));
  };

  return (
    <ChargesContext.Provider
      value={{
        selectedItems,
        setSelectItems,
        isDelete,
        setIsDelete,
        chargeObj,
        setChargeObj,
        addCharge,
      }}
    >
      {children}
    </ChargesContext.Provider>
  );
};

const useCharges = () => {
  const context = useContext(ChargesContext);
  if (!context) {
    throw new Error('useNotification must be used within a Charges Provider');
  }

  return context;
};
export { useCharges };

export default ChargesProvider;
