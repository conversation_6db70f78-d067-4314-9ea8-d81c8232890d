import { Switch } from '@/components/ui/switch';
import { IToolPanelParams } from 'ag-grid-community';
import { useEffect, useState } from 'react';

function HideAndShowRemark({ api }: IToolPanelParams) {
  const [hideRemarks, setHideRemarks] = useState<boolean>(() => {
    const saved = localStorage.getItem('hideRemarks');
    return saved === 'true';
  });

  const applyRemarkVisibility = (hide: boolean) => {
    if (!api) return;
    const remarkColIds: string[] = [];

    api.getColumns()?.forEach((col) => {
      const colId = col.getColId();
      if (colId.includes('.remark')) {
        remarkColIds.push(colId);
      }
    });

    api.setColumnsVisible(remarkColIds, !hide);
  };

  useEffect(() => {
    if (api) {
      applyRemarkVisibility(hideRemarks);
    }
  }, [api, hideRemarks]);

  const toggleRemarkColumns = () => {
    const newState = !hideRemarks;
    setHideRemarks(newState);
    localStorage.setItem('hideRemarks', String(newState));
  };

  return (
    <div className="p-3">
      <div className="grid grid-cols-2">
        <Switch
          onClick={toggleRemarkColumns}
          className="block"
          checked={hideRemarks}
        />
        <p>{!hideRemarks ? 'Hide' : 'Show'} Remark</p>
      </div>
    </div>
  );
}

export default HideAndShowRemark;
