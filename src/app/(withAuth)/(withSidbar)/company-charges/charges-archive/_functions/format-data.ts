import { CellValueChangedEvent, ColDef } from 'ag-grid-community';

export const formatData = (event: CellValueChangedEvent, sanitized) => {
  const colf: ColDef = event.colDef;
  const chargeKey = colf.field?.split('.')[0];

  // Extract id as number
  const charge_id = parseInt(chargeKey.split('@')[0], 10);
  const fieldData = event.data;
  const id = fieldData[`${sanitized}.id`];
  const data = {
    charge_id,
    company_charge_id: fieldData?.company_charge_id,
    next_amount: Number(fieldData[`${sanitized}.next_amount`]),
    remark: fieldData[`${sanitized}.remark`],
  };

  return { id, data };
};

interface ChargeData {
  id: number;
  charge_id: number;
  next_amount?: number;
  remark?: string;
}

interface FormattedCompanyCharges {
  company: string;
  company_charge_id: number;
  company_id: number;
  data: ChargeData[];
}

export function formatCompanyCharges(input: any[]): FormattedCompanyCharges[] {
  return input.map((companyObj) => {
    const { company, company_charge_id, company_id, ...rest } = companyObj;
    const dataMap = new Map<number, ChargeData>();

    Object.entries(rest).forEach(([key, value]) => {
      const baseMatch = key.match(/^(\d+)@.*\.id$/);
      if (baseMatch) {
        const charge_id = parseInt(baseMatch[1]);
        const id = value as number;
        if (!dataMap.has(charge_id)) {
          dataMap.set(charge_id, { id, charge_id });
        }
      }

      const dynamicMatch = key.match(/^(\d+)@([^.]+)\.(next_amount|remark)$/);
      if (dynamicMatch) {
        const charge_id = parseInt(dynamicMatch[1]);
        const field = dynamicMatch[3] as 'next_amount' | 'remark';

        if (!dataMap.has(charge_id)) {
          dataMap.set(charge_id, { id: 0, charge_id });
        }

        const existing = dataMap.get(charge_id)!;
        (existing as any)[field] = value;
        dataMap.set(charge_id, existing);
      }
    });

    return {
      company,
      company_id,
      company_charge_id,
      data: Array.from(dataMap.values()),
    };
  });
}
