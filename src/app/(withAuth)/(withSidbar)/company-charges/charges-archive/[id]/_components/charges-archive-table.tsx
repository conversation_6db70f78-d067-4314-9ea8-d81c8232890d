'use client';
import React, { useMemo, useRef } from 'react';
import {
  ColDef,
  ColumnApiModule,
  ModuleRegistry,
  TextEditorModule,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import AgGridDataTable from '@/components/app/ag-grid-data-table';
import { sidebar } from '../../../config/sidebar-config';
import { useColumnsAndRowData } from './columns-rows-data';
import { autoSizeStrategy } from '@/app/(withAuth)/(withSidbar)/rate-analysis/(shipping-rate)/_configs/ra-sh-table-configs';
ModuleRegistry.registerModules([TextEditorModule, ColumnApiModule]);
const CompanyChargesRateTable = ({ data }) => {
  const gridRef = useRef<AgGridReact | null>(null);
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
      maxWidth: 300,
      sortable: true,
    }),
    [],
  );
  const selectionColumnDef: ColDef = useMemo(() => {
    return {
      resizable: true,
      pinned: 'left',
      filter: false,
      flex: 1,
      sortable: true,
    };
  }, []);
  const { columnDefs, rowData } = useColumnsAndRowData(data);

  return (
    <>
      <AgGridDataTable
        ref={gridRef}
        columnDefs={columnDefs}
        rowData={rowData}
        sideBar={sidebar}
        defaultColDef={defaultColDef}
        undoRedoCellEditing={true}
        undoRedoCellEditingLimit={100}
        autoSizeStrategy={autoSizeStrategy}
        rowDragManaged={true}
        suppressFieldDotNotation={true}
        selectionColumnDef={selectionColumnDef}
      />
    </>
  );
};

export default CompanyChargesRateTable;
