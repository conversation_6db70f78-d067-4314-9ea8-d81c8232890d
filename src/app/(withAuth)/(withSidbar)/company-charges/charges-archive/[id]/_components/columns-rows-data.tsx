import { useMemo } from 'react';
import CenterHeader from './center-header';
import { toast } from 'sonner';
import { CellValueChangedEvent, ColDef, ColGroupDef } from 'ag-grid-community';
import axios from '@/lib/axios';
import { useRouter } from 'next/navigation';
import { formatData } from '../../_functions/format-data';
import { useProfileContext } from '@/components/app/provoders/profile-context';
import CCA_StatusRenderer from './status-renderer';
import { formatDate, processPivotEntry } from '../../../config/constent';
const sanitize = (str: string) => str.replace(/ /g, '_');
export const useColumnsAndRowData = (data) => {
  const router = useRouter();
  const { hasEveryPermission } = useProfileContext();

  const chargeMap = useMemo(() => {
    const map = new Map<number, string>();
    data?.charges?.forEach((c) => map.set(c.id, `${c.id}@${sanitize(c.name)}`));
    return map;
  }, []);

  // create-charge-rate
  const addChargeRate = async (data) => {
    try {
      const response = await axios.post(`charges/create-charge-rate`, {
        ...data,
      });
      toast.success('Charge updated successfully');
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
    }
  };

  // update-charge-rate
  const updateChargeRate = async (data, id) => {
    try {
      const response = await axios.patch(`charges/update-charge-rate/${id}`, {
        ...data,
      });
      toast.success('Charge updated successfully');
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
    }
  };

  const initializeGroup = (companyName, effectiveDate, status) => ({
    company: companyName,
    effectiveDate,
    status,
    charges: [],
    company_ids: new Set(),
    company_charge_ids: new Set(),
  });

  const processEntry = (entry, chargeMap, grouped) => {
    const companyName = entry.companies?.name;
    const effectiveDate = formatDate(entry.effective_from);
    const status = entry.status;
    const groupKey = `${status}_${entry.id}`;

    if (!grouped[groupKey]) {
      grouped[groupKey] = initializeGroup(companyName, effectiveDate, status);
    }

    grouped[groupKey].company_ids.add(entry.companies?.id);
    grouped[groupKey].company_charge_ids.add(entry.id);

    // Note: Using company_charges_pivot as per your original code
    entry.company_charges_pivot?.forEach((pivot) => {
      processPivotEntry(pivot, chargeMap, grouped[groupKey]);
    });
  };

  const convertToRows = (grouped) => {
    return Object.values(grouped).map((group: any) => ({
      ...group,
      company_ids: Array.from(group.company_ids),
      company_charge_ids: Array.from(group.company_charge_ids),
    }));
  };
  const computeRowData = (data, chargeMap) => {
    const grouped = {};
    data?.forEach((entry) => processEntry(entry, chargeMap, grouped));
    return convertToRows(grouped);
  };
  const rowData = useMemo(() => {
    return computeRowData(data?.data, chargeMap);
  }, [data?.data, chargeMap]);

  const columnDefs = useMemo<(ColGroupDef | ColDef)[]>(() => {
    const chargeNameGroup: ColGroupDef = {
      headerName: 'Charge Name',
      headerGroupComponent: CenterHeader,
      marryChildren: true,
      children: [
        {
          headerName: 'Company',
          field: 'company',
          pinned: 'left',
          headerComponent: CenterHeader,
        },
        {
          headerName: 'Eff Date',
          field: 'effectiveDate',
          pinned: 'left',
          headerComponent: CenterHeader,
        },
        {
          headerName: 'Status',
          field: 'status',
          pinned: 'left',
          headerComponent: CenterHeader,
          cellRenderer: CCA_StatusRenderer,
        },
      ],
    };

    const chargeCols: ColGroupDef[] =
      data?.charges?.map((charge): ColGroupDef => {
        const sanitized = sanitize(`${charge.id}@${sanitize(charge.name)}`);
        return {
          headerName: charge.name.replace(/_/g, ' '),
          headerGroupComponent: CenterHeader,
          marryChildren: true,
          children: [
            {
              field: `${sanitized}.current_amount`,
              headerName: 'C Amount',
              headerComponent: CenterHeader,
              minWidth: 80,
            },

            {
              field: `${sanitized}.remark`,
              headerName: 'Remark',
              headerComponent: CenterHeader,
              onCellValueChanged: async (event: CellValueChangedEvent) => {
                const { data, id } = formatData(event, sanitized);
                if (id) {
                  await updateChargeRate(data, id);
                } else {
                  await addChargeRate(data);
                }
              },
              minWidth: 80,
            },
          ],
        };
      }) || [];

    return [chargeNameGroup, ...chargeCols];
  }, [hasEveryPermission]);

  return { columnDefs, rowData };
};
