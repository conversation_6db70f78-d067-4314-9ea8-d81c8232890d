import { cn } from '@/lib/utils';
import { memo } from 'react';

const CCA_StatusRenderer = memo(() => {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div
        className={cn(
          'h-auto w-auto rounded-md px-2 py-0 text-[10px] font-semibold uppercase leading-6 tracking-wide',
          'bg-destructive',
        )}
      >
        Archive
      </div>
    </div>
  );
});

export default CCA_StatusRenderer;
