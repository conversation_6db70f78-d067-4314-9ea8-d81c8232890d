import React from 'react';
import ChargesProvider from '../../config/charges-context';
import TopHeader from '../../config/AppHeader';
import { getTokenAndKey } from '@/app/functions/auth';
import axios from '@/lib/axios-server';
import { fetchType, PageProps } from '../../config/constent';
import { notFound } from 'next/navigation';
import CompanyChargesArchiveTable from './_components/charges-archive-table';
import { CHARGES_RATE_PERMISSION } from '@/configs/leftSideMenu/Permissions';
import PaginationComponent from '../../../rate-analysis/(shipping-rate)/_components/pagination-component';
const fetchData = async ({ page, per_page, search, status }: fetchType) => {
  try {
    const { token } = await getTokenAndKey();
    const response = await axios.get('/charges/charge-archive', {
      headers: { Authorization: `Bearer ${token}` },
      params: { page, per_page, search, status },
    });
    return response.data;
  } catch (error) {
    return null;
  }
};
export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const page = searchParams.page || 1;
  const per_page = searchParams.per_page || 20;
  const search = searchParams.search || undefined;
  const urlParams = await props.params;
  const status = urlParams.id;

  const data: any = await fetchData({ page, per_page, search, status });

  if (data === null) return notFound();

  return (
    <ChargesProvider>
      <TopHeader
        Component={<></>}
        route={{
          href: '/company-charges/charges-archive',
          label: 'Company Charges Archive',
        }}
        PERMISSION={CHARGES_RATE_PERMISSION}
      />
      <div className="p-4">
        <div style={{ height: 'calc( 100vh - 150px )' }}>
          <CompanyChargesArchiveTable data={data} />
        </div>
        <PaginationComponent count={data.total || 0} pageSize={data.per_page} />
      </div>
    </ChargesProvider>
  );
}
