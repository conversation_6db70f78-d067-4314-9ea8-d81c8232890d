import { useMemo } from 'react';
import CenterHeader from './center-header';
import { toast } from 'sonner';
import { CellValueChangedEvent, ColDef, ColGroupDef } from 'ag-grid-community';
import Actions from './actions';
import axios from '@/lib/axios';
import { useRouter } from 'next/navigation';
import { formatData } from '../../_functions/format-data';
import { useProfileContext } from '@/components/app/provoders/profile-context';
import { CHARGES_RATE_PERMISSION } from '@/configs/leftSideMenu/Permissions';
import { formatDate } from '@/configs/vehicles/configs';
import { processPivotEntry } from '../../../config/constent';
const sanitize = (str: string) => str.replace(/ /g, '_');
export const useColumnsAndRowData = (data, approveChargeRates) => {
  const router = useRouter();
  const { hasEveryPermission } = useProfileContext();

  const chargeMap = useMemo(() => {
    const map = new Map<number, string>();
    data?.charges?.forEach((c) => map.set(c.id, `${c.id}@${sanitize(c.name)}`));
    return map;
  }, []);

  const initializeCompanyData = (
    companyName,
    effectiveDate,
    companyId,
    chargeId,
  ) => ({
    company: companyName,
    effectiveDate,
    company_charge_id: chargeId,
    company_id: companyId,
  });

  const processEntry = (entry, chargeMap, companyData) => {
    const companyName = entry.companies?.name;
    const effectiveDate = formatDate(entry.effective_from);
    const key = `${companyName}_${entry.id}`;
    if (!companyData[key]) {
      companyData[key] = initializeCompanyData(
        companyName,
        effectiveDate,
        entry.companies?.id,
        entry.id,
      );
    }

    entry.company_charges_pivot?.forEach((pivot) => {
      processPivotEntry(pivot, chargeMap, companyData[key]);
    });
  };

  const computeRowData = (data, chargeMap) => {
    const companyData = {};
    data?.forEach((entry) => processEntry(entry, chargeMap, companyData));
    return Object.values(companyData);
  };

  const rowData = useMemo(() => {
    return computeRowData(data?.data, chargeMap);
  }, [data?.data, chargeMap]);
  const columnDefs = useMemo<(ColGroupDef | ColDef)[]>(() => {
    const chargeNameGroup: ColGroupDef = {
      headerName: 'Charge Name',
      headerGroupComponent: CenterHeader,
      marryChildren: true,
      children: [
        {
          headerName: 'Company',
          field: 'company',
          editable: false,
          pinned: 'left',
          headerComponent: CenterHeader,
          sortable: true,
        },
        {
          headerName: 'Eff Date',
          field: 'effectiveDate',
          editable: false,
          headerComponent: CenterHeader,
        },
        {
          headerName: 'Actions',
          cellRenderer: Actions,
          sortable: false,
          editable: false,
          headerComponent: CenterHeader,
          cellRendererParams: { approveChargeRates },
          hide: hasEveryPermission(CHARGES_RATE_PERMISSION.APPROVE)
            ? false
            : true,
        },
      ],
    };

    const chargeCols: ColGroupDef[] =
      data?.charges?.map((charge): ColGroupDef => {
        const sanitized = sanitize(`${charge.id}@${sanitize(charge.name)}`);
        return {
          headerName: charge.name.replace(/_/g, ' '),
          headerGroupComponent: CenterHeader,
          marryChildren: true,
          children: [
            {
              field: `${sanitized}.current_amount`,
              headerName: 'C',
              editable: false,
              headerComponent: CenterHeader,
              sortable: true,
            },
            {
              field: `${sanitized}.next_amount`,
              headerName: 'N',
              valueSetter: (params) => {
                const newValue = params.newValue;
                if (isNaN(newValue) || newValue <= 0) {
                  toast.warning('Amount must be a positive number', {
                    style: {
                      color: '#f54e42',
                    },
                  });
                  return false;
                }
                params.data[`${sanitized}.next_amount`] = newValue;
                return true;
              },
              headerComponent: CenterHeader,
              editable: hasEveryPermission(CHARGES_RATE_PERMISSION.UPDATE),
              onCellValueChanged: async (event: CellValueChangedEvent) => {
                const { data, id } = formatData(event, sanitized);
                if (id) {
                  await updateChargeRate(data, id);
                } else {
                  await addChargeRate(data);
                }
              },
              sortable: true,
            },
            {
              field: `${sanitized}.remark`,
              headerName: 'Remark',
              headerComponent: CenterHeader,
              editable: hasEveryPermission(CHARGES_RATE_PERMISSION.UPDATE),
              onCellValueChanged: async (event: CellValueChangedEvent) => {
                const { data, id } = formatData(event, sanitized);
                if (id) {
                  await updateChargeRate(data, id);
                } else {
                  await addChargeRate(data);
                }
              },
              sortable: true,
            },
          ],
        };
      }) || [];

    // update-charge-rate
    const addChargeRate = async (data) => {
      try {
        const response = await axios.post(`charges/create-charge-rate`, {
          ...data,
        });
        toast.success('Charge updated successfully');
        return response.data;
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        router.refresh();
      }
    };

    const updateChargeRate = async (data, id) => {
      try {
        const response = await axios.patch(`charges/update-charge-rate/${id}`, {
          ...data,
        });
        toast.success('Charge updated successfully');
        return response.data;
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        router.refresh();
      }
    };
    return [chargeNameGroup, ...chargeCols];
  }, [hasEveryPermission]);

  return { columnDefs, rowData };
};
