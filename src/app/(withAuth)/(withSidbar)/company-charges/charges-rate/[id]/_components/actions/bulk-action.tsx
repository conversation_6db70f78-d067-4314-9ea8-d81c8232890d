import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CheckCheck } from 'lucide-react';
import React, { forwardRef, useEffect, useState } from 'react';
import CCH_ActionsApprove from './c-chr-approve-action';

const ChargesRateBulkActions = ({
  selectedItems,
  show,
  approveChargeRates,
}) => {
  const [showDisplay, setShowDisplay] = useState(false);
  const [showInternal, setShowInternal] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setShowInternal(show);
    }, 1);
    if (show) {
      setShowDisplay(true);
    } else {
      setTimeout(() => {
        setShowDisplay(false);
      }, 300);
    }
  }, [show]);

  return true ? (
    <div
      className={cn(
        'fixed bottom-12 left-1/2 z-50 w-[500px] max-w-[calc(100%-72px)] -translate-x-1/2 rounded-lg transition-all duration-300',
        showDisplay ? 'block translate-y-[100px]' : 'hidden',
        showInternal ? 'translate-y-0' : 'translate-y-[100px]',
      )}
    >
      <div
        className={cn(
          'shadow-custom flex items-center justify-between rounded-lg border border-primary/10 bg-sidebar p-3 text-sm text-foreground dark:border-white/10',
        )}
      >
        <div className="ms-2 flex items-center">
          {selectedItems.length} items selected
        </div>
        <div className="flex items-center gap-2">
          <CCH_ActionsApprove
            approveRates={approveChargeRates}
            items={selectedItems}
            TriggerButton={forwardRef<HTMLButtonElement>((props, ref) => (
              <Button
                size="sm"
                variant="default"
                className="h-7"
                ref={ref}
                {...props}
              >
                <CheckCheck /> <span className="hidden md:inline">Approve</span>
              </Button>
            ))}
          />
        </div>
      </div>
    </div>
  ) : (
    <></>
  );
};

export default ChargesRateBulkActions;
