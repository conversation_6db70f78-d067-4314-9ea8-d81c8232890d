import { Button, ButtonProps } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { AlertCircle, CalendarIcon, CheckCheck, Loader2 } from 'lucide-react';
import React, { forwardRef, useEffect, useMemo, useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import moment from 'moment';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';
import { formatCompanyCharges } from '../../../_functions/format-data';

const CCHR_ActionsApprove = ({
  items,
  approveRates,
  TriggerButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => (
    <Button
      size="sm"
      ref={ref}
      {...props}
      className={cn('h-6 w-6 [&_svg]:size-3.5', props.className)}
    >
      <CheckCheck />
    </Button>
  )),
}) => {
  const data = formatCompanyCharges(items);
  const isSingle = data.length === 1;
  const firstItem = data?.[0];

  const approvePhraseInitial = isSingle
    ? firstItem?.company
    : `Approve ${data.length} Items`;

  const [dialogOpen, setDialogOpen] = useState(false);
  const [dateOpen, setDateOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const formSchema = z.object({
    approvePhrase: z
      .string()
      .refine((val) => val.trim() === approvePhraseInitial, {
        message: `Value must match the${isSingle ? ` company name` : ''}: ${approvePhraseInitial}`,
      }),
    effectiveDate: z.date({
      required_error: 'Effective date is required',
      invalid_type_error: 'Effective date is required',
    }),
  });

  const form = useForm({
    resolver: zodResolver(formSchema),
    mode: 'onSubmit',
    defaultValues: {
      approvePhrase: '',
      effectiveDate: null,
    },
  });

  const approvePhrase = form.watch('approvePhrase');
  const effectiveDate = form.watch('effectiveDate');

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    await approveRates(
      data.map((item) => ({
        company_charge_id: item.company_charge_id,
        company_id: item.company_id,
        effective_date: moment(values.effectiveDate).format('YYYY-MM-DD'),
        items: item.data.map((el) => ({
          id: el.id,
          next_amount: el.next_amount,
          charge_id: el.charge_id,
          remark: el.remark,
        })),
      })),
    );
    setDialogOpen(false);
    setLoading(false);
  };

  useEffect(() => {
    if (effectiveDate) {
      setDateOpen(false);
    }
  }, [effectiveDate]);
  const hasNextRate = useMemo(() => {
    return data.every((company) =>
      company.data.some((item) => Number(item.next_amount) > 0),
    );
  }, [data]);
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipContent>
          <p>Approve</p>
        </TooltipContent>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex items-center"
            >
              <DialogTrigger asChild>
                <TooltipTrigger asChild>
                  {/* Dynamic button */}
                  <TriggerButton
                    className={cn(!hasNextRate ? 'border border-red-500' : '')}
                  />
                </TooltipTrigger>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[520px]">
                <DialogHeader>
                  <DialogTitle>Approve Charge rate</DialogTitle>
                  <DialogDescription>
                    {hasNextRate
                      ? `Please specify the effective date for the rate`
                      : ``}
                  </DialogDescription>
                </DialogHeader>
                {hasNextRate && (
                  <div className="grid gap-4 pt-3">
                    <div className="grid grid-cols-7 items-center gap-4">
                      <Label htmlFor="name" className="col-span-2 text-right">
                        Effective Date
                      </Label>
                      <FormField
                        control={form.control}
                        name="effectiveDate"
                        render={({ field }) => {
                          return (
                            <Popover
                              modal={true}
                              open={dateOpen}
                              onOpenChange={setDateOpen}
                            >
                              <PopoverTrigger asChild>
                                <div className="col-span-5 w-full">
                                  <Button
                                    variant={'outline'}
                                    className={cn(
                                      'mb-1 w-full justify-start text-left font-normal',
                                      !effectiveDate && 'text-muted-foreground',
                                    )}
                                  >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {effectiveDate ? (
                                      moment(effectiveDate).format('YYYY-MM-DD')
                                    ) : (
                                      <span>Pick a date</span>
                                    )}
                                  </Button>
                                  <FormMessage />
                                </div>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <FormItem>
                                  <FormControl>
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={(value) =>
                                        field.onChange({ target: { value } })
                                      }
                                      initialFocus
                                    />
                                  </FormControl>
                                </FormItem>
                              </PopoverContent>
                            </Popover>
                          );
                        }}
                      />
                    </div>
                  </div>
                )}
                <div className="my-2 flex flex-col gap-4">
                  {hasNextRate && (
                    <div className="flex items-center gap-4 rounded-lg bg-red-500/10 p-3 text-start text-sm text-red-500">
                      <div className="min-w-5">
                        <AlertCircle size={20} />
                      </div>{' '}
                      <span>
                        <strong>Warning:</strong> This action is not reversible.
                        Please be certain.
                      </span>
                    </div>
                  )}
                  {!hasNextRate && (
                    <div className="flex items-center gap-4 rounded-lg bg-red-500/10 p-3 text-start text-sm text-red-500">
                      <div className="min-w-5">
                        <AlertCircle size={20} />
                      </div>
                      <span>
                        <strong>Warning:</strong>{' '}
                        {isSingle
                          ? `This item lacks new Charge rates and
                        can not be approved.`
                          : `Some of the selected items lack new charge rates and can not be approved.`}
                      </span>
                    </div>
                  )}
                </div>
                <Separator />
                {hasNextRate && (
                  <div>
                    <p className="mb-4 text-sm font-light">
                      Enter{isSingle ? ` company name` : ''}{' '}
                      <strong className="font-bold">
                        {approvePhraseInitial}
                      </strong>{' '}
                      to continue:
                    </p>
                    <FormField
                      control={form.control}
                      name="approvePhrase"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline">Cancel</Button>
                  </DialogClose>
                  {hasNextRate && (
                    <Button
                      type="submit"
                      onClick={form.handleSubmit(onSubmit)}
                      disabled={
                        approvePhrase.trim() !== approvePhraseInitial &&
                        !hasNextRate
                      }
                    >
                      {loading ? (
                        <div className="flex w-14 items-center justify-center">
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        'Approve'
                      )}
                    </Button>
                  )}
                </DialogFooter>
              </DialogContent>
            </form>
          </Form>
        </Dialog>
      </Tooltip>
    </TooltipProvider>
  );
};

export default CCHR_ActionsApprove;
