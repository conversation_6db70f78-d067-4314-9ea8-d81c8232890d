'use client';
import React, { useMemo, useRef } from 'react';
import {
  AgGridEvent,
  ColDef,
  ColumnApiModule,
  ModuleRegistry,
  TextEditorModule,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import AgGridDataTable from '@/components/app/ag-grid-data-table';

import { useProfileContext } from '@/components/app/provoders/profile-context';
import { CHARGES_RATE_PERMISSION } from '@/configs/leftSideMenu/Permissions';
import {
  autoSizeStrategy,
  rowSelection,
} from '@/app/(withAuth)/(withSidbar)/rate-analysis/(shipping-rate)/_configs/ra-sh-table-configs';
import { useCharges } from '../../../config/charges-context';

import { useColumnsAndRowData } from './columns-rows-data';
import ChargesRateBulkActions from './actions/bulk-action';
import { toast } from 'sonner';
import axios from '@/lib/axios';
import { useParams, useRouter } from 'next/navigation';
import { sidebar } from './sidebar-config';
ModuleRegistry.registerModules([TextEditorModule, ColumnApiModule]);
type approveType = {
  effective_date: Date;
  company_charge_id: number;
  company_id: number;
  items: {
    id: number;
    next_amount: number;
    charge_id: number;
    remark: string;
  }[];
};
const CompanyChargesRateTable = ({ data }) => {
  const { setSelectItems, selectedItems } = useCharges();
  const params = useParams();
  const router = useRouter();
  const { hasEveryPermission } = useProfileContext();
  const gridRef = useRef<AgGridReact | null>(null);

  const approveChargeRates = async (updates: approveType) => {
    try {
      await axios.patch(`charges/charges-rate/approve`, {
        updates,
        category: params.id,
      });
      gridRef.current?.api.deselectAll();
      toast.success('Approval successful!', {
        description:
          'The selected Charge rates have been successfully approved.',
        action: {
          label: 'Close',
          onClick: () => {},
        },
      });
      router.refresh();
    } catch (_error) {
      toast.error('Error while approving charge rates!', {
        description: 'Please try again',
        action: {
          label: 'Close',
          onClick: () => {},
        },
      });
    }
  };
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
      minWidth: 100,
      sortable: true,
    }),
    [],
  );
  const selectionColumnDef: ColDef = useMemo(() => {
    return {
      resizable: true,
      pinned: 'left',
      filter: false,
      flex: 1,
    };
  }, []);
  const { columnDefs, rowData } = useColumnsAndRowData(
    data,
    approveChargeRates,
  );

  return (
    <>
      <ChargesRateBulkActions
        selectedItems={selectedItems}
        show={selectedItems.length}
        approveChargeRates={approveChargeRates}
      />
      <AgGridDataTable
        ref={gridRef}
        columnDefs={columnDefs}
        rowData={rowData}
        sideBar={sidebar}
        defaultColDef={defaultColDef}
        undoRedoCellEditing={true}
        undoRedoCellEditingLimit={100}
        autoSizeStrategy={autoSizeStrategy}
        rowSelection={
          hasEveryPermission(CHARGES_RATE_PERMISSION.APPROVE)
            ? rowSelection
            : undefined
        }
        rowDragManaged={true}
        suppressFieldDotNotation={true}
        selectionColumnDef={selectionColumnDef}
        onSelectionChanged={(e: AgGridEvent) => {
          setSelectItems(e.api.getSelectedRows());
        }}
      />
    </>
  );
};

export default CompanyChargesRateTable;
