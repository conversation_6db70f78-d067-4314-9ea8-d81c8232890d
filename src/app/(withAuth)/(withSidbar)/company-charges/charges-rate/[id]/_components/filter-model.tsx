import React, { useEffect, useCallback, useState } from 'react';
import HideAndShowRemark from '../../../config/hide-remark';
import { IToolPanelParams } from 'ag-grid-community';
import { MultiSelect } from '@/components/ui/multi-select';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  companiesAutoComplete,
  getDestination,
} from '../../../config/auto-complete';
import FilterCollapse from '@/components/app/filter-collapse';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';

export default function FilterModel(agridAPI: IToolPanelParams) {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [companyValue, setCompanyValue] = useState<string[]>([]);
  const [companyDefaultValues, setCompanyDefaultValues] = useState<
    { label: string; value: string }[]
  >([]);

  const [pointOfLoadingValue, setPointOfLoadingValue] = useState<string[]>([]);
  const [pointOfLoadingDefaultValues, setPointOfLoadingDefaultValues] =
    useState<{ label: string; value: string }[]>([]);

  const [openCollapse, toggleCollapse] = useState<string[]>([]);

  const [from, setFrom] = useState<Date | undefined>();
  const [to, setTo] = useState<Date | undefined>();

  const filterData = useCallback(() => {
    const data = searchParams.get('filterData');
    return data ? JSON.parse(data) : null;
  }, [searchParams]);

  const applyFiltersToURL = useCallback(() => {
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    const data: any = {};

    if (companyValue.length) data.com = companyValue;
    if (pointOfLoadingValue.length) data.po = pointOfLoadingValue;
    if (from) data.from = format(from, 'yyyy-MM-dd');
    if (to) data.to = format(to, 'yyyy-MM-dd');

    if (Object.keys(data).length) {
      params.set('filterData', JSON.stringify(data));
    } else {
      params.delete('filterData');
    }

    router.push(`?${params.toString()}`);
  }, [companyValue, pointOfLoadingValue, from, to, searchParams, router]);

  const clearFilters = () => {
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    params.delete('filterData');
    router.push(`?${params.toString()}`);
    setCompanyValue([]);
    setCompanyDefaultValues([]);
    setPointOfLoadingValue([]);
    setPointOfLoadingDefaultValues([]);
    setFrom(undefined);
    setTo(undefined);
  };

  useEffect(() => {
    const loadInitialFilters = async () => {
      const initial = filterData();
      if (!initial) return;

      if (initial.from) setFrom(new Date(initial.from));
      if (initial.to) setTo(new Date(initial.to));

      if (initial.po?.length) {
        const data = await getDestination('', initial.po);
        const selected =
          data?.filter((el) => initial.po.includes(el.value)) || [];
        setPointOfLoadingDefaultValues(selected);
        setPointOfLoadingValue(selected.map((item) => item.value));
      }

      if (initial.com?.length) {
        const data = await companiesAutoComplete('', initial.com);
        const selected =
          data?.filter((el) => initial.com.includes(el.value)) || [];
        setCompanyDefaultValues(selected);
        setCompanyValue(selected.map((item) => item.value));
      }
    };

    loadInitialFilters();
  }, [filterData]);

  return (
    <div className="min-h-[80vh]">
      <HideAndShowRemark {...agridAPI} />
      <div className="mr-1 flex w-[256px] flex-col gap-3 p-1">
        <FilterCollapse
          label="search"
          isOpen={openCollapse.includes('search')}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes('search')
                ? prev.filter((item) => item !== 'search')
                : [...prev, 'search'],
            )
          }
        >
          <div className="flex flex-col gap-1 pr-1">
            <MultiSelect
              selectedValues={companyValue}
              onSelectedValuesChange={setCompanyValue}
              getData={companiesAutoComplete}
              placeholder="search company"
              defaultValue={companyDefaultValues}
            />
            <MultiSelect
              selectedValues={pointOfLoadingValue}
              onSelectedValuesChange={setPointOfLoadingValue}
              getData={getDestination}
              placeholder="search point of loading"
              defaultValue={pointOfLoadingDefaultValues}
            />
          </div>
        </FilterCollapse>

        <FilterCollapse
          label="Date"
          isOpen={openCollapse.includes('dates')}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes('dates')
                ? prev.filter((item) => item !== 'dates')
                : [...prev, 'dates'],
            )
          }
        >
          <div className="flex flex-col gap-2">
            <div>
              <Label className="py-2 pl-2">From</Label>
              <DatePicker
                id="from"
                date={from}
                onChange={(date) => setFrom(date)}
              />
            </div>
            <div>
              <Label className="py-2 pl-2">To</Label>
              <DatePicker id="to" date={to} onChange={(date) => setTo(date)} />
            </div>
          </div>
        </FilterCollapse>

        <Button className="mx-2" onClick={applyFiltersToURL}>
          Apply
        </Button>
        <Button className="mx-2" variant="destructive" onClick={clearFilters}>
          Clear
        </Button>
      </div>
    </div>
  );
}
