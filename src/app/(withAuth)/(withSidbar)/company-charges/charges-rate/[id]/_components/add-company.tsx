'use client';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import axios from '@/lib/axios';
import { zodResolver } from '@hookform/resolvers/zod';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import React, { useState } from 'react';
import { toast } from 'sonner';
import { useParams, useRouter } from 'next/navigation';
import { Loader } from 'lucide-react';
import { ShadAutoComplete } from '@/components/ui/shad-auto-complete';
import { getCompanies } from '../../../config/auto-complete';

const formSchema = z.object({
  company_id: z.coerce.number(),
});
type formType = z.infer<typeof formSchema>;
export default function AddNewChargesRate() {
  const router = useRouter();
  const params = useParams();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm<formType>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  const onSubmit = async (data: formType) => {
    try {
      setLoading(true);
      const response = await axios.post(`charges/create-company-charge`, {
        ...data,
        category: params.id,
      });
      toast.success('Company add successfully');
      setOpen(false);
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
      setLoading(false);
      form.reset();
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipContent>
          <p>Adding a Company to the grid</p>
        </TooltipContent>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <TooltipTrigger asChild>
              <Button>Add Company</Button>
            </TooltipTrigger>
          </PopoverTrigger>
          <PopoverContent className="w-[350px] rounded-lg">
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="mb-4 space-y-2">
                <h4 className="text-sm font-medium leading-none">
                  Add new Company
                </h4>
                <p className="text-xs text-muted-foreground">
                  Add a new Company to the grid.
                </p>
              </div>
              <div className="grid gap-2">
                {/* Category Selection */}
                <div className="grid grid-cols-[50px_1fr] items-center gap-x-4">
                  <Label className="text-right text-xs">Company</Label>
                  <div>
                    <Controller
                      name="company_id"
                      control={form.control}
                      render={({ field }) => (
                        <ShadAutoComplete
                          selectedValue={field.value}
                          onSelectedValueChange={field.onChange}
                          emptyMessage="No items found."
                          placeholder="Search items..."
                          className="col-span-3 h-8"
                          getData={(search) => getCompanies(search, params.id)}
                        />
                      )}
                    />
                  </div>
                </div>
                {form.formState.errors.company_id && (
                  <p className="pl-16 text-xs text-red-500">
                    {form.formState.errors.company_id.message}
                  </p>
                )}

                <div className="flex justify-end">
                  <Button type="submit">
                    {loading ? <Loader className="animate-ping" /> : 'Submit'}
                  </Button>
                </div>
              </div>
            </form>
          </PopoverContent>
        </Popover>
      </Tooltip>
    </TooltipProvider>
  );
}
