import React from 'react';

import type { CustomCellRendererProps } from 'ag-grid-react';

const CategoryRenderer = (props: CustomCellRendererProps) => {
  return props.value ? (
    <span
      className={`h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]`}
    >
      {props.value.replace(/_/g, ' ').toUpperCase()}
    </span>
  ) : (
    <React.Fragment></React.Fragment>
  );
};

export default CategoryRenderer;
