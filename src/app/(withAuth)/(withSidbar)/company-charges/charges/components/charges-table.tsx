'use client';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  AgGridEvent,
  CellValueChangedEvent,
  ColDef,
  ColumnApiModule,
  ModuleRegistry,
  TextEditorModule,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import AgGridDataTable from '@/components/app/ag-grid-data-table';
import axios from '@/lib/axios';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import CategoryRenderer from './category-renderer';
import { Switch } from '@/components/ui/switch';
import { useProfileContext } from '@/components/app/provoders/profile-context';
import {
  CHARGES_PERMISSION,
  CHARGES_RATE_PERMISSION,
} from '@/configs/leftSideMenu/Permissions';
import { chargesCategory } from '../../config/constent';
import { useCharges } from '../../config/charges-context';
import { sidebar } from '../../config/sidebar-config';
import {
  autoSizeStrategy,
  rowSelection,
} from '@/app/(withAuth)/(withSidbar)/rate-analysis/(shipping-rate)/_configs/ra-sh-table-configs';
import ChargesBulkActions from './actions/bulk-action';
ModuleRegistry.registerModules([TextEditorModule, ColumnApiModule]);

const CompanyChargesTable = ({ data = [] }) => {
  const router = useRouter();
  const { setSelectItems, selectedItems, setIsDelete } = useCharges();
  const { hasEveryPermission } = useProfileContext();
  const [editableCols, setEditableCols] = useState(false);
  const gridRef = useRef<AgGridReact | null>(null);

  useEffect(() => {
    if (hasEveryPermission(CHARGES_PERMISSION.UPDATE)) {
      setEditableCols(true);
    }
  }, [hasEveryPermission]);

  // table columns
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        cellRenderer: (params) =>
          params.data && (
            <div className="capitalize">
              {params.data.name.replace(/_/g, ' ')}
            </div>
          ),
      },
      {
        field: 'category',
        headerName: 'Category',
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          values: chargesCategory.filter((filter) =>
            hasEveryPermission(
              CHARGES_RATE_PERMISSION[`VIEW_CHARGES_${filter.toUpperCase()}`],
            ),
          ),
          cellRenderer: CategoryRenderer,
        },
        cellRenderer: CategoryRenderer,
      },
      {
        field: 'cost',
        headerName: 'Cost',
      },
      {
        field: 'charge_type',
        headerName: 'Charge Type',
        cellEditor: 'agRichSelectCellEditor',
        cellRenderer: CategoryRenderer,
        cellEditorParams: {
          values: ['per_vehicle', 'per_container'],
          cellRenderer: CategoryRenderer,
        },
      },
      {
        field: 'created_at',
        headerName: 'Created At',
        valueGetter: (params) => {
          if (params.data) {
            return params.data.created_at
              ? format(new Date(params.data.created_at), 'yyyy-MM-d')
              : '';
          }
          return '';
        },
        editable: false,
      },
      {
        field: 'updated_at',
        headerName: 'Updated At',
        valueGetter: (params) => {
          if (params.data) {
            return params.data.created_at
              ? format(new Date(params.data.created_at), 'yyyy-MM-d')
              : '';
          }
          return '';
        },
        editable: false,
      },
      {
        field: 'status',
        headerName: 'Status',
        cellRenderer: (params) => {
          const handleToggle = () => {
            const newStatus =
              params.data.status === 'active' ? 'inactive' : 'active';
            params.node.setDataValue('status', newStatus);
          };

          return (
            <div className={'flex items-center' + editableCols ? '' : 'pt-1'}>
              {editableCols ? (
                <Switch
                  checked={params.data.status === 'active'}
                  onCheckedChange={handleToggle}
                />
              ) : (
                <span
                  className={`${params.data.status === 'active' ? 'bg-secondary' : 'bg-red-400'} mb-2 mr-2 rounded-sm px-2 py-1 font-medium capitalize`}
                >
                  {params.data.status}
                </span>
              )}
            </div>
          );
        },
        editable: false,
        maxWidth: 100,
        filter: false,
      },
    ],
    [editableCols],
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
      minWidth: 200,
      editable: editableCols,
    }),
    [editableCols],
  );
  const selectionColumnDef: ColDef = useMemo(() => {
    return {
      sortable: true,
      resizable: true,
      suppressHeaderMenuButton: false,
      pinned: 'left',
      maxWidth: 350,
      filter: false,
    };
  }, []);
  const onCellValueChanged = async (params: CellValueChangedEvent) => {
    const { data } = params;
    try {
      const response = await axios.patch(`/charges/${data.id}`, {
        chargeName: data.name,
        ...data,
      });
      toast.success('Charge updated successfully');
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
    }
  };
  const deleteRecords = async () => {
    try {
      setIsDelete(true);
      const response = await axios.delete(`/charges/${selectedItems}`);
      toast.success('Charge delete successfully');
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
      setIsDelete(false);
    }
  };
  return (
    <>
      <ChargesBulkActions
        selectedItems={selectedItems}
        show={selectedItems.length}
        handleOperaion={deleteRecords}
      />
      <AgGridDataTable
        ref={gridRef}
        columnDefs={colDefs}
        rowData={data}
        sideBar={sidebar}
        defaultColDef={defaultColDef}
        undoRedoCellEditing={true}
        undoRedoCellEditingLimit={100}
        autoSizeStrategy={autoSizeStrategy}
        rowSelection={
          hasEveryPermission(CHARGES_PERMISSION.DELETE)
            ? rowSelection
            : undefined
        }
        rowDragManaged={true}
        onCellValueChanged={onCellValueChanged}
        selectionColumnDef={selectionColumnDef}
        onSelectionChanged={(e: AgGridEvent) => {
          setSelectItems(e.api.getSelectedRows().map((el) => el.id));
        }}
      />
    </>
  );
};

export default CompanyChargesTable;
