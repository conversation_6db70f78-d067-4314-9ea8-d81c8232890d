import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Trash2, Loader2 } from 'lucide-react';
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useCharges } from '../../../config/charges-context';
type WaringType = {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  handleOperation: () => void;
};
export function WaringDailog({ open, setOpen, handleOperation }: WaringType) {
  const { isDelete } = useCharges();
  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure ?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete data from
            our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <Button
            onClick={() => {
              handleOperation();
              setOpen(false);
            }}
          >
            {isDelete ? <Loader2 className="animate-spin" /> : 'Continue'}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

const ChargesBulkActions = ({ selectedItems, show, handleOperaion }) => {
  const [showDisplay, setShowDisplay] = useState(false);
  const [showInternal, setShowInternal] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setShowInternal(show);
    }, 1);
    if (show) {
      setShowDisplay(true);
    } else {
      setTimeout(() => {
        setShowDisplay(false);
      }, 300);
    }
  }, [show]);

  return true ? (
    <div
      className={cn(
        'fixed bottom-12 left-1/2 z-50 w-[300px] max-w-[calc(100%-72px)] -translate-x-1/2 rounded-lg transition-all duration-300',
        showDisplay ? 'block translate-y-[100px]' : 'hidden',
        showInternal ? 'translate-y-0' : 'translate-y-[100px]',
      )}
    >
      <div
        className={cn(
          'shadow-custom flex items-center justify-between rounded-lg border border-primary/10 bg-sidebar p-3 text-sm text-foreground dark:border-white/10',
        )}
      >
        <div className="ms-2 flex items-center">
          {selectedItems.length} items selected
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="destructive"
            className="h-7"
            onClick={() => setOpen(true)}
          >
            {false ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 />}
            <span className="hidden md:inline">Delete</span>
          </Button>
        </div>
      </div>
      <WaringDailog
        open={open}
        setOpen={setOpen}
        handleOperation={handleOperaion}
      />
    </div>
  ) : (
    <></>
  );
};

export default ChargesBulkActions;
