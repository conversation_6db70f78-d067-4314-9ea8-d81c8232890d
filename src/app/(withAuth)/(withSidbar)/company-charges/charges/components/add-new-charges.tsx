'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import axios from '@/lib/axios';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, Controller } from 'react-hook-form';
import * as z from 'zod';
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Loader } from 'lucide-react';
import { CHARGES_PERMISSION } from '@/configs/leftSideMenu/Permissions';
import { useProfileContext } from '@/components/app/provoders/profile-context';
import { chargesCategory } from '../../config/constent';

const formSchema = z.object({
  category: z.string().min(1, 'Category is required'),
  chargeName: z.string().min(1, 'Charge is required'),
  cost: z.coerce.number().optional(),
  charge_type: z.enum(['per_vehicle', 'per_container']),
});
type formType = z.infer<typeof formSchema>;
export default function AddNewCharges() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { hasEveryPermission } = useProfileContext();
  const form = useForm<formType>({
    resolver: zodResolver(formSchema),
    defaultValues: { category: '', chargeName: '' },
  });

  const onSubmit = async (data: formType) => {
    try {
      setLoading(true);
      const response = await axios.post(`charges/create-charge`, { ...data });
      toast.success('Charge add successfully');
      setOpen(false);
      return response.data;
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      router.refresh();
      setLoading(false);
      form.reset();
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipContent>
          <p>Adding a Charge to the grid</p>
        </TooltipContent>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <TooltipTrigger asChild>
              <Button>Add Charge</Button>
            </TooltipTrigger>
          </PopoverTrigger>
          <PopoverContent className="w-[350px] rounded-lg">
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="mb-4 space-y-2">
                <h4 className="text-sm font-medium leading-none">
                  Add new Charge
                </h4>
                <p className="text-xs text-muted-foreground">
                  Add a new charge to the grid.
                </p>
              </div>
              <div className="grid gap-2">
                <div className="grid grid-cols-[50px_1fr] items-center gap-x-4">
                  <Label className="text-right text-xs">Category</Label>
                  <Controller
                    control={form.control}
                    name="category"
                    render={() => (
                      <Select
                        onValueChange={(value) =>
                          form.setValue('category', value)
                        }
                      >
                        <SelectTrigger className="w-[250px]">
                          <SelectValue placeholder="Select a Category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Categories</SelectLabel>
                            {chargesCategory
                              .filter((filter) =>
                                hasEveryPermission(
                                  CHARGES_PERMISSION[
                                    `VIEW_CHARGES_${filter.toUpperCase()}`
                                  ],
                                ),
                              )
                              .map((category) => (
                                <SelectItem key={category} value={category}>
                                  {category.replace(/_/g, ' ').toUpperCase()}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
                {form.formState.errors.category && (
                  <p className="pl-16 text-xs text-red-500">
                    {form.formState.errors.category.message}
                  </p>
                )}
                <div className="grid grid-cols-[50px_1fr] items-center gap-x-4">
                  <Label className="text-right text-xs">Charge Type</Label>
                  <Controller
                    control={form.control}
                    name="charge_type"
                    render={() => (
                      <Select
                        onValueChange={(value) =>
                          form.setValue(
                            'charge_type',
                            value as 'per_vehicle' | 'per_container',
                          )
                        }
                      >
                        <SelectTrigger className="w-[250px]">
                          <SelectValue placeholder="Select a charge type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Charge Type</SelectLabel>
                            {['per_vehicle', 'per_container'].map(
                              (category) => (
                                <SelectItem key={category} value={category}>
                                  {category.replace(/_/g, ' ').toUpperCase()}
                                </SelectItem>
                              ),
                            )}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
                {form.formState.errors.category && (
                  <p className="pl-16 text-xs text-red-500">
                    {form.formState.errors.category.message}
                  </p>
                )}
                <div className="grid grid-cols-[50px_1fr] items-center gap-x-4">
                  <Label className="text-right text-xs">Name</Label>
                  <Input
                    type="text"
                    {...form.register('chargeName')}
                    className="w-[250px]"
                    placeholder="Enter charge name"
                  />
                </div>

                {form.formState.errors.chargeName && (
                  <span className="pl-16 text-xs text-red-500">
                    {form.formState.errors.chargeName.message}
                  </span>
                )}

                <div className="grid grid-cols-[50px_1fr] items-center gap-x-4">
                  <Label className="text-right text-xs">Cost</Label>
                  <Input
                    type="number"
                    {...form.register('cost', { valueAsNumber: true })}
                    className="w-[250px]"
                    placeholder="Enter amount"
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="submit">
                    {loading ? <Loader className="animate-ping" /> : 'Submit'}
                  </Button>
                </div>
              </div>
            </form>
          </PopoverContent>
        </Popover>
      </Tooltip>
    </TooltipProvider>
  );
}
