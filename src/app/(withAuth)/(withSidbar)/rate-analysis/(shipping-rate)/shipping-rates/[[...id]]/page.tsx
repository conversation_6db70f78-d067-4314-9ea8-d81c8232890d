import React from 'react';
import RAShippingRatesPage from '../components/ra-shipping-rates-page';
import { RAShippingRatesProvider } from '../../_components/ra-shipping-rates-context';
import { notFound } from 'next/navigation';
import axios from '@/lib/axios-server';
import {
  getTokenAndKey,
  hasPagePermission,
  networkErrorHandler,
} from '@/app/functions/auth';
import { SHIPPING_RATE_ANALYSIS } from '@/configs/leftSideMenu/Permissions';

const fetchData = async ({ destination_id }) => {
  try {
    const { token } = await getTokenAndKey();

    const { data } = await axios.get('rate-analysis/shipping-rates', {
      params: {
        page: 1,
        per_page: 1000000,
        search: '',
        exactMatch: false,
        type: 'complete',
        destination_id: destination_id,
        filterData: JSON.stringify({}),
      },
      headers: { Authorization: `Bearer ${token}` },
    });

    return { ...data, records: data?.data };
  } catch (error) {
    return null;
  }
};

const fetchCustomPNLs = async () => {
  try {
    const { token } = await getTokenAndKey();

    const { data } = await axios.get(
      'rate-analysis/shipping-rates/custom-pnls',
      {
        headers: { Authorization: `Bearer ${token}` },
      },
    );

    return data?.data;
  } catch (error) {
    return null;
  }
};

const RASH_Page = async (props) => {
  const params = await props.params;

  const { id } = params;

  const destination_id =
    id?.length > 0 ? (Number.isNaN(+id[0]) ? 12 : +id[0]) : 12;

  const { result, code } = await hasPagePermission({
    permission: SHIPPING_RATE_ANALYSIS?.VIEW,
  });

  if (!result) {
    return networkErrorHandler({ result, code });
  }

  const dataPrmomise = fetchData({
    destination_id: destination_id,
  });

  const customPNLsPromise = await fetchCustomPNLs();

  const [data, customPNLs]: any[] = await Promise.all([
    dataPrmomise,
    customPNLsPromise,
  ]);

  if (data == null) return notFound();

  return (
    <>
      <RAShippingRatesProvider
        customPNLs={customPNLs ?? {}}
        destination_id={destination_id}
        baseURL={'/rate-analysis/shipping-rates'}
      >
        <RAShippingRatesPage
          data={data as any}
          destination_id={destination_id}
        />
      </RAShippingRatesProvider>
    </>
  );
};

export default RASH_Page;
