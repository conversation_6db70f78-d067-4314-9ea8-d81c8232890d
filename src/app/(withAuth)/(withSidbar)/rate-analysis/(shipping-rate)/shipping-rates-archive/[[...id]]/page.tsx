import React from 'react';
import RASH_ArchivePage from '../components/ra-sh-archive-page';
import {
  getTokenAndKey,
  hasPagePermission,
  networkErrorHandler,
} from '@/app/functions/auth';
import axios from '@/lib/axios-server';
import { notFound } from 'next/navigation';
import { SHIPPING_RATE_ANALYSIS } from '@/configs/leftSideMenu/Permissions';
import { RAShippingRatesProvider } from '../../_components/ra-shipping-rates-context';

const fetchData = async ({ destination_id, page, per_page, companies }) => {
  try {
    const { token } = await getTokenAndKey();

    const { data } = await axios.get('rate-analysis/shipping-rates-archive', {
      params: {
        page,
        per_page,
        search: '',
        exactMatch: false,
        type: 'complete',
        destination_id: destination_id,
        filterData: JSON.stringify({
          ...(companies?.length
            ? {
                ...{
                  OR: [
                    { 'company.name': companies },
                    companies.includes('General') ? { company_id: null } : {},
                  ],
                },
              }
            : {}),
        }),
      },
      headers: { Authorization: `Bearer ${token}` },
    });

    return { ...data, records: data?.data };
  } catch (error) {
    return null;
  }
};

const RASH_Archive = async (props) => {
  const searchParams = await props.searchParams;
  const params = await props.params;

  const { id } = params;

  const destination_id =
    id?.length > 0 ? (Number.isNaN(+id[0]) ? 12 : +id[0]) : 12;

  const { result, code } = await hasPagePermission({
    permission: SHIPPING_RATE_ANALYSIS?.VIEW_ARCHIVE,
  });

  if (!result) {
    return networkErrorHandler({ result, code });
  }

  const page = searchParams.page ? +searchParams.page : 1;
  const per_page = searchParams.per_page ? +searchParams.per_page : 50;
  const companies =
    searchParams.companies && searchParams.companies != 'undefined'
      ? JSON.parse(searchParams.companies)
      : [];

  const dataPrmomise = fetchData({
    destination_id: destination_id,
    page,
    per_page,
    companies,
  });

  const [data, customPNLs]: any[] = await Promise.all([dataPrmomise]);
  if (data == null) return notFound();

  return (
    <RAShippingRatesProvider
      customPNLs={customPNLs ?? {}}
      destination_id={destination_id}
      baseURL={'/rate-analysis/shipping-rates-archive'}
    >
      <RASH_ArchivePage destination_id={destination_id} data={data as any} />
    </RAShippingRatesProvider>
  );
};

export default RASH_Archive;
