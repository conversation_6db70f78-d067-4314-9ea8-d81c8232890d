import {
  getTokenAndKey,
  hasPagePermission,
  networkErrorHandler,
} from '@/app/functions/auth';
import axios from '@/lib/axios-server';
import { STORAGE_REPORTS } from '@/configs/leftSideMenu/Permissions';
import StorageListPage from './components/storage-list-page';

export const dynamic = 'force-dynamic';

const fetchData = async ({ page, per_page, search }) => {
  try {
    const { token } = await getTokenAndKey();

    const { data } = await axios.get('storage/storage-list', {
      params: {
        page,
        per_page,
        search,
        exactMatch: false,
        filterData: JSON.stringify({}),
      },
      headers: { Authorization: `Bearer ${token}` },
    });

    return { ...data, records: data?.data };
  } catch (error) {
    return null;
  }
};

const StorageReport = async (props) => {
  const searchParams = await props.searchParams;
  const { result, code } = await hasPagePermission({
    permission: STORAGE_REPORTS?.VIEW,
  });

  if (!result) {
    return networkErrorHandler({ result, code });
  }

  const page = searchParams.page ? +searchParams.page : 1;
  const per_page = searchParams.per_page ? +searchParams.per_page : 50;
  const search = searchParams.search ? searchParams.search : '';
  const data = await fetchData({
    page,
    per_page,
    search,
  });

  return <StorageListPage data={data} />;
};

export default StorageReport;
