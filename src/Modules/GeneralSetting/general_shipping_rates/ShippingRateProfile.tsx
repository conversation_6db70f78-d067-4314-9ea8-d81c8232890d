import ViewModal from '@/components/mainComponents/ViewModal';
import axios from '@/lib/axios';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CircularProgress,
  Collapse,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import dayjs from 'dayjs';
import { DatePicker } from '@mui/x-date-pickers';
import { formatDate } from '@/configs/vehicles/configs';
import QuillEditor from '@/components/mainComponents/MuiEditor';

const ShipmentRateProfile = ({ show, setShow, selectedItem }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState('');
  const [rateItems, setRateItems] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [rateItemId, setRateItemId] = useState(null);

  const fetchItems = async () => {
    try {
      setLoading('items');
      let res = await axios.get('shipping-rates/general-shipping-rates', {
        params: {
          id: selectedItem.id,
        },
      });
      setRateItems(res.data);
    } catch (error) {
      console.error(error);
    }
    setLoading('');
  };

  useEffect(() => {
    if (show) {
      fetchItems();
    }
  }, [show]);

  const addGeneralRates = async (values) => {
    try {
      await axios.post('shipping-rates/general-shipping-rates', {
        ...values,
        shipping_rate_id: selectedItem.id,
      });
      fetchItems();
      form.reset();
      setShowForm(false);
      toast.success('Rate Added Successfully');
    } catch (error) {}
  };

  const deleteRateItem = async () => {
    try {
      const id = rateItemId;
      setRateItems((prev) => prev.filter((row) => row.id != rateItemId));
      setRateItemId(null);

      await axios.delete('shipping-rates/general-shipping-rates', {
        params: {
          id,
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const closeProfile = () => {
    setShow(false);
    setShowForm(false);
    form.reset();
    setRateItems([]);
  };

  const form = useForm({
    mode: 'onChange',
    defaultValues: {
      company_ids: [],
      rate_40hc: 0,
      rate_45hc: 0,
      note: '',
      effective_date: new Date(),
    },
  });

  const AddCompany = () => {
    return (
      <>
        {!showForm && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'end',
              paddingBottom: 2,
            }}
          >
            <Box>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  setShowForm(true);
                }}
              >
                {' '}
                <AddIcon />
                Add
              </Button>
            </Box>
          </Box>
        )}
        <Collapse in={showForm}>
          <form onSubmit={form.handleSubmit(addGeneralRates)}>
            <Card
              sx={{
                mb: 1,
                backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
              }}
            >
              <CardContent>
                <Grid container spacing={2} sx={{ paddingBottom: '10px' }}>
                  <Grid
                    size={{
                      xs: 12,
                      md: 6,
                    }}
                  >
                    <Controller
                      control={form.control}
                      name="effective_date"
                      render={({ field, fieldState: { error } }) => (
                        <DatePicker
                          views={['year', 'month', 'day']}
                          label="Effective Date"
                          value={
                            !field.value ? null : dayjs(field.value).toDate()
                          }
                          format="yyyy/MM/dd"
                          onChange={(e) => {
                            field.onChange(e);
                          }}
                          inputRef={field.ref}
                          slotProps={{
                            textField: {
                              variant: 'outlined',
                              error: !!error,
                              helperText: error?.message,
                              size: 'small',
                              fullWidth: true,
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      md: 12,
                    }}
                  >
                    <Controller
                      name="note" // Unique name for the field
                      control={form.control}
                      defaultValue=""
                      render={({ field }) => (
                        <>
                          <Typography sx={{ fontSize: '14px' }}>
                            Note
                          </Typography>
                          <QuillEditor
                            height={120}
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            value={field.value}
                          />
                        </>
                      )}
                    />
                  </Grid>

                  <Grid
                    size={{
                      xs: 12,
                      md: 12,
                    }}
                  >
                    <Controller
                      name={`company_ids`}
                      control={form.control}
                      render={({ field }) => {
                        const item = {
                          name: 'company_id',
                          label: 'Company',
                          type: 'autocomplete',
                          url: '/autoComplete?column=name&modal=companies&id=',
                          keyName: 'name',
                        };
                        return (
                          <FilterAutocomplete
                            url={item.url}
                            label={item.label}
                            name={item.name}
                            keyName={item.keyName ?? item.name}
                            values={field.value}
                            onChange={(event) => {
                              field.onChange(event);
                            }}
                          />
                        );
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
              <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
                <Button
                  size="small"
                  variant="contained"
                  color="warning"
                  onClick={() => {
                    form.reset();
                    setShowForm(false);
                  }}
                >
                  Cancel
                </Button>

                <Button
                  loading={false}
                  size="small"
                  variant="contained"
                  type="submit"
                >
                  Submit
                </Button>
              </CardActions>
            </Card>
          </form>
        </Collapse>
      </>
    );
  };

  return (
    <ViewModal
      createdBy={''}
      name={selectedItem?.title}
      created_at={selectedItem?.created_at}
      updated_at={selectedItem?.created_at}
      onClose={() => closeProfile()}
      show={show}
      rateData={selectedItem}
      title="Shipping Rate Details"
    >
      {
        <Box sx={{ pt: 1 }}>
          <AddCompany />
          <TableContainer
            sx={{
              height: '500px',
              overflowY: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '10px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: '#6E6E6E',
                borderRadius: '12px',
              },
            }}
          >
            <Table aria-label="collapsible table" size="small" stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Company</TableCell>
                  <TableCell>Effective Date</TableCell>
                  <TableCell>40HC</TableCell>
                  <TableCell>45HC</TableCell>
                  <TableCell>Mix</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center' }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : (
                  rateItems.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.company.name}</TableCell>
                      <TableCell>
                        {formatDate(row.effective_date, 'MMM Do YYYY')}
                      </TableCell>
                      <TableCell>${row.shipping_rates.rate_40hc}</TableCell>
                      <TableCell>${row.shipping_rates.rate_45hc}</TableCell>
                      <TableCell>${row.shipping_rates.mix_rate}</TableCell>
                      <TableCell>
                        <IconButton
                          color="error"
                          onClick={() => setRateItemId(row.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <AppConfirmDialog
            maxWidth={'sm'}
            open={rateItemId > 0}
            onDeny={() => {
              setRateItemId(null);
            }}
            onConfirm={() => {
              deleteRateItem();
            }}
            title="Are you sure to delete!"
            dialogTitle="Delete Rate!"
          />
        </Box>
      }
    </ViewModal>
  );
};

export default ShipmentRateProfile;
