import {
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
} from '@mui/material';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import { formatDate } from '@/configs/vehicles/configs';
import JSZip from 'jszip';

const DownloadOptions = ({
  open,
  setOpen,
  selectedItems,
  setSelectedItems,
}) => {
  const [cancelToken, setCancelToken] = useState(null);
  const [printLoading, setPrintLoading] = useState(false);
  const [all, setAll] = useState(false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) =>
    setAll(event.target.checked);

  const { getValues, control, watch, reset } = useForm({
    defaultValues: {
      company_id: null,
      company_name: null,
    },
    mode: 'onChange',
  });

  const onClose = () => {
    setAll(false);
    reset();
    setSelectedItems([]);
    setOpen(false);
  };

  const getCompanyName = async (id: number) => {
    if (!id) return false;
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }

      const controller = new AbortController();
      setCancelToken(controller);
      let { data } = await axios.get(`companies/companyName/${id}`, {
        signal: controller.signal,
      });
      return data?.name;
    } catch (err) {
      console.log(err);
    }
  };

  const downloadPdf = async () => {
    const ids = await selectedItems?.map((e) => e.id);
    if (all) {
      try {
        setPrintLoading(true);
        const res = await axios.get(`shipping-rates/downloadMultipleRates`, {
          params: {
            destination_id: selectedItems[0]?.destination_id,
            id: ids[0],
          },
        });
        const zip = new JSZip();
        res.data.forEach((fileData) => {
          const uint8Array = new Uint8Array(fileData.buffer.data);
          zip.file(fileData.name, uint8Array);
        });
        zip.generateAsync({ type: 'blob' }).then((blob) => {
          const blobUrl = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = blobUrl;
          a.download = `General Rates ${formatDate(new Date())}.zip`;
          document.body.appendChild(a);
          a.click();
          URL.revokeObjectURL(blobUrl);
        });
        setPrintLoading(false);
        onClose();
      } catch (error) {
        console.error('Error fetching or processing data:', error);
        setPrintLoading(false);
      }
    } else {
      const company_id = getValues('company_id');
      const companyName = getValues('company_name');
      if (!company_id && !companyName) {
        toast.warn('Please select a company or enter it manually!');
        return false;
      }

      const company = await getCompanyName(company_id);
      let company_name = company ? company : companyName;

      try {
        setPrintLoading(true);
        const response = await axios.get(`shipping-rates/downloadRates`, {
          params: {
            company_name: company_name.replace(/ /g, '_'),
            ids,
          },
          responseType: 'blob',
        });
        const url = window.URL.createObjectURL(new Blob([response.data]));
        setSelectedItems([]);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${company_name}-shipping-rates.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } catch (error) {
        console.error(error);
      }
      onClose();
      setPrintLoading(false);
    }
  };

  const confirmDialogStatusVehicles = () => {
    return (
      <Box>
        <Grid container>
          <Grid
            sx={{ mb: 1 }}
            size={{
              md: 12,
            }}
          >
            <Box>Download The Rates For Below Destinations: </Box>
            <Box sx={{ fontWeight: 'bold', textTransform: 'uppercase' }}>
              ( {selectedItems?.map((el) => el.destinations?.name).join(' | ')}{' '}
              )
            </Box>
          </Grid>
          <Grid
            sx={{ mb: 1 }}
            size={{
              md: 12,
            }}
          >
            <Divider />
            {selectedItems?.length == 1 && (
              <>
                <Box sx={{ fontWeight: 'bold' }}>
                  <FormControlLabel
                    control={<Checkbox checked={all} onChange={handleChange} />}
                    label={`ALL (${selectedItems
                      ?.map((el) => el.destinations?.name)
                      .join(' | ')
                      .toUpperCase()}) CUSTOMERS.`}
                  />
                </Box>
                <Divider />
              </>
            )}
          </Grid>

          {!all && (
            <>
              <Grid
                sx={{ mb: 1 }}
                size={{
                  md: 12,
                }}
              >
                Please Select A Company Or Enter A Name For PDF:
              </Grid>
              <Grid
                sx={{ mb: 1 }}
                size={{
                  md: 12,
                }}
              >
                <Controller
                  name={`company_id`}
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <AutoComplete
                      url="autoComplete"
                      label="Select Company"
                      fieldName={'name'}
                      field={field}
                      error={error}
                      staticOptions={false}
                      column={'name'}
                      modal={'companies'}
                    />
                  )}
                />
              </Grid>
              {!watch('company_id') && (
                <Grid
                  size={{
                    md: 12,
                  }}
                >
                  <Controller
                    name="company_name"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        id="company_name"
                        value={field.value ?? ''}
                        label="Company Name"
                        fullWidth
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
              )}
            </>
          )}
        </Grid>
      </Box>
    );
  };
  return (
    <AppConfirmDialog
      open={open}
      onDeny={onClose}
      onConfirm={downloadPdf}
      title={confirmDialogStatusVehicles()}
      dialogTitle={'DOWNLOAD PDF'}
      confirmText={'Download'}
      cancelText="Cancel"
      maxWidth={'sm'}
      submitting={printLoading}
    />
  );
};

export default DownloadOptions;

{
  /* <FormControl fullWidth size="small">
        <InputLabel id="demo-select-small-label">Company</InputLabel>
        <Select
          labelId="demo-select-small-label"
          id="demo-select-small"
          //value={status || ''}
          label="Status"
          //onChange={(event) => setStatusFunction(event, setStatus)}
        >
          <MenuItem value={null || ''}>
            <em>None</em>
          </MenuItem>
        </Select>
      </FormControl> */
}
