import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import { useForm } from 'react-hook-form';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

const AssignRate = ({ show, setShow, selectedItems, setSelectedItems }) => {
  const [all, setAll] = useState(true);
  const [loading, setLoading] = useState(false);

  const form: any = useForm({
    defaultValues: { company_ids: [], all_customers: false },
  });

  const sendEmail = async (values: any) => {
    try {
      setLoading(true);
      const res = await axios.post(`shipping-rates/send_general_rates`, {
        ...values,
        ...(selectedItems?.length == 1
          ? { destination_id: selectedItems[0]?.destination_id }
          : {}),
        // destination_id: selectedItem?.destination_id,
        ids: selectedItems?.map((item) => item?.id),
      });
      if (res?.data?.result) {
        setSelectedItems([]);

        setShow(false);
        toast.success('Email has been sent successfully!');
      }
      setLoading(false);
      handleClose();
    } catch (err) {
      handleClose();
      console.log(err);
      setLoading(false);
    }
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 600,
    bgcolor: 'background.paper',
  };

  const handleClose = () => {
    form.reset();
    setSelectedItems([]);
    setShow(false);
  };

  return (
    <Modal open={show} onClose={handleClose}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Assign Rates & Send Email</Typography>
          <IconButton
            aria-label="close"
            sx={{ color: 'grey' }}
            onClick={() => setShow(false)}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <CardContent sx={{ height: '80% !important' }}>
          <form onSubmit={form.handleSubmit(sendEmail)}>
            {selectedItems?.length == 1 && (
              <Grid sx={{ paddingBottom: '15px' }} size={{ xs: 12, md: 12 }}>
                <Controller
                  control={form.control}
                  name="all_customers"
                  render={({ field }) => (
                    <FormControl component="fieldset" variant="standard">
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Checkbox
                              inputRef={field.ref}
                              checked={field.value}
                              onChange={(event) => {
                                setAll(field.value);
                                field.onChange(event);
                              }}
                              name="all_customers"
                            />
                          }
                          sx={{ '& .MuiSvgIcon-root': { fontSize: 28 } }}
                          label={`All (${selectedItems[0]?.destinations?.name.toUpperCase()}) Customers`}
                        />
                      </FormGroup>
                    </FormControl>
                  )}
                />
              </Grid>
            )}

            <Grid sx={{ paddingBottom: '15px' }} size={{ xs: 12, md: 12 }}>
              {all ? (
                <Controller
                  name={`company_ids`}
                  control={form.control}
                  render={({ field }) => {
                    const item = {
                      name: 'company_id',
                      label: 'Company',
                      type: 'autocomplete',
                      url: `/autoComplete?column=name&modal=complete_not_special_companies&destination_ids=${selectedItems
                        ?.map((item) => item.destination_id)
                        .join(',')}&id=`,
                      keyName: 'name',
                    };
                    return (
                      <FilterAutocomplete
                        url={item.url}
                        label={item.label}
                        name={item.name}
                        keyName={item.keyName ?? item.name}
                        values={field.value}
                        onChange={(event) => field.onChange(event)}
                      />
                    );
                  }}
                />
              ) : (
                <Controller
                  name={`company_ids`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      value={field.value ?? ''}
                      label={'Company'}
                      fullWidth
                      variant="outlined"
                      helperText={error?.message}
                      InputProps={{ readOnly: true }}
                    />
                  )}
                />
              )}
            </Grid>

            <Grid sx={{ textAlign: 'right' }} size={{ xs: 6, md: 6 }}>
              <Button
                endIcon={<CloseIcon />}
                size="small"
                variant="contained"
                color="error"
                sx={{ mx: '8px' }}
                onClick={handleClose}
                loadingPosition="end"
              >
                <span>{'Cancel'}</span>
              </Button>
              <Button
                endIcon={<SendIcon />}
                size="small"
                variant="contained"
                type="submit"
                color="success"
                sx={{ mx: '8px' }}
                loading={loading}
                loadingPosition="end"
              >
                <span>{'Send Email'}</span>
              </Button>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Modal>
  );
};

export default AssignRate;
