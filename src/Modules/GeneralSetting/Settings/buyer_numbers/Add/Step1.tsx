import { Box, Grid, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';

const Step1 = ({ form }) => {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">Buyer Numbers Details</Typography>
      <Grid container spacing={3} sx={{ mt: 1 }}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="company_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Company"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'companies'}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="buyer_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.buyer_number?.message.length > 0 ? true : false
                }
                id="buyer_number"
                value={field.value ?? ''}
                label="Buyer Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Step1;
