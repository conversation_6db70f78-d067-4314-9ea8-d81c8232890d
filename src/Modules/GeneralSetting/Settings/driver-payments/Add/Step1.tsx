import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { formFormatDate } from '@/configs/configs';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import {
  Box,
  Chip,
  CircularProgress,
  Grid,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';

const Step1 = ({ form, paymentId }) => {
  const driver = form.watch('driver');
  const start_date = form.watch('start_date');
  const end_date = form.watch('end_date');

  const [totalAmounts, setTotalAmounts] = useState({ amount: 0, bonus: 0 });
  const [containers, setContainers] = useState([]);
  const [_removedContainers, setRemovedContainers] = useState([]);
  const [containersLoading, setContainersLoading] = useState(false);
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  const loadDriverContainers = async () => {
    try {
      setContainersLoading(true);
      const { data } = await axios.get('/driver-payments/containers', {
        params: {
          start_date,
          end_date,
          driver_id: driver,
          payment_id: paymentId,
        },
      });
      setContainers(data?.data);
      setRemovedContainers(data?.removed);
      calcAmounts(data?.data);
      setContainerValues(data?.data, 'containers');
      setContainerValues(data?.removed, 'removed');
      setContainersLoading(false);
    } catch (error) {
      setContainersLoading(false);
    }
  };

  const calcAmounts = (cns) => {
    let amount = 0;
    let bonus = 0;
    cns.forEach((cn) => {
      if (driver == cn.pull_driver_id) {
        amount += cn.pull_amount;
        bonus += cn.pull_driver_bonus ?? 0;
      }
      if (driver == cn.ingate_driver_id) {
        amount += cn.ingate_amount;
        bonus += cn.ingate_driver_bonus ?? 0;
      }
    });
    form.setValue('amount', amount + bonus);
    setTotalAmounts({ amount, bonus });
  };

  const setContainerValues = (cns, key) => {
    const temp = [];
    cns.forEach((cn) => {
      if (cn.pull_driver_id == driver) {
        temp.push({
          id: cn.id,
          amount: cn.pull_amount,
          bonus: cn.pull_driver_bonus,
          type: 'pull',
        });
      }
      if (cn.ingate_driver_id == driver) {
        temp.push({
          id: cn.id,
          amount: cn.ingate_amount,
          bonus: cn.ingate_driver_bonus,
          type: 'ingate',
        });
      }
    });
    form.setValue(key, temp);
  };

  useEffect(() => {
    if (driver && start_date && end_date) {
      loadDriverContainers();
    }
  }, [driver, start_date, end_date]);

  return (
    <>
      <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
        <Typography variant="h5" mb={2}>
          Payment Details
        </Typography>
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="driver"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={'autoComplete'}
                  label="Driver"
                  fieldName="name"
                  field={field}
                  error={error}
                  column={'name'}
                  modal={'drivers'}
                  staticOptions={false}
                  disableAutoComplete={paymentId ? true : false}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              control={form.control}
              name="start_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Start Date"
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  maxDate={end_date ? new Date(end_date) : null}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              control={form.control}
              name="end_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="End Date"
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  minDate={start_date ? new Date(start_date) : null}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="amount"
              control={form.control}
              // defaultValue={defaultValue}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={error?.message.length > 0 ? true : false}
                  id="amount"
                  type="number"
                  value={field.value === '' ? '' : +field.value}
                  label="Total Amount"
                  fullWidth
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  InputProps={{
                    endAdornment: containersLoading ? (
                      <InputAdornment position="end">
                        <CircularProgress size={20} />
                      </InputAdornment>
                    ) : null,
                    readOnly: true,
                  }}
                  ref={field?.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Box
              sx={{
                width: '100%',
                my: 2,
              }}
            >
              <DataTable3
                hidePagination={true}
                // start default props
                height="400px"
                options={{}}
                hideCheckBox
                setOptions={() => {}}
                totalItems={100}
                loading={false}
                items={containers.map((item2, index) => {
                  return { ...item2, index };
                })}
                selectedItems={[]}
                setSelectedItems={() => {}}
                headers={[
                  {
                    id: 'id',
                    label: 'ID',
                    sortable: false,
                  },
                  {
                    id: 'pull_payment_id',
                    label: 'ppid',
                    align: 'right',
                  },
                  {
                    id: 'ingate_payment_id',
                    label: 'ipid',
                    align: 'right',
                  },
                  {
                    id: 'container_number',
                    label: 'Container number',
                    sortable: false,
                  },
                  {
                    id: 'type',
                    label: 'Type',
                    sortable: false,
                    align: 'center',
                  },
                  {
                    id: 'amount',
                    label: 'Amount',
                    sortable: false,
                    align: 'right',
                  },
                  {
                    id: 'bonus',
                    label: 'Bonus',
                    sortable: false,
                    align: 'right',
                  },
                  {
                    id: 'pull_date',
                    label: 'Pull Date',
                    sortable: false,
                    align: 'left',
                  },
                  {
                    id: 'ingate_date',
                    label: 'Ingate Date',
                    sortable: false,
                    align: 'left',
                  },
                ]}
                footerValues={{
                  amount: formatter.format(totalAmounts.amount),
                  bonus: formatter.format(totalAmounts.bonus),
                }}
                totalCalc={containers.length}
                type={(item) => (
                  <>
                    {item.pull_driver_id == driver && (
                      <Chip
                        size="small"
                        label={'Pull'}
                        sx={{
                          color: 'white',
                          backgroundColor: `#66bb6a`,
                          mx: 0.1,
                        }}
                      />
                    )}
                    {item.ingate_driver_id == driver && (
                      <Chip
                        size="small"
                        label={'Ingate'}
                        sx={{
                          color: 'white',
                          backgroundColor: `#e66e19`,
                          mx: 0.1,
                        }}
                      />
                    )}
                  </>
                )}
                amount={(item) =>
                  item.pull_driver_id == item.ingate_driver_id &&
                  item.pull_driver_id == driver
                    ? formatter.format(item.pull_amount + item.ingate_amount)
                    : formatter.format(
                        item.pull_driver_id == driver
                          ? item.pull_amount
                          : item.ingate_amount,
                      )
                }
                bonus={(item) =>
                  item.pull_driver_id == item.ingate_driver_id &&
                  item.pull_driver_id == driver
                    ? formatter.format(
                        item.pull_driver_bonus + item.ingate_driver_bonus,
                      )
                    : formatter.format(
                        item.pull_driver_id == driver
                          ? (item.pull_driver_bonus ?? 0)
                          : (item.ingate_driver_bonus ?? 0),
                      )
                }
                pull_date={(item) =>
                  item.pull_driver_id == driver
                    ? formatDate(item.pull_date)
                    : ''
                }
                ingate_date={(item) =>
                  item.ingate_driver_id == driver
                    ? formatDate(item.ingate_date)
                    : ''
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Step1;
