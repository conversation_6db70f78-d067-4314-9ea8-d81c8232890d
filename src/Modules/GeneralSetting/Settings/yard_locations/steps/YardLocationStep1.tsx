import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import {
  Autocomplete,
  Chip,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  TextField,
  Typography,
} from '@mui/material';
import { Box } from '@mui/system';
import { useState } from 'react';
import { Controller } from 'react-hook-form';
export const customer_status = ['active', 'deactive'];

export default function YardLocationStep1({ form }) {
  const [showPassword, setShowPassword] = useState(false);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 5 }}>
        Yard Location Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.name?.message.length > 0 ? true : false}
            id="name"
            label="Name"
            fullWidth
            variant="outlined"
            {...form.register('name')}
            helperText={form.errors.name?.message}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="location_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Location"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'locations'}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label={
              <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
                Username (Email)
              </Box>
            }
            error={!!form.errors.username}
            id="outlined-error"
            {...form?.register('username')}
            helperText={form.errors.username?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={customer_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <FormControl variant="outlined" sx={{ width: '100%' }}>
            <OutlinedInput
              id="password"
              fullWidth
              placeholder="Password"
              {...form.register('password')}
              error={!!form.errors.password}
              aria-describedby="outlined-weight-helper-text"
              helperText={form.errors.password?.message}
              size="small"
              type={showPassword ? 'text' : 'password'}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={() => setShowPassword((show) => !show)}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              }
            />
            {form.errors.password && (
              <FormHelperText sx={{ color: 'red' }}>
                {form.errors.password.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="emails"
            control={form.control}
            render={({ field, fieldState: { invalid, error } }) => (
              <Autocomplete
                multiple
                id="tags-filled"
                options={[]}
                value={field.value}
                freeSolo
                renderTags={(value: readonly string[], getTagProps) =>
                  value.map((option: string, index: number) => (
                    <Chip
                      key={index}
                      variant="outlined"
                      label={option}
                      {...getTagProps({ index })}
                    />
                  ))
                }
                onChange={(_event: any, newValue) => {
                  field.onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    label="Emails "
                    placeholder="type email and press enter..."
                    size="small"
                    error={invalid}
                    helperText={error?.[0]?.message}
                  />
                )}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label={
              <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
                Address Information
              </Box>
            }
            error={!!form.errors.bio}
            multiline
            rows={4}
            id="outlined-error"
            {...form?.register('address_info')}
            helperText={form.errors.bio?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label={<Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Bio</Box>}
            error={!!form.errors.bio}
            multiline
            rows={4}
            id="outlined-error"
            {...form?.register('bio')}
            helperText={form.errors.bio?.message}
          />
        </Grid>
      </Grid>
      <Typography variant="h5" sx={{ my: 2 }}>
        Loading Rates
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_4v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_4v?.message.length > 0 ? true : false
                }
                id="rate_40hc_4v"
                value={field.value}
                label="40HC (4 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_4v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_4v?.message.length > 0 ? true : false
                }
                id="rate_45hc_4v"
                value={field.value}
                label="45HC (4 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_3v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_3v?.message.length > 0 ? true : false
                }
                id="rate_40hc_3v"
                value={field.value}
                label="40HC (3 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_3v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_3v?.message.length > 0 ? true : false
                }
                id="rate_45hc_3v"
                value={field.value}
                label="45HC (3 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_halfcut"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_halfcut?.message.length > 0
                    ? true
                    : false
                }
                id="rate_40hc_halfcut"
                value={field.value}
                label="40HC (Halfcut)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_halfcut"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_halfcut?.message.length > 0
                    ? true
                    : false
                }
                id="rate_45hc_halfcut"
                value={field.value}
                label="45HC (Halfcut)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
