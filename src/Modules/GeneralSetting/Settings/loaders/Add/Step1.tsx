import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { formatFileName } from '@/utils/imageUtils';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Input,
  TextField,
  Typography,
} from '@mui/material';

import { useState } from 'react';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { loader_status } from '@/configs/general_setting/settings/loaderHeader';

const Step1 = ({ form, removeAttachment, deletedItems, image }) => {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'attachments',
  });

  const [deleteOpen, setDeleteOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState(null);

  return (
    <>
      <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
        <Typography variant="h5">Loader Details</Typography>
        {image}
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 8,
            }}
          >
            <Controller
              name="name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="name"
                  required
                  label="Loader Name"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.name?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <Controller
              name="status"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  defualtValue={loader_status[0]}
                  url={false}
                  label="Status"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={loader_status}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="email"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="email"
                  label="Email"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.email?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="phone"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="phone"
                  label="Phone"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.phone?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="port_of_loading"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url="autoComplete"
                  label="POL (Port Of Loading)"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'locations'}
                />
              )}
            />
          </Grid>
        </Grid>

        <Typography variant="h5" mt={2} mb={1}>
          Documents
        </Typography>

        {fields.map((item: any, i) => (
          <Box
            sx={{
              py: 2,
              px: 2,
              pr: 6,
              mb: 1,
              border: '1px solid gray',
              borderRadius: '6px',
              position: 'relative',
              display: deletedItems.includes(item.document_id)
                ? 'none'
                : 'block',
            }}
            key={item.id}
          >
            <IconButton
              size="small"
              color="error"
              onClick={async () => {
                if (item?.uploaded) {
                  setDeleteOpen(true);
                  setDeleteItem({
                    id: item?.document_id,
                    index: i,
                    filename: item.filename,
                  });
                } else {
                  remove(i);
                }
              }}
              aria-label="delete"
              sx={{ position: 'absolute', right: 10, top: 10 }}
            >
              <CloseIcon />
            </IconButton>
            <Controller
              name={`attachments.${i}.type`}
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  value={field?.value ?? field.value}
                  sx={{ mb: 1 }}
                  size="small"
                  error={error ? true : false}
                  helperText={error?.message}
                  required
                  label="Type"
                  fullWidth
                  variant="outlined"
                ></TextField>
              )}
            />
            <Controller
              name={`attachments.${i}.file`}
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <>
                    <label htmlFor={`fileInput${i}`} style={{ width: '95%' }}>
                      <Input
                        type="file"
                        onChange={(e) => {
                          const input = e.target as HTMLInputElement;
                          if (input.files) {
                            field.onChange(input.files[0]);
                          }
                        }}
                        id={`fileInput${i}`}
                        style={{ display: 'none' }}
                      />
                      <Button
                        component="span"
                        variant="contained"
                        color="primary"
                        style={{ width: '100%' }}
                        onClick={
                          item?.uploaded
                            ? (e) => {
                                e.preventDefault();
                              }
                            : null
                        }
                      >
                        {item?.uploaded
                          ? formatFileName(item.filename)
                          : field?.value?.name
                            ? formatFileName(field?.value?.name)
                            : 'Choose File'}
                      </Button>
                    </label>
                    <Typography
                      mt={0.2}
                      textAlign={'start'}
                      mx={'14px'}
                      variant="body1"
                      fontSize={10}
                      color={'error'}
                    >
                      {error?.message}
                    </Typography>
                  </>
                );
              }}
            />
          </Box>
        ))}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              append({ name: '' });
            }}
          >
            Add Document
          </Button>
        </Box>
      </Box>
      <AppConfirmDialog
        maxWidth={'sm'}
        open={deleteOpen}
        onDeny={() => {
          setDeleteOpen(false);
        }}
        onConfirm={async () => {
          await removeAttachment(deleteItem?.id);
          setDeleteOpen(false);
          setDeleteItem(null);
          // remove(deleteItem.index);
        }}
        title={`Are you sure you want to delete ${deleteItem?.filename}?`}
        dialogTitle={'Delete File'}
      />
    </>
  );
};

export default Step1;
