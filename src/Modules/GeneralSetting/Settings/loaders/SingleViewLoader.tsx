import ProfileCustomRow from '@/components/mainComponents/ProfileCustomRow';
import ViewModal from '@/components/mainComponents/ViewModal';
import { TabPanel } from '@mui/lab';
import { Box, Button, Typography } from '@mui/material';
import { useState } from 'react';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { formatDate } from '@/configs/vehicles/configs';
import DownloadIcon from '@mui/icons-material/Download';
import axios from '@/lib/axios';
import { extractFilename, getImageSizeUrl } from '@/utils/imageUtils';
import { removeUnderScore2 } from '@/configs/common';

const SingleViewLoader = ({ show, setView, data }) => {
  const tabs = [{ label: 'General Info', value: 'general_info' }];
  const [_activeTab, setActiveTab] = useState('general_info');

  const [downloading, setDownloading] = useState(null);

  const downloadDocument = async (key) => {
    try {
      setDownloading(key);
      // Specify that we want to download a blob
      const response = await axios.get('/loaders/documents', {
        params: { key },
        responseType: 'blob', // Important to handle binary data files
      });

      // Create a URL from the Blob
      const url = window.URL.createObjectURL(new Blob([response.data]));

      // Create a link to download it
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', extractFilename(key)); // Set the filename here
      document.body.appendChild(link);

      // Trigger the download
      link.click();

      // Clean up and remove the link
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url); // Free up memory by revoking the blob URL
    } catch (error) {}
    setDownloading(null);
  };

  return (
    <ViewModal
      createdBy={data?.createdByUser?.fullname}
      name={data?.name}
      created_at={data?.created_at}
      updated_at={data?.updated_at}
      status={data?.loginable?.status}
      onClose={() => setView(false)}
      show={show}
      tabs={tabs}
      setActiveTab={setActiveTab}
      title="Loader Profile"
      photo={getImageSizeUrl(data?.photo, 100)}
      entity="loaders"
      entity_id={data?.id}
    >
      <Box sx={{ height: '400px', overflow: 'auto', overflowX: 'hidden' }}>
        <TabPanel value="general_info" sx={{ px: 0, py: 1 }}>
          <ProfileCustomRow
            itemName="Name"
            itemText={data?.name}
            itemName2="POL (Port Of Loading)"
            itemText2={data?.portOfLoading?.name}
          />
          <ProfileCustomRow
            bgColor
            itemName="Email"
            itemText={data?.email}
            itemName2="phone"
            itemText2={data?.phone}
          />
          <ProfileCustomRow
            itemName="Status"
            itemText={removeUnderScore2(data?.status)}
          />
          <Box sx={{ mt: 2 }}>
            <Typography variant="h4" fontSize={16} fontWeight={600} mb={2}>
              Documents
            </Typography>
            <DataTable3
              hidePagination={true}
              height={'auto'}
              // start default props
              options={{}}
              hideCheckBox
              setOptions={() => {}}
              totalItems={100}
              loading={false}
              items={data?.documents}
              selectedItems={[]}
              setSelectedItems={() => {}}
              headers={[
                { id: 'id', label: 'ID', sortable: false },
                {
                  id: 'document_type',
                  label: 'Document Type',
                  sortable: false,
                },
                { id: 'created_at', label: 'Created at', sortable: false },
                { id: 'created_by', label: 'Created by', sortable: false },
                { id: 'download', label: 'Download', sortable: false },
              ]}
              created_by={(item) => <Box>{item?.createdByUser?.fullname}</Box>}
              created_at={(item) => (
                <Box>
                  {item?.created_at ? formatDate(item?.created_at) : ''}
                </Box>
              )}
              download={(item) => (
                <Button
                  loading={downloading == item.key}
                  variant="contained"
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={() => {
                    downloadDocument(item.key);
                  }}
                >
                  Download
                </Button>
              )}
            />
          </Box>
        </TabPanel>
      </Box>
    </ViewModal>
  );
};

export default SingleViewLoader;
