import { contextProvider } from '@/contexts/ProfileContext';
import { useContext, useEffect, useState } from 'react';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { LOADERS } from '@/configs/leftSideMenu/Permissions';
import { Avatar, Box, Chip, Container } from '@mui/material';
import Head from 'next/head';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import axios from '@/lib/axios';
import {
  filterContentLoader,
  HeaderInfo,
  loadCombinationTypes,
} from '@/configs/general_setting/settings/loaderHeader';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { formatDate } from '@/configs/vehicles/configs';
import { recordManager } from '@/configs/configs';
import { applySavedColumns } from '@/utils/columnUtils';
import { getImageSizeUrl } from '@/utils/imageUtils';
import { useRouter } from 'next/router';
import DriverDateFilter from '../drivers/DriverDateFilter';
import { removeUnderScore2 } from '@/configs/common';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';

export default function LoaderReports({ apiUrl, defaultHeaders }) {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const router = useRouter();
  const perms = permittedMenu(profile?.data);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [dateInterval, setDateInterval] = useState({});

  const [tableRecords, setTableRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openFilter, setOpenFilter] = useState(false);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  //@ts-ignore
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [selectedFilter, setSelectedFilter] = useState({
    selected: 'this_week',
  });

  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });

  const pageName = 'Loaders Reports';

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          order: options.orderBy.order,
          filterData: JSON.stringify({
            ...options.filterData,
            dateFilter: selectedFilter,
          }),
        },
      });
      setTotalItems(data.total);
      setDateInterval(data.date);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    // options.orderBy,
    options.filterData,
    selectedFilter,
  ]);

  const handleStatusClicked = (loader_id, type) => {
    const routerData: {
      pathname: string;
      query: {
        filterData: string;
      };
    } = {
      pathname: '/shipment/all',
      query: {
        filterData: JSON.stringify({
          loaders: { some: { id: loader_id } },
          load_combination_type: type,
          loading_date: dateInterval,
        }),
      },
    };

    router.push(routerData);
  };

  const getCombinationsTypesColumns = () => {
    const cols = {};
    loadCombinationTypes.forEach((el) => {
      cols[el] = (item) => {
        return (
          <Box
            onClick={() => {
              handleStatusClicked(item.id, el === 'N/A' ? null : el);
            }}
            style={{
              color: '#2196f3',
              fontWeight: 'bold',
              fontSize: '10px',
              cursor: 'pointer',
            }}
          >
            {item[el]}
          </Box>
        );
      };
    });
    return cols;
  };
  console.log(tableRecords);

  return perms && !perms?.includes(LOADERS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Loaders Reports</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />
        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Loaders Reports'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={false}
              //onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Loader? `}
              dialogTitle={`Delete department Item`}
              showDownload={false}
              onAdd={() => {}}
              onEdit={() => {}}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={false}
              showEditButton={false}
              showDeleteButton={false}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              startCustomComponent={
                <DriverDateFilter
                  selected={selectedFilter}
                  onSelect={(value) => {
                    setSelectedFilter(value);
                  }}
                />
              }
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="loaders"
          name={(item) => (
            <Box
              sx={{
                px: 1,
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'center',
                gap: 1,
                cursor: 'pointer',
              }}
            >
              <Avatar
                sx={{
                  width: 28,
                  height: 28,
                  textTransform: 'capitalize',
                  // color: theme.colors.primary,
                }}
                alt={item.name}
                src={getImageSizeUrl(item?.photo, 100)}
              >
                {item?.photo ? '' : item?.name?.[0]}
              </Avatar>
              {item.name}
            </Box>
          )}
          total={(item) => (
            <>{item?.easy + item?.tight + item?.normal + (item['N/A'] || 0)}</>
          )}
          status={({ status }) => (
            <Chip
              size="small"
              label={removeUnderScore2(status)}
              sx={{
                color: 'white',
                backgroundColor: `${
                  status == 'active' ? '#66bb6a' : '#e66e19'
                }`,
              }}
            />
          )}
          {...getCombinationsTypesColumns()}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
        />
      </Container>
      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Loaders"
        content={filterContentLoader()}
      />
    </>
  );
}
