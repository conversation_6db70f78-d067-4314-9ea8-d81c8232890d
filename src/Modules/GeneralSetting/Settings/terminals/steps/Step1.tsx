import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';

import { Grid, TextField, Typography } from '@mui/material';
import { Box } from '@mui/system';
import { Controller } from 'react-hook-form';

export default function TerminalsStep1({ form }) {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 5 }}>
        Terminal Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.name?.message.length > 0 ? true : false}
            id="name"
            label="Name"
            fullWidth
            variant="outlined"
            {...form.register('name')}
            helperText={form.errors.name?.message}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="location_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Location"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'locations'}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
