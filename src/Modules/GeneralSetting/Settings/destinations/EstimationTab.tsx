import { Box } from '@mui/system';

import React, { useEffect, useState } from 'react';
import {
  Autocomplete,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Collapse,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import { destinationEstimationSchema } from '@/configs/general_setting/settings/destinationHeader';
import { zodResolver } from '@hookform/resolvers/zod';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import EditIcon from '@mui/icons-material/Edit';

const EstimationTab = ({ data, activeTab }) => {
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState('');
  const theme = useTheme();
  const [locations, setLocations] = useState([]);
  const [estimations, setEstimations] = useState([]);
  const [updateId, setUpdateId] = useState(null);
  const fetchItems = async () => {
    try {
      const res = await axios.get('locations/getAll');
      setLocations(res.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchEstimations = async () => {
    try {
      setLoading('fetchEstimations');
      const res = await axios.get('estimations', {
        params: {
          destination_id: data.id,
        },
      });
      setLoading('');
      setEstimations(res.data);
    } catch (error) {
      console.error(error);
    }
  };
  const { handleSubmit, reset, clearErrors, control, setValue } = useForm({
    mode: 'onChange',
    resolver: zodResolver(destinationEstimationSchema),
    defaultValues: {
      location_id: 0,
      average: 0,
    },
  });

  const closeForm = () => {
    reset();
    clearErrors();
    setShowForm(false);
    setUpdateId(null);
  };

  const submitForm = async (values) => {
    try {
      setLoading('adding');
      if (updateId) {
        await axios.patch('estimations', { ...values, id: updateId });
        toast.success('Estimation updated successfully!');
        closeForm();
      } else {
        await axios.post('estimations', { ...values, destination_id: data.id });
        toast.success('Estimation added successfully!');
      }
      setUpdateId(null);
      fetchEstimations();
      reset();
      clearErrors();
    } catch (error) {
      if (error.response) {
        toast.error(error.response?.data?.message);
      } else {
        toast.error('Something went wrong!');
      }
    }
    setLoading('');
  };

  const updateEstimation = async (data) => {
    try {
      setUpdateId(data.id);
      setValue('location_id', data.location_id);
      setValue('average', data.average);
      setShowForm(true);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (activeTab == 'estimation') fetchEstimations();
    fetchItems();
  }, [activeTab]);

  return (
    <Box>
      <Box>
        <Collapse in={showForm} timeout="auto" unmountOnExit>
          <form onSubmit={handleSubmit(submitForm)}>
            <Card
              sx={{
                my: 1,
                backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
              }}
            >
              <CardContent>
                <Box>
                  <Typography variant="h6">
                    {' '}
                    {updateId ? 'Update Estimation' : 'Add Estimation'}
                  </Typography>
                  <Grid container spacing={2} sx={{ py: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 6,
                      }}
                    >
                      <Controller
                        name={`location_id`}
                        control={control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          let selectedLocation = locations.find(
                            (row) => row.id == field.value,
                          );
                          if (selectedLocation == undefined)
                            selectedLocation = null;
                          return (
                            <Autocomplete
                              disabled={updateId ? true : false}
                              size="small"
                              value={selectedLocation}
                              getOptionLabel={(option) =>
                                option?.name ? option?.name : ''
                              }
                              onChange={(_event, value) => {
                                field.onChange(value?.id ?? 0);
                              }}
                              options={locations}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Location"
                                  error={invalid}
                                  helperText={error?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 6,
                      }}
                    >
                      <Controller
                        name="average"
                        control={control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="average"
                              type="number"
                              value={field.value}
                              label="Estimation"
                              fullWidth
                              variant="outlined"
                              onChange={(value) =>
                                field.onChange(+value.target.value)
                              }
                              helperText={error?.message}
                            />
                          );
                        }}
                      />
                    </Grid>
                  </Grid>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'end', pt: 1 }}>
                  <Button
                    variant="contained"
                    color="error"
                    size="small"
                    sx={{ mr: 1 }}
                    onClick={closeForm}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    type="submit"
                    loading={loading == 'adding'}
                  >
                    {updateId ? 'Update' : 'Submit'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </form>
        </Collapse>
      </Box>
      {!showForm && (
        <Box sx={{ display: 'flex', justifyContent: 'end', pb: 1 }}>
          <Box sx={{ display: 'flex' }}>
            {
              // perms?.includes(SHIPPING_RATES?.CREATE) &&
              <Button
                variant="contained"
                size="small"
                onClick={() => setShowForm(true)}
              >
                Add Estimation
              </Button>
            }
          </Box>
        </Box>
      )}
      <TableContainer
        sx={{
          height: '450px',
          '&::-webkit-scrollbar': {
            width: '10px',
            height: '10px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#6E6E6E',
            borderRadius: '20px',
          },
        }}
      >
        <Table aria-label="collapsible table" size="small">
          <TableHead
            sx={{
              backgroundColor:
                theme.palette.mode == 'dark' ? 'gray' : 'lightgray',
              color: 'white',
            }}
          >
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Estimation</TableCell>
              <TableCell sx={{ textAlign: 'end' }}>Edit</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading == 'fetchEstimations' ? (
              <TableRow>
                <TableCell align="center" colSpan={16}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : (
              estimations.map((item, index) => (
                <TableRow key={index}>
                  <TableCell scope="row">{item.id}</TableCell>
                  <TableCell scope="row">{item.locations?.name}</TableCell>
                  <TableCell scope="row">{item.average}</TableCell>
                  <TableCell scope="row" sx={{ textAlign: 'end' }}>
                    <AppTooltip title={'Edit'}>
                      <IconButton
                        color="success"
                        onClick={() => updateEstimation(item)}
                      >
                        <EditIcon />
                      </IconButton>
                    </AppTooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default EstimationTab;
