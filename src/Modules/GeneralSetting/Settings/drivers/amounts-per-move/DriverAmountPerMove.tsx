import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import { useContext, useEffect, useState } from 'react';
import UpdateAmountPerMoveEffectiveDate from './UpdateAmountPerMoveEffectiveDate';
import { Box, Button, IconButton } from '@mui/material';
import UpdateAmountPerMoveAmount from './UpdateAmountPerMoveAmount';
import CreateAmount from './CreateAmounts';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DRIVERS } from '@/configs/leftSideMenu/Permissions';
import { toast } from 'react-toastify';

const DriverAmountPerMove = ({ data, activeTab, setData }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [loading, setLoading] = useState(false);
  const [amounts, setAmounts] = useState([]);
  const [totalAmounts, setTotalAmounts] = useState([]);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: { column: 'effective_from', order: 'desc' },
  });

  const [dateType, setDateType] = useState('');
  const [updateDate, setUpdateDate] = useState(false);
  const [updateAmount, setUpdateAmount] = useState(false);
  const [selectedItemForUpdate, setSelectedItemForUpdate] = useState(null);
  const [showCreate, setShowCreate] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    if (activeTab == 'amount_per_move') {
      fetchAmounts();
    }
  }, [activeTab]);

  const fetchAmounts = async () => {
    try {
      setAmounts([]);
      setLoading(true);
      let res = await axios.get(`/drivers/amounts/${data.id}`, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalAmounts(res.data.total);
      const items = res.data.data;
      setAmounts(
        items.map((item, index) => {
          return {
            ...item,
            next_item: items[index + 1] ? { ...items[index + 1] } : null,
            prev_item: items[index - 1] ? { ...items[index - 1] } : null,
          };
        }),
      );
    } catch (error) {}
    setLoading(false);
  };

  const reduceOneDay = (param) => {
    if (!param) return null;
    const date = new Date(param);
    date.setDate(date.getDate() - 1);
    return date;
  };

  const fixEffectiveTo = (items) => {
    return items.map((item, index) => {
      return {
        ...item,
        effective_to: reduceOneDay(items[index - 1]?.effective_from) ?? null,
        next_item: items[index + 1]
          ? {
              ...items[index + 1],
              effective_to: reduceOneDay(items[index]?.effective_from) ?? null,
            }
          : null,
        prev_item: items[index - 1]
          ? {
              ...items[index - 1],
              effective_to:
                reduceOneDay(items[index - 2]?.effective_from) ?? null,
            }
          : null,
      };
    });
  };

  const deleteAmounts = async () => {
    if (await canUpdateOrDelete(selectedItems[0].id)) {
      try {
        const id = selectedItems[0].id;
        await axios.delete(`/drivers/amounts/${id}`);
        setAmounts((items) =>
          fixEffectiveTo(items.filter((item) => id != item.id)),
        );
        setSelectedItems([]);
      } catch (e) {}
    }
  };

  const canUpdateOrDelete = async (id) => {
    try {
      const res = await axios.get(`/drivers/amounts/can-update/${id}`);
      if (!res?.data?.canUpdate) {
        toast.error(
          'At least one payment has already been created with this amount, so it cannot be edited or removed!',
        );
      }
      return res?.data?.canUpdate;
    } catch (error) {
      return false;
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'end', my: 1 }}>
        {perms?.includes(DRIVERS?.DELETE) &&
        selectedItems.length == 1 &&
        selectedItems[0].index == 0 ? (
          <AppTooltip title={'Trash'}>
            <IconButton
              size="small"
              onClick={() => {
                setDeleteDialogOpen(true);
              }}
              sx={{ mr: 0.5 }}
            >
              <DeleteOutlinedIcon />
            </IconButton>
          </AppTooltip>
        ) : (
          <></>
        )}
        {perms?.includes(DRIVERS?.CREATE) && (
          <Button
            onClick={() => setShowCreate(true)}
            variant="contained"
            size="small"
          >
            Create
          </Button>
        )}
        <CreateAmount
          show={showCreate}
          setShow={setShowCreate}
          driver={data}
          setTableRecords={setAmounts}
          setData={setData}
          fixEffectiveTo={fixEffectiveTo}
        />
        <AppConfirmDialog
          maxWidth={'sm'}
          open={isDeleteDialogOpen}
          onDeny={setDeleteDialogOpen}
          onConfirm={() => {
            deleteAmounts();
            setDeleteDialogOpen(false);
          }}
          title={`Are you sure you want to delete ${selectedItems.length} Item(s)?`}
          dialogTitle={'Delete Driver amount per move Item'}
        />
      </Box>
      <DataTable3
        totalItems={totalAmounts}
        options={options}
        setOptions={setOptions}
        loading={loading}
        items={amounts.map((item, i) => {
          return { ...item, index: i };
        })}
        hideCheckBox={false}
        sortDisable={true}
        headers={[
          { id: 'id', label: 'ID', sortable: false },
          { id: 'amount', label: 'Amount', sortable: false },
          { id: 'effective_from', label: 'Effective from', sortable: false },
          { id: 'effective_to', label: 'Effective to', sortable: false },
          { id: 'created_at', label: 'Created at', sortable: false },
          { id: 'updated_at', label: 'Updated at', sortable: false },
          { id: 'created_by', label: 'Created by', sortable: false },
          { id: 'updated_by', label: 'Updated by', sortable: false },
        ]}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        tableName="drivers"
        amount={(item) => (
          <Box
            sx={{
              height: 20,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: item.index == 0 ? '#2196f3' : '',
            }}
            onClick={
              item.index == 0
                ? (e) => {
                    e.stopPropagation();
                    setSelectedItemForUpdate(item);
                    setUpdateAmount(true);
                  }
                : null
            }
          >
            {item?.amount > 0 && `$${item?.amount}`}
          </Box>
        )}
        effective_from={(item) => (
          <Box
            sx={{ cursor: 'pointer', color: item.index == 0 ? '#2196f3' : '' }}
            onClick={
              item.index == 0
                ? (e) => {
                    e.stopPropagation();
                    setSelectedItemForUpdate(item);
                    setUpdateDate(true);
                    setDateType('effective_from');
                  }
                : null
            }
          >
            {formatDate(item?.effective_from)}
          </Box>
        )}
        effective_to={(item) => {
          return <Box>{formatDate(item?.effective_to)}</Box>;
        }}
        created_at={(item) => (
          <Box>{item?.created_at ? formatDate(item?.created_at) : ''}</Box>
        )}
        updated_at={(item) => (
          <Box>{item?.updated_at ? formatDate(item?.updated_at) : ''}</Box>
        )}
        created_by={(item) => <Box>{item?.createdByUser?.fullname}</Box>}
        updated_by={(item) => <Box>{item?.updatedByUser?.fullname}</Box>}
      />
      <UpdateAmountPerMoveEffectiveDate
        canUpdate={canUpdateOrDelete}
        dateType={dateType}
        onDeny={setUpdateDate}
        open={updateDate}
        selectedItem={selectedItemForUpdate}
        setSelectedItem={setSelectedItemForUpdate}
        setTableRecords={setAmounts}
      />
      <UpdateAmountPerMoveAmount
        canUpdate={canUpdateOrDelete}
        onDeny={setUpdateAmount}
        open={updateAmount}
        selectedItem={selectedItemForUpdate}
        setSelectedItem={setSelectedItemForUpdate}
        setTableRecords={setAmounts}
      />
    </>
  );
};

export default DriverAmountPerMove;
