import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  IconButton,
  Modal,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { formFormatDate } from '@/configs/configs';
import dayjs from 'dayjs';
import { useState } from 'react';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';

const CreateAmount = ({
  show,
  setShow,
  driver,
  setTableRecords,
  setData,
  fixEffectiveTo,
}) => {
  const Root = styled('div')(({ theme }) => ({
    [theme.breakpoints.up('sm')]: { width: '500px' },
    [theme.breakpoints.down('sm')]: { width: '95%', flexWrap: 'wrap' },
  }));
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.up('md'));
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: ` ${theme.colors.alpha.white[100]}`,
    border: `2px solid ${theme.colors.alpha.black[50]}`,
    boxShadow: 24,
    borderRadius: '10px',
    px: 2,
    py: 1,
  };

  const schema = z.object({
    amount_per_move: z
      .number({ required_error: 'Amount per move is required' })
      .min(0),
    effective_from: z.coerce.date(),
  });

  const minDate = driver.amounts_per_move.length
    ? new Date(driver.amounts_per_move[0].effective_from)
    : null;
  if (minDate != null) {
    minDate.setDate(minDate.getDate() + 1);
  }
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: { amount_per_move: 0, effective_from: null },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
  };

  const [loading, setLoading] = useState(false);

  const submit = async (values) => {
    try {
      setLoading(true);
      const { data } = await axios.post(`/drivers/amounts`, {
        ...values,
        driver_id: driver.id,
      });
      if (data.result === true) {
        setTableRecords((tableRecords) => {
          const items = [data.data, ...tableRecords];
          return fixEffectiveTo(items);
        });
        setData((items) =>
          items.map((item) => {
            if (item.id == driver.id) {
              item.amounts_per_move.unshift(data.data);
            }
            return item;
          }),
        );
        setShow(false);
        reset();
        setLoading(false);
        toast.success('Record updated created!');
        return true;
      } else {
      }

      setShow(false);
      reset();
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  return (
    <>
      <Modal open={show}>
        <Root
          sx={
            !matches
              ? {
                  position: 'absolute' as 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  bgcolor: ` ${theme.colors.alpha.white[100]}`,
                  border: `2px solid ${theme.colors.alpha.black[50]}`,
                  boxShadow: 24,
                  borderRadius: '10px',
                  px: 2,
                  py: 1,
                }
              : style
          }
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: 'auto',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography
              variant="h5"
              sx={{ fontSize: '16px', fontWeight: 'bold' }}
            >
              Create Amount
            </Typography>
            <IconButton
              onClick={() => {
                setShow(false);
              }}
              aria-label="delete"
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Box sx={{ width: '100%', height: 'auto' }}>
            <Controller
              name="amount_per_move"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form?.errors?.amount_per_move?.message.length > 0
                      ? true
                      : false
                  }
                  id="amount_per_move"
                  type="number"
                  value={+field.value}
                  label="Amount per move"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(+value.target.value)}
                  ref={field?.ref}
                  helperText={error?.message}
                  sx={{ mb: 2, mt: 2 }}
                />
              )}
            />
            <Controller
              control={form.control}
              name="effective_from"
              render={({ field, fieldState: { error } }) => {
                return (
                  <DatePicker
                    views={['year', 'month', 'day']}
                    label="Effective from"
                    value={!field.value ? null : dayjs(field.value).toDate()}
                    format="yyyy/MM/dd"
                    onChange={(e) => {
                      field.onChange(formFormatDate(e));
                    }}
                    inputRef={field.ref}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !!error,
                        helperText: error?.message,
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                    sx={{ mb: 2 }}
                    minDate={minDate}
                  />
                );
              }}
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              alignItems: 'center',
              justifyContent: 'end',
              my: 1,
              gap: 1,
            }}
          >
            <Button
              loading={loading}
              variant="contained"
              onClick={handleSubmit(submit)}
            >
              Submit
            </Button>
          </Box>
        </Root>
      </Modal>
    </>
  );
};

export default CreateAmount;
