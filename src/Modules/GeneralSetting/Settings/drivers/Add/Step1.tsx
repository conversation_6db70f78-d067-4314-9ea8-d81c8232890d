import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  driver_status,
  driver_types,
  driverDocTypes,
} from '@/configs/general_setting/settings/driverHeader';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Input,
  TextField,
  Typography,
} from '@mui/material';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { useState } from 'react';
import { formatFileName } from '@/utils/imageUtils';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { formFormatDate } from '@/configs/configs';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';

const Step1 = ({ form, removeAttachment, deletedItems, image }) => {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'attachments',
  });

  const [deleteOpen, setDeleteOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState(null);

  const attachments = form.watch('attachments');

  const usedAttachmentTypes = attachments
    .map((item) => item.type)
    .filter((item) => item);

  const hasExpireDate = (label) => {
    const selectedTypeObj = driverDocTypes.find((item) => item.label === label);
    return selectedTypeObj?.expire_date ?? false;
  };

  return (
    <>
      <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
        <Typography variant="h5">Driver Details</Typography>
        {image}
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 8,
            }}
          >
            <Controller
              name="name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="name"
                  required
                  label="Driver Name"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.name?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <Controller
              name="status"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  defualtValue={driver_status[0]}
                  url={false}
                  label="Status"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={driver_status}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="email"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="email"
                  label="Email"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.email?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="phone"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  id="phone"
                  label="Phone"
                  fullWidth
                  variant="outlined"
                  value={field.value ?? ''}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  error={form.errors.phone?.message.length > 0 ? true : false}
                  helperText={error?.message}
                ></TextField>
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="type"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Driver type"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={driver_types}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="port_of_loading"
              control={form.control}
              render={({ field }) => {
                return (
                  <FilterAutocomplete
                    url={`/autoComplete?column=name&modal=locations&ids=`}
                    label="POL (Port Of Loading)"
                    name={'port_of_loading'}
                    keyName={'name'}
                    values={field.value}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                  />
                );
              }}
            />
          </Grid>
        </Grid>
        <Typography variant="h5" mt={2} mb={1}>
          Documents
        </Typography>

        {fields.map((item: any, i) => {
          const selectedType = form.watch(`attachments.${i}.type`);

          const items = driverDocTypes.filter(
            (el) =>
              !usedAttachmentTypes.includes(el.label) ||
              el.label === selectedType,
          );

          return (
            <Box
              sx={{
                py: 2,
                px: 2,
                pr: 6,
                mb: 1,
                border: '1px solid gray',
                borderRadius: '6px',
                position: 'relative',
                display: deletedItems.includes(item.document_id)
                  ? 'none'
                  : 'block',
              }}
              key={item.id}
            >
              <IconButton
                size="small"
                color="error"
                onClick={async () => {
                  if (item?.uploaded) {
                    setDeleteOpen(true);
                    setDeleteItem({
                      id: item?.document_id,
                      index: i,
                      filename: item.filename,
                    });
                  } else {
                    remove(i);
                  }
                }}
                aria-label="delete"
                sx={{ position: 'absolute', right: 10, top: 10 }}
              >
                <CloseIcon />
              </IconButton>
              <Controller
                name={`attachments.${i}.type`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Driver type"
                    fieldName=""
                    field={field}
                    error={error}
                    staticOptions={items.map((item) => item.label)}
                    column={''}
                    modal={''}
                    sx={{ mb: 1 }}
                  />
                )}
              />
              {hasExpireDate(selectedType) && (
                <Controller
                  name={`attachments.${i}.expire_date`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <DatePicker
                      views={['year', 'month', 'day']}
                      label="Expire Date"
                      value={!field.value ? null : dayjs(field.value).toDate()}
                      format="yyyy/MM/dd"
                      onChange={(e) => {
                        field.onChange(formFormatDate(e));
                      }}
                      inputRef={field.ref}
                      slotProps={{
                        textField: {
                          variant: 'outlined',
                          error: !!error,
                          helperText: error?.message,
                          size: 'small',
                          fullWidth: true,
                          sx: {
                            mb: 1,
                          },
                        },
                      }}
                    />
                  )}
                />
              )}
              <Controller
                name={`attachments.${i}.file`}
                control={form.control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <>
                      <label htmlFor={`fileInput${i}`} style={{ width: '95%' }}>
                        <Input
                          type="file"
                          onChange={(e) => {
                            const input = e.target as HTMLInputElement;
                            if (input.files) {
                              field.onChange(input.files[0]);
                            }
                          }}
                          id={`fileInput${i}`}
                          style={{ display: 'none' }}
                        />
                        <Button
                          component="span"
                          variant="contained"
                          color="primary"
                          style={{ width: '100%' }}
                          onClick={
                            item?.uploaded
                              ? (e) => {
                                  e.preventDefault();
                                }
                              : null
                          }
                        >
                          {item?.uploaded
                            ? formatFileName(item.filename)
                            : field?.value?.name
                              ? formatFileName(field?.value?.name)
                              : 'Choose File'}
                        </Button>
                      </label>
                      <Typography
                        mt={0.2}
                        textAlign={'start'}
                        mx={'14px'}
                        variant="body1"
                        fontSize={10}
                        color={'error'}
                      >
                        {error?.message}
                      </Typography>
                    </>
                  );
                }}
              />
            </Box>
          );
        })}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              append({ name: '' });
            }}
          >
            Add Document
          </Button>
        </Box>
      </Box>
      <AppConfirmDialog
        maxWidth={'sm'}
        open={deleteOpen}
        onDeny={() => {
          setDeleteOpen(false);
        }}
        onConfirm={async () => {
          await removeAttachment(deleteItem?.id);
          setDeleteOpen(false);
          setDeleteItem(null);
          // remove(deleteItem.index);
        }}
        title={`Are you sure you want to delete ${deleteItem?.filename}?`}
        dialogTitle={'Delete File'}
      />
    </>
  );
};

export default Step1;
