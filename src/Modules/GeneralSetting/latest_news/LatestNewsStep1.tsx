import { Box, Button, Input, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import React, { useState } from 'react';

import QuillEditor from '@/components/mainComponents/MuiEditor';

export default function LatestNewsStep1({ form }) {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState('');

  const handleUploadedFile = (event) => {
    const file = event.target.files[0];
    let urlImage: string;

    if (file) {
      urlImage = URL.createObjectURL(file);
      form.setValue('image', file);
    }

    setPreview(urlImage);

    if (file) {
      setSelectedFile(file);
    }
  };

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">News Details</Typography>

      <Box display="flex" justifyContent="space-between" sx={{ my: '1.5rem' }}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
          style={{ flex: '0 0 50%' }}
          gap="1.5rem"
        >
          <TextField
            size="small"
            error={form.errors.title?.message.length > 0}
            id="title"
            label="Title"
            fullWidth
            variant="outlined"
            {...form.register('title')}
            helperText={form.errors.title?.message}
          ></TextField>

          <TextField
            size="small"
            error={form.errors.category?.message.length > 0}
            id="category"
            label="Category"
            fullWidth
            variant="outlined"
            {...form.register('category')}
            helperText={form.errors.category?.message}
          ></TextField>

          <TextField
            size="small"
            error={form.errors.short_description?.message.length > 0}
            id="short_description"
            label="Short Description"
            fullWidth
            variant="outlined"
            {...form.register('short_description')}
            helperText={form.errors.short_description?.message}
          ></TextField>
        </Box>

        <Box
          display="flex"
          justifyContent="center"
          alignItems="end"
          flexDirection="column"
          gap="5px"
          sx={{ flex: '0 0 50%', width: '100%', height: 'auto' }}
        >
          <Box
            borderRadius="5px"
            style={{
              backgroundImage: `url(${
                preview
                  ? preview
                  : `${process.env['NEXT_PUBLIC_MINIO_ENDPOINT']}/${form.watch('image')}`
              })`,
              backgroundColor: '#CCCCCC',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              width: '95%',
              height: '80%',
            }}
          ></Box>

          <label htmlFor="fileInput" style={{ width: '95%' }}>
            <Input
              type="file"
              id="fileInput"
              style={{ display: 'none' }}
              onChange={handleUploadedFile}
            />
            <Button
              component="span"
              variant="contained"
              color="primary"
              style={{ width: '100%' }}
            >
              {selectedFile
                ? selectedFile.name.substring(0, 20)
                : 'Choose File'}
            </Button>
          </label>
        </Box>
      </Box>

      <Controller
        name="description"
        control={form.control}
        defaultValue=""
        render={({ field }) => (
          <QuillEditor
            onChange={(value) => {
              field.onChange(value);
            }}
            value={field.value}
            style={{ height: '350px' }}
            placeholder="Description"
            uploadImageUrl="/news/image"
          />
        )}
      />
    </Box>
  );
}
