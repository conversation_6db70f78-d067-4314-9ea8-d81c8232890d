import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  createSchema,
  fieldsObject,
  updateSchema,
} from '@/configs/general_setting/customerHeader';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';

import Step1 from './steps/Step1';

import CustomersHelper from './CustomerHelper';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';

export const CreateCustomers = ({
  show,
  setShow,
  // add,
  // update,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
}) => {
  const [isDone, setIsDone] = useState(false);
  const profileUrl = useRef<string>('');
  //@ts-ignore
  const [file, setFile] = useState();
  const customerHelper = new CustomersHelper();
  const exists = async (column, data, modal) => {
    let res = await customerHelper.exists([column, data], modal);
    return res;
  };
  const checkIsUpdate = () =>
    zodResolver(isUpdate ? updateSchema : createSchema);
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    getValues,
    setError,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: checkIsUpdate(),
    defaultValues: fieldsObject,
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    setValue,
    getValues,
  };

  // const submit = async (data) => {
  //   if (!isUpdate) {
  //     const create = add(data);
  //     if (await create) {
  //       setIsDone(true);
  //     } else {
  //       // error
  //     }
  //   } else {
  //     const edit = update(data);
  //     if (await edit) {
  //       setIsDone(true);
  //     } else {
  //       // error
  //     }
  //   }
  // };
  const [loadingButton, setLoadingButton] = useState(false);

  const submit = async (values) => {
    if (!isUpdate) {
      setLoadingButton(true);
      const { data } = await axios.post('/customers', values);
      if (data.result == true) {
        recordManager(data.data, 'add');
        setIsDone(true);
        toast.success('Done successfully!');
        setLoadingButton(false);

        return true;
      } else {
        setLoadingButton(false);

        return false;
      }
    } else {
      setLoadingButton(true);

      const { data } = await axios.patch(
        'customers/' + selectedItems[0].id,
        values,
      );
      if (data.result === true) {
        recordManager(data.data, 'update');
        setIsDone(true);
        toast.success('Record updated successfully!');
        setLoadingButton(false);
        return true;
      }
      setLoadingButton(false);
      return false;
    }
  };

  const steps = [
    {
      label: 'Customer Info',
      icon: <PeopleAltIcon />,
      step: <Step1 form={form} profileUrl={profileUrl} setFile={setFile} />,
      props: { isUpdate },
      async validate() {
        const isValid = await trigger([
          'username',
          'fullname',
          'email',
          'secondary_email',
          'phone',
          'secondary_phone',
          'password',
          'gender',
          'company_id',
          'status',
          'address',
          'bio',
          'photo',
          // 'note'
        ]);

        let emailExist = false;
        let secondaryEmailExist = false;
        let usernameExist = false;
        if (!isUpdate) {
          const { secondary_email, email, username } = getValues();

          // Create an array of validation checks
          const validationPromises = [];

          if (secondary_email) {
            validationPromises.push(
              exists('secondary_email', secondary_email, 'customers').then(
                (exists) => {
                  if (exists) {
                    setError('secondary_email', {
                      type: 'custom',
                      message:
                        'This secondary email already exists. Try another one!',
                    });
                  }
                },
              ),
            );
          }

          validationPromises.push(
            exists('email', email, 'loginables').then((exists) => {
              if (exists) {
                setError('email', {
                  type: 'custom',
                  message: 'This email already exists. Try another one!',
                });
              }
            }),
          );

          validationPromises.push(
            exists('username', username, 'loginables').then((exists) => {
              if (exists) {
                setError('username', {
                  type: 'custom',
                  message: 'This username already exists. Try another one!',
                });
              }
            }),
          );

          // Wait for all validations to complete
          await Promise.all(validationPromises);
        }

        if (usernameExist) {
          setError('username', {
            type: 'custom',
            message: 'This username is exist try another one!',
          });
        }
        return isValid && !emailExist && !secondaryEmailExist && !usernameExist;
      },
    },
  ];
  useEffect(() => {
    if (selectedItems) {
      setValue('username', selectedItems[0]?.loginable?.username);
      setValue('company_id', selectedItems[0]?.company_id);
      setValue('fullname', selectedItems[0]?.fullname);
      setValue('email', selectedItems[0]?.loginable?.email);
      setValue('password', selectedItems[0]?.password);
      // setValue('since_date', selectedItems[0]?.since_date);
      setValue('gender', selectedItems[0]?.gender);
      setValue('status', selectedItems[0]?.loginable?.status);
      setValue('phone', selectedItems[0]?.phone);
      setValue('secondary_phone', selectedItems[0]?.secondary_phone || null);
      setValue('secondary_email', selectedItems[0]?.secondary_email || null);
      // setValue('country', selectedItems[0]?.country);
      // setValue('company_city', selectedItems[0]?.companies?.company_city);
      // setValue('zip_code', selectedItems[0]?.zip_code);
      setValue('address', selectedItems[0]?.address);
      setValue('bio', selectedItems[0]?.bio);
      // setValue('note', selectedItems[0]?.note || null);
      setValue('photo', selectedItems[0]?.photo);
      // setValue('loading_instruction', selectedItems[0]?.loading_instruction);
    }
  }, [selectedItems]);
  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Customer' : 'Create Customer'}
        isUpdate={isUpdate}
      />
    </form>
  );
};
