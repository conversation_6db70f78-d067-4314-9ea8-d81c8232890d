import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  //@ts-ignore
  FormLabel,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import React, { useState } from 'react';
import Profile from '@/components/mainComponents/Cropper/Profile';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { Controller } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { customer_status } from '@/configs/general_setting/customerHeader';

export default function Step1({ form, profileUrl, setFile }) {
  const [showPassword, setShowPassword] = useState(false);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  return (
    <Grid container spacing={2}>
      <Grid sx={{ mt: 2, mb: 3 }} size={12}>
        <Profile
          width={{ xs: 80, md: 100 }}
          profileUrl={profileUrl}
          setFile={setFile}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>UserName</Box>
          }
          error={!!form.errors.username}
          id="outlined-error-username"
          {...form?.register('username')}
          helperText={form.errors.username?.message}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
              Customer name
            </Box>
          }
          error={!!form.errors.fullname}
          id="outlined-error-fullname"
          {...form.register('fullname')}
          helperText={form.errors.fullname?.message}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          type="email"
          label={<Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Email</Box>}
          error={!!form.errors.email}
          id="outlined-error-email"
          {...form.register('email')}
          helperText={form.errors.email?.message}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          type="email"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
              Secondary Email
            </Box>
          }
          error={!!form.errors.secondary_email}
          id="outlined-error-secondary_email"
          {...form.register('secondary_email')}
          helperText={form.errors.secondary_email?.message}
        />
      </Grid>
      <Grid size={12}>
        <FormControl variant="outlined" sx={{ width: '100%', mb: 2 }}>
          {/* <InputLabel htmlFor="password">Password</InputLabel> */}
          <OutlinedInput
            id="password"
            fullWidth
            placeholder="Password"
            {...form.register('password')}
            error={!!form.errors.password}
            aria-describedby="outlined-weight-helper-text"
            helperText={form.errors.password?.message}
            size="small"
            type={showPassword ? 'text' : 'password'}
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={() => setShowPassword((show) => !show)}
                  onMouseDown={handleMouseDownPassword}
                  edge="end"
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            }
          />
          {form.errors.password && (
            <FormHelperText sx={{ color: 'red' }}>
              {form.errors.password.message}
            </FormHelperText>
          )}
        </FormControl>
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          type="phone"
          label={<Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Phone</Box>}
          error={!!form.errors.phone}
          id="outlined-error-phone"
          {...form.register('phone')}
          helperText={form.errors.phone?.message}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          type="phone"
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
              Secondary Phone
            </Box>
          }
          error={!!form.errors.secondary_phone}
          id="outlined-error-secondary_phone"
          {...form.register('secondary_phone')}
          helperText={form.errors.secondary_phone?.message}
        />
      </Grid>
      {/* <Grid item xs={12} md={6}>
        <Controller
          control={form.control}
          name="since_date"
          render={({ field, fieldState: { error } }) => (
            <DatePicker
views={['year', 'month', 'day']}
              label="Since Date"
              value={!field.value ? null : dayjs(field.value).toDate()}
              format="yyyy/MM/dd"
              onChange={field.onChange}
              inputRef={field.ref}
              slotProps={{
                textField: {
                  variant: 'outlined',
                  error: !!error,
                  helperText: error?.message,
                  size: 'small',
                  fullWidth: true
                }
              }}
            />
          )}
        />
      </Grid> */}
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <Controller
          name="company_id"
          control={form.control}
          render={({ field, fieldState: { error } }) => (
            <AutoComplete
              url="autoComplete"
              label="Company"
              fieldName="name"
              field={field}
              error={error}
              staticOptions={false}
              column={'name'}
              modal={'companies'}
            />
          )}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <Controller
          name="status"
          control={form.control}
          render={({ field, fieldState: { error } }) => (
            <AutoComplete
              url={false}
              label="Status"
              fieldName=""
              field={field}
              error={error}
              staticOptions={customer_status}
              column={''}
              modal={''}
            />
          )}
        />
      </Grid>
      {/* <Grid item xs={12} md={6}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Country</Box>
          }
          error={!!form.errors.country}
          id="outlined-error"
          {...form.register('country')}
          helperText={form.errors.country?.message}
        />
      </Grid> */}
      {/* <Grid item xs={12} md={6}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Company City</Box>
          }
          error={!!form.errors.company_city}
          id="outlined-error"
          {...form.register('company_city')}
          helperText={form.errors.company_city?.message}
        />
      </Grid> */}
      {/* <Grid item xs={12} md={6}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Zip Code</Box>
          }
          error={!!form.errors.zip_code}
          id="outlined-error"
          {...form.register('zip_code')}
          helperText={form.errors.zip_code?.message}
        />
      </Grid> */}
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
              Company Street Address
            </Box>
          }
          error={!!form.errors.address}
          id="outlined-error-address"
          {...form.register('address')}
          helperText={form.errors.address?.message}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <FormControl component="fieldset" error={!!form.errors.gender}>
          <Controller
            rules={{ required: true }}
            control={form.control}
            name="gender"
            defaultValue="male"
            render={({ field }) => (
              <RadioGroup row {...field}>
                <FormControlLabel
                  value="male"
                  control={<Radio />}
                  label="Male"
                />

                <FormControlLabel
                  value="female"
                  control={<Radio />}
                  label="Female"
                />
              </RadioGroup>
            )}
          />
          {form.errors.gender && (
            <FormHelperText sx={{ color: 'red' }}>
              {form.errors.gender.message}
            </FormHelperText>
          )}
        </FormControl>
      </Grid>
      {/* <Grid item xs={12}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          multiline
          rows={4}
          label={<Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Note</Box>}
          error={!!form.errors.note}
          id="outlined-error"
          {...form.register('note')}
          helperText={form.errors.note?.message}
        />
      </Grid> */}
      <Grid size={12}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={<Box sx={{ fontSize: '14px', lineHeight: '1em' }}>Bio</Box>}
          error={!!form.errors.bio}
          id="outlined-error-bio"
          multiline
          rows={4}
          {...form.register('bio')}
          helperText={form.errors.bio?.message}
        />
      </Grid>
      {/* <Grid item xs={12} md={6}>
        <TextField
          sx={{ mb: 2 }}
          fullWidth
          size="small"
          label={
            <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
              Loading Instruction
            </Box>
          }
          error={!!form.errors.loading_instruction}
          id="outlined-error"
          {...form.register('loading_instruction')}
          helperText={form.errors.loading_instruction?.message}
        />
      </Grid> */}
    </Grid>
  );
}
