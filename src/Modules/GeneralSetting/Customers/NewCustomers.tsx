import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  Avatar,
  Box,
  Chip,
  CircularProgress,
  Container,
  IconButton,
  Tab,
  Tabs,
  Tooltip,
} from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { ANNOUNCEMENT, CUSTOMERS } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { copyORViewFun, recordManager } from '@/configs/configs';
import { CreateCustomers } from './CreateCustomers';
import SingleViewCustomer from './SingleViewCustomer';
import {
  filterContentCustomers,
  HeaderInfo,
} from '@/configs/general_setting/customerHeader';
import { removeUnderScore } from '@/configs/common';
import { formatDate } from '@/configs/vehicles/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import CampaignIcon from '@mui/icons-material/Campaign';
import { BroadCastAnnouncement } from '../announcements/BroadcastAnnouncement';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { toast } from 'react-toastify';
import LoginIcon from '@mui/icons-material/Login';

const NewCustomers = ({ apiUrl, defaultHeaders }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [showAnnouncement, setShowAnnouncement] = useState(false);

  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const pageName = 'customers';
  const [loading, setLoading] = useState(false);
  const [approveLoading, setApproveLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [loginLoading, setLoginLoading] = useState({});

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          // order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          // order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);
  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    // options.orderBy,
    options.filterData,
  ]);

  const approveCustomer = async () => {
    try {
      setApproveLoading(true);
      const ids = selectedItems.map((o) => o.id);
      const { data } = await axios.patch('customers/approveCustomer/' + ids);
      if (data?.result) {
        toast.success('Done successfully!');
        setApproveLoading(false);
      }
    } catch (err) {
      setApproveLoading(false);
      console.log('Error:', err);
    }
  };

  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  const choseColor = (status: any) => {
    switch (status) {
      case 'new_register':
        return '#e66e19';
      case 'active':
        return 'green';
      case 'deactive':
        return 'red';
      default:
        return 'green';
    }
  };

  const handleCustomerLogin = (id) => {
    if (loginLoading[id]) return;
    setLoginLoading((prev) => ({ ...prev, [id]: true }));
    const token = localStorage.getItem('token');
    const params = new URLSearchParams({
      id: id,
      token,
    });
    if (token) {
      window.open(
        `${process.env.NEXT_PUBLIC_CUSTOMER_PORTAL_URL || 'http://localhost:3000'}/en/auth/direct-login?${params}`,
        '_blank',
      );
    }
    toast.success('Successfully logged in as customer');
    setLoginLoading((prev) => ({ ...prev, [id]: false }));
  };

  return perms && !perms?.includes(CUSTOMERS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Customers List</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={'all'}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
        </Tabs>
        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Customers'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.fullname} customer ? `}
              dialogTitle={`Delete customer Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(CUSTOMERS?.CREATE)}
              showEditButton={perms?.includes(CUSTOMERS?.UPDATE)}
              showDeleteButton={perms?.includes(CUSTOMERS?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              customActionButtons={() => (
                <>
                  {perms?.includes(ANNOUNCEMENT.SEND_NOTIFICATION) && (
                    <AppTooltip
                      key={'announcement'}
                      title={'Send Announcement'}
                    >
                      <IconButton
                        color="primary"
                        onClick={() => {
                          setShowAnnouncement(true);
                        }}
                      >
                        <CampaignIcon />
                      </IconButton>
                    </AppTooltip>
                  )}
                  {selectedItems?.every(
                    (obj) => obj.loginable.status == 'new_register',
                  ) && (
                    <AppTooltip key={'approve'} title={'Approve Customer'}>
                      <IconButton
                        color="success"
                        size="small"
                        onClick={approveCustomer}
                      >
                        {approveLoading ? (
                          <CircularProgress color="success" size={20} />
                        ) : (
                          <CheckCircleOutlineIcon />
                        )}
                      </IconButton>
                    </AppTooltip>
                  )}
                </>
              )}
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="customer"
          // end default props
          //start custom props
          id={
            (item) =>
              copyORViewFun({
                getSingleRow,
                copy: item?.id,
                display: item?.id,
                id: item,
              })
            // <Box
            //   onClick={(e) => {
            //     e.stopPropagation();
            //     getSingleRow(item);
            //   }}
            //   sx={{
            //     textTransform: 'capitalize',
            //     cursor: 'pointer',
            //     color: '#2196f3',
            //     fontWeight: 'bold',
            //     whiteSpace: 'nowrap',
            //   }}
            // >
            //   {item.id}
            // </Box>
          }
          photo={({ photo }) => (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Avatar
                sx={{
                  height: 30,
                  width: 30,
                }}
                src={`${process.env.NEXT_PUBLIC_NEST_IMAGE_URL}/${photo}`}
              />
            </Box>
          )}
          company_name={({ companies }) => (
            <Box sx={{ whiteSpace: 'nowrap' }}>
              {companies?.name?.length > 20 ? (
                <Tooltip title={companies?.name}>
                  <Box component="span" sx={{ cursor: 'pointer' }}>
                    {companies?.name?.substring(0, 20)}...
                  </Box>
                </Tooltip>
              ) : (
                companies?.name
              )}
            </Box>
          )}
          status={({ loginable }) => (
            <Chip
              size="small"
              label={removeUnderScore(loginable.status)}
              sx={{
                backgroundColor: choseColor(loginable.status),
                color: 'white',
                textTransform: 'capitalize',
              }}
            />
          )}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          created_by={({ users_customers_created_byTousers }) => (
            <>
              {users_customers_created_byTousers?.fullname}
              {users_customers_created_byTousers?.departments?.name &&
                ' | ' + users_customers_created_byTousers?.departments?.name}
            </>
          )}
          updated_by={({ users_customers_updated_byTousers }) => (
            <>
              {users_customers_updated_byTousers?.fullname}
              {users_customers_updated_byTousers?.departments?.name &&
                ' | ' + users_customers_updated_byTousers?.departments?.name}
            </>
          )}
          email={({ loginable }) => loginable.email}
          login_link={({ id }) => (
            <Tooltip title="Login as this customer">
              <IconButton
                size="small"
                onClick={() => handleCustomerLogin(id)}
                sx={{
                  bgcolor: 'primary.main',
                  color: 'white',
                  width: 30,
                  height: 30,
                  '&:hover': { bgcolor: 'primary.dark' },
                  '&:disabled': { bgcolor: 'action.disabledBackground' },
                }}
                disabled={loginLoading[id]}
              >
                {loginLoading[id] ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <LoginIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          )}
        />
      </Container>

      <CreateCustomers
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        // add={(data) => onCreateCustomer(data)} // when add a new item this function will revoke that pop one of item then insert one
        // update={(data) => onEditCustomer(data)}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      <SingleViewCustomer data={viewData} setView={setView} show={view} />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Customers"
        content={filterContentCustomers}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
      ></ColumnDialog>

      <BroadCastAnnouncement
        setSelectedItems={setSelectedItems}
        show={showAnnouncement}
        setShow={setShowAnnouncement}
        selectedItems={selectedItems}
      />
      <PdfModal
        options={options}
        open={showDownload}
        pdf_title={'Customers'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        fetchDownloadRecords={fetchDownloadRecords}
        tableRecords={tableRecords}
        headers={selectedHeaders}
        //start custom props

        //start custom props

        photo={({ photo }) => {
          `${process.env.NEXT_PUBLIC_NEST_IMAGE_URL}/${photo}`;
        }}
        company_name={({ companies }) => companies?.name}
        status={({ loginable }) => removeUnderScore(loginable.status)}
        created_by={({ users_customers_created_byTousers }) =>
          users_customers_created_byTousers?.fullname
        }
        created_at={({ created_at }) => formatDate(created_at)}
        updated_at={({ updated_at }) => formatDate(updated_at)}
        updated_by={({ users_customers_updated_byTousers }) =>
          users_customers_updated_byTousers?.fullname
        }
        email={({ loginable }) => loginable.email}
      ></PdfModal>
    </>
  );
};

export default NewCustomers;
