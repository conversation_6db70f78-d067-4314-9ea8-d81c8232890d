import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { schema, updateSchema } from './UserHeader';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import StepOne from './Add/StepOne';
import StepTwo from './Add/StepTwo';
import UsersHelper from './UserHelper';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
export const CreateUsers = ({
  show,
  setShow,
  add,
  // @ts-ignore
  update,
  isUpdate,
  selectedItems,
  setSelectedItems,
}) => {
  const [isDone, setIsDone] = useState(false);
  const profileUrl = useRef<string>('');
  const [file, setFile] = useState<any>(false);
  const [roles, setRoles] = useState<any>();

  const [perms, setPerms] = useState<Array<number>>([]);

  const usersHelper = new UsersHelper();

  const exists = async (column, data, modal) => {
    let res = await usersHelper.exists([column, data], modal);
    return res;
  };
  const schemaOpt = isUpdate ? updateSchema : schema;
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    getValues,
    setError,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schemaOpt),
    defaultValues: {
      fullname: '',
      username: '',
      email: '',
      password: '',
      status: 'active',
      department_id: null,
      timezone: 'Asia/Kabul',
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    setValue,
    getValues,
    setError,
  };
  const [loadingButton, setLoadingButton] = useState(false);

  const submit = async (data) => {
    let formData = new FormData();
    formData.append('file', file!);
    formData.append('fullname', data?.fullname);
    formData.append('username', data?.username);
    formData.append('password', data?.password);
    formData.append('email', data?.email);
    formData.append('timezone', data?.timezone);
    formData.append('status', data?.status);
    formData.append('department_id', data?.department_id);
    formData.append('permissions', perms ? perms.join(',') : '');
    formData.append('roles', roles ? roles.join(',') : []);

    if (!isUpdate) {
      setLoadingButton(true);
      const create = await add(formData);
      if (await create) {
        setIsDone(true);
        setLoadingButton(false);
      } else {
        setLoadingButton(false);

        // error
      }
    } else {
      setLoadingButton(true);

      const edit = await update(formData);
      if (await edit) {
        setIsDone(true);
        setLoadingButton(false);
      } else {
        setLoadingButton(false);

        // error
      }
    }
  };
  const steps = [
    {
      label: 'User Info',
      icon: <PeopleAltIcon />,
      step: <StepOne form={form} profileUrl={profileUrl} setFile={setFile} />,
      props: { isUpdate },
      async validate() {
        if (isUpdate) {
          const isValid = await trigger([
            'fullname',
            'username',
            'email',
            'timezone',
            'status',
            'department_id',
          ]);
          return isValid;
        } else {
          let emailExist = false;
          let usernameExist = false;
          usernameExist = await exists(
            'username',
            getValues('username'),
            'loginables',
          );
          emailExist = await exists('email', getValues('email'), 'loginables');
          const isValid = await trigger([
            'fullname',
            'username',
            'email',
            'timezone',
            'password',
            'status',
            'department_id',
          ]);
          if (emailExist) {
            setError('email', {
              type: 'custom',
              message: 'This email is exist try another one!',
            });
          } else if (usernameExist) {
            setError('username', {
              type: 'custom',
              message: 'This username is exist try another one!',
            });
          }

          return isValid && !emailExist && !usernameExist;
        }
      },
    },
    {
      label: 'Roles & Permissions',
      icon: <PrivacyTipIcon />,
      step: (
        <StepTwo
          perms={perms}
          setPerms={setPerms}
          haveRoles={isUpdate ? selectedItems[0]?.loginable?.roles : false}
          setRoles={setRoles}
        />
      ),
      props: { isUpdate },
      async validate() {
        const isValid = await trigger([]);
        return isValid;
      },
    },
  ];
  useEffect(() => {
    if (isUpdate || selectedItems) {
      setValue('username', selectedItems[0]?.loginable?.username);
      setValue('fullname', selectedItems[0]?.fullname);
      setValue('email', selectedItems[0]?.loginable?.email);
      setValue('timezone', selectedItems[0]?.loginable?.timezone);
      setValue('status', selectedItems[0]?.loginable?.status);
      setValue('department_id', selectedItems[0]?.department_id);
      setValue('password', null);
      setRoles(selectedItems[0]?.loginable?.roles.map((o) => o.id));
      profileUrl.current = `${process.env.NEXT_PUBLIC_NEST_IMAGE_URL}/${selectedItems[0]?.photo}`;
      setPerms(selectedItems[0]?.loginable?.permissions?.map((o) => o.id));
    } else {
      setPerms([]);
      setRoles([]);
    }
  }, [selectedItems]);
  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update User' : 'Create User'}
        isUpdate={isUpdate}
      />
    </form>
  );
};
