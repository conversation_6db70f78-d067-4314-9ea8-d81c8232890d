import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material';

import { useGet } from '@/services/api_services';
import React from 'react';
import { removeUnderScore2 } from '@/configs/common';

const StepTwo = ({ perms, setPerms, haveRoles, setRoles }) => {
  const [roleNames, setRoleNames] = React.useState<string[]>(
    haveRoles ? haveRoles.map((r) => r.name) : [],
  );

  const { data: permData } = useGet('permissions/all');
  //@ts-ignore
  const { data: roles, isLoading: rolesLoad } = useGet('roles');

  const handleChange = (event: SelectChangeEvent<typeof roleNames>) => {
    const {
      target: { value },
    } = event;
    setRoles(
      roles?.data.filter((o) => value.includes(o.name)).map((o) => o.id),
    );
    setRoleNames(typeof value === 'string' ? value.split(',') : value);
  };

  const permissions = permData?.data?.reduce((acc, obj) => {
    const groupName = obj.group_name;
    if (!acc[groupName]) {
      acc[groupName] = [];
    }
    acc[groupName].push({ id: obj.id, name: obj.name, label: obj.label });
    return acc;
  }, {});

  const checkPermissions = (
    e: React.ChangeEvent<HTMLInputElement>,
    all: boolean = false,
  ) => {
    if (all) {
      setPerms(e.target.checked ? permData?.data?.map((o) => o.id) : []);
    } else {
      let perm: Array<number> = perms == undefined ? [] : perms;
      const value = +e.target.value;
      e.target.checked
        ? perm.push(value)
        : (perm = perm.filter((item) => item !== value));
      setPerms([...perm]);
    }
  };

  const checkCategory = (e: React.ChangeEvent<HTMLInputElement>) => {
    const categorySelected = permissions[e.target.value].map((o) => o.id);
    let perm: Array<number> = perms;
    e.target.checked
      ? (perm = [...(perm || []), ...categorySelected])
      : (perm = perm.filter((item) => !categorySelected.includes(item)));
    setPerms([...perm]);
  };

  return (
    <Box sx={{ mx: 3, textAlign: 'center' }}>
      <Typography variant="h5">Roles & Permissions</Typography>
      {/* <Stack>
        {Array.isArray(errors)
          ? errors.map((err: string, i) => (
              <Alert key={i} severity="error">
                {err}
              </Alert>
            ))
          : errors && <Alert severity="error">{errors}</Alert>}
      </Stack> */}

      <FormControl sx={{ my: 2 }} fullWidth size="small">
        <InputLabel id="roles_label">Roles</InputLabel>
        <Select<string[]>
          labelId="roles_label"
          id="demo-multiple-checkbox"
          multiple
          value={roleNames}
          onChange={handleChange}
          input={<OutlinedInput label="roles" />}
          renderValue={(selected) => selected.join(', ')}
          MenuProps={{
            PaperProps: { style: { maxHeight: 45 * 4.5 + 5, width: 250 } },
          }}
        >
          {roles?.data?.map((r, i) => (
            <MenuItem key={i} value={r.name}>
              <Checkbox checked={roleNames.indexOf(r.name) > -1} />
              <ListItemText primary={r.name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Paper>
        <Stack
          mx={2}
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    perms?.length > 0 && permData?.data?.length == perms?.length
                      ? true
                      : false
                  }
                  indeterminate={
                    permData?.data?.length !== perms?.length
                      ? perms?.length > 0
                      : false
                  }
                  onChange={(e) => checkPermissions(e, true)}
                />
              }
              label="All"
            />
          </Box>
          <Typography>Permissions</Typography>
        </Stack>
      </Paper>
      <Paper sx={{ px: 1, py: 1, mt: 2 }}>
        {permissions && (
          <Stack direction={{ xs: 'column' }} sx={{ flexWrap: 'wrap' }}>
            {Object.entries(permissions)?.map(([name], i) => (
              <Box key={i}>
                <Stack direction="row">
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={permissions[name]
                          .map((o) => o.id)
                          .every((item) => perms?.includes(item))}
                        indeterminate={
                          !permissions[name]
                            .map((o) => o.id)
                            .every((item) => perms?.includes(item))
                            ? permissions[name]
                                .map((o) => o.id)
                                .some((item) => perms?.includes(item))
                            : false
                        }
                        value={name}
                        onChange={checkCategory}
                      />
                    }
                    label={
                      <Typography
                        sx={{ textTransform: 'capitalize', fontWeight: 'bold' }}
                      >
                        {removeUnderScore2(name)}
                      </Typography>
                    }
                  ></FormControlLabel>
                </Stack>
                <Paper
                  variant="outlined"
                  square
                  sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    pl: 3,
                    flexWrap: 'wrap',
                    my: 1,
                  }}
                >
                  {permissions[name].map((p, i) => (
                    <FormControlLabel
                      sx={{ mr: 2 }}
                      key={i}
                      label={
                        <Typography sx={{ textTransform: 'capitalize' }}>
                          {removeUnderScore2(p.label)}
                        </Typography>
                      }
                      control={
                        <Checkbox
                          size="small"
                          name="permissions"
                          value={parseInt(p.id)}
                          checked={
                            perms?.length > 0 ? perms?.includes(p.id) : null
                          }
                          onChange={(e) => checkPermissions(e)}
                        />
                      }
                    />
                  ))}
                </Paper>
              </Box>
            ))}
          </Stack>
        )}
      </Paper>
    </Box>
  );
};

export default StepTwo;
