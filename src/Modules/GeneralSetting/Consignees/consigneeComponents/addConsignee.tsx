import React, { useState } from 'react';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  Box,
  Grid,
  TextField,
  Typography,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import axios from '@/lib/axios';

interface AddConsigneesProps {
  form: {
    register: any;
    control: any;
    formState: any;
    setValue: any;
    clearErrors: any;
    setError: any;
    getValues: any;
    watch: any;
  };
}

export default function AddConsignees({ form }: AddConsigneesProps) {
  const {
    register,
    control,
    formState: { errors },
  } = form;

  const [isDisabled] = useState(false); // <PERSON><PERSON>'s accepted change

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Consignee Info
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="company_id"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                sx={{ my: 1.5 }}
                url="autoComplete"
                label="Select Company"
                fieldName="name"
                field={{
                  ...field,
                  onChange: async (value) => {
                    field.onChange(value);
                  },
                }}
                error={error}
                staticOptions={false}
                column="name"
                modal="companies"
              />
            )}
          />
        </Grid>

        {[
          { name: 'consignee', label: 'Consignee Name' },
          { name: 'consignee_street', label: 'Consignee Street' },
          { name: 'consignee_box', label: 'Consignee Box' },
          { name: 'consignee_city', label: 'Consignee City' },
          { name: 'consignee_zip_code', label: 'Consignee Zip Code' },
          { name: 'consignee_country', label: 'Consignee Country' },
          { name: 'consignee_phone', label: 'Consignee Phone' },
          { name: 'consignee_email', label: 'Consignee Email' },
          { name: 'consignee_fax', label: 'Consignee Fax' },
          { name: 'consignee_poc', label: 'Consignee Point of Contact' },
        ].map((fieldData) => (
          <Grid
            key={fieldData.name}
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              fullWidth
              size="small"
              label={fieldData.label}
              error={!!errors[fieldData.name]}
              id="outlined-error"
              {...register(fieldData.name)}
              helperText={errors[fieldData.name]?.message as string}
              disabled={isDisabled}
            />
          </Grid>
        ))}

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="default_consignee"
            control={control}
            render={({ field }) => {
              const error = form.formState.errors?.default_consignee;

              const handleCheckboxChange = async (
                e: React.ChangeEvent<HTMLInputElement>,
              ) => {
                const isChecked = e.target.checked;
                const companyId = form.getValues('company_id');

                form.setValue('default_consignee', isChecked);

                if (isChecked && companyId) {
                  try {
                    const { data } = await axios.get('/consignees', {
                      params: {
                        filterData: JSON.stringify({ company_id: companyId }),
                      },
                    });

                    const hasDefault = data?.data?.some(
                      (consignee: any) => consignee.default_consignee,
                    );

                    if (hasDefault) {
                      form.setError('default_consignee', {
                        type: 'manual',
                        message:
                          'This company already has a default consignee.',
                      });
                    } else {
                      form.clearErrors('default_consignee');
                    }
                  } catch (err) {
                    console.error('Error checking default consignee:', err);
                  }
                }

                if (!isChecked) {
                  form.clearErrors('default_consignee');
                }
              };

              return (
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        checked={!!form.watch('default_consignee')}
                        disabled={isDisabled}
                        onChange={handleCheckboxChange}
                        color="primary"
                      />
                    }
                    label="Default Consignee"
                  />
                  {error && (
                    <Typography variant="caption" color="error">
                      {error.message as string}
                    </Typography>
                  )}
                </Box>
              );
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
