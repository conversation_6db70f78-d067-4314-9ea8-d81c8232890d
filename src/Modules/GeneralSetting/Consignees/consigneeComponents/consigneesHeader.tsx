import { contextProvider } from '@/contexts/ProfileContext';
import BusinessIcon from '@mui/icons-material/Business';
import { useContext } from 'react';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../../../../configs/leftSideMenu/Permissions';
import { z } from 'zod';

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext || {}; // Add null check
  const perms = permittedMenu(profile?.data); // Optional chaining

  let breadcrumbs = [
    {
      href: 'false',
      name: 'Consignees',
      icon: <BusinessIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    // Optional chaining
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const schema = z.object({
  consignee: z.string().min(1, 'Consignee name is required'),
  consignee_country: z.string().min(1, 'Country is required'),
  consignee_city: z.string().min(1, 'City is required'),
  consignee_zip_code: z.string().nullable().optional(),
  consignee_street: z.string().min(1, 'Street is required'),
  consignee_box: z.string().nullable().optional(),
  consignee_phone: z.string().min(1, 'Phone is required'),
  consignee_email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required'),
  consignee_fax: z.string().nullable().optional(),
  consignee_poc: z.string().nullable().optional(),
  default_consignee: z.boolean(),
  company_id: z.number().int(),
});

export const filterContentConsignees = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'deleted_by',
        label: 'Deleted By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&id=',
        keyName: 'fullname',
      },
      {
        name: 'company_id',
        label: 'Company',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=companies&id=',
        keyName: 'name',
      },
    ],
  },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
    ],
  },
];

export const companyConsigneesHeader = [
  {
    id: 'id',
    label: 'ID',
    align: 'right',
  },
  {
    id: 'company',
    label: 'Company',
  },
  {
    id: 'consignee',
    label: 'Consignee Name',
  },
  {
    id: 'consignee_country',
    label: 'Country',
  },
  {
    id: 'consignee_city',
    label: 'City',
  },
  {
    id: 'consignee_zip_code',
    label: 'Zip Code',
  },
  {
    id: 'consignee_street',
    label: 'Street',
  },
  {
    id: 'consignee_box',
    label: 'Box',
  },
  {
    id: 'consignee_phone',
    label: 'Phone',
  },
  {
    id: 'consignee_email',
    label: 'Email',
  },
  {
    id: 'consignee_fax',
    label: 'Fax',
  },
  {
    id: 'consignee_poc',
    label: 'POC',
  },
  {
    id: 'default_consignee',
    label: 'Default?',
    align: 'center',
  },
  {
    id: 'created_at',
    label: 'Created At',
  },
  {
    id: 'created_by',
    label: 'Created By',
  },
  {
    id: 'updated_at',
    label: 'Updated At',
  },
  {
    id: 'updated_by',
    label: 'Updated By',
  },
];
