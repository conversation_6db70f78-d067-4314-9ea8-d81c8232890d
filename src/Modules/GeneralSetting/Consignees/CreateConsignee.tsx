import CStepper from '@/components/mainComponents/stepper/CStepper';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import InfoIcon from '@mui/icons-material/Info';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import { schema } from './consigneeComponents/consigneesHeader';
import AddConsignees from './consigneeComponents/addConsignee';

export const CreateConsignee = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
  setReload = undefined,
}) => {
  const [loadingButton, setLoadingButton] = useState(false);
  const [isDone, setIsDone] = useState(false);

  const {
    register,
    handleSubmit,
    formState,
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    getValues,
    setError,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      company_id: '',
      consignee: '',
      consignee_country: '',
      consignee_city: '',
      consignee_zip_code: '',
      consignee_street: '',
      consignee_box: '',
      consignee_phone: '',
      consignee_email: '',
      consignee_fax: '',
      consignee_poc: '',
      default_consignee: false,
    },
  });
  const form = {
    register,
    control,
    setValue,
    reset,
    setError,
    clearErrors,
    formState,
    watch,
    getValues,
    isUpdate,
    trigger,
  };
  // When editing, populate the form with selected item data
  useEffect(() => {
    if (isUpdate && selectedItems) {
      reset(selectedItems);
    } else {
      reset({
        company_id: '',
        consignee: '',
        consignee_country: '',
        consignee_city: '',
        consignee_zip_code: '',
        consignee_street: '',
        consignee_box: '',
        consignee_phone: '',
        consignee_email: '',
        consignee_fax: '',
        consignee_poc: '',
        default_consignee: false,
      });
    }
  }, [isUpdate, selectedItems, reset]);

  const submit = async (values) => {
    setLoadingButton(true);
    try {
      let response;
      if (isUpdate) {
        response = await axios.patch(`/consignees/${selectedItems.id}`, values);
        toast.success('Consignee updated successfully');
      } else {
        response = await axios.post('/consignees', values);
        toast.success('Consignee created successfully');
      }
      const savedConsignee = response.data.data || response.data;

      recordManager(savedConsignee, isUpdate ? 'update' : 'add');

      if (typeof setReload === 'function') {
        setReload((prev) => prev + 1);
      }

      setShow(false);
      setIsDone(true);
    } catch (error) {
      toast.error('Error while saving consignee');
      console.error(error);
    }
    setLoadingButton(false);
  };

  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: <AddConsignees form={form} />,
      async validate() {
        const valid = await trigger();
        const hasError = !!form.formState.errors.default_consignee;
        return valid && !hasError;
      },
    },
  ];

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Consignee' : 'Create Consignee'}
        isUpdate={isUpdate}
      />
    </form>
  );
};

export default CreateConsignee;
