import { Box, Container, Typography } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { COMPANY_CONSIGNEES } from '@/configs/leftSideMenu/Permissions';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import axios from '@/lib/axios';
import { applySavedColumns } from '@/utils/columnUtils';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { formatDate } from '@/configs/vehicles/configs';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import { copyORViewFun, recordManager } from '@/configs/configs';
import {
  HeaderInfo,
  filterContentConsignees,
} from './consigneeComponents/consigneesHeader';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import CreateConsignee from './CreateConsignee';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';

interface ConsigneesProps {
  apiUrl: string;
  defaultHeaders: any[];
}

const Consignees = ({ apiUrl, defaultHeaders }: ConsigneesProps) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [_selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });

  const pageName = 'companies consignees';
  const [loading, setLoading] = useState(false);

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    // options.orderBy,
    options.filterData,
  ]);

  return perms && !perms?.includes(COMPANY_CONSIGNEES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Company Consignees</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Company Consignees'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={false}
              onCustomizeColumn={() => setOpenFilter(true)}
              deleteTitle={`Are you sure to delete consignee ${selectedItems[0]?.id}?`}
              dialogTitle={`Delete Consignee Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showSummeryDownloadButton={false}
              showAddButton={perms?.includes(COMPANY_CONSIGNEES?.CREATE)}
              showEditButton={perms?.includes(COMPANY_CONSIGNEES?.UPDATE)}
              showDeleteButton={perms?.includes(COMPANY_CONSIGNEES?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={Object.keys(options.filterData).length <= 0}
            />
          }
          // start default props
          tableName="company_consignees"
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          // end default props
          // start custom props
          id={(item) =>
            copyORViewFun({
              getSingleRow: () => {},
              copy: item?.id,
              display: item?.id,
              id: item,
            })
          }
          company={({ company }) => company?.name}
          created_by={({ createdByUser }) => createdByUser?.fullname}
          updated_by={({ updatedByUser }) => updatedByUser?.fullname}
          deleted_by={({ deletedByUser }) => deletedByUser?.fullname}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          default_consignee={({ default_consignee }) => (
            <Typography variant="body2" noWrap>
              {default_consignee ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
        />

        <CreateConsignee
          setSelectedItems={setSelectedItems}
          show={showCreate}
          setShow={setShowCreate}
          selectedItems={selectedItems[0]}
          isUpdate={isUpdate}
          recordManager={(data, type) => {
            recordManager({
              data,
              type,
              setTableRecords,
              tableRecords,
              selectedItems,
              setSelectedItems,
              setTotalItems,
              totalItems,
              apiUrl,
            });
          }}
        />

        <FilterModal2
          open={openFilter}
          toggleOpen={() => setOpenFilter((d) => !d)}
          options={options}
          setOptions={setOptions}
          title="Filter Consignees"
          content={filterContentConsignees}
        />

        <PdfModal
          options={options}
          open={showDownload}
          pdf_title={'Companies'}
          selectedHeaders={selectedHeaders}
          setShowDownload={setShowDownload}
          fetchDownloadRecords={fetchDownloadRecords}
          tableRecords={tableRecords}
          headers={selectedHeaders}
          created_by={({ createdByUser }) => createdByUser?.fullname}
          updated_by={({ updatedByUser }) => updatedByUser?.fullname}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
        ></PdfModal>
      </Container>
    </>
  );
};

export default Consignees;
