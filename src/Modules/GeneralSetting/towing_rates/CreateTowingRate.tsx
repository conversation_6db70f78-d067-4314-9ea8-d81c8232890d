import CStepper from '@/components/mainComponents/stepper/CStepper';
import { towingRateSchema } from '@/configs/general_setting/towingRatesHeader';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import InfoIcon from '@mui/icons-material/Info';
import Step1 from './Add/step1';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

export const CreateTowingRate = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
}) => {
  const [loadingButton, setLoadingButton] = useState(false);
  const [isDone, setIsDone] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    getValues,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zod<PERSON><PERSON><PERSON>ver(towingRateSchema),
    defaultValues: {
      towing: null,
      location_id: null,
      loading_city_id: null,
      stateId: null,
      branchId: null,
      half_location_id: null,
      half_rate: null,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    getValues,
    setValue,
  };

  const checkForDuplicate = async () => {
    const values = getValues();
    try {
      const response = await axios.post('/towing-rates/check-duplicate', {
        location_id: values.location_id,
        loading_city_id: values.loading_city_id,
        half_location_id: values.half_location_id,
      });

      const { isDuplicate, isDeleted, existingRate, message } = response.data;

      if (isDuplicate) {
        if (isDeleted) {
          toast.error(
            <div>
              {message} (ID: {existingRate.id})
              <br />
            </div>,
            {
              autoClose: false, // Keep the toast open until manually closed
              closeButton: true,
            },
          );
        } else {
          toast.error(
            `A rate already exists for this location and city combination (ID: ${existingRate.id})`,
          );
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      toast.error('Error checking for duplicate rates');
      return false;
    }
  };

  const submit = async (values) => {
    if (!isUpdate) {
      try {
        setLoadingButton(true);

        // Check for duplicates before submitting
        const isDuplicate = await checkForDuplicate();
        if (isDuplicate) {
          setLoadingButton(false);
          return false;
        }

        const { data } = await axios.post('/towing-rates', values);
        if (data.result == true) {
          recordManager(data.data, 'add');
          setIsDone(true);
          toast.success('Done successfully!');
          setLoadingButton(false);
          return true;
        } else {
          setLoadingButton(false);
          return false;
        }
      } catch (err) {
        setLoadingButton(false);
        console.log(err);
      }
    } else {
      try {
        setLoadingButton(true);
        const { data } = await axios.patch(
          `towing-rates/${selectedItems.id}`,
          values,
        );
        if (data.result === true) {
          recordManager(data.data, 'update');
          setIsDone(true);
          toast.success('Record updated successfully!');
          setLoadingButton(false);
          return true;
        }
        setLoadingButton(false);
        return false;
      } catch (err) {
        setLoadingButton(false);
        console.log(err);
      }
    }
  };

  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: <Step1 form={form} selectedItem={selectedItems} />,
      props: isUpdate,
      async validate() {
        const isValid = await trigger([
          'towing',
          'location_id',
          'loading_city_id',
          'branchId',
          'stateId',
          'half_location_id',
          'half_rate',
        ]);
        return isValid;
      },
    },
  ];

  useEffect(() => {
    if (selectedItems) {
      setValue('location_id', selectedItems?.location_id);
      setValue('towing', selectedItems?.towing);
      setValue('half_location_id', selectedItems?.half_location_id);
      setValue('half_rate', selectedItems?.half_rate);
    }
  }, [selectedItems]);

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Towing Rate' : 'Create Towing Rate'}
        isUpdate={isUpdate}
      />
    </form>
  );
};

export default CreateTowingRate;
