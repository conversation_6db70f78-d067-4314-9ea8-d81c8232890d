import React, { useContext, useEffect, useState } from 'react';
import { Box, Container, Tabs, Tab } from '@mui/material';
import Head from 'next/head';
import axios from '@/lib/axios';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { TOWING_RATES } from '@/configs/leftSideMenu/Permissions';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
import { formatDate } from '@/configs/vehicles/configs';
import { HeaderInfo } from '@/configs/general_setting/towingRatesHeader';
import { toast } from 'react-toastify';
import { useCustomColumnTowingRates } from './CustomColumnTowingRates';

const TowingRateTrash = ({ apiUrl, defaultHeaders = [] }) => {
  const { profile } = useContext(contextProvider);
  const perms = permittedMenu(profile?.data);
  const customColumns = useCustomColumnTowingRates();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);

  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });

  const fetchData = async () => {
    try {
      if (cancelToken) cancelToken.abort();

      const controller = new AbortController();
      setCancelToken(controller);
      setLoading(true);
      setData([]);

      const params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };

      const { data } = await axios.get(`${apiUrl}/trash`, {
        signal: controller.signal,
        params,
      });

      setData(data.data);
      setTotalItems(data.total);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching data:', error);
      setLoading(false);
      toast.error('Failed to fetch data from trash');
    }
  };

  useEffect(() => {
    fetchData();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
  ]);

  const handleRestore = async () => {
    if (selectedItems.length === 0) {
      toast.warning('Please select at least one item to restore');
      return;
    }

    const ids = selectedItems.map((item) => item.id);
    try {
      setLoading(true);
      const response = await axios.post(`${apiUrl}/restore`, { ids });

      if (response?.data?.success) {
        const updated = data.filter((row) => !ids.includes(row.id));
        setData(updated);
        setTotalItems(totalItems - ids.length);
        setSelectedItems([]);
        toast.success(`${ids.length} item(s) restored successfully!`);
      } else {
        toast.error(response?.data?.message || 'Failed to restore items');
      }
    } catch (error) {
      console.error('Error restoring:', error);
      toast.error(error.response?.data?.message || 'Error restoring items');
    } finally {
      setLoading(false);
    }
  };

  return perms && !perms.includes(TOWING_RATES?.TRASH_VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Towing Rates Trash</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={[
            ...HeaderInfo(),
            {
              href: 'false',
              name: 'Trash',
              icon: <RestoreFromTrashIcon sx={{ fontSize: '18px' }} />,
              key: 'trash',
            },
          ]}
        />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="tabs"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="All" value="all" />
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              trash
              showRestore={perms?.includes(TOWING_RATES?.RESTORE)}
              onRestore={handleRestore}
              showDeleteButton={false}
              showEditButton={false}
              showAddButton={false}
              hideFilter
              selectedItems={selectedItems}
              title="Towing Rates Trash"
              options={options}
              setOptions={setOptions}
              total={totalItems}
              dialogTitle="Are you sure you want to permanently delete this data?"
              restoreDialogText="Would you like to restore the selected records?"
              restoreTitle={`Restore Selected Item${selectedItems.length > 1 ? 's' : ''}`}
            />
          }
          sortLoading={loading}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={data}
          headers={[
            ...defaultHeaders,
            { id: 'deleted_at', label: 'Deleted At' },
            { id: 'deleted_by', label: 'Deleted By' },
          ]}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={options.tab}
          tableName="towingRatesTrash"
          {...customColumns}
          created_by={({ towing_rates_created_by }) => (
            <>
              {towing_rates_created_by?.fullname}
              {towing_rates_created_by?.departments?.name &&
                ' | ' + towing_rates_created_by?.departments?.name}
            </>
          )}
          deleted_by={({ towing_rates_deleted_by }) => (
            <>
              {towing_rates_deleted_by?.fullname}
              {towing_rates_deleted_by?.departments?.name &&
                ' | ' + towing_rates_deleted_by?.departments?.name}
            </>
          )}
          location_name={({ locations }) => locations?.name}
          half_location={({ half_locations }) => half_locations?.name}
          city_name={({ loading_cities }) => loading_cities?.city_name}
          branch_name={({ loading_cities }) =>
            loading_cities?.loading_states?.name
          }
          state_name={({ loading_cities }) =>
            loading_cities?.loading_states?.parent?.name
          }
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          deleted_at={({ deleted_at }) => formatDate(deleted_at)}
          effective_date={({ effective_date }) => formatDate(effective_date)}
        />
      </Container>
    </>
  );
};

export default TowingRateTrash;
