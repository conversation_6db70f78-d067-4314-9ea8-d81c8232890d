// Create a new file called CustomColumnTowingRates.tsx
import { Chip } from '@mui/material';
import { removeUnderScore2 } from '@/configs/common';

export const useCustomColumnTowingRates = () => {
  return {
    status: (item) => {
      const status = item?.status?.toLowerCase();
      let color = '';

      switch (status) {
        case 'active':
          color = '#4caf50';
          break;
        case 'inactive':
          color = '#ff9800';
          break;
        case 'pending':
          color = '#9e9e9e';
          break;
        default:
          color = '#9e9e9e';
      }
      return (
        <Chip
          size="small"
          label={removeUnderScore2(item?.status || '')}
          sx={{
            backgroundColor: color,
            color: 'white',
            fontSize: '11px',
            minWidth: '80px',
          }}
        />
      );
    },
  };
};
