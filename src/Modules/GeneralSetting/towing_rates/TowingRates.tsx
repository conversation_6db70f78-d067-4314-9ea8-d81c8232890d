import PageAction from '@/components/mainComponents/PageAction/PageAction';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { copyORViewFun, recordManager } from '@/configs/configs';
import {
  HeaderInfo,
  filterContentTowingRates,
  towingRateTabs,
} from '@/configs/general_setting/towingRatesHeader';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { formatDate } from '@/configs/vehicles/configs';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import {
  Box,
  CircularProgress,
  Container,
  Link,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import Head from 'next/head';
import { useCallback, useContext, useEffect, useState } from 'react';
import { TOWING_RATES } from '@/configs/leftSideMenu/Permissions';
import CreateTowingRate from './CreateTowingRate';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import CBasicTooltip from '@/components/mainComponents/datatable/cBasicTooltip';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';
import DownloadIcon from '@mui/icons-material/Download';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import UpdateEffectiveDate from './UpdateEffectiveDate';
import React from 'react';
import ViewSingleTowingRate from './towingRateComponents/ViewSingleTowingRates';
import { useRouter } from 'next/router';
import TowingRateChangeStatus from './TowingRateChangeStatus';
import { TowingRateActionButtons } from './TowingRateActionButtons';
import { useCustomColumnTowingRates } from './CustomColumnTowingRates';

const TowingRates = ({ apiUrl, activeTab, defaultHeaders }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [cancelToken, setCancelToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [tableRecords, setTableRecords] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [openFilter, setOpenFilter] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [downloadLoading2, setDownloadLoading2] = useState(false);
  const [effectiveDate, setEffectiveDate] = useState(null);
  const [edit, setEdit] = useState(false);
  const [newDate, setNewDate] = useState(null);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [title, setTitle] = useState('TowingRate');
  const [openConfirm, setOpenConfirm] = useState(false);

  const router = useRouter();
  const customColumns = useCustomColumnTowingRates();

  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    tab: activeTab,
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });

  // Sync tab state with URL changes
  useEffect(() => {
    const handleRouteChange = (url) => {
      const tabFromUrl = url.split('/').pop();
      if (['all', 'pending', 'active', 'inactive'].includes(tabFromUrl)) {
        setOptions((prev) => ({
          ...prev,
          tab: tabFromUrl,
        }));

        const tabName = towingRateTabs.find((tab) => tab.value === tabFromUrl);
        setTitle(
          tabFromUrl === 'all'
            ? 'TowingRate'
            : `TowingRate ${tabName?.name || ''}`,
        );
      }
    };

    router.events.on('routeChangeComplete', handleRouteChange);
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router]);

  // Set initial tab state
  useEffect(() => {
    const tabName = towingRateTabs.find((tab) => tab.value === activeTab);
    setTitle(
      activeTab === 'all' ? 'TowingRate' : `TowingRate ${tabName?.name || ''}`,
    );
  }, [activeTab]);

  const customButtons = useCallback(
    () => (
      <TowingRateActionButtons
        tab={options.tab}
        setOpenConfirm={setOpenConfirm}
        selectedItems={selectedItems}
      />
    ),
    [options.tab, selectedItems, setOpenConfirm],
  );

  const fetchRecords = async (fd: any = null) => {
    try {
      setLoading(true);
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);

      // Build filter data properly
      const finalFilterData = fd ?? { ...options.filterData };

      // Ensure status is included
      if (options.tab !== 'all' && !finalFilterData.status) {
        finalFilterData.status = options.tab;
      }

      const { data } = await axios.get(`${apiUrl}`, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(finalFilterData),
        },
      });

      setLoading(false);
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      setLoading(false);
      console.error(error);
    }
  };

  const onTabChange = (val) => {
    setSelectedItems([]);
    const tabName = towingRateTabs.find((item) => item.value === val);

    setOptions((prev) => ({
      ...prev,
      tab: val,
    }));

    if (tabName?.value) {
      setTitle(
        tabName.value === 'all' ? 'TowingRate' : `TowingRate ${tabName.name}`,
      );
    } else {
      setTitle('TowingRate');
    }

    // Update the URL without page reload
    router.push(`/general/towing_rates/${val}`, undefined, { shallow: true });
  };

  // Fetch data when options change
  useEffect(() => {
    let fd: any = {};

    if (options.tab !== 'all') {
      fd.status = options.tab;
    }

    if (options.filterData['loading_city_id'])
      fd['loading_city_id'] = options.filterData['loading_city_id'];

    if (options.filterData['branchId'])
      fd['loading_cities.branch_id'] = options.filterData['branchId'];

    if (options.filterData['stateId'])
      fd['loading_cities.loading_states.parent.id'] =
        options.filterData['stateId'];

    if (options.filterData['location_id'])
      fd['location_id'] = options.filterData['location_id'];

    fetchRecords(fd);
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
  ]);

  const downloadPdf = async () => {
    try {
      setDownloadLoading(true);
      const response = await axios.get(`${apiUrl}/towingRatePdf`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = 'towing-rates.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setDownloadLoading(false);
    } catch (err) {
      console.log(err);
      setDownloadLoading(false);
    }
  };

  const halfCutRatePdfDownload = async () => {
    try {
      setDownloadLoading2(true);
      const response = await axios.get(`${apiUrl}/halfCutRatePdf`, {
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = 'half-cut-rates.pdf';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setDownloadLoading2(false);
    } catch (err) {
      console.log(err);
      setDownloadLoading2(false);
    }
  };

  const EditEffectiveDate = (ed: any) => {
    const d = new Date();
    if (!ed) ed = `${d.getFullYear()}-${d.getMonth() + 1}-1`;
    setEdit(true);
    setEffectiveDate(ed);
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`towing-rates/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      setSingleFetchLoading(false);
    }
  };

  const getSingleRow = async (id) => {
    try {
      setSelectedItems([]);
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data?.data);
      }
    } catch (error) {
      console.log(error);
      setView(false);
    }
  };

  return perms && !perms?.includes(TOWING_RATES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          onChange={(_event, val) => onTabChange(val)}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="towing rate tabs"
        >
          {towingRateTabs.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.value}
            />
          ))}
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Towing Rates'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              customActionButtons={customButtons}
              showCustomizeColumn={false}
              customComponent={
                <>
                  <Box sx={{ marginRight: '3px' }}>
                    {perms?.includes(TOWING_RATES?.VIEW) &&
                      (downloadLoading2 ? (
                        <CircularProgress size={24} />
                      ) : (
                        <CBasicTooltip
                          onClick={halfCutRatePdfDownload}
                          title="Half Cut Rate PDF"
                          icon={<DownloadIcon />}
                        />
                      ))}
                  </Box>
                  |
                  <Box sx={{ marginLeft: '3px' }}>
                    {perms?.includes(TOWING_RATES?.VIEW) &&
                      (downloadLoading ? (
                        <CircularProgress size={24} />
                      ) : (
                        <CBasicTooltip
                          onClick={downloadPdf}
                          title="Towing Rate PDF"
                          icon={<DownloadForOfflineIcon />}
                        />
                      ))}
                  </Box>
                </>
              }
              startCustomComponent={
                <Box display="flex" gap="6px">
                  <EventAvailableIcon />{' '}
                  <span
                    style={{
                      fontSize: 17,
                      fontWeight: 'bold',
                    }}
                  >
                    Effective Date :
                  </span>
                  <Link
                    title="Update Effective Date"
                    onClick={() =>
                      EditEffectiveDate(tableRecords[0]?.effective_date)
                    }
                    component="button"
                    variant="body1"
                  >
                    {tableRecords[0]?.effective_date
                      ? formatDate(
                          newDate ? newDate : tableRecords[0]?.effective_date,
                        )
                      : 'Update Date'}
                  </Link>
                  <Typography sx={{ paddingX: '10px' }}>|</Typography>
                </Box>
              }
              //onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} shipping cost ? `}
              dialogTitle={`Delete shipping Item`}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() =>
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                })
              }
              showDownload={false}
              showAddButton={perms?.includes(TOWING_RATES?.CREATE)}
              showEditButton={perms?.includes(TOWING_RATES?.UPDATE)}
              showDeleteButton={perms?.includes(TOWING_RATES?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={defaultHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="towingRates"
          {...customColumns}
          // end default props
          //start custom props

          created_by={({ towing_rates_created_by }) => (
            <>
              {towing_rates_created_by?.fullname}
              {towing_rates_created_by?.departments?.name &&
                ' | ' + towing_rates_created_by?.departments?.name}
            </>
          )}
          location_name={({ locations }) => locations?.name}
          half_location={({ half_locations }) => half_locations?.name}
          city_name={({ loading_cities }) => loading_cities?.city_name}
          branch_name={({ loading_cities }) =>
            loading_cities?.loading_states?.name
          }
          state_name={(item) => {
            return copyORViewFun({
              getSingleRow,
              copy: item?.loading_cities,
              display: item?.loading_cities?.loading_states?.parent?.name,
              id: item?.id,
            });
          }}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
        />

        <ViewSingleTowingRate
          data={viewData}
          setView={setView}
          show={view}
          loading={singleFetchLoading}
        />

        <FilterModal2
          open={openFilter}
          toggleOpen={() => setOpenFilter((d) => !d)}
          options={options}
          setOptions={setOptions}
          title="Filter Towing Rates"
          content={filterContentTowingRates}
        />

        <CreateTowingRate
          setSelectedItems={setSelectedItems}
          show={showCreate}
          setShow={setShowCreate}
          selectedItems={selectedItems[0]}
          isUpdate={isUpdate}
          recordManager={(data, type) => {
            recordManager({
              data,
              type,
              setTableRecords,
              tableRecords,
              selectedItems,
              setSelectedItems,
              setTotalItems,
              totalItems,
              apiUrl,
            });
          }}
        />

        <TowingRateChangeStatus
          apiUrl={apiUrl}
          fetchRecords={fetchRecords}
          openConfirm={openConfirm}
          selectedItems={selectedItems}
          setOpenConfirm={setOpenConfirm}
          setSelectedItems={setSelectedItems}
          tab={options.tab}
        />

        <UpdateEffectiveDate
          open={edit}
          effective_date={effectiveDate}
          onDeny={setEdit}
          setNewDate={setNewDate}
        />
      </Container>
    </>
  );
};

export default TowingRates;
