import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import { Box, IconButton } from '@mui/material';
import VerifiedIcon from '@mui/icons-material/Verified';
import { TOWING_RATES } from '@/configs/leftSideMenu/Permissions';
import { useContext } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';

export const TowingRateActionButtons = ({
  tab,
  setOpenConfirm,
  selectedItems,
}) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const shouldShowButton = () => {
    if (selectedItems.length === 0) {
      return { visible: false, disabled: true };
    }

    if (tab === 'all') {
      return { visible: false, disabled: true };
    }

    if (tab === 'pending') {
      return {
        visible: perms?.includes(TOWING_RATES?.APPROVE),
        disabled: !perms?.includes(TOWING_RATES?.APPROVE),
      };
    }

    if (tab === 'active' || tab === 'inactive') {
      return {
        visible: perms?.includes(TOWING_RATES?.INACTIVE),
        disabled: !perms?.includes(TOWING_RATES?.INACTIVE),
      };
    }

    return { visible: false, disabled: true };
  };

  const { visible } = shouldShowButton();

  if (!visible) {
    return null;
  }

  const getButtonTitle = () => {
    switch (tab) {
      case 'pending':
        return 'Approve';
      case 'active':
        return 'Inactivate';
      case 'inactive':
        return 'Activate';
      default:
        return 'Change Status';
    }
  };

  const getButtonColor = () => {
    switch (tab) {
      case 'pending':
        return 'secondary';
      case 'active':
        return 'error';
      case 'inactive':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  return (
    <Box
      style={{
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
      }}
    >
      <AppTooltip title={getButtonTitle()}>
        <IconButton
          key={'Approved'}
          onClick={() => setOpenConfirm(true)}
          color={getButtonColor()}
        >
          <VerifiedIcon />
        </IconButton>
      </AppTooltip>
    </Box>
  );
};
