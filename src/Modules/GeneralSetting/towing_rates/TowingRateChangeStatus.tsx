import axios from '@/lib/axios';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';

const TowingRateChangeStatus = ({
  selectedItems,
  apiUrl,
  tab,
  setSelectedItems,
  fetchRecords,
  setOpenConfirm,
  openConfirm,
}) => {
  const [submitting, setSubmitting] = useState(false);

  const getConfirmationMessage = () => {
    const count = selectedItems.length;
    switch (tab) {
      case 'pending':
        return `Are you sure you want to approve (${count}) towing rate${count > 1 ? 's' : ''}?`;
      case 'active':
        return `Are you sure you want to inactivate (${count}) towing rate${count > 1 ? 's' : ''}?`;
      case 'inactive':
        return `Are you sure you want to activate (${count}) towing rate${count > 1 ? 's' : ''}?`;
      default:
        return `Are you sure you want to change the status of (${count}) towing rate${count > 1 ? 's' : ''}?`;
    }
  };

  const getNewStatus = () => {
    if (tab === 'pending') return 'active';
    if (tab === 'active') return 'inactive';
    if (tab === 'inactive') return 'active';
    return 'pending';
  };

  const changeStatus = async () => {
    if (selectedItems.length === 0) {
      toast.warn('Please select at least one towing rate');
      return;
    }

    try {
      setSubmitting(true);
      const endpoint =
        tab === 'pending'
          ? `${apiUrl}/approve`
          : `${apiUrl}/toggleActiveStatus`;

      const { data } = await axios.patch(endpoint, {
        ids: selectedItems.map((item) => item.id),
        status: getNewStatus(),
      });

      if (data.result) {
        setSelectedItems([]);
        fetchRecords();
        setOpenConfirm(false);
        toast.success(data.message || 'Status changed successfully');
      } else {
        toast.warn(data.message || 'Update failed, please try again');
      }
    } catch (error) {
      toast.error('Oops! Something went wrong.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <AppConfirmDialog
      open={openConfirm}
      onDeny={() => setOpenConfirm(false)}
      onConfirm={changeStatus}
      dialogTitle={getConfirmationMessage()}
      confirmText="Confirm"
      cancelText="Cancel"
      maxWidth={'sm'}
      submitting={submitting}
    />
  );
};

export default TowingRateChangeStatus;
