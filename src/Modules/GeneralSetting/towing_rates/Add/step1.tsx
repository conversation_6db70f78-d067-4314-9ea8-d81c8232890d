import { Box, Grid, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import Auto_Complete from '../../mix_shipping_rates/Add/Auto_Complete';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { useLocationContext } from '@/contexts/LocationsContext';

interface FilmOptionType {
  inputValue?: string;
  name: string;
  id?: number;
}

export default function Step1({ form, selectedItem }) {
  const [states, setStates] = useState<any[]>([]);
  const [branches, setBranches] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [branchValue, setBranchValue] = useState<FilmOptionType | null>(null);
  const [cityValue, setCityValue] = useState<FilmOptionType | null>(null);
  const [stateValue, setStateValue] = useState<FilmOptionType | null>(
    selectedItem ? selectedItem?.loading_cities?.loading_states?.parent : null,
  );
  const [open, toggleOpen] = useState<any>(false);
  const [dialogValue, setDialogValue] = useState({ name: '' });
  const [loadingButton, setLoadingButton] = useState(false);
  //@ts-ignore
  const [newState, setNewState] = useState<number>(0);

  const handleClose = () => {
    setDialogValue({ name: '' });
    toggleOpen(false);
  };

  const { map }: any = useLocationContext();
  const locations = map((item) => ({
    label: item.name,
    id: item.id,
  })).filter((o) => o.id !== 9);

  const halfLocations = map((item) => ({
    label: item.name,
    id: item.id,
  })).filter((o) => o.id !== 1 && o.id !== 4 && o.id !== 5);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/states`, {
          signal: controller.signal,
        });

        if (res?.status == 200 || res?.data?.result) {
          form.setValue(
            'stateId',
            selectedItem?.loading_cities?.loading_states?.parent.id,
          );
          setStates(res?.data?.data);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchData();
  }, [newState]);

  const loadBranches = async (stateId: number) => {
    if (stateId)
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/branches/${stateId}`, {
          signal: controller.signal,
        });
        if (res?.status == 200 || res?.data?.result) {
          setBranches(res?.data?.data);
          if (
            res?.data?.data?.some(
              (o) =>
                o.name == selectedItem?.loading_cities?.loading_states?.name,
            )
          ) {
            form.setValue(
              'branchId',
              selectedItem?.loading_cities?.loading_states?.id,
            );
            setBranchValue(
              selectedItem
                ? {
                    name: selectedItem?.loading_cities?.loading_states?.name,
                    id: selectedItem?.loading_cities?.loading_states?.id,
                  }
                : null,
            );
          } else {
            setBranchValue(null);
          }
          setCityValue(null);
        }
      } catch (error) {
        console.log(error);
      }
  };

  const loadCities = async (branchId: number) => {
    if (branchId)
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/cities/${branchId}`, {
          signal: controller.signal,
        });
        if (res?.status == 200 || res?.data?.result) {
          setCities(res?.data?.data);
          if (
            res?.data?.data?.some(
              (o) => o.name == selectedItem?.loading_cities?.city_name,
            )
          ) {
            form.setValue('loading_city_id', selectedItem?.loading_cities?.id);
            setCityValue(
              selectedItem
                ? {
                    name: selectedItem?.loading_cities?.city_name,
                    id: selectedItem?.loading_cities?.id,
                  }
                : null,
            );
          } else {
            setCityValue(null);
          }
        }
      } catch (error) {
        console.log(error);
      }
  };

  const changeState = async (newValue: any) => {
    if (newValue && newValue.id) {
      setTimeout(() => {
        form.setValue('stateId', newValue?.id);
        loadBranches(newValue?.id);
      });
      form.setValue('loading_city_id', null);
      setStateValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('state');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setStateValue(newValue);
    }
  };

  const changeBranch = (newValue: any) => {
    if (newValue && newValue.id) {
      setTimeout(() => {
        form.setValue('branchId', newValue?.id);
        loadCities(newValue?.id);
      });
      setBranchValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('branch');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setBranchValue(newValue);
    }
  };

  const changeCity = (newValue: any) => {
    if (newValue && newValue.id) {
      form.setValue('loading_city_id', newValue.id);
      setCityValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('city');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setCityValue(newValue);
    }
  };

  const handleSubmit = async (route: any) => {
    let param: any = { name: dialogValue.name };
    if (route == 'createBranch') param.parentId = stateValue.id;

    if (route == 'createCity')
      param = { city_name: dialogValue.name, branchId: branchValue.id };

    try {
      setLoadingButton(true);
      const { data } = await axios.post(`/mix-shipping-rates/${route}`, param);

      if (data?.result == true) {
        setLoadingButton(false);
        if (route == 'createBranch') {
          form.setValue('branchId', data?.data?.id);
          setBranchValue({ name: data?.data?.name, id: data?.data?.id });
        }

        if (route == 'createState') {
          form.setValue('stateId', data?.data?.id);
          setStateValue({ name: data?.data?.name, id: data?.data?.id });
        }

        if (route == 'createCity') {
          form.setValue('loading_city_id', data?.data?.id);
          setCityValue({ name: data?.data?.city_name, id: data?.data?.id });
        }
        handleClose();
        return true;
      } else {
        setLoadingButton(false);
        return false;
      }
    } catch (err) {
      setLoadingButton(false);
      console.log('ERROR::', err.response.data.message);
    }
  };

  useEffect(() => {
    loadBranches(stateValue?.id);
  }, [stateValue]);

  useEffect(() => {
    loadCities(branchValue?.id);
  }, [branchValue]);

  return (
    <Box sx={{ py: 2, mx: 3 }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        General Information
      </Typography>
      <Grid spacing={2} container>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="stateId"
              control={form.control}
              //@ts-ignore
              render={({ field, fieldState: { error } }) => (
                <Auto_Complete
                  data={states}
                  label={'Select State'}
                  onChange={changeState}
                  handleSubmit={() => handleSubmit('createState')}
                  open={open}
                  handleClose={handleClose}
                  dialogValue={dialogValue}
                  setDialogValue={setDialogValue}
                  error={error}
                  value={stateValue}
                  id="state"
                  loadingButton={loadingButton}
                />
              )}
            />
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            {stateValue || selectedItem ? (
              <Controller
                name="branchId"
                control={form.control}
                //@ts-ignore
                render={({ field, fieldState: { error } }) => (
                  <Auto_Complete
                    data={branches}
                    label={'Select Branch'}
                    onChange={changeBranch}
                    handleSubmit={() => handleSubmit('createBranch')}
                    open={open}
                    handleClose={handleClose}
                    dialogValue={dialogValue}
                    setDialogValue={setDialogValue}
                    error={error}
                    value={branchValue}
                    id="branch"
                    loadingButton={loadingButton}
                  />
                )}
              />
            ) : (
              <EmptyField name="branchId" form={form} label="Select Branch" />
            )}
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            {branchValue ? (
              <Controller
                name="loading_city_id"
                control={form.control}
                //@ts-ignore
                render={({ field, fieldState: { error } }) => (
                  <Auto_Complete
                    data={cities}
                    label={'Select City'}
                    onChange={changeCity}
                    handleSubmit={() => handleSubmit('createCity')}
                    open={open}
                    handleClose={handleClose}
                    dialogValue={dialogValue}
                    setDialogValue={setDialogValue}
                    error={error}
                    value={cityValue}
                    id="city"
                    loadingButton={loadingButton}
                  />
                )}
              />
            ) : (
              <EmptyField
                name="loading_city_id"
                form={form}
                label="Select City "
              />
            )}
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="location_id"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                    url={false}
                    fieldName="name"
                    label="Select Location"
                    field={field}
                    error={error}
                    staticOptions={locations}
                    column={''}
                    modal={''}
                  />
                )}
              />
            ) : (
              <EmptyField
                name="location_id"
                form={form}
                label="Select Location"
              />
            )}
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="towing"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                    error={
                      form.errors.towing?.message.length > 0 ? true : false
                    }
                    id="towing"
                    value={field.value ?? ''}
                    label="Towing Rate"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <EmptyField name="towing" form={form} label="Towing Rate" />
            )}
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="half_location_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                  url={false}
                  fieldName="name"
                  label="Half Cut Location"
                  field={field}
                  error={error}
                  staticOptions={halfLocations}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="half_rate"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                  size="small"
                  error={form.errors.towing?.message.length > 0 ? true : false}
                  id="half_rate"
                  value={field.value ?? ''}
                  label="Half Cut Rate"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => field.onChange(+value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}

export function EmptyField({ name, form, label }) {
  return (
    <Box sx={{ marginY: 1.5 }}>
      <Controller
        name={name}
        control={form.control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            size="small"
            error={form.errors[name]?.message.length > 0 ? true : false}
            id={name}
            value={field.value ?? ''}
            label={label}
            fullWidth
            type="number"
            variant="outlined"
            helperText={error?.message}
            InputProps={{
              readOnly: true,
            }}
          />
        )}
      />
    </Box>
  );
}
