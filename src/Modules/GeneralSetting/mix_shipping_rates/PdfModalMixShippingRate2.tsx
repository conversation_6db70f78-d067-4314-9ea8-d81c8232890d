import {
  Button,
  Card,
  CardActions,
  CardContent,
  DialogContent,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import CloseIcon from '@mui/icons-material/Close';
import { Box } from '@mui/system';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import DownloadIcon from '@mui/icons-material/Download';

// const currencies = [
//   { name: 'USD', exchangeRate: 1 },
//   { name: 'AED', exchangeRate: 3.685 },
//   { name: 'OMR', exchangeRate: 2.6 },
//   { name: 'GEL', exchangeRate: 2.74 },
// ];

const pdfColumns = [
  { id: 'shipping', name: 'Shipping' },
  { id: 'towing', name: 'Towing' },
  { id: 'clearance', name: 'Clearance' },
  { id: 'TDS_charges', name: 'TDS Charges' },
  { id: 'tax_duty', name: 'Tax & Duty' },
  { id: 'total', name: 'Total' },
];

const notes = [
  { id: 'jebel_ali', name: 'Jebel Ali' },
  { id: 'oman', name: 'Oman' },
  { id: 'poti', name: 'Poti' },
  { id: 'custom', name: 'Custom' },
];

const PdfMixShippingRate2 = ({
  open,
  setOpen,
  company = null,
  apiUrl,
  options,
}) => {
  const matches = useMediaQuery('(max-width:700px)');
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [currency, setCurrency] = useState('USD');
  const [note, setNote] = useState('Jebel Ali');
  const [exchangeRate, setExchangeRate] = useState(1);
  const [currencies_rates, setCurrencies_rates] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [customNote, setCustomNote] = useState();

  const name = (str: string): string => str.replace(/ /g, '_');
  const dest = (str: string): string => str.replace(/ /g, '');

  const theme = useTheme();

  const downloadPdf = async () => {
    if (note != 'Custom') {
      setCustomNote(null);
    }
    try {
      setDownloadLoading(true);
      const response = await axios.get(
        `${apiUrl}/shipmentRatePdf/${company?.destination_id ?? options.tab}`,
        {
          responseType: 'blob',
          params: {
            company_id: company?.id ?? null,
            currency,
            exchangeRate,
            selectedColumns,
            note,
            customNote,
          },
        },
      );
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      const date = new Date();
      const formattedDate = date
        .toLocaleString('en-US', { month: 'short', year: 'numeric' })
        .replace(',', '-');

      a.href = url;
      a.download = company
        ? `${name(company?.name)}-${dest(company?.destinations?.name)} ${formattedDate}.pdf`
        : `Mix Shipping Rates ${options.tab == 12 ? 'Jebel Ali' : options.tab == 22 ? 'Poti Georgia' : options.tab == 24 ? 'Salalah' : ''} ${formattedDate}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setOpen(false);
      setDownloadLoading(false);
      setCustomNote(null);
    } catch (err) {
      setDownloadLoading(false);
    }
  };
  const getExchangeRates = async () => {
    let { data } = await axios.get('/exchange_rates');
    setCurrencies_rates([
      ...data?.data,
      { currency: 'USD', rate: 1, created_by: 'Locally Created' },
    ]);
  };
  useEffect(() => {
    getExchangeRates();
  }, []);
  return (
    <Modal
      open={open}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box>
        <Card sx={{ width: matches ? 300 : 650 }}>
          <Box
            sx={{
              m: 0,
              p: 2,
              py: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant="h6">
              Downloads Mix Shipping Rate PDF
            </Typography>
            <IconButton
              aria-label="close"
              sx={{
                color: 'grey',
              }}
              onClick={() => setOpen(false)}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <CardContent>
            <FormControl fullWidth size="small">
              <InputLabel id="demo-select-small-label">Currency</InputLabel>
              <Select
                value={currency}
                label="Currency"
                onChange={(event) => {
                  setCurrency(event.target.value);
                  setExchangeRate(
                    currencies_rates.find(
                      (item) => item.currency == event.target.value,
                    )?.rate,
                  );
                }}
                sx={{ mb: 1.5 }}
              >
                {currencies_rates.map((item, index) => {
                  return (
                    <MenuItem key={index} value={item.currency}>
                      {item.currency}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>

            <TextField
              size="small"
              id="qty"
              type="number"
              value={exchangeRate}
              label="Exchange Rate"
              fullWidth
              variant="outlined"
              onChange={(value) => setExchangeRate(+value.target.value)}
              sx={{ mb: 1.5 }}
            />
            <FilterAutocomplete
              url={''}
              label={'Selected Columns'}
              name={'selected_columns'}
              keyName={'name'}
              values={selectedColumns}
              staticOptions={pdfColumns}
              onChange={(value) => setSelectedColumns(value)}
            />
            <FormControl sx={{ marginTop: '1rem' }} fullWidth size="small">
              <InputLabel id="demo-select-small-label">Notes</InputLabel>
              <Select
                value={note}
                label="Notes"
                onChange={(event) => {
                  setNote(event.target.value);
                }}
                sx={{ mb: 1.5 }}
              >
                {notes.map((note, index) => {
                  return (
                    <MenuItem key={index} value={note.name}>
                      {note.name}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            {note == 'Custom' && (
              <DialogContent
                sx={{
                  ...(theme.palette.mode == 'dark'
                    ? { backgroundColor: '#121212' }
                    : {}),
                  border: 1,
                  borderColor: 'white',
                  px: 0,
                  py: 0.3,
                  width: '100%',
                }}
              >
                <QuillEditor
                  height={200}
                  onChange={(value) => setCustomNote(value)}
                  value={customNote}
                  placeholder="Custom Note"
                />
              </DialogContent>
            )}
          </CardContent>
          <CardActions
            sx={{ px: 2, pb: 2, display: 'flex', justifyContent: 'end' }}
          >
            <Button
              onClick={() => setOpen(false)}
              variant="text"
              color="primary"
              size="small"
              sx={{ marginRight: '5px' }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                downloadPdf();
              }}
              variant="contained"
              color="primary"
              size="small"
              sx={{ marginRight: '5px' }}
              startIcon={<DownloadIcon />}
              loading={downloadLoading}
            >
              Download
            </Button>
          </CardActions>
        </Card>
      </Box>
    </Modal>
  );
};

export default PdfMixShippingRate2;
