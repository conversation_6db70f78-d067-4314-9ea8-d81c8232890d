import axios from '@/lib/axios';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { toast } from 'react-toastify';
import { Controller } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  Box,
  Card,
  FormControl,
  Grid,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material';
import { useState } from 'react';
import { ExcelIcon } from '@/components/mainComponents/svgIcons/SvgIcons';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

const CardItem = ({ children }) => {
  return (
    <Card
      variant="outlined"
      sx={{
        p: 1,
        mb: 1,
        height: 60,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: '7px',
        '&:hover': {
          boxShadow: 'md',
          borderColor: 'neutral.outlinedHoverBorder',
        },
      }}
    >
      {children}
    </Card>
  );
};

const UploadMixShippingExcel = ({ title, open, setOpen }) => {
  const [file, setFile] = useState();
  const [dataType, setDataType] = useState('upload');

  const { setValue, control, getValues } = useForm({
    mode: 'onChange',
  });

  const form = {
    control,
    setValue,
    getValues,
  };

  const handleClose = () => {
    setOpen(false);
  };

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (event) => {
    setFile(event.target.files[0]);
  };

  const handleAutoCompleteChange = async (event, item, reason) => {
    if (reason == 'clear' && event.type == 'click') {
      form.setValue('company_id', null);
    } else {
      form.setValue('company_id', Number(item.id));
    }
  };

  const submit = async (event) => {
    event.preventDefault();
    if (dataType === 'upload') {
      if (!file) return;
      setIsSubmitted(true);
      const formData = new FormData();
      formData.append('file', file);
      formData.append('destinationId', title[0].id);
      formData.append('company_id', form.getValues('company_id'));
      try {
        const { data } = await axios.post(
          `mix-shipping-rates/upload-excel`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );
        toast.success(`${data.successRow} rows uploaded successfully`);
        setIsSubmitted(false);
      } catch (error) {
        setIsSubmitted(false);
        toast.error(error);
      }
    } else {
      setIsSubmitted(true);
      try {
        const response = await axios.get(
          `mix-shipping-rates/excel/download/${title[0].id}`,
          {
            responseType: 'blob', // Ensure it handles binary data
          },
        );

        // Create a temporary link to trigger the download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'mix_shipping_rates.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setIsSubmitted(false);
      } catch (error) {
        setIsSubmitted(false);
        toast.error('Failed to download Excel report!');
      }
    }
  };

  return (
    <>
      <BootstrapDialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          {title[0]?.name}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            value={dataType}
            name="radio-buttons-group"
            onChange={(_event, val) => setDataType(val)}
          >
            <Typography sx={{ fontSize: '17px', opacity: 0.8, pb: 1 }}>
              Upload Excel
            </Typography>
            <Grid
              marginBottom={2}
              marginTop={1}
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="company_id"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url="autoComplete"
                    label="Special Company"
                    fieldName="name"
                    field={field}
                    error={error}
                    staticOptions={false}
                    column={'name'}
                    modal={'companies'}
                    handleOnChangeFromStepper={true}
                    stepperHandleOnChange={handleAutoCompleteChange}
                  />
                )}
              />
            </Grid>

            <CardItem>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <ExcelIcon />
                <TextField
                  sx={{
                    cursor: 'pointer',
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        border: 'none', // Remove border
                      },
                      '&:hover fieldset': {
                        border: 'none', // Remove hover border
                      },
                      '&.Mui-focused fieldset': {
                        border: 'none', // Remove focused border
                      },
                    },
                    '& .MuiInputBase-root': {
                      outline: 'none', // Remove outline on focus
                    },
                  }}
                  required
                  id="file-input"
                  type="file"
                  inputProps={{
                    accept: '.xlsx, .xls',
                  }}
                  onChange={handleChange}
                />
              </Box>
              <Box>
                <FormControl>
                  <Radio value={'upload'} />
                </FormControl>
              </Box>
            </CardItem>

            <Typography
              marginTop={2}
              sx={{ fontSize: '17px', opacity: 0.8, pb: 1 }}
            >
              Download Excel
            </Typography>

            <CardItem>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <ExcelIcon />
                <Typography sx={{ pl: 1, fontWeight: 500 }}>
                  Excel Format
                </Typography>
              </Box>
              <Box>
                <FormControl>
                  <Radio value={'download'} />
                </FormControl>
              </Box>
            </CardItem>
          </RadioGroup>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={submit}>
            {dataType === 'upload'
              ? isSubmitted
                ? 'Uploading...'
                : 'Upload'
              : isSubmitted
                ? 'Downloading...'
                : 'Download'}
          </Button>
        </DialogActions>
      </BootstrapDialog>
    </>
  );
};

export default UploadMixShippingExcel;
