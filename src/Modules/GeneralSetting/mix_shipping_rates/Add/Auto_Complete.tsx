import TextField from '@mui/material/TextField';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';
import { Box } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';

const filter = createFilterOptions<FilmOptionType>();

interface FilmOptionType {
  inputValue?: string;
  name?: string;
  id?: number;
}

export default function Auto_Complete({
  data,
  label,
  onChange,
  handleSubmit,
  open,
  handleClose,
  dialogValue,
  setDialogValue,
  value,
  id,
  error,
  loadingButton,
  showModal = true,
}) {
  return (
    <Box>
      <Autocomplete
        sx={error ? { mt: 1.5 } : { mt: 1.5 }}
        fullWidth
        size="small"
        value={value}
        //@ts-ignore
        onChange={(e, value) => onChange(value)}
        filterOptions={(options, params) => {
          const filtered = filter(options, params);
          if (
            params.inputValue !== '' &&
            !options.some((i) => i.name == params.inputValue)
          ) {
            showModal &&
              filtered.push({
                inputValue: params.inputValue,
                name: `Add "${params.inputValue}"`,
              });
          }
          return filtered;
        }}
        id={id}
        options={data}
        getOptionLabel={(option) => {
          // e.g. value selected with enter, right from the input
          if (typeof option === 'string') return option;

          if (option.inputValue) {
            return option.inputValue;
          }
          return option.name;
        }}
        selectOnFocus
        clearOnBlur
        handleHomeEndKeys
        renderOption={(props, option) => <li {...props}>{option.name}</li>}
        freeSolo
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            variant="outlined"
            error={!!error}
            helperText={error?.message}
            size="small"
          />
        )}
      />
      {showModal && (
        <Dialog open={open == id} onClose={handleClose}>
          <DialogTitle>Add a new {id}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Did you miss any {id} in our list? Please, add it!
            </DialogContentText>
            <TextField
              fullWidth
              size="small"
              autoFocus
              margin="dense"
              id={id}
              value={dialogValue.name}
              onChange={(event) =>
                setDialogValue({
                  ...dialogValue,
                  name: event.target.value,
                })
              }
              label="Name"
              type="text"
              variant="standard"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button
              disabled={loadingButton}
              endIcon={<SaveIcon />}
              onClick={handleSubmit}
              loading={loadingButton}
              loadingPosition="end"
            >
              <span>Add</span>
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
}
