import axios from '@/lib/axios';
import { Box, Grid, TextField, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import Auto_Complete from './Auto_Complete';
import { Controller } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { useLocationContext } from '@/contexts/LocationsContext';

interface FilmOptionType {
  inputValue?: string;
  name: string;
  id?: number;
}

export default function Step1({ form, selectedItem, company = null }) {
  const [states, setStates] = useState<any[]>([]);
  const [branches, setBranches] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  //@ts-ignore
  const [cancelToken, setCancelToken] = useState(null);
  const [loadingButton, setLoadingButton] = useState(false);
  //@ts-ignore
  const [newState, setNewState] = useState<number>(0);
  const [stateValue, setStateValue] = useState<FilmOptionType | null>(
    selectedItem ? selectedItem?.loading_cities?.loading_states?.parent : null,
  );
  const [branchValue, setBranchValue] = useState<FilmOptionType | null>(null);
  const [cityValue, setCityValue] = useState<FilmOptionType | null>(null);
  const [open, toggleOpen] = useState<any>(false);
  const [dialogValue, setDialogValue] = useState({ name: '' });

  const loading_city_id = form.watch('loading_city_id');
  const location_id = form.watch('location_id');

  const { map }: any = useLocationContext();
  const locations = map((item) => ({
    label: item.name,
    id: item.id,
  }));

  useEffect(() => {
    if (loading_city_id && location_id) {
      const fetchCost = async () => {
        try {
          if (cancelToken) {
            await cancelToken.abort();
          }
          const controller = new AbortController();
          setCancelToken(controller);

          const { data } = await axios.get(`towing-rates/getTowingRate`, {
            signal: controller.signal,
            params: {
              loading_city_id,
              location_id,
            },
          });

          if (data.result) form.setValue('towing', data?.towing);
        } catch (error) {
          console.log(error);
        }
      };
      fetchCost();
    } else if (!form.isUpdate) {
      form.setValue('towing', 0);
    }
  }, [loading_city_id, location_id]);

  const handleClose = () => {
    setDialogValue({ name: '' });
    toggleOpen(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/states`, {
          signal: controller.signal,
        });

        if (res?.status == 200 || res?.data?.result) {
          form.setValue(
            'stateId',
            selectedItem?.loading_cities?.loading_states?.parent.id,
          );
          setStates(res?.data?.data);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchData();
  }, [newState]);

  const loadBranches = async (stateId: number) => {
    if (stateId)
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/branches/${stateId}`, {
          signal: controller.signal,
        });
        if (res?.status == 200 || res?.data?.result) {
          setBranches(res?.data?.data);
          if (
            res?.data?.data?.some(
              (o) =>
                o.name == selectedItem?.loading_cities?.loading_states?.name,
            )
          ) {
            form.setValue(
              'branchId',
              selectedItem?.loading_cities?.loading_states?.id,
            );
            setBranchValue(
              selectedItem
                ? {
                    name: selectedItem?.loading_cities?.loading_states?.name,
                    id: selectedItem?.loading_cities?.loading_states?.id,
                  }
                : null,
            );
          } else {
            setBranchValue(null);
          }
          setCityValue(null);
        }
      } catch (error) {
        console.log(error);
      }
  };

  const loadCities = async (branchId: number) => {
    if (branchId)
      try {
        if (cancelToken) await cancelToken.abort();
        const controller = new AbortController();
        setCancelToken(controller);
        const res = await axios.get(`mix-shipping-rates/cities/${branchId}`, {
          signal: controller.signal,
        });
        if (res?.status == 200 || res?.data?.result) {
          setCities(res?.data?.data);
          if (
            res?.data?.data?.some(
              (o) => o.name == selectedItem?.loading_cities?.city_name,
            )
          ) {
            form.setValue('loading_city_id', selectedItem?.loading_cities?.id);
            setCityValue(
              selectedItem
                ? {
                    name: selectedItem?.loading_cities?.city_name,
                    id: selectedItem?.loading_cities?.id,
                  }
                : null,
            );
          } else {
            setCityValue(null);
          }
        }
      } catch (error) {
        console.log(error);
      }
  };

  const changeState = async (newValue: any) => {
    if (newValue && newValue.id) {
      setTimeout(() => {
        form.setValue('stateId', newValue?.id);
        loadBranches(newValue?.id);
      });
      form.setValue('loading_city_id', null);
      setStateValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('state');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setStateValue(newValue);
    }
  };

  const changeBranch = (newValue: any) => {
    if (newValue && newValue.id) {
      setTimeout(() => {
        form.setValue('branchId', newValue?.id);
        loadCities(newValue?.id);
      });
      setBranchValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('branch');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setBranchValue(newValue);
    }
  };

  const changeCity = (newValue: any) => {
    if (newValue && newValue.id) {
      form.setValue('loading_city_id', newValue.id);
      setCityValue(newValue);
    } else if (newValue && newValue.inputValue) {
      toggleOpen('city');
      setDialogValue({
        name: newValue.inputValue,
      });
    } else {
      setCityValue(newValue);
    }
  };

  const handleSubmit = async (route: any) => {
    let param: any = { name: dialogValue.name };
    if (route == 'createBranch') param.parentId = stateValue.id;

    if (route == 'createCity')
      param = { city_name: dialogValue.name, branchId: branchValue.id };

    try {
      setLoadingButton(true);
      const { data } = await axios.post(`/mix-shipping-rates/${route}`, param);

      if (data?.result == true) {
        setLoadingButton(false);
        if (route == 'createBranch') {
          form.setValue('branchId', data?.data?.id);
          setBranchValue({ name: data?.data?.name, id: data?.data?.id });
        }

        if (route == 'createState') {
          form.setValue('stateId', data?.data?.id);
          setStateValue({ name: data?.data?.name, id: data?.data?.id });
        }

        if (route == 'createCity') {
          form.setValue('loading_city_id', data?.data?.id);
          setCityValue({ name: data?.data?.city_name, id: data?.data?.id });
        }
        handleClose();
        return true;
      } else {
        setLoadingButton(false);
        return false;
      }
    } catch (err) {
      setLoadingButton(false);
      console.log('ERROR::', err.response.data.message);
    }
  };

  useEffect(() => {
    if (stateValue?.id) {
      loadBranches(stateValue?.id);
    } else {
      form.setValue('branchId', null);
      form.setValue('loading_city_id', null);
      setCityValue(null);
      setBranchValue(null);
    }
  }, [stateValue]);

  useEffect(() => {
    if (branchValue?.id) {
      loadCities(branchValue?.id);
    } else {
      form.setValue('loading_city_id', null);
      setCityValue(null);
    }
  }, [branchValue]);

  useEffect(() => {
    if (company) {
      form.setValue('destination_id', company?.destinations?.id);
    }
  }, [company]);

  return (
    <Box sx={{ py: 2, mx: 3 }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        General Information
      </Typography>
      <Grid container>
        <Grid
          sx={{ paddingY: 1 }}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="stateId"
            control={form.control}
            //@ts-ignore
            render={({ field, fieldState: { error } }) => (
              <Auto_Complete
                data={states}
                label={'Select State'}
                onChange={changeState}
                handleSubmit={() => handleSubmit('createState')}
                open={open}
                handleClose={handleClose}
                dialogValue={dialogValue}
                setDialogValue={setDialogValue}
                error={error}
                value={stateValue}
                id="state"
                loadingButton={loadingButton}
                showModal={false}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          {stateValue || selectedItem ? (
            <Controller
              name="branchId"
              control={form.control}
              //@ts-ignore
              render={({ field, fieldState: { error } }) => (
                <Auto_Complete
                  data={branches}
                  label={'Select Branch'}
                  onChange={changeBranch}
                  handleSubmit={() => handleSubmit('createBranch')}
                  open={open}
                  handleClose={handleClose}
                  dialogValue={dialogValue}
                  setDialogValue={setDialogValue}
                  error={error}
                  value={branchValue}
                  id="branch"
                  loadingButton={loadingButton}
                  showModal={false}
                />
              )}
            />
          ) : (
            <EmptyField name="branchId" form={form} label="Select Branch" />
          )}
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          {branchValue ? (
            <Controller
              name="loading_city_id"
              control={form.control}
              //@ts-ignore
              render={({ field, fieldState: { error } }) => (
                <Auto_Complete
                  data={cities}
                  label={'Select City'}
                  onChange={changeCity}
                  handleSubmit={() => handleSubmit('createCity')}
                  open={open}
                  handleClose={handleClose}
                  dialogValue={dialogValue}
                  setDialogValue={setDialogValue}
                  error={error}
                  value={cityValue}
                  id="city"
                  loadingButton={loadingButton}
                  showModal={false}
                />
              )}
            />
          ) : (
            <EmptyField
              name="loading_city_id"
              form={form}
              label="Select City "
            />
          )}
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="location_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                url={false}
                fieldName="name"
                label="Select Location"
                field={field}
                error={error}
                staticOptions={locations}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="destination_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                url="destinations/mixShippingDestination"
                label="Select Destination"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={''}
                modal={''}
                disableAutoComplete={company ? true : false}
              />
            )}
          />
        </Grid>

        <Grid container sx={{ paddingY: 1, borderTop: 1, marginTop: 1 }}>
          <Grid
            sx={{ paddingY: 1, paddingRight: 1 }}
            size={{
              xs: 4,
              md: 4,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="towing"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.towing?.message.length > 0 ? true : false
                    }
                    id="towing"
                    value={field.value ?? ''}
                    label="Towing Cost"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                )}
              />
            ) : (
              <EmptyField name="towing" form={form} label="Towing Cost" />
            )}
          </Grid>

          <Grid
            sx={{ paddingY: 1, paddingX: 1 }}
            size={{
              xs: 4,
              md: 4,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="shipping"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.shipping?.message.length > 0 ? true : false
                    }
                    id="shipping"
                    value={field.value ?? ''}
                    label="Shipping Cost"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <EmptyField name="shipping" form={form} label="Shipping Cost" />
            )}
          </Grid>

          <Grid
            sx={{ paddingY: 1, paddingLeft: 1 }}
            size={{
              xs: 4,
              md: 4,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="clearance"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.clearance?.message.length > 0 ? true : false
                    }
                    id="clearance"
                    value={field.value ?? ''}
                    label="Clearance Cost"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <EmptyField name="clearance" form={form} label="Clearance Cost" />
            )}
          </Grid>

          <Grid
            sx={{ paddingRight: 1 }}
            size={{
              xs: 6,
              md: 6,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="TDS_charges"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.TDS_charges?.message.length > 0 ? true : false
                    }
                    id="TDS_charges"
                    value={field.value ?? ''}
                    label="TDS Charges"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <EmptyField name="TDS_charges" form={form} label="TDS Charges" />
            )}
          </Grid>

          <Grid
            sx={{ paddingLeft: 1 }}
            size={{
              xs: 6,
              md: 6,
            }}
          >
            {cityValue || selectedItem ? (
              <Controller
                name="profit"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.TDS_charges?.message.length > 0 ? true : false
                    }
                    id="profit"
                    value={field.value ?? ''}
                    label="Profit"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            ) : (
              <EmptyField name="profit" form={form} label="Profit" />
            )}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}

export function EmptyField({ name, form, label, value = null }) {
  return (
    <Box>
      <Controller
        name={name}
        control={form.control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            sx={{ mt: 1.5 }}
            size="small"
            error={form.errors[name]?.message.length > 0 ? true : false}
            id={name}
            value={value ?? field.value ?? ''}
            label={label}
            fullWidth
            type="number"
            variant="outlined"
            helperText={error?.message}
            InputProps={{
              readOnly: true,
            }}
          />
        )}
      />
    </Box>
  );
}
