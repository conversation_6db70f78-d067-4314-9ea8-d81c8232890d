import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import DoneIcon from '@mui/icons-material/Done';
import { DatePicker } from '@mui/x-date-pickers';
import { useState } from 'react';
import { formFormatDate } from '@/configs/configs';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { useForm } from 'react-hook-form';
import QuillEditor from '@/components/mainComponents/MuiEditor';

const ApproveMixShippingRate = ({
  open,
  setOpen,
  apiUrl,
  fetchRecords,
  company = null,
}) => {
  const [status, setStatus] = useState('');
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [effectiveDate, setEffectiveDate] = useState<string | null>(null);
  const [destination, setDestination] = useState<any>(
    company ? company.destinations.id : null,
  );
  const handleClose = () => setOpen(false);

  const form = useForm({
    defaultValues: {
      subject: 'Mix Shipping Rates',
      body: 'The mix shipping rates are updated, Please check the latest.',
    },
  });

  const approveRates = async () => {
    try {
      if (status.length <= 0) {
        toast.error('Please select a status');
      } else if (effectiveDate == null || destination == null) {
        effectiveDate == null && toast.error('Please select an effective date');
        destination == null && toast.error('Please select a destination');
      } else {
        if (isEmailSent || status == 'sent') {
          if (
            !(form.getValues().subject.length > 0) ||
            !(form.getValues().body.length > 0)
          )
            return toast.error('Email subject is required');
        }
        let { data } = await axios.patch(`${apiUrl}/approve`, {
          destination_id: destination,
          effectiveDate,
          company_id: company?.id ?? null,
          status: status,
          isEmailSent: isEmailSent,
          subject: form.getValues().subject,
          body: form.getValues().body,
        });
        if (data.result) {
          setEffectiveDate(null);
          setDestination(null);
          fetchRecords();
          setOpen(false);
          toast.success('Approved Successfully!');
        }
      }
    } catch (error) {}
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 810,
    bgcolor: 'background.paper',
  };

  const states = [
    { id: 'sent', name: 'Sent' },
    { id: 'approved', name: 'Approved' },
  ];

  return (
    <Modal open={open} onClose={handleClose}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h6">Approve Mix Shipping Rate</Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider />

        <form>
          <CardContent>
            <Box>
              <Grid container>
                <Grid
                  size={{
                    md: 6,
                  }}
                >
                  <Box marginRight={1}>
                    <AutoComplete
                      sx={{ mb: 1.5 }}
                      url="destinations/mixShippingDestination"
                      label="Select Destination"
                      fieldName="name"
                      field={{
                        value: destination,
                        onChange: setDestination,
                      }}
                      error={''}
                      staticOptions={false}
                      column={''}
                      modal={''}
                      disableAutoComplete={company ? true : false}
                    />
                  </Box>
                </Grid>
                <Grid
                  size={{
                    md: 6,
                  }}
                >
                  <Box marginLeft={1}>
                    <DatePicker
                      views={['year', 'month', 'day']}
                      label="Effective Date"
                      format="yyyy/MM/dd"
                      onChange={(value) => {
                        setEffectiveDate(formFormatDate(value));
                      }}
                      slotProps={{
                        textField: {
                          variant: 'outlined',
                          size: 'small',
                          fullWidth: true,
                        },
                      }}
                    />
                  </Box>
                </Grid>

                <Grid
                  size={{
                    md: 6,
                  }}
                >
                  <FormControl
                    sx={{ marginTop: '1rem', marginRight: '1rem' }}
                    fullWidth
                    size="small"
                  >
                    <InputLabel id="demo-select-small-label">
                      Select Status
                    </InputLabel>
                    <Select
                      value={status}
                      label="Select Status"
                      onChange={(event) => {
                        setStatus(event.target.value);
                      }}
                      sx={{ mb: 1.5 }}
                    >
                      {states.map((state, index) => {
                        return (
                          <MenuItem key={index} value={state.id}>
                            {state.name}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid
                  size={{
                    md: 6,
                  }}
                >
                  {status == 'approved' && (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        width: '100%',
                      }}
                    >
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={isEmailSent}
                              onChange={() => setIsEmailSent(!isEmailSent)}
                            />
                          }
                          label="Is Email Sent"
                        />
                      </FormGroup>
                    </Box>
                  )}
                </Grid>

                {(isEmailSent || status == 'sent') && (
                  <>
                    <Grid
                      sx={{ paddingTop: '15px' }}
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => (
                          <TextField
                            size="small"
                            id="subject"
                            value={field.value ?? ''}
                            label="Email & Notification Subject"
                            fullWidth
                            error={!(form.getValues().subject.length > 0)}
                            variant="outlined"
                            onChange={(value) =>
                              field.onChange(value.target.value)
                            }
                            ref={field.ref}
                            helperText={error?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid
                      sx={{ paddingY: '20px' }}
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Typography>Email | Notification Body</Typography>
                      <Controller
                        name="body"
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <QuillEditor
                            onChange={(value) => {
                              field.onChange(value);
                            }}
                            value={field.value}
                            height={180}
                          />
                        )}
                      />
                    </Grid>
                  </>
                )}
                <Typography
                  variant="body1"
                  sx={{ p: 1 }}
                  fontSize={'12 px'}
                  color={'error'}
                >
                  <strong>Note:</strong> All approved rates will be archived,
                  all pending rates will be sent, and all sent rates will be
                  approved
                </Typography>
                <Grid
                  size={{
                    md: 12,
                  }}
                >
                  <Divider />
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'right',
                      mb: 1,
                      pt: 2,
                      gap: 1,
                    }}
                  >
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      startIcon={<CloseIcon />}
                      onClick={handleClose}
                    >
                      Close
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      size="small"
                      startIcon={<DoneIcon />}
                      onClick={() => approveRates()}
                    >
                      Approve
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </form>
      </Card>
    </Modal>
  );
};

export default ApproveMixShippingRate;
