import CStepper from '@/components/mainComponents/stepper/CStepper';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from '@/lib/axios';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { schema } from '../../../configs/general_setting/shippingCostHeader';
import InfoIcon from '@mui/icons-material/Info';
import Step1 from './Add/Step1';
import SaveIcon from '@mui/icons-material/Save';
import React from 'react';
import { Button } from '@mui/material';

export const CreateMixShippingRate = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
  company = null,
  fetchRecords,
}) => {
  const [isDone, setIsDone] = useState(false);
  const [loadingButton, setLoadingButton] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    getValues,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      towing: null,
      shipping: null,
      clearance: null,
      TDS_charges: null,
      profit: null,
      location_id: null,
      destination_id: null,
      loading_city_id: null,
      stateId: null,
      branchId: null,
      company_id: company?.id ?? null,
      update_for_same_pol_pod: false,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    getValues,
    setValue,
  };

  const submit = async (values) => {
    const data = {
      location_id: values.location_id,
      loading_city_id: values.loading_city_id,
    };
    setLoadingButton(true);
    const response = await axios.post(
      '/towing-rates/check-city-and-location',
      data,
    );
    if (response.data.result) {
      if (!isUpdate) {
        try {
          const res = await axios.post('/mix-shipping-rates', values);
          if (res.data.result == true) {
            recordManager(res.data.data, 'add');
            setIsDone(true);
            toast.success('Done successfully!');
            setLoadingButton(false);
            return true;
          } else {
            setLoadingButton(false);
            return false;
          }
        } catch (err) {
          setLoadingButton(false);
          console.log(err);
        }
      } else {
        try {
          const { data } = await axios.patch(
            `mix-shipping-rates/${selectedItems.id}`,
            values,
          );
          if (data.result === true) {
            recordManager(data.data, 'update');
            setIsDone(true);
            toast.success('Record updated successfully!');
            setLoadingButton(false);
            return true;
          }
          setLoadingButton(false);
          return false;
        } catch (err) {
          setLoadingButton(false);
          console.log(err);
        }
      }
    } else {
      setLoadingButton(false);
      toast.error('City and location does not exist in Towing Rates!');
    }
  };

  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: (
        <Step1 form={form} selectedItem={selectedItems} company={company} />
      ),
      props: isUpdate,
      async validate() {
        const isValid = await trigger([
          'shipping',
          'clearance',
          'TDS_charges',
          'profit',
          'location_id',
          'destination_id',
          'loading_city_id',
          'branchId',
          'stateId',
        ]);
        return isValid;
      },
    },
  ];

  useEffect(() => {
    if (selectedItems) {
      setValue('location_id', selectedItems?.location_id);
      setValue('destination_id', selectedItems?.destination_id);
      setValue('towing', selectedItems?.towing);
      setValue('shipping', selectedItems?.shipping);
      setValue('clearance', selectedItems?.clearance);
      setValue('TDS_charges', selectedItems?.TDS_charges);
      setValue('profit', selectedItems?.profit);
      setValue('update_for_same_pol_pod', false);
    }
  }, [selectedItems]);

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Shipping Rate' : 'Create Shipping Rate'}
        isUpdate={isUpdate}
        extraBtns={() =>
          isUpdate &&
          (!isDone ? (
            <Button
              disabled={loadingButton}
              endIcon={<SaveIcon />}
              variant="contained"
              type="submit"
              color="success"
              sx={{ mx: '2.5px' }}
              onClick={async () => {
                setValue('update_for_same_pol_pod', true);
                await handleSubmit(submit)();
                await fetchRecords();
              }}
              loading={loadingButton}
              loadingPosition="end"
            >
              <span>{'Update For same POL & POD'}</span>
            </Button>
          ) : (
            <></>
          ))
        }
      />
    </form>
  );
};
