import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Di<PERSON>r,
  Grid,
  IconButton,
  Modal,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import DoneIcon from '@mui/icons-material/Done';

const RejectMixShippingRate = ({
  open,
  setOpen,
  apiUrl,
  fetchRecords,
  company = null,
  selectedItems,
  setSelectedItems,
}) => {
  const handleClose = () => setOpen(false);

  const rejectRates = async () => {
    try {
      let { data } = await axios.patch(`${apiUrl}/reject`, {
        ids: selectedItems.map((item) => item.id),
        company_id: company?.id ?? null,
      });
      if (data.result) {
        setSelectedItems([]);

        fetchRecords();
        setOpen(false);
        toast.success('Selected items rejected Successfully!');
      }
    } catch (error) {
      toast.error('Oops! Somethings went wrong.');
    }
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 610,
    bgcolor: 'background.paper',
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h6">
            Reject {selectedItems.length} items?
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider />

        <form>
          <CardContent>
            <Box>
              <Grid container>
                <Typography variant="body1" fontSize={'14px'}>
                  Are you sure you want to reject {selectedItems.length} items?
                </Typography>
                <Grid
                  size={{
                    md: 12,
                  }}
                >
                  <Divider />
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'right',
                      mb: 1,
                      pt: 2,
                      gap: 1,
                    }}
                  >
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      startIcon={<CloseIcon />}
                      onClick={handleClose}
                    >
                      Close
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      size="small"
                      startIcon={<DoneIcon />}
                      onClick={() => rejectRates()}
                    >
                      Reject
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </form>
      </Card>
    </Modal>
  );
};

export default RejectMixShippingRate;
