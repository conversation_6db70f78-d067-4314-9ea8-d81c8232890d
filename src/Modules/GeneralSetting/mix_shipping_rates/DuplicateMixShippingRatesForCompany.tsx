import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Divider,
  Grid,
  IconButton,
  Modal,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import DoneIcon from '@mui/icons-material/Done';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { useState } from 'react';

const DuplicateMixShippingRatesForCompany = ({
  open,
  setOpen,
  apiUrl,
  fetchRecords,
  selectedItems,
  setSelectedItems,
}) => {
  const [company, setCompany] = useState<any>(null);
  const handleClose = () => {
    setOpen(false);
    setCompany(null);
  };

  const approveRates = async () => {
    try {
      if (company == null) {
        company == null && toast.error('Please select a company');
      } else {
        let { data } = await axios.post(
          `${apiUrl}/duplicate-mix-shipping-rates`,
          {
            ids: selectedItems.map((item) => item.id),
            company_id: company,
            duplicate_from_general_to_company: true,
          },
        );
        if (data.result) {
          setSelectedItems([]);
          setCompany(null);
          fetchRecords();
          setOpen(false);
          toast.success('Selected items duplicated Successfully!');
        }
      }
    } catch (error) {}
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 610,
    bgcolor: 'background.paper',
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h6">
            Duplicate Mix Shipping Rate For Company
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider />

        <form>
          <CardContent>
            <Box>
              <Grid container>
                <Grid
                  size={{
                    md: 12,
                  }}
                >
                  <AutoComplete
                    sx={{ mb: 1 }}
                    url="/autoComplete?ids="
                    label="Select Company"
                    fieldName="name"
                    field={{
                      value: company,
                      onChange: setCompany,
                    }}
                    error={''}
                    staticOptions={false}
                    column={'name'}
                    modal={'companies'}
                    disableAutoComplete={false}
                  />
                </Grid>
                <Typography
                  variant="body1"
                  sx={{ p: 1 }}
                  fontSize={'14px'}
                  color={'error'}
                >
                  <strong>Note:</strong> Only the rates with the same
                  destination as the company will be duplicated.
                </Typography>
                <Grid
                  size={{
                    md: 12,
                  }}
                >
                  <Divider />
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'right',
                      mb: 1,
                      pt: 2,
                      gap: 1,
                    }}
                  >
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      startIcon={<CloseIcon />}
                      onClick={handleClose}
                    >
                      Close
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      size="small"
                      startIcon={<DoneIcon />}
                      onClick={() => approveRates()}
                    >
                      Duplicate
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </form>
      </Card>
    </Modal>
  );
};

export default DuplicateMixShippingRatesForCompany;
