export const shiplineRateKeyMaping = { hc40: '40hc', hc45: '45hc' };
export const shiplineRateKeys = ['hc40'];
const setRegularPNLFromShiplineRates = (
  allShiplinesWithRates,
  shiplineId,
  locationId,
  shiplinePNL,
) => {
  const findShipline = allShiplinesWithRates.find(
    (item) => item.id == shiplineId,
  );

  if (findShipline && findShipline.shiplines_booking_freight_rates.length > 0) {
    const bookingFrightRate = findShipline.shiplines_booking_freight_rates[0];
    if (bookingFrightRate.freight_rates.length > 0) {
      const locationFreightRate = bookingFrightRate.freight_rates.find(
        (item) => item.point_of_loading_id == locationId,
      );
      if (locationFreightRate && locationFreightRate.rate_types.length > 0) {
        shiplineRateKeys.forEach((key) => {
          const totalKeyValue = locationFreightRate.rate_types.reduce(
            (acc, rateType) => {
              if (
                rateType.container_type === key &&
                rateType.freight_rate_category == 'origin'
              ) {
                return acc + rateType.rate_value;
              }
              return acc;
            },
            0,
          );
          const pnlKey = `regular_rate_${shiplineRateKeyMaping[key]}_average`;
          shiplinePNL[pnlKey] = totalKeyValue;
        });
      }
    }
  }
};
export const getMixShiplinePNL = (
  pnl,
  shiplineId,
  locationId,
  allShiplinesWithRates,
) => {
  let shiplinePNL = pnl.find((item) => {
    return item.shipline_id == shiplineId && item.location_id == locationId;
  });

  if (!shiplinePNL) {
    let template: any = {};
    if (pnl.length > 0) {
      template = pnl[0];

      Object.keys(template).forEach((key) => {
        if (key.endsWith('_average')) {
          template[key] = null;
        } else if (key.startsWith('spot_rate_') && key.endsWith('_qty')) {
          template[key] = 0;
        } else if (key.startsWith('regular_') && key.endsWith('_qty')) {
          template[key] = 1;
        }
      });
    }

    shiplinePNL = {
      ...template,
      location_id: locationId,
      loading_cost:
        pnl.find((item) => item.location_id == locationId)?.loading_cost ?? 0,
      total_qty: 1,
    };
  }

  setRegularPNLFromShiplineRates(
    allShiplinesWithRates,
    shiplineId,
    locationId,
    shiplinePNL, // this value will be updated in setRegularPNLFromShiplineRates function // JS magic
  );
  return shiplinePNL;
};
export const getMultipleMixShiplinePNLs = (
  pnl: any[],
  shiplineId,
  locationIds: number[],
  allShiplinesWithRates: any[],
) => {
  const updatedPNLs: any[] = [];

  locationIds?.forEach((locationId) => {
    const shiplinePNL = getMixShiplinePNL(
      pnl,
      shiplineId,
      locationId,
      allShiplinesWithRates,
    );
    updatedPNLs.push(shiplinePNL);
  });

  return updatedPNLs;
};

export const getCostsForLocations = (pnls, locationIds: number[]) => {
  if (!pnls || !Array.isArray(pnls) || locationIds.length === 0) return [];

  return locationIds.map((locationId) => {
    const shiplinePNL = pnls.find((item) => item.location_id === locationId);

    const getPNLValue = (type: string, suffix: string) =>
      shiplinePNL?.[`${type}_rate_40hc_${suffix}`] ?? 0;

    const regular = getPNLValue('regular', 'average');
    const spot = getPNLValue('spot', 'average');
    const regularQty = getPNLValue('regular', 'qty');
    const spotQty = getPNLValue('spot', 'qty');
    const totalQty = regularQty + spotQty;

    const regularPercent =
      totalQty > 0 ? Math.round((regularQty / totalQty) * 100) : 0;
    const spotPercent =
      totalQty > 0 ? Math.round((spotQty / totalQty) * 100) : 0;
    const average =
      totalQty > 0
        ? Math.trunc((regular * regularQty + spot * spotQty) / totalQty)
        : 0;

    return {
      location_id: locationId,
      regular,
      spot,
      regular_percent: regularPercent,
      spot_percent: spotPercent,
      average,
      loading_cost: shiplinePNL?.loading_cost ?? 0,
      custom: 0,
      ['custom-percentage']: average,
    };
  });
};
