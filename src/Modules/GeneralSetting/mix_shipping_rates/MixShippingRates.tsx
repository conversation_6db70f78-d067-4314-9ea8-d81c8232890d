import { Box, Chip, Container, Icon<PERSON>utton, Tab, Tabs } from '@mui/material';
import axios from '@/lib/axios';
import { useContext, useEffect, useState } from 'react';
import { MIX_SHIPPING_RATES } from '@/configs/leftSideMenu/Permissions';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import Head from 'next/head';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  HeaderInfo,
  filterContentShippingCost,
  states,
} from '@/configs/general_setting/shippingCostHeader';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { choseColor, recordManager } from '@/configs/configs';
import { formatDate } from '@/configs/vehicles/configs';
import { CreateMixShippingRate } from './CreateMixShippingRate';
import { applySavedColumns } from '@/utils/columnUtils';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import ViewSingleShippingCost from './ViewSingleMixShippingRate';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import CBasicTooltip from '@/components/mainComponents/datatable/cBasicTooltip';
import SendIcon from '@mui/icons-material/Send';
import SendMail from './SendMail';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import ContentCopyOutlinedIcon from '@mui/icons-material/ContentCopyOutlined';
import DuplicateMixShippingRates from './DuplicateMixShippingRates';
import ApproveMixShippingRate from './ApproveMixShippingRate';
import UpdateFieldCosts from './UpdateFieldCosts';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import RejectMixShippingRate from './RejectMixShippingRate';
import DifferenceIcon from '@mui/icons-material/Difference';
import RateReview from '@mui/icons-material/RateReview';
import DuplicateMixShippingRatesForCompany from './DuplicateMixShippingRatesForCompany';
import PdfModalMixShippingRate from './PdfModalMixShippingRate';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import PdfMixShippingRate2 from './PdfModalMixShippingRate2';
import React from 'react';
import UploadMixShippingExcel from './UploadMixShippingExcel';
import { ChevronDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import CalculateMixShippingRate from './CalculateMixShippingRate';
import { getCostsForLocations } from './config/mix-shipping-rate-helper';
type statusType = 'pending' | 'approved' | 'archived';
const MixShippingRates = ({
  apiUrl,
  defaultHeaders,
  company = null,
  customStatus = null,
}) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const [tableRecords, setTableRecords] = useState([]);
  const [tabs, setTabs] = useState([]);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [cancelToken1, setCancelToken1] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [loading, setLoading] = useState(false);
  const [openUploadExcel, setOpenUploadExcel] = useState(false);
  //@ts-ignore
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [show, setShow] = useState(false);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [duplicateMixShippingRates, setDuplicateMixShippingRates] =
    useState(false);
  const [
    duplicateMixShippingRatesForCompany,
    setDuplicateMixShippingRatesForCompany,
  ] = useState(false);
  const [approve, setApprove] = useState(false);
  const [showCalculateProfit, setShowCalculateProfit] = useState(false);
  const [reject, setReject] = useState(false);
  const [showDownload, setShowDownload] = useState(false);
  const [showDownload2, setShowDownload2] = useState(false);
  const [status, setStatus] = useState<statusType>('pending');
  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  // Mix shipping rate booking costs
  const [shippingRates, setShippingRates] = useState<any>({});
  const [bookingCostValue, setBookingCostValue] = useState({});
  const [location, setLocation] = useState<number[]>([]);
  const costs = getCostsForLocations(shippingRates?.PNL, location);
  const [bookingStatus, setBookingStatus] = useState('average');
  const pageName = 'Mix Shipping Rates';

  const form = useForm({
    defaultValues: {
      subject: 'Mix Shipping Rates',
      body: 'The mix shipping rates are updated, Please check the latest.',
      company_ids: [],
      towing: null,
      all_customers: false,
      is_special: false,
    },
  });

  const fetchRecords = async () => {
    try {
      setLoading(true);
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);

      let { data } = await axios.get(`${apiUrl}/find/${options.tab}`, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          company_id: company?.id,
          filterData: JSON.stringify(options.filterData),
          status: customStatus ? customStatus : status,
        },
      });
      setLoading(false);
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      setLoading(false);
      console.error(error);
    }
  };

  // shipline-location
  const fetchShippingRatesRecords = async () => {
    try {
      if (cancelToken1) {
        await cancelToken1.abort();
      }
      const controller = new AbortController();
      setCancelToken1(controller);

      let { data } = await axios.get(`${apiUrl}/shipline-location`, {
        signal: controller.signal,
        params: {
          destination_id: company?.destination_id
            ? company?.destination_id
            : options.tab,
        },
      });
      setShippingRates(data);
    } catch (error) {
      console.error(error);
    }
  };
  const updateCustomPNLs = async (updates) => {
    try {
      await axios.put(`${apiUrl}/custom-pnls`, {
        updates,
      });
    } catch (error) {
      toast.error('Error while updating custom P&L!', {});
    }
  };
  useEffect(() => {
    if ((options.tab !== 'all' && !company) || company)
      fetchShippingRatesRecords();
  }, [options.tab, company?.destination_id, company]);

  const applyBookingCost = () => {
    if ((options.tab !== 'all' && !company) || company) {
      setTableRecords((prev) =>
        prev?.map((item) => {
          if (location.includes(item.location_id)) {
            const loadingCost =
              shippingRates?.loading_costs?.find((item) =>
                location.includes(item.id),
              )?.container_loading_cost ?? 0;
            let pro = 0;
            if (bookingStatus === 'shipline') {
              pro = bookingCostValue[bookingStatus];
            } else if (bookingStatus === 'custom')
              pro = bookingCostValue[bookingStatus] ?? 0;
            else {
              const signleCosts = costs.find(
                (cost) => cost.location_id === item.location_id,
              );
              pro = signleCosts[bookingStatus] ?? 0;
            }
            pro = Math.round((pro + loadingCost) / 4);
            return {
              ...item,
              profit: pro,
              rate_analysis: true,
              last_profit:
                item.last_profit !== undefined ? item.last_profit : item.profit,
            };
          }
          return { ...item };
        }),
      );
      if (bookingStatus === 'custom') {
        updateCustomPNLs([
          {
            key: `dest(${company ? company?.destination_id : options.tab}).locs(${location?.join(',')}).mix_rate(rate_40hc).custome_value`,
            value: bookingCostValue[bookingStatus],
          },
        ]);
      }
    }
  };
  const clearBookingCost = () => {
    if ((options.tab !== 'all' && !company) || company)
      setTableRecords((prev) =>
        prev?.map((item) => {
          if (location.includes(item.location_id)) {
            const loadingCost =
              shippingRates?.loading_costs?.find((item) =>
                location.includes(item.id),
              )?.container_loading_cost ?? 0;
            let pro = 0;
            if (bookingStatus === 'shipline')
              pro = bookingCostValue[bookingStatus];
            else
              pro =
                costs.find((cost) => cost.location_id === item.location_id)[
                  bookingStatus
                ] ?? 0;
            pro = Math.round((pro + loadingCost) / 4);
            return {
              ...item,
              profit: item.last_profit ? item.last_profit : undefined,
              rate_analysis: false,
            };
          }
          return {
            ...item,
            profit: item.last_profit ? item.last_profit : undefined,
            rate_analysis: false,
          };
        }),
      );
  };
  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
    status,
    customStatus,
  ]);

  useEffect(() => {
    const fetchTabs = async () => {
      try {
        if (cancelToken) {
          await cancelToken.abort();
        }
        const controller = new AbortController();
        let { data } = await axios.get('destinations/mixShippingDestination', {
          signal: controller.signal,
        });
        setTabs(data?.data);
      } catch (err) {
        console.log(err);
      }
    };

    fetchTabs();
  }, []);

  //@ts-ignore
  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  const sendEmail = async (values: any) => {
    if (values.all_customers) delete values.company_ids;
    values.destination_id = options.tab;
    try {
      setLoading(true);
      const res = await axios.post(`${apiUrl}/sendMail`, values);
      if (res?.data?.result) {
        setShow(false);
        toast.success('Email has been sent successfully!');
      }
      setLoading(false);
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  };

  const changeTab = async (tab: any) => {
    setOptions({ ...options, ...{ tab } });
  };
  return perms && !perms?.includes(MIX_SHIPPING_RATES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Mix Shipping Rates</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        {company == null && <PageHeader breadcrumbs={HeaderInfo()} />}
        {company == null && (
          <div className="flex flex-row items-center gap-x-5">
            <Tabs
              sx={{ fontSize: 10, minHeight: '35px' }}
              className="customTab"
              value={options.tab}
              onChange={(_event, val) => changeTab(val)}
              indicatorColor="primary"
              textColor="inherit"
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
              aria-label="full width tabs example"
            >
              <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
              {tabs?.map((t) => (
                <Tab
                  sx={{ fontSize: 10 }}
                  key={t.id}
                  label={t.name}
                  value={t.id}
                />
              ))}
            </Tabs>
            <DropdownMenu>
              <DropdownMenuTrigger>
                <div className="flex items-center gap-1 text-sm capitalize">
                  <span>{status}</span>
                  <ChevronDown height={14} width={14} />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="z-[99999] max-h-[400px] overflow-y-auto">
                <DropdownMenuLabel className="text-center">
                  Status
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {['pending', 'approved', 'archived'].map(
                  (item: statusType, index) => (
                    <DropdownMenuCheckboxItem
                      key={index}
                      checked={false}
                      className="cursor-pointer items-start capitalize"
                      onClick={() => setStatus(item)}
                    >
                      {item}
                    </DropdownMenuCheckboxItem>
                  ),
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
        <DataTable3
          PageAction={
            <PageAction
              showUploadButton={options.tab !== 'all' ? true : false}
              selectedItems={selectedItems}
              title={'Mix Shipping Rates'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              onUpload={() => setOpenUploadExcel(true)}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} shipping cost ? `}
              dialogTitle={`Delete shipping Item`}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              customComponent={
                <>
                  {perms?.includes(MIX_SHIPPING_RATES?.VIEW) &&
                    (options.tab != 'all' || company != null) && (
                      <CBasicTooltip
                        onClick={() => setShowDownload2(true)}
                        title="PDF Download"
                        icon={<PictureAsPdfIcon />}
                      />
                    )}
                  {selectedItems.length > 0 &&
                    selectedItems.findIndex((el) => el.status == 'approved') ==
                      -1 && (
                      <AppTooltip
                        key={'rejectMixShippingRate'}
                        title={'Reject Mix Shipping Rates'}
                      >
                        <IconButton
                          color="error"
                          onClick={() => {
                            setReject(true);
                          }}
                        >
                          <CancelIcon />
                        </IconButton>
                      </AppTooltip>
                    )}
                  {
                    <AppTooltip
                      key={'approveMixShippingRates'}
                      title={'Approve Mix Shipping Rates'}
                    >
                      <IconButton
                        color="success"
                        onClick={() => {
                          setApprove(true);
                        }}
                      >
                        <CheckCircleOutlineIcon />
                      </IconButton>
                    </AppTooltip>
                  }
                  {selectedItems.length > 0 &&
                    selectedItems.findIndex(
                      (item) => item.status === 'pending',
                    ) === -1 && (
                      <AppTooltip
                        key={'duplicateMixShippingRates'}
                        title={'Duplicate Mix Shipping Rates'}
                      >
                        <IconButton
                          onClick={() => {
                            setDuplicateMixShippingRates(true);
                          }}
                        >
                          <ContentCopyOutlinedIcon />
                        </IconButton>
                      </AppTooltip>
                    )}
                  {selectedItems.length > 0 &&
                    selectedItems.findIndex(
                      (item) => item.status === 'pending',
                    ) === -1 &&
                    company == null && (
                      <AppTooltip
                        key={'duplicateForCompany'}
                        title={'Duplicate Mix Shipping Rates For company'}
                      >
                        <IconButton
                          onClick={() => {
                            setDuplicateMixShippingRatesForCompany(true);
                          }}
                        >
                          <DifferenceIcon />
                        </IconButton>
                      </AppTooltip>
                    )}
                  {perms?.includes(MIX_SHIPPING_RATES?.SEND_EMAIL) &&
                    options.tab != 'all' && (
                      <CBasicTooltip
                        onClick={() => setShow(true)}
                        title="Send To Email"
                        icon={<SendIcon />}
                      />
                    )}
                  {(options.tab !== 'all' ||
                    (options.tab === 'all' && company)) && (
                    <AppTooltip
                      key={'calculateMixShippingRatesProfit'}
                      title={'Calculate Mix Shipping Rates Profit'}
                    >
                      <IconButton
                        onClick={() => {
                          setShowCalculateProfit(true);
                        }}
                      >
                        <RateReview />
                      </IconButton>
                    </AppTooltip>
                  )}
                </>
              }
              showAddButton={perms?.includes(MIX_SHIPPING_RATES?.CREATE)}
              showEditButton={
                perms?.includes(MIX_SHIPPING_RATES?.UPDATE) &&
                selectedItems.length &&
                (selectedItems?.[0]?.status == 'pending' ||
                  selectedItems?.[0]?.status == 'sent')
              }
              showDeleteButton={perms?.includes(MIX_SHIPPING_RATES?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          setItems={setTableRecords}
          activeTab={options.tab}
          tableName="shippingCost"
          //end default props
          //start custom props

          created_by={({ mix_shipping_rates_created_by }) => (
            <>
              {mix_shipping_rates_created_by?.fullname}
              {mix_shipping_rates_created_by?.departments?.name &&
                ' | ' + mix_shipping_rates_created_by?.departments?.name}
            </>
          )}
          location_name={({ locations }) => locations?.name}
          destination_name={({ destinations }) => destinations?.name}
          city_name={({ loading_cities }) => loading_cities?.city_name}
          branch_name={({ loading_cities }) =>
            loading_cities?.loading_states?.name
          }
          state_name={({ loading_cities }) =>
            loading_cities?.loading_states?.parent?.name
          }
          effective_date={({ effective_date }) => formatDate(effective_date)}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          tax_duty={() => '5%+5% '}
          total={(item) =>
            (item.towing ?? 0) +
            (item.shipping ?? 0) +
            (item.clearance ?? 0) +
            (item.TDS_charges ?? 0)
          }
          towing={(item) => (
            <Box sx={{ textAlign: 'center' }}>{item.towing ?? 0}</Box>
          )}
          shipping={(item) =>
            item.status === 'pending' ? (
              <UpdateFieldCosts
                field={'shipping'}
                setTableRecords={setTableRecords}
                item={item}
                apiUrl={apiUrl}
              />
            ) : (
              <Box sx={{ textAlign: 'center' }}>{item.shipping}</Box>
            )
          }
          clearance={(item) =>
            item.status === 'pending' ? (
              <UpdateFieldCosts
                field={'clearance'}
                setTableRecords={setTableRecords}
                item={item}
                apiUrl={apiUrl}
              />
            ) : (
              <Box sx={{ textAlign: 'center' }}>{item.clearance}</Box>
            )
          }
          TDS_charges={(item) =>
            item.status === 'pending' ? (
              <UpdateFieldCosts
                field={'TDS_charges'}
                setTableRecords={setTableRecords}
                item={item}
                apiUrl={apiUrl}
              />
            ) : (
              <Box sx={{ textAlign: 'center' }}>{item.TDS_charges}</Box>
            )
          }
          profit={(item) => {
            const total =
              (item.towing ?? 0) +
              (item.shipping ?? 0) +
              (item.clearance ?? 0) +
              (item.TDS_charges ?? 0);
            let style = {};
            if (item.profit) {
              style =
                item.status === 'pending'
                  ? { color: total > item.profit ? 'green' : 'red' }
                  : {};
            }
            return item.status === 'pending' && !item?.rate_analysis ? (
              <UpdateFieldCosts
                field={'profit'}
                setTableRecords={setTableRecords}
                item={item}
                apiUrl={apiUrl}
              />
            ) : (
              <Box sx={{ textAlign: 'center', ...style }}>{item.profit}</Box>
            );
          }}
          status={({ status }) => (
            <Chip
              size="small"
              label={status}
              sx={{
                backgroundColor: choseColor(status),
                color: 'white',
              }}
            />
          )}
        />
      </Container>
      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Mix Shipping Rates"
        content={filterContentShippingCost}
      />
      <CreateMixShippingRate
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems[0]}
        isUpdate={isUpdate}
        company={company}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
        fetchRecords={fetchRecords}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
      ></ColumnDialog>
      <ViewSingleShippingCost data={viewData} setView={setView} show={view} />
      <SendMail
        tab={options?.tab}
        show={show}
        setShow={setShow}
        form={form}
        loading={loading}
        sendEmail={form.handleSubmit(sendEmail)}
        company={company}
      />
      <ApproveMixShippingRate
        setOpen={setApprove}
        open={approve}
        apiUrl={apiUrl}
        fetchRecords={fetchRecords}
        company={company}
      />
      <CalculateMixShippingRate
        setOpen={setShowCalculateProfit}
        open={showCalculateProfit}
        shippingRates={shippingRates}
        states={states}
        location={location}
        setLocation={setLocation}
        costs={costs}
        setStatus={setBookingStatus}
        status={bookingStatus}
        setBookingCostValue={setBookingCostValue}
        apply={applyBookingCost}
        clearBookingCost={clearBookingCost}
        destination_id={
          options.tab !== 'all' && !company
            ? options.tab
            : company?.destination_id
        }
      />
      <RejectMixShippingRate
        setOpen={setReject}
        open={reject}
        apiUrl={apiUrl}
        fetchRecords={fetchRecords}
        company={company}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
      />
      <DuplicateMixShippingRates
        open={duplicateMixShippingRates}
        onDeny={setDuplicateMixShippingRates}
        apiUrl={apiUrl}
        setDuplicateMixShippingRates={setDuplicateMixShippingRates}
        fetchRecords={fetchRecords}
        selectedItems={selectedItems}
        company={company}
      />
      <DuplicateMixShippingRatesForCompany
        setOpen={setDuplicateMixShippingRatesForCompany}
        open={duplicateMixShippingRatesForCompany}
        apiUrl={apiUrl}
        fetchRecords={fetchRecords}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
      />
      <PdfModalMixShippingRate
        showDownload={showDownload}
        title={'Mix Shipping Rates'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        options={options}
        apiUrl={apiUrl}
        totalItems={totalItems}
      />
      <PdfMixShippingRate2
        open={showDownload2}
        setOpen={setShowDownload2}
        company={company}
        apiUrl={apiUrl}
        options={options}
      />
      <UploadMixShippingExcel
        title={tabs?.filter((tab) => tab.id == options.tab)}
        open={openUploadExcel}
        setOpen={setOpenUploadExcel}
      />
    </>
  );
};
export default MixShippingRates;
