import SendIcon from '@mui/icons-material/Send';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import CloseIcon from '@mui/icons-material/Close';
import { useEffect, useState } from 'react';

export default function SendMail({
  show,
  setShow,
  form,
  sendEmail,
  loading,
  tab,
  company = null,
}) {
  const theme = useTheme();
  const [all, setAll] = useState(true);

  useEffect(() => {
    if (company) {
      form.setValue('company_ids', [company.id]);
      form.setValue('is_special', true);
    }
  }, [company]);

  return (
    <Modal
      onClose={() => {
        form.setValue('all_customers', false);
        setShow(false);
      }}
      aria-labelledby="customized-dialog-title"
      open={show}
    >
      <Card
        style={{
          height: '80vh',
          position: 'absolute' as 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: ` ${theme.colors.alpha.white[100]}`,
          border: `2px solid ${theme.colors.alpha.black[50]}`,
          boxShadow: '24',
          borderRadius: '10px',
          width: '80vw',
        }}
      >
        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'end' }}>
          <IconButton
            onClick={() => {
              setShow(false);
              form.setValue('all_customers', false);
            }}
            aria-label="delete"
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <CardContent sx={{ height: '80% !important' }}>
          <form onSubmit={sendEmail}>
            <Grid container sx={{ paddingX: '30px' }}>
              <Grid
                sx={{ paddingBottom: '10px', borderBottom: 1 }}
                size={{ xs: 12, md: 12 }}
              >
                <Typography variant="h5">
                  Send Mix Shipping Rates{' '}
                  {company ? `to (${company.name})` : ''}{' '}
                </Typography>
              </Grid>

              <Grid sx={{ paddingTop: '15px' }} size={{ xs: 12, md: 12 }}>
                <Controller
                  name="subject"
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors?.subject?.message.length > 0 ? true : false
                      }
                      id="subject"
                      value={field.value ?? ''}
                      label="Email & Notification Subject"
                      fullWidth
                      variant="outlined"
                      onChange={(value) => field.onChange(value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid sx={{ paddingY: '20px' }} size={{ xs: 12, md: 12 }}>
                <Typography>Email | Notification Body</Typography>
                <Controller
                  name="body"
                  control={form.control}
                  defaultValue=""
                  render={({ field }) => (
                    <QuillEditor
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                      height={180}
                    />
                  )}
                />
              </Grid>
              {company == null && (
                <Grid sx={{ paddingBottom: '15px' }} size={{ xs: 12, md: 12 }}>
                  <Controller
                    control={form.control}
                    name="all_customers"
                    render={({ field }) => (
                      <FormControl component="fieldset" variant="standard">
                        <FormGroup>
                          <FormControlLabel
                            control={
                              <Checkbox
                                inputRef={field.ref}
                                checked={field.value}
                                onChange={(event) => {
                                  setAll(field.value);
                                  field.onChange(event);
                                }}
                                name="all_customers"
                              />
                            }
                            sx={{ '& .MuiSvgIcon-root': { fontSize: 28 } }}
                            label="All Current Destination Mix customers"
                          />
                        </FormGroup>
                      </FormControl>
                    )}
                  />
                </Grid>
              )}
              <Grid sx={{ paddingBottom: '15px' }} size={{ xs: 12, md: 12 }}>
                {all && company == null ? (
                  <Controller
                    name={`company_ids`}
                    control={form.control}
                    render={({ field }) => {
                      const item = {
                        name: 'company_id',
                        label: 'Company',
                        type: 'autocomplete',
                        url: `/autoComplete?column=name&modal=mix_companies&destination_id=${tab != 'all' ? tab : 0}&id=${company?.id ?? ''}`,
                        keyName: 'name',
                      };
                      return (
                        <FilterAutocomplete
                          url={item.url}
                          label={item.label}
                          name={item.name}
                          keyName={item.keyName ?? item.name}
                          values={field.value}
                          onChange={(event) => {
                            field.onChange(event);
                          }}
                        />
                      );
                    }}
                  />
                ) : (
                  company == null && (
                    <Controller
                      name=""
                      control={form.control}
                      render={({ field, fieldState: { error } }) => (
                        <TextField
                          size="small"
                          value={field.value ?? ''}
                          label={'Company'}
                          fullWidth
                          variant="outlined"
                          helperText={error?.message}
                          InputProps={{ readOnly: true }}
                        />
                      )}
                    />
                  )
                )}
              </Grid>

              <Grid size={{ xs: 6, md: 6 }}>
                <Typography sx={{ fontSize: 13 }}>
                  <span style={{ fontWeight: 'bold' }}>NOTE:</span> <br />
                  <Box sx={{ paddingLeft: 2 }}>
                    <p>
                      1 - The mix shipping rates list will be generate as a PDF
                      file and will be attached to the email.
                    </p>
                    <p>2 - Notification will be sent too.</p>
                  </Box>
                </Typography>
              </Grid>
              <Grid sx={{ textAlign: 'right' }} size={{ xs: 6, md: 6 }}>
                <Button
                  endIcon={<CloseIcon />}
                  size="small"
                  variant="contained"
                  color="error"
                  sx={{ mx: '8px' }}
                  onClick={() => setShow(false)}
                  loadingPosition="end"
                >
                  <span>{'Cancel'}</span>
                </Button>
                <Button
                  /* disabled={loadingButton} */
                  endIcon={<SendIcon />}
                  size="small"
                  variant="contained"
                  type="submit"
                  color="success"
                  sx={{ mx: '8px' }}
                  loading={loading}
                  loadingPosition="end"
                >
                  <span>{'Send Email'}</span>
                </Button>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Modal>
  );
}
