import {
  <PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DoneIcon from '@mui/icons-material/Done';
import { useEffect, useState } from 'react';
import { getMultipleMixShiplinePNLs } from './config/mix-shipping-rate-helper';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';

function sumFreightRates(data, pointOfLoadingIds) {
  let total = 0;
  const ids = Array.isArray(pointOfLoadingIds)
    ? pointOfLoadingIds
    : [pointOfLoadingIds];
  for (const entry of data) {
    for (const freightEntry of entry.freight_rates) {
      if (ids.includes(freightEntry.point_of_loading_id)) {
        total += freightEntry.rate_types.reduce((sum, rate) => {
          return sum + rate.rate_value;
        }, 0);
      }
    }
  }
  return total;
}

type shiplineType = { name: string; value: number; id: number };

const CalculateMixShippingRate = ({
  open,
  setOpen,
  shippingRates = {} as any,
  states = [],
  location,
  setLocation,
  costs,
  status,
  setStatus,
  setBookingCostValue,
  apply,
  clearBookingCost,
  destination_id,
}) => {
  const [bookingCustomValues, setBookingCustomValues] = useState({});
  const [shipline, setShipline] = useState<shiplineType | null>(null);
  const handleClose = () => {
    clearBookingCost?.();
    setCustomValue(0);
    setOpen(false);
    if (shippingRates?.loading_costs?.length > 0)
      setLocation([shippingRates?.loading_costs?.at(0)?.id]);
    setStatus('average');
  };
  const [customValue, setCustomValue] = useState(null);
  useEffect(() => {
    if (shippingRates?.loading_costs?.length > 0)
      setLocation([shippingRates?.loading_costs?.at(0)?.id]);
  }, [shippingRates?.loading_costs]);

  useEffect(() => {
    setBookingCustomValues(
      Object.entries(shippingRates?.customPNL ? shippingRates?.customPNL : {})
        .filter(([key]) => key.includes(`dest(${destination_id})`))
        .reduce(
          (acc: object, [key, value]: [key: string, value: string]) => ({
            ...acc,
            [key.replace(/dest\([^)]*\)\.?/, '')]: +value,
          }),
          {},
        ),
    );
  }, [destination_id, shippingRates?.customPNL]);

  const multipleShiplinesWithValue = shippingRates?.shiplines?.filter(
    (shipline) => {
      const pnlArray = getMultipleMixShiplinePNLs(
        shippingRates?.PNL,
        shipline.id,
        location,
        shippingRates?.shiplines,
      );
      return pnlArray.some((pnl) => pnl[`regular_rate_40hc_average`]);
    },
  );
  useEffect(() => {
    if (status === 'custom')
      setCustomValue(
        bookingCustomValues[
          `locs(${location?.join(',')}).mix_rate(rate_40hc).custome_value`
        ] ?? 0,
      );
  }, [status, location]);

  useEffect(() => {
    setBookingCostValue((prev) => {
      return {
        ...prev,
        [`${status}`]:
          status === 'shipline'
            ? shipline?.value
            : status === 'custom'
              ? customValue
              : costs[status],
      };
    });
  }, [status, location, customValue, shipline]);

  return (
    <Modal open={open} onClose={handleClose}>
      <Card
        sx={{
          mt: { xs: 2, sm: 8, md: 15 },
          borderRadius: 2,
          mx: 'auto',
          maxWidth: { xs: '400px', sm: 200 * 3 + 'px' },
          bgcolor: 'background.paper',
          boxShadow: 24,
          position: 'relative',
        }}
      >
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h6">
            Calculate Mix Shipping Rate Profit
          </Typography>
          <IconButton
            aria-label="close"
            sx={{ color: 'grey' }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider />

        <CardContent>
          <Box>
            <Grid container size={{ md: 12 }}>
              <Grid size={{ md: 12 }}>
                <FilterAutocomplete
                  url={''}
                  label={'Select locations'}
                  name={'locations'}
                  keyName={'name'}
                  values={location}
                  staticOptions={shippingRates?.loading_costs?.map((item) => ({
                    id: item.id,
                    name: item.name,
                  }))}
                  onChange={(newValue) => setLocation(newValue)}
                />
              </Grid>

              <Grid
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '8px',
                  alignItems: 'center',
                }}
                size={{ md: 12 }}
              >
                {status === 'custom' && (
                  <Grid
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '8px',
                      alignItems: 'center',
                    }}
                    size={{ xs: 12, md: 12 }}
                  >
                    <TextField
                      size="small"
                      id="subject"
                      value={customValue}
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) =>
                        setCustomValue(Number(value.target.value))
                      }
                    />
                  </Grid>
                )}
                {status === 'shipline' && (
                  <Grid
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '8px',
                      alignItems: 'center',
                    }}
                    size={{ md: 12 }}
                  >
                    <p>{shipline?.value ?? 0}</p>
                    <FormControl
                      sx={{ marginTop: '1rem', marginRight: '1rem' }}
                      fullWidth
                      size="small"
                    >
                      <InputLabel id="demo-select-small-label">
                        Select shipline
                      </InputLabel>
                      <Select
                        value={shipline?.name}
                        label="Select shipline"
                        onChange={(event) => {
                          setShipline((prev) => {
                            return { ...prev, name: event.target.value };
                          });
                        }}
                        sx={{ mb: 1.5 }}
                      >
                        {multipleShiplinesWithValue?.map((state, index) => {
                          return (
                            <MenuItem
                              key={index}
                              value={state.id}
                              onClick={() => {
                                let value = 0;
                                if (state.shiplines_booking_freight_rates) {
                                  value = sumFreightRates(
                                    state.shiplines_booking_freight_rates,
                                    location ?? -1,
                                  );
                                }
                                setShipline((prev) => {
                                  return {
                                    ...prev,
                                    value: value,
                                    id: state.id,
                                  };
                                });
                                setBookingCostValue((prev) => {
                                  return { ...prev, [`${status}`]: value };
                                });
                              }}
                            >
                              {state.name}
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                <FormControl
                  sx={{ marginTop: '1rem', marginRight: '1rem' }}
                  fullWidth
                  size="small"
                >
                  {status !== 'custom' && <p>{costs[status]}</p>}
                  <InputLabel id="demo-select-small-label">
                    Select Status
                  </InputLabel>
                  <Select
                    value={status}
                    label="Select Status"
                    onChange={(event) => {
                      setStatus(event.target.value);
                      setBookingCostValue((prev) => {
                        if (
                          event.target.value !== 'custom' ||
                          event.target.value !== 'shipline'
                        )
                          return { ...prev, [`${event.target.value}`]: 0 };
                        return {
                          ...prev,
                          [`${event.target.value}`]: costs[event.target.value],
                        };
                      });
                    }}
                    sx={{ mb: 1.5 }}
                  >
                    {states.map((state, index) => {
                      return (
                        <MenuItem key={index} value={state.id}>
                          {state.name}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>

              <Grid size={{ md: 12 }}>
                <Divider />
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'right',
                    mb: 1,
                    pt: 2,
                    gap: 1,
                  }}
                >
                  <Button
                    variant="contained"
                    color="error"
                    size="small"
                    startIcon={<CloseIcon />}
                    onClick={handleClose}
                  >
                    Close
                  </Button>
                  <Button
                    variant="contained"
                    color="success"
                    size="small"
                    startIcon={<DoneIcon />}
                    onClick={() => {
                      setOpen(false);
                      apply?.();
                    }}
                  >
                    Apply
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Modal>
  );
};

export default CalculateMixShippingRate;
