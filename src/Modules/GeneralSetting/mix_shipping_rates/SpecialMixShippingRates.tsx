import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  HeaderInfo,
  filterContentCompanies,
} from '@/Modules/GeneralSetting/Companies/companyComponents/companyHeader';
import { Box, Container, IconButton, Typography } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { COMPANIES } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { formatDate } from '@/configs/vehicles/configs';
import { CreateCompanies } from '../Companies/CreateCompanies';
import ViewSingleRowCompany from '../Companies/CompanyViewProfile';
import { copyORViewFun, recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import StarIcon from '@/icons/star-icon';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';
import SpecialMixShippingRatesDrawer from '../Companies/SpecialMixShippingRatesDrawer';

const NewCompanies = ({ apiUrl, defaultHeaders }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [reload, setReload] = useState(0);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const pageName = 'companies';
  const [loading, setLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [mixRateSelectedCompany, setMixRateSelectedCompany] = useState(null);
  ////////////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    reload,
  ]);

  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  return perms && !perms?.includes(COMPANIES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Special Mix Shipping Rates</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Special Mix Shipping Rates'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              showEditButton={perms?.includes(COMPANIES?.UPDATE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="company"
          name={({ name, tier_level }) => (
            <Box
              sx={{
                whiteSpace: 'wrap',
                // textAlign: 'center',
                minWidth: 110,
                maxWidth: 220,
                display: 'flex',
                cursor: 'pointer',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <p>{name}</p>
              {tier_level == 'golden' && (
                <div style={{ minWidth: 16, width: 16 }}>
                  <StarIcon
                    height="16px"
                    width="16px"
                    style={{ color: '#fcd34d' }}
                  />
                </div>
              )}
            </Box>
          )}
          created_by={({ users_companies_created_byTousers }) => (
            <>
              {users_companies_created_byTousers?.fullname}
              {users_companies_created_byTousers?.departments?.name &&
                ' | ' + users_companies_created_byTousers?.departments?.name}
            </>
          )}
          parent_id={({ parent_company }) => <>{parent_company?.name}</>}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          is_belong_to_used_car={({ is_belong_to_used_car }) => (
            <Typography variant="body2" noWrap>
              {is_belong_to_used_car == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          mix={({ mix }) => (
            <Typography variant="body2" noWrap>
              {mix == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          contract={({ contract }) => contract && <Box></Box>}
          trade_license={({ trade_license }) => trade_license && <Box></Box>}
          id_passport={({ id_passport }) => id_passport && <Box></Box>}
          complete={({ complete }) => (
            <Typography variant="body2" noWrap>
              {complete == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          mix_halfcut={({ mix_halfcut }) => (
            <Typography variant="body2" noWrap>
              {mix_halfcut == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          complete_halfcut={({ complete_halfcut }) => (
            <Typography variant="body2" noWrap>
              {complete_halfcut == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          is_special_rate={({ is_special_rate }) => (
            <Typography variant="body2" noWrap>
              {is_special_rate == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          id={(item) =>
            copyORViewFun({
              getSingleRow,
              copy: item?.id,
              display: item?.id,
              id: item,
            })
          }
          destinations={({ destinations }) => <Box>{destinations?.name}</Box>}
          mix_special_rates={({ is_mix_special_rate, ...company }) => {
            return is_mix_special_rate ? (
              <AppTooltip title={'Mix Special Rates'}>
                <IconButton
                  color="secondary"
                  onClick={() => {
                    setMixRateSelectedCompany(company);
                  }}
                >
                  <LocalAtmIcon />
                </IconButton>
              </AppTooltip>
            ) : (
              <></>
            );
          }}
        />
      </Container>
      <SpecialMixShippingRatesDrawer
        show={mixRateSelectedCompany != null}
        setShow={(value) =>
          setMixRateSelectedCompany(
            value == false ? null : mixRateSelectedCompany,
          )
        }
        company={mixRateSelectedCompany}
      />

      <CreateCompanies
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        setReload={setReload}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      <ViewSingleRowCompany data={viewData} setView={setView} show={view} />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Companies"
        content={filterContentCompanies}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
      ></ColumnDialog>

      <PdfModal
        options={options}
        open={showDownload}
        pdf_title={'Companies'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        fetchDownloadRecords={fetchDownloadRecords}
        tableRecords={tableRecords}
        headers={selectedHeaders}
        created_by={({ users_companies_created_byTousers }) =>
          users_companies_created_byTousers?.fullname
        }
        created_at={({ created_at }) => formatDate(created_at)}
        updated_at={({ updated_at }) => formatDate(updated_at)}
        is_belong_to_used_car={({ is_belong_to_used_car }) =>
          is_belong_to_used_car == true ? 'Yes' : 'No'
        }
        destinations={({ destinations }) => destinations?.name}
      ></PdfModal>
    </>
  );
};

export default NewCompanies;
