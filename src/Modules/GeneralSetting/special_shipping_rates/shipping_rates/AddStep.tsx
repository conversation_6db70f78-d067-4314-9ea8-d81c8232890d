import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  IconButton,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Controller, useFieldArray } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import CloseIcon from '@mui/icons-material/Close';
import { formFormatDate, static_locations } from '@/configs/configs';

export default function AddStep({ form, destinations, shiplines }) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'rates',
  });

  const locations = static_locations.map((e) => ({
    id: e?.id,
    label: e?.name,
  }));

  const theme = useTheme();
  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        Special Shipping Rates Details
      </Typography>
      <Card
        sx={{
          mb: 1,
          backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
        }}
      >
        <CardContent>
          <Grid container spacing={2} sx={{ paddingBottom: '10px' }}>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Controller
                name={`company_id`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url="autoComplete"
                    label="Select Company"
                    fieldName={'name'}
                    field={field}
                    error={error}
                    staticOptions={false}
                    column={'name'}
                    modal={'companies'}
                    isSpecialRate={true}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Controller
                control={form.control}
                name="effective_date"
                render={({ field, fieldState: { error } }) => (
                  <DatePicker
                    views={['year', 'month', 'day']}
                    label="Effective Date"
                    value={!field.value ? null : dayjs(field.value).toDate()}
                    format="yyyy/MM/dd"
                    onChange={(e) => field.onChange(formFormatDate(e))}
                    inputRef={field.ref}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !!error,
                        helperText: error?.message,
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Controller
                name="tds_amount"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.tds_amount?.message.length > 0 ? true : false
                    }
                    id="tds_amount"
                    value={field.value ?? ''}
                    label="TDS Amount"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>

            <Grid
              size={{
                xs: 12,
                md: 12,
              }}
            >
              <Controller
                name="remark"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.remark?.message.length > 0 ? true : false
                    }
                    id="remark"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="Remark"
                    fullWidth
                    multiline
                    rows={3}
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Box component={'hr'} sx={{ my: 1 }}></Box>
          <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
            Rates Details
          </Typography>
          {fields.map((item: any, i) => (
            <Box
              sx={{
                py: 2,
                px: 2,
                pr: 6,
                mb: 1,
                border: '1px solid gray',
                borderRadius: '6px',
                position: 'relative',
                marginBottom: 2,
              }}
              key={item.id}
            >
              <IconButton
                size="small"
                color="error"
                onClick={() => remove(i)}
                aria-label="delete"
                sx={{ position: 'absolute', right: 10, top: 10 }}
              >
                <CloseIcon />
              </IconButton>
              <Grid container spacing={1} sx={{ paddingBottom: '1px' }}>
                <Grid
                  size={{
                    xs: 12,
                    md: 4,
                  }}
                >
                  <Controller
                    name={`rates.${i}.location_id`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <AutoComplete
                        url={false}
                        label="POL (Port Of Loading)"
                        fieldName=""
                        field={field}
                        error={error}
                        staticOptions={locations}
                        column={''}
                        modal={''}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 4,
                  }}
                >
                  <Controller
                    name={`rates.${i}.destination_id`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <AutoComplete
                        url={false}
                        label="Select Destination"
                        fieldName="name"
                        field={field}
                        error={error}
                        staticOptions={destinations}
                        column={''}
                        modal={''}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 4,
                  }}
                >
                  <Controller
                    name={`rates.${i}.ship_line_id`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <AutoComplete
                        url={false}
                        label="Steamshipline"
                        fieldName="name"
                        field={field}
                        error={error}
                        staticOptions={shiplines}
                        column={''}
                        modal={''}
                      />
                    )}
                  />
                </Grid>

                <Grid
                  size={{
                    xs: 6,
                    md: 3,
                  }}
                >
                  <Controller
                    name={`rates.${i}.rate_45hc`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={error ? true : false}
                        value={field.value ?? 0}
                        label="Rate 45HC"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(
                            value.target.value !== ''
                              ? +value.target.value
                              : '',
                          )
                        }
                        ref={field.ref}
                        helperText={error?.message}
                        sx={{ mt: 2 }}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 6,
                    md: 3,
                  }}
                >
                  <Controller
                    name={`rates.${i}.rate_40hc`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={error ? true : false}
                        value={field.value ?? 0}
                        label="Rate 40HC"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) => {
                          field.onChange(
                            value.target.value !== ''
                              ? +value.target.value
                              : '',
                          );
                        }}
                        ref={field.ref}
                        helperText={error?.message}
                        sx={{ mt: 2 }}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 6,
                    md: 3,
                  }}
                >
                  <Controller
                    name={`rates.${i}.rate_20ft`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={error ? true : false}
                        value={field.value ?? 0}
                        label="Rate 20ft"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(
                            value.target.value !== ''
                              ? +value.target.value
                              : '',
                          )
                        }
                        ref={field.ref}
                        helperText={error?.message}
                        sx={{ mt: 2 }}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 6,
                    md: 3,
                  }}
                >
                  <Controller
                    name={`rates.${i}.mix_rate`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={error ? true : false}
                        value={field.value ?? 0}
                        label="Mix Rate"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(
                            value.target.value !== ''
                              ? +value.target.value
                              : '',
                          )
                        }
                        ref={field.ref}
                        helperText={error?.message}
                        sx={{ mt: 2 }}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Box>
          ))}

          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
            <Button
              variant="contained"
              size="small"
              onClick={() => append({ name: '' })}
            >
              Add Rate
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}
