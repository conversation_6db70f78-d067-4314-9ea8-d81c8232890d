import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>r,
  Grid,
  IconButton,
  Modal,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import EmailPreviewTemplate from './EmailPreviewTemplate';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import DoneIcon from '@mui/icons-material/Done';
import UnpublishedIcon from '@mui/icons-material/Unpublished';

const ChangeRateStatus = ({
  selectedItems,
  open,
  setOpen,
  selectedItem,
  tab,
  setSelectedItems,
  setReload,
}) => {
  const handleClose = () => setOpen(false);
  //const [status, setStatus] = useState('');

  const changesStatus = async (status) => {
    /* const CompanyDestination = selectedItem?.special_shipping_rates.map(
      (item) => ({
        destination_id: item.destination_id,
        ship_line_id: item.ship_line_id,
      }),
    ); */

    try {
      let { data } = await axios.patch(`special-shipping-rates/changeStatus`, {
        status,
        company_id: selectedItems[0]?.company_id,
        type: tab,
        //CompanyDestination,
      });

      if (data.result) {
        setReload(Math.random());
        setSelectedItems([]);
        setOpen(false);
        toast.success('Status Changed Successfully!');
      }
    } catch (error) {
      toast.error('Oops! Somethings went wrong.');
    }
  };

  let style: any;
  if (selectedItems[0]?.status != 'sent') {
    style = {
      position: 'absolute' as 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: 1200,
      bgcolor: 'background.paper',
    };
  } else {
    style = {
      position: 'absolute' as 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: 400,
      bgcolor: 'background.paper',
    };
  }

  return (
    <Modal open={open} onClose={handleClose}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Change Status</Typography>
          <IconButton
            aria-label="close"
            sx={{ color: 'grey' }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Divider />
        <form>
          <CardContent
            style={{
              height: `${selectedItems[0]?.status != 'sent' ? '70vh' : '10vh'}`,
              overflowY: 'auto',
            }}
          >
            <Card>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  {selectedItems[0]?.status != 'sent' ? (
                    <Grid container sx={{ pt: 1 }}>
                      <Grid
                        size={{
                          md: 12,
                        }}
                      >
                        <EmailPreviewTemplate items={selectedItem} tab={tab} />
                      </Grid>

                      <Grid
                        size={{
                          md: 12,
                        }}
                      >
                        <Divider />
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'right',
                            mb: 1,
                            pt: 2,
                          }}
                        >
                          <Button
                            variant="contained"
                            color="error"
                            size="small"
                            startIcon={<CloseIcon />}
                            onClick={handleClose}
                          >
                            Close
                          </Button>

                          <Button
                            sx={{ mx: 1 }}
                            variant="contained"
                            color="warning"
                            size="small"
                            startIcon={<UnpublishedIcon />}
                            onClick={() => changesStatus('rejected')}
                          >
                            Reject
                          </Button>
                          <Button
                            variant="contained"
                            color="success"
                            size="small"
                            startIcon={<DoneIcon />}
                            onClick={() => changesStatus('sent')}
                          >
                            Send
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  ) : (
                    <Button
                      variant="contained"
                      color="success"
                      fullWidth
                      size="small"
                      startIcon={<DoneIcon />}
                      onClick={() => changesStatus('approved')}
                    >
                      Approve
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
        </form>
      </Card>
    </Modal>
  );
};

export default ChangeRateStatus;
