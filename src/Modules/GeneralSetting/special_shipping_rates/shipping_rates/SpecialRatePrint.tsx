import { Box } from '@mui/system';
import { forwardRef } from 'react';
import {
  Table,
  TableCell,
  TableRow,
  TableBody,
  Typography,
  TableFooter,
  Grid,
} from '@mui/material';
import Image from 'next/image';
import logo from '@/public/static/images/logo-500.webp';
import Head from 'next/head';
import { formatDate } from '@/configs/vehicles/configs';

const SpecialRatePrint = forwardRef((props: any, ref: any) => {
  const { data } = props;
  if (!data) {
    return (
      <Box
        ref={ref}
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        No Content to print
      </Box>
    );
  }
  const printStyles: { [key: string]: any } = {
    table: {
      fontFamily: '"Trebuchet MS", Arial, Helvetica, sans-serif',
      borderSpacing: '0',
      fontSize: '14px',
      width: '21cm',
      color: 'black',
    },
    td1: {
      boxSizing: 'border-box',
      border: '1px solid #0a0a0a',
      textAlign: 'left',
      padding: '0 3px',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },
    th1: {
      boxSizing: 'border-box',
      border: '1px solid #0a0a0a',
      borderTop: 'none',
      textAlign: 'center',
      padding: '0 3px',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },

    'tr.head>td': {
      padding: '10px',
    },

    n_units_load: {
      padding: '0 3px',
      boxSizing: 'border-box',
      border: '1px solid #0a0a0a',
      borderTop: 'none',
      textAlign: 'center',
      fontSize: '14px',
      borderCollapse: 'collapse',
      color: 'black',
    },
    tr2: {
      border: '1px solid #0a0a0a',
      padding: '0 3px',
      color: 'black !important',
    },

    'w-50': {
      width: '50%',
    },
  };
  const a4Width = '21cm';
  const a4Height = '29.7cm';

  const PrintRow = ({ cells, sx = {} }) => (
    <TableRow sx={{ sx }}>
      {cells.map((row, index) => (
        <TableCell
          key={index}
          {...(row.classNameName ? { classNameName: row.classNameName } : {})}
          {...(row.sx ? { sx: row.sx } : { sx: printStyles.td1 })}
          {...(row.rowSpan ? { rowSpan: row.rowSpan } : {})}
          {...(row.colSpan ? { colSpan: row.colSpan } : {})}
        >
          <Box display="flex" p="2px" sx={{ color: 'black' }}>
            <Typography
              sx={{
                ...printStyles.p,
                ...(row.stylePara ? { fontSize: 14, fontWeight: 'bold' } : {}),
              }}
            >
              {row.label}
            </Typography>
            {row?.value}
          </Box>
        </TableCell>
      ))}
    </TableRow>
  );

  return (
    // <>
    //   <div>
    //     <div
    //       style={{
    //         height: '500px',
    //         backgroundImage:
    //           "url('https://latest-api.pglsystem.com/images/750px.webp')",
    //         backgroundRepeat: 'no-repeat',
    //         backgroundSize: 'cover',
    //       }}
    //     >
    //       <div
    //         style={{
    //           width: '100%',
    //           height: '100%',
    //           backgroundColor: '#ffff',
    //           opacity: '0.8',
    //         }}
    //       ></div>
    //     </div>
    //     <table style={{ position: 'absolute', inset: 0 }}>
    //       <thead>
    //         <td>
    //           <div style={{ height: '20px', width: '100%' }}></div>
    //         </td>
    //       </thead>
    //       <tbody>
    //         <tr>
    //           <td>
    //             <div className="content" style={{ position: 'relative' }}>
    //               <div style={{ width: '100%' }}>
    //                 <div
    //                   style={{
    //                     width: '90%',
    //                     margin: 'auto',
    //                     paddingTop: '10px',
    //                   }}
    //                 >
    //                   <div
    //                     style={{
    //                       display: 'flex',
    //                       justifyContent: 'center',
    //                       marginTop: '50px',
    //                     }}
    //                   >
    //                     <img
    //                       src="https://api.pglsystem.com/images/logo-500.png"
    //                       width="400px"
    //                     />
    //                   </div>
    //                   <DataTable3
    //                     options={options}
    //                     setOptions={setOptions}
    //                     items={[data]}
    //                     headers={defaultHeaders}
    //                     tableName="specialShippingRate"
    //                     created_at={({ created_at }) => formatDate(created_at)}
    //                     updated_at={({ updated_at }) => formatDate(updated_at)}
    //                     destination={({ destinations }) => destinations?.name}
    //                     company={({ company }) => company?.name}
    //                     status={({ status }) => (
    //                       <Chip
    //                         size="small"
    //                         label={status}
    //                         sx={{
    //                           backgroundColor:
    //                             status === 'approved'
    //                               ? 'green'
    //                               : status === 'pending'
    //                               ? 'orange'
    //                               : status === 'archived'
    //                               ? 'blue'
    //                               : 'red',
    //                         }}
    //                       />
    //                     )}
    //                     effective_date={(item) =>
    //                       formatDate(item.effective_date)
    //                     }
    //                     //Dynamic Headers start
    //                     savannah__ga={({ special_shipping_rates_locations }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.savannah__ga,
    //                       )
    //                     }
    //                     los_angeles__ca={({
    //                       special_shipping_rates_locations,
    //                     }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.los_angeles__ca,
    //                       )
    //                     }
    //                     houston__tx={({ special_shipping_rates_locations }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.houston__tx,
    //                       )
    //                     }
    //                     new_jersey__nj={({
    //                       special_shipping_rates_locations,
    //                     }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.new_jersey__nj,
    //                       )
    //                     }
    //                     jacksonville__fl={({
    //                       special_shipping_rates_locations,
    //                     }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.jacksonville__fl,
    //                       )
    //                     }
    //                     baltimore__md={({ special_shipping_rates_locations }) =>
    //                       nestedTable(
    //                         special_shipping_rates_locations,
    //                         locationNames.baltimore__md,
    //                       )
    //                     }
    //                     //Dynamic Headers End
    //                     created_by={({ created_byToUsers }) =>
    //                       created_byToUsers?.fullname
    //                     }
    //                     updated_by={({ updated_byToUsers }) =>
    //                       updated_byToUsers?.fullname
    //                     }
    //                     ship_line={({ steamshiplines }) => steamshiplines?.name}
    //                   />
    //                 </div>
    //               </div>
    //             </div>
    //           </td>
    //         </tr>
    //       </tbody>
    //       <tfoot>
    //         <tr>
    //           <td style={{ padding: 0 }}>
    //             <div className="footer-space"></div>
    //           </td>
    //         </tr>
    //       </tfoot>
    //     </table>
    //     <div className="footer">
    //       <div
    //         style={{
    //           backgroundColor: '#efefef',
    //           margin: '0 auto',
    //         }}
    //       >
    //         <div
    //           style={{
    //             height: '40px',
    //             background: '#ed2525',
    //             position: 'absolute',
    //             bottom: 0,
    //             left: 0,
    //             right: 0,
    //           }}
    //         ></div>
    //         <div
    //           style={{
    //             height: '80px',
    //             background: '#32a515',
    //             position: 'absolute',
    //             bottom: 0,
    //             left: 0,
    //             width: '56%',
    //             display: 'flex',
    //             alignItems: 'center',
    //             justifyContent: 'space-between',
    //           }}
    //         >
    //           <div
    //             style={{
    //               display: 'flex',
    //               alignItems: 'center',
    //               height: '100%',
    //             }}
    //           >
    //             <div style={{ padding: '15px' }}>
    //               <svg
    //                 xmlns="http://www.w3.org/2000/svg"
    //                 viewBox="0 -960 960 960"
    //                 height="28"
    //                 fill="#ED2525"
    //               >
    //                 <path d="M760-482q0-117-81.5-198.5T480-762v-80q75 0 140.5 28.5t114 77q48.5 48.5 77 114T840-482h-80Zm-160 0q0-50-35-85t-85-35v-80q83 0 141.5 58.5T680-482h-80Zm198 362q-125 0-247-54.5T329-329Q229-429 174.5-551T120-798q0-18 12-30t30-12h162q14 0 25 9.5t13 22.5l26 140q2 16-1 27t-11 19l-97 98q20 37 47.5 71.5T387-386q31 31 65 57.5t72 48.5l94-94q9-9 23.5-13.5T670-390l138 28q14 4 23 14.5t9 23.5v162q0 18-12 30t-30 12Z" />
    //               </svg>
    //             </div>
    //             <div
    //               style={{
    //                 height: '70%',
    //                 width: '1px',
    //                 backgroundColor: '#bbb',
    //               }}
    //             ></div>
    //             <a
    //               href="tel:+****************"
    //               style={{
    //                 color: '#fff',
    //                 fontSize: '16px',
    //                 padding: '5px',
    //                 textDecoration: 'none',
    //               }}
    //             >
    //               +****************
    //             </a>
    //           </div>
    //           <div
    //             style={{
    //               display: 'flex',
    //               alignItems: 'center',
    //               height: '100%',
    //             }}
    //           >
    //             <div style={{ padding: '15px' }}>
    //               <svg
    //                 xmlns="http://www.w3.org/2000/svg"
    //                 viewBox="0 -960 960 960"
    //                 height="28"
    //                 fill="#ED2525"
    //               >
    //                 <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm320-280 320-200v-80L480-520 160-720v80l320 200Z" />
    //               </svg>
    //             </div>
    //             <div
    //               style={{
    //                 height: '70%',
    //                 width: '1px',
    //                 backgroundColor: '#bbb',
    //               }}
    //             ></div>
    //             <a
    //               href="mailto:<EMAIL>"
    //               style={{
    //                 color: '#fff',
    //                 fontSize: '16px',
    //                 padding: '5px',
    //                 textDecoration: 'none',
    //               }}
    //             >
    //               <EMAIL>
    //             </a>
    //           </div>
    //           <div
    //             style={{
    //               width: 0,
    //               height: 0,
    //               borderTop: '0px solid transparent',
    //               borderBottom: '80px solid transparent',
    //               borderLeft: '100px solid #32a515',
    //               position: 'absolute',
    //               right: '-100px',
    //             }}
    //           ></div>
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    // </>
    <>
      <Head>
        <title>Special Shipping Rates</title>
      </Head>
      <Box
        ref={ref}
        width={a4Width}
        height={a4Height}
        p={3}
        id="print"
        sx={{ background: 'white' }}
      >
        <Box
          sx={{
            fontSize: '18px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            color: 'black',
          }}
          mb="20px"
        >
          <Image src={logo} height="50" alt="logo" />
          &nbsp;&nbsp;&nbsp; PGL Special Shipping Rate
        </Box>
        <Table>
          {data.map((row) => (
            <>
              <TableBody>
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'Company: ',
                      value: `${row?.company?.name ?? ''}`,
                      classNameName: 'td1 w-50',
                    },
                    {
                      stylePara: true,
                      label: 'Destination: ',
                      value: row?.destinations?.name ?? '',
                      classNameName: 'td1 w-50',
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'Status: ',
                      value: row?.status ?? '',
                    },
                    {
                      stylePara: true,
                      label: 'Type: ',
                      value: row?.type ?? '',
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'Ship Line: ',
                      value: row?.steamshiplines?.name ?? '',
                    },
                    {
                      stylePara: true,
                      label: 'Effective Date:',
                      value: formatDate(row?.effective_date) ?? '',
                    },
                  ]}
                />
                <PrintRow
                  cells={[
                    {
                      stylePara: true,
                      label: 'Company Type: ',
                      value: row?.company_type ?? '',
                    },
                    {
                      stylePara: true,
                      label: 'Vehicle Type: ',
                      value: row?.vehicle_type ?? '',
                    },
                  ]}
                />
              </TableBody>
              <h2 style={{ color: '#000' }}>Locations</h2>
              <Table>
                <TableBody>
                  <PrintRow
                    cells={[
                      {
                        sx: printStyles.n_units_load,
                        center: true,
                        value: data?.no_units_load ? (
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              width: '100%',
                            }}
                          >
                            {data?.no_units_load}
                          </Box>
                        ) : (
                          ''
                        ),
                        colSpan: 9,
                      },
                    ]}
                  />
                  <PrintRow
                    cells={[
                      {
                        sx: {
                          ...printStyles.n_units_load,
                          py: 0,
                          textAlign: 'left',
                          px: 1,
                          pb: 1,
                        },
                        center: false,
                        value: (
                          <Box sx={{ mt: 1 }}>
                            <Grid container spacing={0.5}>
                              {row?.special_shipping_rates_locations?.map(
                                (loc, i) => {
                                  return (
                                    <Grid key={i} size={12}>
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          gap: 1,
                                          alignItems: 'center',
                                          borderBottom:
                                            i < data?.locs?.length - 1 &&
                                            '1px solid #ccc',
                                          pb: 0.5,
                                        }}
                                      >
                                        <Box sx={{ width: 255, maxWidth: 255 }}>
                                          {loc.locations.name}
                                        </Box>
                                        <Box
                                          sx={{ color: 'black', width: '100%' }}
                                        >
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={18}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              45hc
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={16}
                                            >
                                              {loc?.rate_45hc}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={18}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              40hc
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={18}
                                              fontWeight={600}
                                            >
                                              {loc?.rate_40hc}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={18}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              20ft
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={18}
                                              fontWeight={600}
                                            >
                                              {loc?.rate_20ft}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={18}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              mix
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={18}
                                            >
                                              <span>{loc?.mix_rate}</span>
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={16}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              sedan
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={16}
                                              fontWeight={600}
                                            >
                                              {loc?.sedan_rate}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={16}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              Sedan Dis
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={16}
                                              fontWeight={600}
                                            >
                                              {loc?.sedan_dismantle_rate}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={16}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              SUV
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={16}
                                              fontWeight={600}
                                            >
                                              {loc?.suv_rate}
                                            </Typography>
                                          </Box>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography
                                              variant="h5"
                                              fontSize={16}
                                              width={90}
                                              fontWeight={600}
                                            >
                                              SUV Dis
                                            </Typography>
                                            <Typography
                                              variant={'body1'}
                                              fontSize={16}
                                              fontWeight={600}
                                            >
                                              {loc?.suv_dismantle_rate}
                                            </Typography>
                                          </Box>
                                        </Box>
                                      </Box>
                                      <hr style={{ width: '100%' }} />
                                    </Grid>
                                  );
                                },
                              )}
                            </Grid>
                          </Box>
                        ),
                        colSpan: 9,
                      },
                    ]}
                  />
                </TableBody>
                <TableFooter>
                  <PrintRow
                    cells={[
                      {
                        colSpan: 9,
                        value: (
                          <Box
                            style={{
                              minHeight: 50,
                              color: 'red',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}
                          >
                            <b>Instruction:&nbsp;</b>
                            {data?.loading_instruction}
                          </Box>
                        ),
                      },
                    ]}
                  />
                </TableFooter>
              </Table>
            </>
          ))}
        </Table>
        {/* <h2 style={{ color: '#000' }}>Locations</h2>
        <Table>
          <TableBody>
            <PrintRow
              cells={[
                {
                  sx: printStyles.n_units_load,
                  center: true,
                  value: data?.no_units_load ? (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        width: '100%',
                      }}
                    >
                      {data?.no_units_load}
                    </Box>
                  ) : (
                    ''
                  ),
                  colSpan: 9,
                },
              ]}
            />
            <PrintRow
              cells={[
                {
                  sx: {
                    ...printStyles.n_units_load,
                    py: 0,
                    textAlign: 'left',
                    px: 1,
                    pb: 1,
                  },
                  center: false,
                  value: (
                    <Box sx={{ mt: 1 }}>
                      <Grid container spacing={0.5}>
                        {data?.special_shipping_rates_locations?.map(
                          (loc, i) => {
                            return (
                              <Grid item xs={12} key={i}>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    gap: 1,
                                    alignItems: 'center',
                                    borderBottom:
                                      i < data?.locs?.length - 1 &&
                                      '1px solid #ccc',
                                    pb: 0.5,
                                  }}
                                >
                                  <Box sx={{ width: 255, maxWidth: 255 }}>
                                    {loc.locations.name}
                                  </Box>
                                  <Box sx={{ color: 'black', width: '100%' }}>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        45hc
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                      >
                                        {loc?.rate_45hc}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        40hc
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                        fontWeight={600}
                                      >
                                        {loc?.rate_40hc}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        20ft
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                        fontWeight={600}
                                      >
                                        {loc?.rate_20ft}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={18}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        mix
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={18}
                                      >
                                        <span>{loc?.mix_rate}</span>
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        sedan
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                        fontWeight={600}
                                      >
                                        {loc?.sedan_rate}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        Sedan Dis
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                        fontWeight={600}
                                      >
                                        {loc?.sedan_dismantle_rate}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        SUV
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                        fontWeight={600}
                                      >
                                        {loc?.suv_rate}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Typography
                                        variant="h5"
                                        fontSize={16}
                                        width={90}
                                        fontWeight={600}
                                      >
                                        SUV Dis
                                      </Typography>
                                      <Typography
                                        variant={'body1'}
                                        fontSize={16}
                                        fontWeight={600}
                                      >
                                        {loc?.suv_dismantle_rate}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </Box>
                                <hr style={{ width: '100%' }} />
                              </Grid>
                            );
                          },
                        )}
                      </Grid>
                    </Box>
                  ),
                  colSpan: 9,
                },
              ]}
            />
          </TableBody>
          <TableFooter>
            <PrintRow
              cells={[
                {
                  colSpan: 9,
                  value: (
                    <Box
                      style={{
                        minHeight: 50,
                        color: 'red',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                      }}
                    >
                      <b>Instruction:&nbsp;</b>
                      {data?.loading_instruction}
                    </Box>
                  ),
                },
              ]}
            />
          </TableFooter>
        </Table> */}
        <style>
          {`
          @media print {
            @page {
              size: A4; /* DIN A4 standard, Europe */
              margin: 0;
            }
            html, body {
                ont-size: 16px;
            }
          }
        `}
        </style>
      </Box>
    </>
  );
});
export default SpecialRatePrint;
