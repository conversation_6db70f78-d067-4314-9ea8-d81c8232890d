import ViewModal from '@/components/mainComponents/ViewModal';
import { Box, Chip, Grid, useTheme } from '@mui/material';

const ViewRateLog = ({ show, setView, data, selectedItem }) => {
  const theme = useTheme();
  const changedData = data?.map((d) => {
    const { special_shipping_rates_locations } = d;
    special_shipping_rates_locations.map((e) => {
      d['loc_' + e?.location_id] = e;
    });
    return d;
  });

  const makeName = (st: string) =>
    st.replace('rate', '').replace('_', '').replace('dismantle', 'dis');

  const nestRate = (obj: any) => {
    delete obj.location_id;
    return (
      <table>
        <thead>
          {Object.keys(obj).map((key) => {
            if (typeof obj[key] === 'number' && obj[key] !== 0) {
              return (
                <th
                  key={key}
                  style={{
                    textTransform: 'capitalize',
                  }}
                >
                  {makeName(key)}
                </th>
              );
            }
          })}
        </thead>
        <tr>
          {Object.keys(obj).map((key) => {
            if (typeof obj[key] === 'number' && obj[key] !== 0) {
              return <td key={key}> {obj[key]} </td>;
            }
          })}
        </tr>
      </table>
    );
  };

  return (
    <ViewModal
      createdBy={
        <>
          {selectedItem?.created_byToUsers?.fullname}
          {selectedItem?.created_byToUsers?.departments?.name &&
            ' | ' + selectedItem?.created_byToUsers?.departments?.name}
        </>
      }
      name={selectedItem?.name}
      created_at={data?.created_at}
      updated_at={data?.updated_at}
      onClose={() => setView(false)}
      show={show}
      title="Rate Logs View"
    >
      <Box>
        <Grid
          container
          sx={{
            paddingY: '6px',
            paddingLeft: '20px',
            fontSize: '14px',
            backgroundColor:
              theme.palette.mode == 'dark' ? 'gray' : 'lightgray',
          }}
        >
          <Grid
            size={{
              xs: 1,
              sm: 1,
              md: 1,
            }}
          >
            Status
          </Grid>
          <Grid
            size={{
              xs: 1,
              sm: 1,
              md: 1,
            }}
          >
            Ship Line
          </Grid>

          <Grid
            size={{
              xs: 10,
              sm: 10,
              md: 10,
            }}
          >
            <Grid container>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Houston,TX
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Savannah,GA
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Los Angeles, CA
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                New Jersey, NJ
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Baltimore, MD
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Jacksonville, FL
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        {changedData &&
          changedData?.map((el, i) => (
            <Grid
              key={i}
              container
              sx={{
                paddingY: '6px',
                paddingLeft: '20px',
                fontSize: '12px',
                borderBottom: 1,
              }}
            >
              <Grid
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
                size={{
                  xs: 1,
                  sm: 1,
                  md: 1,
                }}
              >
                <Chip
                  size="small"
                  label={el?.status}
                  sx={{
                    color: 'white',
                    textTransform: 'capitalize',
                    backgroundColor: el?.status === 'archived' ? 'blue' : 'red',
                  }}
                />
              </Grid>
              <Grid
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
                size={{
                  xs: 1,
                  sm: 1,
                  md: 1,
                }}
              >
                {el?.steamshiplines?.name}
              </Grid>
              <Grid
                size={{
                  xs: 10,
                  sm: 10,
                  md: 10,
                }}
              >
                <Grid container>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_2 && nestRate(el?.loc_2)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_1 && nestRate(el?.loc_1)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_4 && nestRate(el?.loc_4)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_5 && nestRate(el?.loc_5)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_6 && nestRate(el?.loc_6)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {el?.loc_9 && nestRate(el?.loc_9)}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          ))}
      </Box>
    </ViewModal>
  );
};

export default ViewRateLog;
