import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';

import { Box, Container, IconButton, Typography } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { COMPANIES } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { formatDate } from '@/configs/vehicles/configs';
import { CreateCompanies } from './CreateCompanies';
import ViewSingleRowCompany from './CompanyViewProfile';
import { copyORViewFun, recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import UploadContract from './companyComponents/uploadContract';
import { auctionButtonCompany } from './companyComponents/customeActionButton';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import CustomerProfile from './companyComponents/customerProfile';
import StarIcon from '@/icons/star-icon';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import LocalAtmIcon from '@mui/icons-material/LocalAtm';
import SpecialMixShippingRatesDrawer from './SpecialMixShippingRatesDrawer';
import CustomerFinancialDueReport from '@/configs/general_setting/CustomerFinancialDueReport';
import {
  filterContentCompanies,
  HeaderInfo,
} from './companyComponents/companyHeader';
import { getColor } from './CompaniesHelper';
import DownloadCompaniesSummery from '@/components/mainComponents/cModal/DownloadCompaniesSummery';

const NewCompanies = ({ apiUrl, defaultHeaders }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [reload, setReload] = useState(0);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [openContract, setOpenContract] = useState(false);
  const [openProfile, setOpenProfile] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showFinancialDueDownload, setShowFinancialDueDownload] =
    useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [downloadType, setDownloadType] = useState('excel');
  const [isDownloading, setIsDownloading] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const pageName = 'companies';
  const [loading, setLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [mixRateSelectedCompany, setMixRateSelectedCompany] = useState(null);
  const [openDownloadCompaniesSummery, setOpenDownloadCompaniesSummery] =
    useState(false);
  ////////////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          // order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          // order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  const fetchCustomerFinancialDueBalance = async (id) => {
    setIsDownloading(true);
    try {
      let { data } = await axios.get(`./companies/financial_due/${id}`);
      setIsDownloading(false);
      return { result: true, data: data };
    } catch (error) {
      setIsDownloading(false);
      return { result: false, message: error };
    }
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    // options.orderBy,
    options.filterData,
    reload,
  ]);

  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  /*   const updateSingleRow = async (item) => {
    setViewData(item);
    setTableRecords((prev) => {
      return prev.map((row) => {
        if (row.id == item.id) return item;
        return row;
      });
    });
  }; */

  const customButtons = () =>
    auctionButtonCompany({
      perms: perms,
      setOpenContract: setOpenContract,
      setOpenProfile: setOpenProfile,
      selectedItems: selectedItems,
    });

  const getContractDownloadUrl = async (contract: string) => {
    const contractName = contract.substring(
      contract.lastIndexOf('/') + 1,
      contract.length,
    );
    try {
      const { data } = await axios.get(
        `${apiUrl}/download-contract/${contractName}`,
      );

      const a = document.createElement('a');
      a.href = data.url;
      a.target = '_blank';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(data.url);

      return { result: true, data: data.url };
    } catch (error) {
      console.log(error);
      return { result: false, message: error };
    }
  };

  return perms && !perms?.includes(COMPANIES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Companies List</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Companies'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              customActionButtons={customButtons}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} company ? `}
              dialogTitle={`Delete company Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showSummeryDownloadButton={true}
              setOpenDownloadCompaniesSummery={() =>
                setOpenDownloadCompaniesSummery(true)
              }
              setShowFinancialDueDownload={() =>
                setShowFinancialDueDownload(true)
              }
              showAddButton={perms?.includes(COMPANIES?.CREATE)}
              showEditButton={perms?.includes(COMPANIES?.UPDATE)}
              showDeleteButton={perms?.includes(COMPANIES?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="company"
          // end default props
          //start custom props
          name={({ name, tier_level, join_date }) => (
            <Box
              sx={{
                whiteSpace: 'wrap',
                // textAlign: 'center',
                minWidth: 110,
                maxWidth: 220,
                display: 'flex',
                cursor: 'pointer',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: 1,
                bgcolor: `${getColor(join_date)}`,
                padding: 0.5,
                borderRadius: 2,
              }}
            >
              <p>{name}</p>
              {tier_level == 'golden' && (
                <div style={{ minWidth: 16, width: 16 }}>
                  <StarIcon
                    height="16px"
                    width="16px"
                    style={{ color: '#fcd34d' }}
                  />
                </div>
              )}
            </Box>
          )}
          created_by={({ users_companies_created_byTousers }) => (
            <>
              {users_companies_created_byTousers?.fullname}
              {users_companies_created_byTousers?.departments?.name &&
                ' | ' + users_companies_created_byTousers?.departments?.name}
            </>
          )}
          customer_region={({ customer_region }) => {
            switch (customer_region) {
              case 'local':
                return 'Local';
              case 'export':
                return 'Export';
              case 'local_and_export':
                return 'Local & Export';
              default:
                return customer_region;
            }
          }}
          parent_id={({ parent_company }) => <>{parent_company?.name}</>}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          is_belong_to_used_car={({ is_belong_to_used_car }) => (
            <Typography variant="body2" noWrap>
              {is_belong_to_used_car == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          mix={({ mix }) => (
            <Typography variant="body2" noWrap>
              {mix == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          contract={({ contract }) =>
            contract && (
              <a onClick={() => getContractDownloadUrl(contract)}>
                <CloudDownloadIcon sx={{ color: 'green' }} />
              </a>
            )
          }
          trade_license={({ trade_license }) =>
            trade_license && (
              <a onClick={() => getContractDownloadUrl(trade_license)}>
                <CloudDownloadIcon sx={{ color: '#f13434' }} />
              </a>
            )
          }
          id_passport={({ id_passport }) =>
            id_passport && (
              <a onClick={() => getContractDownloadUrl(id_passport)}>
                <CloudDownloadIcon sx={{ color: '#0288d1' }} />
              </a>
            )
          }
          complete={({ complete }) => (
            <Typography variant="body2" noWrap>
              {complete == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          mix_halfcut={({ mix_halfcut }) => (
            <Typography variant="body2" noWrap>
              {mix_halfcut == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          complete_halfcut={({ complete_halfcut }) => (
            <Typography variant="body2" noWrap>
              {complete_halfcut == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          is_special_rate={({ is_special_rate }) => (
            <Typography variant="body2" noWrap>
              {is_special_rate == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )}
            </Typography>
          )}
          id={(item) =>
            copyORViewFun({
              getSingleRow,
              copy: item?.id,
              display: item?.id,
              id: item,
            })
          }
          destinations={({ destinations }) => <Box>{destinations?.name}</Box>}
          mix_special_rates={({ is_mix_special_rate, ...company }) => {
            return is_mix_special_rate ? (
              <AppTooltip title={'Mix Special Rates'}>
                <IconButton
                  color="secondary"
                  onClick={() => {
                    setMixRateSelectedCompany(company);
                  }}
                >
                  <LocalAtmIcon />
                </IconButton>
              </AppTooltip>
            ) : (
              <></>
            );
          }}
        />
      </Container>
      <SpecialMixShippingRatesDrawer
        show={mixRateSelectedCompany != null}
        setShow={(value) =>
          setMixRateSelectedCompany(
            value == false ? null : mixRateSelectedCompany,
          )
        }
        company={mixRateSelectedCompany}
      />
      <UploadContract
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        open={openContract}
        setOpen={setOpenContract}
      />

      <CustomerProfile
        open={openProfile}
        setOpen={setOpenProfile}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setReload={setReload}
      />

      <CreateCompanies
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      <ViewSingleRowCompany
        //updateSingleRow={updateSingleRow}
        data={viewData}
        setView={setView}
        show={view}
        //perms={perms}
      />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Companies"
        content={filterContentCompanies}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
      ></ColumnDialog>

      <PdfModal
        options={options}
        open={showDownload}
        pdf_title={'Companies'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        fetchDownloadRecords={fetchDownloadRecords}
        tableRecords={tableRecords}
        headers={selectedHeaders}
        //start custom props
        created_by={({ users_companies_created_byTousers }) =>
          users_companies_created_byTousers?.fullname
        }
        created_at={({ created_at }) => formatDate(created_at)}
        updated_at={({ updated_at }) => formatDate(updated_at)}
        is_belong_to_used_car={({ is_belong_to_used_car }) =>
          is_belong_to_used_car == true ? 'Yes' : 'No'
        }
        destinations={({ destinations }) => destinations?.name}
      ></PdfModal>

      <CustomerFinancialDueReport
        showDownload={showFinancialDueDownload}
        setShowDownload={setShowFinancialDueDownload}
        downloadType={downloadType}
        setDownloadType={setDownloadType}
        fetchData={fetchCustomerFinancialDueBalance}
        id={selectedItems[0]?.id}
        isDownloading={isDownloading}
      />
      <DownloadCompaniesSummery
        selectedItems={selectedItems}
        openDownloadCompaniesSummery={openDownloadCompaniesSummery}
        setOpenDownloadCompaniesSummery={setOpenDownloadCompaniesSummery}
      />
    </>
  );
};

export default NewCompanies;
