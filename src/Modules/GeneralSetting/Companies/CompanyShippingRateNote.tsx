/* import QuillEditor from '@/components/mainComponents/MuiEditor';
import { Box } from '@mui/system';

import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
;

const CompanyShippingRateNote = ({ data, activeTab, updateSingleRow }) => {
  const [loading, setLoading] = useState(false);
  const schema = z.object({
    shipping_rate_note: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
  });

  useEffect(() => {
    if (activeTab == 'shipping_rate_note') {
      form.setValue('shipping_rate_note', data.shipping_rate_note);
    }
  }, [activeTab]);

  const form = useForm({
    mode: 'onChange',
    resolver: zod<PERSON><PERSON>olver(schema),
    defaultValues: {
      shipping_rate_note: data.shipping_rate_note,
    },
  });
  const updateShippingRateNote = async (values) => {
    try {
      setLoading(true);
      const res = await axios.patch('companies/shipping-rate-note', {
        id: data.id,
        ...values,
      });
      toast.success('Note updated successfully!');
      updateSingleRow({
        ...data,
        shipping_rate_note: res.data.data.shipping_rate_note,
      });
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  return (
    <Box>
      <form onSubmit={form.handleSubmit(updateShippingRateNote)}>
        <Controller
          name="shipping_rate_note" // Unique name for the field
          control={form.control}
          defaultValue=""
          render={({ field }) => (
            <>
              <QuillEditor
                height={300}
                onChange={(value) => {
                  field.onChange(value);
                }}
                value={field.value}
              />
            </>
          )}
        />
        <Box sx={{ pt: 2, display: 'flex', justifyContent: 'end' }}>
          <Button
            loading={loading}
            size="small"
            variant="contained"
            type="submit"
          >
            Update
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default CompanyShippingRateNote;
 */
