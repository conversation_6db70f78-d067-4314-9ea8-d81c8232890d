import React, { useContext, useEffect, useState } from 'react';
import { nanoid } from 'nanoid';
import {
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';
import Tooltip from '@mui/material/Tooltip';
import { convertToTitleCase } from '@/utils/convertToTitleCase';
import { ThemeContext } from '@/theme/ThemeProvider';

const allCategories: string[] = [
  'finance',
  'clearance',
  'title',
  'fedex',
  'auction',
  'load',
  'used_cars',
];

type ChargeType = {
  id?: number;
  name: string;
  label?: string;
  amount?: number;
  cost?: number;
  remark?: string;
  category?: string;
};

type CategoryOfChargesType = {
  [key: string]: ChargeType[];
};

const chargesInTheSheet: CategoryOfChargesType = {
  finance: [
    { name: 'towing_charge', label: 'Towing Charge', amount: 30, cost: 0 },
    { name: 'storage_charge', label: 'Storage Charge', amount: 30, cost: 0 },
    { name: 'dispatch_charge', label: 'Dispatch Charge', amount: 30, cost: 0 },
    { name: 'attestation_fee', label: 'Attestation fee', amount: 152, cost: 0 },
    { name: 'coo_charges', label: 'COO Charges', amount: 160, cost: 150 },
    { name: 'hybrid_charges', label: 'Hybrid Charges', amount: 160, cost: 150 },
    {
      name: 'custom_duty_vat',
      label: 'Custom duty & Vat',
      amount: 0,
      cost: 0,
    },
    { name: 'vcc', label: 'VCC', amount: 40, cost: 30 },
    {
      name: 'clearance_charge',
      label: 'Clearance Charge',
      amount: 270,
      cost: 0,
    },
    {
      name: 'inspection_charges',
      label: 'Inspection Charges',
      amount: 140,
      cost: 24,
    },
  ],
  clearance: [
    {
      name: 'local_service_charges',
      label: 'Local Service Charges',
      amount: 250,
      cost: 0,
    },
    { name: 'bill_of_entry', label: 'Bill Of Entry', amount: 50, cost: 0 },
    { name: 'handling_fee', label: 'Handling Fee', amount: 50, cost: 0 },
    {
      name: 'unloading_charges',
      label: 'Unloading Charges',
      amount: 200,
      cost: 250,
    },
    {
      name: 'consignee_charges',
      label: 'Consignee Charges',
      amount: 200,
      cost: 0,
    },
    { name: 'exit_paper', label: 'Exit paper', amount: 10, cost: 10 },
  ],
  title: [
    {
      name: 'ca_non_repairable_cert_normal',
      label: 'CA-Non Repairable Certificate (Normal)',
      amount: 400,
      cost: 300,
    },
    {
      name: 'ca_non_repairable_cert_urgent',
      label: 'CA-Non Repairable Certificate (Urgent)',
      amount: 400,
      cost: 350,
    },
    { name: 'wa_bill_of_sale', label: 'WA-Bill of Sale', amount: 50, cost: 50 },
  ],
  fedex: [
    {
      name: 'fedex_2day',
      label: 'FedEx 2 Day (Inside the USA)',
      amount: 30,
      cost: 9.2,
    },
    {
      name: 'fedex_overnight',
      label: 'FedEx overnight (Inside the USA)',
      amount: 60,
      cost: 47.55,
    },
    {
      name: 'fedex_international_standard',
      label: 'FedEx International Label Standard',
      amount: 80,
      cost: 58.4,
    },
    {
      name: 'fedex_international_priority',
      label: 'FedEx International Label Priority',
      amount: 220,
      cost: 150,
    },
  ],
  auction: [
    {
      name: 'uae_transfer_fee_for_copart',
      label: 'UAE transfer fee for copart / per transiction',
      amount: 35,
      cost: 30,
    },
    {
      name: 'uae_transfer_fee_for_iaa',
      label: ' UAE transfer fee for IAA  / per transiction',
      amount: 35,
      cost: 30,
    },
    {
      name: 'local_auction_per_deal_with_customer',
      label: 'for local auctions per our deal with customers / per car',
      amount: 100,
      cost: 30,
    },
    {
      name: 'broker_fee_for_customer',
      label: ' broker fee for some customers ',
      amount: 50,
      cost: 0,
    },
  ],
  load: [
    {
      name: 'hazardous',
      label: 'HAZARDOUS per car/per cont',
      amount: 550,
      cost: 550,
    },
  ],
  used_cars: [
    {
      name: 'storage_fee',
      label: 'Storage fee after 30days',
      amount: 10,
      cost: 0,
    },
    { name: 'recovery_fee', label: 'Recovery Fee', amount: 40, cost: 40 },
    {
      name: 'sharjah_yard_storage',
      label: 'Sharjah Yard Storage',
      amount: 10,
      cost: 1,
    },
  ],
};

const allCharges: CategoryOfChargesType = {
  finance: [
    { name: 'towing_charge', label: 'Towing Charge' },
    { name: 'halfcut_charge', label: 'Halfcut Charge' },
    { name: 'shipping_charge', label: 'Shipping Charge' },
    { name: 'storage_charge', label: 'Storage Charge' },
    { name: 'custom_hold', label: 'Custom Hold Charge' },
    { name: 'transfer_fee', label: 'Transfer fee' },
    { name: 'wire_fee', label: 'Wire fee' },
    {
      name: 'tds_charges',
      label: 'Transit Disruption Surcharge (TDS)',
    },
    { name: 'dispatch_charge', label: 'Dispatch Charge' },
    { name: 'manheim_purchase', label: 'Manheim Purchase' },
    { name: 'hybrid_fee', label: 'Hybrid Fee' },
    { name: 'absorbers_fee', label: 'Absorbers fee' },
    { name: 'shortage', label: 'Shortage fee' },
    { name: 'relist_fee', label: 'Relist fee' },
    { name: 'suv_charges', label: 'Full size / SUV' },
    { name: 'sublot', label: 'Sublot' },
    { name: 'attestation_fee', label: 'Attestation fee' },
    { name: 'coo_charges', label: 'COO Charges' },
    { name: 'hybrid_charges', label: 'Hybrid Charges' },
    { name: 'custom_duty_vat', label: 'Custom duty & Vat' },
    { name: 'vcc', label: 'VCC' },
    { name: 'clearance_charge', label: 'Clearance Charge' },
    { name: 'transportation_fee', label: 'Transportation fee' },
    { name: 'office_fee_and_bank', label: 'Office Fee & Bank' },
    { name: 'empty_containers', label: 'Empty Containers' },
    { name: 'inspection_charges', label: 'Inspection Charges' },
    { name: 'detention_charges', label: 'Detention Charges' },
    { name: 'registration_fee', label: 'Registeration Fee' },
    { name: 'other_charges', label: 'Other Charges' },
  ],
  clearance: [
    { name: 'local_service_charges', label: 'Local Service Charges' },
    { name: 'bill_of_entry', label: 'Bill Of Entry' },
    { name: 'handling_fee', label: 'Handling Fee' },
    { name: 'unloading_charges', label: 'Unloading Charges' },
    { name: 'consignee_charges', label: 'Consignee Charges' },
    { name: 'exit_paper', label: 'Exit paper' },
  ],
  title: [
    {
      name: 'ca_non_repairable_cert_normal',
      label: 'CA-Non Repairable Certificate (Normal)',
    },
    {
      name: 'ca_non_repairable_cert_urgent',
      label: 'CA-Non Repairable Certificate (Urgent)',
    },
    { name: 'wa_bill_of_sale', label: 'WA-Bill of Sale' },
    { name: 'title_charges', label: 'Title Charges' },
  ],
  fedex: [
    { name: 'fedex_2day', label: 'FedEx 2 Day (Inside the USA)' },
    { name: 'fedex_overnight', label: 'FedEx overnight (Inside the USA)' },
    {
      name: 'fedex_international_standard',
      label: 'FedEx International Label Standard',
    },
    {
      name: 'fedex_international_priority',
      label: 'FedEx International Label Priority',
    },
    {
      name: 'fed_ex_or_mailing_fee',
      label: 'FedEx or Mailing Fee',
    },
  ],
  auction: [
    {
      name: 'uae_transfer_fee_for_copart',
      label: 'UAE transfer fee for copart / per transiction',
    },
    {
      name: 'uae_transfer_fee_for_iaa',
      label: ' UAE transfer fee for IAA  / per transiction',
    },
    {
      name: 'local_auction_per_deal_with_customer',
      label: 'for local auctions per our deal with customers / per car',
    },
    {
      name: 'broker_fee_for_customer',
      label: ' broker fee for some customers ',
    },
    {
      name: 'local_auction_fee_charges_per_car',
      label: ' local auction fee charges per car',
    },
  ],
  load: [
    { name: 'wrapping', label: 'WRAPPING' },
    { name: 'dumprid', label: 'DUMPRID' },
    { name: 'hazardous', label: 'HAZARDOUS per car/per cont' },
    { name: 'additional_video', label: 'Additional video' },
    { name: 'additional_photo', label: 'Additional photo' },
  ],
  used_cars: [
    { name: 'storage_fee', label: 'Storage fee after 30days' },
    { name: 'recovery_fee', label: 'Recovery Fee' },
    { name: 'sharjah_yard_storage', label: 'Sharjah Yard Storage' },
  ],
};

export const allChargesMap: Record<string, string> = {
  towing_charge: 'Towing Charge',
  halfcut_charge: 'Halfcut Charge',
  shipping_charge: 'Shipping Charge',
  storage_charge: 'Storage Charge',
  custom_hold: 'Custom Hold Charge',
  transfer_fee: 'Transfer fee',
  wire_fee: 'Wire fee',
  tds_charges: 'Transit Disruption Surcharge (TDS)',
  dispatch_charge: 'Dispatch Charge',
  manheim_purchase: 'Manheim Purchase',
  hybrid_fee: 'Hybrid Fee',
  absorbers_fee: 'Absorbers fee',
  shortage: 'Shortage fee',
  relist_fee: 'Relist fee',
  suv_charges: 'Full size / SUV',
  sublot: 'Sublot',
  attestation_fee: 'Attestation fee',
  coo_charges: 'COO Charges',
  hybrid_charges: 'Hybrid Charges',
  custom_duty_vat: 'Custom duty & Vat',
  vcc: 'VCC',
  clearance_charge: 'Clearance Charge',
  transportation_fee: 'Transportation fee',
  office_fee_and_bank: 'Bank & Office Fee',
  empty_containers: 'Empty Containers',
  inspection_charges: 'Inspection Charges',
  detention_charges: 'Detention Charges',
  registration_fee: 'Registeration Fee',
  other_charges: 'Other Charges',
  local_service_charges: 'Local Service Charges',
  bill_of_entry: 'Bill Of Entry',
  handling_fee: 'Handling Fee',
  unloading_charges: 'Unloading Charges',
  consignee_charges: 'Consignee Charges',
  exit_paper: 'Exit paper',
  ca_non_repairable_cert_normal: 'CA-Non Repairable Certificate (Normal)',
  ca_non_repairable_cert_urgent: 'CA-Non Repairable Certificate (Urgent)',
  wa_bill_of_sale: 'WA-Bill of Sale',
  title_charges: 'Title Charges',
  fedex_2day: 'FedEx 2 Day (Inside the USA)',
  fedex_overnight: 'FedEx overnight (Inside the USA)',
  fedex_international_standard: 'FedEx International Label Standard',
  fedex_international_priority: 'FedEx International Label Priority',
  fed_ex_or_mailing_fee: 'FedEx or Mailing Fee',
  uae_transfer_fee_for_copart: 'UAE transfer fee for copart / per transiction',
  uae_transfer_fee_for_iaa: ' UAE transfer fee for IAA  / per transiction',
  local_auction_per_deal_with_customer:
    'for local auctions per our deal with customers / per car',
  broker_fee_for_customer: ' broker fee for some customers ',
  local_auction_fee_charges_per_car: ' local auction fee charges per car',
  wrapping: 'WRAPPING',
  dumprid: 'DUMPRID',
  hazardous: 'HAZARDOUS per car/per cont',
  additional_video: 'Additional video',
  additional_photo: 'Additional photo',
  storage_fee: 'Storage fee after 30days',
  recovery_fee: 'Recovery Fee',
  sharjah_yard_storage: 'Sharjah Yard Storage',
};

const getRemainingCharges = (
  allCharges: CategoryOfChargesType,
  companyCharges: CategoryOfChargesType | {},
): CategoryOfChargesType => {
  const result: CategoryOfChargesType = {};

  for (const category in allCharges) {
    if (allCharges.hasOwnProperty(category)) {
      const allChargesInCurrentCategory = allCharges[category];

      const companyChargesInCurrentCategory = companyCharges[category] || [];
      const companyChargesNames = new Set(
        companyChargesInCurrentCategory.map((charge) => charge.name),
      );

      const remainingChargesInCurrentCategory =
        allChargesInCurrentCategory.filter(
          (charge) => !companyChargesNames.has(charge.name),
        );

      if (remainingChargesInCurrentCategory.length > 0) {
        result[category] = remainingChargesInCurrentCategory;
      }
    }
  }

  return result;
};

export const groupByCategory = (items) => {
  return items.reduce((grouped, item) => {
    const { category } = item;
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(item);
    return grouped;
  }, {});
};

const newCharge = (category: string) => {
  return {
    id: nanoid(),
    category: category,
    name: '',
    amount: 0,
    cost: 0,
    remark: '',
  };
};

export default function Step1Next({ form, isUpdate }) {
  const [companyCharges, setCompanyCharges] = useState<
    CategoryOfChargesType | {}
  >(form.getValues('company_charges'));
  const [remainingCategories, setRemainingCategories] = useState<string[] | []>(
    [],
  );
  const [remainingCharges, setRemainingCharges] = useState<
    CategoryOfChargesType | {}
  >({});
  const [categoryToBeAdded, setCategoryToBeAdded] = useState('none');
  const [showSelectCategoryWarning, setShowSelectCategoryWarning] =
    useState(false);

  const theme = useContext(ThemeContext);

  useEffect(() => {
    setRemainingCategories(
      allCategories.filter(
        (category) => !Object.keys(companyCharges).includes(category),
      ) || [],
    );
    setRemainingCharges(getRemainingCharges(allCharges, companyCharges));
    form.setValue('company_charges', companyCharges);
  }, [companyCharges]);

  useEffect(() => {
    if (!isUpdate) {
      const initialCompanyCharges = {};
      allCategories.forEach((category) => {
        initialCompanyCharges[category] = chargesInTheSheet[category].map(
          (charge) => ({
            id: nanoid(),
            amount: charge.amount,
            cost: charge.cost,
            category: category,
            name: charge.name,
            remark: '',
          }),
        );
      });
      setCompanyCharges(initialCompanyCharges);
    }
  }, [isUpdate]);

  const handleChargesChange = (category, companyChargesIndex, field, value) => {
    setCompanyCharges((prevCompanyCharges) => {
      const updatedCompanyCharges = { ...prevCompanyCharges };
      updatedCompanyCharges[category][companyChargesIndex][field] = value;
      return updatedCompanyCharges;
    });
  };

  const handleAddChargeCategory = (category) => {
    if (!category || !remainingCategories) {
      return;
    }

    setCompanyCharges((prevCompanyCharges) => ({
      ...prevCompanyCharges,
      [category]: [newCharge(category)],
    }));
  };

  const handleRemoveChargeCategory = (category) => {
    setCompanyCharges((prevCompanyCharges) => {
      const deepCopyCompanyCharges = JSON.parse(
        JSON.stringify(prevCompanyCharges),
      );
      delete deepCopyCompanyCharges[category];
      return deepCopyCompanyCharges;
    });
  };

  const handleAddCharge = (category) => {
    setCompanyCharges((prevCompanyCharges) => ({
      ...prevCompanyCharges,
      [category]: [...prevCompanyCharges[category], newCharge(category)],
    }));
  };

  const handleRemoveCharge = (category, chargeId) => {
    setCompanyCharges((prevCompanyCharges) => ({
      ...prevCompanyCharges,
      [category]: prevCompanyCharges[category].filter(
        (charge) => charge.id !== chargeId,
      ),
    }));
  };

  return (
    <Grid>
      <Grid sx={{ mt: 2, mb: 3, textAlign: 'center' }} size={12}>
        <Typography fontWeight={600} fontSize={18}>
          Company Charges
        </Typography>
      </Grid>
      {Object.keys(companyCharges).map((category) => (
        <Box
          key={category}
          border={1}
          borderColor="gray"
          borderRadius={2}
          mb={2.5}
        >
          <Grid
            container
            mb={4}
            py={0.5}
            borderBottom={0.5}
            borderColor="gray"
            bgcolor={theme.mode === 'light' ? '#EBEBEB' : 'unset'}
            sx={{ borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
          >
            <Grid size={1}></Grid>
            <Grid size={10}>
              <Typography variant="h6" align="center" pt={0.5}>
                {category.toUpperCase().replace(/_/g, ' ')}
              </Typography>
            </Grid>
            <Grid
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
              size={1}
            >
              <Tooltip title="Remove Category">
                <Button
                  color="error"
                  onClick={() => {
                    handleRemoveChargeCategory(category);
                  }}
                >
                  <CancelIcon />
                </Button>
              </Tooltip>
            </Grid>
          </Grid>
          {companyCharges[category].map(
            (companyCharge, chargeIndex: number) => {
              return (
                <Grid container spacing={2} key={companyCharge.id} px={2}>
                  <Grid size={7}>
                    <FormControl
                      fullWidth
                      error={
                        !!form.errors.company_charges &&
                        !!form.errors.company_charges[companyCharge.category] &&
                        !!form.errors.company_charges[companyCharge.category][
                          chargeIndex
                        ]?.name
                      }
                    >
                      <InputLabel id="add_category_label">Name</InputLabel>
                      <Select
                        sx={{ height: '34px' }}
                        fullWidth
                        size="small"
                        label="Name"
                        id={`name_${companyCharge?.id}`}
                        name={`name_${companyCharge?.name}`}
                        variant="outlined"
                        value={companyCharge?.name}
                        onChange={(e) => {
                          handleChargesChange(
                            category,
                            chargeIndex,
                            'name',
                            e.target.value,
                          );
                        }}
                      >
                        <MenuItem value={companyCharge?.name}>
                          {
                            allCharges[category].find(
                              (charge) => charge.name === companyCharge?.name,
                            )?.label
                          }
                        </MenuItem>
                        {remainingCharges[category.toLocaleLowerCase()]?.map(
                          (item) => (
                            <MenuItem value={item.name} key={item.name}>
                              {item?.label}
                            </MenuItem>
                          ),
                        )}
                      </Select>
                      <FormHelperText>
                        {!!form.errors.company_charges &&
                          form.errors.company_charges[companyCharge.category] &&
                          form.errors.company_charges[companyCharge.category][
                            chargeIndex
                          ]?.name?.message}
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                  <Grid size={1.5}>
                    <TextField
                      sx={{ mb: 2 }}
                      fullWidth
                      size="small"
                      label={
                        <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
                          Amount
                        </Box>
                      }
                      id={`amount_${companyCharge?.id}`}
                      name={`amount_${companyCharge?.amount}`}
                      variant="outlined"
                      type="number"
                      value={companyCharge?.amount}
                      onChange={(e) => {
                        handleChargesChange(
                          category,
                          chargeIndex,
                          'amount',
                          Number(e.target.value),
                        );
                      }}
                      error={
                        !!form.errors.company_charges &&
                        !!form.errors.company_charges[companyCharge.category] &&
                        !!form.errors.company_charges[companyCharge.category][
                          chargeIndex
                        ]?.amount
                      }
                      helperText={
                        !!form.errors.company_charges &&
                        form.errors.company_charges[companyCharge.category] &&
                        form.errors.company_charges[companyCharge.category][
                          chargeIndex
                        ]?.amount?.message
                      }
                    />
                  </Grid>
                  <Grid size={1.5}>
                    <TextField
                      sx={{ mb: 2 }}
                      fullWidth
                      size="small"
                      label={
                        <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
                          Cost
                        </Box>
                      }
                      id={`cost_${companyCharge?.id}`}
                      name={`cost_${companyCharge?.cost}`}
                      variant="outlined"
                      type="number"
                      value={companyCharge?.cost}
                      onChange={(e) => {
                        handleChargesChange(
                          category,
                          chargeIndex,
                          'cost',
                          Number(e.target.value),
                        );
                      }}
                      error={
                        !!form.errors.company_charges &&
                        !!form.errors.company_charges[companyCharge.category] &&
                        !!form.errors.company_charges[companyCharge.category][
                          chargeIndex
                        ]?.cost
                      }
                      helperText={
                        !!form.errors.company_charges &&
                        form.errors.company_charges[companyCharge.category] &&
                        form.errors.company_charges[companyCharge.category][
                          chargeIndex
                        ]?.cost?.message
                      }
                    />
                  </Grid>
                  <Grid size={1.5}>
                    <TextField
                      sx={{ mb: 2 }}
                      fullWidth
                      size="small"
                      label={
                        <Box sx={{ fontSize: '14px', lineHeight: '1em' }}>
                          Remark
                        </Box>
                      }
                      id={`remark_${companyCharge?.id}`}
                      name={`remark_${companyCharge?.remark}`}
                      variant="outlined"
                      value={companyCharge?.remark}
                      onChange={(e) => {
                        handleChargesChange(
                          category,
                          chargeIndex,
                          'remark',
                          e.target.value,
                        );
                      }}
                    />
                  </Grid>
                  <Grid
                    display="flex"
                    flexDirection="column"
                    justifyContent="start"
                    alignItems="start"
                    size={0.5}
                  >
                    <Tooltip title="Remove Charge">
                      <Button
                        color="error"
                        onClick={() => {
                          handleRemoveCharge(category, companyCharge.id);
                        }}
                        sx={{
                          padding: '0 5px 0 0',
                          marginTop: 0.3,
                          minWidth: 'auto',
                        }}
                      >
                        <CancelIcon />
                      </Button>
                    </Tooltip>
                  </Grid>
                </Grid>
              );
            },
          )}
          <Grid display="flex" justifyContent="center" mb={2} size={12}>
            <Button
              variant="outlined"
              color="primary"
              disabled={
                !remainingCharges[category.toLocaleLowerCase()] ||
                companyCharges[category].filter((charge) => !charge.name)
                  .length > 0
              }
              onClick={() => {
                handleAddCharge(category);
              }}
            >
              Add Charge
            </Button>
          </Grid>
        </Box>
      ))}
      <Grid
        container
        spacing={2}
        display="flex"
        justifyContent="center"
        alignItems="start"
        mt={2}
        mb={3}
      >
        <Grid size={2}>
          <FormControl
            fullWidth
            error={
              categoryToBeAdded === 'none' &&
              remainingCategories.length > 0 &&
              showSelectCategoryWarning
            }
          >
            <InputLabel id="add_category_label">Category</InputLabel>
            <Select
              sx={{ height: '34px' }}
              fullWidth
              size="small"
              label="Category"
              id="add_category"
              name="add_category"
              variant="outlined"
              value={categoryToBeAdded}
              onChange={(e) => {
                setCategoryToBeAdded(e.target.value);
              }}
            >
              <MenuItem value="none">
                <em>None</em>
              </MenuItem>
              {remainingCategories.map((category) => (
                <MenuItem value={category} key={category}>
                  {convertToTitleCase(category)}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              {categoryToBeAdded === 'none' &&
                remainingCategories.length > 0 &&
                showSelectCategoryWarning &&
                'Select a category'}
            </FormHelperText>
          </FormControl>
        </Grid>

        <Grid p={0} size={3}>
          <Button
            sx={{ height: '34px' }}
            variant="outlined"
            color="primary"
            onClick={() => {
              if (categoryToBeAdded !== 'none') {
                handleAddChargeCategory(categoryToBeAdded);
                setCategoryToBeAdded('none');
                setShowSelectCategoryWarning(false);
              } else {
                setShowSelectCategoryWarning(true);
              }
            }}
            disabled={
              Object.keys(companyCharges).length === allCategories.length
            }
          >
            Add Category
          </Button>
        </Grid>
      </Grid>
    </Grid>
  );
}
