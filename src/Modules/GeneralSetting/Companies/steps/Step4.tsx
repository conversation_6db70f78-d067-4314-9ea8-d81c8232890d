import { Autocomplete, Box, Grid, TextField, Typography } from '@mui/material';

export default function Step4({ form, yardLocationIds, setYardLocationIds }) {
  const s_array = (data, fieldName) =>
    data &&
    Object?.values(data).map((value: any) => {
      return {
        id: value?.id,
        label: value?.[fieldName] ? value?.[fieldName] : '',
        location_id: value?.location_id,
      };
    });

  let notes = form && form.getValues('notes');

  const addNote = (e, id) => {
    const updatedNotes = notes.map((item) => {
      if (item.location_id === id) {
        return { ...item, note: e.target.value };
      }
      return item;
    });
    const existingNote = updatedNotes.find((item) => item.location_id === id);
    if (!existingNote) {
      updatedNotes.push({ note: e.target.value, location_id: id });
    }
    notes = updatedNotes;
    form?.setValue('notes', notes);
  };

  //@ts-ignore
  const addLocation = (e, value, loc_id) => {
    let oldValue: any;
    value
      ? (oldValue = yardLocationIds.filter(
          (item) => item?.location_id != value?.location_id || item == null,
        ))
      : (oldValue = yardLocationIds.filter((ov) => ov?.location_id != loc_id));
    const updatedIds = value ? [...oldValue, value] : oldValue;
    const uniqueIds = Array.from(new Set(updatedIds));
    setYardLocationIds(uniqueIds);
  };

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Location & Yard Info
      </Typography>
      <Grid container spacing={2}>
        {form?.locations &&
          form.locations.map((item, i) => {
            let Value = item.yards_location.find((yard) =>
              yardLocationIds.some((value) => yard?.id === value?.id),
            );
            return item?.yards_location.length > 0 ? (
              <Grid
                key={i}
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Autocomplete
                  value={Value}
                  onChange={(e, value) => addLocation(e, value, item?.id)}
                  options={s_array(item?.yards_location, 'name') || []}
                  isOptionEqualToValue={(option, value) =>
                    option?.id == value?.id
                  }
                  getOptionLabel={(item) =>
                    item?.label?.replace(/_/g, ' ') ||
                    item?.name?.replace(/_/g, ' ')
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={`loading Company ${item?.name}`}
                      size="small"
                    />
                  )}
                />
              </Grid>
            ) : (
              ''
            );
          })}
      </Grid>
      <Grid container spacing={2} sx={{ borderTop: 1, marginTop: 2 }}>
        {form?.locations &&
          form?.locations.map((item, i) => {
            const note = notes.find((note) => note.location_id === item.id);
            return (
              <Grid
                key={i}
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <TextField
                  size="small"
                  defaultValue={note?.note || null}
                  id={item?.name}
                  label={item?.name + ' Note'}
                  fullWidth
                  variant="outlined"
                  onChange={(e) => {
                    addNote(e, item?.id);
                  }}
                  helperText={form.errors.item?.name?.message}
                />
              </Grid>
            );
          })}
      </Grid>
    </Box>
  );
}
