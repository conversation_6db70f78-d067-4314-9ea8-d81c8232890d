import { Box, Grid, TextField, Typography } from '@mui/material';

export default function Step3({ form }) {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Notify Info
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Party"
            error={!!form.errors.notify_party}
            id="outlined-error"
            {...form.register('notify_party')}
            helperText={form.errors.notify_party?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Street"
            error={!!form.errors.notify_street}
            id="outlined-error"
            {...form.register('notify_street')}
            helperText={form.errors.notify_street?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Box"
            error={!!form.errors.notify_box}
            id="outlined-error"
            {...form.register('notify_box')}
            helperText={form.errors.notify_box?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify City"
            error={!!form.errors.notify_city}
            id="outlined-error"
            {...form.register('notify_city')}
            helperText={form.errors.notify_city?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify State/Province"
            error={!!form.errors.notify_state}
            id="outlined-error"
            {...form.register('notify_state')}
            helperText={form.errors.notify_state?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Zip/Postal Code"
            error={!!form.errors.notify_zip}
            id="outlined-error"
            {...form.register('notify_zip')}
            helperText={form.errors.notify_zip?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Country"
            error={!!form.errors.notify_country}
            id="outlined-error"
            {...form.register('notify_country')}
            helperText={form.errors.notify_country?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Phone"
            error={!!form.errors.notify_phone}
            id="outlined-error"
            {...form.register('notify_phone')}
            helperText={form.errors.notify_phone?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Email"
            error={!!form.errors.notify_email}
            id="outlined-error"
            {...form.register('notify_email')}
            helperText={form.errors.notify_email?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Fax"
            error={!!form.errors.notify_fax}
            id="outlined-error"
            {...form.register('notify_fax')}
            helperText={form.errors.notify_fax?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Notify Point of Contact"
            error={!!form.errors.notify_poc}
            id="outlined-error"
            {...form.register('notify_poc')}
            helperText={form.errors.notify_poc?.message}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
