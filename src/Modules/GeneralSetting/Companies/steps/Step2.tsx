import { Box, Grid, TextField, Typography } from '@mui/material';

import React from 'react';

export default function Step2({ form }) {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Consignee Info
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Name"
            error={!!form.errors.consignee}
            id="outlined-error"
            {...form.register('consignee')}
            helperText={form.errors.consignee?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Street"
            error={!!form.errors.consignee_street}
            id="outlined-error"
            {...form.register('consignee_street')}
            helperText={form.errors.consignee_street?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Box"
            error={!!form.errors.consignee_box}
            id="outlined-error"
            {...form.register('consignee_box')}
            helperText={form.errors.consignee_box?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee City"
            error={!!form.errors.consignee_city}
            id="outlined-error"
            {...form.register('consignee_city')}
            helperText={form.errors.consignee_city?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Zip Code"
            error={!!form.errors.consignee_zip_code}
            id="outlined-error"
            {...form.register('consignee_zip_code')}
            helperText={form.errors.consignee_zip_code?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Country"
            error={!!form.errors.consignee_country}
            id="outlined-error"
            {...form.register('consignee_country')}
            helperText={form.errors.consignee_country?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Phone"
            error={!!form.errors.consignee_phone}
            id="outlined-error"
            {...form.register('consignee_phone')}
            helperText={form.errors.consignee_phone?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Email"
            error={!!form.errors.consignee_email}
            id="outlined-error"
            {...form.register('consignee_email')}
            helperText={form.errors.consignee_email?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Fax"
            error={!!form.errors.consignee_fax}
            id="outlined-error"
            {...form.register('consignee_fax')}
            helperText={form.errors.consignee_fax?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Consignee Point of Contact"
            error={!!form.errors.consignee_poc}
            id="outlined-error"
            {...form.register('consignee_poc')}
            helperText={form.errors.consignee_poc?.message}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
