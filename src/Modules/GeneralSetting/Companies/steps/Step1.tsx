import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import { bookings_party } from '@/configs/booking/bookingHeader';
import { formFormatDate } from '@/configs/configs';

import { useLocationContext } from '@/contexts/LocationsContext';
import axios from '@/lib/axios';
import {
  currencies,
  ex_rateApiUrl,
} from '@/Modules/exchange_rates/ExchangeRatesHeader';
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  TextField,
  Typography,
  FormControl,
  Grid,
  Autocomplete,
  Chip,
  MenuItem,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useState } from 'react';
import { Controller, useWatch } from 'react-hook-form';
import {
  billLoadingType,
  rateApplyDate,
} from '../companyComponents/companyHeader';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { COMPANIES } from '@/configs/leftSideMenu/Permissions';

export default function Step1({ form, parentHierarchy, isUpdate }) {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const handleInputChange = async () => {};
  const { map }: any = useLocationContext();
  const locations = map((item) => ({
    name: item.name,
    id: item.id,
  }));
  const formExRates = form.watch('exchange_rates');
  const handleChange = async (event, item, reason) => {
    if (reason == 'clear' && event.type == 'click') {
      form.setValue('parent_id', null);
    } else {
      form.setValue('parent_id', Number(item.id));
    }
  };
  const [ex_rates, setExRate] = useState(formExRates);
  const fetchRates = async () => {
    try {
      const controller = new AbortController();
      let { data } = await axios.get(ex_rateApiUrl, {
        signal: controller.signal,
      });
      setExRate(data.data);
      form.setValue('exchange_rates', data.data);
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    if (formExRates?.length === 0) {
      fetchRates();
    }
  }, []);
  // const handleAddRate = () => {
  //   const newRate = {
  //     key: str_Random(3),
  //     currency: '',
  //     rate: 0,
  //   };
  //   setExRate((prev) => [...prev, newRate]);
  // };
  // const str_Random = (length) => {
  //   let result = '';
  //   const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  //   for (let i = 0; i < length; i++) {
  //     const randomInd = Math.floor(Math.random() * characters.length);
  //     result += characters.charAt(randomInd);
  //   }
  //   return result;
  // };
  // const handleRemoveRate = (id) => {
  //   const remaining = ex_rates.filter((rate) =>
  //     typeof id === 'number' ? rate.id != id : rate.key != id,
  //   );
  //   setExRate(remaining);
  //   form.setValue(`exchange_rates`, remaining);
  // };
  const handleFieldChange = (index, fieldName, value) => {
    const updatedRates = [...ex_rates];
    updatedRates[index] = {
      ...updatedRates[index],
      [fieldName]: value,
    };
    setExRate(updatedRates);
    form.setValue(`exchange_rates`, updatedRates);
  };

  const mixHalfcut = useWatch({ control: form.control, name: 'mix_halfcut' });
  const mix = useWatch({ control: form.control, name: 'mix' });
  const shouldShowDropdown = mixHalfcut || mix;

  const customerRegionOptions = [
    { label: 'Local', value: 'local' },
    { label: 'Export', value: 'export' },
    { label: 'Local and Export', value: 'local_and_export' },
  ];

  return (
    <Box sx={{ py: 2, mx: 3 }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        General Information
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Company Name"
            error={!!form.errors.name}
            id="outlined-error"
            {...form.register('name')}
            helperText={form.errors.name?.message}
            onChange={handleInputChange}
            disabled={
              isUpdate && !perms?.includes(COMPANIES?.UPDATE_COMPANY_NAME)
            }
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="destination_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Destination"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'destinations'}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <Controller
            control={form.control}
            name="join_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Join Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <Controller
            name="parent_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Parent Company"
                fieldName="name"
                field={field}
                disableAutoComplete={parentHierarchy}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'companies'}
                handleOnChangeFromStepper={true}
                stepperHandleOnChange={handleChange}
                parentHierarchyCheck={true}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <Controller
            name="rate_apply_date"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Rate Apply Date"
                fieldName=""
                field={field}
                error={error}
                staticOptions={rateApplyDate}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <Controller
            name="bill_of_loading_type"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Bill Of Loading Type"
                fieldName=""
                field={field}
                error={error}
                staticOptions={billLoadingType}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Days Limit"
            type="number"
            error={!!form.errors.days_limit}
            id="outlined-error"
            {...form.register('days_limit')}
            helperText={form.errors.days_limit?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Credit Limit"
            type="number"
            error={!!form.errors.credit_limit}
            id="outlined-error"
            {...form.register('credit_limit')}
            helperText={form.errors.credit_limit?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 6,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            size="small"
            label="Percentage Of In Process Cargo"
            type="number"
            error={!!form.errors.perecentage_of_in_process_cargo}
            id="outlined-error"
            {...form.register('perecentage_of_in_process_cargo')}
            helperText={form.errors.perecentage_of_in_process_cargo?.message}
          />
        </Grid>
        <Grid
          sx={{ display: 'flex', justifyContent: 'space-between' }}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            control={form.control}
            name="is_belong_to_used_car"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="is_belong_to_used_car"
                      />
                    }
                    label="Is belong to used car?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />

          <Controller
            control={form.control}
            name="is_special_rate"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="is_special_rate"
                      />
                    }
                    label="Is Special Rate?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
          <Controller
            control={form.control}
            name="is_mix_special_rate"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="is_mix_special_rate"
                      />
                    }
                    label="Is Mix Special Rate?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />

          <Controller
            control={form.control}
            name="tier_level"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value == 'golden'}
                        onChange={(_e, checked) => {
                          field.onChange(checked ? 'golden' : null);
                        }}
                        name="tier_level"
                      />
                    }
                    label="Is Golden?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
          <Controller
            control={form.control}
            name="scrap"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="scrap"
                      />
                    }
                    label="Scrap"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
          <Controller
            control={form.control}
            name="has_customer"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="has_customer"
                      />
                    }
                    label="Has Customer?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          sx={{ display: 'flex', justifyContent: 'space-between' }}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            control={form.control}
            name="has_customer_invoice"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="has_customer_invoice"
                      />
                    }
                    label="Has Customer Invoice?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>

        {/* <Grid item xs={12} md={12}>
          <TextField
            fullWidth
            size="small"
            label="Customer Code"
            error={!!form.errors.code}
            id="outlined-error"
            {...form.register('code')}
            helperText={form.errors.code?.message}
            onChange={handleInputChange}
          />
        </Grid> */}

        <Grid
          sx={{ borderTop: 1, marginTop: 1 }}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Typography sx={{ fontWeight: 'bold' }}>
            Company Shipping Type :
          </Typography>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 3,
          }}
        >
          <Controller
            control={form.control}
            name="mix"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="mix"
                      />
                    }
                    label="Mix"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 3,
          }}
        >
          <Controller
            control={form.control}
            name="complete"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="complete"
                      />
                    }
                    label="Complete"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 3,
          }}
        >
          <Controller
            control={form.control}
            name="mix_halfcut"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="mix_halfcut"
                      />
                    }
                    label="Mix Halfcut"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 3,
          }}
        >
          <Controller
            control={form.control}
            name="complete_halfcut"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="complete_halfcut"
                      />
                    }
                    label="Complete Halfcut"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        {shouldShowDropdown && (
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="customer_region"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  select
                  label="Customer Region"
                  fullWidth
                  size="small"
                  error={!!error}
                  helperText={error?.message}
                >
                  {customerRegionOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        )}
        <Grid
          size={{
            xs: 8,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            label="Loading Instruction"
            error={!!form.errors.name}
            id="outlined-error"
            {...form.register('loading_instruction')}
            helperText={form.errors.loading_instruction?.message}
            multiline
            minRows={6}
          />
        </Grid>
        <Grid
          size={{
            xs: 8,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            label="Documentation Instructions"
            error={!!form.errors.name}
            id="outlined-error"
            {...form.register('documentation_instruction')}
            helperText={form.errors.documentation_instruction?.message}
            multiline
            minRows={6}
          />
        </Grid>
        <Grid
          size={{
            xs: 8,
            md: 4,
          }}
        >
          <TextField
            fullWidth
            label="Half Cut Loading Instruction"
            error={!!form.errors.name}
            id="outlined-error"
            {...form.register('half_cut_loading_instruction')}
            helperText={form.errors.half_cut_loading_instruction?.message}
            multiline
            minRows={6}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="booking_parties"
            control={form.control}
            render={({ field, fieldState: { invalid, error } }) => (
              <Autocomplete
                multiple
                id="tags-filled"
                options={bookings_party}
                value={field.value}
                renderTags={(value: readonly string[], getTagProps) =>
                  value.map((option: string, index: number) => (
                    <Chip
                      key={index}
                      variant="outlined"
                      label={option}
                      {...getTagProps({ index })}
                    />
                  ))
                }
                onChange={(_event: any, newValue) => {
                  field.onChange(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    label="Booking Parties"
                    placeholder="Booking Parties"
                    size="small"
                    error={invalid}
                    helperText={error?.[0]?.message}
                  />
                )}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="banned_locations"
            control={form.control}
            render={({ field }) => (
              <FilterAutocomplete
                url={''}
                label={'Banned locations'}
                name={'banned_locations'}
                keyName={'name'}
                values={field.value}
                staticOptions={locations}
                onChange={(newValue) => field.onChange(newValue)}
              />
            )}
          />
        </Grid>
        <Grid
          sx={{ borderTop: 1, marginTop: 2, marginBottom: 2 }}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Typography sx={{ fontWeight: 'bold' }}>Exchange Rates:</Typography>
        </Grid>
        {ex_rates?.map((rate: any, i: number) => (
          <Grid
            key={i + 1}
            container
            spacing={2}
            sx={{
              paddingBottom: '10px',
            }}
          >
            <Grid
              size={{
                xs: 6,
                md: 6,
              }}
            >
              <Controller
                name={`exchange_rates[${i}].currency`}
                control={form.control}
                render={({ fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={rate.currency}
                    getOptionLabel={(option) => (option ? option : '')}
                    onChange={(_event: any, newValue) =>
                      handleFieldChange(i, 'currency', newValue)
                    }
                    options={currencies.filter(
                      (currency) =>
                        !ex_rates
                          ?.map((rate) => rate.currency)
                          ?.includes(currency),
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Currency"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 6,
                md: 6,
              }}
            >
              <Controller
                name={`exchange_rates[${i}].rate`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={rate.rate <= 0 ? true : false}
                    id="rate"
                    value={rate.rate ?? ''}
                    label={
                      rate.rate <= 0 ? 'Rate Should be greater than 0' : 'Rate'
                    }
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(e) =>
                      handleFieldChange(i, 'rate', +e.target.value)
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            {/* <Grid item xs={12} md={1}>
              <Tooltip title="Remove Rate">
                <Button
                  color="error"
                  onClick={() => handleRemoveRate(rate.id ?? rate?.key)}
                  sx={{ padding: 0, minWidth: 'auto' }}
                >
                  <CancelIcon />
                </Button>
              </Tooltip>
            </Grid> */}
          </Grid>
        ))}
        {/* <Grid
          item
          xs={12}
          md={12}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <Tooltip title="Remove Rate">
            <Button
              color="info"
              variant="contained"
              disabled={currencies?.length <= ex_rates.length}
              onClick={handleAddRate}
            >
              Add Rate
            </Button>
          </Tooltip>
        </Grid> */}
      </Grid>
    </Box>
  );
}
