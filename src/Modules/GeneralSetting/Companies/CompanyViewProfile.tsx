import ProfileCustomRow from '@/components/mainComponents/ProfileCustomRow';
import ViewModal from '@/components/mainComponents/ViewModal';
import TabPanel from '@mui/lab/TabPanel';
import { Box } from '@mui/system';
import moment from 'moment';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelIcon from '@mui/icons-material/Cancel';
import React, { useEffect, useState } from 'react';
import {
  Chip,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import { groupByCategory } from '@/Modules/GeneralSetting/Companies/steps/Step1Next';
import axios from '@/lib/axios';
import { choseColor } from '@/configs/configs';
import { FormatAmount } from '@/components/mainComponents/FormatAmount';
import { formatDate } from '@/configs/vehicles/configs';
import VerifiedIcon from '@mui/icons-material/Verified';
import { convertToTitleCase } from '@/utils/convertToTitleCase';

const ViewSingleRowCompany = ({ show, setView, data }) => {
  let tabs = [
    { label: 'General Info', value: 'general_info' },
    { label: 'Consignee Info', value: 'consignee_info' },
    { label: 'Company Charges', value: 'company_charges' },
    { label: 'Exchange Rates', value: 'exchange_rates' },
  ];
  if (data?.child_company?.length > 0) {
    tabs.push({ label: 'Child Companies', value: 'child_companies' });
  }
  //@ts-ignore
  const [activeTab, setActiveTab] = useState('general_info');
  const [profile, setProfile] = useState();
  const [rates, setRates] = useState([]);
  const theme = useTheme();
  const companyCharges = groupByCategory(data?.company_charges || []);

  /* if (data?.mix && perms?.includes(MIX_SHIPPING_RATES?.VIEW_OFFER)) {
    tabs.push({ label: 'Mix Shipping Rates', value: 'mix_shipping_rate' });
  } */

  useEffect(() => {
    setProfile(data?.profile);
    data?.id && getCurrencies();
  }, [data]);

  const getCurrencies = async () => {
    let res = await axios.get(`/exchange_rates/company/${data?.id}`);
    const grouped = res.data?.reduce((acc, rate) => {
      if (!acc[rate.currency]) {
        acc[rate.currency] = [];
      }
      acc[rate.currency].push(rate);
      return acc;
    }, {});
    for (const currency in grouped) {
      grouped[currency] = grouped[currency].sort(
        (a: any, b: any) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      );
    }
    setRates(grouped);
  };
  return (
    <ViewModal
      createdBy={
        <>
          {data?.users_companies_created_byTousers?.fullname}
          {data?.users_companies_created_byTousers?.departments?.name &&
            ' | ' + data?.users_companies_created_byTousers?.departments?.name}
        </>
      }
      name={data?.name}
      created_at={data?.created_at}
      updated_at={data?.updated_at}
      status={data?.country}
      onClose={() => setView(false)}
      show={show}
      tabs={tabs}
      setActiveTab={setActiveTab}
      title="Company Profile"
      entity="companies"
      entity_id={data?.id}
      shippingLogs={false}
    >
      <Box sx={{ height: '600px', overflow: 'auto', overflowX: 'hidden' }}>
        <TabPanel value="general_info" sx={{ px: 0, py: 1 }}>
          <ProfileCustomRow
            itemName="Name"
            itemText={data?.name}
            itemName2="Company City"
            itemText2={data?.company_city}
          />
          <ProfileCustomRow
            bgColor
            itemName="Used Car"
            itemText={
              data?.is_belong_to_used_car == true ? (
                <CheckCircleOutlineIcon sx={{ color: 'green' }} />
              ) : (
                <CancelIcon sx={{ color: 'red' }} />
              )
            }
            itemName2="Join Date"
            itemText2={moment(data?.join_date).format('YYYY - MM - DD')}
          />
          <ProfileCustomRow
            itemName="Country"
            itemText={data?.country}
            itemName2="Loading Instruction"
            itemText2={data?.loading_instruction}
          />
          <ProfileCustomRow
            bgColor
            itemName="Notify Country"
            itemText={data?.notify_country}
            itemName2="Notify City"
            itemText2={data?.notify_city}
          />
          <ProfileCustomRow
            itemName="Notify Box"
            itemText={data?.notify_box}
            itemName2="Notify Party"
            itemText2={data?.notify_party}
          />
          <ProfileCustomRow
            bgColor
            itemName="Notify Email"
            itemText={data?.notify_email}
            itemName2="Notify Phone"
            itemText2={data?.notify_phone}
          />
          <ProfileCustomRow
            itemName="Notify State"
            itemText={data?.notify_state}
            itemName2="Notify Street"
            itemText2={data?.notify_street}
          />
          <ProfileCustomRow
            bgColor
            itemName="Notify Fax"
            itemText={data?.notify_fax}
            itemName2="Notify POC"
            itemText2={data?.notify_poc}
          />
          <ProfileCustomRow
            itemName="Notify Zip Code"
            itemText={data?.notify_zip}
            itemName2="preferred com way"
            itemText2={data?.preferred_com_way}
          />

          <ProfileCustomRow
            bgColor
            itemName="Created By"
            itemText={
              <>
                {data?.users_companies_created_byTousers?.fullname}
                {data?.users_companies_created_byTousers?.departments?.name &&
                  ' | ' +
                    data?.users_companies_created_byTousers?.departments?.name}
              </>
            }
            itemName2="Updated By"
            itemText2={
              <>
                {data?.users_companies_updated_byTousers?.fullname}
                {data?.users_companies_updated_byTousers?.departments?.name &&
                  ' | ' +
                    data?.users_companies_updated_byTousers?.departments?.name}
              </>
            }
          />
          <ProfileCustomRow
            itemName="Rate Apply Date"
            itemText={
              <Box sx={{ textTransform: 'capitalize ' }}>
                {data?.rate_apply_date?.replace(/_/g, ' ')}
              </Box>
            }
            itemName2="Note"
            itemText2={data?.note}
          />
          <ProfileCustomRow
            itemName="Bill Loading Type"
            itemText={
              <Box sx={{ textTransform: 'capitalize ' }}>
                {data?.bill_loading_type?.replace(/_/g, ' ')}
              </Box>
            }
          />
          <Box
            sx={{
              display: 'flex',
              pl: 2,
              alignItems: 'center',
              backgroundColor:
                theme.palette.mode == 'dark' ? 'gray' : 'lightgray',
            }}
          >
            <Box sx={{ textTransform: 'uppercase', fontWeight: 'bold' }}>
              Customer Profile:
            </Box>
            <Box sx={{ pl: 5, width: '80%' }}>
              {profile && (
                <div
                  style={{ paddingTop: -5 }}
                  dangerouslySetInnerHTML={{ __html: profile }}
                ></div>
              )}
            </Box>
          </Box>
        </TabPanel>
        <TabPanel value="consignee_info" sx={{ px: 0, py: 1 }}>
          <ProfileCustomRow
            bgColor
            itemName="Consignee"
            itemText={data?.consignee}
            itemName2="Consignee Box"
            itemText2={data?.consignee_box}
          />
          <ProfileCustomRow
            itemName="Consignee Country"
            itemText={data?.consignee_country}
            itemName2="Consignee City"
            itemText2={data?.consignee_city}
          />
          <ProfileCustomRow
            bgColor
            itemName="Consignee Email"
            itemText={data?.consignee_email}
            itemName2="Consignee Phone"
            itemText2={data?.consignee_phone}
          />
          <ProfileCustomRow
            itemName="Consignee Fax"
            itemText={data?.consignee_fax}
            itemName2="Consignee POC"
            itemText2={data?.consignee_poc}
          />
          <ProfileCustomRow
            bgColor
            itemName="Consignee Street"
            itemText={data?.consignee_street}
            itemName2="Consignee Zip Code"
            itemText2={data?.consignee_zip_code}
          />
        </TabPanel>
        {data?.child_company?.length > 0 && (
          <TabPanel value="child_companies" sx={{ px: 0, py: 1 }}>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell align="left">Name</TableCell>
                    <TableCell align="left">Consignee Email</TableCell>
                    <TableCell align="left">Phone</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data?.child_company.map((company) => (
                    <TableRow
                      key={company.id}
                      sx={{ '&:last-child td, &:last-child td': { border: 0 } }}
                    >
                      <TableCell align="left" component="td" scope="row">
                        {company.id}
                      </TableCell>
                      <TableCell align="left" component="td" scope="row">
                        {company.name}
                      </TableCell>
                      <TableCell align="left">
                        {company.consignee_email}
                      </TableCell>
                      <TableCell align="left">{company.phone}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>
        )}
        {/* <TabPanel value="shipping_rates" sx={{ px: 1, py: 1 }}>
          <CompanyShippingRates
            perms={perms}
            data={data}
            activeTab={activeTab}
            updateSingleRow={updateSingleRow}
          ></CompanyShippingRates>
        </TabPanel>
        <TabPanel value="shipping_rate_note" sx={{ px: 1, py: 1 }}>
          <CompanyShippingRateNote
            data={data}
            activeTab={activeTab}
            updateSingleRow={updateSingleRow}
          ></CompanyShippingRateNote>
        </TabPanel>
        <TabPanel value="shipping_rate_note" sx={{ px: 1, py: 1 }}>
          {perms?.includes(SHIPPING_RATES?.VIEW) && (
            <CompanyShippingRates
              perms={perms}
              data={data}
              activeTab={activeTab}
              updateSingleRow={updateSingleRow}
            ></CompanyShippingRates>
          )}
        </TabPanel> */}

        {/* <TabPanel value="shipping_rate_note" sx={{ px: 1, py: 1 }}>
          {perms?.includes(SHIPPING_RATES?.UPDATE) && (
            <CompanyShippingRateNote
              data={data}
              activeTab={activeTab}
              updateSingleRow={updateSingleRow}
            ></CompanyShippingRateNote>
          )}
        </TabPanel> */}
        {/* <TabPanel value="mix_shipping_rate" sx={{ px: 1, py: 0 }}>
          <MixShippingRates selectedData={data} perms={perms} />
        </TabPanel> */}
        <TabPanel value="company_charges" sx={{ px: 1, py: 0 }}>
          {Object.keys(companyCharges).map((category) => (
            <Box
              key={category}
              border={1}
              borderColor="gray"
              borderRadius={2}
              my={3}
            >
              <Typography
                variant="h6"
                align="center"
                mb={2}
                py={0.5}
                borderBottom={0.5}
                borderColor="gray"
                bgcolor="#EBEBEB"
                sx={{ borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
              >
                {category.toUpperCase().replace(/_/g, ' ')}
              </Typography>
              {companyCharges[category].map((companyCharge, chargeIndex) => {
                const currency = category === 'clearance' ? 'AED ' : '$';
                return (
                  <Grid
                    container
                    key={companyCharge.id}
                    borderBottom={
                      companyCharges[category].length - 1 !== chargeIndex
                        ? 1
                        : 0
                    }
                    borderColor="gray"
                    mb={1}
                    px={2}
                    pb={
                      companyCharges[category].length - 1 !== chargeIndex
                        ? 1
                        : 0
                    }
                  >
                    <Grid size={0.5}>
                      <Typography>{chargeIndex + 1}</Typography>
                    </Grid>
                    <Grid size={5}>
                      <Typography>
                        {convertToTitleCase(companyCharge?.name)}
                      </Typography>
                    </Grid>
                    <Grid size={2}>
                      <Typography>
                        {currency}
                        {companyCharge?.amount}
                      </Typography>
                    </Grid>
                    <Grid size={2}>
                      <Typography>
                        {currency}
                        {companyCharge?.cost}
                      </Typography>
                    </Grid>
                    <Grid size={2.5}>
                      <Typography>{companyCharge?.remark}</Typography>
                    </Grid>
                  </Grid>
                );
              })}
            </Box>
          ))}
        </TabPanel>
        <TabPanel value="exchange_rates" sx={{ px: 0, py: 1 }}>
          {Object.keys(rates).map((item: any) => (
            <>
              <br />
              <ProfileCustomRow
                bgColor
                itemName="SNO"
                itemText="Currency"
                itemName2="Rate"
                itemText2="Effective Date From"
              />
              {rates[item]?.map((rate, index) => (
                <ProfileCustomRow
                  key={rate.id}
                  bgColor
                  itemName={index + 1}
                  itemText={
                    <>
                      <Chip
                        size="small"
                        label={rate?.currency}
                        sx={{
                          backgroundColor: choseColor(rate?.currency),
                        }}
                      />
                      {[0].includes(index) && (
                        <VerifiedIcon
                          sx={{ color: 'green', fontSize: 'medium' }}
                        />
                      )}
                    </>
                  }
                  itemName2={
                    <FormatAmount amount={rate?.rate} currency="none" />
                  }
                  itemText2={`${formatDate(rate?.created_at)} | ${new Date(rate?.created_at).toLocaleTimeString()}`}
                />
              ))}
            </>
          ))}
        </TabPanel>
      </Box>
    </ViewModal>
  );
};

export default ViewSingleRowCompany;
