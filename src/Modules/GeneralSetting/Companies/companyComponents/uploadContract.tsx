import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import {
  Autocomplete,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextField,
} from '@mui/material';
import { toast } from 'react-toastify';
import { uploadApi } from '@/lib/axios';
import LoadPlanAttachments from '@/Modules/shipment/shipmentComponents/LoadPlanAttachments';

const DocTypes = [
  { label: 'Contract', id: 'contract' },
  { label: 'Trade License', id: 'trade_license' },
  { label: 'ID or Passport', id: 'id_passport' },
];

export default function UploadContract({
  selectedItems,
  open,
  setOpen,
  setSelectedItems,
}) {
  const handleClose = () => setOpen(false);
  const [file, setFile] = React.useState<any>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [docType, setDocType] = React.useState(null);

  const handleContractUpload = async (): Promise<void> => {
    try {
      if (docType) {
        setIsLoading(true);
        const formData = new FormData();
        formData.append('docType', docType);
        formData.append('file', file[0]!);
        const res = await uploadApi.post(
          `companies/uploadContract/${selectedItems[0]?.id}`,
          formData,
        );

        if (res?.data?.result) {
          const updatedItems = [...selectedItems];
          const itemIndex = updatedItems.findIndex(
            (item) => item?.id === res?.data?.data?.id,
          );
          if (itemIndex !== -1) {
            updatedItems[itemIndex].contract = res?.data?.data?.contract;
            setSelectedItems([]);
          }
          setOpen(false);
          setFile([]);

          toast.success('Uploaded successfully!');
          setIsLoading(false);
        }
      } else toast.warn('Please select document type!');
    } catch (error) {
      console.log(error);
      const errorResponse = error?.response?.data;
      if (errorResponse) {
        toast.error(errorResponse.message);
      }
      setIsLoading(false);
    }
  };

  //@ts-ignore
  const onChange = (e, selectedValue) => setDocType(selectedValue?.id);

  return (
    <Box>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        fullWidth={true}
      >
        <DialogTitle id="alert-dialog-title">Upload Documents</DialogTitle>

        <DialogContent>
          <Grid container sx={{ marginTop: 1 }}>
            <Grid
              size={{
                xs: 12,
                md: 12,
              }}
            >
              <Autocomplete
                size="small"
                disablePortal
                id="combo-box-demo"
                options={DocTypes}
                onChange={onChange}
                renderInput={(params) => (
                  <TextField {...params} label="Document Types" />
                )}
              />
            </Grid>
            <Grid
              sx={{ marginTop: 1 }}
              size={{
                xs: 12,
                md: 12,
              }}
            >
              <LoadPlanAttachments
                attachments={file}
                setAttachments={setFile}
              />
            </Grid>
          </Grid>

          {/* {preview && (
            <p className="mb-5">
              <img src={preview as string} alt="Upload preview" />
            </p>
          )} */}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleContractUpload}
            autoFocus
            style={{
              pointerEvents: isLoading ? 'none' : 'unset',
              width: '71px',
            }}
          >
            {isLoading ? <CircularProgress size="20px" /> : <>Upload</>}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
