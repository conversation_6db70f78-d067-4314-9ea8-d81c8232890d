import { Box, Drawer, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import MixShippingRates from '../mix_shipping_rates/MixShippingRates';
import { shippingCostHeader } from '@/configs/general_setting/shippingCostHeader';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
type statusType = 'pending' | 'approved' | 'archived';
const SpecialMixShippingRatesDrawer = ({ show, setShow, company }) => {
  const [status, setStatus] = useState<statusType>('pending');

  return (
    <Drawer anchor={'right'} open={show} onClose={() => setShow(false)}>
      <Box sx={{ minWidth: '90vw', maxWidth: '90vw', px: 0 }}>
        <Box sx={{ p: 1, display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={() => setShow(false)}>
            <CloseIcon />
          </IconButton>
          <Typography variant="h6" sx={{ ml: 2, fontSize: '20px' }}>
            Mix Special Rates <small>({company?.name})</small>
          </Typography>
          <div className="px-4">
            <DropdownMenu>
              <DropdownMenuTrigger>
                <div className="flex items-center gap-1 text-sm capitalize">
                  <span>{status}</span>
                  <ChevronDown height={14} width={14} />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="z-[99999] max-h-[400px] overflow-y-auto">
                <DropdownMenuLabel className="text-center">
                  Status
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {['pending', 'approved', 'archived'].map(
                  (item: statusType, index) => (
                    <DropdownMenuCheckboxItem
                      key={index}
                      checked={false}
                      className="cursor-pointer items-start capitalize"
                      onClick={() => setStatus(item)}
                    >
                      {item}
                    </DropdownMenuCheckboxItem>
                  ),
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Box>
        <MixShippingRates
          apiUrl={'/mix-shipping-rates'}
          defaultHeaders={shippingCostHeader}
          company={company}
          customStatus={status}
        />
      </Box>
    </Drawer>
  );
};

export default SpecialMixShippingRatesDrawer;
