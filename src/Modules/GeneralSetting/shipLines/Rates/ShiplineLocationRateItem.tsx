import { Box, Grid, IconButton, TextField } from '@mui/material';
import React from 'react';
import { Controller, useFieldArray } from 'react-hook-form';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';

function ShiplineLocationRateItem({ control, itemIndex, type, rateType }) {
  const { fields, append, remove } = useFieldArray({
    control,
    name: `location_rates[${itemIndex}].${rateType}.${type}`,
  });
  if (fields.length === 0) {
    return (
      <IconButton
        size="small"
        color="success"
        onClick={async () => {
          append({
            id: 'id_' + Date.now(),
            rate_name: '',
            rate_value: '',
          });
        }}
        aria-label="add"
      >
        <AddIcon />
      </IconButton>
    );
  }
  return fields.map((rate_item: any, i: number) => {
    return (
      <>
        <Grid key={rate_item.id} container spacing={1} sx={{ mb: 1 }}>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name={`location_rates[${itemIndex}].${rateType}.${type}[${i}].rate_name`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={error ? true : false}
                  label="Rate name"
                  fullWidth
                  variant="outlined"
                  {...field}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Controller
                name={`location_rates.${itemIndex}.${rateType}.${type}[${i}].rate_value`}
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="Rate Value"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
              <IconButton
                size="small"
                color="error"
                onClick={async () => remove(i)}
                aria-label="delete"
              >
                <CloseIcon />
              </IconButton>
              <IconButton
                size="small"
                color="success"
                onClick={async () =>
                  append({
                    id: 'id_' + Date.now(),
                    rate_name: '',
                    rate_value: '',
                  })
                }
                aria-label="add"
              >
                <AddIcon />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </>
    );
  });
}

export default ShiplineLocationRateItem;
