import ViewModal from '@/components/mainComponents/ViewModal';
import { Box, Grid, useTheme } from '@mui/material';

const ViewRateLog = ({ show, setView, data, selectedItem }) => {
  const theme = useTheme();
  const rates = (rateTypes) => {
    const originHc40Sum = rateTypes.reduce(
      (sum, rate) =>
        rate.freight_rate_category === 'origin' &&
        rate.container_type === 'hc40'
          ? sum + rate.rate_value
          : sum,
      0,
    );

    const originHc45Sum = rateTypes.reduce(
      (sum, rate) =>
        rate.freight_rate_category === 'origin' &&
        rate.container_type === 'hc45'
          ? sum + rate.rate_value
          : sum,
      0,
    );

    const destinationHc40Sum = rateTypes.reduce(
      (sum, rate) =>
        rate.freight_rate_category === 'destination' &&
        rate.container_type === 'hc40'
          ? sum + rate.rate_value
          : sum,
      0,
    );
    const destinationHc45Sum = rateTypes.reduce(
      (sum, rate) =>
        rate.freight_rate_category === 'destination' &&
        rate.container_type === 'hc40'
          ? sum + rate.rate_value
          : sum,
      0,
    );

    return (
      <table style={{ width: '100%' }}>
        <tr>
          <th style={{ borderBottom: 1, textAlign: 'center' }} colSpan={2}>
            Origin
          </th>
          <th style={{ borderBottom: 1, textAlign: 'center' }} colSpan={2}>
            Destination
          </th>
        </tr>
        <tr>
          <td>45HC</td>
          <td>40HC</td>
          <td>45HC</td>
          <td>40HC</td>
        </tr>
        <tr>
          <td>{originHc40Sum}</td>
          <td>{originHc45Sum}</td>
          <td>{destinationHc45Sum}</td>
          <td>{destinationHc40Sum}</td>
        </tr>
      </table>
    );
  };

  return (
    <ViewModal
      createdBy={
        <>
          {selectedItem?.createdByUser?.fullname}
          {selectedItem?.createdByUser?.departments?.name &&
            ' | ' + selectedItem?.createdByUser?.departments?.name}
        </>
      }
      name={selectedItem?.name}
      created_at={data?.created_at}
      updated_at={data?.updated_at}
      onClose={() => setView(false)}
      show={show}
      title="Archived Rate View "
    >
      <Box>
        <Grid
          container
          sx={{
            paddingY: '6px',
            paddingLeft: '20px',
            fontSize: '14px',
            textAlign: 'center',
            backgroundColor:
              theme.palette.mode == 'dark' ? 'gray' : 'lightgray',
          }}
        >
          <Grid
            size={{
              xs: 1,
              sm: 1,
              md: 1,
            }}
          >
            Ship Line
          </Grid>

          <Grid
            size={{
              xs: 11,
              sm: 11,
              md: 11,
            }}
          >
            <Grid container>
              <Grid
                size={{
                  xs: 1,
                  sm: 1,
                  md: 1,
                }}
              >
                Destination
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Houston,TX
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Savannah,GA
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Los Angeles, CA
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                New Jersey, NJ
              </Grid>
              <Grid
                size={{
                  xs: 2,
                  sm: 2,
                  md: 2,
                }}
              >
                Baltimore, MD
              </Grid>
              <Grid
                size={{
                  xs: 1,
                  sm: 1,
                  md: 1,
                }}
              >
                Jacksonville, FL
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        {data?.length > 0 &&
          data?.map((item, i) => (
            <Grid
              key={i}
              container
              sx={{
                paddingY: '6px',
                paddingLeft: '20px',
                fontSize: '12px',
                borderBottom: 1,
              }}
            >
              <Grid
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
                size={{
                  xs: 1,
                  sm: 1,
                  md: 1,
                }}
              >
                {item?.steamshiplines?.name}
              </Grid>
              <Grid
                size={{
                  xs: 11,
                  sm: 11,
                  md: 11,
                }}
              >
                {item?.freight_rates?.map((rate, index) => (
                  <Grid
                    container
                    key={index}
                    sx={{
                      py: 1,
                      ...((index + 1) % 2 != 0 ? { borderBottom: 1 } : {}),
                    }}
                  >
                    <Grid
                      size={{
                        xs: 1,
                        sm: 1,
                        md: 1,
                      }}
                    >
                      {rate?.destinations?.name}
                    </Grid>
                    <Grid
                      size={{
                        xs: 2,
                        sm: 2,
                        md: 2,
                      }}
                    >
                      {rate?.point_of_loading_id == 2
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                    <Grid
                      size={{
                        xs: 2,
                        sm: 2,
                        md: 2,
                      }}
                    >
                      {rate?.point_of_loading_id == 1
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                    <Grid
                      size={{
                        xs: 2,
                        sm: 2,
                        md: 2,
                      }}
                    >
                      {rate?.point_of_loading_id == 4
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                    <Grid
                      size={{
                        xs: 2,
                        sm: 2,
                        md: 2,
                      }}
                    >
                      {rate?.point_of_loading_id == 5
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                    <Grid
                      size={{
                        xs: 2,
                        sm: 2,
                        md: 2,
                      }}
                    >
                      {rate?.point_of_loading_id == 6
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                    <Grid
                      size={{
                        xs: 1,
                        sm: 1,
                        md: 1,
                      }}
                    >
                      {rate?.point_of_loading_id == 9
                        ? rates(rate?.rate_types)
                        : ''}
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          ))}
        {/*   {data.length > 0 &&
          data?.map((item, i) => (
            <Grid
              key={i}
              container
              sx={{
                paddingY: '6px',
                paddingLeft: '20px',
                fontSize: '12px',
                borderBottom: 1,
              }}
            >
              <Grid
                item
                xs={1}
                sm={1}
                md={1}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {item?.steamshiplines?.name}
              </Grid>

              <Grid
                item
                xs={1}
                sm={1}
                md={1}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {item?.destinations?.name}
              </Grid>
              <Grid
                item
                xs={1}
                sm={1}
                md={1}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <Chip
                  size="small"
                  label={item?.status}
                  sx={{
                    color: 'white',
                    textTransform: 'capitalize',
                    backgroundColor:
                      item?.status === 'archived' ? 'blue' : 'red',
                  }}
                />
              </Grid>
              <Grid item xs={9} sm={9} md={9}>
                <Grid container>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 2)}
                  </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 1)}
                  </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 4)}
                  </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 5)}
                  </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 6)}
                  </Grid>
                  <Grid item xs={2} sm={2} md={2}>
                    {nestRate(item?.freight_rates, 9)}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          ))} */}
      </Box>
    </ViewModal>
  );
};

export default ViewRateLog;
