import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Input,
  TextField,
  Typography,
} from '@mui/material';

import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import ShiplineLocationRateItem from './ShiplineLocationRateItem';
import { useState } from 'react';
import { formFormatDate, static_locations } from '@/configs/configs';

export default function AddStep({ form, destinations }) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'location_rates',
  });

  const locations = static_locations.map((e) => ({
    id: e?.id,
    label: e?.name,
  }));

  const [selectedFile, setSelectedFile] = useState(null);
  //@ts-ignore
  const [preview, setPreview] = useState('');

  const handleUploadedFile = (event) => {
    const file = event.target.files[0];
    let urlImage: string;
    if (file) {
      urlImage = URL.createObjectURL(file);
      form.setValue('attachment', file);
    }
    setPreview(urlImage);
    if (file) {
      setSelectedFile(file);
    }
  };

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        Shipline Rates Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="shipline_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Steamshipline"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'steamshiplines'}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name={`contruct_number`}
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error ? true : false}
                value={field.value ?? ''}
                label="Contruct Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 4,
          }}
        >
          <Controller
            control={form.control}
            name="effective_date_from"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Effective Date From"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => field.onChange(formFormatDate(e))}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 4,
          }}
        >
          <Controller
            control={form.control}
            name="effective_date_to"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Exp Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => field.onChange(formFormatDate(e))}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 4,
          }}
        >
          <label htmlFor="fileInput" style={{ width: '95%' }}>
            {selectedFile ? (
              <Box>
                {selectedFile.name.substring(0, 50)}
                <IconButton
                  size="small"
                  color="error"
                  onClick={async (e) => {
                    e.preventDefault();
                    setPreview('');
                    form.setValue('attachment', null);
                    setSelectedFile(null);
                  }}
                  aria-label="delete"
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            ) : (
              <>
                <Input
                  type="file"
                  id="fileInput"
                  style={{ display: 'none' }}
                  onChange={handleUploadedFile}
                />
                <Button
                  component="span"
                  variant="contained"
                  color="primary"
                  style={{ width: '100%' }}
                >
                  Choose File
                </Button>
              </>
            )}
          </label>
        </Grid>
      </Grid>
      <Box component={'hr'} sx={{ my: 1 }}></Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Freight Rates
      </Typography>
      {fields.map((item: any, i) => (
        <Box
          sx={{
            pt: 4,
            pb: 2,
            px: 2,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={async () => remove(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 1 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={2}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`location_rates.${i}.point_of_destination_id`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Select Destination"
                    fieldName="name"
                    field={field}
                    error={error}
                    staticOptions={destinations}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`location_rates.${i}.point_of_loading_id`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="POL (Port Of Loading)"
                    fieldName="name"
                    field={field}
                    error={error}
                    staticOptions={locations}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`location_rates.${i}.free_days`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="Free Days"
                    fullWidth
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Box
            sx={{
              mt: 2,
              border: '1px solid #cccccc',
              borderRadius: '6px',
              pt: '5px',
              px: 1,
              pb: 1,
            }}
          >
            <Typography sx={{ pb: 1 }}>40HC</Typography>
            <Grid container spacing={2}>
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Box
                  sx={{
                    border: '1px solid #cccccc',
                    borderRadius: '6px',
                    paddingTop: '2px',
                    px: 1,
                    pb: 1,
                  }}
                >
                  <Typography sx={{ pb: 1 }}>Origin</Typography>
                  <ShiplineLocationRateItem
                    control={form.control}
                    itemIndex={i}
                    type={'origin_rates'}
                    rateType={'rate_40hc'}
                  />
                </Box>
              </Grid>
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Box
                  sx={{
                    border: '1px solid #cccccc',
                    borderRadius: '6px',
                    paddingTop: '2px',
                    px: 1,
                    pb: 1,
                  }}
                >
                  <Typography sx={{ pb: 1 }}>Destination</Typography>
                  <ShiplineLocationRateItem
                    control={form.control}
                    itemIndex={i}
                    type={'destination_rates'}
                    rateType={'rate_40hc'}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              mt: 2,
              border: '1px solid #cccccc',
              borderRadius: '6px',
              pt: '5px',
              px: 1,
              pb: 1,
            }}
          >
            <Typography sx={{ pb: 1 }}>45HC</Typography>
            <Grid container spacing={2}>
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Box
                  sx={{
                    border: '1px solid #cccccc',
                    borderRadius: '6px',
                    paddingTop: '2px',
                    px: 1,
                    pb: 1,
                  }}
                >
                  <Typography sx={{ pb: 1 }}>Origin</Typography>
                  <ShiplineLocationRateItem
                    control={form.control}
                    itemIndex={i}
                    type={'origin_rates'}
                    rateType={'rate_45hc'}
                  />
                </Box>
              </Grid>
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Box
                  sx={{
                    border: '1px solid #cccccc',
                    borderRadius: '6px',
                    paddingTop: '2px',
                    px: 1,
                    pb: 1,
                  }}
                >
                  <Typography sx={{ pb: 1 }}>Destination</Typography>
                  <ShiplineLocationRateItem
                    control={form.control}
                    itemIndex={i}
                    type={'destination_rates'}
                    rateType={'rate_45hc'}
                  />
                </Box>
              </Grid>
            </Grid>
          </Box>
          <Grid container spacing={1} sx={{ mt: 2 }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`location_rates[${i}].origin_note`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    label="Origin Note"
                    fullWidth
                    variant="outlined"
                    {...field}
                    multiline
                    rows={3}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`location_rates[${i}].destination_note`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    label="Destination Note"
                    fullWidth
                    variant="outlined"
                    {...field}
                    multiline
                    rows={3}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
        <Button
          variant="contained"
          size="small"
          onClick={() => {
            const id = 'id_' + Date.now();
            append({
              id: id,
              point_of_destination_id: '',
              point_of_loading_id: '',
              free_days: 0,
              origin_note: '',
              destination_note: '',
              rate_40hc: {
                origin_rates: [
                  {
                    id: 'orgin_' + id,
                    rate_value: 0,
                    rate_name: '',
                  },
                ],
                destination_rates: [
                  {
                    id: 'destination_' + id,
                    rate_value: 0,
                    rate_name: '',
                  },
                ],
              },
              rate_45hc: {
                origin_rates: [
                  {
                    id: 'orgin2_' + id,
                    rate_value: 0,
                    rate_name: '',
                  },
                ],
                destination_rates: [
                  {
                    id: 'destination2_' + id,
                    rate_value: 0,
                    rate_name: '',
                  },
                ],
              },
            });
          }}
        >
          Add Rate
        </Button>
      </Box>
    </Box>
  );
}
