import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { Box, Grid, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import { rate_category } from './Header';

export default function AddStepOne({ form }) {
  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        Shipline Rate Name Details
      </Typography>
      <Box>
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="rate_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.rate_name?.message.length > 0 ? true : false
                  }
                  id="rate_name"
                  value={field.value ?? ''}
                  label="Rate Name"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="shipline_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url="autoComplete"
                  label="Steamshipline"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'steamshiplines'}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="rate_category"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Rate Category"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={rate_category}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          ></Grid>
        </Grid>
      </Box>
    </Box>
  );
}
