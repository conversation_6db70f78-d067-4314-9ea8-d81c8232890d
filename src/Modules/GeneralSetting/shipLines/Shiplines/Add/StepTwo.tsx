import {
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { contact_types } from '../Header';

export default function StepTwo({ form }) {
  const {
    fields: wlField,
    append: wlAppend,
    remove: wlRemove,
  } = useFieldArray({
    control: form.control,
    name: 'webLinks',
  });

  const {
    fields: cField,
    append: cAppend,
    remove: cRemove,
  } = useFieldArray({
    control: form.control,
    name: 'contacts',
  });

  /* useEffect(() => {
    if (cField.length === 0) cAppend({ name: '', email: '' });
    if (wlField.length === 0) wlAppend({ url: '' });
  }, [cField, wlField, cAppend, wlAppend]); */

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        Shipline Profiles Details
      </Typography>
      {/* Start Add Website Links */}
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Website Links
      </Typography>
      {wlField.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
            marginBottom: 2,
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={() => wlRemove(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={1} sx={{ paddingBottom: '1px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`webLinks.${i}.web_link`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.web_link?.message.length > 0 ? true : false
                    }
                    id="web_link"
                    value={field.value ?? ''}
                    label="Web Link (URL)"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`webLinks.${i}.description`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.description?.message.length > 0 ? true : false
                    }
                    id="description"
                    value={field.value ?? ''}
                    label="Description"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
        <Button
          variant="contained"
          size="small"
          onClick={() => wlAppend({ name: '' })}
        >
          Add More Website Link
        </Button>
      </Box>
      {/* End Add Website Links */}
      {/* Start Add Contacts */}
      <Box component={'hr'} sx={{ my: 1 }}></Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Contacts
      </Typography>
      {cField.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
            marginBottom: 2,
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={() => cRemove(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={1} sx={{ paddingBottom: '1px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`contacts.${i}.contact_type`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Contact Types"
                    fieldName=""
                    field={field}
                    error={error}
                    staticOptions={contact_types}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`contacts.${i}.contact_line`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.contact_line?.message.length > 0
                        ? true
                        : false
                    }
                    id="contact_line"
                    value={field.value ?? ''}
                    label="Contact Line"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>

            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`contacts.${i}.point_of_contact`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.point_of_contact?.message.length > 0
                        ? true
                        : false
                    }
                    id="point_of_contact"
                    value={field.value ?? ''}
                    label="Point Of Contact"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`contacts.${i}.related_section`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.related_section?.message.length > 0
                        ? true
                        : false
                    }
                    id="related_section"
                    value={field.value ?? ''}
                    label="Related Section"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
        <Button
          variant="contained"
          size="small"
          onClick={() => cAppend({ name: '' })}
        >
          Add More Contact
        </Button>
      </Box>
      {/* End Add Contacts */}
    </Box>
  );
}
