import {
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';

export default function StepOne({ form }) {
  const {
    fields: credField,
    append: credAppend,
    remove: credRemove,
  } = useFieldArray({
    control: form.control,
    name: 'credentials',
  });
  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        Shipline Profiles Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="name"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.name?.message.length > 0 ? true : false}
                id="name"
                value={field.value ?? ''}
                label="Name"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onC<PERSON>e(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="contract_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.contract_number?.message.length > 0 ? true : false
                }
                id="contract_number"
                value={field.value ?? ''}
                label="Contruct Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="note"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.note?.message.length > 0 ? true : false}
                id="note"
                value={field.value ?? ''}
                label="Note"
                multiline
                rows={3}
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
      {/* Start Add Credentials */}
      <Box component={'hr'} sx={{ my: 1 }}></Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Credentials
      </Typography>
      {credField.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
            marginBottom: 2,
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={() => credRemove(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={1} sx={{ paddingBottom: '1px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`credentials.${i}.username`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.username?.message.length > 0 ? true : false
                    }
                    id="username"
                    value={field.value ?? ''}
                    label="Username"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`credentials.${i}.password`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.password?.message.length > 0 ? true : false
                    }
                    id="password"
                    value={field.value ?? ''}
                    label="Password"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
        <Button
          variant="contained"
          size="small"
          onClick={() => credAppend({ name: '' })}
        >
          Add More Credential
        </Button>
      </Box>
      {/* End Add Credentials */}
    </Box>
  );
}
