import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import axios from '@/lib/axios';
import { SHIPLINES } from '@/configs/leftSideMenu/Permissions';
import { Box, Grid } from '@mui/material';

export default function ViewCredential({ open, setOpen, selectedItem, perms }) {
  const [cancelToken, setCancelToken] = React.useState(null);
  const [credentials, setCredentials] = React.useState([]);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const view_credential = perms?.includes(SHIPLINES?.VIEW_CREDENTIAL);

  const fetchCredential = async () => {
    try {
      const { id } = selectedItem;
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setCredentials([]);
      let { data } = await axios.get(`steamshiplines/getCredentials/${id}`);
      setCredentials(data.data);
    } catch (error) {
      console.error(error);
    }
  };

  React.useEffect(() => {
    if (view_credential && Object.keys(selectedItem).length > 0)
      fetchCredential();
  }, [selectedItem]);
  const handleClose = () => setOpen(false);

  return (
    <Dialog
      fullScreen={fullScreen}
      open={open}
      onClose={handleClose}
      aria-labelledby="responsive-dialog-title"
      sx={{
        '& .MuiDialog-paper': {
          width: '600px', // Custom width
          maxWidth: 'none', // Disable default maxWidth
        },
      }}
    >
      <DialogTitle id="responsive-dialog-title">
        {selectedItem?.name} Credentials
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          {view_credential ? (
            <Box>
              {/* Header Row */}
              <Grid
                container
                spacing={2}
                sx={{
                  fontWeight: 'bold',
                  borderBottom: '1px solid #ccc',
                  pb: 1,
                }}
              >
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  USERNAME
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  PASSWORD
                </Grid>
              </Grid>

              {/* Credentials Rows */}
              {credentials?.length > 0 ? (
                credentials.map((cred, index) => (
                  <Grid
                    container
                    spacing={2}
                    key={index}
                    sx={{
                      borderBottom: '1px solid #f0f0f0',
                      pb: 1,
                      pt: 1,
                    }}
                  >
                    <Grid
                      size={{
                        xs: 12,
                        md: 6,
                      }}
                    >
                      {cred?.username || 'N/A'}
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 6,
                      }}
                    >
                      {cred?.password || 'N/A'}
                    </Grid>
                  </Grid>
                ))
              ) : (
                <Box sx={{ mt: 2 }}>No credentials available.</Box>
              )}
            </Box>
          ) : (
            <Box sx={{ mt: 2 }}>
              You don't have permission to view credentials.
            </Box>
          )}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} autoFocus>
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
}
