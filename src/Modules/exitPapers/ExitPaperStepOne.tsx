import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { formFormatDate } from '@/configs/configs';
import {
  currencyOptions,
  is_credit_given,
  is_mukhasa,
} from '@/configs/exitPapers/exitPapersHeader';
import axios from '@/lib/axios';
import {
  Autocomplete,
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useCallback, useContext, useEffect, useRef } from 'react';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import isValidTwoDecimal from '@/utils/isValidTwoDecimal';
import { contextProvider } from '@/contexts/ProfileContext';
import { EXIT_PAPERS } from '@/configs/leftSideMenu/Permissions';

const ExitPaperStepOne = ({ form, watch, isUpdate }) => {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'vehicles',
  });

  const {
    fields: customVehicleFields,
    append: appendCustomVehicle,
    remove: removeCustomVehicle,
  } = useFieldArray({
    control: form.control,
    name: 'custom_vehicles',
  });
  const isMukhasa = watch('is_mukhasa', false);
  const prevVehicleIdsRef = useRef([]);
  const hasCredits: boolean =
    typeof watch('customerPaymentTransactionId') === 'number' &&
    !!watch('customerPaymentTransactionId');
  const hasPaymentsApplied: boolean =
    typeof watch('amount_applied') === 'number' && watch('amount_applied') > 0;
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const userPermissions = profile?.data?.loginable?.permissions?.map(
    (op) => op.name,
  );

  const disableIfHasCreditsOrHasPayments = () =>
    (hasCredits &&
      !userPermissions.includes(EXIT_PAPERS?.UPDATE_IF_HAS_CREDITS)) ||
    hasPaymentsApplied
      ? { pointerEvents: 'none', opacity: 0.5 }
      : {};

  const getVehicle = useCallback(
    async (vehicleId, index) => {
      try {
        const res = await axios.get(`exit_papers/vehicle/${vehicleId}`);
        if (res.status === 200) {
          const expiredDate = res?.data?.containers?.clear_logs[0]?.log_invoices
            ?.invoice_date
            ? new Date(
                new Date(
                  res?.data?.containers?.clear_logs[0]?.log_invoices?.invoice_date,
                ).setMonth(
                  new Date(
                    res?.data?.containers?.clear_logs[0]?.log_invoices?.invoice_date,
                  ).getMonth() + 6,
                ),
              ).toISOString()
            : null;
          const sixMonthsLater = new Date(
            new Date().setMonth(new Date().getMonth() + 6),
          );
          form.setValue(
            'expired_date',
            expiredDate ? expiredDate : sixMonthsLater,
          );
          if (!isUpdate) {
            const vatAndCustom =
              res?.data?.mix_shipping_vehicles[0]?.vat_and_custom;
            const vatAndcustom =
              vatAndCustom && !isNaN(vatAndCustom) ? vatAndCustom / 2 : 0;
            const fivePercent = (res?.data?.price * 0.05).toFixed(2);

            form.setValue(
              `vehicles.${index}.vat`,
              vatAndcustom ? vatAndcustom : parseFloat(fivePercent),
            );
            if (!isMukhasa) {
              form.setValue(
                `vehicles.${index}.custom_duty`,
                vatAndcustom ? vatAndcustom : parseFloat(fivePercent),
              );
            } else {
              form.setValue(`vehicles.${index}.custom_duty`, 0);
            }
          }
          form.setValue(
            `vehicles.${index}.company_name`,
            res?.data?.companies?.name,
          );
          form.setValue(
            `vehicles.${index}.vehicle_description`,
            `${res?.data?.year} ${res?.data?.make} ${res?.data?.model} ${res?.data?.color}`,
          );
          form.setValue(`vehicles.${index}.vehicle_price`, res?.data?.price);
        }
      } catch (error) {
        console.log('Error fetching vehicle data:', error);
      }
    },
    [form],
  );

  useEffect(() => {
    const vehicleIds =
      form.watch('vehicles')?.map((item) => item.vehicle_id) || [];
    vehicleIds.forEach((vehicleId, index) => {
      if (vehicleId && prevVehicleIdsRef.current[index] !== vehicleId) {
        getVehicle(vehicleId, index);
      }
    });

    prevVehicleIdsRef.current = vehicleIds;
  }, [form.watch('vehicles').map((item) => item.vehicle_id), getVehicle]);

  useEffect(() => {
    if (fields.length === 0 && !isUpdate) {
      append({
        vehicle_id: '',
        company_name: '',
        vehicle_description: '',
        vat: 0,
        custom_duty: 0,
        vehicle_price: 0,
      });
    }
  }, []);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">Exit Paper Details</Typography>
      <Grid container spacing={2}>
        <Grid
          mt={1.5}
          sx={{
            ...disableIfHasCreditsOrHasPayments(),
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="is_mukhasa"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Is Mukhasa"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={is_mukhasa}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          mt={1.5}
          sx={{
            ...(hasCredits ? { pointerEvents: 'none', opacity: 0.5 } : {}),
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="is_credit_given"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Is Credit Given"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={is_credit_given}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>
        </Grid>

        {!isMukhasa && (
          <Grid
            sx={{
              ...disableIfHasCreditsOrHasPayments(),
            }}
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Grid size={12}>
              <Controller
                name="service_charge"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.service_charge?.message.length > 0
                        ? true
                        : false
                    }
                    id="Service Charge"
                    value={field.value ?? ''}
                    label="Service Charge"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        )}
        <Grid
          sx={{
            ...disableIfHasCreditsOrHasPayments(),
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="issue_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Issue Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="request_no"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.request_no?.message.length > 0 ? true : false
                  }
                  id="request_no"
                  value={field.value ?? ''}
                  label="Request No"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="declaration_number"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.declaration_number?.message.length > 0
                      ? true
                      : false
                  }
                  id="declaration_number"
                  value={field.value ?? ''}
                  label="Declaration Number"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="declaration_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Declaration Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="claim_number"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.claim_number?.message.length > 0 ? true : false
                  }
                  id="claim_number"
                  value={field.value ?? ''}
                  label="Claim Number"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="claim_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Claim Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              control={form.control}
              name="expired_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  disabled
                  views={['year', 'month', 'day']}
                  label="Expire Date"
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      size: 'small',
                      error: !!error,
                      helperText: error?.message,
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="prove"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.prove?.message.length > 0 ? true : false}
                  id="prove"
                  value={field.value ?? ''}
                  label="Prove"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid sx={{ px: 0.5, mb: 1 }} size={12}>
          <Grid size={12}>
            <Controller
              name="description"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.description?.message.length > 0 ? true : false
                  }
                  id="description"
                  value={field.value ?? ''}
                  label="Descrption"
                  fullWidth
                  variant="outlined"
                  multiline
                  rows={5}
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
      </Grid>
      <Box component={'hr'} sx={{ my: 1 }}></Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Vehicle Details
      </Typography>
      {fields.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
            marginBottom: 2,
            ...disableIfHasCreditsOrHasPayments(),
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={() => remove(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={2}>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.vehicle_id`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => {
                    return (
                      <AutoComplete
                        url="autoComplete"
                        label="Select Vin"
                        fieldName="vin"
                        field={field}
                        error={error}
                        customeName="vin"
                        staticOptions={false}
                        column={'vin'}
                        modal={'vehicles'}
                      />
                    );
                  }}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.company_name`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      id="company_name"
                      value={field.value ?? ''}
                      label="Company Name"
                      fullWidth
                      variant="outlined"
                      disabled
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.vehicle_description`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      id="vehicle_description"
                      value={field.value ?? ''}
                      label="Vehicle Description"
                      fullWidth
                      variant="outlined"
                      disabled
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.vat`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.vehicles?.[i]?.vat?.message.length > 0
                          ? true
                          : false
                      }
                      id="Vat"
                      value={field.value ?? ''}
                      label="Vat"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(e) => {
                        const val = +e.target.value;
                        if (isValidTwoDecimal(val)) {
                          field.onChange(val);
                        }
                      }}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            {!isMukhasa && (
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Grid size={12}>
                  <Controller
                    name={`vehicles.${i}.custom_duty`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.vehicles?.[i]?.custom_duty?.message
                            .length > 0
                            ? true
                            : false
                        }
                        id="Custom Duty"
                        value={field.value ?? ''}
                        label="Custom Duty"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(e) => {
                          const val = +e.target.value;
                          if (isValidTwoDecimal(val)) {
                            field.onChange(val);
                          }
                        }}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            )}
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`vehicles.${i}.currency`}
                control={form.control}
                render={({ field, fieldState: { error, invalid } }) => {
                  return (
                    <Autocomplete
                      sx={{ width: '100%' }}
                      size="small"
                      value={field.value}
                      onChange={(_, value) => {
                        field.onChange(value);
                      }}
                      options={currencyOptions}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Currency"
                          error={invalid}
                          helperText={error?.message}
                        />
                      )}
                    />
                  );
                }}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.claim_amount`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.vehicles?.[i]?.custom_duty?.message.length >
                        0
                          ? true
                          : false
                      }
                      id="Claim Amount"
                      value={field.value ?? ''}
                      label="Claim Amount"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) => field.onChange(+value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`vehicles.${i}.vehicle_price`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      id="Vehicle Price"
                      value={field.value ?? ''}
                      label="Vehicle Price"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) => field.onChange(+value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                      disabled
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 1,
          ...disableIfHasCreditsOrHasPayments(),
        }}
      >
        <Button
          variant="contained"
          size="small"
          onClick={() => append({ name: 'vehicles' })}
        >
          Add Vehicle
        </Button>
      </Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Custom Vehicle Details
      </Typography>
      {customVehicleFields.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
            marginBottom: 2,
            ...disableIfHasCreditsOrHasPayments(),
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={() => removeCustomVehicle(i)}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Grid container spacing={2}>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_vin`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => {
                    return (
                      <TextField
                        size="small"
                        error={
                          form.errors.custom_vehicles?.[i]?.c_vin?.message
                            .length > 0
                            ? true
                            : false
                        }
                        id="c_vehicles_vin"
                        value={field.value ?? ''}
                        label="Vin"
                        fullWidth
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    );
                  }}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_company_name`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.custom_vehicles?.[i]?.c_company_name
                          ?.message.length > 0
                          ? true
                          : false
                      }
                      id="c_company_name"
                      value={field.value ?? ''}
                      label="Company Name"
                      fullWidth
                      variant="outlined"
                      onChange={(value) => field.onChange(value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 4,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_vehicle_description`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.custom_vehicles?.[i]?.c_vehicle_description
                          ?.message.length > 0
                          ? true
                          : false
                      }
                      id="c_vehicle_description"
                      value={field.value ?? ''}
                      label="Vehicle Description"
                      fullWidth
                      variant="outlined"
                      onChange={(value) => field.onChange(value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_vat`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.custom_vehicles?.[i]?.c_vat?.message
                          .length > 0
                          ? true
                          : false
                      }
                      id="c_vat"
                      value={field.value ?? ''}
                      label="Vat"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) => field.onChange(+value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            {!isMukhasa && (
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <Grid size={12}>
                  <Controller
                    name={`custom_vehicles.${i}.c_custom_duty`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.custom_vehicles?.[i]?.c_custom_duty
                            ?.message.length > 0
                            ? true
                            : false
                        }
                        id="c_custom_duty"
                        value={field.value ?? ''}
                        label="Custom Duty"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(+value.target.value)
                        }
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            )}
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`custom_vehicles.${i}.c_currency`}
                control={form.control}
                render={({ field, fieldState: { error, invalid } }) => {
                  return (
                    <Autocomplete
                      sx={{ width: '100%' }}
                      size="small"
                      value={field.value}
                      onChange={(_, value) => {
                        field.onChange(value);
                      }}
                      options={currencyOptions}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Currency"
                          error={invalid}
                          helperText={error?.message}
                        />
                      )}
                    />
                  );
                }}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_claim_amount`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.vehicles?.[i]?.custom_duty?.message.length >
                        0
                          ? true
                          : false
                      }
                      id="c_claim_amount"
                      value={field.value ?? ''}
                      label="Claim Amount"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) => field.onChange(+value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_vehicle_price`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.custom_vehicles?.[i]?.c_vehicle_price
                          ?.message.length > 0
                          ? true
                          : false
                      }
                      id="c_vehicle_price"
                      value={field.value ?? ''}
                      label="Vehicle Price"
                      fullWidth
                      type="number"
                      variant="outlined"
                      onChange={(value) => field.onChange(+value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Grid size={12}>
                <Controller
                  name={`custom_vehicles.${i}.c_container_number`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={
                        form.errors.custom_vehicles?.[i]?.c_container_number
                          ?.message.length > 0
                          ? true
                          : false
                      }
                      id="c_container_number"
                      value={field.value ?? ''}
                      label="Container number"
                      fullWidth
                      variant="outlined"
                      onChange={(value) => field.onChange(value.target.value)}
                      ref={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>
        </Box>
      ))}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 1,
          ...disableIfHasCreditsOrHasPayments(),
        }}
      >
        <Button
          variant="contained"
          size="small"
          onClick={() => appendCustomVehicle({ name: 'custom_vehicles' })}
        >
          Add Custom Vehicle
        </Button>
      </Box>
    </Box>
  );
};

export default ExitPaperStepOne;
