import CStepper from '@/components/mainComponents/stepper/CStepper';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import InfoIcon from '@mui/icons-material/Info';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import ExitPaperStepOne from './ExitPaperStepOne';
import { schema } from '@/configs/exitPapers/exitPapersHeader';

interface ExchangeRateType {
  currency: string;
  rate: number;
  created_at: string;
}

export const CreateExitPaper = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
}) => {
  const [loadingButton, setLoadingButton] = useState(false);
  const [isDone, setIsDone] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    control,
    getValues,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      issue_date: null,
      declaration_number: null,
      declaration_date: null,
      claim_number: null,
      claim_date: null,
      request_no: null,
      service_charge: null,
      status: null,
      is_credit_given: null,
      is_mukhasa: null,
      prove: null,
      description: null,
      expired_date: null,
      vehicles: [],
      custom_vehicles: [],
      // START: Exit Paper Credits
      exchange_rate_for_service_charge: {},
      customerPaymentTransactionId: null,
      amount: 0,
      amount_applied: 0,
      customer_credits: [],
      // END: Exit Paper Credits
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    setValue,
    getValues,
  };

  const steps = [
    {
      label: 'Exit Paper Details',
      icon: <InfoIcon />,
      step: <ExitPaperStepOne form={form} watch={watch} isUpdate={isUpdate} />,
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger([
          'issue_date',
          'declaration_number',
          'declaration_date',
          'claim_number',
          'claim_date',
          'request_no',
          'service_charge',
          'status',
          'is_credit_given',
          'is_mukhasa',
          'prove',
          'description',
          'vehicles',
          'custom_vehicles',
        ]);
        return isValid;
      },
    },
  ];

  const submit = async (values) => {
    if (values.is_mukhasa) {
      values.vehicles = (values.vehicles || []).map((v) => ({
        ...v,
        custom_duty: 0,
      }));
      values.custom_vehicles = (values.custom_vehicles || []).map((v) => ({
        ...v,
        c_custom_duty: 0,
      }));
    }
    values.status = form.getValues('status');

    // START: Handle Exit Paper Credits
    if (
      typeof selectedItems[0]?.customerPaymentTransactionId === 'number' &&
      selectedItems[0]?.customerPaymentTransactionId > 0
    ) {
      // START: Handle Data for Multi Exit Paper Credits
      // // Get Exit Papers Excluding current exit paper
      const selected = selectedItems[0];
      const otherExitPapers = (
        selected?.customerPaymentTransaction?.exitPapers || []
      )
        .filter((exitPaper) => exitPaper.id !== selected?.id)
        .map((exitPaper) => {
          const validVINs = exitPaper.exit_papers_vehicles
            .map((epv) => epv.vehicles?.vin)
            .filter((vin) => vin);

          const filteredVehicleCredits = (
            selected?.customerPaymentTransaction?.customer_credits
              .filter((credit) => credit?.deleted_at === null)
              .filter((credit) => validVINs.includes(credit.vehicle?.vin)) || []
          ).map((vc) => ({
            vehicle_id: vc?.vehicle_id,
            amount: Number(vc?.amount),
            remark: vc?.remark,
            id: vc?.id,
          }));

          const validAmountForCustomVehicles = exitPaper.exit_papers_vehicles
            .map((epv) =>
              epv.vehicle_id === null
                ? Number(epv?.vat || 0) + Number(epv?.custom_duty || 0)
                : undefined,
            )
            .filter((vin) => vin);

          const filteredCustomVehicleCredits = (
            selected?.customerPaymentTransaction?.customer_credits
              .filter((credit) => credit?.deleted_at === null)
              .filter((credit) =>
                validAmountForCustomVehicles.includes(Number(credit?.amount)),
              ) || []
          ).map((cvc) => ({
            amount: Number(cvc?.amount),
            remark: cvc?.remark,
            id: cvc?.id,
          }));

          return {
            ...exitPaper,
            customerPaymentTransaction: {
              customer_credits: [
                ...filteredVehicleCredits,
                ...filteredCustomVehicleCredits,
              ],
            },
          };
        });

      // Get all exit paper credits except current exit paper's credits
      const otherExitPaperCredits = otherExitPapers.flatMap(
        (exitPaper) =>
          exitPaper?.customerPaymentTransaction?.customer_credits || [],
      );

      // Get all exit paper credits ids except current exit paper's credits ids
      const otherExitPaperCreditsIds = otherExitPaperCredits.map(
        (credit) => credit?.id,
      );

      // Get all service charges except current exit paper's service charge
      const otherTotalServiceCharges =
        otherExitPapers.reduce((sum, acc) => sum + acc.service_charge, 0) || 0;

      // remove other exit papers' credits from current exit papers' credits
      const current_customer_credits = values?.customer_credits.filter(
        (credit) => !otherExitPaperCreditsIds.includes(credit?.id),
      );
      // END: Handle Data for Multi Exit Paper Credits

      // START: Handle Custom Vehicle Credits (vehicle_id is null)
      const existingCustomVehicleCredits = current_customer_credits.filter(
        (credit) => credit?.vehicle_id === null,
      );

      let ModifiedOrNewCustomVehicleCredits = values?.custom_vehicles.map(
        (vehicle) => ({
          amount: +vehicle?.c_custom_duty + +vehicle?.c_vat,
          remark: '',
        }),
      );

      // Updated: match by index
      const updatedCustomVehicleCredits = ModifiedOrNewCustomVehicleCredits.map(
        (modified, index) => {
          const existing = existingCustomVehicleCredits[index];
          return {
            ...modified,
            id: existing?.id ?? null,
            remark: existing?.remark || modified.remark,
          };
        },
      ).filter((credit) => credit);

      // New: entries in modified that exceed existing list
      const newCustomVehicleCredits = ModifiedOrNewCustomVehicleCredits.slice(
        existingCustomVehicleCredits.length,
      ).map((mod) => ({
        ...mod,
        id: null,
      }));
      // END: Handle Custom Vehicle Credits (vehicle_id is null)

      // START: Handle Vehicle Credits (vehicle_id !== null)
      const existingVehicleCredits = current_customer_credits.filter(
        (credit) => credit?.vehicle_id !== null,
      );

      let ModifiedOrNewVehicleCredits = values?.vehicles.map((vehicle) => ({
        vehicle_id: vehicle?.vehicle_id,
        amount: +vehicle?.custom_duty + +vehicle?.vat,
        remark: '',
      }));

      const existingVehicleIds = existingVehicleCredits.map(
        (v) => v.vehicle_id,
      );

      // Updated: matching vehicle_id
      const updatedVehicleCredits = ModifiedOrNewVehicleCredits.filter((mod) =>
        existingVehicleIds.includes(mod.vehicle_id),
      ).map((modified) => {
        const existing = existingVehicleCredits.find(
          (e) => e.vehicle_id === modified.vehicle_id,
        );
        return {
          ...modified,
          id: existing?.id ?? null,
          remark: existing?.remark || modified.remark,
        };
      });

      // New: not present before
      const newVehicleCredits = ModifiedOrNewVehicleCredits.filter(
        (mod) => !existingVehicleIds.includes(mod.vehicle_id),
      ).map((mod) => ({
        ...mod,
        id: null,
      }));
      // END: Handle Vehicle Credits (vehicle_id !== null)

      // ✅ START: FINAL COMBINED RESULT for Custom and Normal Vehicle Credits
      // and also credits from other exit papers (for multi exit paper credits)
      const vehicleCredits = [...updatedVehicleCredits, ...newVehicleCredits];
      const customVehicleCredits = Array.from(
        new Map(
          [...updatedCustomVehicleCredits, ...newCustomVehicleCredits].map(
            // remove duplicates
            (v) => [JSON.stringify(v), v],
          ),
        ).values(),
      );
      values.customer_credits = [
        ...vehicleCredits,
        ...customVehicleCredits,
        ...otherExitPaperCredits,
      ];
      // ✅ END: FINAL COMBINED RESULT for Custom and Normal Vehicle Credits
      // and also credits from other exit papers (for multi exit paper credits)

      const currency = form.getValues('vehicles')?.[0]?.currency;
      values.currency = currency;

      const exchange_rate_for_service_charge =
        currency === 'AED'
          ? { currency: 'USD', rate: 1 }
          : selectedItems[0]?.exit_papers_vehicles
              .sort(
                // Sort vehicles so that those with a non-null vehicle_id come before those with null
                (a, b) => {
                  if (a.vehicle_id === null && b.vehicle_id !== null) return 1;
                  if (a.vehicle_id !== null && b.vehicle_id === null) return -1;
                  return 0;
                },
              )[0]
              ?.vehicles?.companies?.exchange_rates.filter(
                (exchange_rate: ExchangeRateType) =>
                  exchange_rate?.currency === 'AED',
              )
              .sort(
                (a: { created_at: string }, b: { created_at: string }) =>
                  new Date(b.created_at).getTime() -
                  new Date(a.created_at).getTime(),
              )[0] || { currency: 'AED', rate: 3.685 };

      const totalCreditAmount = values.customer_credits?.reduce(
        (sum, item) => sum + (Number(item?.amount) || 0),
        0,
      );
      let totalAmountMinusServiceCharge;
      if (exchange_rate_for_service_charge?.currency === 'AED') {
        totalAmountMinusServiceCharge = Number(
          (
            totalCreditAmount -
            (values?.service_charge + otherTotalServiceCharges) /
              exchange_rate_for_service_charge?.rate
          ).toFixed(2),
        );
      } else {
        totalAmountMinusServiceCharge = Number(
          (totalCreditAmount - values?.service_charge).toFixed(2),
        );
      }
      values.amount = totalAmountMinusServiceCharge;
      values.credits_service_charge =
        values?.service_charge + otherTotalServiceCharges;
    }
    // END: Handle Exit Paper Credits

    if (!isUpdate) {
      try {
        setLoadingButton(true);
        const { data } = await axios.post('/exit_papers', values);
        if (data.result == true) {
          recordManager(data?.data, 'add');
          setIsDone(true);
          toast.success('Done successfully!');
          setLoadingButton(false);
          return true;
        } else {
          setLoadingButton(false);
          return false;
        }
      } catch (error) {
        setLoadingButton(false);
        console.log(error);
        return false;
      }
    } else {
      try {
        setLoadingButton(true);
        const { data } = await axios.patch(
          'exit_papers/' + selectedItems[0].id,
          values,
        );
        if (data.result === true) {
          recordManager(data.data, 'update');
          setIsDone(true);
          toast.success('Record updated successfully!');
          setLoadingButton(false);
          return true;
        }
        setLoadingButton(false);
        return false;
      } catch (error) {
        setLoadingButton(false);
        console.log(error);
        return false;
      }
    }
  };

  useEffect(() => {
    if (selectedItems.length > 0 && isUpdate) {
      setValue('issue_date', selectedItems[0]?.issue_date);
      setValue('declaration_number', selectedItems[0]?.declaration_number);
      setValue('declaration_date', selectedItems[0]?.declaration_date);
      setValue('claim_number', selectedItems[0]?.claim_number);
      setValue('request_no', selectedItems[0]?.request_no);
      setValue('service_charge', selectedItems[0]?.service_charge);
      setValue('status', selectedItems[0]?.status);
      setValue('is_credit_given', selectedItems[0]?.is_credit_given);
      setValue('is_mukhasa', selectedItems[0]?.is_mukhasa);
      setValue('prove', selectedItems[0]?.prove);
      setValue('description', selectedItems[0]?.description);
      setValue('expired_date', selectedItems[0]?.expired_date);
      setValue(
        'vehicles',
        selectedItems[0]?.exit_papers_vehicles
          ?.filter((item) => item?.vehicles !== null)
          .map((item) => ({
            vehicle_id: item?.vehicle_id,
            vat: +item?.vat,
            custom_duty: +item?.custom_duty,
            currency: item?.currency,
            claim_amount: item?.claim_amount,
            company_name: item?.vehicles?.companies?.name,
            vehicle_description: `${item?.vehicles?.year} ${item?.vehicles?.make} ${item?.vehicles?.model} ${item?.vehicles?.color}`,
            vehicle_price: item?.vehicles?.price,
          })) || [],
      );
      setValue(
        'custom_vehicles',
        selectedItems[0]?.exit_papers_vehicles
          ?.filter((item) => item?.vehicles === null)
          .map((item) => ({
            c_vin: item?.custom_vin,
            c_company_name: item?.custom_company_name,
            c_vehicle_description: item?.custom_vehicle_description,
            c_vehicle_price: item?.custom_vehicle_price,
            c_vat: +item?.vat,
            c_custom_duty: +item?.custom_duty,
            c_currency: item?.currency,
            c_claim_amount: item?.claim_amount,
            c_container_number: item?.custom_container_number,
          })) || [],
      );
      // START: Exit Paper Credits Values
      if (
        typeof selectedItems[0]?.customerPaymentTransactionId === 'number' &&
        selectedItems[0]?.customerPaymentTransactionId > 0
      ) {
        setValue(
          'customerPaymentTransactionId',
          selectedItems[0]?.customerPaymentTransactionId,
        );
        setValue(
          'amount',
          Number(selectedItems[0]?.customerPaymentTransaction?.amount),
        );
        setValue(
          'amount_applied',
          Number(selectedItems[0]?.customerPaymentTransaction?.amount_applied),
        );
        setValue(
          'customer_credits',
          selectedItems[0]?.customerPaymentTransaction?.customer_credits.map(
            (credit) => ({ ...credit, amount: +credit?.amount }),
          ),
        );
      }
      // END: Exit Paper Credits Values
    }
  }, [selectedItems, isUpdate]);

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update ExitPaper' : 'Create ExitPaper'}
        isUpdate={isUpdate}
      />
    </form>
  );
};
