import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Chip, Container, IconButton, Typography } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { YARD_LOCATIONS } from '@/configs/leftSideMenu/Permissions';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog2';
import { applySavedColumns } from '@/utils/columnUtils';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { choseColor, recordManager } from '@/configs/configs';
import {
  yardLocationsHeaders,
  loadingCompanyRateHeaders,
  HeaderInfoLoadingCompanyRates,
  loadingCompanyPendingRateHeaders,
  filterContentSpecificStatusLoadingCompanyRates,
} from '@/configs/yard_locations/yarLocationHeader';
import { formatDate } from '@/configs/vehicles/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { removeUnderScore } from '@/configs/common';
import { useRouter } from 'next/router';
import { CreateLoadRate } from './CreateLoadRate';
import ChangeRateStatus from './ChangeStatus';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

////////////////////////////////////////////////////////////////////

const YardLocationLoadRate = ({ apiUrl }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const router = useRouter();
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [changeStatus, setChangeStatus] = useState(false);
  // const [openConfirm, setOpenConfirm] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const pageName = 'loading_company';
  const [loading, setLoading] = useState(false);

  const [columnSizing, _setColumnSizing] = useState({});
  ////////////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(`${apiUrl}/getRates/`, {
        signal: controller.signal,
        params: {
          status: router.query.id,
          page: options.page,
          perPage: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          perPage: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: yardLocationsHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile, columnSizing]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    router,
  ]);

  return perms && !perms?.includes(YARD_LOCATIONS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Loading Company Rates</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfoLoadingCompanyRates()} />

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={'Yard Locations'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} company ? `}
              dialogTitle={`Delete company Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={
                perms?.includes(YARD_LOCATIONS?.CREATE) &&
                router.query.id == 'pending'
              }
              showEditButton={
                (perms?.includes(YARD_LOCATIONS?.UPDATE) &&
                  router.query.id == 'pending') ||
                (perms?.includes(YARD_LOCATIONS?.EDIT_ACTIVE_RATE) &&
                  router.query.id == 'active')
              }
              showDeleteButton={
                perms?.includes(YARD_LOCATIONS?.DELETE) &&
                router.query.id == 'pending'
              }
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              customActionButtons={() => (
                <>
                  {selectedItems.every(
                    (item) =>
                      (item.status == 'pending' &&
                        perms?.includes(YARD_LOCATIONS?.UPDATE)) ||
                      (item.status == 'active' &&
                        perms?.includes(YARD_LOCATIONS?.MOVE_TO_ARCHIVE)),
                  ) && (
                    <AppTooltip title={'Change Status'}>
                      <IconButton onClick={() => setChangeStatus(true)}>
                        <PublishedWithChangesIcon />
                      </IconButton>
                    </AppTooltip>
                  )}
                </>
              )}
            />
          }
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={
            router.query.id == 'pending'
              ? loadingCompanyPendingRateHeaders
              : loadingCompanyRateHeaders
          }
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="yardLocations"
          id={(item) => item?.id}
          name={(item) => item?.yard_location?.name}
          created_by={({ users_yardLocations_created_byTousers }) => (
            <>
              {users_yardLocations_created_byTousers?.fullname}
              {users_yardLocations_created_byTousers?.departments?.name &&
                ' | ' +
                  users_yardLocations_created_byTousers?.departments?.name}
            </>
          )}
          updated_by={({ users_yardLocations_updated_byTousers }) => (
            <>
              {users_yardLocations_updated_byTousers?.fullname}
              {users_yardLocations_updated_byTousers?.departments?.name &&
                ' | ' +
                  users_yardLocations_updated_byTousers?.departments?.name}
            </>
          )}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          location_name={({ locations }) => locations?.name}
          status={({ loginable }) =>
            loginable?.status && (
              <Chip
                size="small"
                label={removeUnderScore(loginable?.status)}
                sx={{
                  backgroundColor: choseColor(loginable?.status),
                  color: 'white',
                  textTransform: 'capitalize',
                }}
              />
            )
          }
          username={({ loginable }) => loginable?.username}
          emails={({ emails }) =>
            emails?.map((row, index) => (
              <Typography
                key={index}
                sx={{ fontSize: '12px', textAlign: 'left' }}
              >
                {index + 1}) {row}
              </Typography>
            ))
          }
          rate_40hc_3v={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_40hc_3v ?? item.rate_40hc_3v;
            return rate ? formatter.format(rate) : '';
          }}
          rate_40hc_4v={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_40hc_4v ?? item.rate_40hc_4v;
            return rate ? formatter.format(rate) : '';
          }}
          rate_40hc_halfcut={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_40hc_halfcut ??
              item.rate_40hc_halfcut;
            return rate ? formatter.format(rate) : '';
          }}
          rate_45hc_3v={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_45hc_3v ?? item.rate_45hc_3v;
            return rate ? formatter.format(rate) : '';
          }}
          rate_45hc_4v={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_45hc_4v ?? item.rate_45hc_4v;
            return rate ? formatter.format(rate) : '';
          }}
          rate_45hc_halfcut={(item) => {
            const rate =
              item.loading_company_rate?.[0]?.rate_45hc_halfcut ??
              item.rate_45hc_halfcut;
            return rate ? formatter.format(rate) : '';
          }}
          effective_date_from={(item) => {
            const date = item?.effective_date_from;
            return date ? formatDate(date) : '';
          }}
          effective_date_to={(item) => {
            const date = item?.effective_date_to;
            return date ? formatDate(date) : '';
          }}
          rate_status={(item) => {
            const status = item?.status;
            return status ? (
              <Chip
                size="small"
                label={removeUnderScore(status)}
                sx={{
                  backgroundColor: choseColor(status),
                  color: 'white',
                  textTransform: 'capitalize',
                }}
              />
            ) : (
              ''
            );
          }}
        />
      </Container>

      <CreateLoadRate
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        status={router.query.id}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      <ChangeRateStatus
        setOpenConfirm={setChangeStatus}
        openConfirm={changeStatus}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setReload={() => fetchRecords()}
      />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Yard Locations Rates"
        content={filterContentSpecificStatusLoadingCompanyRates}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={yardLocationsHeaders}
      ></ColumnDialog>

      <PdfModal
        options={options}
        open={showDownload}
        pdf_title={'Yard Locations'}
        selectedHeaders={
          router.query.id == 'pending'
            ? loadingCompanyPendingRateHeaders
            : loadingCompanyRateHeaders
        }
        setShowDownload={setShowDownload}
        fetchDownloadRecords={fetchDownloadRecords}
        tableRecords={tableRecords}
        headers={
          router.query.id == 'pending'
            ? loadingCompanyPendingRateHeaders
            : loadingCompanyRateHeaders
        }
        //start custom props
        location_name={({ locations }) => locations?.name}
        created_by={({ users_yardLocations_created_byTousers }) =>
          users_yardLocations_created_byTousers?.fullname
        }
        updated_by={({ users_yardLocations_updated_byTousers }) =>
          users_yardLocations_updated_byTousers?.fullname
        }
        created_at={({ created_at }) => formatDate(created_at)}
        updated_at={({ updated_at }) => formatDate(updated_at)}
        name={(item) => item?.yard_location?.name}
        rate_status={(item) => removeUnderScore(item?.status)}
        effective_date_from={(item) => formatDate(item?.effective_date_from)}
        effective_date_to={(item) => formatDate(item?.effective_date_to)}
      ></PdfModal>
    </>
  );
};

export default YardLocationLoadRate;
