import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { Box, FormControl, FormHelperText } from '@mui/material';
import { useState } from 'react';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

//const rateStatus = ['pending', 'approved', 'rejected', 'archived'];

const ChangeRateStatus = ({
  setOpenConfirm,
  openConfirm,
  selectedItems,
  setSelectedItems,
  setReload,
}) => {
  const [submitting, setSubmitting] = useState(false);
  const [effectiveDateFrom, setEffectiveDateFrom] = useState(null);
  const [effectiveDateTo, setEffectiveDateTo] = useState(null);
  const [dateError, setDateError] = useState('');
  const [toDateError, setToDateError] = useState('');

  console.log('selectedItems for change status', selectedItems);

  const validateDates = () => {
    if (!effectiveDateFrom) {
      setDateError('Effective date from is required');
      return false;
    }
    if (effectiveDateTo) {
      if (effectiveDateFrom > effectiveDateTo) {
        setToDateError('Effective date from cannot be after effective date to');
        return false;
      }
    }

    setDateError('');
    setToDateError('');
    return true;
  };

  const formatDate = (date) => {
    if (!date) return null;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const changesStatus = async () => {
    if (selectedItems.every((item) => item.status == 'pending')) {
      if (!validateDates()) {
        toast.error('Oops! Validation Error');
        return;
      }
    }

    if (selectedItems.length > 0) {
      const params = selectedItems.map((item) => ({
        rate_id: item?.id,
        yard_location_id: item?.yard_location_id,
      }));
      try {
        setSubmitting(true);
        const status = selectedItems.every((item) => item.status == 'pending')
          ? 'active'
          : 'archived';
        let { data } = await axios.patch(`loading-company-rates/changeStatus`, {
          params,
          status: status,
          effective_date_from: formatDate(effectiveDateFrom),
          effective_date_to: formatDate(effectiveDateTo),
        });

        if (data.result) {
          setSelectedItems([]);
          setReload(Math.random());
          setOpenConfirm(false);
          setEffectiveDateFrom(null);
          setEffectiveDateTo(null);
          toast.success('Status Changed Successfully!');
        }
      } catch (error) {
        toast.error('Oops! Somethings went wrong.');
      }
      setSubmitting(false);
    }
  };

  const confirmDialogStatus = () => {
    return (
      <Box>
        <Box sx={{ fontWeight: 'bold', fontSize: '18px', mb: 2 }}>
          {selectedItems.every((item) => item.status == 'pending')
            ? 'Do You Want to Activate The Load Rate?'
            : 'Do You Want to Move The Load Rate to Archive?'}
        </Box>
        <Box sx={{ mt: 1, mb: 3 }}>
          <span style={{ color: '#e74c3c', fontWeight: 'bold' }}> NOTE: </span>
          <span>
            {selectedItems.every((item) => item.status == 'pending')
              ? 'By approving this, the load rate of this yard location will be activated and any other active load rate of the yard location will be archived.'
              : 'By moving this load rate to archive, it will no longer be available for activation.'}
          </span>
        </Box>
        {selectedItems.every((item) => item.status == 'pending') && (
          <FormControl fullWidth error={!!dateError}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Box sx={{ mb: 2 }}>
                <DatePicker
                  label="Effective Date From"
                  value={effectiveDateFrom}
                  onChange={(newValue) => {
                    setEffectiveDateFrom(newValue);
                    setDateError('');
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                      error: !!dateError,
                    },
                  }}
                />
              </Box>
              <Box sx={{ mb: 2 }}>
                <DatePicker
                  label="Effective Date To"
                  value={effectiveDateTo}
                  onChange={(newValue) => {
                    setEffectiveDateTo(newValue);
                    setToDateError('');
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                      error: !!toDateError,
                    },
                  }}
                />
              </Box>
            </LocalizationProvider>
            {toDateError && <FormHelperText>{toDateError}</FormHelperText>}
          </FormControl>
        )}
      </Box>
    );
  };

  return (
    <AppConfirmDialog
      open={openConfirm}
      onDeny={() => {
        setOpenConfirm(false);
        setEffectiveDateFrom(null);
        setEffectiveDateTo(null);
        setDateError('');
        setToDateError('');
      }}
      onConfirm={changesStatus}
      title={confirmDialogStatus()}
      dialogTitle={'Change Status'}
      confirmText={'Change'}
      cancelText="Cancel"
      maxWidth={'sm'}
      submitting={submitting}
    />
  );
};

export default ChangeRateStatus;
