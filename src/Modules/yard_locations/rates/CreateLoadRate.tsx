import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loadRateSchema } from '@/configs/yard_locations/yarLocationHeader';
import InfoIcon from '@mui/icons-material/Info';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import LoadRateCreationForm from '../steps/LoadRateCreationForm';

export const CreateLoadRate = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
  status,
}) => {
  const [isDone, setIsDone] = useState(false);
  // const exists = async (column, data, modal) => {
  //   let res = await yardLocationsHelper.exists([column, data], modal);
  //   return res;
  // };

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    setValue,
    control,
    watch,
    getValues,
    setError,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(loadRateSchema),

    defaultValues: {
      yard_location_id: null,
      rate_40hc_4v: null,
      rate_40hc_3v: null,
      rate_40hc_halfcut: null,
      rate_45hc_4v: null,
      rate_45hc_3v: null,
      rate_45hc_halfcut: null,
      effective_date_from: null,
      effective_date_to: null,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    getValues,
    setError,
    setValue,
  };

  const [loadingButton, setLoadingButton] = useState(false);

  const submit = async (values) => {
    try {
      if (!isUpdate) {
        setLoadingButton(true);
        const { data } = await axios.post(
          '/loading-company-rates/createRate',
          values,
        );
        if (data.result == true) {
          recordManager(data.data, 'add');
          setIsDone(true);
          toast.success('Done successfully!');
          setLoadingButton(false);
          return true;
        } else {
          setLoadingButton(false);
          toast.error('Failed to create load rate');
          return false;
        }
      } else {
        setLoadingButton(true);
        const { data } = await axios.patch(
          'loading-company-rates/' + selectedItems[0].id,
          values,
        );
        if (data.result === true) {
          recordManager(data.data, 'update');
          setIsDone(true);
          toast.success('Record updated successfully!');
          setLoadingButton(false);
          return true;
        }
        setLoadingButton(false);
        toast.error('Failed to update load rate');
        return false;
      }
    } catch (error) {
      setLoadingButton(false);
      toast.error(error.response?.data?.message || 'An error occurred');
      return false;
    }
  };

  const steps = [
    {
      label: 'Load Rate',
      icon: <InfoIcon />,
      step: (
        <LoadRateCreationForm form={form} isUpdate={isUpdate} status={status} />
      ),
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger(['yard_location_id']);
        if (!isValid) {
          toast.error('Please fill in the required field');
        }
        return isValid;
      },
    },
  ];
  useEffect(() => {
    if (selectedItems) {
      console.log('items to be edited:', selectedItems[0]);
      setValue('yard_location_id', selectedItems[0]?.yard_location_id);
      setValue('rate_40hc_4v', selectedItems[0]?.rate_40hc_4v);
      setValue('rate_40hc_3v', selectedItems[0]?.rate_40hc_3v);
      setValue('rate_40hc_halfcut', selectedItems[0]?.rate_40hc_halfcut);
      setValue('rate_45hc_4v', selectedItems[0]?.rate_45hc_4v);
      setValue('rate_45hc_3v', selectedItems[0]?.rate_45hc_3v);
      setValue('rate_45hc_halfcut', selectedItems[0]?.rate_45hc_halfcut);
      setValue('effective_date_from', selectedItems[0]?.effective_date_from);
      setValue('effective_date_to', selectedItems[0]?.effective_date_to);
    }
  }, [selectedItems]);
  return (
    <form onSubmit={handleSubmit(submit)}>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Load Rate' : 'Create Load Rate'}
        isUpdate={false}
      />
    </form>
  );
};
