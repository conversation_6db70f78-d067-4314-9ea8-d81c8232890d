import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import axios from '@/lib/axios';
import { Grid, TextField, Typography } from '@mui/material';
import { Box } from '@mui/system';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

export default function LoadRateCreationForm({ form, isUpdate, status }) {
  const [yardLocations, setYardLocations] = useState([]);

  console.log('formmmm:', form.watch());

  const formatDate = (date) => {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const parseDate = (dateString) => {
    if (!dateString) return null;
    if (dateString instanceof Date) return dateString;
    return new Date(dateString);
  };

  useEffect(() => {
    const fetchYardLocations = async () => {
      try {
        const response = await axios.get(
          'loading-company-rates/getYardLocations',
        );
        console.log('Yard locations response:', response.data);
        if (response.data.result) {
          // Transform the data to match AutoComplete expected format
          const transformedData = response.data.data.map((location) => ({
            id: location.id,
            label: location.name,
            loading_company_rate: location.loading_company_rate,
          }));
          setYardLocations(transformedData);
        } else {
          console.error('Failed to fetch yard locations');
        }
      } catch (error) {
        console.error('Error fetching yard locations:', error);
      }
    };
    fetchYardLocations();
  }, []);

  // Custom validation for effective dates
  useEffect(() => {
    const subscription = form.watch((value) => {
      // Clear previous errors first
      form.clearErrors(['effective_date_from', 'effective_date_to']);

      // Only validate if in update mode and status is active
      if (isUpdate && status === 'active') {
        // Validate effective_date_from (required)
        if (!value.effective_date_from) {
          form.setError('effective_date_from', {
            type: 'manual',
            message: 'Effective date from is required',
          });
        }

        // Validate effective_date_to (optional, but must be after from date if provided)
        if (value.effective_date_to && value.effective_date_from) {
          const fromDate = parseDate(value.effective_date_from);
          const toDate = parseDate(value.effective_date_to);

          if (fromDate && toDate && toDate <= fromDate) {
            form.setError('effective_date_to', {
              type: 'manual',
              message: 'Effective date to must be after effective date from',
            });
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, isUpdate, status]);

  // const handleChange = (value) => {
  //   console.log('Selected value:', value);
  //   console.log("yyyyyard locations: ", yardLocations);
  //   const selectedLocation = yardLocations.find(loc => loc.id === value);
  //   console.log('Selected location:', selectedLocation);
  //   // setSelectedYardLocation(selectedLocation);

  //   if (selectedLocation?.loading_company_rate?.length > 0) {
  //       form.setError('yard_location_id', {
  //         type: 'manual',
  //         message: 'This yard location already has a pending rate'
  //       });
  //       form.setValue('yard_location_id', value);
  //   }
  // };

  // Monitor form value changes
  // useEffect(() => {
  //   const subscription = form.watch((value, { name }) => {
  //     if (name === 'yard_location_id') {
  //       console.log("valuessssss:", value)
  //       handleChange(value.yard_location_id);
  //     }
  //   });
  //   return () => subscription.unsubscribe();
  // }, [form]);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 5 }}>
        {isUpdate ? 'Edit Load Rate' : 'Create Load Rate'}
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="yard_location_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url=""
                label="Select Yard Location"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={yardLocations}
                column={'name'}
                modal={'yard_locations'}
                // onChange={handleYardLocationChange}
              />
            )}
          />
        </Grid>
        {isUpdate && status === 'active' && (
          <>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="effective_date_from"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Effective Date From"
                      value={parseDate(field.value)}
                      onChange={(newValue) => {
                        field.onChange(formatDate(newValue));
                      }}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small',
                          error: !!error,
                          helperText: error?.message,
                        },
                      }}
                    />
                  </LocalizationProvider>
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="effective_date_to"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Effective Date To"
                      value={parseDate(field.value)}
                      onChange={(newValue) => {
                        field.onChange(formatDate(newValue));
                      }}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small',
                          error: !!error,
                          helperText: error?.message,
                        },
                      }}
                    />
                  </LocalizationProvider>
                )}
              />
            </Grid>
          </>
        )}

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_4v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_4v?.message.length > 0 ? true : false
                }
                id="rate_40hc_4v"
                value={field.value}
                label="40HC (4 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_4v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_4v?.message.length > 0 ? true : false
                }
                id="rate_45hc_4v"
                value={field.value}
                label="45HC (4 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_3v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_3v?.message.length > 0 ? true : false
                }
                id="rate_40hc_3v"
                value={field.value}
                label="40HC (3 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_3v"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_3v?.message.length > 0 ? true : false
                }
                id="rate_45hc_3v"
                value={field.value}
                label="45HC (3 veihicles)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_40hc_halfcut"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_40hc_halfcut?.message.length > 0
                    ? true
                    : false
                }
                id="rate_40hc_halfcut"
                value={field.value}
                label="40HC (Halfcut)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="rate_45hc_halfcut"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.rate_45hc_halfcut?.message.length > 0
                    ? true
                    : false
                }
                id="rate_45hc_halfcut"
                value={field.value}
                label="45HC (Halfcut)"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
