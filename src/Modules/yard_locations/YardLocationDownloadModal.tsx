import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import React from 'react';
import {
  loadingCompanyRateHeaders,
  yardLocationsHeaders,
} from '@/configs/yard_locations/yarLocationHeader';

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

const YardLocationsPDFModal = ({
  showDownload,
  setShowDownload,
  apiUrl,
  tableRecords,
  options,
  activeTab,
}) => {
  const fetchDownloadRecords = async () => {
    try {
      const endpoint = activeTab === 'rates' ? `${apiUrl}/getRates` : apiUrl;
      const params =
        activeTab === 'rates'
          ? {
              page: options.page,
              per_page: -1,
              search: options.search,
              exactMatch: options.exactMatch,
              column: options.orderBy.column,
              order: options.orderBy.order,
              filterData: JSON.stringify(options.filterData),
              isRateProfile: 1,
            }
          : {
              page: options.page,
              per_page: -1,
              search: options.search,
              exactMatch: options.exactMatch,
              column: options.orderBy.column,
              order: options.orderBy.order,
              filterData: JSON.stringify(options.filterData),
            };

      const { data } = await axios.get(endpoint, {
        params: params,
      });

      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  const formatRate = (rate) => {
    if (rate === null || rate === undefined) return '';
    return formatter.format(rate);
  };

  // Use the appropriate headers based on the active tab
  const headers =
    activeTab === 'rates' ? loadingCompanyRateHeaders : yardLocationsHeaders;

  return (
    <PdfModal
      options={options}
      open={showDownload}
      pdf_title={
        activeTab === 'rates' ? 'Yard Location Rates' : 'Yard Locations'
      }
      selectedHeaders={headers}
      setShowDownload={setShowDownload}
      fetchDownloadRecords={fetchDownloadRecords}
      tableRecords={tableRecords}
      headers={headers}
      // Custom props for yard locations
      created_by={({ users_yardLocations_created_byTousers }) =>
        users_yardLocations_created_byTousers?.fullname
      }
      updated_by={({ users_yardLocations_updated_byTousers }) =>
        users_yardLocations_updated_byTousers?.fullname
      }
      created_at={({ created_at }) => formatDate(created_at)}
      updated_at={({ updated_at }) => formatDate(updated_at)}
      location_name={({ locations }) => locations?.name}
      status={({ loginable }) => loginable?.status}
      username={({ loginable }) => loginable?.username}
      emails={({ emails }) => emails?.join(', ')}
      // Rate specific props with proper formatting
      rate_40hc_3v={({ rate_40hc_3v }) => formatRate(rate_40hc_3v)}
      rate_40hc_4v={({ rate_40hc_4v }) => formatRate(rate_40hc_4v)}
      rate_40hc_halfcut={({ rate_40hc_halfcut }) =>
        formatRate(rate_40hc_halfcut)
      }
      rate_45hc_3v={({ rate_45hc_3v }) => formatRate(rate_45hc_3v)}
      rate_45hc_4v={({ rate_45hc_4v }) => formatRate(rate_45hc_4v)}
      rate_45hc_halfcut={({ rate_45hc_halfcut }) =>
        formatRate(rate_45hc_halfcut)
      }
      effective_date_from={({ effective_date_from }) =>
        formatDate(effective_date_from)
      }
      effective_date_to={({ effective_date_to }) =>
        formatDate(effective_date_to)
      }
      rate_status={({ rate_status }) => rate_status}
      // Name field handling for both tabs
      name={(item) => (activeTab === 'rates' ? item.name : item.name)}
    />
  );
};

export default YardLocationsPDFModal;
