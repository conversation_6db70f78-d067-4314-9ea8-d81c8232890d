import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  createSchema,
  updateSchema,
} from '@/configs/yard_locations/yarLocationHeader';
import InfoIcon from '@mui/icons-material/Info';
import yardLocationsHelper from './YardLocationHeper';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import YardLocationStep1 from './steps/YardLocationStep1';

export const CreateYardLocations = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
}) => {
  const [isDone, setIsDone] = useState(false);
  const exists = async (column, data, modal) => {
    let res = await yardLocationsHelper.exists([column, data], modal);
    return res;
  };

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    setValue,
    control,
    watch,
    getValues,
    setError,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      isUpdate && selectedItems[0]?.loginable ? updateSchema : createSchema,
    ),

    defaultValues: {
      name: null,
      location_id: null,
      username: null,
      password: null,
      emails: [],
      status: null,
      bio: null,
      address_info: null,
      rate_40hc_4v: null,
      rate_40hc_3v: null,
      rate_40hc_halfcut: null,
      rate_45hc_4v: null,
      rate_45hc_3v: null,
      rate_45hc_halfcut: null,
      effective_date_from: null,
      effective_date_to: null,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    getValues,
    setError,
  };

  const [loadingButton, setLoadingButton] = useState(false);

  const submit = async (values) => {
    try {
      if (!isUpdate) {
        setLoadingButton(true);
        const { data } = await axios.post('/loading-company-rates', values);
        if (data.result == true) {
          recordManager(data.data, 'add');
          setIsDone(true);
          toast.success('Done successfully!');
          setLoadingButton(false);

          return true;
        } else {
          setLoadingButton(false);

          return false;
        }
      } else {
        setLoadingButton(true);

        const { data } = await axios.patch(
          'yardslocations/' + selectedItems[0].id,
          values,
        );
        if (data.result === true) {
          recordManager(data.data, 'update');
          setIsDone(true);
          toast.success('Record updated successfully!');
          setLoadingButton(false);

          return true;
        }
        setLoadingButton(false);

        return false;
      }
    } catch (error) {
      setLoadingButton(false);
      return false;
    }
  };

  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: <YardLocationStep1 form={form} />,
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger(['name', 'location_id']);
        let name = false;
        if (!isUpdate) {
          name = await exists('name', getValues('name'), 'yard_locations');
          if (name) {
            setError('name', {
              type: 'custom',
              message: 'This name is exist try another one!',
            });
          }
        }
        return isValid && !name;
      },
    },
  ];
  useEffect(() => {
    if (selectedItems) {
      setValue('name', selectedItems[0]?.name);
      setValue('location_id', selectedItems[0]?.location_id);
      setValue('emails', selectedItems[0]?.emails ?? []);
      setValue('status', selectedItems[0]?.loginable?.status);
      setValue('username', selectedItems[0]?.loginable?.username);
      setValue('bio', selectedItems[0]?.bio);
      setValue('address_info', selectedItems[0]?.address_info);
      setValue('rate_40hc_4v', selectedItems[0]?.rate_40hc_4v);
      setValue('rate_40hc_3v', selectedItems[0]?.rate_40hc_3v);
      setValue('rate_40hc_halfcut', selectedItems[0]?.rate_40hc_halfcut);
      setValue('rate_45hc_4v', selectedItems[0]?.rate_45hc_4v);
      setValue('rate_45hc_3v', selectedItems[0]?.rate_45hc_3v);
      setValue('rate_45hc_halfcut', selectedItems[0]?.rate_45hc_halfcut);
      setValue(
        'effective_date_from',
        selectedItems[0]?.loading_company_rate?.[0]?.effective_date_from,
      );
      setValue(
        'effective_date_to',
        selectedItems[0]?.loading_company_rate?.[0]?.effective_date_to,
      );
    }
  }, [selectedItems]);
  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Yard Location' : 'Create Yard Location'}
        isUpdate={false}
      />
    </form>
  );
};
