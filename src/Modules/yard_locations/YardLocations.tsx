import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Chip, Container, Tab, Tabs, Typography } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { YARD_LOCATIONS } from '@/configs/leftSideMenu/Permissions';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog2';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { choseColor, copyORViewFun, recordManager } from '@/configs/configs';
import {
  filterContentYardLocations,
  filterContentLoadingCompanyRates,
  HeaderInfo,
  yardLocationsHeaders,
  loadingCompanyRateHeaders,
} from '@/configs/yard_locations/yarLocationHeader';
import ViewSingleYardLocations from './ViewSingleYardLocations';
import { CreateYardLocations } from './CreateYardLocations';
import { formatDate } from '@/configs/vehicles/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { removeUnderScore } from '@/configs/common';
import YardLocationsPDFModal from './YardLocationDownloadModal';

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

const NewYardLocations = ({ apiUrl }) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  // State management
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [activeTab, setActiveTab] = useState('profiles');
  const [loading, setLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);

  // Table options and pagination
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });

  const [columnSizing, _setColumnSizing] = useState({});
  const pageName = 'Yard Locations';

  // Data transformation functions
  const reformYardLocationData = (data) => {
    if (activeTab !== 'rates') return data;

    return data
      .filter((item) => item.status !== 'archived') // Filter out archived records
      .map((item) => ({
        id: item.yard_location.id,
        name: item.yard_location.name,
        rate_40hc_3v: item.rate_40hc_3v,
        rate_40hc_4v: item.rate_40hc_4v,
        rate_40hc_halfcut: item.rate_40hc_halfcut,
        rate_45hc_3v: item.rate_45hc_3v,
        rate_45hc_4v: item.rate_45hc_4v,
        rate_45hc_halfcut: item.rate_45hc_halfcut,
        effective_date_from: item.effective_date_from,
        effective_date_to: item.effective_date_to,
        rate_status: item.status,
        rate_id: item.id,
        created_at: item.created_at,
        created_by: item.created_by,
        updated_at: item.updated_at,
        updated_by: item.updated_by,
      }));
  };

  // API calls
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);

      const endpoint = activeTab === 'rates' ? `${apiUrl}/getRates` : apiUrl;
      const params =
        activeTab === 'rates'
          ? {
              page: options.page,
              per_page: options.perPage,
              search: options.search,
              exactMatch: options.exactMatch,
              column: options.orderBy.column,
              order: options.orderBy.order,
              filterData: JSON.stringify(options.filterData),
              isRateProfile: 1,
            }
          : {
              page: options.page,
              per_page: options.perPage,
              search: options.search,
              exactMatch: options.exactMatch,
              column: options.orderBy.column,
              order: options.orderBy.order,
              filterData: JSON.stringify(options.filterData),
            };

      const { data } = await axios.get(endpoint, {
        signal: controller.signal,
        params: params,
      });

      setTotalItems(data.total);
      const processedData =
        activeTab === 'rates' ? reformYardLocationData(data.data) : data.data;
      setTableRecords(processedData);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Render helper functions
  const renderRatesTable = (rate) => (
    <table style={{ width: '100%' }}>
      <tbody>
        <tr>
          <td style={{ paddingLeft: 3 }}>
            {rate ? formatter.format(rate) : '\u00A0\u00A0\u00A0\u00A0\u00A0'}
          </td>
        </tr>
      </tbody>
    </table>
  );

  const renderDatesTable = (date) => (
    <table style={{ width: '100%' }}>
      <tbody>
        <tr>
          <td style={{ paddingLeft: 3 }}>
            {date ? formatDate(date) : '\u00A0\u00A0\u00A0\u00A0\u00A0'}
          </td>
        </tr>
      </tbody>
    </table>
  );

  const renderStatusTable = (status) => {
    if (!status) return <></>;

    return (
      <table style={{ width: '100%' }}>
        <tbody>
          <tr>
            <td style={{ paddingLeft: 3 }}>
              <Chip
                size="small"
                label={removeUnderScore(status)}
                sx={{
                  backgroundColor: choseColor(status),
                  color: 'white',
                  textTransform: 'capitalize',
                }}
              />
            </td>
          </tr>
        </tbody>
      </table>
    );
  };

  // Effects
  useEffect(() => {
    if (profile) {
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: yardLocationsHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
    }
  }, [profile, columnSizing]);

  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.orderBy,
    options.filterData,
    activeTab,
  ]);

  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
    // Clear filter data when switching tabs
    setOptions((prev) => ({
      ...prev,
      filterData: {},
      search: '',
      exactMatch: false,
      orderBy: {
        column: 'id',
        order: 'desc',
      },
    }));
  };

  if (perms && !perms?.includes(YARD_LOCATIONS?.VIEW)) {
    return (
      <Box
        display={'flex'}
        justifyContent={'center'}
        alignItems={'center'}
        height="100vh"
        fontWeight="bold"
        fontSize="20px"
      >
        Unauthorized
      </Box>
    );
  }

  return (
    <>
      <Head>
        <title>Yard Locations List</title>
      </Head>

      <Container
        sx={{
          maxWidth: 'unset !important',
          padding: '0 10px !important',
        }}
      >
        <PageHeader breadcrumbs={HeaderInfo()} />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab
            sx={{ fontSize: 10 }}
            key="profiles"
            label="Profiles"
            value="profiles"
          />
          <Tab sx={{ fontSize: 10 }} key="rates" label="Rates" value="rates" />
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              selectedItems={selectedItems}
              title={activeTab === 'rates' ? 'Loading Rates' : 'Yard Locations'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} company ? `}
              dialogTitle={`Delete company Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(YARD_LOCATIONS?.CREATE)}
              showEditButton={
                perms?.includes(YARD_LOCATIONS?.UPDATE) &&
                activeTab === 'profiles'
              }
              showDeleteButton={
                perms?.includes(YARD_LOCATIONS?.DELETE) &&
                activeTab === 'profiles'
              }
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
          }
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={
            activeTab === 'rates' ? loadingCompanyRateHeaders : selectedHeaders
          }
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="yardLocations"
          columnsForMergingRows={activeTab === 'rates' ? ['id', 'name'] : []}
          id={(item) =>
            activeTab === 'rates'
              ? item?.id
              : copyORViewFun({
                  getSingleRow,
                  copy: item?.id,
                  display: item?.id,
                  id: item,
                })
          }
          name={(item) =>
            activeTab === 'rates'
              ? item?.name
              : copyORViewFun({
                  getSingleRow,
                  copy: item?.name,
                  display: item?.name,
                  id: item,
                })
          }
          created_by={({ users_yardLocations_created_byTousers }) => (
            <>
              {users_yardLocations_created_byTousers?.fullname}
              {users_yardLocations_created_byTousers?.departments?.name &&
                ' | ' +
                  users_yardLocations_created_byTousers?.departments?.name}
            </>
          )}
          updated_by={({ users_yardLocations_updated_byTousers }) => (
            <>
              {users_yardLocations_updated_byTousers?.fullname}
              {users_yardLocations_updated_byTousers?.departments?.name &&
                ' | ' +
                  users_yardLocations_updated_byTousers?.departments?.name}
            </>
          )}
          created_at={({ created_at }) => formatDate(created_at)}
          updated_at={({ updated_at }) => formatDate(updated_at)}
          location_name={({ locations }) => locations?.name}
          status={({ loginable }) =>
            loginable?.status && (
              <Chip
                size="small"
                label={removeUnderScore(loginable?.status)}
                sx={{
                  backgroundColor: choseColor(loginable?.status),
                  color: 'white',
                  textTransform: 'capitalize',
                }}
              />
            )
          }
          username={({ loginable }) => loginable?.username}
          emails={({ emails }) =>
            emails?.map((row, index) => (
              <Typography
                key={index}
                sx={{ fontSize: '12px', textAlign: 'left' }}
              >
                {index + 1}) {row}
              </Typography>
            ))
          }
          rate_40hc_3v={(item) => renderRatesTable(item.rate_40hc_3v)}
          rate_40hc_4v={(item) => renderRatesTable(item.rate_40hc_4v)}
          rate_40hc_halfcut={(item) => renderRatesTable(item.rate_40hc_halfcut)}
          rate_45hc_3v={(item) => renderRatesTable(item.rate_45hc_3v)}
          rate_45hc_4v={(item) => renderRatesTable(item.rate_45hc_4v)}
          rate_45hc_halfcut={(item) => renderRatesTable(item.rate_45hc_halfcut)}
          effective_date_from={(item) =>
            renderDatesTable(item.effective_date_from)
          }
          effective_date_to={(item) => renderDatesTable(item.effective_date_to)}
          rate_status={(item) => renderStatusTable(item.rate_status)}
        />
      </Container>

      <CreateYardLocations
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />
      <ViewSingleYardLocations data={viewData} setView={setView} show={view} />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title={
          activeTab === 'rates'
            ? 'Filter Loading Company Rates'
            : 'Filter Yard Locations'
        }
        content={
          activeTab === 'rates'
            ? filterContentLoadingCompanyRates
            : filterContentYardLocations
        }
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={yardLocationsHeaders}
      />
      <YardLocationsPDFModal
        showDownload={showDownload}
        setShowDownload={setShowDownload}
        apiUrl={apiUrl}
        tableRecords={tableRecords}
        options={options}
        activeTab={activeTab}
      />
    </>
  );
};

export default NewYardLocations;
