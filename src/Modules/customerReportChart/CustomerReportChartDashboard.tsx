import React, { useContext, useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import ModeStandbyIcon from '@mui/icons-material/ModeStandby';
import axios from '@/lib/axios';
import { CustomerReportChartFilter } from './CustomerReportChartFilter';
import dynamic from 'next/dynamic';
import { ApexOptions } from 'apexcharts';
import { ThemeContext } from '@/theme/ThemeProvider';
const ReactApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
  loading: () => (
    <div className="text-center text-gray-500">Loading chart...</div>
  ),
});

function CustomerReportChartDashboard({ apiUrl }) {
  const [cancelToken, setCancelToken] = useState(null);

  const [data, setData] = useState(null);
  const [options, setOptions] = useState({
    selected: 'daily',
    from: '',
    to: '',
  });
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          selected: options.selected,
          from: options.from,
          to: options.to,
        },
      });
      setData(data);
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    fetchRecords();
  }, [options.selected, options.from, options.to]);

  return (
    <>
      <Head>
        <title>Customer Valume Dashboard</title>
      </Head>
      <div className="flex min-h-screen flex-col px-2 py-1">
        <header>
          <PageHeader
            breadcrumbs={[
              {
                href: '/customer_charts',
                name: 'Dashboard',
                icon: <ModeStandbyIcon sx={{ fontSize: '18px' }} />,
                key: '1',
              },
            ]}
          />
          <div className="flex justify-end p-3">
            <CustomerReportChartFilter
              setOption={setOptions}
              selected={options.selected}
            />
          </div>
        </header>
        <main className="flex min-h-[calc(100vh-80vh)] flex-grow items-center">
          <div className="w-full py-5">
            <ApexChart data={data?.data || []} />
          </div>
        </main>
      </div>
    </>
  );
}
const ApexChart = ({ data }) => {
  const { mode } = useContext(ThemeContext);
  const keys = [];
  const chartData = data?.map((item) => {
    if (item.company) keys.push(item.company);
    return item?.counts?.reduce((prev, cur) => prev + cur.count, 0);
  });

  const chartConfig: {
    series: { name: string; data: number[] }[];
    options: ApexOptions;
  } = {
    series: [{ name: 'Volume', data: chartData }],
    options: {
      chart: {
        type: 'bar' as const,
        height: '100%',
        width: '100%',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        },
      },
      theme: {
        mode: mode === 'dark' ? 'dark' : 'light',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 5,
          borderRadiusApplication: 'end' as const,
        },
      },
      dataLabels: {
        enabled: true,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['--background'],
      },
      xaxis: {
        categories: keys,
        labels: {
          style: {
            fontSize: '12px',
          },
        },
      },
      yaxis: {
        title: {
          text: 'Volume',
          style: {
            fontSize: '14px',
          },
        },
        labels: {
          style: {
            fontSize: '12px',
          },
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        enabled: true,
        style: {
          fontSize: '12px',
        },
      },
    },
  };

  const { series, options } = useMemo(() => chartConfig, [keys, chartData]);
  return (
    <div className="h-[550px] w-full rounded-lg p-4 shadow-md sm:h-[700px] md:h-[800px]">
      <ReactApexChart
        options={options}
        series={series}
        type="bar"
        height="100%"
      />
    </div>
  );
};
export default CustomerReportChartDashboard;
