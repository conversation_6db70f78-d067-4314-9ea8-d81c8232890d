import { Button } from '@/components/ui/button';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from './DatePicker';
import React, { SetStateAction, useState } from 'react';
import { format } from 'date-fns';
const options = [
  {
    value: 'daily',
    title: 'Daily',
  },
  {
    value: 'weekly',
    title: 'Weekly',
  },
  {
    value: 'monthly',
    title: 'monthly',
  },
  {
    value: 'custom',
    title: 'Custom',
  },
];
type CustomerReportChartFilterProps = {
  setOption: React.Dispatch<
    SetStateAction<{ selected: string; from?: string; to?: string }>
  >;
  selected: string;
};
export function CustomerReportChartFilter({
  setOption,
  selected,
}: CustomerReportChartFilterProps) {
  const [custom, setCustom] = useState(false);
  const [from, setFrom] = React.useState<Date>();
  const [to, setTo] = React.useState<Date>();
  const handleChange = (value) => {
    if (value === 'custom') {
      setCustom(true);
    } else {
      setCustom(false);
      setOption((prev) => {
        return { ...prev, selected: value };
      });
    }
  };
  const handleSubmit = () => {
    setOption({
      from: format(from, 'yyyy-MM-dd'),
      to: format(to, 'yyyy-MM-dd'),
      selected: 'custom',
    });
  };
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="capitalize">
          {selected}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px]">
        <Select onValueChange={handleChange} defaultValue={selected}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a The time" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Options</SelectLabel>
              {options.map((item) => (
                <SelectItem value={item.value} key={item.value}>
                  {item.title}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        {custom && (
          <>
            <div className="my-2 flex flex-col gap-2">
              <div>
                <p>From</p>
                <DatePicker date={from} setDate={setFrom} />
              </div>
              <div>
                <p>To</p>
                <DatePicker date={to} setDate={setTo} />
              </div>
            </div>
            <div className="flex justify-end">
              <Button size="sm" onClick={handleSubmit}>
                submit
              </Button>
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
}
