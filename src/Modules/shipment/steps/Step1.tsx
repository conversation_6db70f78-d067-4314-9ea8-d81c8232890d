import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import CustomAutoComplete from '@/components/mainComponents/cComponents/CustomAutoComplete';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  Typography,
  Box,
  Autocomplete,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';

import React, { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import {
  inGete,
  container_title_status,
  aes_title_status,
} from '@/configs/shipment/shipmentHeader';
import axios from '@/lib/axios';
import { formFormatDate } from '@/configs/configs';
import AllowToEditDialog from '@/configs/shipment/AllowRoEditDialog';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';

const s_array = (data, fieldName) =>
  data &&
  Object?.values(data).map((value: any) => ({
    id: value?.id,
    label: value?.[fieldName] ? value?.[fieldName] : '',
  }));

export default function Step1({
  form,
  isUpdate,
  bookingURL = 'autoComplete',
  //@ts-ignore
  yardLocation = false,
  selectedItem,
  onHandWithTitles = false,
  listOfCompanies = [],
  isInstractionLoading,
  setIsInstractionLoading,
}) {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(customParseFormat);
  // Create a new date object
  const dateObj = new Date();
  !isUpdate
    ? form.setValue(
        'loading_date',
        dateObj.toLocaleString('en-US', { timeZone: 'America/New_York' }),
      )
    : '';
  const [yards, setYards] = useState<any>([]);
  const [drivers, setDrivers] = useState<any>([]);
  const [updatedBookings, setUpdatedBookings] = useState<any>([]);
  const [bookingId, setBookingId] = useState();
  const [renderKey, setRenderKey] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [updateConfirm, setUpdateConfirm] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [unknownLoader, setUnknownLoader] = useState<number[]>(
    isUpdate ? selectedItem?.loaders?.map((o) => o?.id) : [],
  );
  const [bookingParties, setBookingParties] = useState({
    company_booking_parties: [],
    booking_party: '',
  });
  const [openBookingPartiesConfirm, setOpenBookingPartiesConfirm] =
    useState(false);

  useEffect(() => {
    setUnknownLoader(isUpdate ? selectedItem?.loaders?.map((o) => o.id) : []);
  }, [selectedItem]);

  const CheckLocDes = (res) => {
    const resData = res?.data?.data[0];
    const formData = form?.selectedItems[0];
    if (
      formData.bookings?.destinations?.id != resData?.port_of_discharge ||
      formData.vessels?.locations?.id != resData?.vessels.localtions?.id
    ) {
      if (form?.confirmData?.id != resData?.id) {
        form?.setAllowTochange(true);
        form?.setOpen(true);
        form?.setConfirmData({
          ...resData,
          formData: {
            destinations: formData?.bookings?.destinations,
            locations: formData?.bookings?.vessels?.locations,
          },
        });
      }
    } else {
      form?.setAllowTochange(false);
    }
  };

  const getBookings = async () => {
    try {
      const res = await axios.get(`containers/yardLocation/${bookingId}`);
      if (res.status === 200) {
        setBookingParties((prev) => ({
          ...prev,
          booking_party: res?.data?.data[0]?.party,
        }));
        let no_units_load = '';
        let complete = 0;
        let half = 0;

        isUpdate
          ? selectedItem?.vehicles.map((vehicle) => {
              if (vehicle?.halfcut_status == 'completed') complete += 1;
              if (vehicle?.halfcut_status == 'half_cut') half += 1;
            })
          : '0';

        //--- This for create load and add vehicles to it.
        if (!isUpdate && selectedItem?.length > 0) {
          selectedItem?.map((vehicle) => {
            if (vehicle?.halfcut_status == 'completed') complete += 1;
            if (vehicle?.halfcut_status == 'half_cut') half += 1;
          });
        }

        /*  no_units_load = `${complete ? `${complete} USED AUTO | ` : ''} ${
          half ? 'ONE LOTS OF USED AUTO PARTS | ' : ''
        }`; */

        no_units_load = half
          ? 'ONE LOTS OF USED AUTO PARTS | '
          : `${complete} USED AUTO | `;

        // const veh_count = isUpdate ? selectedItem?.vehicles?.length : '0';

        form?.setValue(
          'no_units_load',
          res.data?.data[0].size.trim()?.includes('40')
            ? no_units_load + '40 HC'
            : res.data?.data[0].size.trim()?.includes('45')
              ? no_units_load + '45 HC'
              : '',
        );

        form?.setValue('booking_suffix', res?.data?.data[0]?.booking_suffix);
        form.setValue(
          'loading_cost',
          res.data?.data[0]?.vessels?.locations?.container_loading_cost,
        );
        setYards(
          s_array(
            res.data?.data[0]?.vessels?.locations?.yards_location,
            'name',
          ),
        );
        if (
          selectedItem?.booking_id == bookingId &&
          selectedItem?.duplicate !== true
        ) {
          form?.setValue('booking_suffix', selectedItem?.booking_suffix);
        } else {
          form?.setValue('booking_suffix', res?.data?.data[0]?.booking_suffix);
        }
        isUpdate && CheckLocDes(res);
        setRenderKey((prv) => prv + 1);
      }
    } catch (error) {}
  };

  const getDrivers = async () => {
    try {
      const res = await axios.get(`containers/drivers/${bookingId}`);
      if (res.status === 200) {
        setDrivers(s_array(res.data?.data, 'name'));
        setRenderKey((prv) => prv + 1);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (isUpdate) getVesselBooking(searchTerm);
  }, [searchTerm]);

  const getVesselBooking = async (search = '') => {
    if (selectedItem)
      try {
        const { data } = await axios.get(`bookings/getPortLoadingBooking`, {
          params: {
            pol: selectedItem?.bookings?.vessels?.port_of_loading,
            port_of_discharge: selectedItem?.bookings?.port_of_discharge,
            search: search,
            id: selectedItem?.booking_id,
          },
        });
        if (data?.result == true) {
          setUpdatedBookings(s_array(data?.data, 'booking_number'));
        }
      } catch (error) {}
  };

  const id = form.watch('booking_id');
  const company_id = form.watch('company_id');
  const pull_date = form.watch('pull_date');
  const ingate_date = form.watch('ingate_date');

  useEffect(() => {
    setBookingId(id);
    if (id != undefined && bookingId == id && id != '' && id != null) {
      if (isUpdate) {
        getVesselBooking();
      }
      getBookings();
      getDrivers();

      if (isUpdate && id != form?.confirmData?.id) {
        form?.setConfirmData({ ...form.confirmData, id });
      }
    }
    if (!id) {
      if (!isUpdate) {
        form?.setValue('no_units_load', null);
      }
      form?.setValue('booking_suffix', null);
    }
  }, [id, bookingId]);

  // Handle shipping_document_id setting for create mode
  useEffect(() => {
    if (!isUpdate && id && id != undefined && id != '' && id != null) {
      const fetchBookingDetails = async () => {
        try {
          const res = await axios.get(`containers/yardLocation/${id}`);
          if (res.status === 200 && res?.data?.data?.[0]) {
            const bookingParty = res?.data?.data[0]?.party;
            if (bookingParty === 'TGL') {
              form?.setValue('shipping_document_id', 135);
            } else {
              form?.setValue('shipping_document_id', 134);
            }
          }
        } catch (error) {
          console.error('❌ Error fetching booking details:', error);
        }
      };

      fetchBookingDetails();
    }
  }, [id, isUpdate]);

  //////////////////////////

  useEffect(() => {
    if (!isUpdate) {
      const fetchCompanyData = async () => {
        if (company_id) {
          try {
            setIsInstractionLoading(true);
            const res = await axios.get(`companies/loading/${company_id}`);

            if (res.status === 200) {
              form.setValue(
                'loading_instruction',
                res?.data?.data?.loading_instruction,
              );
              form.setValue(
                'documentation_instruction',
                res?.data?.data?.documentation_instruction,
              );

              setBookingParties((prev) => ({
                ...prev,
                company_booking_parties: res?.data?.data?.booking_parties,
              }));
            }
            setIsInstractionLoading(false);
          } catch (error) {
            console.error('Error fetching company data:', error);
          }
        } else {
          form.setValue('loading_instruction', null);
          form.setValue('documentation_instruction', null);
          setBookingParties((prev) => ({
            ...prev,
            company_booking_parties: [],
          }));
        }
      };

      fetchCompanyData();
    }
  }, [company_id]);

  const [isFirstTime, setIsFirstTime] = useState(isUpdate);

  useEffect(() => {
    if (
      bookingParties.booking_party &&
      bookingParties.company_booking_parties.length > 0
    ) {
      if (isFirstTime) {
        setIsFirstTime(false);
      } else {
        if (
          !bookingParties.company_booking_parties.includes(
            bookingParties.booking_party,
          )
        ) {
          setOpenBookingPartiesConfirm(true);
        }
      }
    }
  }, [bookingParties]);

  const shipId = [
    {
      id: 134,
      label: 'Peace Global Logistics',
    },
    {
      id: 135,
      label: 'TOTAL GLOBAL LOGISTICS',
    },
  ];

  const onInputChange = (value) => {
    if (value == '') {
      setSearchTerm('');
    }
    if (value) {
      setSearchTerm(value);
    }
  };

  const onKeyDown = (e, field) => {
    if (e?.key === 'Backspace') {
      field.onChange('');
      if (field.value == '') {
        getVesselBooking();
      }
    } else {
      onInputChange(e.target.value);
    }
  };

  ////////////////////////////
  yards && yards.length > 0
    ? form.getValues('yard_location_id') || form.watch('yard_location_id')
      ? form?.setIsYardExist(false)
      : form?.setIsYardExist(true)
    : form?.setIsYardExist(false);

  let pull_driver = form.watch('pull_driver') ?? null;
  let ingate_driver = form.watch('ingate_driver') ?? null;

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Shipment Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="booking_id"
            control={form.control}
            render={({ field, fieldState: { error } }) =>
              isUpdate ? (
                <Autocomplete
                  size="small"
                  disablePortal
                  id="combo-box-demo"
                  options={updatedBookings}
                  value={
                    updatedBookings.find((item) => item.id === field.value) ||
                    null
                  }
                  //@ts-ignore
                  onChange={(event, selectedValue) =>
                    field.onChange(selectedValue?.id || null)
                  }
                  onKeyDown={(e) => onKeyDown(e, field)}
                  getOptionLabel={(option) => option.label}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Booking | Vessel"
                      error={!!error}
                      helperText={error ? error.message : null}
                      onPaste={(e) => {
                        const value = e?.clipboardData?.getData('Text');
                        if (value) {
                          field.onChange('');
                          onInputChange(value.trim());
                        }
                      }}
                    />
                  )}
                />
              ) : (
                <AutoComplete
                  url={bookingURL}
                  label="Booking | Vessel"
                  fieldName="booking_number"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'booking_number'}
                  modal={'bookings'}
                  customeName={'booking_number'}
                />
              )
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="container_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.container_number?.message.length > 0
                    ? true
                    : false
                }
                id="container_number"
                value={field.value ?? ''}
                label="Container Number"
                fullWidth
                variant="outlined"
                onChange={(value) => {
                  const newValue =
                    value.target.value.length == 0 ? null : value.target.value;
                  field.onChange(newValue);
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="booking_suffix"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.booking_suffix?.message.length > 0 ? true : false
                }
                helperText={error?.message}
                id="booking_suffix"
                value={field.value ?? ''}
                label="Booking Suffix"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="seal_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={!!error}
                id="seal_number"
                value={field.value ?? ''}
                label="Seal Number"
                fullWidth
                variant="outlined"
                onChange={(value) => {
                  field.onChange(value.target.value);
                  // Trigger validation on change
                  form.trigger('seal_number');
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          key={renderKey}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          {yardLocation ? (
            <Controller
              name="yard_location_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  disabled={true}
                  url="autoComplete"
                  label="Yard Location"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={yards}
                  column={'name'}
                  modal={'yard_locations'}
                  // defaultValue={yards[0]}
                />
              )}
            />
          ) : (
            <Controller
              name="yard_location_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Select Yard Location"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={yards}
                  column={''}
                  modal={''}
                  // defaultValue={yards[0]}
                />
              )}
            />
          )}
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="loading_instruction"
            control={form.control}
            disabled={isInstractionLoading}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.loading_instruction?.message.length > 0
                    ? true
                    : false
                }
                id="loading_instruction"
                value={field.value ?? ''}
                label="Loading Instruction"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
                disabled={isInstractionLoading}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="company_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <CustomAutoCompleteWrapper
                  field={field}
                  onHandWithTitles={onHandWithTitles}
                  listOfCompanies={listOfCompanies}
                  isUpdate={isUpdate}
                  selectedItem={selectedItem}
                  error={error}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="documentation_instruction"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.documentation_instruction?.message.length > 0
                    ? true
                    : false
                }
                id="documentation_instruction"
                value={field.value ?? ''}
                label="Documentation Instructions"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
                disabled={isInstractionLoading}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="pin_in"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.pin_in?.message.length > 0 ? true : false}
                id="pin_in"
                value={field.value ?? ''}
                label="Pin In"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="actions"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.actions?.message.length > 0 ? true : false}
                id="actions"
                value={field.value ?? ''}
                label="Actions"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="pin_out"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.pin_out?.message.length > 0 ? true : false}
                id="pin_out"
                value={field.value ?? ''}
                label="Pin Out"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="loading_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Loading Date"
                value={
                  !field.value ? null : dayjs.utc(field.value).hour(12).toDate()
                }
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="invoice_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.invoice_number?.message.length > 0 ? true : false
                }
                id="invoice_number"
                value={field.value ?? ''}
                label="Invoice Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="photo_link"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.photo_link?.message.length > 0 ? true : false
                }
                id="photo_link"
                value={field.value ?? ''}
                label="Photo Link"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="aes_itn_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.aes_itn_number?.message.length > 0 ? true : false
                }
                id="aes_itn_number"
                value={field.value ?? ''}
                label="AES ITN Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid size={6}>
          <Controller
            name="aes_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="AES Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={aes_title_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="title_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Title Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={container_title_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        {/* loader */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            key={renderKey}
            name="loaders"
            control={form.control}
            render={({ field }) => (
              <FilterAutocomplete
                url={`containers/loaders/${id}?id=`}
                label="Loaders"
                name={'loaders'}
                keyName={'name'}
                values={field.value ?? []}
                disabled={!id}
                onChange={(values) => {
                  field.onChange(values);
                  setUnknownLoader(values);
                }}
              />
            )}
          />
          {unknownLoader?.includes(15) && (
            <Controller
              name="loader_remark"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.loader_remark?.message.length > 0 ? true : false
                  }
                  sx={{ mt: 1 }}
                  id="loader_remark"
                  value={field.value ?? ''}
                  label="Unknown Loader Remark"
                  fullWidth
                  multiline
                  rows={2}
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          )}
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="load_combination_type"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <FormControl sx={{ minWidth: '100%' }} size="small">
                <InputLabel id="demo-select-small-label">
                  Load Combination Type
                </InputLabel>
                <Select
                  labelId="demo-select-small-label"
                  label="Arrival Notice type"
                  fullWidth
                  variant="outlined"
                  size="small"
                  value={field.value}
                  onChange={(event) => field.onChange(event.target.value)}
                  error={error ? true : false}
                  sx={{ textAlign: 'start' }}
                >
                  {[
                    {
                      value: 'easy',
                      title: 'Easy',
                    },
                    {
                      value: 'tight',
                      title: 'Tight',
                    },
                    {
                      value: 'normal',
                      title: 'Normal',
                    },
                  ].map((item, i) => (
                    <MenuItem key={i} value={item.value}>
                      {item.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="clearance_invoice_link"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.clearance_invoice_link?.message.length > 0
                    ? true
                    : false
                }
                id="clearance_invoice_link"
                value={field.value ?? ''}
                label="Clearance Invoice Link"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="bill_of_loading_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.bill_of_loading_number?.message.length > 0
                    ? true
                    : false
                }
                id="bill_of_loading_number"
                value={field.value ?? ''}
                label="Bill Of Loading Number"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="amount"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form?.errors?.amount?.message.length > 0 ? true : false}
                id="amount"
                type="number"
                value={field.value === '' ? '' : +field.value}
                label="Amount"
                fullWidth
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field?.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="no_units_load"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.no_units_load?.message.length > 0 ? true : false
                }
                helperText={error?.message}
                id="no_units_load"
                value={field.value ?? ''}
                label="No Units Load"
                fullWidth
                variant="outlined"
                onClick={() => setOpenConfirm(true)}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                InputProps={{
                  readOnly: !updateConfirm,
                }}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="shipping_document_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Shipping Document Id"
                fieldName="shipper_exporter"
                field={field}
                error={error}
                staticOptions={shipId}
                column={''}
                modal={''}
                defualtValue={shipId[0]}
                customeName={'shipper_exporter'}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="ingate"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="InGate"
                fieldName=""
                field={field}
                error={error}
                staticOptions={inGete}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="of_loading_photo"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.of_loading_photo?.message.length > 0
                    ? true
                    : false
                }
                id="of_loading_photo"
                value={field.value ?? ''}
                label="Ofloading Photo Link"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="of_loading_video"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.of_loading_video?.message.length > 0
                    ? true
                    : false
                }
                id="of_loading_video"
                value={field.value ?? ''}
                label="OfLoading Video Link"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="aes_filling_link"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.aes_filling_link?.message.length > 0
                    ? true
                    : false
                }
                id="aes_filling_link"
                value={field.value ?? ''}
                label="AES Filling Link"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          {isUpdate && selectedItem?.bookings?.type == 'HAZ' && (
            <Box display="flex" justifyContent="space-between" flexWrap="wrap">
              {/* <Controller
                control={form.control}
                name="invisible_for_customer"
                render={({ field }) => (
                  <FormControl component="fieldset" variant="standard">
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            inputRef={field.ref}
                            checked={field.value}
                            onChange={field.onChange}
                            name="invisible_for_customer"
                          />
                        }
                        label="Invisible in customer portal"
                      />
                    </FormGroup>
                  </FormControl>
                )}
              /> */}
              {selectedItem?.bookings?.type == 'HAZ' && (
                <Controller
                  control={form.control}
                  name="shipment_type_approved"
                  render={({ field }) => (
                    <FormControl component="fieldset" variant="standard">
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Checkbox
                              inputRef={field.ref}
                              checked={field.value}
                              onChange={field.onChange}
                              name="shipment_type_approved"
                            />
                          }
                          label="HAZ Type Approval"
                        />
                      </FormGroup>
                    </FormControl>
                  )}
                />
              )}
            </Box>
          )}
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        ></Grid>
        {/* pull_driver */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="pull_driver"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Pull Driver"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={drivers}
                column={'name'}
                modal={'drivers'}
                disableAutoComplete={!id || selectedItem?.pull_payment_id}
              />
            )}
          />
        </Grid>
        {/* ingate_driver */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="ingate_driver"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Ingate Driver"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={drivers}
                column={'name'}
                modal={'drivers'}
                disableAutoComplete={!id || selectedItem?.ingate_payment_id}
              />
            )}
          />
        </Grid>
        {/* pull date */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="pull_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Pull Date"
                value={
                  !field.value ? null : dayjs.utc(field.value).hour(12).toDate()
                }
                format="yyyy/MM/dd"
                onChange={(e) => field.onChange(formFormatDate(e))}
                inputRef={field.ref}
                maxDate={ingate_date ? new Date(ingate_date) : null}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
                disabled={selectedItem?.pull_payment_id}
              />
            )}
          />
        </Grid>
        {/* ingate date */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="ingate_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Ingate Date"
                value={
                  !field.value ? null : dayjs.utc(field.value).hour(12).toDate()
                }
                format="yyyy/MM/dd"
                onChange={(e) => field.onChange(formFormatDate(e))}
                inputRef={field.ref}
                minDate={pull_date ? new Date(pull_date) : null}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
                disabled={selectedItem?.ingate_payment_id}
              />
            )}
          />
        </Grid>
        {/* pull_driver_bonus */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="pull_driver_bonus"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.pull_driver_bonus?.message.length > 0
                    ? true
                    : false
                }
                id="pull_driver_bonus"
                value={field.value}
                label="Pull driver bonus"
                fullWidth
                type="number"
                variant="outlined"
                disabled={!pull_driver || selectedItem?.pull_payment_id}
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        {/* ingate_driver_bonus */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="ingate_driver_bonus"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.ingate_driver_bonus?.message.length > 0
                    ? true
                    : false
                }
                id="ingate_driver_bonus"
                value={field.value}
                label="Ingate driver bonus"
                fullWidth
                type="number"
                variant="outlined"
                disabled={!ingate_driver || selectedItem?.ingate_payment_id}
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>

        {/* Pull drivers_note */}
        {/* pull_driver_notes: dto.pull_driver_notes,
        ingate_driver_notes: dto.ingate_driver_notes, */}
        <Grid
          size={{
            xs: 6,
            md: 6,
          }}
        >
          <Controller
            name="pull_driver_notes"
            control={form.control}
            key={renderKey}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.pull_driver_notes?.message.length > 0
                    ? true
                    : false
                }
                id="pull_driver_notes"
                value={field.value ?? ''}
                label="Pull Driver Notes"
                fullWidth
                variant="outlined"
                multiline
                rows={3}
                disabled={!pull_driver}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        {/* In Gate drivers_note */}
        <Grid
          size={{
            xs: 6,
            md: 6,
          }}
        >
          <Controller
            name="ingate_driver_notes"
            control={form.control}
            key={renderKey}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.ingate_driver_notes?.message.length > 0
                    ? true
                    : false
                }
                id="ingate_driver_notes"
                value={field.value ?? ''}
                label="Ingate Driver Notes"
                fullWidth
                variant="outlined"
                multiline
                rows={3}
                disabled={!ingate_driver}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
      {form.allowTochange && (
        <AllowToEditDialog
          setOpen={form?.setOpen}
          open={form?.open}
          setAllowTochange={form?.setAllowTochange}
          data={form?.confirmData}
        />
      )}
      {!updateConfirm && (
        <UpdateConfirm
          open={openConfirm}
          setOpen={setOpenConfirm}
          setUpdateConfirm={setUpdateConfirm}
        />
      )}
      <BookingPartiesDoesntMatchConfirm
        open={openBookingPartiesConfirm}
        setOpen={setOpenBookingPartiesConfirm}
      />
    </Box>
  );
}

const UpdateConfirm = ({ open, setOpen, setUpdateConfirm }) => {
  return (
    <React.Fragment>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Change (No Unites Load) Confirmation
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are your sure to change the value of (No Unites Load) which setted
            by system?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              setUpdateConfirm(true);
              setOpen(false);
            }}
            autoFocus
          >
            Yes
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};

const BookingPartiesDoesntMatchConfirm = ({ open, setOpen }) => {
  return (
    <React.Fragment>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Booking Party Mismatch
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            The selected booking party is not included in the company's list of
            booking parties.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setOpen(false);
            }}
            autoFocus
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};

const CustomAutoCompleteWrapper = ({
  field,
  onHandWithTitles,
  listOfCompanies,
  isUpdate,
  selectedItem,
  error,
}) => {
  const [defaultValue, setDefaultValue] = useState(null);

  const defaultValueOnAssign = (field) => {
    if (!onHandWithTitles || !listOfCompanies || listOfCompanies.length === 0) {
      return null; // Return null if conditions are not met
    }

    const excludedIds = new Set(['577', '806', '158']);
    let defaultCompany = null;
    if (selectedItem?.every((o) => o.is_scrap === true)) {
      defaultCompany = listOfCompanies.find((company) => company.id === '1062');
    } else {
      defaultCompany = selectedItem?.some((o) => o.load_type === 'mix')
        ? listOfCompanies.find((company) => excludedIds.has(company.id))
        : listOfCompanies.find((company) => !excludedIds.has(company.id)) ||
          null;
    }

    if (defaultCompany) {
      field.onChange(defaultCompany.id);
      return defaultCompany.label;
    }

    return null;
  };

  useEffect(() => {
    if (onHandWithTitles && listOfCompanies && listOfCompanies.length > 0) {
      const defaultValue = defaultValueOnAssign(field);
      setDefaultValue(defaultValue);
    }
  }, [onHandWithTitles, listOfCompanies]);

  return (
    <CustomAutoComplete
      url={
        onHandWithTitles
          ? false
          : isUpdate
            ? 'autoComplete/shipmentCompany'
            : 'AutoComplete'
      }
      label="Select Company"
      fieldName="name"
      field={field}
      error={error}
      staticOptions={onHandWithTitles ? listOfCompanies : false}
      column={onHandWithTitles ? '' : 'name'}
      modal={onHandWithTitles ? '' : 'companies'}
      defaultValue={defaultValue}
      handleOnChangeFromStepper={onHandWithTitles ? true : false}
      CustomFlag={
        onHandWithTitles ? '' : isUpdate ? 'checkifCompanyHasVehicle' : ''
      }
      shipementId={onHandWithTitles ? null : isUpdate ? selectedItem?.id : null}
      stepperHandleOnChange={(_event, value) => {
        if (onHandWithTitles) {
          const selectedKey = value?.id;
          field.onChange(selectedKey);
        }
      }}
    />
  );
};
