import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { Grid, TextField, Typography } from '@mui/material';
import { Box } from '@mui/system';
import React from 'react';
import { Controller } from 'react-hook-form';
import { containerStatus } from '@/configs/shipment/shipmentHeader';

export default function Step2({ form }) {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Shipment Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.aes_itn_number?.message.length > 0 ? true : false
            }
            id="aes_itn_number"
            required
            label="Aes Itn Number"
            fullWidth
            variant="outlined"
            {...form.register('aes_itn_number')}
            helperText={form.errors.aes_itn_number?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.tracking_contatiner?.message.length > 0 ? true : false
            }
            id="tracking_contatiner"
            required
            label="Tracking Contatiner"
            fullWidth
            variant="outlined"
            {...form.register('tracking_contatiner')}
            helperText={form.errors.tracking_contatiner?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.bill_of_loading_number?.message.length > 0
                ? true
                : false
            }
            id="bill_of_loading_number"
            required
            label="Bill Of Loading Number"
            fullWidth
            variant="outlined"
            {...form.register('bill_of_loading_number')}
            helperText={form.errors.bill_of_loading_number?.message}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.seal_number?.message.length > 0 ? true : false}
            id="seal_number"
            required
            label="Seal Number"
            fullWidth
            variant="outlined"
            {...form.register('seal_number')}
            helperText={form.errors.seal_number?.message}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.measurement?.message.length > 0 ? true : false}
            id="measurement"
            required
            label="Measurement"
            fullWidth
            variant="outlined"
            {...form.register('measurement')}
            helperText={form.errors.measurement?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.invoice_number?.message.length > 0 ? true : false
            }
            id="invoice_number"
            required
            label="Invoice Number"
            fullWidth
            variant="outlined"
            {...form.register('invoice_number')}
            helperText={form.errors.invoice_number?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="amount"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="amount"
                type="number"
                value={field.value === '' ? '' : +field.value}
                label="Amount"
                fullWidth
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field?.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.photo_link?.message.length > 0 ? true : false}
            id="photo_link"
            required
            label="Photo Link"
            fullWidth
            variant="outlined"
            {...form.register('photo_link')}
            helperText={form.errors.photo_link?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.pin_in?.message.length > 0 ? true : false}
            id="pin_in"
            required
            label="Pin In"
            fullWidth
            variant="outlined"
            {...form.register('pin_in')}
            helperText={form.errors.pin_in?.message}
          />
        </Grid>

        <Grid size={12}>
          <Controller
            name="status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={containerStatus}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
