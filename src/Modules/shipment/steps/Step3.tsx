import { shipmentCosts } from '@/configs/shipment/shipmentHeader';
import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import Tooltip from '@mui/material/Tooltip';
import CancelIcon from '@mui/icons-material/Cancel';
import { Controller } from 'react-hook-form';
import { allChargesMap } from '@/Modules/GeneralSetting/Companies/steps/Step1Next';

type ChargeType = {
  id: number;
  name: string;
  const: number;
  amount: number;
  category: string;
  remark: string;
};

const defaultCharge: ChargeType = {
  id: -1,
  name: 'none',
  const: 0,
  amount: 0,
  category: '',
  remark: '',
};

export default function Step3({ form }) {
  const [showDescription, setShowDescription] = useState(false);
  const [companyCharges, setCompanyCharges] = useState([]);
  const [shipmentCharges, setShipmentCharges] = useState(
    form.getValues('container_charges') || [],
  );
  const [chargeToBeAdded, setChargeToBeAdded] =
    useState<ChargeType>(defaultCharge);
  const [remainingCharges, setRemainingCharges] = useState([]);
  const [showSelectChargeWarning, setShowSelectChargeWarning] = useState(false);

  let containerCosts = form && form.getValues('container_costs');

  const addContainerCosts = (e, value) => {
    const updatedContainerCosts = containerCosts;

    const existingIndex = updatedContainerCosts.findIndex(
      (item) => item.name === value,
    );
    if (
      (Number(e.target.value) === 0 || e.target.value === '') &&
      existingIndex !== -1
    ) {
      if (form.isUpdate) {
        updatedContainerCosts[existingIndex].value = 0;
      } else {
        updatedContainerCosts.splice(existingIndex, 1);
      }
      if (value === 'others') {
        setShowDescription(false);
        form.setIsDescriptionEmpty(false);
      }
    } else {
      if (value === 'others') {
        updateDescriptionVisibility(e, value);
        if (!updatedContainerCosts[existingIndex]?.description) {
          form.setIsDescriptionEmpty(true);
        }
      }
      if (existingIndex === -1) {
        updatedContainerCosts.push({ value: e.target.value, name: value });
      } else {
        updatedContainerCosts[existingIndex].value = e.target.value;
      }
    }

    containerCosts = updatedContainerCosts;
    form?.setValue('container_costs', containerCosts);
  };

  const updateDescriptionVisibility = (e, value) => {
    setShowDescription(value === 'others' && e.target.value > 0);
  };

  const addCostDescription = (e, value) => {
    const existingIndex = containerCosts.findIndex(
      (item) => item.name === value,
    );

    if (e.target.value.trim() === '') {
      form.setIsDescriptionEmpty(true);
    } else {
      form.setIsDescriptionEmpty(false);
    }

    containerCosts[existingIndex].description = e.target.value;
    form?.setValue('container_costs', containerCosts);
  };

  const toggleDescription = (isOthers, value) => {
    if (showDescription) {
      return true;
    } else {
      if (isOthers && value > 0) {
        return true;
      } else {
        return false;
      }
    }
  };

  const defaultValue = (cost, item) => {
    if (cost) {
      return cost?.value || null;
    } else {
      if (item.value == 'loading_cost') {
        containerCosts.push({
          value: form.getValues('loading_cost'),
          name: 'loading_cost',
        });
        return form.getValues('loading_cost');
      }
    }
  };

  const getCompanyCharges = async (companyId) => {
    try {
      let { data } = await axios.get(
        `companies/company-charges/${companyId}?charge_type=per_container`,
      );

      if (data?.company_charges.length > 0) {
        setCompanyCharges(data?.company_charges);
      }

      return data?.company_charges;
    } catch (error) {
      console.error(error);
    }
  };

  const handleShipmentChargeInputChange = (index, field, value) => {
    const newCharges = [...shipmentCharges];
    newCharges[index][field] = value;
    setShipmentCharges(newCharges);
  };

  const handleAddCharge = (newCharge) => {
    setShipmentCharges((prevState) => [...prevState, newCharge]);
  };

  const handleRemoveCharge = (chargeName) => {
    setShipmentCharges((prevState) =>
      prevState.filter((charge) => charge.name !== chargeName),
    );
  };

  const handleRemoveAllCharges = () => {
    setShipmentCharges([]);
  };

  useEffect(() => {
    const handleChangesInShipmentCharges = async () => {
      let companyChargesToBeSet: any[];

      if (companyCharges.length === 0) {
        companyChargesToBeSet = await getCompanyCharges(
          parseFloat(form.getValues('company_id')),
        );
      } else {
        companyChargesToBeSet = companyCharges;
      }

      setRemainingCharges(
        companyChargesToBeSet.filter(
          (charge) =>
            !shipmentCharges.some(
              (shipmentCharge) => shipmentCharge.name === charge.name,
            ),
        ),
      );
    };

    handleChangesInShipmentCharges().catch((e) =>
      console.log(
        e,
        'error occurred while handling changes in the shipment charges',
      ),
    );

    form.setValue('container_charges', shipmentCharges);
  }, [shipmentCharges]);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 3 }}>
        Shipment Costs/Charges
      </Typography>
      <Grid container spacing={2}>
        {shipmentCosts &&
          shipmentCosts.map((item, i) => {
            const cost = containerCosts?.find((c) => c.name === item.value);
            const isOthers = item.value === 'others';

            return (
              <React.Fragment key={i}>
                <Grid
                  key={i}
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <TextField
                    size="small"
                    defaultValue={defaultValue(cost, item)}
                    id={item?.value}
                    label={item?.name}
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(e) => {
                      addContainerCosts(e, item?.value);
                    }}
                    InputProps={{
                      inputProps: { min: 0 },
                    }}
                    helperText={form.errors.item?.name?.message}
                  />
                </Grid>
                {isOthers && toggleDescription(isOthers, cost?.value) && (
                  <Grid
                    size={{
                      xs: 12,
                      md: 6,
                    }}
                  >
                    <TextField
                      size="small"
                      defaultValue={cost?.description || null}
                      id={item?.description}
                      label={'Description'}
                      fullWidth
                      variant="outlined"
                      onChange={(e) => {
                        addCostDescription(e, item?.value);
                      }}
                      helperText={form.errors.item?.description?.message}
                      error={form.isDescriptionEmpty}
                      required
                    />
                  </Grid>
                )}
              </React.Fragment>
            );
          })}
      </Grid>
      <Grid
        container
        border={1}
        borderColor="#C0C0C0"
        borderRadius={1.5}
        mt={2}
      >
        <Grid
          container
          display="flex"
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          borderBottom={1}
          borderColor="#DCDCDC"
          mb={3}
          pl={2}
          pr={1}
        >
          <Grid>
            <Typography variant="h5" my={2}>
              Shipment Charges
            </Typography>
          </Grid>
          <Grid>
            <Tooltip title="Remove All Charges">
              <Button
                color="error"
                onClick={() => {
                  handleRemoveAllCharges();
                }}
                sx={{
                  minWidth: 'auto',
                }}
              >
                <CancelIcon width="42px" />
              </Button>
            </Tooltip>
          </Grid>
        </Grid>
        {shipmentCharges?.map((charge, index) => (
          <Grid
            key={charge.id}
            style={{ display: 'flex' }}
            gap={2}
            mb={2}
            mx={2}
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Grid
              size={{
                xs: 12,
                md: 7,
              }}
            >
              <Controller
                name={`charges_name_${charge.id}`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.cost?.message.length > 0}
                    id="name"
                    value={allChargesMap[charge.name]}
                    label="Name"
                    fullWidth
                    type="text"
                    onChange={(e) =>
                      handleShipmentChargeInputChange(
                        index,
                        'name',
                        e.target.value,
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                    disabled={true}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 1.5,
              }}
            >
              <Controller
                name={`charges_amount_${charge.id}`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.amount?.message.length > 0}
                    id="amount"
                    value={charge.amount}
                    label="Amount"
                    fullWidth
                    type="number"
                    onChange={(e) =>
                      handleShipmentChargeInputChange(
                        index,
                        'amount',
                        Number(e.target.value),
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 1.5,
              }}
            >
              <Controller
                name={`charges_cost_${charge.id}`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.cost?.message.length > 0}
                    id="cost"
                    value={charge.cost}
                    label="Cost"
                    fullWidth
                    type="number"
                    onChange={(e) =>
                      handleShipmentChargeInputChange(
                        index,
                        'cost',
                        Number(e.target.value),
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 1.5,
              }}
            >
              <Controller
                name={`charges_remark_${charge.id}`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.remark?.message.length > 0}
                    id="remark"
                    value={charge.remark}
                    label="Remark"
                    fullWidth
                    onChange={(e) =>
                      handleShipmentChargeInputChange(
                        index,
                        'remark',
                        e.target.value,
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              display="flex"
              flexDirection="column"
              justifyContent="start"
              alignItems="start"
              size={0.5}
            >
              <Tooltip title="Remove Charge">
                <Button
                  color="error"
                  onClick={() => {
                    handleRemoveCharge(charge.name);
                  }}
                  sx={{
                    margin: '-2px 0 2px 0',
                    minWidth: 'auto',
                  }}
                >
                  <CancelIcon />
                </Button>
              </Tooltip>
            </Grid>
          </Grid>
        ))}
        <Grid container my={1.2} gap={2} mx={2}>
          <Grid
            size={{
              xs: 7,
              lg: 5,
            }}
          >
            <FormControl
              fullWidth
              error={
                chargeToBeAdded.name === 'none' &&
                remainingCharges.length > 0 &&
                showSelectChargeWarning
              }
            >
              <Autocomplete
                fullWidth
                size="small"
                id="add_company_charges"
                options={[defaultCharge, ...remainingCharges]}
                value={chargeToBeAdded}
                onChange={(_, newValue) => {
                  setChargeToBeAdded(newValue ?? defaultCharge);
                }}
                getOptionLabel={(option) =>
                  option.name === 'none'
                    ? 'None'
                    : allChargesMap[option.name] || option.name
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Company Charges"
                    error={
                      chargeToBeAdded.name === 'none' &&
                      remainingCharges.length > 0 &&
                      showSelectChargeWarning
                    }
                  />
                )}
              />
              <FormHelperText>
                {chargeToBeAdded.name === 'none' &&
                  remainingCharges.length > 0 &&
                  showSelectChargeWarning &&
                  'Select a charge'}
              </FormHelperText>
            </FormControl>
          </Grid>
          <Grid
            p={0}
            size={{
              xs: 4,
              lg: 3,
            }}
          >
            <Button
              sx={{ height: '34px' }}
              variant="outlined"
              color="primary"
              onClick={() => {
                if (chargeToBeAdded.name !== 'none') {
                  handleAddCharge(chargeToBeAdded);
                  setChargeToBeAdded(defaultCharge);
                  setShowSelectChargeWarning(false);
                } else {
                  setShowSelectChargeWarning(true);
                }
              }}
              disabled={remainingCharges.length === 0}
            >
              Add Charge
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
