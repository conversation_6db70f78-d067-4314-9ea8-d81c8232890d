import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Button, Container, IconButton, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { CONTAINERS } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { filterContentStatus } from '@/configs/shipment/ShippmentStatusHeader';
import { useLocationContext } from '@/contexts/LocationsContext';
import { appButton } from '@/configs/shipment/shipmentAppButton';
import ViewSingleShipment from '../shipmentComponents/ViewSingleShipment';
import { CreateShipment } from '../CreateShipment';
import PrintModal from '@/components/mainComponents/cModal/PrintModal';
import { removeUnderScore, removeUnderScore2 } from '@/configs/common';
import { printFormContent } from '@/configs/shipment/shipmentHeader';
import { recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { useCustomColumnShipmentWithLocation } from './ShipmentStatusWithLocationsCustomColumn';
import ShipmentWithLocationPDFModal from './shipmentWithLocationsPDFModal';
import { toast } from 'react-toastify';
import ShipmentChangeStatus from '../shipmentComponents/ShipmentChangeStatus';
import LoadPlanPreview from '../shipmentComponents/LoadPlanPreview';
import { formatDate } from '@/configs/vehicles/configs';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import PendingActionsIcon from '@mui/icons-material/PendingActions';
import LoadPlanFormDialog from '../shipmentComponents/LoadPlanMultiplePreview';
import UpdateTitleAndAesStatus from '../shipmentComponents/UpdateTitle&AesStatus';
import UpdateLoadingDate from '../shipmentComponents/UpdateLoadingDate';
import UpdateEtaAndEtd from '../shipmentComponents/UpdateEta&Etd';
import UpdateSiCuttOff from '../shipmentComponents/UpdateSiCuttOff';
import UpdateBrd from '../shipmentComponents/UpdateBrd';
import DropZoneModal from '@/components/mainComponents/cModal/DropZoneModal';

///////////////////////////////////////////////////////////////
const NewShipmentsWithLocations = ({
  defaultHeaders,
  apiUrl,
  statusTab,
  breadcrumbs,
  activeTab,
}) => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [openPrint, setOpenPrint] = useState(false);
  const [printForm, setPrintForm] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const title = 'Shipments ' + removeUnderScore(statusTab);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [view, setView] = useState(false);
  const [viewData, setViewData] = useState({});
  const router = useRouter();
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showLoadPlanDialog, setShowLoadPlanDialog] = useState(false);
  const [setOpenTransaction] = useState(false);
  const [updateLoadingDate, setUpdateLoadingDate] = useState(false);
  const [updateSiCuttOff, setUpdateSiCuttOff] = useState(false);
  const [updateDate, setUpdateDate] = useState(false);
  const [dateType, setDateType] = useState('');
  const [updateBrd, setUpdateBrd] = useState(false);
  const [forceReload, setForceReload] = useState(false);
  const [releaseMulti, setReleaseMulti] = useState(false);
  const [showUploadImageButton, setShowUploadImage] = useState(false);
  const [singleContainer, setSingleContainer] = useState(null);

  const toggleUploadImages = async () =>
    setShowUploadImage(showUploadImageButton != showUploadImageButton);

  let tempFilterData = {};
  if (router.query.filterData) {
    //@ts-ignore
    tempFilterData = JSON.parse(router.query.filterData);
  }
  if (router.query.company) {
    tempFilterData['company_id'] = [+router.query.company_id];
  }
  if (router.query.halfcut_status)
    tempFilterData['halfcut_status'] = [router.query.halfcut_status];

  const { map, isLoadingLocations }: any = useLocationContext();
  const [isDup, setIsDup] = useState(false);
  const [updateStatus, setUpdateStatus] = useState(false);
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [printData, setPrintData] = useState([]);
  const defaultPerPage = 20;
  const tabsWithStoredPerPage = ['pending', 'at_the_dock', 'checked'];

  const [options, setOptions] = useState(() => {
    let storedPerPage = defaultPerPage;

    if (tabsWithStoredPerPage.includes(statusTab)) {
      const savedPerPage = localStorage.getItem(`${statusTab}_perPage`);
      if (savedPerPage) {
        storedPerPage = parseInt(savedPerPage, 10);
      }
    }

    return {
      page: 1,
      perPage: storedPerPage,
      filterData: { ...tempFilterData },
      tab: activeTab,
      search: '',
      exactMatch: false,
      orderBy: { column: 'id', order: 'desc' },
    };
  });

  useEffect(() => {
    if (tabsWithStoredPerPage.includes(statusTab)) {
      localStorage.setItem(`${statusTab}_perPage`, options.perPage.toString());
    }
  }, [options.perPage, statusTab]);

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile, activeTab]);

  useEffect(() => {
    if (activeTab != options.tab && activeTab == router.query.id) {
      setTotalItems(0);
      setOptions({
        ...options,
        page: 1,
        perPage: 20,
        filterData: {},
        tab: activeTab,
        search: '',
      });
    }
  }, [router]);

  useEffect(() => {
    if (options.tab == activeTab) {
      fetchRecords();
    }
  }, [
    options.page,
    options.perPage,
    options.search,
    options.tab,
    activeTab,
    options.filterData,
    router,
    isLoadingLocations,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const getSingleRow = async (id) => {
    try {
      setSelectedItems([]);
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data?.data);
      }
    } catch (error) {
      setView(false);
    }
  };

  const fetchRecords = async () => {
    try {
      if (locations.length > 0) {
        if (cancelToken) {
          await cancelToken.abort();
        }
        const controller = new AbortController();
        setCancelToken(controller);
        setTableRecords([]);
        setSelectedItems([]);
        setLoading(true);
        let params = {
          page: options.page,
          per_page: statusTab == 'at_loading' ? -1 : options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          // column: options.orderBy.column,
          // order: options.orderBy.order,
          filterData: JSON.stringify(options.filterData),
          status: statusTab,
        };
        if (options.tab !== 'all') {
          //@ts-ignore
          params.locationId = locations?.filter((item) => {
            if (item.name == activeTab) {
              return item?.id;
            }
          })[0]?.id;
        }
        let { data } = await axios.get(apiUrl, {
          signal: controller.signal,
          params: params,
        });
        setTotalItems(data.total);
        setTableRecords(data.data);
        setLoading(false);
      }
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`containers/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      console.log(error);
      setSingleFetchLoading(false);
    }
  };

  const onTabChange = (val) => {
    setSelectedItems([]);
    switch (statusTab) {
      case 'at_loading':
        router.push(`/shipment/at_loading/${val}`);
        break;
      case 'pending':
        router.push(`/shipment/pending/${val}`);
        break;
      case 'at_the_dock':
        router.push(`/shipment/at_the_dock/${val}`);
        break;
      case 'checked':
        router.push(`/shipment/checked_page/${val}`);
        break;
      case 'clearance':
        router.push(`/shipment/clearance/${val}`);
        break;
      case 'on_the_way':
        router.push(`/shipment/on_the_way/${val}`);
        break;
    }
  };

  const onEdit = async () => {
    try {
      setIsDup(false);
      setIsUpdate(true);
      setShowCreate(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setSelectedItems([res?.data?.data]);
      }
    } catch (error) {
      setIsDup(false);
      setIsUpdate(false);
      setShowCreate(false);
      console.log(error);
    }
  };

  const checkDeleteLoad = selectedItems.some((element) => {
    return element?.vehicles?.length > 0;
  });
  const onDelete = () => {
    if (checkDeleteLoad) {
      toast.warn(
        'You can not delete active shipment, some or one of selected shipment is active please review the selected Items! ',
      );
    } else {
      recordManager({
        data: null,
        type: 'delete',
        setTableRecords,
        tableRecords,
        selectedItems,
        setSelectedItems,
        setTotalItems,
        totalItems,
        apiUrl: 'containers',
      });
    }
  };

  const handDuplicate = () => {
    const updatedSelectedItems = selectedItems.map((item) => ({
      ...item,
      container_number: '',
      pin_out: null,
      actions: null,
    }));
    setIsUpdate(false);
    setIsDup(true);
    setShowCreate(true);
    setSelectedItems(updatedSelectedItems);
  };

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////
  const locations = map((item) => {
    return {
      name: item.name,
      key: item.key,
      id: item.id,
      icon: item.icon,
      to: item.to,
    };
  });

  const checkAskForETAConfirmation = () => {
    return selectedItems.some((element) => element.status == 'arrived');
  };

  const pageName = statusTab + options.tab;

  const mergedDataTableColumn = useCustomColumnShipmentWithLocation({
    setSelectedItems,
    getSingleRow: getSingleRow,
    setUpdateStatus: setUpdateStatus,
    setUpdateLoadingDate: setUpdateLoadingDate,
    setUpdateSiCuttOff: setUpdateSiCuttOff,
    setUpdateDate: setUpdateDate,
    setDateType: setDateType,
    setUpdateBrd: setUpdateBrd,
    setSingleContainer: setSingleContainer,
    onImageUpload: setShowUploadImage,
  });
  const openLoadPlan = () => {
    const firstNonNullYardId = selectedItems.find(
      (obj) => obj.yard_location_id !== null,
    );
    const result = selectedItems.every(
      (obj) =>
        obj.yard_location_id === firstNonNullYardId?.yard_location_id &&
        obj.bookings?.port_of_discharge ===
          firstNonNullYardId?.bookings?.port_of_discharge,
    );
    if (!result) {
      toast.error(
        'All selected containers should be in the same yard location!',
      );
      return;
    }

    const firstNonNullDestinationId = selectedItems.find(
      (o) => o.bookings.port_of_discharge !== null,
    )?.bookings?.port_of_discharge;

    const dest = selectedItems.every(
      (o) =>
        o.bookings.port_of_discharge &&
        o.bookings.port_of_discharge == firstNonNullDestinationId,
    );
    if (!dest) {
      toast.error(
        'All selected containers should be in the same port of discharge!',
      );
      return;
    }
    setShowLoadPlanDialog(true);
  };

  const customAuctionButtons = () => (
    <>
      {perms?.includes(CONTAINERS?.SEND_LOAD_PLAN) && (
        <AppTooltip title={'Load Plan'}>
          <IconButton color="primary" onClick={openLoadPlan}>
            <PendingActionsIcon />
          </IconButton>
        </AppTooltip>
      )}

      {appButton({
        handDuplicate: statusTab == 'clearance' ? handDuplicate : false,
        selectedItems,
        setOpenConfirm,
        tab: statusTab == 'on_the_way' ? 'on_the_way' : options.tab,
        // tab: options.tab,
        mainTab: statusTab,
        setPrintForm: setPrintForm,
        showAll: false,
        CHECK_TO_ANY: perms?.includes(CONTAINERS?.CHECK_TO_ANY),
        FINAL_CHECK_TO_ANY: perms?.includes(CONTAINERS?.FINAL_CHECK_TO_ANY),
        STATUS: perms?.includes(CONTAINERS?.STATUS),
        setOpenPrint,
        fetchOne,
        setPrintData,
        setReleaseMulti,
        releaseMulti,
        setOpenTransaction,
      })}
    </>
  );
  const recordManagerInside = (data, type) => {
    recordManager({
      data,
      type,
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl,
    });
  };

  const LoadPlan = ({ item }) => {
    const [showLoadPlan, setShowLoadPlan] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadPlanData, setLoadPlanData] = useState(null);

    const fetchDetails = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get(`containers/${item.id}`);
        setLoadPlanData(data.data);
        setLoading(false);
        setShowLoadPlan(true);
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
    };

    return (
      <>
        {!['Savannah, GA', 'Houston, TX'].includes(
          item.bookings.vessels.locations.name,
        ) ||
        (item.bookings.vessels.locations.name == 'Houston, TX' &&
          item.yard_location_id == 30) ? (
          item.loadplan_sent_at ? (
            <Button
              loading={loading}
              size="small"
              color="success"
              onClick={() => {
                fetchDetails();
              }}
            >
              {formatDate(item.loadplan_sent_at, 'YYYY MMM DD')}
            </Button>
          ) : (
            <Button
              loading={loading}
              size="small"
              variant="contained"
              color="primary"
              onClick={() => {
                fetchDetails();
              }}
            >
              Load Plan
            </Button>
          )
        ) : (
          ''
        )}
        {loadPlanData && (
          <LoadPlanPreview
            loadPlanData={loadPlanData}
            showPreview={showLoadPlan}
            updateData={() => (item.loadplan_sent_at = new Date())}
            setShowPreview={setShowLoadPlan}
          />
        )}
      </>
    );
  };

  const showExportButton = () => {
    return perms?.includes(CONTAINERS?.ON_THE_WAY_EXPORT) || false;
  };

  const RDPrintCustomeTitle = () => {
    let Result = '';
    selectedItems.map((container, index) => {
      Result += container?.container_number;
      Result += index != selectedItems.length - 1 ? '-' : '';
    });
    return Result;
  };

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(CONTAINERS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>
          {removeUnderScore2(statusTab)} | {activeTab}
        </title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={breadcrumbs.concat({
            href: 'false',
            name: removeUnderScore2(activeTab),
            icon: <></>,
            key: '4',
          })}
        />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={activeTab}
          onChange={(_event, val) => onTabChange(val)}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
          {locations?.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem?.name}
              value={tabItem?.name}
            />
          ))}
        </Tabs>

        <DataTable3
          forceReload={forceReload}
          PageAction={
            <PageAction
              onFilterClick={() => setOpenFilter(true)}
              customActionButtons={customAuctionButtons}
              onEdit={onEdit}
              showEditButton={perms?.includes(CONTAINERS?.UPDATE)}
              selectedItems={selectedItems}
              title={title}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={perms?.includes(CONTAINERS?.VIEW)}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              showDownload={showExportButton()}
              onDownload={() => setShowDownload(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              dialogTitle={'Delete Shipments'}
              deleteTitle={`Are you sure you want to delete this item${
                selectedItems.length > 0 ? 's' : ''
              }`}
              showDeleteButton={
                perms?.includes(CONTAINERS?.DELETE) && !checkDeleteLoad
              }
              onDelete={onDelete}
            />
          }
          // start default props
          hidePagination={statusTab == 'at_loading' ? true : false}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          headers={selectedHeaders}
          items={tableRecords}
          setItems={setTableRecords}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={options.tab}
          tableName="shipmentWithLocations"
          //start custom props
          {...mergedDataTableColumn}
          loadplan_sent_at={(item) =>
            perms?.includes(CONTAINERS?.SEND_LOAD_PLAN) && (
              <LoadPlan item={item} />
            )
          }
        />
      </Container>
      <UpdateBrd
        onDeny={setUpdateBrd}
        open={updateBrd}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        setForceReload={setForceReload}
      />
      <UpdateLoadingDate
        onDeny={setUpdateLoadingDate}
        open={updateLoadingDate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />
      <UpdateSiCuttOff
        onDeny={setUpdateSiCuttOff}
        open={updateSiCuttOff}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        setForceReload={setForceReload}
      />
      <UpdateEtaAndEtd
        dateType={dateType}
        onDeny={setUpdateDate}
        open={updateDate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        askForConfirmation={checkAskForETAConfirmation()}
        setForceReload={setForceReload}
      />
      <ShipmentChangeStatus
        open={openConfirm}
        onDeny={setOpenConfirm}
        fetchRecords={fetchRecords}
        options={{ tab: statusTab }}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
      />
      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Shipments"
        content={filterContentStatus()}
      />

      <CreateShipment
        loading={singleFetchLoading}
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={recordManagerInside}
        isDup={isDup}
        isClearance={statusTab == 'clearance' ? true : false}
      />

      {showUploadImageButton && selectedItems?.length < 2 && (
        <DropZoneModal
          open={showUploadImageButton}
          setShowUploadImage={toggleUploadImages}
          url={apiUrl}
          route={'containers-images'}
          singleVehicle={singleContainer}
          perms={perms}
        />
      )}

      <UpdateTitleAndAesStatus
        onDeny={setUpdateStatus}
        open={updateStatus}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />

      <ViewSingleShipment
        loading={singleFetchLoading}
        data={viewData}
        setData={setViewData}
        setView={setView}
        show={view}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
      ></ColumnDialog>

      <PrintModal
        loading={singleFetchLoading}
        open={openPrint}
        toggleOpen={() => {
          setOpenPrint((d) => !d);
          setSelectedItems([]);
        }}
        initialData={printForm == 'release_document' ? printData : printData}
        title={`Print ` + removeUnderScore(printForm)}
        fileName={`Release for Container# ` + RDPrintCustomeTitle()}
        ContentPrint={printFormContent(printForm)}
      />

      <ShipmentWithLocationPDFModal
        status={statusTab}
        showDownload={showDownload}
        title={title}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        apiUrl={apiUrl}
        options={options}
        locations={locations}
      />
      <LoadPlanFormDialog
        selectedItems={selectedItems}
        showPreview={showLoadPlanDialog}
        updateData={() => {
          setSelectedItems([]);
          fetchRecords();
        }}
        setShowPreview={setShowLoadPlanDialog}
      />
    </>
  );
};
export default NewShipmentsWithLocations;
