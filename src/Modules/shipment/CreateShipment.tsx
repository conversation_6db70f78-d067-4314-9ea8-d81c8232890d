import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createSchema } from '@/configs/shipment/shipmentHeader';
import InfoIcon from '@mui/icons-material/Info';
import Step1 from './steps/Step1';
import Step3 from './steps/Step3';
import axios from '@/lib/axios';
import { formatDate } from '@/configs/vehicles/configs';
import { toast } from 'react-toastify';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import CStepperWithOutModal from '@/components/mainComponents/stepper/CStepperWithOutModal';
import allShipmentsHelper from './shipmentComponents/AllShipmentsHelper';
import CoverPhoto from '../vehicles/VehicleCoverPhoto';
import ContainerNumberConfrimationDialog from '@/configs/shipment/ContainerNumberConfrimationDialog';
import { getImageSizeUrl } from '@/utils/imageUtils';
import ContainerNumberSuffixConfirmation from '@/configs/shipment/ContainerNumberSuffixConfirmation';

export const CreateShipment = ({
  show,
  setShow,
  isUpdate,
  selectedItems,
  load = false,
  isDup = false,
  recordManager,
  setSelectedItems,
  hideModal = false,
  isClearance = false,
  loading = false,
}) => {
  const [isDone, setIsDone] = useState(false);
  //@ts-ignore
  const [isYardExist, setIsYardExist] = useState(false);
  const [isDescriptionEmpty, setIsDescriptionEmpty] = useState(false);
  const [allowTochange, setAllowTochange] = useState(false);
  const [open, setOpen] = useState(false);
  const [confirmData, setConfirmData] = useState(1);
  const [checkContainerNumber, setCheckContainerNumber] = useState(false);
  const [checkContainerNumberDone, setCheckContainerNumberDone] =
    useState(false);
  const [checkContainerNumberDialog, setCheckContainerNumberDialog] =
    useState(false);
  const [
    checkContainerNumberSuffixDialog,
    setcheckContainerNumberSuffixDialog,
  ] = useState(false);
  const [
    checkContainerNumberSuffixConfirmation,
    setcheckContainerNumberSuffixConfirmation,
  ] = useState(true);

  const exists = async (column, data, modal, extraParams = {}) => {
    let res = await allShipmentsHelper.exists(
      [column, data],
      modal,
      extraParams,
    );
    return res;
  };
  const statusObj = load ? { status: 'at_loading' } : {};

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    setValue,
    control,
    setError,
    getValues,
    watch,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(createSchema),
    defaultValues: {
      booking_id: null,
      company_id: null,
      yard_location_id: null,
      container_number: null,
      clearance_invoice_link: null,
      pin_in: null,
      ingate: null,
      loading_date: null,
      actions: null,
      loading_instruction: null,
      documentation_instruction: null,
      shipping_document_id: null,
      booking_suffix: null,
      aes_itn_number: null,
      tracking_contatiner: null,
      container_id_update_date: null,
      bill_of_loading_number: null,
      seal_number: null,
      hs_code: null,
      no_units_load: null,
      invoice_number: null,
      amount: null,
      photo_link: null,
      ...statusObj,
      pin_out: null,
      invisible_for_customer: true,
      container_costs: [],
      pull_date: null,
      ingate_date: null,
      ingate_driver: null,
      pull_driver: null,
      ingate_driver_bonus: null,
      pull_driver_bonus: null,
      driver_notes: null,
      //ingate_notes: null,

      pull_driver_notes: null,
      ingate_driver_notes: null,

      of_loading_photo: null,
      of_loading_video: null,
      loaders: [],
      loader_remark: null,
      title_status: null,
      aes_status: null,
      shipment_type_approved: null,
      load_combination_type: null,
      aes_filling_link: null,
      loading_cost: null,
      container_charges: [],
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    setValue,
    watch,
    control,
    getValues,
    setIsYardExist,
    isDescriptionEmpty,
    setIsDescriptionEmpty,
    selectedItems,
    isUpdate,
    open,
    setOpen,
    setConfirmData,
    confirmData,
    allowTochange,
    setAllowTochange,
  };

  const watchedFieldValueITN = watch('aes_itn_number');
  const watchedFieldValueInv = watch('invoice_number');

  const digitsAfterLetters = watch('container_number')?.slice(4); // Extract characters after the first 4 letters
  const hasMoreThan7Digits = /^\d{8,}$/.test(digitsAfterLetters);

  const [loadingButton, setLoadingButton] = useState(false);

  let container_num = watch('container_number');
  const [isInstractionLoading, setIsInstractionLoading] = useState(false);

  const getContainerSuffix = async () => {
    let con_n = container_num?.split('-');
    try {
      const res = await axios.get(
        `containers/checkContainerNumberSuffix/${container_num}`,
      );
      if (res.status === 200) {
        var result =
          res.data?.data > 1
            ? con_n?.[0] + '-' + res.data?.data
            : container_num;
        if (res.data?.data) {
          setcheckContainerNumberSuffixDialog(true);
        }
        form.setValue('container_number', result);
      }
    } catch (error) {}
  };

  const submit = async (_data) => {
    let values = getValues();

    //@ts-ignore
    values = {
      ...values,
      booking_suffix: String(values.booking_suffix),
      ...(isDup && isClearance ? { status: 'clearance' } : {}),
    };
    if (!isUpdate) {
      try {
        setLoadingButton(true);
        const { data } = await axios.post('/containers', values);
        if (data.result == true) {
          if (file) {
            await uploadFile(file, data?.data?.id);
          }
          recordManager(data.data, 'add');
          setIsDone(true);
          setLoadingButton(false);
          toast.success('Record created successfully!');
          return true;
        }
      } catch (error) {}
    } else {
      try {
        const prev_booking_id = selectedItems[0].booking_id;
        const booking_suffix_gt = selectedItems[0].booking_suffix;
        setLoadingButton(true);
        const { data } = await axios.patch(
          'containers/' + selectedItems[0].id,
          { ...values, prev_booking_id, booking_suffix_gt },
        );
        if (data.result === true) {
          if (file) {
            await uploadFile(file, data?.data?.id);
          }
          recordManager(data.data, 'update');
          setIsDone(true);
          toast.success('Record updated successfully!');
          setLoadingButton(false);

          return true;
        }
        return true;
      } catch (error) {
        console.log(error);
      }
    }
    setLoadingButton(false);
    return false;
  };
  useEffect(() => {
    if (!show) {
      setcheckContainerNumberSuffixConfirmation(true);
    }
  }, [show]);
  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: (
        <Step1
          form={form}
          isUpdate={isUpdate}
          selectedItem={selectedItems[0]}
          isInstractionLoading={isInstractionLoading}
          setIsInstractionLoading={setIsInstractionLoading}
        />
      ),
      props: {
        isUpdate,
      },
      async validate() {
        let tnNumber = false;
        let InvNumber = false;
        let sealNumber = false;
        if (
          container_num &&
          !isUpdate &&
          checkContainerNumberSuffixConfirmation
        ) {
          await getContainerSuffix();
        }
        if (
          hasMoreThan7Digits &&
          checkContainerNumberDone == false &&
          isUpdate == false
        ) {
          setCheckContainerNumberDialog(true);
          setCheckContainerNumber(true);
        }

        if (selectedItems[0]?.aes_itn_number != watchedFieldValueITN) {
          tnNumber = await exists(
            'aes_itn_number',
            getValues('aes_itn_number'),
            'containers',
          );
          if (tnNumber) {
            setError('aes_itn_number', {
              type: 'custom',
              message: 'AES Itn Number exist try another one!',
            });
          }
          if (watchedFieldValueITN == '' || watchedFieldValueITN == null) {
            clearErrors('aes_itn_number');
          }
        } else {
          clearErrors('aes_itn_number');
        }
        const sealValue = getValues('seal_number');
        if (sealValue && sealValue !== selectedItems[0]?.seal_number) {
          const bookingId = getValues('booking_id');
          let vesselId = null;

          if (bookingId) {
            try {
              const bookingRes = await axios.get(`bookings/${bookingId}`);
              vesselId = bookingRes.data?.data?.vessel_id;
            } catch (error) {
              console.error('Error fetching booking data:', error);
            }
          }

          sealNumber = await exists('seal_number', sealValue, 'containers', {
            vessel_id: vesselId,
          });

          if (sealNumber) {
            setError('seal_number', {
              type: 'custom',
              message: 'Seal Number already exists for this vessel!',
            });
            return false;
          }
        } else {
          clearErrors('seal_number');
        }
        if (selectedItems[0]?.invoice_number != watchedFieldValueInv) {
          InvNumber = await exists(
            'invoice_number',
            getValues('invoice_number'),
            'containers',
          );
          if (InvNumber) {
            setError('invoice_number', {
              type: 'custom',
              message: 'Invoice Number exist try another one!',
            });
          }
          if (watchedFieldValueInv == '' || watchedFieldValueInv == null) {
            clearErrors('invoice_number');
          }
        } else {
          clearErrors('invoice_number');
        }

        const isValid = await trigger([
          'booking_id',
          'shipping_document_id',
          'loading_date',
        ]);
        if (allowTochange) {
          setOpen(allowTochange ? true : false);
        }
        // if (isYardExist) {
        //   setError('yard_location_id', {
        //     type: 'custom',
        //     message: 'Yard Location is Required!',
        //   });
        // } else {
        //   clearErrors('yard_location_id');
        // }
        // return isValid && !isYardExist && !tnNumber && !InvNumber;
        return (
          // !checkContainerNumberSuffixConfirmation
          isValid &&
          !tnNumber &&
          !InvNumber &&
          !sealNumber &&
          !allowTochange &&
          !checkContainerNumber
        );
      },
    },
    {
      label: 'Shipment Costs/Charges',
      icon: <AttachMoneyIcon />,
      step: <Step3 form={form} />,
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger([]);
        if (isDescriptionEmpty) {
          setError('container_costs', {
            type: 'custom',
            message: 'Description can not be empty!',
          });
        }

        return isValid && !isDescriptionEmpty;
      },
    },
  ];

  useEffect(() => {
    if (selectedItems) {
      setValue('booking_id', selectedItems[0]?.booking_id);
      setValue('seal_number', selectedItems[0]?.seal_number);
      setValue('no_units_load', selectedItems[0]?.no_units_load);
      setValue('company_id', selectedItems[0]?.company_id);
      setValue('yard_location_id', selectedItems[0]?.yard_location_id);
      setValue('container_number', selectedItems[0]?.container_number || null);
      setValue(
        'clearance_invoice_link',
        selectedItems[0]?.clearance_invoice_link,
      );
      setValue('pin_out', selectedItems[0]?.pin_out);
      setValue('ingate', selectedItems[0]?.ingate);
      setValue('loading_date', formatDate(selectedItems[0]?.loading_date));
      setValue('actions', selectedItems[0]?.actions);
      setValue(
        'documentation_instruction',
        selectedItems[0]?.documentation_instruction,
      );
      setValue('loading_instruction', selectedItems[0]?.loading_instruction);
      setValue(
        'invisible_for_customer',
        selectedItems[0]?.invisible_for_customer,
      );
      setValue('of_loading_video', selectedItems[0]?.of_loading_video);
      setValue('of_loading_photo', selectedItems[0]?.of_loading_photo);
      if (!isUpdate) {
        setImageUrl(null);
      }
      if (isUpdate) {
        setImageUrl(getImageSizeUrl(selectedItems[0]?.cover_photo, 250));

        setValue(
          'shipping_document_id',
          selectedItems[0]?.shipping_document_id,
        );
        setValue('booking_suffix', selectedItems[0]?.booking_suffix);
        setValue('aes_itn_number', selectedItems[0]?.aes_itn_number);
        setValue('tracking_contatiner', selectedItems[0]?.tracking_contatiner);
        setValue(
          'container_id_update_date',
          selectedItems[0]?.container_id_update_date,
        );
        setValue(
          'bill_of_loading_number',
          selectedItems[0]?.bill_of_loading_number,
        );
        setValue('seal_number', selectedItems[0]?.seal_number);
        setValue('hs_code', selectedItems[0]?.hs_code);
        setValue('invoice_number', selectedItems[0]?.invoice_number);
        setValue('amount', +selectedItems[0]?.amount);
        setValue('photo_link', selectedItems[0]?.photo_link);
        setValue('pin_in', selectedItems[0]?.pin_in);
        setValue('container_costs', selectedItems[0]?.container_costs);
        setValue('ingate_date', selectedItems[0]?.ingate_date);
        setValue('pull_date', selectedItems[0]?.pull_date);
        setValue('ingate_driver', selectedItems[0]?.inGateDriver?.id);
        setValue('pull_driver', selectedItems[0]?.pullDriver?.id);
        setValue('ingate_driver_bonus', selectedItems[0]?.ingate_driver_bonus);
        setValue('pull_driver_bonus', selectedItems[0]?.pull_driver_bonus);
        setValue('pull_driver_notes', selectedItems[0]?.pull_driver_notes);
        setValue('ingate_driver_notes', selectedItems[0]?.ingate_driver_notes);
        setValue(
          'loaders',
          selectedItems[0]?.loaders?.map((item) => item.id),
        );
        setValue('loader_remark', selectedItems[0]?.loader_remark);
        setValue(
          'load_combination_type',
          selectedItems[0]?.load_combination_type,
        );
        setValue('title_status', selectedItems[0]?.title_status);
        setValue('aes_status', selectedItems[0]?.aes_status);
        setValue(
          'shipment_type_approved',
          selectedItems[0]?.shipment_type_approved,
        );
        setValue('aes_filling_link', selectedItems[0]?.aes_filling_link);
        setValue('container_charges', selectedItems[0]?.container_charges);
      }
    }
  }, [selectedItems, isUpdate]);

  useEffect(() => {
    setFile(null);
    setImageUrl(null);
  }, [show]);

  const [imageUrl, setImageUrl] = useState(null);
  const [imageLoading, setImageLoading] = useState(false);
  const [file, setFile] = useState(null);

  const uploadFile = async (file, itemId) => {
    try {
      const formData = new FormData();
      formData.append('cover', file);
      formData.append('id', itemId);
      await axios.post('containers/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    } catch (e) {}
  };

  const onSelectImage = async (file) => {
    setFile(file);
  };

  const onRemoveImage = async () => {
    try {
      setImageLoading(true);
      setFile(null);
      if (selectedItems[0]?.cover_photo) {
        await axios.delete(`containers/image/${selectedItems[0].id}`);
        setImageUrl(null);
      }
      setImageLoading(false);
    } catch (error) {
      setImageLoading(false);
      setImageUrl(null);
    }
  };

  return (
    <>
      <ContainerNumberConfrimationDialog
        open={checkContainerNumberDialog}
        setOpen={setCheckContainerNumberDialog}
        setCheckContainerNumber={setCheckContainerNumber}
        setCheckContainerNumberDone={setCheckContainerNumberDone}
      />
      <ContainerNumberSuffixConfirmation
        open={checkContainerNumberSuffixDialog}
        setOpen={setcheckContainerNumberSuffixDialog}
        setcheckContainerNumberSuffixConfirmation={
          setcheckContainerNumberSuffixConfirmation
        }
        form={form}
        container_number={container_num}
      />
      {hideModal ? (
        <form>
          <CStepperWithOutModal
            steps={steps}
            form={form}
            submit={handleSubmit(submit)}
            done={isDone}
            setDone={setIsDone}
            title={isUpdate ? 'Update Shipment' : 'Create Shipment'}
            isUpdate={isUpdate}
            hasQuickSave={isUpdate ? true : false}
            sidbarTop={
              <CoverPhoto
                onRemoveImage={onRemoveImage}
                onSelectImage={onSelectImage}
                prevUrl={imageUrl}
                loading={imageLoading}
              />
            }
          />
        </form>
      ) : (
        <form>
          <CStepper
            loadingButton={loadingButton}
            disableButton={isInstractionLoading}
            loading={loading}
            setSelectedItems={setSelectedItems}
            show={show}
            setShow={setShow}
            steps={steps}
            form={form}
            submit={handleSubmit(submit)}
            done={isDone}
            setDone={setIsDone}
            title={
              isDup
                ? 'Duplicate Shipment'
                : isUpdate
                  ? 'Update Shipment'
                  : `Create ${load ? 'Load' : 'Shipment'}`
            }
            isUpdate={isUpdate}
            hasQuickSave={true}
            sidbarTop={
              <CoverPhoto
                onRemoveImage={onRemoveImage}
                onSelectImage={onSelectImage}
                prevUrl={imageUrl}
                loading={imageLoading}
              />
            }
          />
        </form>
      )}
    </>
  );
};
