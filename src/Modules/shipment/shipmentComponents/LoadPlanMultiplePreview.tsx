import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import LoadPlanAttachments from './LoadPlanAttachments';
import LoadPlanPreviewTemplate from './LoadPlanPreviewTemplate';
import QuillEditor from '@/components/mainComponents/MuiEditor';

function LoadPlanMultiplePreview({
  showPreview,
  setShowPreview,
  selectedItems,
  updateData,
}) {
  const [submitingLoadPlan, setSubmitingLoadPlan] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadPlanData, setLoadPlanData] = useState([]);

  const schema = z.object({
    subject: z.string({
      required_error: 'Subject is required',
    }),
    description: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
    emails: z.string().email().array().nonempty(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      description: '',
      emails: [],
    },
  });
  useEffect(() => {
    if (showPreview) {
      fetchDetails();
    }
  }, [showPreview]);

  const fetchDetails = async () => {
    try {
      setLoading(true);
      const ids = selectedItems.map((row) => row.id);
      const { data } = await axios.get(`containers/multiple/${ids}`);
      setLoadPlanData(data.data);
      form.setValue('emails', data?.data[0]?.yards_location?.emails ?? []);
      form.setValue(
        'subject',
        'New Load Plan To ' + data?.data[0]?.bookings?.destinations?.name,
      );
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const sendLoadPlan = async (data) => {
    try {
      setSubmitingLoadPlan(true);
      const formData = new FormData();
      for (var i = 0; i < attachments.length; i++) {
        formData.append('files', attachments[i]);
      }
      formData.append('subject', data.subject);
      formData.append('description', data.description);
      formData.append('emails', data.emails);
      formData.append(
        'ids',
        JSON.stringify(selectedItems.map((row) => row.id)),
      );
      await axios.post('containers/send-loading-plan', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      updateData();
      toast.success('Load plan send successfully!');
      closeForm();
    } catch (error) {
      console.log(error);
    }
    setSubmitingLoadPlan(false);
  };

  const closeForm = () => {
    setSubmitingLoadPlan(false);
    form.reset();
    setShowPreview(false);
    setAttachments([]);
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 1100,
    bgcolor: 'background.paper',
  };
  return (
    <Modal open={showPreview}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Send Load Plan</Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={() => setShowPreview(false)}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendLoadPlan)}>
          <CardContent style={{ height: '80vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent>
                <Box>
                  <Grid container spacing={2} sx={{ pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="emails"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) =>
                                field.onChange(newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="To"
                                  placeholder="type email and press enter"
                                  size="small"
                                  error={invalid}
                                  helperText={error?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => (
                          <TextField
                            size="small"
                            error={error ? true : false}
                            id="subject"
                            value={field.value}
                            label="Subject"
                            fullWidth
                            variant="outlined"
                            onChange={(value) =>
                              field.onChange(value.target.value)
                            }
                            helperText={error?.message}
                          />
                        )}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="description" // Unique name for the field
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => field.onChange(value)}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <LoadPlanAttachments
                        attachments={attachments}
                        setAttachments={setAttachments}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      {loading ? (
                        <CircularProgress />
                      ) : (
                        <LoadPlanPreviewTemplate loads={loadPlanData} />
                      )}
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={() => setShowPreview(false)}
            >
              Cancel
            </Button>

            <Button
              loading={submitingLoadPlan}
              size="small"
              variant="contained"
              type="submit"
            >
              Send
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default LoadPlanMultiplePreview;
