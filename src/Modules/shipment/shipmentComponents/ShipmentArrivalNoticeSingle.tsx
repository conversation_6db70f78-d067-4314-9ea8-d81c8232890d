import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useEffect, useState } from 'react';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import UpdateEtaAndEtd from './UpdateEta&Etd';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import moment from 'moment';

const ShipmentArrivalNoticeSingle = ({
  selectedContainer,
  setSelectedContainer,
  onRecordManager,
}) => {
  ////// states

  const [updateDate, setUpdateDate] = useState(false);
  const [selectedItemsForUpdate, setSelectedItemsForUpdate] = useState([]);
  const [_forceReload, setForceReload] = useState(false);
  const [updateFreeDaysItem, setUpdateFreeDaysItem] = useState(null);
  const [freeDaysLoading, setFreeDaysLoading] = useState(false);
  const [donwloadLoading, setDownloadLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  //////////////// functions
  const closeForm = () => {
    setSelectedContainer(null);
  };

  const schema = z.object({
    subject: z.string({
      required_error: 'Subject is required',
    }),
    description: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
    free_days: z.number().nullable(),
    cc: z.array(z.string().email()).nullable(),
    emails: z.array(z.string().email()).nullable(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      description: `<p>In the meantime, For release issues you may keep contact with this group to get it soon to avoid any delay "the payment must be cleared" In addition, once you get the release from our team for clearance procedure you can email to <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><strong><EMAIL></strong></a> to proceed with further clearance.</p><p>Note: Also 7 days free assigned from the shipping line for your container that is attached in the below screenshot</p>`,
      free_days: 0,
      cc: [],
      emails: [],
    },
  });

  const sendArrivalNotice = async (values) => {
    try {
      if (freeDaysLoading) {
        toast.error('Wait untill free days are updated!');
        return;
      }

      setSubmitLoading(true);

      let { data } = await axios.post('containers/arrival-notices', {
        ...values,
        free_days: [{ id: selectedContainer?.id, value: values.free_days }],
        container_ids: [selectedContainer?.id],
        company_id: selectedContainer?.company_id,
      });
      if (data?.result) toast.success('Arrival Notice sent successfully!');
      else {
        toast.error(data?.message);
      }
      setSubmitLoading(false);
      setSelectedContainer(null);
      onRecordManager(
        { ...selectedContainer, arrival_notice_sent_at: new Date() },
        'update',
      );
    } catch (error) {
      setSubmitLoading(false);
    }
  };
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number,
  ) => {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (...args: Parameters<T>): void => {
      clearTimeout(timeoutId);

      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };
  const updateFreeDays = async (booking_id, value) => {
    try {
      setFreeDaysLoading(true);
      const { data } = await axios.patch(
        `bookings/updateFreeDays/${booking_id}`,
        {
          free_days: value,
        },
      );
      if (data.result === true) {
        setSelectedContainer((prev) =>
          prev.map((item) => {
            if (item?.bookings?.id == booking_id) {
              item.bookings.free_days = value;
            }
            return item;
          }),
        );
        toast.success('Record updated successfully!');
        setFreeDaysLoading(false);
      }
    } catch (error) {
      setFreeDaysLoading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setDownloadLoading(true);
      const values = form.getValues();
      const response = await axios.post(
        `containers/arrival-notices/get-pdf`,
        {
          ...values,
          free_days: [{ id: selectedContainer?.id, value: values.free_days }],
          container_ids: [selectedContainer?.id],
          company_id: selectedContainer?.company_id,
        },
        {
          responseType: 'blob',
        },
      );
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = `Arrival Notice (${
        selectedContainer?.companies?.name
      }) ${moment(new Date()).format('YYYY-MM-DD')}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setDownloadLoading(false);
    } catch (err) {
      setDownloadLoading(false);
    }
  };

  //////////////// use effects

  useEffect(() => {
    form.setValue('free_days', selectedContainer?.bookings.free_days);
    setForceReload((prev) => !prev);

    form.setValue(
      'subject',
      `${selectedContainer?.companies?.name} Arrival Notice for CNT# ` +
        selectedContainer?.container_number,
    );

    if (
      selectedContainer &&
      selectedContainer?.companies?.customers.length > 0
    ) {
      const customer = selectedContainer.companies.customers[0];
      const primaryEmail = customer.loginable.email;
      const secondaryEmail = customer.secondary_email || ''; // Set secondary email, or empty string if it's null
      const concatenatedEmails = [];
      concatenatedEmails.push(primaryEmail);
      if (secondaryEmail) {
        concatenatedEmails.push(secondaryEmail);
      }
      form.setValue('emails', concatenatedEmails);
    }

    if (selectedContainer?.company_id === 813) {
      form.setValue('cc', [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]);
    }
  }, [selectedContainer]);

  //////////////////////////////////// variables
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: 1200,
    minWidth: '60%',
    bgcolor: 'background.paper',
  };
  const debouncedUpdateFreeDays = debounce(updateFreeDays, 1000);

  return (
    <Modal open={selectedContainer != null}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">
            Arrival Notice ({selectedContainer?.companies?.name}) for container
            ({selectedContainer?.container_number})
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={closeForm}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendArrivalNotice)}>
          <CardContent style={{ height: '70vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="subject"
                              value={field.value}
                              label="Subject"
                              fullWidth
                              variant="outlined"
                              onChange={(value) =>
                                field.onChange(value.target.value)
                              }
                              helperText={error?.message}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="emails"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) => {
                                field.onChange(newValue);
                              }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Emails"
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="cc"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) => {
                                field.onChange(newValue);
                              }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Cc "
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="description"
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                  </Grid>
                  <UpdateEtaAndEtd
                    dateType={'eta'}
                    onDeny={setUpdateDate}
                    open={updateDate}
                    selectedItems={selectedItemsForUpdate}
                    setSelectedItems={setSelectedItemsForUpdate}
                    setTableRecords={() => {}}
                    tableRecords={[]}
                    setForceReload={() => {}}
                  />
                  <Box sx={{ mt: 3, mb: 1 }}>
                    <Typography sx={{ fontSize: '14px', mb: 0.2 }}>
                      Containers
                    </Typography>
                    <Grid container>
                      <Grid
                        display="flex"
                        alignItems="center"
                        size={{
                          xs: 12,
                          md: 6,
                          lg: 4,
                        }}
                      >
                        <Box width="50%">Free Days :</Box>
                        <Box>
                          <Box
                            sx={{
                              cursor: 'pointer',
                              color: '#768aff',
                              width: 100,
                              display: 'inline-block',
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              setUpdateFreeDaysItem(selectedContainer);
                              setForceReload((prev) => !prev);
                            }}
                          >
                            <style jsx global>
                              {`
                                .arrival-notice-free-days
                                  input[type='number']::-webkit-inner-spin-button,
                                .arrival-notice-free-days
                                  input[type='number']::-webkit-outer-spin-button {
                                  -webkit-appearance: none;
                                  margin: 0;
                                  text-align: center;
                                }
                              `}
                            </style>
                            {updateFreeDaysItem != null &&
                            updateFreeDaysItem.id == selectedContainer?.id ? (
                              <Controller
                                name={`free_days`}
                                control={form.control}
                                render={({ field, fieldState: { error } }) => {
                                  return (
                                    <TextField
                                      sx={{
                                        '& input': {
                                          textAlign: 'center',
                                        },
                                      }}
                                      className="arrival-notice-free-days"
                                      size="small"
                                      error={error ? true : false}
                                      id={`free_days`}
                                      value={field.value}
                                      label="Free days"
                                      fullWidth
                                      variant="outlined"
                                      onChange={(value) => {
                                        debouncedUpdateFreeDays(
                                          selectedContainer?.bookings?.id,
                                          +value.target.value,
                                        );
                                        field.onChange(+value.target.value);
                                      }}
                                      helperText={error?.message}
                                      InputProps={{
                                        endAdornment: (
                                          <InputAdornment position="end">
                                            {freeDaysLoading && (
                                              <CircularProgress size={20} />
                                            )}
                                          </InputAdornment>
                                        ),

                                        // sx: {
                                        //   '-webkit-appearance': 'none',
                                        //   margin: 0,
                                        //   textAlign: 'center !important',
                                        // },
                                      }}
                                      type="number"
                                    />
                                  );
                                }}
                              />
                            ) : (
                              form.getValues(`free_days`)
                            )}
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={closeForm}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              color="info"
              onClick={downloadPdf}
              loading={donwloadLoading}
            >
              Download
            </Button>
            <Button
              size="small"
              variant="contained"
              type="submit"
              loading={submitLoading}
              onClick={() => {}}
            >
              Send
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
};

export default ShipmentArrivalNoticeSingle;
