import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Divider,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import LoadPlanPreviewTemplate from './LoadPlanPreviewTemplate';
import LoadPlanAttachments from './LoadPlanAttachments';
import QuillEditor from '@/components/mainComponents/MuiEditor';

function LoadPlanPreview({
  showPreview,
  setShowPreview,
  loadPlanData,
  updateData,
}) {
  const [submitingLoadPlan, setSubmitingLoadPlan] = useState(false);
  const [attachments, setAttachments] = useState([]);

  const schema = z.object({
    subject: z.string({
      required_error: 'Subject is required',
    }),
    description: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
    emails: z.string().email().array().nonempty(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      description: '',
      emails: [],
    },
  });
  useEffect(() => {
    if (showPreview) {
      form.setValue(
        'subject',
        'New Load Plan To ' + loadPlanData?.bookings?.destinations?.name,
      );
      form.setValue('emails', loadPlanData?.yards_location?.emails ?? []);
    }
  }, [showPreview]);

  const sendLoadPlan = async (data) => {
    try {
      setSubmitingLoadPlan(true);
      const formData = new FormData();
      for (var i = 0; i < attachments.length; i++) {
        formData.append('files', attachments[i]);
      }
      formData.append('subject', data.subject);
      formData.append('description', data.description);
      formData.append('emails', data.emails);
      formData.append('ids', JSON.stringify([loadPlanData.id]));

      await axios.post('containers/send-loading-plan', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      updateData();
      closeForm();
      toast.success('Load plan send successfully!');
    } catch (error) {
      console.log(error);
    }
    setSubmitingLoadPlan(false);
  };
  const closeForm = () => {
    setSubmitingLoadPlan(false);
    form.reset();
    setShowPreview(false);
    setAttachments([]);
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 1000,
    bgcolor: 'background.paper',
  };
  return (
    <Modal open={showPreview}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">
            {loadPlanData.loadplan_sent_at ? 'Resend' : 'Send'} Load Plan
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={() => closeForm()}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendLoadPlan)}>
          <CardContent style={{ height: '70vh', overflowY: 'auto' }}>
            <Card sx={{ my: 1 }} elevation={0}>
              <CardContent>
                <Box>
                  {loadPlanData.loadplan_sent_at && (
                    <>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography style={{ minWidth: 100, paddingRight: 5 }}>
                          Sent By
                        </Typography>
                        <Typography style={{ paddingRight: 5 }}>
                          {loadPlanData?.loadplan_sent_byToUser?.fullname}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography style={{ minWidth: 100, paddingRight: 5 }}>
                          Sent At
                        </Typography>
                        <Typography style={{ paddingRight: 5 }}>
                          {formatDate(
                            loadPlanData.loadplan_sent_at,
                            'YYYY MMMM DD hh:mm A',
                          )}
                        </Typography>
                      </Box>
                    </>
                  )}

                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="emails"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) => {
                                field.onChange(newValue);
                              }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="To"
                                  placeholder="type email and press enter"
                                  size="small"
                                  error={invalid}
                                  helperText={error?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="subject"
                              value={field.value}
                              label="Subject"
                              fullWidth
                              variant="outlined"
                              onChange={(value) =>
                                field.onChange(value.target.value)
                              }
                              helperText={error?.message}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="description" // Unique name for the field
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <LoadPlanAttachments
                        attachments={attachments}
                        setAttachments={setAttachments}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
            <LoadPlanPreviewTemplate loads={[loadPlanData]} />
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={() => setShowPreview(false)}
            >
              Cancel
            </Button>

            <Button
              loading={submitingLoadPlan}
              size="small"
              variant="contained"
              type="submit"
            >
              {loadPlanData.loadplan_sent_at ? 'Re Send' : 'Send'}
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default LoadPlanPreview;
