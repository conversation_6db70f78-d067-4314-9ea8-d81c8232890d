import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { formFormatDate } from '@/configs/configs';
import axios from '@/lib/axios';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { toast } from 'react-toastify';

function UpdateSiCuttOff({
  open,
  onDeny,
  selectedItems,
  tableRecords,
  setSelectedItems,
  setTableRecords,
  setForceReload,
}) {
  const [date, setDate] = useState<any>();
  const [submitting, setSubmitting] = useState(false);

  const updateCuttOff = async () => {
    if (!date) {
      toast.warn('Please make changes to the current date to update the date');
    } else {
      let id = selectedItems[0].bookings?.vessels?.id;
      setSubmitting(true);
      const { data } = await axios.patch(`vessels/updateSiCuttOff/${id}`, {
        date_param: formFormatDate(date),
      });
      if (data.result === true) {
        const updatedRecords = tableRecords.map((record) => {
          if (record?.bookings?.vessels?.id === id) {
            record.bookings.vessels = {
              ...record.bookings.vessels,
              ...data?.data,
            };
          }

          return record;
        });
        setTableRecords(updatedRecords);
        setDate(null);
        setForceReload((value) => !value);
        onDeny(false);
        setSelectedItems([]);
        toast.success('Record updated successfully!');
        setSubmitting(false);
        return true;
      } else {
        setDate(null);
        setSelectedItems([]);
        onDeny(false);
      }
    }
    setSubmitting(false);
  };

  return (
    <AppConfirmDialog
      open={open}
      onDeny={onDeny}
      onConfirm={updateCuttOff}
      submitting={submitting}
      title={
        <>
          <DatePicker
            views={['year', 'month', 'day']}
            label="Update Si Cut Off"
            sx={{ width: '100%' }}
            value={
              date ||
              (!selectedItems
                ? null
                : dayjs(
                    selectedItems[0]?.bookings?.vessels?.si_cut_off &&
                      new Date(selectedItems[0]?.bookings?.vessels?.si_cut_off)
                        .toISOString()
                        .split('T')[0],
                  ).toDate())
            }
            format="yyyy/MM/dd"
            onChange={(e) => setDate(e)}
          />
        </>
      }
      dialogTitle={`Update Si Cutt Off`}
      confirmText={'Update'}
      cancelText="Cancel"
      maxWidth={'sm'}
      onClose={() => {
        onDeny(false);
        setDate(null);
        setSelectedItems([]);
      }}
    />
  );
}

export default UpdateSiCuttOff;
