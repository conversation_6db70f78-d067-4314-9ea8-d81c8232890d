import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  HeaderInfo,
  filterContentShipments,
  printFormContent,
  shipmentSearchFields,
  shipmentTabs,
} from '@/configs/shipment/shipmentHeader';
import { filterContentStatus } from '@/configs/shipment/ShippmentStatusHeader';
import { Box, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { CreateShipment } from './CreateShipment';
import PrintModal from '@/components/mainComponents/cModal/PrintModal';
import { removeUnderScore, removeUnderScore2 } from '@/configs/common';
import { appButton } from '@/configs/shipment/shipmentAppButton';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { CONTAINERS } from '@/configs/leftSideMenu/Permissions';
import ViewSingleShipment from './shipmentComponents/ViewSingleShipment';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { recordManager, getDateDifference } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { toast } from 'react-toastify';
import { useCustomColumnShipment } from './shipmentComponents/ShipmentCustomColumn';
import ShipmentPDFModal from './shipmentComponents/ShipmentPDFModal';
import ShipmentChangeStatus from './shipmentComponents/ShipmentChangeStatus';
import UpdateEtaAndEtd from './shipmentComponents/UpdateEta&Etd';
import AddClearanceInvoiceLink from './shipmentComponents/AddClearanceInvoiceLink';
import SearchBy from '@/components/mainComponents/cComponents/SearchBy';
import AddContainerTransaction from './shipmentComponents/AddContainerTransaction';
import UpdateTitleAndAesStatus from './shipmentComponents/UpdateTitle&AesStatus';
import ShipmentArrivalNoticeSingle from './shipmentComponents/ShipmentArrivalNoticeSingle';
import UpdatePullAndIngateDate from './shipmentComponents/UpdatePull&IngateDate';
import ProfitLossChangeStatus from './ProfitLostReport/ProfitLossChangeStatus';
import UpdatePullAndIngateDriver from './shipmentComponents/UpdatePull&IngateDriver';
import DropZoneModal from '@/components/mainComponents/cModal/DropZoneModal';
import UploadingCard from '@/components/mainComponents/cModal/UploadingCard';
import { useUploadQueue } from '@/contexts/UploadQueueContext';
import UpdateSiCuttOff from './shipmentComponents/UpdateSiCuttOff';
import UpdateLoadingDate from './shipmentComponents/UpdateLoadingDate';
import UpdateBrd from './shipmentComponents/UpdateBrd';
import ChangePendingArriveStatus from '@/configs/shipment/ChangePendingArrivalStatus';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const Shipment = ({
  defaultHeaders,
  activeTab,
  apiUrl,
  fullContainer,
  mixContainer,
}) => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [loading, setLoading] = useState(false);
  const [openPrint, setOpenPrint] = useState(false);
  const [openInvoiceLink, setOpenIvoinceLink] = useState(false);
  const [printForm, setPrintForm] = useState('');
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [title, setTitle] = useState('Shipments');
  const [openConfirm, setOpenConfirm] = useState(false);
  const [updateDate, setUpdateDate] = useState(false);
  const [updateSiCuttOff, setUpdateSiCuttOff] = useState(false);
  const [updateLoadingDate, setUpdateLoadingDate] = useState(false);
  const [updateBrd, setUpdateBrd] = useState(false);
  const [updateDriverDate, setUpdateDriverDate] = useState(false);
  const [updateStatus, setUpdateStatus] = useState(false);
  const [dateType, setDateType] = useState('');
  const [driverDateType, setDriverDateType] = useState('');
  const [view, setView] = useState(false);
  const [viewData, setViewData] = useState({});
  const router = useRouter();
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [printData, setPrintData] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [isDup, setIsDup] = useState(false);
  const [releaseMulti, setReleaseMulti] = useState(false);
  const [openTransaction, setOpenTransaction] = useState(false);
  const [openPlStatusConfirm, setOpenPlStatusConfirm] = useState(false);
  const [openPendingArrival, setOpenPendingArrival] = useState(false);
  const [showUploadImageButton, setShowUploadImage] = useState(false);
  const [singleContainer, setSingleContainer] = useState(null);
  const [deleteReason, setDeleteReason] = useState('');

  const toggleUploadImages = async () =>
    setShowUploadImage(showUploadImageButton != showUploadImageButton);

  const {
    showUploadingCard,
    vehicleIds,
    fetchingRecords,
    setVehicleIds,
    setFetchingRecords,
  } = useUploadQueue();

  const notPaginated = ['pending'];
  let tempFilter = {};
  if (router.query.filterData) {
    //@ts-ignore
    tempFilter = JSON.parse(router.query.filterData);
  }
  const [options, setOptions] = useState(() => {
    const storedPerPage = localStorage.getItem('shipments_perPage');
    const defaultPerPage =
      activeTab === 'all' ||
      activeTab === 'arrived' ||
      activeTab === 'draft_check'
        ? 20
        : 100;

    return {
      page: 1,
      perPage: storedPerPage ? parseInt(storedPerPage) : defaultPerPage,
      filterData: tempFilter,
      tab: activeTab,
      search: '',
      exactMatch: false,
      orderBy: {
        column: 'loading_date',
        order: 'desc',
      },
    };
  });
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState(null);
  const [openDriverModal, setOpenDriverModal] = useState(false);
  const [isIngate, setIsIngate] = useState(false);
  const [forceReload, setForceReload] = useState(false);

  const viewPermissions = {
    draft_check: CONTAINERS?.DRAFT_CHECK_VIEW,
    cbp_inspection: CONTAINERS?.INSPECTION_VIEW,
    pending: CONTAINERS?.PENDING_VIEW,
    at_loading: CONTAINERS.AT_LOADING_VIEW,
    at_the_dock: CONTAINERS?.AT_THE_DOCK_VIEW,
    checked_page: CONTAINERS?.CHECKED_VIEW,
    final_checked: CONTAINERS?.FINAL_CHECKED_VIEW,
    on_the_way: CONTAINERS?.ON_THE_WAY_VIEW,
    arrived: CONTAINERS?.ARRIVED_VIEW,
  };

  let filteredTab = shipmentTabs.filter((item) => {
    const requiredPermission = viewPermissions[item.value];
    return requiredPermission ? perms?.includes(requiredPermission) : true;
  });

  const fetchSpecificRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      let { data } = await axios.get(`${apiUrl}/get_specific_vehicles`, {
        signal: controller.signal,
        params: {
          id: vehicleIds,
        },
      });
      setTableRecords(
        tableRecords.map((container) => {
          const newVehicleData = data?.data.find(
            (item) => item.id === container.id,
          );
          return newVehicleData
            ? { ...container, ...newVehicleData }
            : container;
        }),
      );
      setVehicleIds([]);
      setFetchingRecords(false);
    } catch (error) {
      console.error(error);
    }
  };

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////

  useEffect(() => {
    if (fetchingRecords) {
      fetchSpecificRecords();
    }
  }, [fetchingRecords]);
  useEffect(() => {
    localStorage.setItem('shipments_perPage', options.perPage.toString());
  }, [options.perPage]);

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile, activeTab]);
  //@ts-ignore
  const handleTabChange = (event, newTab) => {
    router.push(`/shipment/${newTab}`);
  };

  useEffect(() => {
    if (activeTab != options.tab && activeTab == router.query.id) {
      setTotalItems(0);
      setOptions({
        page: 1,
        perPage:
          activeTab == 'all' ||
          activeTab == 'arrived' ||
          activeTab == 'draft_check'
            ? 20
            : 100,
        filterData: {},
        tab: activeTab,
        search: '',
        exactMatch: false,
        orderBy: {
          column: 'loading_date',
          order: 'desc',
        },
      });
    }
  }, [router]);

  useEffect(() => {
    if (activeTab == options.tab) {
      fetchRecords();
    }
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
    activeTab,
    router,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let params: any = {
        page: options.page,
        per_page: notPaginated.includes(activeTab) ? 3000 : options.perPage,
        search: options.search,
        searchOptions:
          options.search && localStorage.getItem('searchBy-shipment'),
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };
      if (
        activeTab !== 'all' &&
        activeTab !== 'submit_si' &&
        activeTab !== 'title_archive'
      ) {
        //@ts-ignore
        if (activeTab === 'pending_arrival') {
          params.pending_arrival = true;
        } else {
          params.status = activeTab;
        }
      }

      if (fullContainer || mixContainer) {
        if (mixContainer) {
          params.mix_container = true;
        }
        if (fullContainer) {
          params.full_container = true;
        }
      }

      // if (router.query.point_of_loading) {
      //   //@ts-ignore
      //   params.locationId = +router.query.point_of_loading;
      // }
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`containers/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      setSingleFetchLoading(false);
    }
  };

  const onTabChange = (val) => {
    setSelectedItems([]);
    const tabName = shipmentTabs.find((item) => item.value == val);
    if (tabName.value) {
      tabName.value == 'all'
        ? setTitle('Shippments')
        : setTitle('Shippments  ' + tabName.name);
    } else {
      setTitle('Shippments');
    }

    if (
      val == 'pending' ||
      val == 'at_the_dock' ||
      val == 'at_loading' ||
      val == 'checked_page' ||
      val == 'on_the_way'
    ) {
      router.push(`/shipment/${val}/all`);
    } else {
      router.push(`/shipment/${val}`);
    }
  };

  const getSingleRow = async (id) => {
    try {
      setSelectedItems([]);
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data?.data);
      }
    } catch (error) {
      console.log(error);
      setView(false);
    }
  };

  const handDuplicate = () => {
    const updatedSelectedItems = selectedItems.map((item) => ({
      ...item,
      container_number: '',
      pin_out: null,
      actions: null,
      duplicate: true,
    }));

    setIsUpdate(false);
    setIsDup(true);
    setShowCreate(true);
    setSelectedItems(updatedSelectedItems);
  };

  const onAdd = () => {
    setIsDup(false);
    setIsUpdate(false);
    setShowCreate(true);
  };

  const onEdit = async () => {
    try {
      setIsDup(false);
      setIsUpdate(true);
      setShowCreate(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setSelectedItems([res?.data?.data]);
      }
    } catch (error) {
      setIsDup(false);
      setIsUpdate(false);
      setShowCreate(false);
      console.log(error);
    }
  };

  const checkDeleteLoad = selectedItems.some((element) => {
    return (
      element?.vehicles?.length > 0 ||
      element?.invoices?.length > 0 ||
      element?.clear_logs?.length > 0 ||
      element?.mix_shipping_containers?.length > 0 ||
      element?.mix_shipping_invoices?.length > 0
    );
  });

  const onDelete = () => {
    if (checkDeleteLoad) {
      toast.warn(
        'You can not delete active shipment, some or one of selected shipment is active please review the selected Items! ',
      );
    } else {
      recordManager({
        data: null,
        type: 'delete',
        setTableRecords,
        tableRecords,
        selectedItems,
        setSelectedItems,
        setTotalItems,
        totalItems,
        apiUrl: 'containers',
        deleteReason,
        setDeleteReason,
      });
    }
  };

  const handleChange = async (ids) => {
    try {
      setLoading(true);
      const controller = new AbortController();
      setCancelToken(controller);

      let params: any = {
        page: options.page,
        per_page: notPaginated.includes(activeTab) ? 3000 : options.perPage,
        search: options.search,
        searchOptions:
          options.search && localStorage.getItem('searchBy-shipment'),
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };
      if (
        activeTab !== 'all' &&
        activeTab !== 'submit_si' &&
        activeTab !== 'title_archive'
      ) {
        //@ts-ignore
        if (activeTab === 'pending_arrival') {
          params.pending_arrival = true;
        } else {
          params.status = activeTab;
        }
      }

      if (fullContainer || mixContainer) {
        if (mixContainer) {
          params.mix_container = true;
        }
        if (fullContainer) {
          params.full_container = true;
        }
      }
      axios
        .patch(
          `/containers/changePendingArrivalStatus`,
          { ids },
          {
            params: params, // Adds query parameters
          },
        )
        .then((res) => {
          setSelectedItems([]);
          setLoading(false);
          setOpenPendingArrival(false);
          setTableRecords(res.data.data);
          setTotalItems(res.data.total);
        });
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const handleDeleteReason = (value) => {
    setDeleteReason(value);
  };

  const onRecordManager = (data, type) => {
    recordManager({
      data,
      type,
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl,
    });
  };

  console.log('King', deleteReason);

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////
  const pageName = 'shipment_' + activeTab;

  const mergedDataTableColumn = useCustomColumnShipment({
    getSingleRow: getSingleRow,
    setDriverDateType: setDriverDateType,
    setOpenIvoinceLink: setOpenIvoinceLink,
    setSelectedItems: setSelectedItems,
    setUpdateLoadingDate: setUpdateLoadingDate,
    setUpdateSiCuttOff: setUpdateSiCuttOff,
    setUpdateBrd: setUpdateBrd,
    setUpdateDate: setUpdateDate,
    setDateType: setDateType,
    setUpdateDriverDate: setUpdateDriverDate,
    setUpdateStatus: setUpdateStatus,
    activeTab,
    setSelectedContainer,
    setOpenDriverModal: setOpenDriverModal,
    setIsIngate: setIsIngate,
    setSingleContainer: setSingleContainer,
    onImageUpload: setShowUploadImage,
  });

  const appButtonVar = () =>
    appButton({
      selectedItems,
      setOpenConfirm,
      tab: options.tab,
      mainTab: 'shipments',
      setPrintForm: setPrintForm,
      showAll: false,
      CHECK_TO_ANY: perms?.includes(CONTAINERS?.CHECK_TO_ANY),
      FINAL_CHECK_TO_ANY: perms?.includes(CONTAINERS?.FINAL_CHECK_TO_ANY),
      STATUS: perms?.includes(CONTAINERS?.STATUS),
      showChangePlStatusButton: perms?.includes(
        CONTAINERS?.CHANGE_PL_STATUS_PENDING,
      ),
      showChangePendingArrivalButton:
        activeTab === 'pending_arrival' ? true : false,
      handDuplicate,
      fetchOne,
      setPrintData,
      setOpenPrint,
      setOpenTransaction: setOpenTransaction,
      checkTransactionPerm: perms?.includes(CONTAINERS?.CREATE_TRANSACTION),
      setReleaseMulti,
      releaseMulti,
      setOpenPlStatusConfirm,
      setOpenPendingArrival,
    });

  const RDPrintCustomeTitle = () => {
    let Result = '';
    selectedItems.map((container, index) => {
      Result += container?.container_number;
      Result += index != selectedItems.length - 1 ? '-' : '';
    });
    return Result;
  };

  const checkHeader = () => {
    const excludedIds = ['edit', 'docs', 'photo'];
    const filteredColumns = selectedHeaders.filter(
      (column) => !excludedIds.includes(column.id),
    );
    return filteredColumns;
  };

  const checkAskForETAConfirmation = () => {
    return selectedItems.some((element) => element.status == 'arrived');
  };

  const showExportButton = (currentTab) => {
    const tabPermissions = {
      all: CONTAINERS?.DATA_EXPORT,
      draft_check: CONTAINERS?.DRAFT_CHECK_EXPORT,
      cbp_inspection: CONTAINERS?.INSPECTION_EXPORT,
      final_checked: CONTAINERS?.FINAL_CHECKED_EXPORT,
      on_the_way: CONTAINERS?.ON_THE_WAY_EXPORT,
      arrived: CONTAINERS?.ARRIVED_EXPORT,
      pending_arrival: CONTAINERS?.DATA_EXPORT,
    };

    return perms?.includes(tabPermissions[currentTab]) || false;
  };

  useEffect(() => {
    if (activeTab === 'draft_check') {
    }
  }, [activeTab]);

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(CONTAINERS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title> Ship | {removeUnderScore2(options?.tab)}</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={HeaderInfo().concat({
            href: 'false',
            name: removeUnderScore2(activeTab),
            icon: <></>,
            key: '4',
          })}
        />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          onChange={(_event, val) => onTabChange(val)}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          {filteredTab.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.value}
            />
          ))}
        </Tabs>
        <DataTable3
          forceReload={forceReload}
          PageAction={
            <PageAction
              reasonOnDelete
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              onFilterClick={() => setOpenFilter(true)}
              customActionButtons={appButtonVar}
              showAddButton={
                options.tab == 'all' && perms?.includes(CONTAINERS?.CREATE)
                  ? true
                  : false
              }
              onAdd={onAdd}
              onEdit={onEdit}
              showEditButton={perms?.includes(CONTAINERS?.UPDATE)}
              selectedItems={selectedItems}
              title={title}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteReason={deleteReason}
              onDeleteReason={handleDeleteReason}
              dialogTitle={'Delete Shipments'}
              deleteTitle={`Are you sure you want to delete this item${
                selectedItems.length > 0 ? 's' : ''
              }`}
              showDeleteButton={
                perms?.includes(CONTAINERS?.DELETE) && !checkDeleteLoad
                  ? true
                  : false
              }
              onDelete={onDelete}
              showDownload={showExportButton(options.tab)}
              onDownload={() => setShowDownload(true)}
              onEnter={() => !loading && fetchRecords()}
            />
          }
          hidePagination={notPaginated.includes(activeTab) ? true : false}
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={options.tab}
          tableName="shipments"
          //start custom props
          {...mergedDataTableColumn}
          tt={(item) => (
            <Box>
              {getDateDifference(
                item?.bookings?.vessels?.etd,
                item?.bookings?.eta,
              )}{' '}
              days
            </Box>
          )}
        />
      </Container>
      <ShipmentChangeStatus
        open={openConfirm}
        onDeny={setOpenConfirm}
        fetchRecords={fetchRecords}
        options={options}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
      />

      <CreateShipment
        loading={singleFetchLoading}
        setSelectedItems={setSelectedItems}
        isDup={isDup}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={onRecordManager}
      />

      {showUploadImageButton && selectedItems?.length < 2 && (
        <DropZoneModal
          open={showUploadImageButton}
          setShowUploadImage={toggleUploadImages}
          url={apiUrl}
          route={'containers-images'}
          singleVehicle={singleContainer}
          perms={perms}
        />
      )}

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Shipments"
        content={
          options.tab == 'all'
            ? filterContentShipments()
            : filterContentStatus()
        }
      />
      <PrintModal
        loading={singleFetchLoading}
        open={openPrint}
        toggleOpen={() => {
          setOpenPrint((d) => !d);
          setSelectedItems([]);
        }}
        initialData={printForm == 'release_document' ? printData : printData}
        title={`Print ` + removeUnderScore(printForm)}
        fileName={`Release for Container# ` + RDPrintCustomeTitle()}
        ContentPrint={printFormContent(printForm)}
      />
      <UpdateBrd
        onDeny={setUpdateBrd}
        open={updateBrd}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        setForceReload={setForceReload}
      />
      <UpdateLoadingDate
        onDeny={setUpdateLoadingDate}
        open={updateLoadingDate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />
      <UpdateSiCuttOff
        onDeny={setUpdateSiCuttOff}
        open={updateSiCuttOff}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        setForceReload={setForceReload}
      />
      <UpdateEtaAndEtd
        dateType={dateType}
        onDeny={setUpdateDate}
        open={updateDate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        askForConfirmation={checkAskForETAConfirmation()}
        setForceReload={setForceReload}
      />
      <UpdatePullAndIngateDate
        dateType={driverDateType}
        onDeny={setUpdateDriverDate}
        open={updateDriverDate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />
      <UpdateTitleAndAesStatus
        onDeny={setUpdateStatus}
        open={updateStatus}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />
      <AddClearanceInvoiceLink
        onDeny={setOpenIvoinceLink}
        open={openInvoiceLink}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
      />
      <ViewSingleShipment
        data={viewData}
        setData={setViewData}
        setView={setView}
        show={view}
        loading={singleFetchLoading}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
        SearchBy={SearchBy(shipmentSearchFields, 'shipment')}
        showSearchBy={true}
      ></ColumnDialog>
      <ShipmentPDFModal
        showDownload={showDownload}
        title={title}
        selectedHeaders={checkHeader()}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        options={options}
        apiUrl={apiUrl}
        totoalItems={totalItems}
        selectedItems={selectedItems}
      />
      <AddContainerTransaction
        fetchRecords={fetchRecords}
        openConfirm={openTransaction}
        selectedItems={selectedItems}
        setOpenConfirm={setOpenTransaction}
        setSelectedItems={setSelectedItems}
      />
      <ShipmentArrivalNoticeSingle
        selectedContainer={selectedContainer}
        setSelectedContainer={setSelectedContainer}
        onRecordManager={onRecordManager}
      />
      <ProfitLossChangeStatus
        fetchRecords={fetchRecords}
        openConfirm={openPlStatusConfirm}
        selectedItems={selectedItems}
        setOpenConfirm={setOpenPlStatusConfirm}
        setSelectedItems={setSelectedItems}
        optionsTab={['pending']}
      />
      <ChangePendingArriveStatus
        open={openPendingArrival}
        setOpen={setOpenPendingArrival}
        handleChange={handleChange}
        selectedItems={selectedItems}
        loading={loading}
      />
      <UpdatePullAndIngateDriver
        onDeny={setOpenDriverModal}
        open={openDriverModal}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        tableRecords={tableRecords}
        setTableRecords={setTableRecords}
        isIngate={isIngate}
      />
      {showUploadingCard && <UploadingCard />}
    </>
  );
};
export default Shipment;
