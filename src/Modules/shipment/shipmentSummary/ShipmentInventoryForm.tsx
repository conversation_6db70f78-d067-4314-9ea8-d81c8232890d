import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import QuillEditor from '@/components/mainComponents/MuiEditor';
// import LoadPlanAttachments from '../shipmentComponents/LoadPlanAttachments';
import InventoryPreviewTemplate from './InventoryPreviewTemplate';
import LoadPlanAttachments from '../shipmentComponents/LoadPlanAttachments';
import { formatDate } from '@/configs/vehicles/configs';

function ShipmentInventoryForm({
  selectedCompany,
  setSelectedCompany,
  filterData,
}) {
  const [sendEmail, setSendEmail] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [fetchingInventories, setFetchingInventories] = useState(false);
  const [inventories, setInventories] = useState([]);
  const [downloading, setDownloading] = useState(false);

  const schema = z.object({
    subject: z.string({
      required_error: 'Subject is required',
    }),
    from: z
      .string({
        required_error: 'From Date is required',
      })
      .nullable(),
    to: z
      .string({
        required_error: 'To Date is required',
      })
      .nullable(),
    description: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: 'Loaded Containers Inventory',
      description: '',
      from: '',
      to: '',
      // emails: []
    },
  });
  useEffect(() => {
    if (selectedCompany) {
      form.setValue('from', filterData.ingate_date?.from ?? '');
      form.setValue('to', filterData.ingate_date?.to ?? '');
      fetchInventoryData(filterData.ingate_date);
    }
  }, [selectedCompany]);

  const sendInventory = async (values) => {
    try {
      setSendEmail(true);
      const formData = new FormData();
      for (var i = 0; i < attachments.length; i++) {
        formData.append('files', attachments[i]);
      }
      formData.append('company_id', selectedCompany.id);
      formData.append('from', values.from);
      formData.append('to', values.to);
      formData.append('subject', values.subject);
      formData.append('description', values.description);
      const { data } = await axios.post('containers/inventories', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (data?.result) toast.success('Inventory send successfully!');
      else {
        toast.error(data?.message);
      }
    } catch (error) {
      console.error(error);
    }
    setSendEmail(false);
  };

  const fetchInventoryData = async (values) => {
    try {
      setFetchingInventories(true);
      const { data } = await axios.get('containers/inventories', {
        params: {
          company_id: selectedCompany.id,
          from: values.from,
          to: values.to,
        },
      });
      setInventories(data);
    } catch (error) {
      console.log(error);
    }
    setFetchingInventories(false);
  };

  const downloadPdf = async () => {
    try {
      setDownloading(true);
      const res = await axios.get('containers/download-inventories', {
        params: {
          company_id: selectedCompany.id,
          from: filterData.ingate_date.from,
          to: filterData.ingate_date.to,
        },
        responseType: 'blob',
      });
      const url = window.URL.createObjectURL(new Blob([res.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = `shipment-inventory - ${selectedCompany?.name}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      toast.error(error);
    }
    setDownloading(false);
  };
  const closeForm = () => {
    setSendEmail(false);
    setSelectedCompany(null);
    setAttachments([]);
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 1200,
    bgcolor: 'background.paper',
  };
  return (
    <Modal open={selectedCompany != null}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">
            Shipment Inventory ({selectedCompany?.name})
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={() => closeForm()}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendInventory)}>
          <CardContent style={{ height: '70vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Typography variant="h6">
                    Ingate Date:{' '}
                    {formatDate(form.getValues('from'), 'MMM Do YYYY')} -{' '}
                    {formatDate(form.getValues('to'), 'MMM Do YYYY')}
                  </Typography>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="subject"
                              value={field.value}
                              label="Subject"
                              fullWidth
                              variant="outlined"
                              onChange={(value) =>
                                field.onChange(value.target.value)
                              }
                              helperText={error?.message}
                            />
                          );
                        }}
                      />
                    </Grid>

                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="description" // Unique name for the field
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>

                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <LoadPlanAttachments
                        attachments={attachments}
                        setAttachments={setAttachments}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
            {fetchingInventories ? (
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress />{' '}
              </Box>
            ) : (
              <InventoryPreviewTemplate items={inventories} />
            )}
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={() => setSelectedCompany(null)}
            >
              Cancel
            </Button>

            <Button
              size="small"
              variant="contained"
              type="button"
              loading={downloading}
              onClick={downloadPdf}
            >
              Download
            </Button>
            <Button
              loading={sendEmail}
              size="small"
              variant="contained"
              type="submit"
            >
              Send Inventory
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default ShipmentInventoryForm;
