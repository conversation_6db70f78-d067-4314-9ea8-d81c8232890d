import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Actions,
  CardContent,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState } from 'react';

function ShipmentArrivalNoticeDaysModal({ show, setShow, submit }) {
  const [submitLoading, setSubmitLoading] = useState(false);

  const sendInventory = async (values) => {
    try {
      setSubmitLoading(true);
      submit(values);
      onSubmitSuccess();
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
    }
  };

  const onSubmitSuccess = () => {
    setShow(false);
  };

  const arrivalNoticeTypes = [
    {
      value: 'all',
      title: 'All',
    },
    {
      value: 'sent',
      title: '<PERSON><PERSON>',
    },
    {
      value: 'not_sent',
      title: 'Not sent',
    },
  ];

  const schema = z.object({
    days: z.number(),
    type: z.enum(['all', 'sent', 'not_sent']),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      days: 10,
      type: 'all',
    },
  });

  const closeForm = () => {
    setShow(false);
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    // maxWidth: 1200,
    // minWidth: '60%',
    bgcolor: 'background.paper',
  };

  return (
    <Modal open={show}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Arrival Notice</Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={closeForm}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendInventory)}>
          <CardContent style={{ overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="days"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="days"
                              value={field.value}
                              label="Eta Days"
                              fullWidth
                              variant="outlined"
                              type="number"
                              onChange={(value) =>
                                field.onChange(parseInt(value.target.value))
                              }
                              helperText={error?.message}
                              InputProps={{ sx: { mb: 1 } }}
                            />
                          );
                        }}
                      />
                      <Controller
                        name="type"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <FormControl sx={{ minWidth: '100%' }} size="small">
                              <InputLabel id="demo-select-small-label">
                                Arrival Notice type
                              </InputLabel>
                              <Select
                                labelId="demo-select-small-label"
                                label="Arrival Notice type"
                                fullWidth
                                variant="outlined"
                                size="small"
                                value={field.value}
                                onChange={(event) =>
                                  field.onChange(event.target.value)
                                }
                                error={error ? true : false}
                              >
                                {arrivalNoticeTypes.map((item, i) => (
                                  <MenuItem key={i} value={item.value}>
                                    {item.title}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          );
                        }}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={closeForm}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              type="submit"
              loading={submitLoading}
              onClick={() => {}}
            >
              OK
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default ShipmentArrivalNoticeDaysModal;
