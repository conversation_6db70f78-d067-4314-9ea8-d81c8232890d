import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { formatDate } from '@/configs/vehicles/configs';
import { handleTracking } from '../shipmentComponents/ShipmentCustomColumn';
import { toast } from 'react-toastify';
import UpdateEtaAndEtd from '../shipmentComponents/UpdateEta&Etd';
import moment from 'moment';

function ShipmentArrivalNoticeForm({
  selectedCompany,
  setSelectedCompany,
  arrivalNoticeData,
}) {
  const [containers, setContainers] = useState([]);
  const [selectedContainers, setsSelectedContainers] = useState([]);
  const [containerLoading, setContainerLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [freeDaysLoading, setFreeDaysLoading] = useState(false);
  const [donwloadLoading, setDownloadLoading] = useState(false);
  const [_eta, setEta] = useState(new Date());

  useEffect(() => {
    getCompanyContainers();
  }, [selectedCompany]);

  useEffect(() => {
    form.setValue(
      'subject',
      `${selectedCompany.name} Arrival Notice for CNT# ` +
        selectedContainers.map((item) => item.container_number).join(', '),
    );
    const descriptionEmail =
      selectedCompany.destination_id == 22
        ? '<EMAIL>'
        : '<EMAIL>';

    form.setValue(
      'description',
      `<p>In the meantime, For release issues you may keep contact with this group to get it soon to avoid any delay "the payment must be cleared" In addition, once you get the release from our team for clearance procedure you can email to <a href="mailto:${descriptionEmail}" rel="noopener noreferrer" target="_blank"><strong>${descriptionEmail}</strong></a> to proceed with further clearance.</p><p>Note: Also 7 days free assigned from the shipping line for your container (s) that are attached in the below screenshot</p>`,
    );
    if (selectedContainers.length > 0) {
      setEta(new Date(selectedContainers[0].bookings?.eta));
    }
  }, [selectedContainers, selectedCompany]);

  const sendInventory = async (values) => {
    try {
      if (selectedContainers.length == 0) {
        toast.error('You must select at least one container!');
        return;
      }
      if (freeDaysLoading) {
        toast.error('Wait untill free days are updated!');
        return;
      }
      const selectedContainerIDs = selectedContainers.map((item) => item.id);
      setSubmitLoading(true);

      let { data } = await axios.post('containers/arrival-notices', {
        ...values,
        free_days: values.free_days.filter((item) =>
          selectedContainerIDs.includes(item.id),
        ),
        container_ids: selectedContainerIDs,
        company_id: selectedCompany.id,
      });
      if (data?.result) toast.success('Arrival Notice sent successfully!');
      else {
        toast.error(data?.message);
      }
      onSubmitSuccess(selectedContainerIDs);
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
    }
  };

  //@ts-ignore
  const downloadPdf = async () => {
    try {
      if (selectedContainers.length == 0) {
        toast.error('You must select at least one container!');
        return;
      }
      setDownloadLoading(true);
      const values = form.getValues();
      const selectedContainerIDs = selectedContainers.map((item) => item.id);
      const response = await axios.post(
        `containers/arrival-notices/get-pdf`,
        {
          ...values,
          free_days: values.free_days.filter((item) =>
            selectedContainerIDs.includes(item.id),
          ),
          container_ids: selectedContainerIDs,
          company_id: selectedCompany.id,
        },
        {
          responseType: 'blob',
        },
      );
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = `Arrival Notice (${selectedCompany?.name}) ${moment(
        new Date(),
      ).format('YYYY-MM-DD')}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setDownloadLoading(false);
    } catch (err) {
      setDownloadLoading(false);
    }
  };

  const onSubmitSuccess = (selectedContainerIDs) => {
    setContainers((prev) =>
      prev.filter((item) => !selectedContainerIDs.includes(item.id)),
    );
    setsSelectedContainers([]);
    setSelectedCompany(null);
  };

  const getCompanyContainers = async () => {
    try {
      setContainerLoading(true);
      const today = new Date();
      const next10Days = new Date(today);
      next10Days.setDate(today.getDate() + arrivalNoticeData.days);
      const formattedToday = today.toISOString().slice(0, 10);
      const formattedNext10Days = next10Days.toISOString().slice(0, 10);
      const filterData = {
        ['bookings.eta']:
          arrivalNoticeData.days >= 0
            ? {
                from: formattedToday,
                to: formattedNext10Days,
              }
            : {
                from: formattedNext10Days,
                to: formattedToday,
              },
        company_id: [selectedCompany.id],
      };
      if (arrivalNoticeData.type == 'sent') {
        filterData['arrival_notice_sent_at'] = {
          not: null,
        };
      } else if (arrivalNoticeData.type == 'not_sent') {
        filterData['arrival_notice_sent_at'] = null;
      }
      let { data } = await axios.get('/containers', {
        params: {
          per_page: 10000,
          filterData: JSON.stringify(filterData),
        },
      });
      setContainers(data.data);
      setContainerLoading(false);
    } catch (error) {
      setContainerLoading(false);
    }
  };

  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number,
  ) => {
    let timeoutId: ReturnType<typeof setTimeout>;

    return (...args: Parameters<T>): void => {
      clearTimeout(timeoutId);

      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  const updateFreeDays = async (booking_id, value) => {
    try {
      setFreeDaysLoading(true);
      const { data } = await axios.patch(
        `bookings/updateFreeDays/${booking_id}`,
        {
          free_days: value,
        },
      );
      if (data.result === true) {
        setContainers((prev) =>
          prev.map((item) => {
            if (
              item?.bookings?.id == data.data?.id ||
              item?.bookings?.parent?.id == data.data?.id
            ) {
              item.bookings.free_days = value;
            }
            return item;
          }),
        );
        toast.success('Record updated successfully!');
        setFreeDaysLoading(false);
      }
    } catch (error) {
      setFreeDaysLoading(false);
    }
  };

  const debouncedUpdateFreeDays = debounce(updateFreeDays, 1000);

  const schema = z.object({
    subject: z.string({
      required_error: 'Subject is required',
    }),
    description: z
      .string({
        required_error: 'Description is required',
      })
      .nullable(),
    free_days: z
      .array(z.object({ id: z.number(), value: z.number() }))
      .nullable(),
    cc: z.array(z.string().email()).nullable(),
    emails: z.array(z.string().email()).nullable(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      description: `<p>In the meantime, For release issues you may keep contact with this group to get it soon to avoid any delay "the payment must be cleared" In addition, once you get the release from our team for clearance procedure you can email to <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><strong><EMAIL></strong></a> to proceed with further clearance.</p><p>Note: Also 7 days free assigned from the shipping line for your container (s) that are attached in the below screenshot</p>`,
      free_days: [],
      cc: [],
      emails: [],
    },
  });

  const closeForm = () => setSelectedCompany(null);

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: 1200,
    minWidth: '60%',
    bgcolor: 'background.paper',
  };

  const [updateDate, setUpdateDate] = useState(false);
  const [selectedItemsForUpdate, setSelectedItemsForUpdate] = useState([]);
  const [forceReload, setForceReload] = useState(false);

  useEffect(() => {
    form.setValue(
      'free_days',
      containers.map((item) => {
        return { id: item.id, value: item?.bookings.free_days };
      }),
    );
    setForceReload((prev) => !prev);

    if (containers.length > 0) {
      if (containers[0].companies.customers.length > 0) {
        const customer = containers[0].companies.customers[0];
        const primaryEmail = customer.loginable.email;
        const secondaryEmail = customer.secondary_email || '';
        const concatenatedEmails = [primaryEmail];
        if (secondaryEmail) concatenatedEmails.push(secondaryEmail);

        form.setValue('emails', concatenatedEmails);
      }

      if (containers[0]?.company_id === 813) {
        form.setValue('cc', [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]);
      }
    }
  }, [containers]);

  const [updateFreeDaysItem, setUpdateFreeDaysItem] = useState(null);
  return (
    <Modal open={selectedCompany != null}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">
            Arrival Notice ({selectedCompany?.name})
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={closeForm}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendInventory)}>
          <CardContent style={{ height: '70vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          return (
                            <TextField
                              size="small"
                              error={error ? true : false}
                              id="subject"
                              value={field.value}
                              label="Subject"
                              fullWidth
                              variant="outlined"
                              onChange={(value) =>
                                field.onChange(value.target.value)
                              }
                              helperText={error?.message}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {containerLoading && (
                                      <CircularProgress size={20} />
                                    )}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="emails"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) =>
                                field.onChange(newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Emails"
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="cc"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) =>
                                field.onChange(newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Cc "
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid
                      size={{
                        xs: 12,
                        md: 12,
                      }}
                    >
                      <Controller
                        name="description"
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                  </Grid>
                  <UpdateEtaAndEtd
                    dateType={'eta'}
                    onDeny={setUpdateDate}
                    open={updateDate}
                    selectedItems={selectedItemsForUpdate}
                    setSelectedItems={setSelectedItemsForUpdate}
                    setTableRecords={setContainers}
                    tableRecords={containers}
                    setForceReload={() => {}}
                  />
                  <Box sx={{ my: 1 }}>
                    <Typography sx={{ fontSize: '14px', mb: 0.2 }}>
                      Containers
                    </Typography>
                    <DataTable3
                      hidePagination={true}
                      height={'auto'}
                      // start default props
                      options={{}}
                      setOptions={() => {}}
                      totalItems={100}
                      loading={false}
                      forceReload={forceReload}
                      items={containers.map((item, index) => {
                        return { ...item, index };
                      })}
                      selectedItems={selectedContainers}
                      setSelectedItems={setsSelectedContainers}
                      headers={[
                        {
                          id: 'booking_number',
                          label: 'Booking #',
                        },
                        {
                          id: 'container_number',
                          label: 'Container #',
                        },
                        {
                          id: 'companies',
                          label: 'Company',
                        },
                        {
                          id: 'port_of_loading',
                          label: 'Port of Loading',
                        },
                        {
                          id: 'destination',
                          label: 'Destination',
                        },
                        {
                          id: 'eta',
                          label: 'ETA',
                        },
                        {
                          id: 'free_days',
                          label: 'Free days',
                        },
                        {
                          id: 'arrival_notice_sent_at',
                          label: 'Arrival Notice sent at',
                          width: 100,
                        },
                        {
                          id: 'tracking_contatiner',
                          label: 'Track',
                        },
                      ]}
                      booking_number={(item) => {
                        return (
                          <>
                            {item?.bookings?.parent
                              ? item?.bookings?.parent.booking_number
                              : item?.bookings?.booking_number}
                          </>
                        );
                      }}
                      companies={(item) => {
                        return <>{item?.companies?.name}</>;
                      }}
                      port_of_loading={(item) => {
                        return <>{item?.bookings?.vessels?.locations?.name}</>;
                      }}
                      destination={(item) => {
                        return <>{item?.bookings?.destinations?.name}</>;
                      }}
                      eta={(item) => (
                        <Box
                          sx={{ cursor: 'pointer', color: '#768aff' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedItemsForUpdate([item]);
                            setUpdateDate(true);
                            // setDateType('eta');
                          }}
                        >
                          {formatDate(item?.bookings?.eta)}
                        </Box>
                      )}
                      free_days={(item) => (
                        <Box
                          sx={{
                            cursor: 'pointer',
                            color: '#768aff',
                            width: 100,
                            display: 'inline-block',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setUpdateFreeDaysItem(item);
                            setForceReload((prev) => !prev);
                          }}
                        >
                          <style jsx global>
                            {`
                              .arrival-notice-free-days
                                input[type='number']::-webkit-inner-spin-button,
                              .arrival-notice-free-days
                                input[type='number']::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                                text-align: center;
                              }
                            `}
                          </style>
                          {updateFreeDaysItem != null &&
                          updateFreeDaysItem.id == item.id ? (
                            <Controller
                              name={`free_days.${item.index}.value`}
                              control={form.control}
                              render={({ field, fieldState: { error } }) => {
                                return (
                                  <TextField
                                    sx={{
                                      '& input': {
                                        textAlign: 'center',
                                      },
                                    }}
                                    className="arrival-notice-free-days"
                                    size="small"
                                    error={error ? true : false}
                                    id={`free_days.${item.index}.value`}
                                    value={field.value}
                                    label="Free days"
                                    fullWidth
                                    variant="outlined"
                                    onChange={(value) => {
                                      debouncedUpdateFreeDays(
                                        item?.bookings?.id,
                                        +value.target.value,
                                      );
                                      field.onChange(+value.target.value);
                                    }}
                                    helperText={error?.message}
                                    InputProps={{
                                      endAdornment: (
                                        <InputAdornment position="end">
                                          {freeDaysLoading && (
                                            <CircularProgress size={20} />
                                          )}
                                        </InputAdornment>
                                      ),

                                      // sx: {
                                      //   '-webkit-appearance': 'none',
                                      //   margin: 0,
                                      //   textAlign: 'center !important',
                                      // },
                                    }}
                                    type="number"
                                  />
                                );
                              }}
                            />
                          ) : (
                            form.getValues(`free_days.${item.index}.value`)
                          )}
                        </Box>
                      )}
                      arrival_notice_sent_at={(item) => {
                        return (
                          <>
                            {item?.arrival_notice_sent_at ? (
                              <Chip
                                size="small"
                                sx={{
                                  fontSize: '10px',
                                  backgroundColor: 'red',
                                  fontWeight: 600,
                                  color: 'white',
                                }}
                                label={formatDate(item?.arrival_notice_sent_at)}
                              />
                            ) : (
                              <Chip
                                size="small"
                                sx={{
                                  fontSize: '10px',
                                  backgroundColor: 'green',
                                  color: 'white',
                                  fontWeight: 600,
                                }}
                                label={'NOT SENT'}
                              />
                            )}
                          </>
                        );
                      }}
                      tracking_contatiner={({ bookings, container_number }) => {
                        return (
                          <Box
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                            sx={{ display: 'flex', justifyContent: 'center' }}
                          >
                            <IconButton
                              href={handleTracking(
                                bookings?.vessels?.steamshiplines?.name,
                                container_number,
                              )}
                              target="_blank"
                              size="small"
                              sx={
                                [
                                  'MAERSK',
                                  'ONE',
                                  'MSC',
                                  'HMM',
                                  'CMA CGM',
                                ].includes(
                                  bookings?.vessels?.steamshiplines?.name,
                                )
                                  ? {
                                      color: '#3f50b5',
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      height: 30,
                                      width: 30,
                                      border: '1px solid #3f50b5',
                                    }
                                  : {
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      color: '#d70505',
                                      height: 30,
                                      width: 30,
                                      border: '1px solid #d70505',
                                    }
                              }
                            >
                              <DirectionsBoatIcon />
                            </IconButton>
                          </Box>
                        );
                      }}
                      //start custom props
                      // {...mergedDataTableColumn}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={closeForm}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              color="info"
              onClick={downloadPdf}
              loading={donwloadLoading}
            >
              Download
            </Button>
            <Button
              size="small"
              variant="contained"
              type="submit"
              loading={submitLoading}
              onClick={() => {}}
            >
              Send
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default ShipmentArrivalNoticeForm;
