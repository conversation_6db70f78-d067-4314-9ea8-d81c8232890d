import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { brd_erd_status, vesselStatus } from '@/configs/booking/vesselHeader';
import { formFormatDate } from '@/configs/configs';
import {
  Box,
  Button,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import axios from '@/lib/axios';

export default function Step1({ form, isUpdate, setTarget }) {
  const [eta, setEta] = useState([]);
  const [pol, setPol] = useState(null);
  const [ssl, setSsl] = useState(null);
  const [locationsId, setLocationsId] = useState();
  const [terminals, setTerminals] = useState([]);
  //@ts-ignore
  const [renderKeyCus, setRenderKeyCus] = useState(-1);
  //@ts-ignore
  const [compErr, setCompErr] = useState(false);
  useEffect(() => {
    if (!isUpdate) setTarget(pol?.id, ssl?.id);
  }, [pol, ssl]);

  useEffect(() => {
    setEta(form.getValues('eta'));
  }, [form.getValues('eta')]);

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'booking_targets',
  });

  const [destinations, setDestinations] = useState([]);

  const fetchDestinations = async () => {
    try {
      const { data } = await axios.get(
        '/autoComplete?column=name&modal=destinations&per_page=100',
      );
      setDestinations(data?.data);
    } catch (error) {}
  };

  const s_array = (data, fieldName) => {
    return (
      data &&
      Object?.values(data).map((value: any) => {
        return {
          id: value?.id,
          label: value?.[fieldName] ? value?.[fieldName] : '',
        };
      })
    );
  };

  const id = form.watch('port_of_loading');

  useEffect(() => {
    setLocationsId(id);
    if (id != undefined && locationsId == id && id != '') {
      form.setValue('terminal_id', undefined);
      getTerminals();
    }
  }, [id, locationsId]);

  const getTerminals = async () => {
    try {
      const res = await axios.get(`terminals/terminalsFind/${locationsId}`);
      if (res.status === 200) {
        setTerminals(s_array(res.data?.data[0]?.terminals, 'name'));
        setRenderKeyCus((prv) => prv + -1);
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchDestinations();
  }, []);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Vessel Details
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.name?.message.length > 0 ? true : false}
            id="name"
            required
            label="Vessel Name"
            fullWidth
            variant="outlined"
            {...form.register('name')}
            helperText={form.errors.name?.message}
          ></TextField>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="etd"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="ETD"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.scac_code?.message.length > 0 ? true : false}
            id="name"
            required
            label="SCAC Code"
            fullWidth
            variant="outlined"
            {...form.register('scac_code')}
            helperText={form.errors.scac_code?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="etd_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="ETD Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={brd_erd_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="si_cut_off"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Si Cut Off"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="w_load"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="w_load"
                type="number"
                label="W Load"
                fullWidth
                value={field.value}
                variant="outlined"
                onChange={(value) => field.onChange(+value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="chasis_no"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="chasis_no"
                type="number"
                defaultValue={field.value}
                label="Chasis No"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(+value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="avg_load"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="avg_load"
                type="number"
                value={field.value}
                label="Avg Load"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(+value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="port_of_loading"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              if (error) setCompErr(true);
              return (
                <AutoComplete
                  url="autoComplete"
                  label="POL (Port Of Loading)"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'locations'}
                  passSelectedOption={setPol}
                />
              );
            }}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="brd"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="BRD"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="terminal_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  url={false}
                  label="Select Terminals"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={terminals}
                  column={''}
                  modal={''}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="brd_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="BRD Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={brd_erd_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="erd"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="ERD"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="erd_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="ERD Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={brd_erd_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="steamshipline_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Steamshipline"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'steamshiplines'}
                passSelectedOption={setSsl}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="vessel_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={vesselStatus}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
      </Grid>
      {isUpdate && eta.length > 0 && (
        <Box component={'hr'} sx={{ my: 2 }}></Box>
      )}
      {isUpdate && eta.length > 0 && (
        <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
          ETA
        </Typography>
      )}
      {isUpdate &&
        eta.length > 0 &&
        eta.map((item, i) => (
          <Controller
            control={form.control}
            name={`eta.${i}.eta`}
            key={i}
            render={({ field, fieldState: { error } }) => {
              return (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label={item.destination_name}
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  sx={{ mb: 2 }}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              );
            }}
          />
        ))}
      <Box component={'hr'} sx={{ my: 2 }}></Box>
      <Typography variant="h5" sx={{ mb: 1.5, mt: 1.5 }}>
        Booking targets
      </Typography>
      {fields.map((item: any, i) => (
        <Box
          sx={{
            py: 2,
            px: 2,
            pr: 6,
            mb: 1,
            border: '1px solid gray',
            borderRadius: '6px',
            position: 'relative',
          }}
          key={item.id}
        >
          <IconButton
            size="small"
            color="error"
            onClick={async () => {
              remove(i);
            }}
            aria-label="delete"
            sx={{ position: 'absolute', right: 10, top: 10 }}
          >
            <CloseIcon />
          </IconButton>
          <Controller
            name={`booking_targets.${i}.destination_id`}
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="POD (Port of Destination)"
                fieldName="destination_id"
                field={field}
                error={error}
                staticOptions={destinations.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                column={'name'}
                modal={'destinations'}
              />
            )}
          />
          <Controller
            name={`booking_targets.${i}.target`}
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error ? true : false}
                value={field.value ?? 0}
                label="Target"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => {
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  );
                }}
                ref={field.ref}
                helperText={error?.message}
                sx={{ mt: 2 }}
              />
            )}
          />
        </Box>
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
        <Button
          variant="contained"
          size="small"
          onClick={() => {
            append({ name: '' });
          }}
        >
          Add Target
        </Button>
      </Box>
    </Box>
  );
}
