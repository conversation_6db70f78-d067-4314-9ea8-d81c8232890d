import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';

import { vessels_status } from '@/configs/booking/vesselHeader';
import { removeUnderScore } from '@/configs/common';
import axios from '@/lib/axios';
import {
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ChangeStatus = ({
  openConfirm,
  setOpenConfirm,
  tab,
  selectedItems,
  apiUrl,
  setSelectedItems,
  fetchRecords,
}) => {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState('');
  const [showPasswordField, setShowPasswordField] = useState(true);

  const changeStatusFun = async () => {
    if (status == '' || status == null) {
      toast.warn('Please select a status from list');
    }
    if (selectedItems.length > 0 && status != null && status != '') {
      try {
        setLoading(true);
        let { data } = await axios.patch(`${apiUrl}/changeVesselStatus`, {
          vesselsIds: selectedItems?.map((item) => item?.id),
          vesselsStatus: status,
        });
        if (data.result == false && data.status == 303) {
          setStatus(null);
          toast.warn(
            'Please review the vessels it may have load at At The Dock or At loading status!',
          );
        }
        if (data.result) {
          const updatedItems = [...selectedItems];
          for (let i = 0; i < updatedItems.length; i++) {
            updatedItems[i].vessel_status = status;
          }
          setSelectedItems([]);
          setStatus(null);
          fetchRecords();
          setOpenConfirm(false);
        }
      } catch (error) {
        console.log(error);
      }
      setLoading(false);
    }
  };

  const changeStatusFun2 = () => {
    if (status == 'archived' && selectedItems.length > 0) {
      if (password == 'Confirm') {
        changeStatusFun();
      } else {
        toast.warn('Please enter the correct text');
      }
    } else if (selectedItems.length > 0 && status != 'archived') {
      changeStatusFun();
    }
  };

  const setStatusFunction = (event: SelectChangeEvent, setStatus) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      setStatus(newValue);
    } else {
      setStatus('');
    }
  };

  useEffect(() => {
    if (status == 'archived') {
      setShowPasswordField(true);
    } else {
      setShowPasswordField(false);
    }
  }, [status]);

  return (
    <AppConfirmDialog
      open={openConfirm}
      onDeny={setOpenConfirm}
      onConfirm={changeStatusFun2}
      submitting={loading}
      title={
        <>
          <FormControl fullWidth size="small">
            <InputLabel id="demo-select-small-label">Status</InputLabel>
            <Select
              labelId="demo-select-small-label"
              id="demo-select-small"
              value={status || ''}
              label="Status"
              onChange={(event) => setStatusFunction(event, setStatus)}
            >
              <MenuItem value={''}>
                <em>None</em>
              </MenuItem>
              {vessels_status.map((item, index) => {
                if (item != tab) {
                  return (
                    <MenuItem key={index} value={item}>
                      {removeUnderScore(item)}
                    </MenuItem>
                  );
                }
              })}
            </Select>
          </FormControl>
          {showPasswordField && (
            <FormControl
              size="small"
              fullWidth
              sx={{ mt: 1 }}
              variant="outlined"
            >
              <InputLabel htmlFor="outlined-adornment-password">
                Please write <b style={{ color: 'red' }}>Confirm</b> to confirm
                the changes
              </InputLabel>
              <OutlinedInput
                id="outlined-adornment-password"
                type={'text'}
                value={password}
                onChange={(event) => setPassword(event.target.value)}
                label="Please write the above text"
              />
            </FormControl>
          )}
        </>
      }
      dialogTitle={'Change Vessels Status'}
      confirmText="Change"
      cancelText="Cancel"
      maxWidth="sm"
    />
  );
};

export default ChangeStatus;
