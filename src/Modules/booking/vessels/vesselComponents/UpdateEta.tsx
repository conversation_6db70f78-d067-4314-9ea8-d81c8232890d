import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { formFormatDate } from '@/configs/configs';
import axios from '@/lib/axios';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
} from '@mui/material';

function UpdateEta({
  open,
  onDeny,
  booking,
  setBooking,
  tableRecords,
  setTableRecords,
}) {
  const [date, setDate] = useState<any>();
  const [askForConfirmation, setAskForConfirmation] = useState(false);
  const [confirmation, setConfirmation] = useState('');
  const [etaStatus, setEtaStatus] = useState<any>();

  const updateEtdEta = async () => {
    /* if (!date) {
      toast.warn('Please make changes to the current date to update the date');
    } else { */
    if (askForConfirmation && confirmation != 'confirm') {
      toast.warn('Please type the word confirm in the confirmation box');
      return;
    }

    const { data } = await axios.patch(`bookings/updateManyETA`, {
      eta: date || booking?.eta,
      eta_status: etaStatus,
      vessel_id: booking.vessel_id,
      port_of_discharge: booking.port_of_discharge,
    });

    if (data.result === true) {
      const updatedRecords = tableRecords.map((record) => {
        if (record?.id == data?.data[0].vessel_id) {
          record.bookings = data?.data;
        }
        return record;
      });
      setTableRecords(updatedRecords);
      setDate(null);
      onDeny(false);
      setBooking(false);
      toast.success('Record updated successfully!');
      return true;
    } else {
      setDate(null);
      setBooking(false);
      onDeny(false);
    }
    //}
  };

  const checkContainers = async () => {
    try {
      const { data } = await axios.get(`bookings/checkArrivedContainers`, {
        params: {
          vessel_id: booking.vessel_id,
          port_of_discharge: booking.port_of_discharge,
        },
      });
      setAskForConfirmation(data);
    } catch (error) {}
  };

  useEffect(() => {
    if (open) {
      checkContainers();
    }
  }, [open]);

  return (
    <AppConfirmDialog
      open={open}
      onDeny={onDeny}
      onConfirm={updateEtdEta}
      title={
        <>
          <DatePicker
            views={['year', 'month', 'day']}
            label={`Update ETA`}
            sx={{ width: '100%' }}
            value={
              (date ?? booking?.eta ?? null)
                ? dayjs(date ?? booking?.eta).toDate()
                : null
            }
            format="yyyy/MM/dd"
            onChange={(e) => setDate(formFormatDate(e))}
          />

          <FormControl sx={{ width: '100%', marginTop: 2 }}>
            <InputLabel>Eta Status</InputLabel>
            <Select
              value={etaStatus || booking?.eta_status}
              onChange={(e) => setEtaStatus(e.target.value)}
              label="Eta Status"
            >
              <MenuItem value="actual">Actual</MenuItem>
              <MenuItem value="estimate">Estimate</MenuItem>
            </Select>
          </FormControl>
          {askForConfirmation ? (
            <div style={{ paddingTop: '10px' }}>
              <p style={{ marginBottom: '4px' }}>
                Type the word <b>"confirm"</b> to update the ETA
              </p>
              <TextField
                size="small"
                label="Confirmation box"
                fullWidth
                variant="outlined"
                value={confirmation}
                onChange={(value) => setConfirmation(value.target.value)}
              ></TextField>
            </div>
          ) : (
            <></>
          )}
        </>
      }
      dialogTitle={`Update ETA`}
      confirmText={'Update'}
      cancelText="Cancel"
      maxWidth={'sm'}
      onClose={() => {
        onDeny(false);
        setBooking(false);
      }}
    />
  );
}

export default UpdateEta;
