import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { bookings_status } from '@/configs/booking/bookingHeader';
import { removeUnderScore } from '@/configs/common';
import axios from '@/lib/axios';
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import React, { useState } from 'react';
import { toast } from 'react-toastify';

import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';

const ChangeStatus = ({
  openConfirm,
  setOpenConfirm,
  fetchRecords,
  apiUrl,
  tab,
  selectedItems,
  setSelectedItems,
}) => {
  const [status, setStatus] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [is_cancelled_with_charges, setIs_cancelled_with_charges] =
    useState('');

  const checkIsCancelledWithCharge =
    status == 'cancelled' && selectedItems.some((item) => item?.type == 'SPOT');

  const changeStatus = async () => {
    if (status == '' || status == null) {
      toast.warn('Please select a status from list');
    }

    if (
      checkIsCancelledWithCharge &&
      (is_cancelled_with_charges == '' || is_cancelled_with_charges == null)
    ) {
      toast.warn(
        'Please indicate whether it is cancelled with charges or without charges.',
      );
      return '';
    }

    if (selectedItems.length > 0 && status != null && status != '') {
      try {
        const checkLoad = selectedItems.every(
          (row) =>
            (row.containerLoads?.containers !== 0 ||
              row.containerLoads?.loads !== 0) &&
            status === 'cancelled',
        );

        if (checkLoad) {
          toast.warn("Can't cancel bookings which has load and container!");
          return;
        }
        setSubmitting(true);
        let { data } = await axios.patch(`${apiUrl}/changeBookingStatus`, {
          bookingIds: selectedItems?.map((item) => item?.id),
          status: status,
          is_cancelled_with_charges: is_cancelled_with_charges,
        });
        if (data.result) {
          setSelectedItems([]);
          setStatus('');
          fetchRecords();
          setOpenConfirm(false);
          toast.success(data?.message);
          setIs_cancelled_with_charges('');
        }
      } catch (error) {
        setStatus('');
      }
      setSubmitting(false);
    }
  };

  const setStatusFunction = (event: SelectChangeEvent, setStatus) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      setStatus(newValue);
    } else {
      setStatus('');
    }
  };
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIs_cancelled_with_charges((event.target as HTMLInputElement).value);
  };
  return (
    <AppConfirmDialog
      open={openConfirm}
      onDeny={() => {
        setStatus('');
        setOpenConfirm(false);
      }}
      onConfirm={changeStatus}
      submitting={submitting}
      title={
        <>
          <FormControl fullWidth size="small">
            <InputLabel id="demo-select-small-label">Status</InputLabel>
            <Select
              labelId="demo-select-small-label"
              id="demo-select-small"
              value={status || ''}
              label="Status"
              onChange={(event) => setStatusFunction(event, setStatus)}
            >
              <MenuItem value={''}>
                <em>None</em>
              </MenuItem>
              {bookings_status.map((item, index) => {
                if (item != tab) {
                  return (
                    <MenuItem key={index} value={item}>
                      {removeUnderScore(item)}
                    </MenuItem>
                  );
                }
              })}
            </Select>
          </FormControl>
          <FormControl fullWidth size="small">
            {checkIsCancelledWithCharge ? (
              <>
                <br />
                <RadioGroup
                  aria-labelledby="demo-controlled-radio-buttons-group"
                  name="controlled-radio-buttons-group"
                  value={is_cancelled_with_charges}
                  onChange={handleChange}
                >
                  <FormControlLabel
                    value="with_charges"
                    control={<Radio />}
                    label="With charges"
                  />
                  <FormControlLabel
                    value="without_charges"
                    control={<Radio />}
                    label="Without charges"
                  />
                </RadioGroup>
              </>
            ) : (
              ''
            )}
          </FormControl>
        </>
      }
      dialogTitle={'Change Bookings Status'}
      confirmText="Change"
      cancelText="Cancel"
      maxWidth="sm"
    />
  );
};

export default ChangeStatus;
