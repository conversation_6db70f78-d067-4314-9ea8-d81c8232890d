import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  Alert,
  Autocomplete,
  Box,
  Chip,
  Grid,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { Controller } from 'react-hook-form';
import {
  Bookings_size,
  bookings_party,
  bookings_type,
  bookings_si,
  eta_status,
} from '@/configs/booking/bookingHeader';
import { formFormatDate } from '@/configs/configs';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';

export default function Step1({
  isUpdate,
  form,
  props,
  askForConfirmation = false,
  setIsRated,
  isRated,
}) {
  useEffect(() => {
    form.setValue('si', bookings_si?.[0]);
  }, [bookings_si]);

  const type = form.watch('type');
  const point_of_destination_id = form.watch('port_of_discharge');
  const vessel_id = form.watch('vessel_id');

  const size = form.watch('size').split(' ')[0];
  const [cancelToken, setCancelToken] = useState(null);

  useEffect(() => {
    if (
      type == 'REGULAR' &&
      point_of_destination_id &&
      vessel_id &&
      (size == '40' || size == '45')
    ) {
      const fetchCost = async () => {
        try {
          if (cancelToken) {
            await cancelToken.abort();
          }
          const controller = new AbortController();
          setCancelToken(controller);

          const { data } = await axios.get(
            `shiplineFreightRates/getRateForBooking`,
            {
              signal: controller.signal,
              params: {
                vessel_id,
                point_of_destination_id,
                container_type: `hc${size}`,
              },
            },
          );

          if (data.result) {
            let bookingCost = 0;
            await data?.data?.freight_rates?.map((frate) =>
              frate?.rate_types?.map((rate) => {
                bookingCost += rate?.rate_value;
              }),
            );
            form.setValue('cost', bookingCost ?? 0);
            setIsRated(bookingCost !== 0);
          } else setIsRated(data?.result);
        } catch (error) {
          console.log(error);
        }
      };
      fetchCost();
    }
  }, [type, point_of_destination_id, vessel_id, size]);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Booking Details
      </Typography>
      <Grid container spacing={2}>
        <Grid size={12}>
          <Controller
            name="vessel_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                disableAutoComplete={props.disableAutoComplete}
                url="autoComplete/autoCompleteMulti"
                label="Vessels"
                fieldName={['name', 'steamshiplines.name', 'locations.name']}
                // fieldName={'name'}
                field={field}
                error={error}
                staticOptions={false}
                column={['name', 'locations', 'steamshiplines']}
                // column={'name'}
                modal={'vessels'}
                pol={form?.selectedItems?.vessels?.port_of_loading}
              />
            )}
          />
        </Grid>

        {isUpdate && (
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              size="small"
              error={
                form.errors.booking_number?.message.length > 0 ? true : false
              }
              id="booking_number"
              label="Booking Number"
              fullWidth
              variant="outlined"
              {...form.register('booking_number')}
              helperText={form.errors.booking_number?.message}
            />
          </Grid>
        )}

        {!isUpdate && (
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <Controller
              name="booking_numbers"
              control={form.control}
              render={({ field, fieldState: {} }) => (
                <Autocomplete
                  multiple
                  id="tags-filled"
                  options={[]}
                  value={field.value}
                  freeSolo
                  renderTags={(value: readonly string[], getTagProps) =>
                    value.map((option: string, index: number) => (
                      <Chip
                        key={index}
                        variant="outlined"
                        label={option}
                        {...getTagProps({ index })}
                      />
                    ))
                  }
                  onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                      event.stopPropagation(); // Prevent form submission
                    }
                  }}
                  onChange={(_event: any, newValue) => {
                    field.onChange(newValue);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      label="Booking Number"
                      placeholder="Type booking number and press enter..."
                      size="small"
                      error={
                        form.errors.booking_numbers?.message.length > 0
                          ? true
                          : false
                      }
                      helperText={form.errors.booking_numbers?.message}
                    />
                  )}
                />
              )}
            />
          </Grid>
        )}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="qty"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="qty"
                type="number"
                value={field.value}
                label="QTY"
                fullWidth
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="eta"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="ETA"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="cost"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="cost"
                type="number"
                value={field.value}
                label="Cost"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(+value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
          {!isRated && <Alert severity="error">Unrated booking cost</Alert>}
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="eta_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="ETA Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={eta_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="tds_cost"
            control={form.control}
            // defaultValue={defaultValue}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={error?.message.length > 0 ? true : false}
                id="tds_cost"
                type="number"
                value={field.value}
                label="tds_Cost"
                fullWidth
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="port_of_discharge"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="POD (Port of Destination)"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'destinations'}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="size"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Size"
                fieldName=""
                field={field}
                error={error}
                staticOptions={Bookings_size}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="party"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Booking Party"
                fieldName=""
                field={field}
                error={error}
                staticOptions={bookings_party}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="type"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Booking Type"
                fieldName=""
                field={field}
                error={error}
                staticOptions={bookings_type}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="si"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Booking SI"
                fieldName=""
                field={field}
                error={error}
                staticOptions={bookings_si}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        <Grid size={12}>
          <TextField
            error={form.errors.description?.message.length > 0 ? true : false}
            id="description"
            required
            label="Description"
            multiline
            rows={3}
            fullWidth
            variant="outlined"
            {...form.register('description')}
            helperText={form.errors.description?.message}
          />
        </Grid>

        <Grid size={12}>
          {askForConfirmation ? (
            <div style={{ paddingTop: '10px' }}>
              <p style={{ marginBottom: '4px' }}>
                The booking has some containers that are already arrived type
                word <b>"confirm"</b> To update th ETA{' '}
              </p>
              <TextField
                size="small"
                label="Confirmation box"
                fullWidth
                variant="outlined"
                error={
                  form.errors.confirmation?.message.length > 0 ? true : false
                }
                {...form.register('confirmation')}
                helperText={form.errors.confirmation?.message}
              ></TextField>
            </div>
          ) : (
            <></>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
