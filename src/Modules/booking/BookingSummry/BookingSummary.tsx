import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Container, Grid, IconButton, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import { BOOKINGS } from '@/configs/leftSideMenu/Permissions';
import {
  tableColumns,
  SummaryHeaderInfo,
  filterContentBookingSummary,
} from '@/configs/booking/summaryHeader';
import { useLocationContext } from '@/contexts/LocationsContext';
import { useRouter } from 'next/router';
import { CreateVessels } from '../vessels/CreateVessels';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { CreateBookings } from '../bookingCreation/CreateBookings';
import { useCustomColumnBookingSummary } from './BookingSummaryCustomColumn';
import BookingSumPDFModal from './BookingSumPdfModal';
import { removeUnderScore2 } from '@/configs/common';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import ChangeStatus from '../vessels/vesselComponents/ChangeStatus';
import { static_locations, getDateDifference } from '@/configs/configs';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import BookingCalender from '../bookingComponents/BookingCalender';

const BookingSummary = () => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [showDownload, setShowDownload] = useState(false);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [destination, setDestinationData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [apiUrl] = useState('/bookings/summary');
  const [isUpdate, setIsUpdate] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const router = useRouter();
  const [totalData, setTotalData]: any = useState({});
  const [ShowCreateBooking, setShowCreateBooking] = useState(false);
  const [vesselSingleFetchLoading, setVesselSingleFetchLoading] =
    useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 2000000,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const { map }: any = useLocationContext();

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////

  useEffect(() => {
    if (options.tab == router.query.id) {
      setOptions({
        ...options,
        tab: options.tab,
        perPage: 2000000,
        page: 1,
        search: '',
        orderBy: {
          column: 'id',
          order: 'desc',
        },
      });
    }
  }, [router]);

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: tableColumns,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);

  useEffect(() => {
    const allRoute = router.query.id;
    if (allRoute != undefined) {
      setOptions({ ...options, tab: allRoute.toString() });
    }
    if (options.tab == allRoute) {
      fetchRecords();
    }
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    router,
    options.tab,
  ]);

  useEffect(() => {
    if (!showCreate) {
      setSelectedItems([]);
    }
  }, [showCreate]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  //@ts-ignore
  const handleTabChange = (event, newTab) => {
    router.push(`/booking/booking_summary/${newTab}`);
  };

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setDestinationData([]);
      setSelectedItems([]);
      setLoading(true);
      let params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };
      if (options.tab !== 'all') {
        //@ts-ignore
        params.locationId = static_locations?.filter((item) => {
          if (item.name == options.tab) {
            return item.id;
          }
        })[0].id;
      }
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setTableRecords(data.data.data);
      setTotalData(data.data.totalData);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const onAdd = () => {
    setIsUpdate(false);
    setShowCreate(true);
  };

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////

  const pageName = 'Booking Summary';
  const locations = map((item) => ({
    name: item.name,
    key: item.key,
    id: item.id,
    icon: item.icon,
    to: item.to,
  }));

  const mergedDataTableColumn = useCustomColumnBookingSummary({
    destination: destination,
    setDestinationData: setDestinationData,
    setIsUpdate: setIsUpdate,
    setSelectedItems: setSelectedItems,
    setShowCreate: setShowCreate,
    setShowCreateBooking: setShowCreateBooking,
    tableRecords: tableRecords,
    totalData: totalData,
    setVesselSingleFetchLoading: setVesselSingleFetchLoading,
  });

  const customAutionButtons = () => (
    <Box style={{ display: 'flex', alignItems: 'center' }}>
      <AppTooltip key={'changeVesselssStatus'} title={'Change Status'}>
        <IconButton
          color="primary"
          onClick={() => {
            setOpenConfirm(true);
          }}
        >
          <PublishedWithChangesIcon />
        </IconButton>
      </AppTooltip>
    </Box>
  );

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(BOOKINGS?.VIEW_SUMMARY) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>BS | {options?.tab?.replace('_', ' ')}</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={SummaryHeaderInfo().concat({
            href: 'false',
            name: removeUnderScore2(options.tab),
            icon: <></>,
            key: '4',
          })}
        />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          onChange={handleTabChange}
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
          {locations?.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.name}
            />
          ))}
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              onFilterClick={() => setOpenFilter(true)}
              selectedItems={selectedItems}
              title={'Booking Summary'}
              total={totalItems}
              showCustomizeColumn={true}
              customActionButtons={customAutionButtons}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              options={options}
              setOptions={setOptions}
              showDeleteButton={false}
              showAddButton={perms?.includes(BOOKINGS?.CREATE)}
              showEditButton={false}
              dialogTitle={''}
              deleteTitle={''}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={onAdd}
              addButtonTitle={'Add New Vessel'}
            />
          }
          // start default props
          // hideCheckBox
          // onRowClicked
          hidePagination
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders.concat(destination)}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName={'bookingSummary'}
          activeTab={options.tab}
          // end default props
          //start custom props
          {...mergedDataTableColumn}
          forceReload={destination}
          tt={(item) => <Box>{getDateDifference(item.etd, item.eta)} days</Box>}
        />
        {options.tab !== 'all' && (
          <Grid container spacing={2} sx={{ mt: 0, mb: 2 }}>
            <Grid
              size={{
                xs: 12,
                md: 5,
              }}
            >
              <BookingCalender location={options.tab} />
            </Grid>
          </Grid>
        )}
      </Container>

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter In Booking Summary"
        content={filterContentBookingSummary}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={tableColumns}
      ></ColumnDialog>

      <CreateVessels
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={() => {}}
        loading={vesselSingleFetchLoading}
      />
      {ShowCreateBooking && (
        <CreateBookings
          setSelectedItems={setSelectedItems}
          disableAutoComplete
          show={ShowCreateBooking}
          setShow={setShowCreateBooking}
          selectedItems={selectedItems[0] && selectedItems[0].id}
          isUpdate={false}
          addBooking={true}
          recordManager={() => {}}
        />
      )}

      <ChangeStatus
        openConfirm={openConfirm}
        setOpenConfirm={setOpenConfirm}
        tab={'all'}
        selectedItems={selectedItems}
        apiUrl={'/vessels'}
        setSelectedItems={setSelectedItems}
        fetchRecords={() => {}}
      />

      <BookingSumPDFModal
        showDownload={showDownload}
        apiUrl={apiUrl}
        options={options}
        selectedHeaders={selectedHeaders.concat(destination)}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
      />
    </>
  );
};

export default BookingSummary;
