import { useContext, useEffect, useState } from 'react';
import { Box, Container, Grid, Typography } from '@mui/material';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import Head from 'next/head';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import { BOOKINGS } from '@/configs/leftSideMenu/Permissions';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import ModeStandbyIcon from '@mui/icons-material/ModeStandby';
import BarChartDashboardCard from '@/components/mainComponents/BarChartDashboardCard';

function BookingDashboard() {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const [cancelToken, setCancelToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [analytics, setAnalytics] = useState({
    today_instructions: {},
    missed_instructions: {},
    seven_day_instructions: {},
    today_validated_titles: {},
    missed_validated_titles: {},
    containers_tobe_ingate: {},
    missed_containers_tobe_ingate: {},
    vessels_tobe_finalized: {},
    today_date: {},
    seven_day_date: {},
    aes_titles: {},
    missed_aes_titles: {},
    bl_amendment: {},
    amend_si: {},
  });

  const locations = [
    { label: 'All', id: 'all' },
    { label: 'Savannah GA', id: 1 },
    { label: 'Houston TX', id: 2 },
    { label: 'New Jersey NJ', id: 5 },
    { label: 'Los Angeles CA', id: 4 },
    { label: 'Baltimore DM', id: 6 },
    { label: 'Jacksonville FL', id: 9 },
  ];
  useEffect(() => {
    fetchRecords();
  }, []);

  const onItemClick = (item, path, filter = {}, port_of_loading) => {
    let filterData = { ...filter };
    if (item.id != 'all') {
      filterData[port_of_loading] = [item.id];
    }
    window.open(`${path}?filterData=${JSON.stringify(filterData)}`, '_blank');
  };

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setLoading(true);
      const { data } = await axios.get('booking-analytics', {
        signal: controller.signal,
      });
      setAnalytics(data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////
  return perms && !perms?.includes(BOOKINGS?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Booking Dashboard</title>
      </Head>
      <Container
        sx={{
          maxWidth: 'unset !important',
          padding: '0 10px !important',
        }}
      >
        <PageHeader
          breadcrumbs={[
            {
              href: '/booking/dashboard',
              name: 'Dashboard',
              icon: <ModeStandbyIcon sx={{ fontSize: '18px' }} />,
              key: '1',
            },
          ]}
        />
        <Typography variant="h5" fontWeight={600} sx={{ paddingTop: 3 }}>
          Booking Analytics
        </Typography>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Shipping Instructions"
              subtitle="Due Today"
              loading={loading}
              missedData={analytics?.missed_instructions}
              data={analytics?.today_instructions}
              labels={locations}
              onGreen={(item) => {
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: { col_name: 'si', in: ['missing', 'submitted', null] },
                    'vessels.si_cut_off': { equals: new Date() },
                    status: ['up_coming', 'in_process', 'archived'],
                  },
                  'vessels.port_of_loading',
                );
              }}
              onRed={(item) => {
                const today = new Date();
                today.setDate(today.getDate() - 1);
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: { col_name: 'si', in: ['missing', 'submitted', null] },
                    'vessels.si_cut_off': {
                      to: new Date(today).toISOString().split('T')[0],
                    },
                    status: ['up_coming', 'in_process', 'archived'],
                  },
                  'vessels.port_of_loading',
                );
              }}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: { col_name: 'si', in: ['missing', 'submitted', null] },
                    'vessels.si_cut_off': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    status: ['up_coming', 'in_process', 'archived'],
                  },
                  'vessels.port_of_loading',
                )
              }
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Shipping Instructions"
              subtitle="Due Next 7Day"
              loading={loading}
              data={analytics?.seven_day_instructions}
              labels={locations}
              onGreen={(item) => {
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    container_number: { not: null },
                    status: { not: 'arrived' },
                    ingate_date: null,
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onRed={(item) => {
                const today = new Date();
                today.setDate(today.getDate() - 1);
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': {
                      to: new Date(today).toISOString().split('T')[0],
                    },
                    container_number: { not: null },
                    status: { not: 'arrived' },
                    ingate_date: null,
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: { col_name: 'si', in: ['missing', 'submitted', null] },
                    'vessels.si_cut_off': analytics.seven_day_date,
                    status: ['up_coming', 'in_process', 'archived', 'applied'],
                  },
                  'vessels.port_of_loading',
                )
              }
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Validated Titles"
              subtitle="Due Today"
              loading={loading}
              missedData={analytics?.missed_validated_titles}
              data={analytics?.today_validated_titles}
              labels={locations}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': { to: analytics.today_date },
                    OR: { col_name: 'title_status', in: ['submitted', null] },
                    container_number: { not: null },
                  },
                  'bookings.vessels.port_of_loading',
                )
              }
            />
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Containers to be ingate"
              subtitle="Due Today"
              loading={loading}
              data={analytics?.containers_tobe_ingate}
              missedData={analytics?.missed_containers_tobe_ingate}
              labels={locations}
              onGreen={(item) => {
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    container_number: { not: null },
                    status: { not: 'arrived' },
                    ingate_date: null,
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onRed={(item) => {
                const today = new Date();
                today.setDate(today.getDate() - 1);
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': {
                      to: new Date(today).toISOString().split('T')[0],
                    },
                    container_number: { not: null },
                    status: { not: 'arrived' },
                    ingate_date: null,
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.erd': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    container_number: { not: null },
                    status: { not: 'arrived' },
                    ingate_date: null,
                  },
                  'bookings.vessels.port_of_loading',
                )
              }
            />
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Vessels to be finalized"
              subtitle=""
              loading={loading}
              data={analytics?.vessels_tobe_finalized}
              labels={locations}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/booking/vessels/all',
                  {
                    vessel_status: ['to_be_finalized'],
                  },
                  'port_of_loading',
                )
              }
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="AES Filling"
              subtitle="Due Today"
              loading={loading}
              missedData={analytics?.missed_aes_titles}
              data={analytics?.aes_titles}
              labels={locations}
              onGreen={(item) => {
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.si_cut_off': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    aes_status: null,
                    container_number: { not: null },
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onRed={(item) => {
                const today = new Date();
                today.setDate(today.getDate() - 1);
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.si_cut_off': {
                      to: new Date(today).toISOString().split('T')[0],
                    },
                    aes_status: null,
                    container_number: { not: null },
                  },
                  'bookings.vessels.port_of_loading',
                );
              }}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/shipment/all',
                  {
                    'bookings.vessels.si_cut_off': {
                      from: analytics.today_date,
                      to: analytics.today_date,
                    },
                    aes_status: null,
                    container_number: { not: null },
                  },
                  'bookings.vessels.port_of_loading',
                )
              }
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 4,
            }}
          >
            <BarChartDashboardCard
              title="Booking Amendment SI"
              subtitle=""
              loading={loading}
              missedData={analytics?.amend_si}
              data={analytics?.bl_amendment}
              labels={locations}
              onGreen={(item) => {
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: {
                      col_name: 'si',
                      in: ['bl_amendment'],
                    },
                  },
                  'vessels.port_of_loading',
                );
              }}
              onRed={(item) => {
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: {
                      col_name: 'si',
                      in: ['amend_si'],
                    },
                  },
                  'vessels.port_of_loading',
                );
              }}
              onItemClick={(item) =>
                onItemClick(
                  item,
                  '/booking/all',
                  {
                    OR: {
                      col_name: 'si',
                      in: ['amend_si', 'bl_amendment'],
                    },
                  },
                  'vessels.port_of_loading',
                )
              }
            />
          </Grid>
        </Grid>
      </Container>
    </>
  );
}
export default BookingDashboard;
