import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardA<PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  Divider,
  Typography,
} from '@mui/material';
import PrintIcon from '@mui/icons-material/Print';
import { useEffect, useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import { printFormContent } from '@/configs/shipment/shipmentHeader';
import { removeUnderScore } from '@/configs/common';
import axios from '@/lib/axios';
import { LoadingPage } from '@/components/layout/LoadingPage';

const PrintForm = () => {
  const [id, setId] = useState();
  const [title, setTitle] = useState();
  const [items, setItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    //@ts-ignore
    setTitle(queryParams.get('title'));
    //@ts-ignore
    setId(queryParams.get('item'));
  }, []);

  useEffect(() => {
    if (id) {
      setItems([]);
      fetchRecord();
    }
  }, [id, title]);

  const fetchRecord = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      let { data } = await axios.get(`/containers/${id}`);
      setSingleFetchLoading(true);
      setItems(data.data);
      setSingleFetchLoading(false);
    } catch (error) {
      console.error(error);
      setSingleFetchLoading(false);
    }
  };
  const componentRef = useRef<HTMLDivElement>(null);
  const handlePrint = useReactToPrint({ contentRef: componentRef });
  const ContentPrint = printFormContent(title);

  return (
    <Card
      sx={{
        mt: 2,
        borderRadius: 2,
        mx: 'auto',
        maxWidth: {
          xs: '95%',
          // sm: (content.length == 2 ? 400 : 340) * content.length + 'px'
        },

        bgcolor: 'background.paper',
        boxShadow: 24,
        position: 'relative',
      }}
    >
      <CardHeader
        title={
          <Box display="flex" columnGap={3} sx={{ alignItems: 'center' }}>
            <PrintIcon sx={{ fontSize: '30px' }} />
            <Typography variant="h5">
              {`Print ` + removeUnderScore(title)}
            </Typography>
          </Box>
        }
      />
      <Divider />
      {singleFetchLoading ? (
        <LoadingPage
          style={{
            position: 'absolute',
            transform: 'translate(-50%, -50%)',
            top: '50%',
            left: '50%',
          }}
        />
      ) : (
        <CardContent
          sx={{
            maxHeight: { xs: '75vh', md: '85vh' },
            overflowY: 'auto',
            px: 3,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              columnGap: 3,
              rowGap: 3,
              justifyContent: 'space-around',
              flexDirection: { xs: 'column', sm: 'row' },
              minHeight: '29.7cm',
            }}
          >
            <ContentPrint
              ref={componentRef}
              data={title == 'release_document' ? [items] : items}
            />
          </Box>
        </CardContent>
      )}
      <Divider />
      <CardActions sx={{ justifyContent: 'center', mx: 2 }}>
        <Button
          size="small"
          variant="contained"
          onClick={() => {
            handlePrint();
          }}
        >
          Print
        </Button>
        <Button
          size="small"
          variant="contained"
          color="warning"
          onClick={() => window.close()}
        >
          Cancel
        </Button>
      </CardActions>
    </Card>
  );
};

export default PrintForm;
