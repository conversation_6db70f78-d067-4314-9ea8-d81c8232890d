import {
  Autocomplete,
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import { pageTitle, currencies } from './BankAccountsHeaders';
export default function ExchangeRateStep1({ form }) {
  //@ts-ignore
  const [activeCollapes, setActiveCollapes] = useState(0);
  const theme = useTheme();
  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        {pageTitle} Details
      </Typography>
      <Card
        sx={{
          mb: 1,
          backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
        }}
      >
        <CardContent>
          <Grid container spacing={2} sx={{ paddingBottom: '10px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="bank_name"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.bank_name?.message.length > 0 ? true : false
                    }
                    id="bank_name"
                    value={field.value ?? ''}
                    label="Bank Name"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="account_name"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.account_name?.message.length > 0
                        ? true
                        : false
                    }
                    id="account_name"
                    value={field.value ?? ''}
                    label="Account Name"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="account_number"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.account_number?.message.length > 0
                        ? true
                        : false
                    }
                    id="account_number"
                    value={field.value ?? ''}
                    label="Account Number"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="ibn"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.ibn?.message.length > 0 ? true : false}
                    id="ibn"
                    value={field.value ?? ''}
                    label="IBN#"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="bic_code"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.bic_code?.message.length > 0 ? true : false
                    }
                    id="bic_code"
                    value={field.value ?? ''}
                    label="BIC CODE"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`currency`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={field.value}
                    getOptionLabel={(option) => (option ? option : '')}
                    onChange={(_event: any, newValue) => {
                      form.setValue(`currency`, newValue);
                    }}
                    options={currencies}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Currency"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="country"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.country?.message.length > 0 ? true : false
                    }
                    id="country"
                    value={field.value ?? ''}
                    label="Country"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}
