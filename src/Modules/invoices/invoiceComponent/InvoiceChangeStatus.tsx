import { removeUnderScore } from '@/configs/common';
import {
  Box,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from '@mui/material';
import { useState } from 'react';
import InvoiceHelper from './InvoiceHelper';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { toast } from 'react-toastify';
import { countVehicleCosts, getInvoicePayments } from '@/configs/configs';

const InvoiceChangeStatus = ({
  openConfirm,
  actionType = 'approve',
  setActionType,
  setOpenConfirm,
  activeTab,
  selectedItems,
  setSelectedItems,
  fetchRecords,
}) => {
  const invoicesHelper = new InvoiceHelper();
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordField, setShowPasswordField] = useState(false);
  const [password, setPassword] = useState('');
  const [status, setStatus] = useState('');
  const [rejectedReason, setRejectedReason] = useState('');

  const setStatusFunction = (event) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      if (newValue == 'paid') setShowPasswordField(true);
      else setShowPasswordField(false);
      setStatus(newValue);
    } else {
      setStatus('');
      setShowPasswordField(false);
    }
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const setPasswordFunction = (event, setStatus) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      setStatus(newValue);
    } else {
      setStatus('');
    }
  };

  const onPasswordCheck = () => {
    if (password == '') {
      toast.warn('Please fill the password field');
    } else if (status == 'paid' && password == 'Approve@123') {
      changesStatus();
    } else {
      toast.warn('This password you have provide is wrong please try again');
    }
  };

  const checkDueBalance = () => {
    let result = 0;
    selectedItems.forEach((item) => {
      let cost = 0;
      item?.containers?.vehicles?.forEach((vehicle) => {
        cost += countVehicleCosts(
          {
            ...vehicle?.vehicle_costs,
            vehicle_charges: vehicle?.vehicle_charges,
          },
          item.title_charge_visible,
          item.towing_charge_visible,
        );
      });
      result += cost - (getInvoicePayments(item?.payments) + +item?.discount);
    });
    return result <= 1;
  };

  const changesStatus = async () => {
    try {
      if (
        selectedItems.length > 0 &&
        ['pending', 'final_review', 'auto_generated'].includes(activeTab)
      ) {
        const res = await invoicesHelper.invoiceReview(
          {
            invoicesIds: selectedItems?.map((item) => item?.id),
            actionType,
            activeTab,
            ...(rejectedReason && { rejected_reason: rejectedReason }),
          },
          activeTab,
        );
        if (res.result) {
          setSelectedItems([]);
          setStatus('');
          fetchRecords();
          setPassword('');
          setOpenConfirm(false);
          setActionType('approve');
          setRejectedReason('');
          setShowPasswordField(false);
        } else {
          toast.warn(res.message || 'Something went wrong, please try again.');
        }
      } else if (
        (selectedItems.length > 0 && activeTab == 'open' && status == 'paid') ||
        (activeTab == 'past_due' && status == 'paid')
      ) {
        const checkDue = checkDueBalance();
        if (checkDue) {
          const res = await invoicesHelper.changeOpenPaidStatus({
            invoicesIds: selectedItems?.map((item) => item?.id),
          });
          if (res.result) {
            setSelectedItems([]);
            setStatus('');
            setPassword('');
            fetchRecords();
            setOpenConfirm(false);
            setShowPasswordField(false);
          }
        } else {
          toast.warn('Please select invoices with zero due balances.');
          setOpenConfirm(false);
        }
      } else if (selectedItems.length > 0 && status != '' && status != null) {
        const res = await invoicesHelper.changeStatus({
          invoicesIds: selectedItems?.map((item) => item?.id),
          invoiceStatus: status,
        });
        if (res.result) {
          setSelectedItems([]);
          setStatus('');
          setPassword('');
          fetchRecords();
          setOpenConfirm(false);
          setShowPasswordField(false);
        } else if (res.message) {
          toast.warn(res.message);
        }
      }
    } catch (error) {}
  };

  const changeStatusTitle =
    activeTab == 'auto_generated' ? (
      'Do You Want to move this Invoice to Inital Review status'
    ) : activeTab == 'pending' ? (
      'Do You Want to Approve this Invoice'
    ) : activeTab == 'final_review' ? (
      'Do You Want to Final Review this Invoice??'
    ) : (
      <Box sx={{ height: 70 }}>
        <FormControl fullWidth size="small">
          <InputLabel id="demo-select-small-label">Status</InputLabel>
          <Select
            labelId="demo-select-small-label"
            id="demo-select-small"
            value={status}
            label="Status"
            onChange={setStatusFunction}
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {[
              'initial_review',
              'open',
              'past_due',
              'paid',
              'irrecoverable_debt',
            ].map((item, index) => {
              if (item != activeTab) {
                return (
                  <MenuItem key={index} value={item}>
                    {removeUnderScore(item)}
                  </MenuItem>
                );
              }
            })}
          </Select>
        </FormControl>
        {showPasswordField && (
          <FormControl size="small" fullWidth sx={{ mt: 1 }} variant="outlined">
            <InputLabel htmlFor="outlined-adornment-password">
              Password
            </InputLabel>
            <OutlinedInput
              id="outlined-adornment-password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(event) => setPasswordFunction(event, setPassword)}
              endAdornment={
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              }
              label="Password"
            />
          </FormControl>
        )}
      </Box>
    );

  const rejectedReasonComponent = (
    <TextField
      variant="outlined"
      fullWidth
      multiline
      placeholder="Rjection Reason"
      minRows={5}
      size="small"
      name="textarea"
      value={rejectedReason}
      onChange={(event) => {
        setRejectedReason(event.target.value);
      }}
    />
  );

  return (
    <AppConfirmDialog
      open={openConfirm}
      onDeny={() => {
        setStatus('');
        setOpenConfirm(false);
        setActionType('approve');
        setRejectedReason('');
      }}
      onConfirm={showPasswordField ? onPasswordCheck : changesStatus}
      title={
        actionType === 'approve' ? changeStatusTitle : rejectedReasonComponent
      }
      dialogTitle={
        actionType === 'reject'
          ? 'Invoice Rejection'
          : ['auto_generated', 'pending', 'final_review'].includes(activeTab)
            ? 'Invoice Approve'
            : 'Change Status'
      }
      confirmText={actionType === 'approve' ? 'Change' : 'Reject'}
      cancelText="Cancel"
      maxWidth="sm"
      onClose={() => {
        setOpenConfirm(false);
        setActionType('approve');
        setRejectedReason('');
        setStatus('');
        setPassword('');
        setShowPasswordField(false);
      }}
    />
  );
};

export default InvoiceChangeStatus;
