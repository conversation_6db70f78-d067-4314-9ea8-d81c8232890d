import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Container, IconButton, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { INVOICES } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import {
  HeaderInfo,
  filterContentInvoice,
  invoiceSearchFields,
  invoicesTabs,
} from '@/configs/invoices/invoiceHeader';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import InvoiceHelper from './invoiceComponent/InvoiceHelper';
import VerifiedIcon from '@mui/icons-material/Verified';
import CancelIcon from '@mui/icons-material/Cancel';
import PrintIcon from '@mui/icons-material/Print';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import { CreateInvoices } from './CreateInvoice';
import PrintModal from '@/components/mainComponents/cModal/PrintModal';
import InvoicePrint from '@/configs/invoices/InvoicePrint';
import multiInvoicePrint from '@/configs/invoices/MultiInvoicePrint';
import { removeUnderScore2 } from '@/configs/common';
import ViewSingleInvoice from './invoiceComponent/ViewSingleInvoice';
import { formatDate } from '@/configs/vehicles/configs';
import { getInvoicePayments, recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { toast } from 'react-toastify';
import { useCustomColumnInvoice } from './invoiceComponent/InvoiceCustomColumn';
import InvoicePDFModal from './invoiceComponent/InvoicePDFModal';
import InvoiceChangeStatus from './invoiceComponent/InvoiceChangeStatus';
import SearchBy from '@/components/mainComponents/cComponents/SearchBy';

const NewInvoice = ({ defaultHeaders, activeTab }) => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [loading, setLoading] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [actionType, setActionType] = useState('approve');
  const [view, setView] = useState(false);
  const [viewData, setViewData] = useState({});
  const router = useRouter();
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const invoicesHelper = new InvoiceHelper();
  const [openMultiPrint, setOpenMultiPrint] = useState(false);
  const [openPrint, setopenPrint] = useState(false);
  const [deleteReason, setDeleteReason] = useState('');

  let apiUrl = '/invoices';
  if (activeTab === 'mix') {
    apiUrl = '/invoices/mix';
  }

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////
  useEffect(() => {
    if (activeTab == router.query.id) {
      setOptions({
        ...options,
        tab: activeTab,
        page: 1,
        perPage: 20,
        search: '',
        // orderBy: {
        //   column: 'id',
        //   order: 'desc'
        // }
      });
    }
  }, [router]);

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile, options.tab]);

  useEffect(() => {
    if (options.tab == activeTab) {
      const allRoute = router.query.id;
      if (allRoute != undefined) {
        setOptions({ ...options, tab: allRoute.toString() });
      }
      if (options.tab == allRoute) {
        if (router.query.company_id) {
          let filterData2 = {
            company_id: [+router.query.company_id],
            halfcut_status: [router.query.halfcut_status],
          };
          fetchRecords(filterData2);
        } else {
          fetchRecords();
        }
      }
    }
  }, [
    options.page,
    options.perPage,
    options.search,
    options.tab,
    options.filterData,
    router,
    activeTab,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  // const getSingleRow = async (item) => {
  //   setSelectedItems([]);
  //   setViewData(item);
  //   setView(true);
  // };

  const getSingleRow = async (id) => {
    try {
      // setSelectedItems([]);
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data?.data);
      }
    } catch (error) {
      console.log(error);
      setView(false);
    }
  };

  const fetchRecords = async (filterData2 = null) => {
    setOptions({ ...options, tab: options.tab });
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let params = {
        status: activeTab !== 'all' && activeTab !== 'mix' ? activeTab : '',
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        searchOptions:
          options.search && localStorage.getItem('searchBy-invoice'),
        exactMatch: options.exactMatch,
        filterData: filterData2
          ? JSON.stringify(filterData2)
          : JSON.stringify(options.filterData),
      };
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`invoices/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      console.log(error);
      setSingleFetchLoading(false);
    }
  };

  const onTabChange = (val) => {
    setSelectedItems([]);
    router.push(`/invoices/${val}`);
  };

  const getInvoiceMultiPrintData = async () => {
    const firstItemId = selectedItems?.[0]?.companies?.id;
    const ids = selectedItems
      .filter((value) => value.companies?.id === firstItemId)
      .map((value) => value?.id);
    if (ids.length != selectedItems.length) {
      toast.warn('Please Select Invoices that are related to the same Company');
    } else {
      try {
        const res = await invoicesHelper.getInvoiceMultiPrintData(
          ids.join(','),
        );
        if (res.result) {
          setOpenMultiPrint(true);
          setSelectedItems(res?.data);
        }
      } catch (e) {
        console.log(e);
      }
    }
  };

  const getInvoicePrintData = async (id) => {
    setopenPrint(true);
    setSingleFetchLoading(true);
    try {
      const res = await invoicesHelper.getInvoicePrintData(id);
      if (res.result) {
        setSelectedItems([res?.data]);
        setSingleFetchLoading(false);
      } else {
        setopenPrint(false);
        setSelectedItems([]);
        setSingleFetchLoading(false);
      }
    } catch (e) {
      console.log(e);
      setSingleFetchLoading(false);
    }
  };

  const fileNameHandler = (items) => {
    let total = 0;
    items?.forEach((el) => {
      total += el?.invoice_amount - getInvoicePayments(el?.payments);
    });
    return (
      '$ ' +
      total +
      '_' +
      items[0]?.companies?.name +
      '_' +
      formatDate(new Date())
    );
  };

  // const onEdit = () => {
  //   setIsUpdate(true);
  //   setShowCreate(true);
  // };
  const onEdit = async () => {
    try {
      setIsUpdate(true);
      setShowCreate(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setSelectedItems([res?.data?.data]);
      }
    } catch (error) {
      setIsUpdate(false);
      setShowCreate(false);
      console.log(error);
    }
  };

  const onAdd = () => {
    setIsUpdate(false);
    setShowCreate(true);
  };

  const onDelete = () => {
    recordManager({
      data: null,
      type: 'delete',
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl: 'invoices',
      deleteReason,
      setDeleteReason,
    });
  };

  const handleDeleteReason = (value) => {
    setDeleteReason(value);
  };

  const onRecordManager = (data, type) => {
    recordManager({
      data,
      type,
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl,
    });
  };

  const onPrev = async () => {
    if (selectedItems.length > 0) {
      const currentIndex = tableRecords.findIndex(
        (item) => item.id === selectedItems[0].id,
      );
      if (currentIndex > 0) {
        const prevItemId = tableRecords[currentIndex - 1].id;
        await getInvoicePrintData(prevItemId);
      }
    }
  };

  const onNext = async () => {
    if (selectedItems.length > 0) {
      const currentIndex = tableRecords.findIndex(
        (item) => item.id === selectedItems[0].id,
      );
      if (currentIndex < tableRecords.length - 1) {
        const nextItemId = tableRecords[currentIndex + 1].id;
        await getInvoicePrintData(nextItemId);
      }
    }
  };

  // const getSingleRow = async (id: number) => {
  //   setSingleFetchLoading(true);
  //   setView(true);
  //   try {
  //     if (cancelToken) await cancelToken.abort();
  //     const controller = new AbortController();
  //     setCancelToken(controller);
  //     const res = await axios.get(`invoices/${id}`, {
  //       signal: controller.signal,
  //     });
  //     if (res && res?.status == 200) {
  //       setViewData(res?.data?.data);
  //       setSingleFetchLoading(false);
  //     }
  //   } catch (err) {
  //     setSingleFetchLoading(false);
  //     setView(false);
  //   }
  // };

  // const [printData, setPrinData] = useState([]);

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////

  const pageName = 'invoice_' + options.tab;
  const title = 'Invoice';
  const customActionButtons = () => (
    <Box style={{ display: 'flex', alignItems: 'center' }}>
      {activeTab == 'auto_generated' && perms?.includes(INVOICES?.VIEW) ? (
        <AppTooltip key={'Approved'} title={'Approved'}>
          <IconButton
            key={'Approved'}
            color="secondary"
            onClick={() => setOpenConfirm(true)}
          >
            <VerifiedIcon />
          </IconButton>
        </AppTooltip>
      ) : (
        <></>
      )}

      {activeTab == 'pending' &&
      perms?.includes(INVOICES?.INVOICE_INITIAL_REVIEW) ? (
        <AppTooltip key={'Approved'} title={'Approved'}>
          <IconButton
            key={'Approved'}
            color="secondary"
            onClick={() => setOpenConfirm(true)}
          >
            <VerifiedIcon />
          </IconButton>
        </AppTooltip>
      ) : (
        <></>
      )}
      {activeTab == 'final_review' &&
      perms?.includes(INVOICES?.INVOICE_FINAL_REVIEW) ? (
        <>
          <AppTooltip key={'FinalReviewApprove'} title={'Final Review Approve'}>
            <IconButton
              key={'FinalReviewApprove'}
              color="primary"
              onClick={() => setOpenConfirm(true)}
            >
              <VerifiedIcon />
            </IconButton>
          </AppTooltip>
          <AppTooltip key={'FinalReviewReject'} title={'Final Review Reject'}>
            <IconButton
              key={'FinalReviewReject'}
              color="error"
              onClick={() => {
                setActionType('reject');
                setOpenConfirm(true);
              }}
            >
              <CancelIcon />
            </IconButton>
          </AppTooltip>
        </>
      ) : (
        <></>
      )}
      {!['all', 'pending', 'final_review', 'auto_generated'].includes(
        activeTab,
      ) &&
      (activeTab == 'open' || 'past_due') &&
      perms?.includes(INVOICES?.OPEN_DUE_TO_PAID) &&
      perms?.includes(INVOICES?.STATUS) ? (
        <AppTooltip key={'changeInvoiceStatus'} title={'Change Status'}>
          <IconButton color="primary" onClick={() => setOpenConfirm(true)}>
            <PublishedWithChangesIcon />
          </IconButton>
        </AppTooltip>
      ) : (
        <></>
      )}
      <AppTooltip key={'invoice_print'} title={'Invoice Print'}>
        <IconButton
          color="secondary"
          onClick={() => getInvoicePrintData(selectedItems[0]?.id)}
        >
          <PrintIcon />
        </IconButton>
      </AppTooltip>

      <AppTooltip key={'multi_invoice_print'} title={'Multi Invoice Print'}>
        <IconButton
          color="secondary"
          onClick={() => getInvoiceMultiPrintData()}
        >
          <FileCopyIcon />
        </IconButton>
      </AppTooltip>
    </Box>
  );

  const mergedDataTableColumn = useCustomColumnInvoice({
    getSingleRow: getSingleRow,
    onPrint: (id) => getInvoicePrintData(id),
    tableRecords: tableRecords,
  });

  const isFirstItem =
    selectedItems?.length > 0 &&
    tableRecords.findIndex((item) => item.id === selectedItems[0].id) === 0;

  const isLastItem =
    selectedItems?.length > 0 &&
    tableRecords.findIndex((item) => item.id === selectedItems[0].id) ===
      tableRecords.length - 1;

  const fileNamePrintModal = `Invoice number ${
    selectedItems[0]?.invoice_number
  }  (${selectedItems[0]?.companies?.name}) ${formatDate(new Date())}`;

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////
  return perms && !perms?.includes(INVOICES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title> Invoices | {options?.tab?.replace('_', ' ')}</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={HeaderInfo().concat({
            href: 'false',
            name: removeUnderScore2(activeTab),
            icon: <></>,
            key: '4',
          })}
        />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          onChange={(_event, val) => onTabChange(val)}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
          {invoicesTabs.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.value}
            />
          ))}
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              reasonOnDelete
              deletePassword="Approve@123"
              passwordOnDelete
              deleteReason={deleteReason}
              onDeleteReason={handleDeleteReason}
              onFilterClick={() => setOpenFilter(true)}
              onEdit={onEdit}
              showAddButton={
                perms?.includes(INVOICES?.CREATE) && activeTab != 'mix'
              }
              showEditButton={perms?.includes(INVOICES?.UPDATE)}
              showDeleteButton={perms?.includes(INVOICES?.DELETE)}
              selectedItems={selectedItems}
              title={title}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={perms?.includes(INVOICES?.VIEW)}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              showDownload={perms?.includes(INVOICES?.DATA_EXPORT)}
              onDownload={() => setShowDownload(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              onAdd={onAdd}
              deleteTitle={`Are you sure to delete invoice? `}
              dialogTitle={`Delete Invoice Item`}
              customActionButtons={customActionButtons}
              onDelete={onDelete}
            />
          }
          // start default props
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          allOptions={['open', 'pending']}
          activeTab={options.tab}
          tableName="newInvoice"
          // end default props
          //start custom props

          {...mergedDataTableColumn}
        />
      </Container>

      <CreateInvoices
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems}
        isUpdate={isUpdate}
        recordManager={onRecordManager}
        loading={singleFetchLoading}
      />

      <InvoiceChangeStatus
        activeTab={activeTab}
        openConfirm={openConfirm}
        actionType={actionType}
        fetchRecords={fetchRecords}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setOpenConfirm={setOpenConfirm}
        setActionType={setActionType}
      />

      {openPrint && (
        <PrintModal
          open={openPrint}
          toggleOpen={() => setopenPrint((d) => !d)}
          initialData={selectedItems[0]}
          title={`Print ` + 'Invoice'}
          ContentPrint={InvoicePrint}
          showNextPrev
          fileName={fileNamePrintModal}
          onPrev={onPrev}
          onNext={onNext}
          isFirstItem={isFirstItem}
          isLastItem={isLastItem}
          loading={singleFetchLoading}
        />
      )}
      {openMultiPrint && (
        <PrintModal
          open={openMultiPrint}
          toggleOpen={() => setOpenMultiPrint((d) => !d)}
          initialData={selectedItems}
          title={`Print ` + 'Invoices'}
          ContentPrint={multiInvoicePrint}
          maxWidth={'90%'}
          fileName={fileNameHandler(selectedItems)}
        />
      )}

      <ViewSingleInvoice
        loading={singleFetchLoading}
        data={viewData}
        setView={setView}
        show={view}
      />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter of Invoices"
        content={filterContentInvoice}
      />

      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
        SearchBy={SearchBy(invoiceSearchFields, 'invoice')}
        showSearchBy={true}
      ></ColumnDialog>

      <InvoicePDFModal
        showDownload={showDownload}
        apiUrl={apiUrl}
        options={options}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        title={title}
        totalItems={totalItems}
      />
    </>
  );
};
export default NewInvoice;
