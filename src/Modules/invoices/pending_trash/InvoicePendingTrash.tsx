// import React from 'react';

// const InvoiceTrash = () => {
//   return <div>InvoiceTrash</div>;
// };

// export default InvoiceTrash;

import PageAction from '@/components/mainComponents/PageAction/PageAction';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { INVOICES } from '@/configs/leftSideMenu/Permissions';

import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import { Box, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';

import { formatDate } from '@/configs/vehicles/configs';
import { toast } from 'react-toastify';
import {
  HeaderInfoInvoiceTrash,
  invoicePendingTrashHeader,
} from '@/configs/invoices/InvoiceTrashHeader';
import { useCustomColumnInvoice } from '../invoiceComponent/InvoiceCustomColumn';
import ViewSingleInvoice from '../invoiceComponent/ViewSingleInvoice';

const InvoicePendingTrash = () => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [data, setData] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [apiUrl] = useState('/invoices/pending-trash');
  const [cancelToken, setCancelToken] = useState(null);
  //@ts-ignore
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [view, setView] = useState(false);
  const [viewData, setViewData] = useState({});

  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////
  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    router,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const getSingleRow = async (id) => {
    try {
      // setSelectedItems([]);
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data?.data);
      }
    } catch (error) {
      console.log(error);
      setView(false);
    }
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`invoices/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      console.log(error);
      setSingleFetchLoading(false);
    }
  };

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setData([]);
      setLoading(true);
      let params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };

      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setData(data.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const onRestore = async () => {
    const ids = selectedItems.map((item) => item.id);
    try {
      let res = await axios.put(`/invoices/pending-trash/restore/${ids}`);
      if (res?.data) {
        const tempRecord2 = data.filter((row) => !ids.includes(row.id));
        setSelectedItems([]);
        setTotalItems(totalItems - ids.length);
        setData(tempRecord2);
        toast.success('Records Restored successfully!');
      }
    } catch (error) {
      toast.error('Your restoration process failed, Please try again');
    }
  };

  const onDelete = async () => {
    const ids = selectedItems.map((item) => item.id);
    try {
      let res = await axios.put(`/invoices/pending-trash/delete/${ids}`);
      if (res?.data) {
        const tempRecord2 = data.filter((row) => !ids.includes(row.id));
        setSelectedItems([]);
        setTotalItems(totalItems - ids.length);
        setData(tempRecord2);
        toast.success('Records Deleted successfully!');
      }
    } catch (error) {
      toast.error('Your deletion process failed, Please try again');
    }
  };

  // const getSingleRow = async (id: number) => {
  //   setSingleFetchLoading(true);
  //   setView(true);
  //   try {
  //     if (cancelToken) await cancelToken.abort();
  //     const controller = new AbortController();
  //     setCancelToken(controller);
  //     const res = await axios.get(`invoices/${id}`, {
  //       signal: controller.signal,
  //     });
  //     if (res && res?.status == 200) {
  //       setViewData(res?.data?.data);
  //       setSingleFetchLoading(false);
  //     }
  //   } catch (err) {
  //     setSingleFetchLoading(false);
  //     setView(false);
  //   }
  // };

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////

  const mergedDataTableColumn = useCustomColumnInvoice({
    getSingleRow: getSingleRow,
    onPrint: () => {},
    tableRecords: [],
    hideCalc: true,
  });
  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(INVOICES?.TRASH_VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Invoice Pending Trash</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfoInvoiceTrash()} />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              trash
              showRestore={perms?.includes(INVOICES.RESTORE_PENDING_TRASH)}
              showDeleteButton={perms?.includes(INVOICES.DELETE_PENDING_TRASH)}
              onRestore={onRestore}
              onDelete={onDelete}
              showEditButton={false}
              showAddButton={false}
              hideFilter
              selectedItems={selectedItems}
              title={'Invoice Pending Trash'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              dialogTitle={'Are you sure you want to delete this data?'}
              deleteTitle={
                'Please confirm by pressing "Yes" or "No" button to proceed'
              }
              restoreDialogText={
                'Would you like to restore your data to its previous state? Please confirm by pressing "Yes" or "No" button to proceed'
              }
              restoreTitle={`Restore Selected Item${
                selectedItems.length > 1 ? 's' : ''
              }`}
            />
          }
          // start default props
          sortLoading={loading}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={data}
          headers={invoicePendingTrashHeader}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={options.tab}
          tableName="invoicePendingTrash"
          //start custom props
          {...mergedDataTableColumn}
          deleted_at={(item) => (
            <Box>{formatDate(item?.deleted_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
          )}
          deleted_by={({ users_invoices_deleted_byTousers }) =>
            users_invoices_deleted_byTousers?.fullname
          }
          deleted_reason={({ deleted_reason }) => {
            return (
              <Box
                sx={{
                  minWidth: '150px',
                  maxWidth: '220px',
                  whiteSpace: 'wrap',
                  lineHeight: '1.5',
                }}
              >
                {deleted_reason}
              </Box>
            );
          }}
        />
        <ViewSingleInvoice
          loading={singleFetchLoading}
          data={viewData}
          setView={setView}
          show={view}
        />
      </Container>
      <ViewSingleInvoice
        data={viewData}
        setView={setView}
        show={view}
        loading={singleFetchLoading}
      />
    </>
  );
};

export default InvoicePendingTrash;
