// import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { Grid, TextField, Typography } from '@mui/material';
import { Box } from '@mui/system';
import React from 'react';
// import { Controller } from 'react-hook-form';
// import { invoice_status } from '@/configs/invoices/invoiceHeader';

export default function Step2({ form }) {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Invoice Details
      </Typography>
      <Grid container spacing={2}>
        <Grid size={12}>
          <TextField
            size="small"
            error={
              form.errors.evidence_proof?.message.length > 0 ? true : false
            }
            id="evidence_proof"
            label="Evidence Proof"
            fullWidth
            variant="outlined"
            {...form.register('evidence_proof')}
            helperText={form.errors.evidence_proof?.message}
          />
        </Grid>
        {/*<Grid item xs={12}>*/}
        {/*  <Controller*/}
        {/*    name="status"*/}
        {/*    control={form.control}*/}
        {/*    render={({ field, fieldState: { error } }) => (*/}
        {/*      <AutoComplete*/}
        {/*        url={false}*/}
        {/*        label="Status"*/}
        {/*        fieldName=""*/}
        {/*        field={field}*/}
        {/*        error={error}*/}
        {/*        staticOptions={invoice_status}*/}
        {/*        column={''}*/}
        {/*        modal={''}*/}
        {/*      />*/}
        {/*    )}*/}
        {/*  />*/}
        {/*</Grid>*/}
        <Grid size={12}>
          <TextField
            error={form.errors.description?.message.length > 0 ? true : false}
            id="description"
            label="Description"
            multiline
            rows={3}
            fullWidth
            variant="outlined"
            {...form.register('description')}
            helperText={form.errors.description?.message}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
