import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  Typography,
} from '@mui/material';
import { Box } from '@mui/system';
import React, { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import dayjs from 'dayjs';
import axios from '@/lib/axios';
import { formFormatDate } from '@/configs/configs';
import { formatDate } from '@/configs/vehicles/configs';

export default function Step1({ form, isUpdate, setValue }) {
  const [renderKeyCus, setRenderKeyCus] = useState(0);
  const [eta, setEta] = useState([]);
  const [containerId, setContainerId] = useState();
  const [company, setCompany] = useState([]);

  let total = 0;
  const getTotal = (items) => {
    items &&
      items?.map((item) => {
        let sTow = item?.vehicle_costs?.towing_cost || 0; // Default to 0 if undefined
        let sDismantle = item?.vehicle_costs?.dismantal_cost || 0; // Default to 0 if undefined
        let sShipping = item?.vehicle_costs?.ship_cost || 0; // Default to 0 if undefined
        let sStorage = item?.vehicle_costs?.title_charge || 0; // Default to 0 if undefined
        let sOther = item?.vehicle_costs?.other_cost || 0; // Default to 0 if undefined
        total += +sTow + +sDismantle + +sShipping + +sStorage + +sOther;
      });
    setValue('invoice_amount', total || null);
  };
  const getETA = async () => {
    try {
      const res = await axios.get(`containers/ETA/${containerId}`);
      if (res.status === 200) {
        if (!isUpdate) {
          setEta(res.data?.data[0]?.bookings?.eta);
        }
        setValue(
          'invoice_due_date',
          formatDate(res.data?.data[0]?.bookings?.eta) || null,
        );

        if (
          form.isUpdate &&
          form.selectedItems[0]?.container_id != res.data?.data[0]?.id
        ) {
          setValue('invoice_number', res.data?.data[0]?.invoice_number || null);
        }
        if (!form.isUpdate) {
          setValue('invoice_number', res.data?.data[0]?.invoice_number || null);
        }

        setValue('purpose', res.data?.data[0]?.no_units_load || null);

        getTotal(res.data.data[0]?.vehicles);
        setCompany([
          {
            label: res.data?.data[0].companies?.name,
            id: res.data?.data[0].companies?.id,
          },
        ]);
        setRenderKeyCus((prv) => prv + 1);
      }
    } catch (error) {
      // Handle the error here, e.g., show an error message
    }
  };

  const id = form.watch('container_id');

  useEffect(() => {
    setContainerId(id);
    if (id != undefined && containerId == id && id != '') {
      getETA();
    }
    if (!id) {
      setEta(null);
      setValue('invoice_due_date', null);
    }
  }, [id, containerId]);

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Invoice Details
      </Typography>
      <Grid container spacing={2}>
        <Grid size={12}>
          <Controller
            name="container_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Container"
                fieldName="container_number"
                field={field}
                error={error}
                staticOptions={false}
                column={'container_number'}
                modal={'containers'}
                customeName={'container_number'}
              />
            )}
          />
        </Grid>
        <Grid
          key={renderKeyCus + 100}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.invoice_number?.message.length > 0 ? true : false
            }
            id="invoice_number"
            label="Invoice Number"
            fullWidth
            variant="outlined"
            {...form.register('invoice_number')}
            helperText={form.errors.invoice_number?.message}
          />
        </Grid>

        <Grid
          key={renderKeyCus + 50}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="company_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                // url="autoComplete"
                // label="Select Company"
                // fieldName="name"
                // field={field}
                // error={error}
                // staticOptions={false}
                // column={'name'}
                // modal={'companies'}
                // defualtValue={company[0]}
                url={false}
                label="Select Company"
                fieldName=""
                field={field}
                error={error}
                staticOptions={company}
                column={''}
                modal={''}
                defualtValue={company[0]}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="invoice_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Invoice Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          key={renderKeyCus}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="invoice_due_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Invoice Due Date"
                value={
                  !field.value
                    ? //@ts-ignore
                      dayjs(eta).toDate()
                    : dayjs(field.value).toDate()
                }
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          key={renderKeyCus + 'purpose'}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.purpose?.message.length > 0 ? true : false}
            id="purpose"
            label="Purpose"
            fullWidth
            variant="outlined"
            {...form.register('purpose')}
            helperText={form.errors.purpose?.message}
          />
        </Grid>

        <Grid
          key={renderKeyCus + 120}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            InputProps={{ readOnly: true }}
            size="small"
            error={
              form.errors.invoice_amount?.message.length > 0 ? true : false
            }
            id="invoice_amount"
            label="Invoice Amount"
            fullWidth
            variant="outlined"
            {...form.register('invoice_amount', {
              setValueAs: (value) => (value ? +value : null), // Cast the value to a float
            })}
            helperText={form.errors.invoice_amount?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.payment_received?.message.length > 0 ? true : false
            }
            id="payment_received"
            label="Payment Received"
            fullWidth
            variant="outlined"
            disabled
            {...form.register('payment_received', {
              setValueAs: (value) => (value ? +value : null), // Cast the value to a float
            })}
            helperText={form.errors.payment_received?.message}
          />
          <Typography
            textAlign="left"
            variant="caption"
            display="block"
            gutterBottom
            marginBottom={0}
            color={'green'}
          >
            Payments are now managed through the Payments section
          </Typography>
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="payments"
            render={({ field }) => {
              return (
                <TextField
                  InputProps={{
                    style: {
                      maxHeight: '600px',
                    },
                  }}
                  size="small"
                  id="payments"
                  label="Received Date/s"
                  fullWidth
                  variant="outlined"
                  disabled
                  multiline
                  value={
                    !field?.value
                      ? null
                      : field.value
                          .map((payment) => formatDate(payment?.payment_date))
                          .join('\t | \t')
                  }
                />
              );
            }}
          />
        </Grid>

        <Grid
          sx={{ paddingTop: '8px !important' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.discount?.message.length > 0 ? true : false}
            id="discount"
            label="Discount Amount"
            type="number"
            fullWidth
            variant="outlined"
            {...form.register('discount', {
              setValueAs: (value) => (value ? +value : null), // Cast the value to a float
            })}
            helperText={form.errors.discount?.message}
          />
        </Grid>

        <Grid
          sx={{ paddingTop: '8px !important' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.payment_method?.message.length > 0 ? true : false
            }
            id="payment_method"
            label="Payment Method"
            fullWidth
            variant="outlined"
            {...form.register('payment_method')}
            helperText={form.errors.payment_method?.message}
          />
        </Grid>
        <Grid
          style={{ display: 'flex' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="title_charge_visible"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="title_charge_visible"
                      />
                    }
                    label="Show Title Charge on Invoice ?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>

        <Grid
          style={{ display: 'flex' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="towing_charge_visible"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="towing_charge_visible"
                      />
                    }
                    label="Show Towing Charge on Invoice ?"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
