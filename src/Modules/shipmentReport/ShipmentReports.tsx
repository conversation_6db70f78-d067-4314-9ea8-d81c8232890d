import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';

import { Box, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { REPORTS } from '@/configs/leftSideMenu/Permissions';

import {
  filterContentShipmentReport,
  HeaderInfo,
  headers,
  IngateEtdEtLOptions,
  PodPolCompanyOptions,
  removeAllZeroRow,
  shipmentSearchFields,
} from './shipmentReportConfig';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import SearchBy from '@/components/mainComponents/cComponents/SearchBy';
import { applySavedColumns } from '@/utils/columnUtils';
import ShipmentReportFilter from './ShipmentReportFilter';
const ShipmentReport = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const [apiUrl] = useState('/containers/report');
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showDownload, setShowDownload] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [defaultHeaders, setDefaultHeaders] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState({
    selected: 'company',
  });
  const [selectedFilterDate, setSelectedFilterDate] = useState({
    selected: 'ingate',
  });
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20000000000000,
    filterData: {},
    tab: 'shipment',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'on_the_way',
      order: 'desc',
    },
  });
  const filterTableRecords = removeAllZeroRow(tableRecords, [
    ...new Set(
      [defaultHeaders, selectedHeaders].flat().map((header) => header.id),
    ),
  ]);

  const pageName = 'shipment_report' + options.tab;
  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    // options.orderBy,
    options.filterData,
    options.tab,
    selectedFilter.selected,
    selectedFilterDate.selected,
  ]);
  useEffect(() => {
    const allHeaders = headers(selectedFilter.selected);
    const slicedHeaders =
      selectedHeaders.length > 0
        ? allHeaders.slice(0, selectedHeaders.length)
        : allHeaders.slice(0, 7);

    const newColumns = [slicedHeaders[0], ...slicedHeaders.slice(1).reverse()];
    setDefaultHeaders(newColumns);
  }, [selectedFilter.selected, selectedHeaders]);
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setLoading(true);
      let params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        filterData: options.filterData,
        status: options.tab,
        selectedFilter: selectedFilter.selected,
        selectedFilterDate: selectedFilterDate.selected,
      };

      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      const transformedData = data.data?.map((item) => {
        const newItem = { ...item };
        Object.entries(item).forEach(([key, value]) => {
          if (key.match(/^[a-z]{3}_\d{4}$/)) {
            const month = key.split('_')[0];
            const percentage =
              toTotal(data.data)[key] !== 0
                ? ((Number(value) * 100) / toTotal(data.data)[key]).toFixed(2)
                : 0;
            newItem[`${month}_%`] = `${percentage} %`;
          }
        });
        return newItem;
      });
      setTableRecords(transformedData);

      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (profile) {
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
    }
  }, [profile, options.tab, selectedFilter.selected]);

  function toTotal(records: any[]) {
    const result = records.reduce((acc, curr) => {
      Object.keys(curr).forEach((key) => {
        if (typeof curr[key] === 'number' && key !== 'id') {
          acc[key] = (acc[key] || 0) + curr[key];
        }
      });
      acc.total =
        (acc.total || 0) +
        Object.values(curr)
          .filter((val) => typeof val === 'number' && val !== curr.id)
          .reduce((sum: number, val: number) => sum + val, 0);
      return acc;
    }, {});
    return result;
  }

  //@ts-ignore
  const handleTabChange = (event, newTab) => {
    setOptions({ ...options, tab: newTab });
  };

  return perms && !perms?.includes(REPORTS?.VIEW_SHIPMENT_REPORT) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Shipment | Summary List</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo.breadcrumbs} />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          onChange={handleTabChange}
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab
            sx={{ fontSize: 10 }}
            key="shipment"
            label="shipment"
            value="shipment"
          />
          <Tab
            sx={{ fontSize: 10 }}
            key="vehicle"
            label="vehicle"
            value="vehicle"
          />
        </Tabs>

        <DataTable3
          PageAction={
            <>
              <PageAction
                showDeleteButton={false}
                showEditButton={false}
                selectedItems={selectedItems}
                title={'Shipment Report'}
                options={options}
                setOptions={setOptions}
                total={filterTableRecords.length}
                dialogTitle={''}
                deleteTitle={''}
                hideFilter={
                  selectedFilter.selected === 'company' ? false : true
                }
                onFilterClick={() => setOpenFilter(true)}
                onDownload={() => setShowDownload(true)}
                showDownload={true}
                showCustomizeColumn={true}
                onCustomizeColumn={() => setShowColumnDialog(true)}
                startCustomComponent={
                  <Box
                    sx={{ display: 'flex', flexDirection: 'row', gap: '6px' }}
                  >
                    <ShipmentReportFilter
                      selected={selectedFilterDate}
                      onSelect={(value) => {
                        setSelectedFilterDate(value);
                      }}
                      options={IngateEtdEtLOptions}
                    />
                    <ShipmentReportFilter
                      selected={selectedFilter}
                      onSelect={(value) => {
                        setSelectedFilter(value);
                      }}
                      options={PodPolCompanyOptions}
                    />
                  </Box>
                }
              />
            </>
          }
          // start default props
          hidePagination
          hideCheckBox
          options={options}
          setOptions={setOptions}
          setSelectedItems={setSelectedItems}
          selectedItems={selectedItems}
          totalItems={totalItems}
          loading={loading}
          items={filterTableRecords}
          setItems={setTableRecords}
          activeTab={options.tab}
          tableName="shipmentReport"
          headers={defaultHeaders}
          name={(item) => (
            <Box
              sx={{
                textTransform: 'capitalize',
                cursor: 'pointer',
                color: '#2196f3',
                fontWeight: 'bold',
                textAlign: 'left',
              }}
            >
              {item.name}
            </Box>
          )}
          totalCalc={filterTableRecords.length > 0 ? true : false}
          footerValues={{
            name: 'Total',
            ...toTotal(filterTableRecords),
          }}
        />
        <ColumnDialog
          selectedSetting={selectedSetting}
          setSelectedSetting={setSelectedSetting}
          pageName={pageName}
          selectedHeaders={defaultHeaders}
          setSelectedHeaders={setSelectedHeaders}
          setShowColumnDialog={setShowColumnDialog}
          showColumnDialog={showColumnDialog}
          defaultHeaders={headers(selectedFilter.selected)}
          SearchBy={SearchBy(shipmentSearchFields, 'Shipment_Report')}
          showSearchBy={true}
        ></ColumnDialog>
        <FilterModal2
          open={openFilter}
          toggleOpen={() => setOpenFilter((d) => !d)}
          options={options}
          setOptions={setOptions}
          title="Filter Shipment Report"
          content={filterContentShipmentReport(options.tab)}
        />

        <PdfModal
          options={options}
          open={showDownload}
          pdf_title={'Shipment Shipment Report'}
          selectedHeaders={defaultHeaders}
          setShowDownload={setShowDownload}
          fetchDownloadRecords={() => filterTableRecords}
          tableRecords={filterTableRecords}
          headers={[
            headers(selectedFilter.selected)[0],
            ...headers(selectedFilter.selected).slice(1).reverse(),
          ]}
          // custom props
          //start custom props
          total={(item) => {
            return (
              item.final_checked +
              item.pending +
              item.checked +
              item.at_loading +
              item.on_the_way +
              item.at_the_dock +
              item.cbp_inspection +
              item.arrived
            );
          }}
        ></PdfModal>
      </Container>
    </>
  );
};

export default ShipmentReport;
