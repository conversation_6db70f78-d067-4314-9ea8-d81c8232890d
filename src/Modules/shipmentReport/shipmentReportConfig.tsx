import DashboardIcon from '@mui/icons-material/Dashboard';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';

export const PodPolCompanyOptions = [
  {
    value: 'company',
    title: 'Company',
  },
  {
    value: 'pol',
    title: 'Port of Loading',
  },
  {
    value: 'pod',
    title: 'Port of Destination',
  },
];

export const IngateEtdEtLOptions = [
  {
    value: 'ingate',
    title: 'In Gate Date',
  },
  {
    value: 'pol_date',
    title: 'Port of Loading Date',
  },
  {
    value: 'pod_date',
    title: 'Port of Destination Date',
  },
];
export const HeaderInfo = {
  breadcrumbs: [
    {
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    },
    {
      href: 'false',
      name: 'Shipment Report',
      icon: <DirectionsBoatIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ],
};

function getPast12Months(): string[] {
  const months: string[] = [];

  for (let i = 0; i < 12; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);

    const label = date
      .toLocaleString('en-US', {
        month: 'short',
        year: 'numeric',
      })
      .toLowerCase()
      .replace(' ', '_');
    months.push(label);
    months.push(
      `${date
        .toLocaleString('en-US', {
          month: 'short',
        })
        .toLowerCase()}_%`,
    );
  }
  return months;
}
export const headers = (column: string) => {
  const label = {
    company: 'Company',
    pol: 'Port of Loading',
    pod: 'Port of Destination',
  };
  return [
    {
      id: 'name',
      label: label[column],
      wrap: true,
    },
    ...getPast12Months().map((item) => ({
      id: item.replace(/ /g, '_'),
      label: item.toUpperCase().replace(/_/g, ' '),
      align: 'right',
    })),
  ];
};

const companies = [
  {
    name: 'UNITED UNSTOPPABLE CAR AUCTION L L C Poti Georgia',
    id: 806,
  },
  {
    name: 'UNITED UNSTOPPABLE CAR AUCTION L L C OMAN',
    id: 577,
  },
  {
    name: 'UNITED UNSTOPPABLE CAR AUCTION L L C',
    id: 158,
  },
];
export const filterContentShipmentReport = (tab) => {
  const items =
    tab === 'shipment'
      ? [
          {
            name: 'company_id',
            label: 'Company',
            type: 'autocomplete',
            url: '/autoComplete?column=name&modal=companies&ids=',
            keyName: 'name',
          },
          {
            name: 'location_id',
            label: 'Locations',
            type: 'autocomplete',
            url: '/autoComplete?column=name&modal=locations&ids=',
            keyName: 'name',
          },
          {
            name: 'destination_id',
            label: 'Destination',
            type: 'autocomplete',
            url: '/autoComplete?column=name&modal=destinations&ids=',
            keyName: 'name',
          },
        ]
      : [
          {
            name: 'company_id',
            label: 'Company',
            type: 'autocomplete',
            url: '',
            options: companies,
            keyName: 'name',
          },
        ];

  return [
    {
      title: 'ID Filtering',
      items: [...items],
    },
  ];
};

export const shipmentSearchFields = getPast12Months().map((item) => ({
  value: item.replace(/ /g, '_'),
  name: item.toUpperCase().replace(/_/g, ' '),
}));

export const removeAllZeroRow = (items, columns) => {
  const filteredData = items.filter((item) => {
    const relevantKeys = columns.filter(
      (key: string) => key !== 'name' && !key.endsWith('_%'),
    );
    return relevantKeys.some((key) => item[key] !== 0);
  });
  return filteredData || [];
};
