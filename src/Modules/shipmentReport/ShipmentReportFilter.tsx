import { Box, Menu, MenuItem } from '@mui/material';
import { useState } from 'react';

const ShipmentReportFilter = ({ selected, onSelect, options }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const onSubmit = (value) => {
    onSelect({
      selected: value,
    });
    handleClose();
  };

  return (
    <>
      <Box
        onClick={handleClick}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        sx={{
          py: 0.5,
          px: 2,
          cursor: 'pointer',
          fontWeight: 600,
          border: '1px solid black',
          borderRadius: '4px',
        }}
      >
        {options.find((item) => item.value === selected?.selected)?.title}
      </Box>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <Box sx={{ width: 300 }}>
          {options.map((item, i) => (
            <MenuItem
              onClick={() => {
                onSubmit(item.value);
              }}
              key={i}
            >
              {item.title}
            </MenuItem>
          ))}
        </Box>
      </Menu>
    </>
  );
};

export default ShipmentReportFilter;
