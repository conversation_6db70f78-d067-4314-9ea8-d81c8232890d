import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import { Autocomplete, Box, Grid, TextField, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import { currencyOptions } from '../customerPayments/customerPaymentsComponent/customerPaymentHeader';
import { chargesType } from '@/configs/disputeBookings/disputeBookingsHeader';
import React, { useState } from 'react';

const DisputeBookingStepOne = ({ form }) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');

  const progress = [
    { id: 0, label: '0%' },
    { id: 25, label: '25%' },
    { id: 50, label: '50%' },
    { id: 75, label: '75%' },
    { id: 100, label: '100%' },
  ];

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">Dispute Booking Details</Typography>
      <Grid container spacing={2}>
        <Grid
          mt={1.5}
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="booking_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="Select Booking"
                fieldName="booking_number"
                field={{
                  ...field,
                  onChange: (value) => {
                    field.onChange(value);
                  },
                }}
                error={error}
                staticOptions={false}
                column={'booking_number'}
                modal={'bookings'}
                customeName={'booking_number'}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="container_ids"
            control={form.control}
            render={({ field }) => {
              return (
                <FilterAutocomplete
                  url={`/autoComplete?column=container_number&modal=containers&ids=`}
                  label="Select Containers"
                  name={'container_ids'}
                  keyName={'container_number'}
                  values={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                  }}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="invoice_numbers"
            control={form.control}
            render={({ field }) => {
              return (
                <Autocomplete
                  size="small"
                  multiple
                  freeSolo
                  options={[]}
                  inputValue={inputValue}
                  onInputChange={(_, newInputValue) =>
                    setInputValue(newInputValue)
                  }
                  value={field.value || []}
                  onChange={(_, newValue) => {
                    field.onChange(newValue);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      inputRef={inputRef}
                      label="Enter Invoice Numbers"
                      placeholder="Type invoice number and press space"
                      onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        const input = e.target as HTMLInputElement;
                        const trimmed = input.value.trim();
                        if (e.key === ' ') {
                          e.preventDefault();

                          if (trimmed && !field.value?.includes(trimmed)) {
                            const newValues = [...(field.value || []), trimmed];
                            field.onChange(newValues);
                          }

                          setInputValue('');
                        }
                        if (e.key === 'Enter') {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="charges_type"
            control={form.control}
            render={({ field, fieldState: { error, invalid } }) => {
              return (
                <Autocomplete
                  sx={{ width: '100%' }}
                  size="small"
                  value={field.value ?? []}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                  options={chargesType}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Charges Type"
                      error={invalid}
                      helperText={error?.message}
                    />
                  )}
                  multiple={true}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="progress"
            control={form.control}
            render={({ field, fieldState: { error, invalid } }) => {
              let selectedProgress = progress.find(
                (row) => row.id == field.value,
              );
              if (selectedProgress === undefined) selectedProgress = null;
              return (
                <Autocomplete
                  sx={{ width: '100%' }}
                  size="small"
                  value={selectedProgress}
                  getOptionLabel={(option) =>
                    option?.label ? option?.label : ''
                  }
                  onChange={(_event, value) =>
                    field.onChange(value?.id ?? null)
                  }
                  options={progress}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Progress"
                      error={invalid}
                      helperText={error?.message}
                    />
                  )}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="total_amount"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.total_amount?.message.length > 0 ? true : false
                }
                id="total_amount"
                value={field.value ?? ''}
                label="Total Amount"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) => field.onChange(+value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="currency"
            control={form.control}
            render={({ field, fieldState: { error, invalid } }) => {
              return (
                <Autocomplete
                  sx={{ width: '100%' }}
                  size="small"
                  value={field.value}
                  onChange={(_, value) => {
                    field.onChange(value);
                  }}
                  options={currencyOptions}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Currency"
                      error={invalid}
                      helperText={error?.message}
                    />
                  )}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="to_be_done"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.to_be_done?.message.length > 0 ? true : false
                }
                id="to_be_done"
                value={field.value ?? ''}
                label="To Be Done"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="responsible"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.responsible?.message.length > 0 ? true : false
                }
                id="responsible"
                value={field.value ?? ''}
                label="Responsible"
                fullWidth
                variant="outlined"
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="details"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.details?.message.length > 0 ? true : false}
                id="details"
                value={field.value ?? ''}
                label="Details"
                fullWidth
                variant="outlined"
                multiline
                rows={5}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default DisputeBookingStepOne;
