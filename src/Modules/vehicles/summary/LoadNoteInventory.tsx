import { zodResolver } from '@hookform/resolvers/zod';
import {
  <PERSON><PERSON>,
  Card,
  CardActions,
  CardContent,
  Grid,
  IconButton,
  InputLabel,
  Modal,
  TextField,
} from '@mui/material';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import { z } from 'zod';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

export const schema = z.object({});

const LoadNoteInventory = ({ loadNoteField, setShow, show }) => {
  const autoComplete = [
    'The vehicles we have On Hand  \nwill not fit in a combination of  \n4 cars in a container,  \nIts a kind request to buy more  \nsmall size vehicles or approve  \ncombination of 3 cars to be loaded  \nin the container',
    "Combination of vehicles is incomplete. \n You're requested to buy more vehicles \n in order to complete the loading combination \n or another option is to allow us to load these \n in a mix container for fast and on time loading",
    "Unfortunately due to no available \n vessel schedule for the week from \n the ship lines we were unable \n to load your cars into a container, \n we're following up daily for availability \n and will do our utmost to \n ship your cars and avoid delays",
    'No Comment',
    'Custom Note',
  ];

  let defaultValuesNote = {};
  const { control, handleSubmit, watch, getValues, reset, setValue } = useForm({
    resolver: zodResolver(schema),
    defaultValues: defaultValuesNote,
  });
  useEffect(() => {
    const defaultValuesNote = loadNoteField.reduce((obj, row) => {
      obj[row.name] =
        row.reason != null
          ? autoComplete.includes(row.reason)
            ? row.reason
            : 'Custom Note'
          : null;
      obj[`Custom ${row.name}`] = !autoComplete.includes(row.reason)
        ? row.reason
        : null;
      return obj;
    }, {});
    Object.keys(defaultValuesNote).forEach((key) => {
      //@ts-ignore
      setValue(key, defaultValuesNote[key]);
    });
  }, [loadNoteField, setValue]);

  const onSubmit = async () => {
    const values = getValues();
    const valueTemp = loadNoteField.map((item) => {
      return {
        location_id: item?.location_id,
        company_id: item?.companyid,
        reason:
          values[item?.name] != 'Custom Note'
            ? values[item?.name]
            : values[`Custom ${item?.name}`],
      };
    });
    const res = await axios.put('companies/add-load-note', valueTemp);
    if (res.status == 200) {
      toast.success('Successfully Note Added');
      setShow(false);
    }
  };

  return (
    <Modal
      onClose={() => setShow(false)}
      aria-labelledby="customized-dialog-title"
      open={show}
      BackdropProps={{
        onClick: () => {}, // handle backdrop click event
      }}
    >
      <Card
        style={{
          position: 'absolute' as 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '50vw',
          minHeight: '30vh',
          maxHeight: '80vh',
        }}
      >
        <IconButton
          aria-label="close"
          onClick={() => {
            reset();
            setShow(false);
          }}
          sx={{ position: 'absolute', right: 8, top: 0 }}
        >
          <CloseIcon />
        </IconButton>

        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent sx={{ mt: 3, height: '500px !important' }}>
            <Grid
              sx={{ height: '440px !important', overflow: 'auto' }}
              container
              spacing={1}
            >
              {loadNoteField?.map((row, index) => (
                <Grid sx={{ my: 1 }} key={index} size={12}>
                  <InputLabel
                    sx={{ mb: 1 }}
                  >{`Note for ${row.name} for ${row.companyname}`}</InputLabel>
                  <Controller
                    key={index}
                    //@ts-ignore
                    name={row.name}
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <AutoComplete
                        showIndex={true}
                        url={false}
                        name={row.name}
                        label="Select Note or Add Custom"
                        fieldName=""
                        field={field}
                        error={error}
                        staticOptions={autoComplete}
                        column={''}
                        modal={''}
                      />
                    )}
                  />
                  {/* @ts-ignore */}
                  {watch(row.name) == 'Custom Note' && (
                    <Controller
                      //@ts-ignore
                      name={`Custom ${row.name}`}
                      control={control}
                      render={({ field }) => (
                        <TextField
                          sx={{ mt: 1.5 }}
                          size="small"
                          multiline
                          rows={2}
                          value={field.value ?? ''}
                          label="Custom Note"
                          fullWidth
                          variant="outlined"
                          onChange={(value) =>
                            //@ts-ignore
                            field.onChange(value.target.value)
                          }
                          ref={field.ref}
                        />
                      )}
                    />
                  )}
                </Grid>
              ))}
            </Grid>
          </CardContent>
          <CardActions sx={{ position: 'absolute', bottom: 0, right: '10px' }}>
            <Button
              loading={false}
              color="primary"
              type="submit"
              variant="contained"
            >
              Save
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
};

export default LoadNoteInventory;
