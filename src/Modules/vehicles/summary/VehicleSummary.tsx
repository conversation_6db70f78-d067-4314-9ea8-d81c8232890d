import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  HeaderInfoVehicleSummary,
  tableColumnsVehicleSummary,
} from '@/configs/vehicles/summaryHeader';
import {
  Box,
  Button,
  Container,
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  Tab,
  Tabs,
} from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import { menuItems } from '@/configs/vehicles/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { useCustomColumnVehicleSum } from './VehicleSumCustomCoulmn';
import EmailIcon from '@mui/icons-material/Email';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';
import EmailAndExcelInventory from './EmailAndExcelInventory';
import LoadNoteInventory from './LoadNoteInventory';
import { toast } from 'react-toastify';
import { static_locations } from '@/configs/configs';
import MultiLoadNoteInventory from './MuiltiLoadNoteInventory';
import {
  destinationColor,
  destinationTextColor,
} from '@/configs/booking/bookingHeader';
import Link from 'next/link';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import SettingModal from './settingModal';

const VehicleSummary = () => {
  const selectedItem = JSON.parse(
    localStorage.getItem('vehicle_summary_dropdown'),
  );
  const activeState = selectedItem ? selectedItem : 'active_customer';
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [data, setData] = useState([]);
  const [totalData, setTotalData]: any = useState({});
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const [state, setState] = useState(activeState);
  const [apiUrl] = useState('/vehicles/summary');
  const [cancelToken, setCancelToken] = useState(null);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 10000000000,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    inventory: localStorage.getItem('inventory')
      ? JSON.parse(localStorage.getItem('inventory'))
      : true,
    orderBy: { column: 'on_the_way', order: 'desc' },
  });
  const [selectedItems, setSelectedItems] = useState([]);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [type, setType] = useState(null);
  const [showLoadNoteModal, setShowLoadNoteModal] = useState(false);
  const [showMultiLoadNoteModal, setShowMultiLoadNoteModal] = useState(false);
  const [loadNoteField, setLoadNoteField] = useState([]);
  const [vehicleCounts, setVehicleCounts] = useState([]);
  const [vehicleNotesLoading, setVehiclesNotesLoading] = useState(false);
  const [activeVehicles, setActiveVehicles] = useState([]);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    auction: JSON.parse(localStorage.getItem('auction-setting')) ?? true,
    onTheWay: JSON.parse(localStorage.getItem('onTheWay-setting')) ?? true,
    onHandNoTitle:
      JSON.parse(localStorage.getItem('onHandNoTitle-setting')) ?? true,
    onHandWithTitle:
      JSON.parse(localStorage.getItem('onHandWithTitle-setting')) ?? true,
  });

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////
  useEffect(() => {
    const allRoute = router.query.id;
    if (allRoute != undefined) {
      setOptions({ ...options, tab: allRoute.toString() });
    }
    if (options.tab == allRoute) {
      fetchRecords();
    }
  }, [
    options.page,
    options.inventory,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
    router,
    state,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const getVehiclesCountBasedOnLocation = async () => {
    try {
      setVehiclesNotesLoading(true);
      const data = await axios.get(
        'vehicles/vehicles-count-based-on-location',
        {
          params: {
            company_id: selectedItems.length == 1 ? selectedItems[0]?.id : null,
            state: state,
          },
        },
      );
      setVehicleCounts(data.data);
      setVehiclesNotesLoading(false);
    } catch (error) {
      setVehiclesNotesLoading(false);
      console.error('Error submitting form:', error);
    }
  };

  const handleChange = (event) => {
    localStorage.setItem(
      'vehicle_summary_dropdown',
      JSON.stringify(event.target.value),
    );
    setState(event.target.value);
  };

  //@ts-ignore
  const onTabChange = (val) => router.push(`/vehicles/vehicle_summary/${val}`);

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setData([]);
      setLoading(true);
      let params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        inventory: options.inventory,
        filterData: JSON.stringify(options.filterData),
        ...(state !== 'all' ? { state: state } : {}),
      };
      if (options.tab !== 'all') {
        //@ts-ignore
        params.locationId = locations?.filter((item) => {
          if (item.name == options.tab) {
            return item.id;
          }
        })[0]?.id;
      }
      if (router.query.yard_location) {
        //@ts-ignore
        params.yard_location_id = router.query.yard_location;
      }
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setData(data.data.data);
      setTotalData(data.data.totalData);
      setActiveVehicles(data.data.activeVehicles);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const handleCompanyPort = async () => {
    const ids = selectedItems.map((item) => item.id);
    try {
      const res = await axios.get(`/vehicles/company-port-on-hand/${ids}`);
      if (res.status == 200 && res?.data?.data?.length >= 0) {
        setShowLoadNoteModal(true);
        setLoadNoteField(res?.data?.data);
      }
    } catch (error) {}
  };

  const handleEmail = async (type) => {
    if (state == 'on_the_way' || state == 'on_hand_no_title') {
      setShowEmailModal(true);
      setType(type);
    } else {
      const ids = selectedItems.map((item) => item.id);
      try {
        const res = await axios.get(`/vehicles/company-port-on-hand/${ids}`);
        if (res.status == 200 && res?.data?.data?.length >= 0) {
          const dataExist = res?.data?.data.filter(
            (item) => item.reason != null,
          );
          if (dataExist.length > 0) {
            setShowEmailModal(true);
            setType(type);
          } else {
            toast.warn(
              'Please review, this company dose not have load comment ',
            );
          }
        }
      } catch (error) {}
    }
  };

  const handleNoNote = async () => {
    const ids = selectedItems.map((item) => item.id);
    try {
      const res = await axios.put(`/companies/no-comments-load/${ids}`);
      if (res.status == 200) {
        toast.success('No Comment Added');
      }
    } catch (error) {}
  };

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////
  const locations = static_locations.map((item) => ({
    name: item.name,
    id: item.id,
  }));
  const mergedDataTableColumn = useCustomColumnVehicleSum({
    data,
    totalData,
    options,
    locations,
    inventory: options?.inventory,
    settings: settings,
  });

  const idLocation = locations?.filter((item) => {
    if (item.name == options.tab) {
      return item.id;
    }
  })[0]?.id;

  const checkHeader = () => {
    let filteredColumns = [...tableColumnsVehicleSummary];
    if (options.tab === 'Savannah, GA' || options.tab === 'Houston, TX') {
      filteredColumns = filteredColumns.filter(
        (column) => column.id !== 'loading_companies',
      );
    }
    if (!options?.inventory) {
      filteredColumns = filteredColumns.filter(
        (column) => column.id !== 'reason',
      );
    }
    if (
      options?.tab === 'Savannah, GA' ||
      options?.tab === 'Houston, TX' ||
      options?.tab === 'Los Angeles, CA' ||
      options?.tab === 'New Jersey, NJ'
    ) {
      filteredColumns = filteredColumns.filter(
        (column) =>
          column.id !== 'mix_halfcut' && column.id !== 'complete_halfcut',
      );
    }
    return filteredColumns;
  };
  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  const customComponent = (
    <>
      <Box sx={{ display: 'flex' }}>
        {selectedItems.length == 1 && (
          <Box>
            <Button
              startIcon={<EmailIcon />}
              variant="contained"
              sx={{ mx: 2 }}
              onClick={() => {
                handleEmail('email');
              }}
            >
              Inv
            </Button>
            <Button
              startIcon={<DownloadForOfflineIcon />}
              variant="contained"
              color="success"
              sx={{ color: 'white' }}
              loading={vehicleNotesLoading}
              onClick={async () => {
                await getVehiclesCountBasedOnLocation();
                handleEmail('download');
              }}
            >
              Inv
            </Button>
          </Box>
        )}
        {selectedItems.length >= 1 && (
          <Box>
            <Button
              variant="contained"
              sx={{ mx: 2 }}
              onClick={
                selectedItems.length == 1
                  ? () => handleCompanyPort()
                  : () => setShowMultiLoadNoteModal(true)
              }
            >
              Add Note
            </Button>
          </Box>
        )}
        {selectedItems.length >= 1 && (
          <Box>
            <Button
              variant="contained"
              color="info"
              sx={{ color: 'white' }}
              onClick={() => handleNoNote()}
            >
              No Note
            </Button>
          </Box>
        )}
        <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
          <InputLabel id="demo-select-small-label">Status</InputLabel>
          <Select
            labelId="demo-select-small-label"
            id="demo-select-small"
            value={state}
            label="Status"
            onChange={handleChange}
            sx={{ width: '200px' }}
          >
            {menuItems.map((item) => (
              <MenuItem key={item.value} value={item.value}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <IconButton size="large" onClick={() => setShowSettings(true)}>
          <SettingsSuggestIcon />
        </IconButton>
      </Box>
    </>
  );

  return perms && !perms?.includes(VEHICLES?.VIEW_SUMMARY) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>V Sum | {options?.tab}</title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfoVehicleSummary()} />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          textColor="inherit"
          onChange={(_event, val) => onTabChange(val)}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
          {locations?.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem?.name}
              value={tabItem?.name}
            />
          ))}
        </Tabs>

        <DataTable3
          PageAction={
            <>
              <PageAction
                showDeleteButton={false}
                showEditButton={false}
                hideFilter
                selectedItems={selectedItems}
                title={'Vehicle Summary'}
                options={options}
                setOptions={setOptions}
                total={totalItems}
                dialogTitle={''}
                deleteTitle={''}
                customComponent={customComponent}
                searchComponentSlot={
                  <FormControlLabel
                    sx={{ ml: 1 }}
                    style={{ fontSize: '12px' }}
                    labelPlacement="start"
                    control={
                      <Switch
                        className="mySwitch"
                        checked={options?.inventory}
                        onChange={() => {
                          localStorage.setItem(
                            'inventory',
                            `${!options?.inventory}`,
                          );
                          setOptions({
                            ...options,
                            inventory: !options?.inventory,
                          });
                        }}
                      />
                    }
                    label="Inventory"
                  />
                }
                showExactMutch={false}
              />
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                {loading ? (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      minHeight: '36px',
                      px: 4,
                      fontSize: 12,
                    }}
                  >
                    Loading...
                  </Box>
                ) : (
                  <Tabs
                    variant="scrollable"
                    scrollButtons
                    allowScrollButtonsMobile
                    aria-label="scrollable force tabs example"
                    centered={true}
                    sx={{ minHeight: '32px', justifyContent: 'center' }}
                  >
                    {activeVehicles
                      .sort((a, b) => b.count - a.count)
                      ?.map((item) => (
                        <Tab
                          key={item?.destination.id}
                          href={`/vehicles/all?filterData=${JSON.stringify({
                            carstate: [
                              'on_the_way',
                              'on_hand_no_title',
                              'on_hand_with_title',
                            ],
                            OR: [
                              {
                                ['containers.bookings.port_of_discharge']: {
                                  in: [item?.destination.id],
                                },
                                ['destination_id']: {
                                  in: [item?.destination.id],
                                },
                              },
                              {
                                destination_id: { in: [item?.destination.id] },
                              },
                              {
                                ['companies.destination_id']: {
                                  in: [item?.destination.id],
                                },
                              },
                            ],
                            ...(options.tab != 'all'
                              ? {
                                  point_of_loading: {
                                    in: [
                                      locations?.filter((item) => {
                                        if (item.name == options.tab) {
                                          return item.id;
                                        }
                                      })[0]?.id,
                                    ],
                                  },
                                }
                              : {}),
                            ...(router.query.yard_location
                              ? {
                                  yard_location_id: {
                                    in: [+router.query.yard_location],
                                  },
                                }
                              : {}),
                          })}`}
                          LinkComponent={Link}
                          sx={{
                            maxWidth: 'unset !important',
                            minWidth: 'unset !important',
                            width: 'unset !important',
                            fontSize: 12,
                            px: 1,
                            py: 0.5,
                            minHeight: '36px',
                          }}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {item?.destination.name}{' '}
                              <Box
                                sx={{
                                  ml: 1,
                                  bgcolor:
                                    destinationColor[item?.destination.id] ??
                                    '#50af55',
                                  color:
                                    destinationTextColor[
                                      item?.destination.id
                                    ] ?? 'white',
                                  fontSize: 12,
                                  fontWeight: 700,
                                  px: 0.5,
                                  borderRadius: 5,
                                  position: 'relative',
                                }}
                              >
                                {item?.count}
                              </Box>
                            </Box>
                          }
                          disableRipple={true}
                        />
                      ))}
                  </Tabs>
                )}
              </Box>
            </>
          }
          // start default props
          hidePagination
          // selectedFun={(item) => handleCompanyPort(item)}
          // unSelectedFun={() => setShowNoteButton(false)}
          options={options}
          setSelectedItems={setSelectedItems}
          selectedItems={selectedItems}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={data}
          setItems={setData}
          headers={checkHeader()}
          activeTab={options.tab}
          tableName="vehicleSummary"
          //start custom props
          {...mergedDataTableColumn}
        />
      </Container>
      <EmailAndExcelInventory
        state={state}
        show={showEmailModal}
        setShow={setShowEmailModal}
        type={type}
        selectedItems={selectedItems[0]}
        vehicleCounts={vehicleCounts}
      />
      <LoadNoteInventory
        show={showLoadNoteModal}
        setShow={setShowLoadNoteModal}
        loadNoteField={
          idLocation
            ? loadNoteField.filter((item) => item?.location_id == idLocation)
            : loadNoteField
        }
      />
      <MultiLoadNoteInventory
        show={showMultiLoadNoteModal}
        setShow={setShowMultiLoadNoteModal}
        selectedItems={selectedItems}
        locations={
          options.tab == 'all'
            ? locations
            : locations.filter((item) => item?.id == idLocation)
        }
      />
      <SettingModal
        show={showSettings}
        setShow={setShowSettings}
        setSettings={setSettings}
      />
    </>
  );
};

export default VehicleSummary;
