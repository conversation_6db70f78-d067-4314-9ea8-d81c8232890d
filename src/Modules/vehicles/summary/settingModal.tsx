import {
  Button,
  Card,
  CardActions,
  CardContent,
  Checkbox,
  FormControlLabel,
  Grid,
  IconButton,
  Modal,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { z } from 'zod';
import { useEffect, useState } from 'react';

export const schema = z.object({});

const SettingModal = ({ setShow, show, setSettings }) => {
  const [localSettings, setLocalSettings] = useState({
    auction: JSON.parse(localStorage.getItem('auction-setting')) ?? true,
    onTheWay: JSON.parse(localStorage.getItem('onTheWay-setting')) ?? true,
    onHandNoTitle:
      JSON.parse(localStorage.getItem('onHandNoTitle-setting')) ?? true,
    onHandWithTitle:
      JSON.parse(localStorage.getItem('onHandWithTitle-setting')) ?? true,
  });

  useEffect(() => {
    setLocalSettings({
      auction: JSON.parse(localStorage.getItem('auction-setting')) ?? true,
      onTheWay: JSON.parse(localStorage.getItem('onTheWay-setting')) ?? true,
      onHandNoTitle:
        JSON.parse(localStorage.getItem('onHandNoTitle-setting')) ?? true,
      onHandWithTitle:
        JSON.parse(localStorage.getItem('onHandWithTitle-setting')) ?? true,
    });
  }, [show]);

  const handleChange = (event) => {
    const { name, checked } = event.target;
    setLocalSettings((prevSettings) => {
      const newSettings = { ...prevSettings, [name]: checked };
      localStorage.setItem(`${name}-setting`, JSON.stringify(checked));
      setSettings(newSettings); // Update parent state
      return newSettings;
    });
  };

  const onSubmit = async (event) => {
    event.preventDefault();
    setShow(false);
  };

  return (
    <Modal
      onClose={() => setShow(false)}
      aria-labelledby="customized-dialog-title"
      open={show}
    >
      <Card
        style={{
          position: 'absolute' as 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '28vw',
          minHeight: '30vh',
          maxHeight: '50vh',
          borderRadius: '5px',
        }}
      >
        <IconButton
          aria-label="close"
          onClick={() => {
            setShow(false);
          }}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
        <Typography variant="h6" sx={{ padding: '15px 15px 10px 15px' }}>
          Column Colors Visibility
        </Typography>
        <hr />

        <form onSubmit={onSubmit}>
          <CardContent>
            <Grid
              container
              rowSpacing={0}
              columnSpacing={{ xs: 1, sm: 1, md: 1 }}
            >
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="auction"
                      onChange={handleChange}
                      checked={localSettings.auction}
                    />
                  }
                  label="Auction"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="onTheWay"
                      onChange={handleChange}
                      checked={localSettings.onTheWay}
                    />
                  }
                  label="On the way"
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="onHandNoTitle"
                      onChange={handleChange}
                      checked={localSettings.onHandNoTitle}
                    />
                  }
                  label="On hand no title"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="onHandWithTitle"
                      onChange={handleChange}
                      checked={localSettings.onHandWithTitle}
                    />
                  }
                  label="On hand with title"
                />
              </Grid>
            </Grid>
          </CardContent>
          <CardActions sx={{ position: 'absolute', bottom: 4, right: 8 }}>
            <Button
              loading={false}
              color="warning"
              variant="contained"
              onClick={() => {
                setShow(false);
              }}
            >
              Close
            </Button>
            <Button
              loading={false}
              color="primary"
              type="submit"
              variant="contained"
            >
              Save
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
};

export default SettingModal;
