import {
  <PERSON><PERSON>,
  <PERSON>,
  CardActions,
  CardContent,
  IconButton,
  Modal,
  TextField,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { object, string } from 'zod';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import moment from 'moment';

const getSchema = () =>
  object({
    note: string().optional(),
    note2: string().optional(),
    subject: string().optional(),
  });

const EmailAndExcelInventory = ({
  show,
  setShow,
  type = 'email',
  selectedItems,
  state,
  vehicleCounts,
  downloadType = '',
  downloadSummary,
}: {
  show: boolean;
  setShow: (value: boolean) => void;
  type?: string;
  selectedItems: any;
  state: any;
  vehicleCounts: any;
  downloadType?: string;
  downloadSummary?: (note: string) => void; // ✅ Make it optional and accept an argument
}) => {
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [loadingPdfBtn, setLoadingPdfBtn] = useState(false);
  const [loadingImageBtn, setLoadingImageBtn] = useState(false);

  const formated = () => {
    const result = vehicleCounts?.data
      ?.map((item) => {
        return `<p>${item.location} (${item.count}) vehicles</p>`;
      })
      .join('');
    return result;
  };

  const {
    control,
    handleSubmit,
    // formState: { errors },
    reset,
    setValue,
    getValues,
  } = useForm({
    resolver: zodResolver(getSchema()),
    defaultValues: {
      // subject: 'Inventory List',
      note: `<p>Our goal is to provide fast loading services. If you can help us in obtaining titles for your cars without title or have any other questions, please don't hesitate to reach out!</p>`,
      note2: '',
    },
  });

  useEffect(() => {
    if (state === 'on_hand_with_title') {
      setValue(
        'note2',
        `<p>Dear customer, </p><p>Above is the list of your With Title Vehicles with all their facts and information. </p><br>${formated()}<br> <p> Total: ${selectedItems?.on_hand_with_title} Cars </p><br> Appreciate your attention on this to help us load your cars fast by buying more Small cars or Allowing us to load your cars in Mix Containers or processing combinations of 3 Vehicles.`,
      );
    } else if (state === 'on_hand_no_title') {
      setValue(
        'note2',
        `<p>Dear customer, </p><p>Above is the list of your No-Title Vehicles with all their facts and information. </p><br>${formated()}<br> <p> Total: ${selectedItems?.on_hand_no_title} Cars </p><br> <p>We appreciate your attention to this.</p><br> <ul><li><p>With access to your account, we can promptly update the title mailing instructions upon receiving your vehicle. </p></li> <li><p>If we have your account information (owner name, license number), we can efficiently follow your vehicle titles at the auction and swiftly mail the titles to our Locations.</p></li> <li><p>For accounts to which we do not have access, it is crucial for the customer to update the mailing instructions for the title upon receiving the notice in the WhatsApp group or email. This ensures that we can avoid any unnecessary delays.</p></li></ul> `,
      );
    } else if (state === 'on_the_way') {
      setValue(
        'note2',
        `<p><strong>On The Way Vehicles- PGL</strong></p><br> <p>Hello Dear Customer</p><p>The above report covers all of your company's On The Way vehicle(s) to PGL with all its facts. If you have any question regarding that let us know please. </p><br><p><strong>Dispatch Department- PGL</strong></p><br>${formated()}<br><p>Total: ${selectedItems?.on_the_way} Vehicles</p>`,
      );
    } else {
      setValue('note2', '');
    }
  }, [vehicleCounts]);

  const handleEmailExcel = (data) => {
    if (type == 'email') emailToCustomerInventory(data);
    else downloadExcelInventory(data);
  };

  const emailToCustomerInventory = async (data) => {
    try {
      setLoadingBtn(true);
      await axios.post('/vehicles/email-inventory-excel', {
        company_id: selectedItems?.id,
        state: state,
        ...data,
      });
      setShow(false);
      setLoadingBtn(false);
    } catch (error) {
      console.error('Error submitting form:', error);
      setLoadingBtn(false);
    }
  };

  const downloadExcelInventory = async (data) => {
    try {
      setLoadingBtn(true);
      const response = await axios.post(
        '/vehicles/download-inventory-excel',
        { company_id: selectedItems?.id, note: data.note2, state: state },
        { responseType: 'blob' },
      );
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = `${selectedItems.name} inventory ${moment().format(
        'YYYY-MM-DD',
      )}.xlsx`;
      link.click();
      setLoadingBtn(false);
      setShow(false);
    } catch (error) {
      setLoadingBtn(false);
      console.error('Error submitting form:', error);
    }
  };

  const downloadGeneratedPdf = async () => {
    try {
      setLoadingPdfBtn(true);
      const response = await axios.post(
        '/vehicles/download-inventory-pdf',
        {
          company_id: selectedItems?.id,
          state: state,
          note: getValues('note2'),
        },
        { responseType: 'blob' },
      );
      const blob = new Blob([response.data], {
        type: 'application/pdf', // Corrected content type for PDF
      });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = `${selectedItems.name} inventory ${moment().format(
        'YYYY-MM-DD',
      )}.pdf`;
      link.click();
      setLoadingPdfBtn(false);
      setShow(false);
    } catch (error) {
      setLoadingPdfBtn(false);
      console.error('Error submitting form:', error);
    }
  };

  const downloadGeneratedImage = async () => {
    try {
      setLoadingImageBtn(true);
      const response = await axios.post(
        '/vehicles/download-inventory-image',
        {
          company_id: selectedItems?.id,
          state: state,
          note: getValues('note2'),
        },
        { responseType: 'blob' },
      );
      const blob = new Blob([response.data], {
        type: 'image/png', // Corrected content type for PDF
      });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = `${selectedItems.name} inventory ${moment().format(
        'YYYY-MM-DD',
      )}.png`;
      link.click();
      setLoadingImageBtn(false);
      setShow(false);
    } catch (error) {
      setLoadingImageBtn(false);
      console.error('Error submitting form:', error);
    }
  };

  const onSubmit = (data) => {
    handleEmailExcel(data);
    reset();
  };

  return (
    <Modal
      onClose={() => setShow(false)}
      aria-labelledby="customized-dialog-title"
      open={show}
    >
      <Card
        style={{
          position: 'absolute' as 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '50vw',
        }}
      >
        <IconButton
          aria-label="close"
          onClick={() => setShow(false)}
          sx={{ position: 'absolute', right: 8, top: 5 }}
        >
          <CloseIcon />
        </IconButton>

        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent sx={{ mt: 3, height: '80% !important' }}>
            {/* {showSubjectField && (
              <Controller
                name="subject"
                control={control}
                render={({ field }) => (
                  <>
                    <TextField
                      {...field}
                      error={Boolean(errors.subject)}
                      helperText={errors.subject?.message || ''}
                      sx={{ mb: 1.5 }}
                      label="Subject"
                      variant="outlined"
                      fullWidth
                      size="small"
                    />
                  </>
                )}
              />
            )} */}

            {type == 'email' ? (
              <Controller
                name="note"
                control={control}
                render={({ field }) => (
                  <>
                    <QuillEditor
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                      height={200}
                      placeholder="Note"
                    />
                  </>
                )}
              />
            ) : (
              <>
                {state == 'on_hand_with_title' ||
                state == 'on_hand_no_title' ||
                'on_the_way' ? (
                  <Controller
                    name="note2"
                    control={control}
                    render={({ field }) => (
                      <>
                        <QuillEditor
                          onChange={(value) => {
                            field.onChange(value);
                          }}
                          value={field.value}
                          placeholder="Note"
                        />
                      </>
                    )}
                  />
                ) : (
                  <Controller
                    name="note2"
                    control={control}
                    render={({ field }) => (
                      <>
                        <TextField
                          {...field}
                          label="Note"
                          variant="outlined"
                          fullWidth
                          size="small"
                          minRows={10}
                          multiline
                        />
                      </>
                    )}
                  />
                )}
              </>
            )}
          </CardContent>
          {downloadType == 'downloadSummary' ? (
            <CardActions sx={{ justifyContent: 'end', mx: 2, mb: 2, mt: 2 }}>
              <Button
                loading={loadingPdfBtn}
                color="success"
                onClick={() => downloadSummary(getValues('note2'))}
                variant="contained"
              >
                Add Note
              </Button>
            </CardActions>
          ) : (
            <CardActions sx={{ justifyContent: 'end', mx: 2, mb: 2 }}>
              {type == 'download' && (
                <Button
                  loading={loadingImageBtn}
                  color="success"
                  onClick={() => downloadGeneratedImage()}
                  variant="contained"
                >
                  Download Image
                </Button>
              )}
              {type == 'download' && (
                <>
                  <Button
                    loading={loadingPdfBtn}
                    color="success"
                    onClick={() => downloadGeneratedPdf()}
                    variant="contained"
                  >
                    Download pdf
                  </Button>
                </>
              )}
              <Button
                loading={loadingBtn}
                color={type == 'email' ? 'primary' : 'success'}
                type="submit"
                variant="contained"
              >
                {type == 'email' ? 'Send Email' : 'Download Excel'}
              </Button>
            </CardActions>
          )}
        </form>
      </Card>
    </Modal>
  );
};

export default EmailAndExcelInventory;
