import Step1 from '@/Modules/shipment/steps/Step1';
import { createSchema } from '@/configs/shipment/shipmentHeader';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Avatar,
  Backdrop,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Fade,
  Grid,
  IconButton,
  Modal,
  Typography,
  useMediaQuery,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { styled, useTheme } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import ContainerNumberConfrimationDialog from '@/configs/shipment/ContainerNumberConfrimationDialog';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';

const Root = styled('div')(({ theme }) => ({
  display: 'flex',
  [theme.breakpoints.up('md')]: {
    width: '90vw',
    height: '70vh',
  },
  [theme.breakpoints.down('md')]: {
    width: '95%',
    flexWrap: 'wrap',
  },
}));

const AddAndCreateLoad = ({
  open,
  onDeny,
  selectedItems,
  fetchTableRecord,
}) => {
  const [isYardExist, setIsYardExist] = useState(false);
  const theme = useTheme();
  const matches = useMediaQuery(theme.breakpoints.up('md'));
  const [checkContainerNumber, setCheckContainerNumber] = useState(false);
  const [checkContainerNumberDone, setCheckContainerNumberDone] =
    useState(false);
  const [checkContainerNumberDialog, setCheckContainerNumberDialog] =
    useState(false);
  const [openConfirm2, setOpenConfirm2] = useState(false);
  const [openConfirm3, setOpenConfirm3] = useState(false);
  const [checkVehicleDesc, setCheckVehicleDesc] = useState(false);
  const [bookingDestination, setBookingDestination] = useState();
  const [listCompanies, setListCompanies] = useState([]);
  const [isInstractionLoading, setIsInstractionLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    setValue,
    control,
    setError,
    getValues,
    watch,
  } = useForm({
    resolver: zodResolver(createSchema),
    defaultValues: {
      booking_id: null,
      no_units_load: null,
      yard_location_id: null,
      shipping_document_id: null,
      company_id: null,
      container_number: null,
      clearance_invoice_link: null,
      pin_out: null,
      ingate: null,
      loading_date: null,
      actions: null,
      loading_instruction: null,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    setValue,
    watch,
    control,
    getValues,
    setIsYardExist,
    handleSubmit,
  };

  const digitsAfterLetters = watch('container_number')?.slice(4); // Extract characters after the first 4 letters
  const hasMoreThan7Digits = /^\d{8,}$/.test(digitsAfterLetters);
  const validate = async () => {
    const isValid = await trigger([
      'booking_id',
      'shipping_document_id',
      'loading_date',
    ]);

    if (isYardExist) {
      setError('yard_location_id', {
        type: 'custom',
        message: 'Yard Location is Required!',
      });
    } else {
      clearErrors('yard_location_id');
    }

    return isValid && !isYardExist;
  };
  const containerNumberCondition = () => {
    return hasMoreThan7Digits
      ? checkContainerNumberDone
        ? true
        : false
      : true;
  };
  const [loadingButton, setLoadingButton] = useState(false);
  const submit = async (_data) => {
    const values = getValues();

    const sendValue = {
      ...values,
      vehiclesIds: selectedItems.map((item) => item.id),
      //@ts-ignore
      booking_suffix: String(values.booking_suffix),
    };
    // const Result = await validate();
    if (hasMoreThan7Digits && checkContainerNumberDone == false) {
      setCheckContainerNumberDialog(true);
      setCheckContainerNumber(true);
    }
    if (validate) {
      if (!checkContainerNumber && containerNumberCondition()) {
        try {
          setLoadingButton(true);
          const { data } = await axios.patch(
            '/vehicles/createLoadAddToContainer',
            sendValue,
          );
          if (data.result == true) {
            onDeny(false);
            fetchTableRecord();
            toast.success('Successfully create load and add vehicles to that ');
            setLoadingButton(false);
            reset();
            setValue('shipping_document_id', 134);
            return true;
          } else {
            setLoadingButton(false);
            reset();
            setValue('shipping_document_id', 134);

            return false;
          }
        } catch (error) {
          reset();
          setValue('shipping_document_id', 134);
          setLoadingButton(false);
        }
      }
    }
  };
  const getLoadType = () => {
    const listCompanies = [
      { id: 1062, name: 'UNITED UAE SCRAP' },
      { id: 806, name: 'United Unstoppable Car Auction L L C Poti Georgia' },
      { id: 577, name: 'UNITED UNSTOPPABLE CAR AUCTION L L C OMAN' },
      { id: 158, name: 'UNITED UNSTOPPABLE CAR AUCTION L L C' },
    ];

    // Combine selectedItems with listCompanies if condition is met
    const company_ids = selectedItems.reduce(
      (acc, { company_id, companies, customers, destination_id, is_scrap }) => {
        acc[company_id] = companies.name;
        // if (load_type === 'mix') {
        const dest = customers?.companies?.destinations?.id;
        if (is_scrap) {
          acc[1062] = listCompanies.find(
            (company) => company.id === 1062,
          )?.name;
        } else if (destination_id === 22 || dest === 22) {
          acc[806] = listCompanies.find((company) => company.id === 806)?.name;
        } else if (destination_id === 24 || dest === 24) {
          acc[577] = listCompanies.find((company) => company.id === 577)?.name;
        } else {
          acc[158] = listCompanies.find((company) => company.id === 158)?.name;
        }
        // }
        return acc;
      },
      {},
    );

    return Object.keys(company_ids).map((key) => ({
      id: key,
      label: company_ids[key],
    }));
  };

  useEffect(() => {
    setListCompanies(getLoadType());
  }, []);
  useEffect(() => {
    const checkVehicleDescVar = selectedItems.some(
      (item) =>
        (item?.make?.includes('HYUNDAI') &&
          item?.model?.includes('SANTA FE')) ||
        item?.model?.includes('HYBRID'),
    );
    setCheckVehicleDesc(checkVehicleDescVar);

    const hasScrap = selectedItems.every(
      (item) =>
        item?.is_scrap === true &&
        selectedItems[0]?.customers?.companies?.destinations?.id == 24,
    );

    if (hasScrap) {
      setValue('company_id', 1062);
    } else if (
      selectedItems[0]?.load_type == 'mix' &&
      selectedItems[0]?.halfcut_status == 'completed' &&
      selectedItems[0]?.customers?.companies?.destinations?.id == 12
    ) {
      setValue('company_id', 158);
    } else if (
      selectedItems[0]?.load_type == 'mix' &&
      selectedItems[0]?.halfcut_status == 'completed' &&
      selectedItems[0]?.customers?.companies?.destinations?.id == 24
    ) {
      setValue('company_id', 577);
    } else if (
      selectedItems[0]?.load_type == 'mix' &&
      selectedItems[0]?.halfcut_status == 'completed' &&
      selectedItems[0]?.customers?.companies?.destinations?.id == 22
    ) {
      setValue('company_id', 806);
    } else setValue('company_id', selectedItems[0]?.company_id);

    setValue('yard_location_id', selectedItems[0]?.yard_location_id);
  }, [selectedItems]);

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: ` ${theme.colors.alpha.white[100]}`,
    border: `2px solid ${theme.colors.alpha.black[50]}`,
    boxShadow: 24,
    borderRadius: '10px',
  };

  const bookingId = watch('booking_id');

  useEffect(() => {
    if (bookingId) {
      getBookingDestination();
    }
  }, [bookingId]);

  const getBookingDestination = async () => {
    try {
      const res = await axios.get(
        `bookings/getBookingDestination/${bookingId}`,
      );
      setBookingDestination(res.data.port_of_discharge);
    } catch (error) {}
  };

  const addAndCreate = () => {
    const destination_id =
      selectedItems[0]?.destination_id ??
      selectedItems[0]?.customers?.companies?.destinations?.id;
    if (destination_id != bookingDestination) {
      setOpenConfirm3(true);
    } else {
      handleSubmit(submit)();
    }
  };

  return (
    <>
      <Modal
        open={open}
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        closeAfterTransition
        slots={{ backdrop: Backdrop }}
        slotProps={{
          backdrop: {
            timeout: 500,
          },
        }}
      >
        <Fade in={open}>
          <Root sx={{ ...style }}>
            <Box sx={{ p: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  width: '100%',
                  justifyContent: 'space-between',
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="h5"
                  sx={{ fontSize: '18px', fontWeight: 'bold' }}
                >
                  Add to Container and Create Load
                </Typography>
                <IconButton
                  onClick={() => {
                    reset();
                    onDeny(false);
                  }}
                  aria-label="delete"
                >
                  <CloseIcon />
                </IconButton>
              </Box>
              <Grid container>
                <Grid
                  sx={{
                    padding: '10px',
                  }}
                  style={
                    matches
                      ? { borderRight: '1px solid lightgray' }
                      : { borderRight: 'unset' }
                  }
                  size={{
                    xs: 12,
                    sm: 12,
                    md: 6,
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Selected Vehicles
                  </Typography>
                  <Box
                    sx={{ overflowY: 'auto' }}
                    style={matches ? { height: '55vh' } : { height: '25vh' }}
                  >
                    {selectedItems.map((data, index) => {
                      return (
                        <Card key={index} sx={{ my: 1 }}>
                          <CardHeader
                            avatar={
                              <Avatar
                                sx={{
                                  bgcolor: 'text.primary',
                                  width: '25px',
                                  height: '25px',
                                  fontSize: '13px',
                                }}
                                aria-label="recipe"
                              >
                                {index + 1}
                              </Avatar>
                            }
                            title={
                              data?.year +
                              ' ' +
                              data?.make +
                              ' ' +
                              data?.model +
                              ' ' +
                              data?.color
                            }
                            subheader={`Vin: ${data.vin}  lot#: ${data.lot_number}`}
                          />

                          <CardContent sx={{ marginTop: '-15px' }}>
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Company Name
                              </Box>
                              <Box>{data?.customers?.companies?.name}</Box>
                            </Box>
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Company destination
                              </Box>
                              <Box>
                                {data?.customers?.companies?.destinations?.name}
                              </Box>
                            </Box>
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Point of Loading
                              </Box>
                              <Box>{data?.pol_locations?.name}</Box>
                            </Box>

                            <Box
                              display="flex"
                              justifyContent="space-between"
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Point Of Destination
                              </Box>
                              <Box>{data?.destinations?.name}</Box>
                            </Box>
                            {data?.yards_location?.name ? (
                              <Box
                                display="flex"
                                justifyContent="space-between"
                                // mb={2}
                                sx={{ borderBottom: '1px dotted' }}
                              >
                                <Box sx={{ fontWeight: 'bold' }}>
                                  Yard Location
                                </Box>
                                <Box>{data?.yards_location?.name}</Box>
                              </Box>
                            ) : (
                              ''
                            )}
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>Price</Box>
                              <Box>{data?.price}</Box>
                            </Box>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Box>
                </Grid>
                <Grid
                  sx={{
                    padding: '10px',
                  }}
                  size={{
                    xs: 12,
                    sm: 12,
                    md: 6,
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Create Load
                  </Typography>
                  <Box
                    sx={{ overflowY: 'auto' }}
                    style={matches ? { height: '55vh' } : { height: '25vh' }}
                  >
                    <Step1
                      bookingURL={`bookings/getBookingForAddVehicle/?destination=${
                        selectedItems[0]?.destination_id == undefined
                          ? selectedItems[0]?.customers?.companies?.destinations
                              ?.id
                          : selectedItems[0]?.destination_id
                      }&point_of_loading=${
                        selectedItems[0]?.point_of_loading == undefined
                          ? 1
                          : selectedItems[0]?.point_of_loading
                      }`}
                      form={form}
                      isUpdate={false}
                      yardLocation={true}
                      selectedItem={selectedItems}
                      onHandWithTitles={true}
                      listOfCompanies={listCompanies}
                      isInstractionLoading={isInstractionLoading}
                      setIsInstractionLoading={setIsInstractionLoading}
                    />
                  </Box>
                </Grid>
              </Grid>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'end',
                  width: '100%',
                }}
              >
                <Button
                  disabled={loadingButton}
                  endIcon={<SendIcon />}
                  variant="contained"
                  type="submit"
                  color="success"
                  sx={{ mx: '2.5px' }}
                  onClick={() =>
                    checkVehicleDesc ? setOpenConfirm2(true) : addAndCreate()
                  }
                  loading={loadingButton}
                  loadingPosition="end"
                >
                  <span>Add</span>
                </Button>

                <Button
                  startIcon={<CloseIcon />}
                  color="error"
                  onClick={() => {
                    form.reset();
                    onDeny(false);
                  }}
                >
                  Close
                </Button>
              </Box>
            </Box>
          </Root>
        </Fade>
      </Modal>
      <ContainerNumberConfrimationDialog
        open={checkContainerNumberDialog}
        setOpen={setCheckContainerNumberDialog}
        setCheckContainerNumber={setCheckContainerNumber}
        setCheckContainerNumberDone={setCheckContainerNumberDone}
      />
      <AppConfirmDialog
        open={openConfirm2}
        onDeny={() => {
          setOpenConfirm2(false);
        }}
        onConfirm={handleSubmit(submit)}
        title={`${selectedItems.length == 1 ? 'this' : 'these'}  ${
          selectedItems.length == 1 ? 'vehicle' : 'vehicles'
        } are Hybrid or HYUNDAI SANTA FE, Do you want to add to this container?`}
        dialogTitle={`Are you sure to add ${
          selectedItems.length == 1 ? 'this' : 'these'
        }  ${
          selectedItems.length == 1 ? 'vehicle' : 'vehicles'
        } are Hybrid or HYUNDAI SANTA FE`}
        confirmText="Approve"
        cancelText="Cancel"
        maxWidth="sm"
      />
      {openConfirm3 && (
        <AppConfirmDialog
          open={openConfirm3}
          onDeny={() => {
            setOpenConfirm3(false);
          }}
          onConfirm={async () => {
            await handleSubmit(submit)();
            setOpenConfirm3(false);
          }}
          title={`${selectedItems.length == 1 ? 'this vehicle' : 'these vehicles'} are not have the same destination with the selected booking.`}
          dialogTitle={`Are you sure to add ${
            selectedItems.length == 1 ? 'this vehicle' : 'these vehicles'
          } to this booking`}
          confirmText="Approve"
          cancelText="Cancel"
          maxWidth="sm"
        />
      )}
    </>
  );
};

export default AddAndCreateLoad;
