import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import { Badge, Box, Button, IconButton } from '@mui/material';
import { toast } from 'react-toastify';
import TimeToLeaveIcon from '@mui/icons-material/TimeToLeave';
import DirectionsBoatFilledIcon from '@mui/icons-material/DirectionsBoatFilled';
import InventoryIcon from '@mui/icons-material/Inventory';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange';
import CarRentalIcon from '@mui/icons-material/CarRental';
import NoCrashIcon from '@mui/icons-material/NoCrash';
import MinorCrashIcon from '@mui/icons-material/MinorCrash';
import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { useContext } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import { CarCrash } from '@mui/icons-material';
import SimCardDownloadIcon from '@mui/icons-material/SimCardDownload';

export const AuctionButtonVehicles = ({
  tab,
  setAddToContainer,
  setOpenConfirm,
  checkStatusPerm,
  setAddToPglu,
  setAddToDone,
  setAddToArrived,
  setAddToUnitedTrading,
  setPgluType,
  selectedItems,
  setAddToDamage,
  //@ts-ignore
  setAddToLoad,
  setOpenTransaction,
  checkTransactionPerm,
  checkAddToUnitedTradingPerm = false,
  onAddStorage,
  showStorage,
  setShowTransferLetterDownload,
}) => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const addToContainer = () => {
    const checkPoint = selectedItems.filter((data) => {
      if (selectedItems[0].point_of_loading != data.point_of_loading) {
        return data;
      }
    });

    const checkCompanyYardLocationWhen = selectedItems.filter((data) => {
      if (selectedItems[0]?.yards_location?.id != data?.yards_location?.id) {
        return data;
      }
    });

    const sameDestination = selectedItems.every(
      //@ts-ignore
      (destination, i, array) =>
        destination.destination_id === array[0].destination_id,
    );

    if (
      checkPoint.length <= 0 &&
      sameDestination &&
      checkCompanyYardLocationWhen <= 0
    ) {
      setAddToContainer(true);
    } else {
      if (checkCompanyYardLocationWhen.length > 0) {
        toast.warn('please select those items that have same yard location.');
      }
      if (!sameDestination) {
        toast.warn(
          'please select those items that have same point of destinations (POD).',
        );
      }
      if (checkPoint.length > 0) {
        toast.warn(
          'please select those items that have same point of loading.',
        );
      }
    }
  };

  const addAndCreateLoad = () => {
    const checkPoint = selectedItems.filter((data) => {
      if (selectedItems[0].point_of_loading != data.point_of_loading) {
        return data;
      }
    });
    const checkCompanyYardLocationWhenCreateLoad = selectedItems.filter(
      (data) => {
        if (selectedItems[0]?.yards_location?.id != data?.yards_location?.id) {
          return data;
        }
      },
    );

    const sameDestination = selectedItems.every(
      //@ts-ignore
      (destination, i, array) =>
        destination.destination_id === array[0].destination_id,
    );

    if (
      checkPoint.length <= 0 &&
      checkCompanyYardLocationWhenCreateLoad <= 0 &&
      sameDestination
    ) {
      setAddToLoad(true);
    } else {
      if (!sameDestination) {
        toast.warn(
          'please select those items that have same point of destination (POD).',
        );
      }

      if (checkCompanyYardLocationWhenCreateLoad.length > 0) {
        toast.warn('please select those items that have same yard location.');
      }
      if (checkPoint.length > 0) {
        toast.warn(
          'please select those item that have same point of loading. ',
        );
      }
    }
  };

  return (
    <Box style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
      {tab == 'all' && selectedItems?.length && (
        <>
          <AppTooltip key={'addDamage'} title={'Add to Damage'}>
            <IconButton
              key={'addDamage'}
              color="secondary"
              // onClick={AddToDamage}
              onClick={() => {
                setAddToDamage(true);
              }}
            >
              <CarCrash />
            </IconButton>
          </AppTooltip>
        </>
      )}
      {tab == 'on_hand_with_title' && (
        <>
          <Button onClick={addAndCreateLoad} color="secondary">
            Add Load
          </Button>
          <AppTooltip key={'addContainer'} title={'Add to container'}>
            <IconButton
              key={'addContainer'}
              color="secondary"
              onClick={addToContainer}
            >
              <DirectionsBoatFilledIcon />
            </IconButton>
          </AppTooltip>
        </>
      )}
      {tab === 'in_process' &&
        perms?.includes(VEHICLES?.TITLE_TRACKING_ADD) &&
        selectedItems?.length == 1 && (
          <AppTooltip key={'send_title_tracking'} title={'Add Title Tracking'}>
            <IconButton
              color="secondary"
              onClick={() => {
                setAddToDone(true);
              }}
            >
              <MinorCrashIcon />
            </IconButton>
          </AppTooltip>
        )}
      {tab === 'send' && (
        <AppTooltip key={'arrived_vehicles'} title={'Add to Arrived'}>
          <IconButton
            color="secondary"
            onClick={() => {
              setAddToArrived(true);
            }}
          >
            <NoCrashIcon />
          </IconButton>
        </AppTooltip>
      )}
      {checkAddToUnitedTradingPerm && (
        <AppTooltip
          key={'united_trading_vehicle'}
          title={'Add to United Trading'}
        >
          <IconButton
            color="secondary"
            onClick={() => {
              setAddToUnitedTrading(true);
            }}
          >
            <CarRentalIcon />
          </IconButton>
        </AppTooltip>
      )}

      <AppTooltip key={'PGLU_vehicle'} title={'Add to PGLU'}>
        <IconButton
          color="secondary"
          onClick={() => {
            const hasPGLU = selectedItems.some(
              (item) => item?.pgl_used_cars?.length > 0,
            );
            if (!hasPGLU) {
              setAddToPglu(true);
              setPgluType('pglu');
            }
            if (hasPGLU) {
              toast.warn('Already added to PGLU');
            }
          }}
        >
          <TimeToLeaveIcon />
        </IconButton>
      </AppTooltip>

      <AppTooltip
        key={'PGLU_inventory_vehicle'}
        title={'Add to PGLU Inventory'}
      >
        <IconButton
          color="secondary"
          onClick={() => {
            const hasAllContainers = selectedItems.every(
              (item) => item?.containers?.id,
            );
            const hasYardInventory = selectedItems.some(
              (item) => item?.yard_inventories?.length > 0,
            );

            if (hasAllContainers && !hasYardInventory) {
              setAddToPglu(true);
              setPgluType('pglu_inventory');
            }
            if (!hasAllContainers) {
              toast.warn('Container does not assigned to this vehicle.');
            }
            if (hasYardInventory) {
              toast.warn('Already added to Inventory');
            }
          }}
        >
          <InventoryIcon />
        </IconButton>
      </AppTooltip>
      {selectedItems?.length == 1 && showStorage && (
        <AppTooltip key={'Storage_form'} title={'Add Storage'}>
          <IconButton
            color="secondary"
            onClick={() => {
              onAddStorage();
            }}
          >
            <WarehouseIcon />
          </IconButton>
        </AppTooltip>
      )}
      {selectedItems?.length === 1 && setShowTransferLetterDownload && (
        <AppTooltip
          key={'transfer_letter_download'}
          title={'Download Transfer Letter'}
        >
          <IconButton
            color="primary"
            onClick={() => {
              setShowTransferLetterDownload(true);
            }}
          >
            <SimCardDownloadIcon />
          </IconButton>
        </AppTooltip>
      )}
      {[
        'on_hand_with_title',
        'on_hand_no_title',
        'pending',
        'all',
        'shipped',
        'on_the_way',
        'auction_unpaid',
        'auction_paid',
      ].includes(tab) &&
        checkStatusPerm && (
          <AppTooltip key={'changeVehiclesStatus'} title={'Change Status'}>
            <IconButton
              color="primary"
              onClick={() => {
                setAddToContainer(false);
                setOpenConfirm(true);
              }}
            >
              <PublishedWithChangesIcon />
            </IconButton>
          </AppTooltip>
        )}
      {selectedItems.length == 1 &&
        !selectedItems[0].transaction_at &&
        checkTransactionPerm && (
          <AppTooltip title={'Add Transaction'}>
            <Badge
              sx={{ fontSize: '5px' }}
              badgeContent={'Tx'}
              color="primary"
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              className="cBadge"
            >
              <IconButton
                color="success"
                onClick={() => {
                  setOpenTransaction(true);
                }}
              >
                <CurrencyExchangeIcon />
              </IconButton>
            </Badge>
          </AppTooltip>
        )}
    </Box>
  );
};
