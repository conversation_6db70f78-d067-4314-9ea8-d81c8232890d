import React from 'react';
import {
  Document,
  Page,
  Text,
  Image,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';

// Register Arabic font
Font.register({
  family: 'Vazirmatn',
  src: '/fonts/Vazirmatn-Regular.ttf',
});

const styles = StyleSheet.create({
  page: {
    width: '210mm',
    height: '297mm',
    position: 'absolute',
    fontFamily: 'Vazirmatn',
    fontSize: 12,
    direction: 'rtl',
  },
  backgroundImage: {
    position: 'absolute',
    width: '210mm',
    height: '297mm',
  },
  field_date: {
    position: 'absolute',
    color: '#000',
    fontSize: 12,
  },
  field: {
    position: 'absolute',
    color: '#000',
    fontSize: 14,
  },
});

const TransferLetterPdf = ({ data }: { data: any }) => {
  const vehicle = data.vehicles?.[0] || {};

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Image
          style={styles.backgroundImage}
          src="/images/Oman-Latterhead.jpg"
        />

        <Text style={{ ...styles.field_date, top: '40mm', right: '31mm' }}>
          {data.transferDate}
        </Text>
        <Text style={{ ...styles.field, top: '129mm', right: '42mm' }}>
          {data.transferor}
        </Text>
        <Text style={{ ...styles.field, top: '139mm', right: '42mm' }}>
          {data.transferorId}
        </Text>

        {/* Vehicle info */}
        <Text
          style={{
            ...styles.field,
            top: '162mm',
            right: '79mm',
            textAlign: 'center',
            width: '100mm',
          }}
        >
          {' '}
          {vehicle.make} {vehicle.model}
        </Text>
        <Text
          style={{
            ...styles.field,
            top: '173mm',
            right: '79mm',
            textAlign: 'center',
            width: '100mm',
          }}
        >
          {vehicle.color}
        </Text>
        <Text
          style={{
            ...styles.field,
            top: '184mm',
            right: '79mm',
            textAlign: 'center',
            width: '100mm',
          }}
        >
          {vehicle.vin}
        </Text>
        <Text
          style={{
            ...styles.field,
            top: '195mm',
            right: '79mm',
            textAlign: 'center',
            width: '100mm',
          }}
        >
          {vehicle.year}
        </Text>
      </Page>
    </Document>
  );
};

export default TransferLetterPdf;
