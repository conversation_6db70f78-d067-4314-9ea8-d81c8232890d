import React, { useEffect, useState } from 'react';
import {
  Autocomplete,
  Backdrop,
  Button,
  Fade,
  Grid,
  IconButton,
  Modal,
  TextField,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Box } from '@mui/system';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Avatar from '@mui/material/Avatar';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

import AllVehiclesHelper from './AllVehiclesHelper';
import { styled, useTheme } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';

const Root = styled('div')(({ theme }) => ({
  display: 'flex',
  [theme.breakpoints.up('md')]: {
    width: '90vw',
    height: '70vh',
  },
  [theme.breakpoints.down('md')]: {
    width: '95%',
    flexWrap: 'wrap',
  },
}));

async function getData(url, content, setData) {
  try {
    const res = await axios.get(url, { params: content });
    if (res.status === 200) {
      setData(res.data?.data);
    }
  } catch (error) {}
}

const AddToContainer = ({
  selectedItems,
  open,
  onDeny,
  fetchRecords,
  setSelectedItems,
}) => {
  const theme = useTheme();
  const [options, setOptions] = useState([]);
  const [container, setContainer] = useState<any>();
  const [checkCompanyDestinationsNull, setCheckCompanyDestinationsNull] =
    useState(false);
  const [checkYardNull, setCheckYardNull] = useState(false);

  const [
    checkCompanyAndBookingDestinations,
    setCheckCompanyAndBookingDestinations,
  ] = useState(false);

  const [
    checkVehicleAndBookingDestinations,
    setCheckVehicleAndBookingDestinations,
  ] = useState(false);
  const [checkYardLocation, setCheckYardLocation] = useState(false);
  const [checkVehicleDesc, setCheckVehicleDesc] = useState(false);
  const allVehiclesHelper = new AllVehiclesHelper();
  const matches = useMediaQuery(theme.breakpoints.up('md'));
  const [openConfirm, setOpenConfirm] = useState(false);
  const [openConfirm2, setOpenConfirm2] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState(false);
  const [errorText, setErrorText] = useState('');

  useEffect(() => {
    setContainer(null);
    setError(false);
    setOptions([]);
    if (open) {
      setCheckCompanyDestinationsNull(
        selectedItems.some(
          (item) => item?.customers?.companies?.destinations == null,
        ),
      );

      setCheckYardNull(
        selectedItems.some((item) => item?.yard_location_id == null),
      );

      getData(
        'containers/atLoadingAtDock',
        {
          point_of_loading: selectedItems[0]?.point_of_loading,
          vehicles_destination: selectedItems[0]?.destination_id,
        },
        setOptions,
      );
    }
  }, [open]);
  useEffect(() => {
    const checkCompanyYardLocationWhen = selectedItems.filter((data) => {
      if (
        selectedItems[0]?.yards_location?.id != container?.yards_location?.id
      ) {
        return data;
      }
    });

    const checkCompanyDestination = selectedItems.filter((data) => {
      if (
        selectedItems[0]?.customers?.companies?.destinations?.id !=
        container?.bookings?.destinations?.id
      ) {
        return data;
      }
    });

    const sameDestination = selectedItems.every(
      //@ts-ignore
      (destination, i, array) => {
        let dest = destination?.customers?.companies?.destinations?.id;
        return dest == container?.bookings?.destinations?.id;
      },
    );

    setCheckVehicleAndBookingDestinations(sameDestination);
    setCheckCompanyAndBookingDestinations(checkCompanyDestination.length <= 0);
    setCheckYardLocation(checkCompanyYardLocationWhen.length <= 0);
  }, [container]);

  useEffect(() => {
    const checkVehicleDescVar = selectedItems.some(
      (item) =>
        (item?.make?.includes('HYUNDAI') &&
          item?.model?.includes('SANTA FE')) ||
        item?.model?.includes('HYBRID'),
    );
    setCheckVehicleDesc(checkVehicleDescVar);
  }, [selectedItems]);

  const setStatusFunction = (event) => setContainer(event);

  const handleAddToContainer = async () => {
    if (selectedItems.length > 0) {
      try {
        const res = await allVehiclesHelper.addToContainer({
          vehiclesIds: selectedItems?.map((item) => item?.id),
          containerId: container?.id,
          yard: selectedItems[0]?.yard_location_id,
        });
        if (res.result) {
          setSelectedItems([]);
          setContainer(null);
          onDeny(false);
          setError(false);
          setOpenConfirm(false);
          setErrorText('');
          toast.success('Successfully added to container');
          onDeny(false);
          setOpenConfirm2(false);
          fetchRecords();
        }
      } catch (error) {
        toast.error('Oops! Somethings went wrong.');
      }
    }
  };

  const addToContainer = () => {
    !checkVehicleAndBookingDestinations &&
    !checkCompanyAndBookingDestinations &&
    !checkCompanyDestinationsNull &&
    container
      ? setOpenConfirm(true)
      : handleAddToContainer();
  };

  const sureToAdd = () => {
    setOpenConfirm(true);
    const containerName = container?.bookings?.destinations?.name;
    const isMatch =
      containerName.normalize().trim().toLowerCase() ===
      password.normalize().trim().toLowerCase();

    if (containerName && isMatch) {
      handleAddToContainer();
      setError(false);
    } else {
      setError(true);
      setErrorText('Please provide correct destination');
    }
  };

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: ` ${theme.colors.alpha.white[100]}`,
    border: `2px solid ${theme.colors.alpha.black[50]}`,
    boxShadow: 24,
    borderRadius: '10px',
  };

  return (
    <>
      <Modal
        open={open}
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        closeAfterTransition
        slots={{ backdrop: Backdrop }}
        slotProps={{
          backdrop: {
            timeout: 500,
          },
        }}
      >
        <Fade in={open}>
          <Root sx={{ ...style }}>
            <Box sx={{ p: 2, width: '100%' }}>
              <Box
                sx={{
                  display: 'flex',
                  width: '100%',
                  justifyContent: 'space-between',
                  flexWrap: 'wrap',
                }}
              >
                <Typography
                  variant="h5"
                  sx={{ fontSize: '18px', fontWeight: 'bold' }}
                >
                  Add to Container
                </Typography>
                <IconButton onClick={() => onDeny(false)} aria-label="delete">
                  <CloseIcon />
                </IconButton>
              </Box>
              <Grid container>
                <Grid
                  sx={{
                    padding: '10px',
                  }}
                  style={
                    matches
                      ? { borderRight: '1px solid lightgray' }
                      : { borderRight: 'unset' }
                  }
                  size={{
                    xs: 12,
                    sm: 12,
                    md: 6,
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Selected Vehicles
                  </Typography>
                  <Box
                    sx={{ overflowY: 'auto' }}
                    style={matches ? { height: '55vh' } : { height: '25vh' }}
                  >
                    {selectedItems.map((data, index) => {
                      return (
                        <Card key={index} sx={{ my: 1 }}>
                          <CardHeader
                            avatar={
                              <Avatar
                                sx={{
                                  bgcolor: 'text.primary',
                                  width: '25px',
                                  height: '25px',
                                  fontSize: '13px',
                                }}
                                aria-label="recipe"
                              >
                                {index + 1}
                              </Avatar>
                            }
                            title={
                              data?.year +
                              ' ' +
                              data?.make +
                              ' ' +
                              data?.model +
                              ' ' +
                              data?.color
                            }
                            subheader={`Vin: ${data.vin}  lot#: ${data.lot_number}`}
                          />

                          <CardContent sx={{ marginTop: '-15px' }}>
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Company Name
                              </Box>
                              <Box>{data?.customers?.companies?.name}</Box>
                            </Box>

                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Company destination
                              </Box>
                              <Box>
                                {data?.customers?.companies?.destinations?.name}
                              </Box>
                            </Box>
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Point of Loading
                              </Box>
                              <Box>{data?.pol_locations?.name}</Box>
                            </Box>

                            <Box
                              display="flex"
                              justifyContent="space-between"
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>
                                Point Of Destination
                              </Box>
                              <Box>{data?.destinations?.name}</Box>
                            </Box>
                            {data?.yards_location?.name ? (
                              <Box
                                display="flex"
                                justifyContent="space-between"
                                // mb={2}
                                sx={{ borderBottom: '1px dotted' }}
                              >
                                <Box sx={{ fontWeight: 'bold' }}>
                                  Yard Location
                                </Box>
                                <Box>{data?.yards_location?.name}</Box>
                              </Box>
                            ) : (
                              ''
                            )}
                            <Box
                              display="flex"
                              justifyContent="space-between"
                              // mb={2}
                              sx={{ borderBottom: '1px dotted' }}
                            >
                              <Box sx={{ fontWeight: 'bold' }}>Price</Box>
                              <Box>{data?.price}</Box>
                            </Box>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </Box>
                </Grid>
                <Grid
                  sx={{
                    padding: '10px',
                    height: '40vh',
                  }}
                  size={{
                    xs: 12,
                    sm: 12,
                    md: 6,
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 'bold',
                      marginBottom: '10px',
                    }}
                  >
                    Booking & Container
                  </Typography>

                  <Autocomplete
                    sx={{ marginTop: '30px' }}
                    //@ts-ignore
                    onChange={(event, item) => setStatusFunction(item)}
                    /* value={options.find((option) => option.id) ?? ''} */
                    options={options}
                    getOptionLabel={(item) =>
                      `${
                        (item?.bookings?.booking_number
                          ? item?.bookings?.booking_number
                          : '') +
                        (item?.booking_suffix
                          ? '-' + item?.booking_suffix
                          : '') +
                        ' | ' +
                        (item?.container_number ? item?.container_number : '')
                      }`
                    }
                    /* isOptionEqualToValue={(option, value) =>
                      option.id == value.id
                    } */
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={'Booking | Container'}
                        variant="outlined"
                        size="small"
                      />
                    )}
                  />
                  {container ? (
                    <Card sx={{ maxWidth: '100%', marginTop: '20px' }}>
                      <CardHeader
                        title={
                          container?.no_units_load +
                          ' ' +
                          container?.bookings?.size
                        }
                        subheader={`Bookings#: ${container?.bookings?.booking_number}  Container#: ${container?.container_number}`}
                      />

                      <CardContent sx={{ marginTop: '-15px' }}>
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          // mb={2}
                          sx={{ borderBottom: '1px dotted' }}
                        >
                          <Box sx={{ fontWeight: 'bold' }}>Company Name</Box>
                          <Box>{container?.companies?.name}</Box>
                        </Box>
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          // mb={2}
                          sx={{ borderBottom: '1px dotted' }}
                        >
                          <Box sx={{ fontWeight: 'bold' }}>
                            Point of Loading
                          </Box>
                          <Box>
                            {container?.bookings?.vessels?.locations?.name}
                          </Box>
                        </Box>

                        <Box
                          display="flex"
                          justifyContent="space-between"
                          // mb={2}
                          sx={{ borderBottom: '1px dotted' }}
                        >
                          <Box sx={{ fontWeight: 'bold' }}>
                            Point of Discharge
                          </Box>
                          <Box>{container?.bookings?.destinations?.name}</Box>
                        </Box>
                        {container?.yards_location ? (
                          <Box
                            display="flex"
                            justifyContent="space-between"
                            // mb={2}
                            sx={{ borderBottom: '1px dotted' }}
                          >
                            <Box sx={{ fontWeight: 'bold' }}>Yard Location</Box>
                            <Box>{container?.yards_location?.name}</Box>
                          </Box>
                        ) : (
                          ''
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    ''
                  )}

                  {container &&
                    selectedItems[0]?.company_id !==
                      container?.companies?.id && (
                      <Typography sx={{ color: 'red', mt: 2 }}>
                        Vehicles and container belong to different companies?
                        Are you Sure to Add.
                      </Typography>
                    )}
                  {checkCompanyDestinationsNull && (
                    <Typography sx={{ color: 'red', mt: 2 }}>
                      Some of your selected vehicles do not have company
                      destination.
                    </Typography>
                  )}
                  {!checkCompanyAndBookingDestinations &&
                    !checkCompanyDestinationsNull &&
                    container && (
                      <Typography sx={{ color: 'red', mt: 2 }}>
                        The destinations of the booking and the vehicle's
                        company are not the same.
                      </Typography>
                    )}
                  {!checkYardLocation && !checkYardNull && container && (
                    <Typography sx={{ color: 'red', mt: 2 }}>
                      The Yard Location of the booking and the vehicle's yard
                      locations are not the same.
                    </Typography>
                  )}

                  {!checkVehicleAndBookingDestinations && container && (
                    <Typography sx={{ color: 'red', mt: 2 }}>
                      The destinations of the booking and the vehicle's are not
                      the same.
                    </Typography>
                  )}
                </Grid>
              </Grid>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'end',
                  width: '100%',
                }}
              >
                <Button
                  sx={{ mx: 2 }}
                  endIcon={<SendIcon />}
                  variant="contained"
                  type="submit"
                  onClick={() => {
                    checkVehicleDesc ? setOpenConfirm2(true) : addToContainer();
                  }}
                >
                  Add
                </Button>

                <Button
                  startIcon={<CloseIcon />}
                  color="error"
                  onClick={() => onDeny(false)}
                >
                  Close
                </Button>
              </Box>
            </Box>
          </Root>
        </Fade>
      </Modal>
      <AppConfirmDialog
        open={openConfirm}
        onDeny={() => {
          setOpenConfirm(false);
          setError(false);
          setErrorText('');
          setPassword('');
        }}
        onConfirm={() => sureToAdd()}
        title={
          <>
            <Box component="p">
              please type this:
              <Box
                component="span"
                sx={{ fontSize: '16px', color: 'red', fontWeight: 'bold' }}
              >
                {' '}
                {container?.bookings?.destinations?.name}
              </Box>{' '}
              at below text field
            </Box>

            <TextField
              size="small"
              error={error ? true : false}
              id="type_password_here"
              label="Type destination here"
              fullWidth
              variant="outlined"
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setError(false);
                setPassword(event.target.value);
              }}
              value={password}
              helperText={error ? errorText : ''}
            />
          </>
        }
        dialogTitle={`Are you sure to add ${
          selectedItems.length == 1 ? 'this' : 'these'
        }  ${
          selectedItems.length == 1 ? 'vehicle' : 'vehicles'
        } to different destination`}
        confirmText="Approve"
        cancelText="Cancel"
        maxWidth="sm"
      />
      <AppConfirmDialog
        open={openConfirm2}
        onDeny={() => {
          setOpenConfirm2(false);
        }}
        onConfirm={() => handleAddToContainer()}
        title={`${selectedItems.length == 1 ? 'this' : 'these'}  ${
          selectedItems.length == 1 ? 'vehicle' : 'vehicles'
        } are Hybrid or HYUNDAI SANTA FE, Do you want to add to this container?`}
        dialogTitle={`Are you sure to add ${
          selectedItems.length == 1 ? 'this' : 'these'
        }  ${
          selectedItems.length == 1 ? 'vehicle' : 'vehicles'
        } are Hybrid or HYUNDAI SANTA FE`}
        confirmText="Approve"
        cancelText="Cancel"
        maxWidth="sm"
      />
    </>
  );
};

export default AddToContainer;
