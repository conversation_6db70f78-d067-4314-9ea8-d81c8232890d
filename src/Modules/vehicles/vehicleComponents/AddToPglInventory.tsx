import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { formFormatDate } from '@/configs/configs';
import { Grid, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';

export const AddToPglUInventoryForm = ({
  pgluInvYard,
  setPgluInvYard,
  setPgluInvStorage,
  setPgluInvDateIn,
  setPgluInvRemarks,
}) => {
  const onChange = (value) => {
    setPgluInvYard(value);
  };
  let field = {
    onChange: onChange,
    value: pgluInvYard,
  };
  return (
    <Grid container spacing={2}>
      <Grid size={12}>
        <AutoComplete
          url="autoComplete"
          label="Select Yard"
          fieldName="name"
          field={field}
          error={{}}
          staticOptions={false}
          column={'name'}
          modal={'pgl_used_cars_yards'}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <DatePicker
          views={['year', 'month', 'day']}
          label="Date in"
          format="yyyy/MM/dd"
          onChange={(value) => setPgluInvDateIn(formFormatDate(value))}
          slotProps={{
            textField: {
              variant: 'outlined',
              size: 'small',
              fullWidth: true,
            },
          }}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 6,
        }}
      >
        <TextField
          size="small"
          id="storage"
          label="Storage Amount"
          type="number"
          onChange={(event) => setPgluInvStorage(event.target.value)}
          fullWidth
          variant="outlined"
        />
      </Grid>
      <Grid size={12}>
        <TextField
          size="small"
          id="remarks"
          label="Remarks"
          onChange={(event) => setPgluInvRemarks(event.target.value)}
          fullWidth
          placeholder="Please leave a comment"
          multiline
          rows={4}
          variant="outlined"
        />
      </Grid>
    </Grid>
  );
};
