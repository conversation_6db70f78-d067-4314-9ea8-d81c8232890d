import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import axios from '@/lib/axios';
import { Grid, TextField } from '@mui/material';
import React, { useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  titleStepOne,
  deliverLocations,
} from '@/configs/vehicles/vehiclesHeader';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { formFormatDate } from '@/configs/configs';

const SimplificationTrack = ({
  open,
  onDeny,
  singleVehicle,
  setSingleVehicle,
  setTableRecords,
  setUpdateDate,
}) => {
  const form = useForm({
    defaultValues: singleVehicle || {},
  });

  const {
    watch,
    formState: { errors },
    handleSubmit,
    reset,
  } = form;

  const title_status = watch('title_status');
  const title_delivery_location = watch('title_delivery_location');
  const trn = watch('trn');

  useEffect(() => {
    if (singleVehicle) {
      form.reset(singleVehicle);
    }
  }, [singleVehicle, form]);

  if (!titleStepOne.includes(title_status) && title_status) {
    form.setValue('title_status', 'CUSTOM');
    form.setValue('title_status_custom', title_status);
  }

  if (
    !deliverLocations.includes(title_delivery_location) &&
    title_delivery_location
  ) {
    form.setValue('title_delivery_location', 'OTHER');
    form.setValue('title_delivery_location_other', title_delivery_location);
  }

  useEffect(() => {
    if (trn) {
      form.setValue('title_status', '');
    }
  }, [trn]);

  const datePickerComponent = (
    <Grid container spacing={1} sx={{ marginTop: '10px' }}>
      <Grid container sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Grid
          size={{
            xs: 12,
            md: title_status === 'CUSTOM' ? 6.5 : 12,
          }}
        >
          <Controller
            name="title_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Title Status Step1"
                fieldName="title_status"
                customeName="title_status"
                field={field}
                error={error}
                staticOptions={titleStepOne}
                column=""
                modal=""
                onSelect={(e) => field.onChange(e)}
              />
            )}
          />
        </Grid>
        {title_status === 'CUSTOM' && (
          <Grid
            size={{
              xs: 12,
              md: 5,
            }}
          >
            <TextField
              size="small"
              error={!!errors.title_status_custom}
              id="title_status_Custom"
              label="Title Status Step1 Custom"
              fullWidth
              defaultValue={singleVehicle?.title_status_custom || ''}
              variant="outlined"
              {...form.register('title_status_custom')}
              helperText={errors.title_status_custom?.message?.toString()}
            />
          </Grid>
        )}
      </Grid>
      {title_status !== 'CUSTOM' && (
        <>
          <Grid
            sx={{ paddingLeft: '0px!important', marginTop: '5px' }}
            size={12}
          >
            <TextField
              size="small"
              error={!!errors.trn}
              id="trn"
              label="TRN"
              fullWidth
              variant="outlined"
              {...form.register('trn')}
              helperText={errors.trn?.message?.toString()}
            />
          </Grid>
          <Grid
            container
            spacing={2}
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '-3px',
            }}
          >
            <Grid
              size={{
                xs: 12,
                md: title_delivery_location === 'OTHER' ? 6 : 12,
              }}
            >
              <Controller
                name="title_delivery_location"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Deliver Locations"
                    fieldName="title_delivery_location"
                    field={field}
                    error={error}
                    staticOptions={deliverLocations}
                    column=""
                    modal=""
                    customeName="title_delivery_location"
                    onSelect={(e) => field.onChange(e)}
                  />
                )}
              />
            </Grid>
            {title_delivery_location === 'OTHER' && (
              <Grid
                size={{
                  xs: 12,
                  md: 6,
                }}
              >
                <TextField
                  size="small"
                  error={!!errors.title_delivery_location_other}
                  id="title_delivery_location_other"
                  label="Title Deliver Location Custom"
                  fullWidth
                  variant="outlined"
                  {...form.register('title_delivery_location_other')}
                  helperText={errors.title_delivery_location_other?.message?.toString()}
                />
              </Grid>
            )}
          </Grid>
        </>
      )}
      <Grid sx={{ marginTop: '10px', marginLeft: '-2%' }} size={12}>
        <DatePicker
          views={['year', 'month', 'day']}
          label="Update Last Title Follow Up Date"
          sx={{ width: '102%' }}
          value={
            singleVehicle?.last_title_follow_up_date
              ? dayjs(singleVehicle.last_title_follow_up_date).toDate()
              : null
          }
          format="yyyy/MM/dd"
          onChange={(e) =>
            form.setValue('last_title_follow_up_date', formFormatDate(e))
          }
        />
      </Grid>
      {/* Receive Date Picker */}
      <Grid sx={{ marginTop: '10px', marginLeft: '-2%' }} size={12}>
        <DatePicker
          views={['year', 'month', 'day']}
          label="Update Title Receive Date"
          sx={{ width: '102%' }}
          value={
            singleVehicle?.title_receive_date
              ? dayjs(singleVehicle.title_receive_date).toDate()
              : null
          }
          format="yyyy/MM/dd"
          onChange={(e) => {
            if (e) {
              form.setValue('title_receive_date', formFormatDate(e));
            }
          }}
        />
      </Grid>
      <Grid sx={{ marginTop: '10px', marginLeft: '-2%' }} size={12}>
        <TextField
          size="small"
          error={!!errors.title_status_step_two}
          id="title_status_step_two"
          label="Title Status Step2"
          fullWidth
          variant="outlined"
          {...form.register('title_status_step_two')}
          helperText={errors.title_status_step_two?.message?.toString()}
        />
      </Grid>
    </Grid>
  );

  const onSubmit = async (data) => {
    const updatedData: { [key: string]: any } = {};
    if (data.title_status === 'CUSTOM') {
      updatedData.title_status = data.title_status_custom;
    } else {
      updatedData.title_status = data.title_status;
    }

    if (data.title_status !== 'CUSTOM') {
      updatedData.trn = data.trn;
    }

    if (data.title_delivery_location === 'OTHER') {
      updatedData.title_delivery_location = data.title_delivery_location_other;
    } else {
      updatedData.title_delivery_location = data.title_delivery_location;
    }
    if (data.last_title_follow_up_date) {
      updatedData.last_title_follow_up_date = data.last_title_follow_up_date;
    }
    if (data.title_receive_date) {
      updatedData.title_receive_date = data.title_receive_date;
    }

    if (data.title_status_step_two) {
      updatedData.title_status_step_two = data.title_status_step_two;
    }

    try {
      const response = await axios.patch(
        `vehicles/updatTrackNumber/${singleVehicle.id}`,
        updatedData,
      );
      if (response.status) {
        const updatedData1 = {
          ...updatedData,
          last_title_follow_up_date: dayjs(
            response.data.data.last_title_follow_up_date,
          ).toDate(),
          title_receive_date: dayjs(
            response.data.data.title_receive_date,
          ).toDate(),
        };
        setTableRecords((prevRecords) =>
          prevRecords.map((record) =>
            record.id === singleVehicle.id
              ? { ...record, ...updatedData1 }
              : record,
          ),
        );
        setUpdateDate(false);
        setSingleVehicle([]);
        reset();
        toast.success('Record updated successfully!');
        onDeny();
      } else {
        toast.error('Error Occured! Please try again');
      }
    } catch (error) {
      console.error('Error updating title:', error);
    }
  };

  const handleUpdate = () => handleSubmit(onSubmit)();

  return (
    <AppConfirmDialog
      open={open}
      onDeny={onDeny}
      onConfirm={handleUpdate}
      title={datePickerComponent}
      dialogTitle="Update Title Status Step One"
      confirmText="Update"
      cancelText="Cancel"
      maxWidth="sm"
    />
  );
};

export default SimplificationTrack;
