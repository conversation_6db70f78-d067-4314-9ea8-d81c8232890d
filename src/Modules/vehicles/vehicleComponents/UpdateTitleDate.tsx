import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { formFormatDate } from '@/configs/configs';
import axios from '@/lib/axios';
import { DatePicker } from '@mui/x-date-pickers';
import React, { useState } from 'react';
import { toast } from 'react-toastify';

const UpdateTitleDate = ({
  open,
  onDeny,
  selectedItems,
  dateType,
  tableRecords,
  setTableRecords,
  setSelectedItems,
  setUpdateDate,
}) => {
  const [date, setDate] = useState<any>();

  const datePickerComponent = (
    <DatePicker
      views={['year', 'month', 'day']}
      label={`Update ${
        dateType === 'last_title_follow_up_date'
          ? 'Last title follow up date'
          : 'Title receive date'
      }`}
      sx={{ width: '100%' }}
      value={date}
      format="yyyy/MM/dd"
      onChange={(newValue) => {
        setDate(newValue);
      }}
    />
  );
  //////////////////////////////////////////////////////////
  const updateTitlesDate = async () => {
    if (!date) {
      toast.warn('Please make changes to the current date to update the date');
    } else {
      let id = selectedItems[0]?.id;
      const { data } = await axios.patch(
        `${
          dateType === 'last_title_follow_up_date'
            ? 'vehicles/updatedLastTitleFollowUpDate/'
            : 'vehicles/updateTitleReceiveDate/'
        }${id}`,
        { date_param: formFormatDate(date) },
      );
      if (data.result === true) {
        const updatedRecords = tableRecords.map((record) => {
          if (dateType === 'last_title_follow_up_date') {
            if (record?.id === id) {
              record.last_title_follow_up_date =
                data?.data?.last_title_follow_up_date;
            }
          } else {
            if (record?.id === id) {
              record.title_receive_date = data?.data?.title_receive_date;
            }
          }
          return record;
        });
        setTableRecords(updatedRecords);
        setDate(null);
        setUpdateDate(false);
        setSelectedItems([]);
        toast.success('Record updated successfully!');
        return true;
      } else {
        setDate(null);
        setSelectedItems([]);
        setUpdateDate(false);
      }
    }
  };

  /////////////////////////////////////////////////////
  return (
    <AppConfirmDialog
      open={open}
      onDeny={onDeny}
      onConfirm={updateTitlesDate}
      title={datePickerComponent}
      dialogTitle={`Update ${
        dateType === 'last_title_follow_up_date'
          ? 'Last title follow up date'
          : 'Title receive date'
      }`}
      confirmText={'Update'}
      cancelText="Cancel"
      maxWidth={'sm'}
    />
  );
};

export default UpdateTitleDate;
