import { formFormatDate } from '@/configs/configs';
import { Grid, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useEffect, useState } from 'react';

export const AddTitleTrackingForm = ({
  setTitleTrackingDate,
  setTitleTrackingArrivalDate,
  setTitleTrackingNumber,
}) => {
  const [date, setDate] = useState<Date | null>(null);

  useEffect(() => {
    const currentDate = new Date();
    setDate(currentDate); // Set current date
    setTitleTrackingDate(currentDate); // Ensure parent component gets the date
  }, [setTitleTrackingDate]);

  return (
    <Grid container spacing={2}>
      <Grid
        size={{
          xs: 12,
          md: 12,
        }}
      >
        <DatePicker
          views={['year', 'month', 'day']}
          label="Title Tracking Date"
          format="yyyy/MM/dd"
          value={date}
          onChange={(newValue) => {
            setDate(newValue); // Update internal state
            setTitleTrackingDate(formFormatDate(newValue)); // Pass formatted date to parent
          }}
          slotProps={{
            textField: {
              variant: 'outlined',
              size: 'small',
              fullWidth: true,
            },
          }}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 12,
        }}
      >
        <DatePicker
          views={['year', 'month', 'day']}
          label="Title Tracking Arrival Date"
          format="yyyy/MM/dd"
          onChange={(value) => {
            setTitleTrackingArrivalDate(formFormatDate(value)); // Pass formatted date to parent
          }}
          slotProps={{
            textField: {
              variant: 'outlined',
              size: 'small',
              fullWidth: true,
            },
          }}
        />
      </Grid>
      <Grid
        size={{
          xs: 12,
          md: 12,
        }}
      >
        <TextField
          size="small"
          id="title"
          label="Outside of USA Title TRN#"
          onChange={(event) => setTitleTrackingNumber(event.target.value)}
          fullWidth
          variant="outlined"
        />
      </Grid>
    </Grid>
  );
};
