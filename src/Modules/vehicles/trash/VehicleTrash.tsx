import PageAction from '@/components/mainComponents/PageAction/PageAction';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
import {
  HeaderInfoVehicleTrash,
  VehicleTrashHeader,
} from '@/configs/vehicles/TrashHeader';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import { Box, Chip, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useContext, useEffect, useState } from 'react';
import { removeUnderScore } from '@/configs/common';
import { choseColor } from '@/configs/configs';
import { formatDate } from '@/configs/vehicles/configs';
import { toast } from 'react-toastify';
import ViewSingleVehicle from '../vehicleComponents/ViewSingleVehicle';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';

const VehicleTrash = () => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [data, setData] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [apiUrl] = useState('/vehicles/trash');
  const [cancelToken, setCancelToken] = useState(null);
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const [timeOut, setTimeOut] = useState(null);

  const [options, setOptions] = useState({
    page: 1,
    perPage: 100,
    filterData: {},
    tab: 'all',
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////
  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    router,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setData([]);
      setLoading(true);
      let params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
        exactMatch: options.exactMatch,
        filterData: JSON.stringify(options.filterData),
      };

      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: params,
      });
      setTotalItems(data.total);
      setData(data.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const onRestore = async () => {
    const ids = selectedItems.map((item) => item.id);
    try {
      let res = await axios.put(`/vehicles/trash/restore/${ids}`);
      if (res.data.result) {
        const tempRecord2 = data.filter((row) => !ids.includes(row.id));
        setSelectedItems([]);
        setTotalItems(totalItems - ids.length);
        setData(tempRecord2);
        toast.success('Records Restored successfully!');
      }
    } catch (error) {
      toast.error('Your restoration process failed, Please try again');
    }
  };

  const getSingleRow = async (id: number) => {
    setSingleFetchLoading(true);
    setView(true);
    try {
      if (cancelToken) await cancelToken.abort();
      const controller = new AbortController();
      setCancelToken(controller);
      const res = await axios.get(`vehicles/${id}`, {
        signal: controller.signal,
      });
      if (res && res?.status == 200) {
        setViewData(res?.data);
        setSingleFetchLoading(false);
      }
    } catch (err) {
      console.log(err);
      setSingleFetchLoading(false);
      setView(false);
    }
  };

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(VEHICLES?.TRASH_VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Vehicle Trash</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfoVehicleTrash()} />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={options.tab}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              trash
              showRestore={perms?.includes(VEHICLES.RESTORE)}
              onRestore={onRestore}
              showDeleteButton={false}
              showEditButton={false}
              showAddButton={false}
              hideFilter
              selectedItems={selectedItems}
              title={'Vehicle Trash'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              dialogTitle={
                'Are you sure you want to permanently delete this data?'
              }
              deleteTitle={
                'This is a permanent action, and the deleted data cannot be restored. Are you sure you want to proceed with the deletion? Please confirm by pressing "Yes" or "No" button to proceed'
              }
              restoreDialogText={
                'Would you like to restore your data to its previous state? Please confirm by pressing "Yes" or "No" button to proceed'
              }
              restoreTitle={`Restore Selected Item${
                selectedItems.length > 1 ? 's' : ''
              }`}
            />
          }
          // start default props
          sortLoading={loading}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={data}
          headers={VehicleTrashHeader}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={options.tab}
          tableName="vehicleTrash"
          //start custom props
          id={(item) => (
            <AppTooltip title={'Double Click View Profile'}>
              <Box
                onDoubleClick={(e) => {
                  clearTimeout(timeOut);
                  e.stopPropagation();
                  getSingleRow(item?.id);
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  clearTimeout(timeOut);
                  setTimeOut(
                    setTimeout(async () => {
                      try {
                        await navigator.clipboard.writeText(item?.id);
                        toast.success('Copy to ClipBoard');
                      } catch (error) {
                        console.error('Failed to copy text: ', error);
                      }
                    }, 300),
                  );
                }}
                style={{
                  color: '#2196f3',
                  fontWeight: 'bold',
                  fontSize: '10px',
                  cursor: 'pointer',
                }}
              >
                {item?.id}
              </Box>
            </AppTooltip>
          )}
          vehicle_description={(item) => {
            return (
              <Box sx={{ minWidth: '200px', maxWidth: '220px' }}>
                {`${item?.year ?? ''} ${item?.make ?? ''} ${
                  item?.model ?? ''
                } ${item?.color ?? ''}`}
              </Box>
            );
          }}
          carstate={({ carstate }) =>
            carstate ? (
              <Chip
                size="small"
                label={removeUnderScore(carstate)}
                sx={{
                  fontSize: '10px',
                  backgroundColor: choseColor(carstate),
                  color: 'white',
                }}
              />
            ) : (
              ''
            )
          }
          customer_remark={({ customer_remark }) => {
            const remark = customer_remark;
            return (
              <Box
                sx={{
                  minWidth: '150px',
                  maxWidth: '220px',
                  whiteSpace: 'wrap',
                  // textAlign: 'center',
                }}
              >
                <Box
                  component="span"
                  sx={{
                    backgroundColor: '#d70505',
                    color: 'white',
                    px: remark ? 1 : 0,
                  }}
                >
                  {remark}
                </Box>
              </Box>
            );
          }}
          company={({ companies }) => {
            return (
              <Box sx={{ width: 150, whiteSpace: 'wrap' }}>
                {companies?.name}
              </Box>
            );
          }}
          points_of_loading={({ pol_locations }) => pol_locations?.name}
          deleted_at={(item) => {
            return (
              <Box>{formatDate(item?.deleted_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
            );
          }}
          deleted_by={({ users_vehicles_deleted_byTousers }) =>
            users_vehicles_deleted_byTousers?.fullname
          }
          deleted_by_confirm={({ users_vehicles_deleted_by_confirmTousers }) =>
            users_vehicles_deleted_by_confirmTousers?.fullname
          }
          deleted_reason={({ deleted_reason }) => {
            return (
              <Box
                sx={{
                  minWidth: '150px',
                  maxWidth: '220px',
                  whiteSpace: 'wrap',
                  lineHeight: '1.5',
                }}
              >
                {deleted_reason}
              </Box>
            );
          }}
        />
      </Container>

      <ViewSingleVehicle
        loading={singleFetchLoading}
        data={viewData}
        setView={setView}
        show={view}
      />
    </>
  );
};

export default VehicleTrash;
