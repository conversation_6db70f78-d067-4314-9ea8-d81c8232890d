import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import {
  $format,
  extractBranchShortCut,
  formatDate,
} from '@/configs/vehicles/configs';
import { Box, IconButton } from '@mui/material';
import React from 'react';
import EditIcon from '@mui/icons-material/Edit';
import axios from '@/lib/axios';

export const useCustomColumnTowCost = ({
  tableRecords,
  setSelectedItems,
  setSingleFetchLoading,
  setIsUpdate,
  setShowCreate,
}) => {
  /////////////////////////////////////////////////////////
  const totalAmount = (data, data2) => {
    let total = 0;
    tableRecords.forEach((el) => {
      total += +el?.[data]?.[data2] ? +el?.[data]?.[data2] : 0;
    });
    return total;
  };
  const totalProfit = () => {
    let total = 0;
    tableRecords.forEach((el) => {
      const towingCost = +el.vehicle_costs?.towing_cost
        ? +el.vehicle_costs?.towing_cost
        : 0;
      const towAmount = +el.vehicle_towings?.tow_amount
        ? +el.vehicle_towings?.tow_amount
        : 0;
      total += towingCost - towAmount;
    });

    return total;
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`vehicles/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      console.log(error);
      setSingleFetchLoading(false);
    }
  };

  const onEdit = async (e, id) => {
    e.stopPropagation();
    try {
      setIsUpdate(true);
      setShowCreate(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setSelectedItems([res?.data]);
      }
    } catch (error) {
      setIsUpdate(false);
      setShowCreate(false);
      console.log(error);
    }
  };

  ////////////////////////////////////////////////////////
  return {
    deliver_date: ({ deliver_date }) => formatDate(deliver_date),
    container_number: ({ containers }) => containers?.container_number,
    auction_city: (item) => (
      <Box sx={{ width: 150, whiteSpace: 'wrap' }}>
        {item?.auction_cities
          ? `${item?.auction_cities.city_name} ${extractBranchShortCut(item?.auction_cities?.loading_states?.parent?.name)}`
          : item.auction_city}
      </Box>
    ),
    to: ({ pol_locations }) => pol_locations?.name,
    tow_amount_charged: ({ vehicle_costs }) =>
      $format(vehicle_costs?.towing_cost),
    tow_amount_actual: ({ vehicle_towings }) =>
      $format(vehicle_towings?.tow_amount),
    company: ({ customers }) => customers?.companies?.name,
    profit: (item) =>
      (+item?.vehicle_costs?.towing_cost
        ? +item?.vehicle_costs?.towing_cost
        : 0) -
      (+item.vehicle_towings?.tow_amount
        ? +item.vehicle_towings?.tow_amount
        : 0),

    vehicle_description: (item) => {
      return (
        <Box>
          {`${item?.year ?? ''} ${item?.make ?? ''} ${item?.model ?? ''} ${
            item?.color ?? ''
          }`}
        </Box>
      );
    },
    totalCalc: tableRecords.length > 0 ? true : false,
    footerValues: {
      company: 'This total based on this pagination',
      tow_amount_charged: $format(totalAmount('vehicle_costs', 'towing_cost')),
      tow_amount_actual: $format(totalAmount('vehicle_towings', 'tow_amount')),
      profit: totalProfit(),
    },
    edit: (item) => (
      <AppTooltip title={'Edit'}>
        <IconButton
          size="small"
          color="secondary"
          onClick={(e) => onEdit(e, item?.id)}
        >
          <EditIcon style={{ fontSize: 16 }} />
        </IconButton>
      </AppTooltip>
    ),
  };
};
