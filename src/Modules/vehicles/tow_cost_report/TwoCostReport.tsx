import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  HeaderInfo,
  filterContentVehicleTowCost,
  headers,
} from '@/configs/vehicles/towCostReportHeader';

import { Box, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import {
  $format,
  extractBranchShortCut,
  formatDate,
} from '../../../configs/vehicles/configs';
import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { CreateVehicles } from '../CreateVehicles';
import { recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import { useCustomColumnTowCost } from './TowCostCustomColumns';

const TwoCostReport = () => {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////

  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [apiUrl] = useState('/vehicles/towCostReport');
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
  });
  const pageName = 'vehicles_tow_cost_report';
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////
  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders: headers,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile]);
  useEffect(() => {
    fetchRecords();
  }, [
    options.page,
    options.perPage,
    options.filterData,
    options.exactMatch,
    options.search,
  ]);

  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);
      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };
  const fetchDownloadRecords = async () => {
    try {
      let { data } = await axios.get(apiUrl, {
        params: {
          page: options.page,
          per_page: options.perPage,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////

  return perms && !perms?.includes(VEHICLES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Tow Cost Report</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={HeaderInfo.breadcrumbs} />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={'all'}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          <Tab sx={{ fontSize: 10 }} key="all" label="all" value="all" />
        </Tabs>

        <DataTable3
          PageAction={
            <PageAction
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
              selectedItems={selectedItems}
              title={'Tow Cost Reports'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              showDeleteButton={false}
              showEditButton={false}
              dialogTitle={''}
              deleteTitle={''}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onFilterClick={() => setOpenFilter(true)}
              // onEdit={() => {
              //   setIsUpdate(true);
              //   setShowCreate(true);
              // }}
            />
          }
          // start default props
          hideCheckBox
          onRowClicked
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          tableName="towCostReport"
          // end default props
          //start custom props
          // eslint-disable-next-line react-hooks/rules-of-hooks
          {...useCustomColumnTowCost({
            setSelectedItems: setSelectedItems,
            tableRecords: tableRecords,
            setIsUpdate: setIsUpdate,
            setShowCreate: setShowCreate,
            setSingleFetchLoading: setSingleFetchLoading,
          })}
        />
      </Container>
      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Tow Cost Report"
        content={filterContentVehicleTowCost()}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={headers}
      ></ColumnDialog>

      <CreateVehicles
        loading={singleFetchLoading}
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems[0]}
        isUpdate={isUpdate}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      <PdfModal
        options={options}
        open={showDownload}
        pdf_title={'Tow Cost Reports'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        fetchDownloadRecords={fetchDownloadRecords}
        tableRecords={tableRecords}
        hide_columns={['edit']}
        //start custom props
        deliver_date={({ deliver_date }) => formatDate(deliver_date)}
        container_number={({ containers }) => containers?.container_number}
        auction_city={(item) =>
          item?.auction_cities
            ? `${item?.auction_cities.city_name} ${extractBranchShortCut(item?.auction_cities?.loading_states?.parent?.name)}`
            : item.auction_city
        }
        to={({ pol_locations }) => pol_locations?.name}
        tow_amount_actual={({ vehicle_costs }) =>
          $format(vehicle_costs?.towing_cost)
        }
        tow_amount_charged={({ vehicle_towings }) =>
          $format(vehicle_towings?.tow_amount)
        }
        company={({ customers }) => customers?.companies?.name}
        profit={(item) =>
          (+item?.vehicle_costs?.towing_cost
            ? +item?.vehicle_costs?.towing_cost
            : 0) -
          (+item.vehicle_towings?.tow_amount
            ? +item.vehicle_towings?.tow_amount
            : 0)
        }
      ></PdfModal>
    </>
  );
};

export default TwoCostReport;
