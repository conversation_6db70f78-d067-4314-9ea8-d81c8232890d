import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  IconButton,
  Modal,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { formFormatDate } from '@/configs/configs';
import dayjs from 'dayjs';
import { z } from 'zod';
import SaveIcon from '@mui/icons-material/Save';
import axios from '@/lib/axios';
import { LoadingPage } from '@/components/layout/LoadingPage';
import { toast } from 'react-toastify';

const schema = z.object({
  storage_values: z
    .array(
      z.object({
        id: z.number().nullable().optional(),
        date: z.union([z.string(), z.coerce.date()]),
        cost: z.number(),
      }),
    )
    .optional(),
});

const AddStorageForm = ({
  show,
  setShow,
  selectedItem,
  singleFetchLoading,
}) => {
  const [loading, setLoading] = useState(false);
  const [storageTotal, setStorageTotal] = useState(0);
  const theme = useTheme();
  const isDarkTheme = theme.palette.mode === 'dark';
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    clearErrors,
    setValue,
    getValues,
    watch,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      storage_values: [],
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    control,
    setValue,
    watch,
    getValues,
  };

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'storage_values',
  });

  const submit = async (values) => {
    try {
      setLoading(true);
      await axios.patch(`vehicles/addStorage/${selectedItem.id}`, {
        ...values,
      });
      toast.success('Record updated successfully!');
      setLoading(false);
      setShow(false);
    } catch (error) {
      setLoading(false);
    }
  };
  const totalStorage = () => {
    const storageValues = getValues('storage_values');
    const total = storageValues.reduce(
      (acc, fieldValue) => acc + (fieldValue.cost || 0),
      0,
    );
    setStorageTotal(total);
  };
  useEffect(() => {
    setStorageTotal(0);
    if (selectedItem) {
      setValue(
        'storage_values',
        selectedItem?.vehicle_storages?.map((el) => {
          return {
            id: el?.id,
            cost: el?.cost,
            date: el?.date?.split('T')[0], // remove timezone
          };
        }) ?? [],
      );
      totalStorage();
    }
  }, [selectedItem]);

  return (
    <Modal
      open={show}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Card
        sx={{
          position: 'absolute' as 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          maxWidth: 1200,
          minWidth: '60%',
          bgcolor: 'background.paper',
        }}
      >
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Storage Costs</Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: 'grey',
            }}
            onClick={() => {
              setShow(false);
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <Box
          sx={{
            m: 0,
            p: 2,
            height: '60vh',
            overflow: 'auto',
            position: 'relative',
          }}
        >
          {singleFetchLoading ? (
            <LoadingPage
              style={{
                position: 'absolute',
                transform: 'translate(-50%, -50%)',
                top: '50%',
                left: '50%',
              }}
            />
          ) : (
            <>
              {fields.map((item: any, i) => (
                <Box
                  sx={{
                    py: 2,
                    px: 2,
                    pr: 6,
                    mb: 1,
                    border: '1px solid gray',
                    borderRadius: '6px',
                    position: 'relative',
                  }}
                  key={item.id}
                >
                  <IconButton
                    size="small"
                    color="error"
                    onClick={async () => {
                      remove(i);
                      totalStorage();
                    }}
                    aria-label="delete"
                    sx={{ position: 'absolute', right: 10, top: 10 }}
                  >
                    <CloseIcon />
                  </IconButton>
                  {/* <Controller
                    name={`booking_targets.${i}.destination_id`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <AutoComplete
                        url={false}
                        label="POD (Port of Destination)"
                        fieldName="destination_id"
                        field={field}
                        error={error}
                        staticOptions={destinations.map((item) => ({
                          id: item.id,
                          label: item.name,
                        }))}
                        column={'name'}
                        modal={'destinations'}
                      />
                    )}
                  /> */}
                  <Controller
                    control={form.control}
                    name={`storage_values.${i}.date`}
                    render={({ field, fieldState: { error } }) => (
                      <DatePicker
                        views={['year', 'month', 'day']}
                        label="Storage Date"
                        value={
                          !field.value ? null : dayjs(field.value).toDate()
                        }
                        format="yyyy/MM/dd"
                        onChange={(e) => {
                          field.onChange(formFormatDate(e));
                        }}
                        inputRef={field.ref}
                        slotProps={{
                          textField: {
                            variant: 'outlined',
                            error: !!error,
                            helperText: error?.message,
                            size: 'small',
                            fullWidth: true,
                          },
                        }}
                      />
                    )}
                  />
                  <Controller
                    name={`storage_values.${i}.cost`}
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={error ? true : false}
                        value={field.value ?? 0}
                        label="Storage Cost Value"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) => {
                          field.onChange(
                            value.target.value !== ''
                              ? +value.target.value
                              : '',
                          );

                          totalStorage();
                        }}
                        ref={field.ref}
                        helperText={error?.message}
                        sx={{ mt: 2 }}
                      />
                    )}
                  />
                </Box>
              ))}
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => {
                    append({});
                  }}
                >
                  Add Storage
                </Button>
              </Box>
            </>
          )}
        </Box>
        <Box sx={{ display: 'flex', p: 2, width: '100%' }}>
          <Box sx={{ display: 'flex', justifyContent: 'start', p: 2 }}>
            <Box
              sx={{
                mx: '2.5px',
                border: '2px solid #2e7d32',
                py: 1,
                px: 2,
                borderRadius: '3px',
                position: 'relative',
                minWidth: '140px',
              }}
            >
              <span>{storageTotal}</span>
              <span
                style={{
                  position: 'absolute',
                  top: '-20%',
                  fontSize: '11px',
                  backgroundColor: isDarkTheme ? 'hsl(var(--border))' : 'white',
                  padding: '0px 10px',
                  left: '5%',
                  color: '#2e7d32',
                  border: 'none',
                }}
              >
                TOTAL COSTS
              </span>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', marginLeft: 'auto', p: 2 }}>
            <Button
              disabled={loading}
              endIcon={<SaveIcon />}
              variant="contained"
              type="submit"
              color="success"
              sx={{ mx: '2.5px' }}
              onClick={handleSubmit(submit)}
              loading={loading}
              loadingPosition="end"
            >
              <span>Submit</span>
            </Button>
          </Box>
        </Box>
      </Card>
    </Modal>
  );
};

export default AddStorageForm;
