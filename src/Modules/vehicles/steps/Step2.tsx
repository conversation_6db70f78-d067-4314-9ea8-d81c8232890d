import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';

import {
  Autocomplete,
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { Controller } from 'react-hook-form';
import {
  account_owner_status,
  ach,
  deliverLocations,
  inspection_options,
  is_Key_present,
  titleStepOne,
} from '@/configs/vehicles/vehiclesHeader';
import { formFormatDate } from '@/configs/configs';

export default function Step2({ form }) {
  const title_status = form.watch('title_status');
  const title_delivery_location = form.watch('title_delivery_location');

  if (!titleStepOne.includes(title_status) && title_status) {
    form.setValue('title_status', 'CUSTOM');
    form.setValue('title_status_custom', title_status);
  }
  if (
    !deliverLocations.includes(title_delivery_location) &&
    title_delivery_location
  ) {
    form.setValue('title_delivery_location', 'OTHER');
    form.setValue('title_delivery_location_other', title_delivery_location);
  }

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        Vehicles Info
      </Typography>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="yard_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url="autoComplete"
                label="UAE Yards"
                fieldName="name"
                field={field}
                error={error}
                staticOptions={false}
                column={'name'}
                modal={'pgl_used_cars_yards'}
              />
            )}
          />
        </Grid>
        {/* <Grid item xs={12} md={6}>
          <Controller
            control={form.control}
            name="purchased_at"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Purchased Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            control={form.control}
            name="payment_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                timezone="America/Los_Angeles"
                views={['year', 'month', 'day']}
                label="Payment Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                minDate={dayjs()
                  .tz('America/Los_Angeles')

                  .startOf('day')
                  .toDate()}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid> */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.towing_company?.message.length > 0 ? true : false
            }
            id="towing_company"
            required
            label="Towing Company"
            autoComplete="on"
            fullWidth
            variant="outlined"
            {...form.register('towing_company')}
            helperText={form.errors.towing_company?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.vehicle_description?.message.length > 0 ? true : false
            }
            id="vehicle_description"
            required
            label="Description"
            fullWidth
            variant="outlined"
            {...form.register('vehicle_description')}
            helperText={form.errors.vehicle_description?.message}
          />
        </Grid>
        {/* <Grid item xs={12} md={6}>
          <Controller
            name="tow_amount"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.tow_amount?.message.length > 0 ? true : false
                }
                id="Tow Cost on PGL"
                value={field.value}
                label="Tow Cost on PGL"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid> */}
        {/* <Grid item xs={12} md={6}>
          <Controller
            name="dismantle_costs"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.dismantle_costs?.message.length > 0 ? true : false
                }
                id="Dismantle Cost on PGL"
                value={field.value}
                label="Dismantle Cost on PGL"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid> */}
        {/* <Grid item xs={12} md={6}>
          <Controller
            name="storage_cost"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.storage_cost?.message.length > 0 ? true : false
                }
                id="Storage Cost on PGL"
                value={field.value}
                label="Storage Cost on PGL"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid> */}
        {/* <Grid item xs={12} md={6}>
          <Controller
            name="title_cost"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.title_cost?.message.length > 0 ? true : false
                }
                id="Title Cost on PGL"
                value={field.value}
                label="Title Cost on PGL"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid> */}
        {/* <Grid item xs={6} md={6}>
          <Controller
            name="damage_cost"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.damage_cost?.message.length > 0 ? true : false
                }
                id="Damage Cost on PGL"
                value={field.value}
                label="Damage Cost on PGL"
                fullWidth
                type="number"
                variant="outlined"
                onChange={(value) =>
                  field.onChange(
                    value.target.value !== '' ? +value.target.value : '',
                  )
                }
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid> */}
        <Grid
          size={{
            xs: 6,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.hat_number?.message.length > 0 ? true : false}
            id="hat_number"
            label="Hat Number"
            fullWidth
            variant="outlined"
            {...form.register('hat_number')}
            helperText={form.errors.hat_number?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.check_no?.message.length > 0 ? true : false}
            id="check_id"
            label="Check No.-Issued to Tow"
            fullWidth
            variant="outlined"
            {...form.register('check_no')}
            helperText={form.errors.check_no?.message}
          />
        </Grid>

        {/* <Grid
          item
          xs={12} md={6}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <FormLabel component="legend">Is Key Present</FormLabel>
          <FormControl component="fieldset">
            <Controller
              control={form.control}
              name="is_key_present"
              render={({ field }) => (
                <Checkbox
                  {...field}
                  checked={field.value}
                  onChange={(e) => field.onChange(e.target.checked)}
                />
              )}
            />
          </FormControl>
        </Grid> */}

        {/* <Grid
          item
          xs={12} md={6}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'start',
          }}
        >
          <Controller
            control={form.control}
            name="is_key_present"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="is_key_present"
                      />
                    }
                    label="Is Key Present"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid> */}

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="is_key_present"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Is key present"
                fieldName=""
                field={field}
                error={error}
                staticOptions={is_Key_present}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: title_status === 'CUSTOM' ? 6 : 12,
            md: title_status === 'CUSTOM' ? 3 : 6,
          }}
        >
          <Controller
            name="title_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Title Status Step1"
                fieldName="title_status"
                field={field}
                error={error}
                staticOptions={titleStepOne}
                column={''}
                modal={''}
                customeName={'title_status'}
                onSelect={(e) => {
                  field.onChange(e);
                }}
              />
            )}
          />
        </Grid>
        {title_status === 'CUSTOM' ? (
          <Grid
            size={{
              xs: 3,
              md: 3,
            }}
          >
            <TextField
              size="small"
              error={
                form.errors.title_status?.message.length > 0 ? true : false
              }
              id="title_status_Custom"
              label="Title Status Step1 Custom"
              fullWidth
              variant="outlined"
              {...form.register('title_status_custom')}
              helperText={form.errors.title_status_custom?.message}
            />
          </Grid>
        ) : (
          ''
        )}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.title_status_step_two?.message.length > 0
                ? true
                : false
            }
            id="title_status_step_two"
            label="Title Status Step2"
            fullWidth
            variant="outlined"
            {...form.register('title_status_step_two')}
            helperText={form.errors.title_status_step_two?.message}
          />
        </Grid>
        {title_status !== 'CUSTOM' && (
          <>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <TextField
                size="small"
                error={form.errors.trn?.message.length > 0 ? true : false}
                id="trn"
                label="TRN"
                fullWidth
                variant="outlined"
                {...form.register('trn')}
                helperText={form.errors.trn?.message}
              />
            </Grid>
            <Grid
              size={{
                xs: title_delivery_location === 'OTHER' ? 6 : 12,
                md: title_delivery_location === 'OTHER' ? 3 : 6,
              }}
            >
              <Controller
                name="title_delivery_location"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Deliver Locations"
                    fieldName="title_delivery_location"
                    field={field}
                    error={error}
                    staticOptions={deliverLocations}
                    column={''}
                    modal={''}
                    customeName={'title_delivery_location'}
                    onSelect={(e) => {
                      field.onChange(e);
                    }}
                  />
                )}
              />
            </Grid>
            {title_delivery_location === 'OTHER' ? (
              <Grid
                size={{
                  xs: 3,
                  md: 3,
                }}
              >
                <TextField
                  size="small"
                  error={
                    form.errors.title_delivery_location_other?.message.length >
                    0
                      ? true
                      : false
                  }
                  id="title_delivery_location_other"
                  label="Title Deliver Location Custom"
                  fullWidth
                  variant="outlined"
                  {...form.register('title_delivery_location_other')}
                  helperText={
                    form.errors.title_delivery_location_other?.message
                  }
                />
              </Grid>
            ) : (
              ''
            )}
          </>
        )}

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="last_title_follow_up_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Last Title Follow Up Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="title_receive_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Title Receive Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="date_posted_in_central_dispatch"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Date Posted In Central Dispatch"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.posted_by_in_central_dispatch?.message.length > 0
                ? true
                : false
            }
            id="posted_by_in_central_dispatch"
            label="Posted By In Central Dispatch"
            fullWidth
            variant="outlined"
            {...form.register('posted_by_in_central_dispatch')}
            helperText={form.errors.posted_by_in_central_dispatch?.message}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            id="auction_invoice"
            label="Auction Invoice Link"
            fullWidth
            variant="outlined"
            {...form.register('auction_invoice')}
            error={
              form.errors.auction_invoice?.message.length > 0 ? true : false
            }
            helperText={form.errors.auction_invoice?.message}
          />
        </Grid>

        {/* <Grid item xs={12} md={6}>
          <Controller
            name="halfcut_status"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Halfcut Status"
                fieldName=""
                field={field}
                error={error}
                staticOptions={vehicles_halfcut_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid> */}

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="account_owner"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="Acount Owner"
                fieldName=""
                field={field}
                error={error}
                staticOptions={account_owner_status}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.auction_photos_link?.message.length > 0 ? true : false
            }
            id="auction_photos_link"
            label="Auction photos link"
            fullWidth
            variant="outlined"
            {...form.register('auction_photos_link')}
            helperText={form.errors.auction_photos_link?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.photo_link?.message.length > 0 ? true : false}
            id="photo_link"
            label="Photo Link"
            fullWidth
            variant="outlined"
            {...form.register('photo_link')}
            helperText={form.errors.photo_link?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="pickup_due_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Pickup Due Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 6,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.hat_number?.message.length > 0 ? true : false}
            id="title_tracking_number"
            label="Outside of USA Title TRN#"
            fullWidth
            variant="outlined"
            {...form.register('title_tracking_number')}
            helperText={form.errors.hat_number?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="title_tracking_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Title Tracking Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="title_tracking_arrival_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Title Tracking Arrival Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="pickup_status"
            render={({ field }) => (
              <FormControl
                sx={{ width: '100%', textAlign: 'left' }}
                size="small"
              >
                <InputLabel id="demo-select-small-label">
                  Picked Up Status
                </InputLabel>
                <Select
                  {...field}
                  labelId="demo-select-small-label"
                  id="demo-select-small"
                  label="Picked Up Status"
                  size="small"
                  sx={{ width: '100%' }}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  <MenuItem value={'picked_up'}>Picked Up</MenuItem>
                  <MenuItem value={'delivered'}>Delivered</MenuItem>
                </Select>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          sx={{ my: 0.2 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="inspection"
            control={form.control}
            render={({ field, fieldState: { error, invalid } }) => {
              const selectedInspection =
                inspection_options.find((row) => row.id == field.value) ?? null;
              return (
                <Autocomplete
                  size="small"
                  value={selectedInspection}
                  getOptionLabel={(option) =>
                    option?.label ? option?.label : ''
                  }
                  onChange={(_event, value) => {
                    field.onChange(value?.id ?? null);
                  }}
                  options={inspection_options}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Inspection"
                      error={invalid}
                      helperText={error?.message}
                    />
                  )}
                />
              );
            }}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={
              form.errors.inspector_name?.message.length > 0 ? true : false
            }
            id="inspector_name"
            label="Inspector Name"
            fullWidth
            variant="outlined"
            {...form.register('inspector_name')}
            helperText={form.errors.inspector_name?.message}
          />
        </Grid>
        <Grid
          style={{ display: 'flex' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="pickup_photos"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="pickup_photos"
                      />
                    }
                    label="Pick up Photos Compared "
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>

        <Grid
          style={{ display: 'flex' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="delivery_photos"
            render={({ field }) => (
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        inputRef={field.ref}
                        checked={field.value}
                        onChange={field.onChange}
                        name="delivery_photos"
                      />
                    }
                    label="Delivery Photos Compared"
                  />
                </FormGroup>
              </FormControl>
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="ach"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <AutoComplete
                url={false}
                label="ACH Payment Approved"
                fieldName=""
                field={field}
                error={error}
                staticOptions={ach}
                column={''}
                modal={''}
              />
            )}
          />
        </Grid>
        {/* Only show dispatch_type field if we're in edit mode (when the value is already set) */}
        {form.watch('dispatch_type') && (
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              control={form.control}
              name="dispatch_type"
              render={({ field }) => (
                <FormControl
                  sx={{ width: '100%', textAlign: 'left' }}
                  size="small"
                >
                  <InputLabel id="demo-select-small-label">
                    Dispatch Type
                  </InputLabel>
                  <Select
                    {...field}
                    labelId="demo-select-small-label"
                    id="demo-select-small"
                    label="Dispatch Type"
                    size="small"
                    sx={{ width: '100%' }}
                  >
                    <MenuItem value={'self_pickup_not_delivered_to_pgl'}>
                      Self Pickup Not PGL
                    </MenuItem>
                    <MenuItem value={'dispatch'}>Dispatch</MenuItem>
                    <MenuItem value={'self_pickup_delivered_to_pgl'}>
                      Self Pickup PGL
                    </MenuItem>
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        )}

        <Grid size={6}>
          <TextField
            error={
              form.errors.dispatch_remark?.message.length > 0 ? true : false
            }
            id="dispatch_remark"
            required
            label="Dispatch Remark"
            multiline
            rows={3}
            fullWidth
            variant="outlined"
            {...form.register('dispatch_remark')}
            helperText={form.errors.dispatch_remark?.message}
          />
        </Grid>
        <Grid size={6}>
          <TextField
            error={
              form.errors.auction_remark?.message.length > 0 ? true : false
            }
            id="auction_remark"
            required
            label="Auction Remark"
            multiline
            rows={3}
            fullWidth
            variant="outlined"
            {...form.register('auction_remark')}
            helperText={form.errors.auction_remark?.message}
          />
        </Grid>
        <Grid size={6}>
          <TextField
            error={
              form.errors.customer_remark?.message.length > 0 ? true : false
            }
            id="customer_remark"
            required
            label="Customer Remark"
            multiline
            rows={3}
            fullWidth
            variant="outlined"
            {...form.register('customer_remark')}
            helperText={form.errors.customer_remark?.message}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.remark?.message.length > 0 ? true : false}
            id="damage_note"
            label="Damage Note"
            fullWidth
            variant="outlined"
            {...form.register('damage_note')}
            helperText={form.errors.damage_note?.message}
          />
        </Grid>
        {/* <Grid item xs={12} md={6}>
          <TextField
            size="small"
            error={form.errors.towed_from?.message.length > 0 ? true : false}
            id="towed_from"
            label="Towed From"
            fullWidth
            variant="outlined"
            {...form.register('towed_from')}
            helperText={form.errors.towed_from?.message}
          />
        </Grid> */}
      </Grid>
    </Box>
  );
}
