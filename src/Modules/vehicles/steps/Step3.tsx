import React, { useEffect, useState } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  FormHelperText,
  Grid,
  TextField,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import axios from '@/lib/axios';
import CancelIcon from '@mui/icons-material/Cancel';
import Tooltip from '@mui/material/Tooltip';
import { allChargesMap } from '@/Modules/GeneralSetting/Companies/steps/Step1Next';
import { nanoid } from 'nanoid';

type ChargeType = {
  id: number;
  name: string;
  const: number;
  amount: number;
  category: string;
  remark: string;
};

const defaultCharge: ChargeType = {
  id: -1,
  name: 'none',
  const: 0,
  amount: 0,
  category: '',
  remark: '',
};

const mixVehicleChargesMap: Record<string, string> = {
  attestation_fee: 'Attestation Fee',
  inspection_charges: 'Inspection Charges',
  title_charges: 'Title Charges',
  sharjah_yard_storage: 'Sharjah Yard Storage',
  fed_ex_or_mailing_fee: 'FedEx/ Mailing Fee',
  recovery_fee: 'Recovery Fee',
  custom_hold: 'Custom Hold',
  relist_fee: 'Relist Fee',
  detention_charges: 'Detention Charges',
  shortage: 'Shortage',
  suv_charges: 'SUV Charges',
  tds_charges: 'TDS Charges',
  registration_fee: 'Register Fee',
  transportation_fee: 'Transport Fee',
  office_fee_and_bank: 'Office Fee & Bank',
  empty_containers: 'Empty Containers',
  coo_charges: 'COO Charges',
  hybrid_charges: 'Hybrid Charges',
  other_charges: 'Other Charges',
};

export default function Step3({ form, item }) {
  const [totalCost, setTotalCost] = useState(0);
  const [profitLoss, setProfitLoss] = useState(0);
  const [percentProfit, setPercentProfit] = useState(0);
  const [companyCharges, setCompanyCharges] = useState([]);
  const [vehicleCharges, setVehicleCharges] = useState(
    form.getValues('vehicle_charges') || [],
  );
  const [chargeToBeAdded, setChargeToBeAdded] =
    useState<ChargeType>(defaultCharge);
  const [remainingCharges, setRemainingCharges] = useState([]);
  const [showSelectChargeWarning, setShowSelectChargeWarning] = useState(false);

  const canEdit: boolean =
    item?.containers == null ||
    item?.containers?.invoices?.length == 0 ||
    (item?.containers?.invoices?.length &&
      !['open', 'past_due', 'paid'].includes(
        item?.containers?.invoices[0].status,
      ));

  const canEdit2 =
    canEdit || item?.containers?.prelim_pl_status != 'reviewed' || true;

  const handleInputChange = () => {
    const vehiclePrice = parseFloat(form.getValues('vehicle_price')) || 0;
    const otherCost = parseFloat(form.getValues('other_costs')) || 0;
    const towingCost = parseFloat(form.getValues('towing_cost')) || 0;
    const dismantalCost = parseFloat(form.getValues('dismantal_cost')) || 0;
    const shipCost = parseFloat(form.getValues('ship_cost')) || 0;
    const pglStorageCosts =
      parseFloat(form.getValues('pgl_storage_costs')) || 0;
    const titleCharge = parseFloat(form.getValues('title_charge')) || 0;
    const dubaiCustomCost =
      parseFloat(form.getValues('dubai_custom_cost')) || 0;
    const salesCost = parseFloat(form.getValues('sales_cost')) || 0;

    const total = (
      vehiclePrice +
      otherCost +
      towingCost +
      dismantalCost +
      shipCost +
      pglStorageCosts +
      titleCharge +
      dubaiCustomCost
    ).toFixed(2);
    const calculatedProfitLoss = +salesCost - parseFloat(total);
    const calculatedPercentProfit = calculatedProfitLoss
      ? parseFloat(
          ((calculatedProfitLoss / parseFloat(total)) * 100).toFixed(2),
        )
      : 0;

    setProfitLoss(calculatedProfitLoss);
    setPercentProfit(calculatedPercentProfit);
    setTotalCost(parseFloat(total));
  };

  const getCompanyCharges = async (companyId) => {
    try {
      let { data } = await axios.get(
        `companies/company-charges/${companyId}?charge_type=per_vehicle`,
      );
      console.log(data);

      let filteredCharges;
      if (form.getValues('is_mix_vehicle')) {
        filteredCharges = data?.company_charges.filter(
          (charge) =>
            charge.name && mixVehicleChargesMap.hasOwnProperty(charge.name),
        );
        const missingChargeKeys = Object.keys(mixVehicleChargesMap).filter(
          (key) => !data?.company_charges.some((charge) => charge.name === key),
        );

        const newCharge = (name) => {
          return {
            id: nanoid(),
            name: name,
            amount: 0,
            cost: 0,
            remark: '',
            category: 'finance',
          };
        };

        const missingCharges = missingChargeKeys.map((key) => newCharge(key));

        filteredCharges = [...filteredCharges, ...missingCharges];
      } else {
        filteredCharges = data?.company_charges.filter(
          (charges) =>
            charges.name &&
            (charges.category === 'finance' ||
              charges.category === 'title' ||
              charges.category === 'fedex' ||
              charges.category === 'auction'),
        );
      }

      if (data?.company_charges.length > 0) {
        setCompanyCharges(filteredCharges);
      }

      return filteredCharges;
    } catch (error) {
      console.error(error);
    }
  };

  const handleVehicleChargeInputChange = (index, field, value) => {
    const newCharges = [...vehicleCharges];
    newCharges[index][field] = value;
    setVehicleCharges(newCharges);
    handleInputChange();
  };

  const handleAddCharge = (newCharge) => {
    setVehicleCharges((prevState) => [...prevState, newCharge]);
  };

  const handleRemoveCharge = (chargeName) => {
    setVehicleCharges((prevState) =>
      prevState.filter((charge) => charge.name !== chargeName),
    );
  };

  const handleRemoveAllCharges = () => {
    setVehicleCharges([]);
  };

  useEffect(() => {
    const handleChangesInVehicleCharges = async () => {
      let companyChargesToBeSet: any[];

      if (companyCharges.length === 0) {
        companyChargesToBeSet = await getCompanyCharges(
          parseFloat(form.getValues('company_id')),
        );
      } else {
        companyChargesToBeSet = companyCharges;
      }

      setRemainingCharges(
        companyChargesToBeSet.filter(
          (charge) =>
            !vehicleCharges.some(
              (vehicleCharge) => vehicleCharge.name === charge.name,
            ),
        ),
      );
    };
    handleChangesInVehicleCharges().catch((e) =>
      console.log(
        e,
        'error occurred while handling changes in the vehicle charges',
      ),
    );
    handleInputChange();
    form.setValue('vehicle_charges', vehicleCharges);
  }, [vehicleCharges]);

  return (
    <>
      <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
        <Typography variant="h5" sx={{ mb: 3 }}>
          Charge Info
        </Typography>
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="vehicle_price"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.vehicle_price?.message.length > 0}
                  id="vehicle_price"
                  value={field.value}
                  label="Vehicle Price and Auction Fee"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="other_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.other_cost?.message.length > 0}
                  id="other_cost"
                  value={field.value}
                  label="Other Charges"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  // disabled={true}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="towing_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.towing_cost?.message.length > 0}
                  id="towing_cost"
                  value={field.value}
                  label="Tow Charged on Customer"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              size="small"
              // error={}
              id="total_cost_of_vehicle"
              label="total Charges of vehicle"
              fullWidth
              variant="outlined"
              // onChange={(value) => +value.target.value}
              value={+totalCost}
              disabled={!canEdit}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="dismantal_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.dismantal_cost?.message.length > 0}
                  id="dismantal_cost"
                  value={field.value}
                  label="Dismantal Charge on Customer"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="sales_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.sales_cost?.message.length > 0}
                  id="sales_cost"
                  value={field.value}
                  label="Sales Charge"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="ship_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.shipping_cost?.message.length > 0}
                  id="shipping_cost"
                  value={field.value}
                  label="Shipping Charge"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              size="small"
              id="profit_loss"
              label="Profit / Loss"
              fullWidth
              type="number"
              variant="outlined"
              // onChange={(value) => +value.target.value}
              value={profitLoss}
              disabled={!canEdit}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="pgl_storage_costs"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.pgl_storage_costs?.message.length > 0}
                  id="pgl_storage_costs"
                  value={field.value}
                  label="Storage Charged on Customer"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              size="small"
              id="percent_profit"
              label="Percent Profit"
              fullWidth
              variant="outlined"
              // onChange={(value) => +value.target.value}
              value={percentProfit}
              disabled={!canEdit}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="title_charge"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.title_charge?.message.length > 0}
                  id="title_charge"
                  value={field.value}
                  label="Title Charged on Customer"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="dubai_custom_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.dubai_custom_cost?.message.length > 0}
                  id="dubai_custom_cost"
                  value={field.value}
                  label="Custom Charge"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => {
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    );
                    handleInputChange();
                  }}
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit}
                />
              )}
            />
          </Grid>
          {/* <Grid item xs={12} md={6}>
            <TextField
              size="small"
              error={form.errors.towed_from?.message.length > 0 ? true : false}
              id="vin"
              label="Towed From"
              fullWidth
              variant="outlined"
              {...form.register('towed_from')}
              helperText={form.errors.towed_from?.message}
            />
          </Grid> */}
          {/* <Grid item xs={12} md={6}></Grid> */}
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              error={form.errors.invoice_description?.message.length > 0}
              id="invoice_description"
              label="Invoice Description"
              multiline
              rows={2}
              fullWidth
              variant="outlined"
              {...form.register('invoice_description')}
              helperText={form.errors.invoice_description?.message}
              disabled={!canEdit}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <TextField
              error={form.errors.add_information?.message.length > 0}
              id="add_information"
              label="Additional Information"
              multiline
              rows={2}
              fullWidth
              variant="outlined"
              {...form.register('add_information')}
              helperText={form.errors.add_information?.message}
              disabled={!canEdit}
            />
          </Grid>
        </Grid>
      </Box>
      <Box borderTop={0.5} borderColor="#DCDCDC" ml={2}>
        <Grid
          container
          border={1}
          borderColor="#DCDCDC"
          borderRadius={2}
          mt={2}
        >
          <Grid
            container
            display="flex"
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            borderBottom={1}
            borderColor="#DCDCDC"
            mb={3}
            pl={2}
            pr={1}
          >
            <Grid>
              <Typography variant="h5" my={2}>
                Vehicle Charges
              </Typography>
            </Grid>
            <Grid>
              <Tooltip title="Remove All Charges">
                <Button
                  color="error"
                  onClick={() => {
                    handleRemoveAllCharges();
                  }}
                  sx={{
                    minWidth: 'auto',
                  }}
                >
                  <CancelIcon width="42px" />
                </Button>
              </Tooltip>
            </Grid>
          </Grid>
          {vehicleCharges?.map((charge, index) => (
            <Grid
              key={charge.id}
              style={{ display: 'flex' }}
              gap={2}
              mb={2}
              mx={2}
              size={{
                xs: 12,
                md: 12,
              }}
            >
              <Grid
                size={{
                  xs: 12,
                  md: 7,
                }}
              >
                <Controller
                  name={`charges_name_${charge.id}`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={form.errors.cost?.message.length > 0}
                      id="name"
                      value={
                        form.getValues('is_mix_vehicle')
                          ? mixVehicleChargesMap[charge.name]
                          : allChargesMap[charge.name]
                      }
                      label="Name"
                      fullWidth
                      type="text"
                      onChange={(e) =>
                        handleVehicleChargeInputChange(
                          index,
                          'name',
                          e.target.value,
                        )
                      }
                      ref={field.ref}
                      helperText={error?.message}
                      disabled={true}
                    />
                  )}
                />
              </Grid>
              <Grid
                size={{
                  xs: 12,
                  md: 1.5,
                }}
              >
                <Controller
                  name={`charges_amount_${charge.id}`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={form.errors.amount?.message.length > 0}
                      id="amount"
                      value={charge.amount}
                      label="Amount"
                      fullWidth
                      type="number"
                      onChange={(e) =>
                        handleVehicleChargeInputChange(
                          index,
                          'amount',
                          Number(e.target.value),
                        )
                      }
                      ref={field.ref}
                      helperText={error?.message}
                      disabled={!canEdit}
                    />
                  )}
                />
              </Grid>
              <Grid
                size={{
                  xs: 12,
                  md: 1.5,
                }}
              >
                <Controller
                  name={`charges_cost_${charge.id}`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={form.errors.cost?.message.length > 0}
                      id="cost"
                      value={charge.cost}
                      label="Cost"
                      fullWidth
                      type="number"
                      onChange={(e) =>
                        handleVehicleChargeInputChange(
                          index,
                          'cost',
                          Number(e.target.value),
                        )
                      }
                      ref={field.ref}
                      helperText={error?.message}
                      disabled={!canEdit}
                    />
                  )}
                />
              </Grid>
              <Grid
                size={{
                  xs: 12,
                  md: 1.5,
                }}
              >
                <Controller
                  name={`charges_remark_${charge.id}`}
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={form.errors.remark?.message.length > 0}
                      id="remark"
                      value={charge.remark}
                      label="Remark"
                      fullWidth
                      onChange={(e) =>
                        handleVehicleChargeInputChange(
                          index,
                          'remark',
                          e.target.value,
                        )
                      }
                      ref={field.ref}
                      helperText={error?.message}
                      disabled={!canEdit}
                    />
                  )}
                />
              </Grid>
              <Grid
                display="flex"
                flexDirection="column"
                justifyContent="start"
                alignItems="start"
                size={0.5}
              >
                <Tooltip title="Remove Charge">
                  <Button
                    color="error"
                    onClick={() => {
                      handleRemoveCharge(charge.name);
                    }}
                    sx={{
                      margin: '-2px 0 2px 0',
                      minWidth: 'auto',
                    }}
                  >
                    <CancelIcon />
                  </Button>
                </Tooltip>
              </Grid>
            </Grid>
          ))}
          <Grid container my={1.2} gap={2} mx={2}>
            <Grid
              size={{
                xs: 7,
                lg: 5,
              }}
            >
              <Autocomplete
                fullWidth
                size="small"
                id="add_company_charges"
                options={[defaultCharge, ...remainingCharges]}
                getOptionLabel={(option) =>
                  option.name === 'none'
                    ? 'None'
                    : form.getValues('is_mix_vehicle')
                      ? mixVehicleChargesMap[option.name]
                      : allChargesMap[option.name]
                }
                value={chargeToBeAdded}
                onChange={(_, newValue) => {
                  setChargeToBeAdded(newValue ?? defaultCharge);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Company Charges"
                    error={
                      chargeToBeAdded.name === 'none' &&
                      remainingCharges.length > 0 &&
                      showSelectChargeWarning
                    }
                  />
                )}
              />
              <FormHelperText error>
                {chargeToBeAdded.name === 'none' &&
                  remainingCharges.length > 0 &&
                  showSelectChargeWarning &&
                  'Select a charge'}
              </FormHelperText>
            </Grid>
            <Grid
              p={0}
              size={{
                xs: 4,
                lg: 3,
              }}
            >
              <Button
                sx={{ height: '34px' }}
                variant="outlined"
                color="primary"
                onClick={() => {
                  if (chargeToBeAdded.name !== 'none') {
                    handleAddCharge(chargeToBeAdded);
                    setChargeToBeAdded(defaultCharge);
                    setShowSelectChargeWarning(false);
                  } else {
                    setShowSelectChargeWarning(true);
                  }
                }}
                disabled={remainingCharges.length === 0}
              >
                Add Charge
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      {/* plg costs */}
      <Box sx={{ py: 1, ml: 2, textAlign: 'center' }}>
        <Typography
          variant="h5"
          sx={{ mb: 1 }}
          borderTop={0.5}
          borderColor="#DCDCDC"
          pt={2}
          mt={2}
        >
          Costs on PGL
        </Typography>
        <Grid container spacing={2}>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="tow_amount"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.tow_amount?.message.length > 0}
                  id="tow_amount"
                  value={field.value}
                  label="Tow Cost on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit2}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 6,
              md: 6,
            }}
          >
            <Controller
              name="damage_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.damage_cost?.message.length > 0}
                  id="Damage Cost on PGL"
                  value={field.value}
                  label="Damage Cost on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit2}
                />
              )}
            />
          </Grid>

          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="dismantle_costs"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.dismantle_costs?.message.length > 0}
                  id="Dismantle Cost on PGL"
                  value={field.value}
                  label="Dismantle Cost on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit2}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="title_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.title_cost?.message.length > 0}
                  id="Title Cost on PGL"
                  value={field.value}
                  label="Title Cost on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit2}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="storage_cost"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.storage_cost?.message.length > 0}
                  id="Storage Cost on PGL"
                  value={field.value}
                  label="Storage Cost on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  disabled={!canEdit2}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Controller
              name="other_costs"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.other_costs?.message.length > 0}
                  id="Other Costs on PGL"
                  value={field.value}
                  label="Other Costs on PGL"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                  // disabled={true}
                />
              )}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 12,
            }}
          >
            <TextField
              error={form.errors.cost_remark?.message.length > 0}
              id="cost_remark"
              label="Costs Remarks"
              multiline
              rows={2}
              fullWidth
              variant="outlined"
              {...form.register('cost_remark')}
              helperText={form.errors.cost_remark?.message}
            />
          </Grid>
        </Grid>
      </Box>
    </>
  );
}
