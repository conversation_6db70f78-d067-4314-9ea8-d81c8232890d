import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';

import {
  Autocomplete,
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Controller } from 'react-hook-form';
import {
  is_title_exist,
  load_type_options,
  tax_status_options,
} from '@/configs/vehicles/vehiclesHeader';
import axios from '@/lib/axios';
import { formFormatDate } from '@/configs/configs';
import { useLocationContext } from '@/contexts/LocationsContext';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { contextProvider } from '@/contexts/ProfileContext';
import { vehicles_halfcut_status2 } from '@/configs/vehicles/halfcutHeader';
import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
import { fuel_type_enum, vehicle_size_enum } from '@/configs/vehicles/configs';

export default function Step1({
  form,
  selectedCarState,
  isUpdate,
  setSelectedCompany,
  hasPayments,
  isManualAuctionCity,
  setIsManualAuctionCity,
}) {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(customParseFormat);

  const now = dayjs().utc().tz('America/New_York');
  const noonToday = now.clone().startOf('day').add(12, 'hour');

  const isAfterNoonInSavannah = now.isAfter(noonToday);

  //////////////////////////////////// states ///////////////////////////////////////////
  const [customers, setCustomers] = useState([]);
  const [bannedLocations, setBannedLocations] = useState([]);
  const [yardLocations, setYardLocations] = useState([]);
  const [locationId, setLocationId] = useState();
  const [companyId, setCompanyId] = useState();
  const [renderKeyCus, setRenderKeyCus] = useState(-1);
  const [renderKey, setRenderKey] = useState(0);
  const [vinError, setVinError] = useState(false);
  const [compErr, setCompErr] = useState(false);
  const [loadErr, setLoadErr] = useState(false);
  const [companyList, setCompanyList] = useState([]);
  const [isScrapAvailable, setIsScrapAvailable] = useState(false);
  const useProfileContext = useContext(contextProvider);
  const { profile, hasEveryPermission } = useProfileContext;
  const dept_id = profile?.data?.department_id;

  const id = form.watch('company_id');
  const location_id = form.watch('point_of_loading');
  const halfcut_status = form.watch('halfcut_status');
  const ready_for_pickup_date_editable = form.watch(
    'ready_for_pickup_date_editable',
  );

  // const hasPastPaymentDatePerm =
  //////////////////////////////////// function ///////////////////////////////////////////

  const s_array = (data, fieldName) =>
    data &&
    Object?.values(data).map((value: any) => ({
      id: value?.id,
      label: value?.[fieldName] ? value?.[fieldName] : '',
    }));

  const { map }: any = useLocationContext();
  const locations = map((item) => ({
    label: item.name,
    id: item.id,
  })).filter((item) => !bannedLocations.includes(item.id));

  const controllerRef = useRef(null);

  const getCustomers = async () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }

    // Create a new AbortController
    controllerRef.current = new AbortController();
    const signal = controllerRef.current.signal;

    try {
      const res = await axios.get(`customers/companyCustomer/${companyId}`, {
        signal,
      });
      if (res.status === 200) {
        setCustomers(s_array(res.data?.data, 'fullname'));
        setBannedLocations(
          res.data?.data[0]?.companies?.banned_locations
            ? res.data?.data[0]?.companies?.banned_locations?.map(
                (item) => item.id,
              )
            : [],
        );
        setRenderKeyCus((prv) => prv + -1);
      }
    } catch (error) {}
  };

  const getCompanyYardLocation = async (id, location_id) => {
    try {
      const res = await axios.get(`companies/company-yard-locations/${id}`, {
        params: {
          location_id,
        },
      });
      if (res.status === 200) {
        const cmYardLocations = res?.data?.data?.yard_locations;
        if (cmYardLocations && cmYardLocations?.length > 0) {
          form?.setValue('yard_location_id', cmYardLocations[0]?.id);
          setRenderKeyCus((prv) => prv + -1);
        } else {
          form?.setValue('yard_location_id', null);
        }
        if (!isUpdate) {
          updateYardLocation(halfcut_status, location_id);
        }
      }
    } catch (error) {}
  };

  const getYards = async () => {
    try {
      const res = await axios.get(
        `yardsLocations/locationYardLocation/${locationId}`,
      );
      if (res.status === 200) {
        setYardLocations(s_array(res.data?.data, 'name'));
        setRenderKey((prv) => prv + 1);
      }
    } catch (error) {}
  };

  const updateYardLocation = (status, location_id) => {
    form?.setValue(
      'yard_location_id',
      status == 'half_cut' && location_id === 2 ? 17 : null,
    );
  };

  useEffect(() => {
    setCompanyId(id);
    if (id != undefined && companyId == id && id != '') {
      setCustomers([]);
      form.setValue('customer_id', null);
      setRenderKeyCus((prv) => prv + -1);
      getCustomers();
    }
  }, [id, companyId]);

  useEffect(() => {
    if (location_id && id && !isUpdate) {
      getCompanyYardLocation(id, location_id);
    }
  }, [location_id, id, isUpdate]);

  useEffect(() => {
    setLocationId(location_id);
    if (
      location_id != undefined &&
      locationId == location_id &&
      location_id != ''
    ) {
      getYards();
      if (yardLocations.length > 0) {
        form?.setValue('yard_location_id', null);
      }
    }
  }, [location_id, locationId]);

  useEffect(() => {
    const tempComp = companyList.find((row) => row.id == id);

    setIsScrapAvailable(tempComp?.scrap ?? false);
    setSelectedCompany(tempComp);
  }, [companyList]);

  useEffect(() => {
    const tempComp = companyList.find((row) => row.id == id);
    if (tempComp != undefined) {
      let load_type = null;
      let halfcutStatus = 'unknown';

      if (tempComp?.complete || tempComp?.mix) {
        halfcutStatus = 'completed';
      } else if (tempComp?.complete_halfcut || tempComp?.mix_halfcut) {
        halfcutStatus = 'half_cut';
      }

      load_type =
        (halfcutStatus === 'completed' &&
          ((tempComp?.complete && 'full') || (tempComp?.mix && 'mix'))) ||
        (halfcutStatus === 'half_cut' &&
          ((tempComp?.complete_halfcut && 'full') ||
            (tempComp?.mix_halfcut && 'mix')));

      if (tempComp?.scrap) {
        setIsScrapAvailable(true);
        form.setValue('is_scrap', true);
      } else {
        setIsScrapAvailable(false);
        form.setValue('is_scrap', false);
      }

      if (tempComp?.id == 18 || halfcutStatus == 'unknown') {
        // Unknow compnay id: 18
        load_type = null;
      }
      if (tempComp && !isUpdate && tempComp?.destination_id == 22) {
        form.setValue('destination_id', tempComp?.destination_id);
      }

      form.setValue('halfcut_status', halfcutStatus);
      form.setValue('load_type', load_type);
    }
  }, [id]);

  useEffect(() => {
    if (!halfcut_status || halfcut_status !== 'completed') {
      form.setValue('is_scrap', false);
    }
    if (!isUpdate) {
      updateYardLocation(halfcut_status, location_id);
      const tempComp = companyList.find((row) => row.id == id);
      if (tempComp) {
        const load_type =
          (halfcut_status === 'completed' &&
            ((tempComp?.complete && 'full') || (tempComp?.mix && 'mix'))) ||
          (halfcut_status === 'half_cut' &&
            ((tempComp?.complete_halfcut && 'full') ||
              (tempComp?.mix_halfcut && 'mix')));
        form.setValue('load_type', load_type);
      }
    }
  }, [halfcut_status]);

  // const onHalfcutStatusChange = (val) => {
  //   const tempComp = companyList.find((row) => row.id == id);
  //   if (tempComp != undefined) {
  //     let load_type = '';
  //     if (tempComp?.complete) {
  //       load_type = 'full';
  //     } else if (tempComp?.mix) {
  //       load_type = 'mix';
  //     }
  //     if (val == 'half_cut') {
  //       if (tempComp?.complete_halfcut) {
  //         load_type = 'full';
  //       } else if (tempComp?.mix_halfuct) {
  //         load_type = 'mix';
  //       }
  //     }

  //     form.setValue('load_type', load_type);
  //   }
  // };

  ////////////////////////////////////////// variables ///////////////////////////////////

  yardLocations.length > 0
    ? form.getValues('yard_location_id') || form.watch('yard_location_id')
      ? form?.setIsYardExist(false)
      : form?.setIsYardExist(true)
    : form?.setIsYardExist(false);

  //////////////////////////////// return JSX ////////////////////////////

  return (
    <Box sx={{ pt: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ mb: 1.5 }}>
        General Info
      </Typography>
      <Grid container spacing={2}>
        <Grid
          sx={{ px: 0.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid size={12}>
            <Controller
              name="company_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                if (error) setCompErr(true);
                return (
                  <AutoComplete
                    sx={error ? { mt: 1.5 } : { my: 1.5 }}
                    url="autoComplete"
                    label="Select Company"
                    fieldName="name"
                    field={field}
                    error={error}
                    staticOptions={false}
                    column={'name'}
                    modal={'companies'}
                    emitOptions={setCompanyList}
                  />
                );
              }}
            />
          </Grid>

          <Grid size={12}>
            <Controller
              key={renderKeyCus}
              name="customer_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  sx={compErr ? { mt: 0 } : { my: 1.5 }}
                  url={false}
                  label="Select Customer"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={customers}
                  column={''}
                  modal={''}
                  defualtValue={customers[0]}
                />
              )}
            />
          </Grid>
          <Grid sx={vinError ? { mt: 0 } : { my: 1.5 }} size={12}>
            <Controller
              name="vin"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                if (error) setVinError(true);
                return (
                  <TextField
                    size="small"
                    error={form.errors.vin?.message.length > 0 ? true : false}
                    id="vin"
                    value={field.value ?? ''}
                    label="Vin Number"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                );
              }}
            />
          </Grid>

          <Grid sx={vinError ? { mt: 0 } : { my: 1.5 }} size={12}>
            <Controller
              name="year"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.year?.message.length > 0 ? true : false}
                  id="year"
                  value={field.value ?? ''}
                  label="Year"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="make"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.make?.message.length > 0 ? true : false}
                  id="make"
                  value={field.value ?? ''}
                  label="Make"
                  fullWidth
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(value.target.value.toUpperCase())
                  }
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="model"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.model?.message.length > 0 ? true : false}
                  id="model"
                  value={field.value ?? ''}
                  label="Model"
                  fullWidth
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(value.target.value.toUpperCase())
                  }
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="color"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.color?.message.length > 0 ? true : false}
                  id="color"
                  value={field.value ?? ''}
                  label="Color"
                  fullWidth
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(value.target.value.toUpperCase())
                  }
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="price"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.price?.message.length > 0 ? true : false}
                  id="Price"
                  value={field.value ?? ''}
                  label="Price"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) => field.onChange(+value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="lot_number"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.lot_number?.message.length > 0 ? true : false
                  }
                  id="lot_number"
                  value={field.value ?? ''}
                  label="Lot Number"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid size={12}>
            <Controller
              name="weight"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={form.errors.weight?.message.length > 0 ? true : false}
                  id="weight"
                  value={field.value}
                  label="Weight"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="purchased_at"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Purchased Date"
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid sx={{ mt: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="payment_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Payment Date"
                  value={!field.value ? null : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="request_for_pickup_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Towing Request Date"
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => field.onChange(formFormatDate(e))}
                  minDate={
                    hasEveryPermission(
                      VEHICLES.CAN_SELECT_PAST_TOWING_REQUEST_DATE,
                    )
                      ? null
                      : undefined
                  }
                  shouldDisableDate={(date) => {
                    if (
                      hasEveryPermission(
                        VEHICLES.CAN_SELECT_PAST_TOWING_REQUEST_DATE,
                      )
                    )
                      return false;
                    const isSameDay = dayjs(date).isSame(now, 'day');
                    const isPastDay = dayjs(date).isBefore(now, 'day');
                    return isAfterNoonInSavannah && (isSameDay || isPastDay);
                  }}
                  disabled={![10].includes(dept_id)}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            sx={{ my: 1.5, display: 'flex', gap: 1, alignItems: 'center' }}
            size={12}
          >
            <Controller
              control={form.control}
              name="ready_for_pickup_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Ready For Pickup Date"
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => field.onChange(formFormatDate(e))}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                  disabled={!ready_for_pickup_date_editable}
                />
              )}
            />
            <Controller
              control={form.control}
              name="ready_for_pickup_date_editable"
              render={({ field }) => (
                <FormControl component="fieldset" variant="standard">
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          inputRef={field.ref}
                          checked={field.value}
                          onChange={field.onChange}
                          name="ready_for_pickup_date_editable"
                        />
                      }
                      label="Manual"
                    />
                  </FormGroup>
                </FormControl>
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="tax_status"
              control={form.control}
              render={({ field, fieldState: { error, invalid } }) => {
                const selectedTaxStatus =
                  tax_status_options.find((row) => row.id == field.value) ??
                  null;
                return (
                  <Autocomplete
                    size="small"
                    value={selectedTaxStatus}
                    getOptionLabel={(option) =>
                      option?.label ? option?.label : ''
                    }
                    onChange={(_event, value) => {
                      field.onChange(value?.id ?? null);
                    }}
                    options={tax_status_options}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Tax Status"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                );
              }}
            />
          </Grid>

          <Grid size={12}>
            <Controller
              name="tax_amount"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.tax_amount?.message.length > 0 ? true : false
                  }
                  id="tax_amount"
                  value={field.value}
                  label="Tax Amount"
                  fullWidth
                  type="number"
                  variant="outlined"
                  onChange={(value) =>
                    field.onChange(
                      value.target.value !== '' ? +value.target.value : '',
                    )
                  }
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="destination_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url="autoComplete"
                  label="Point of destination"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'destinations'}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="tax_document_link"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.tax_document_link?.message.length > 0
                      ? true
                      : false
                  }
                  id="tax_document_link"
                  value={field.value ?? ''}
                  label="Tax Document Link"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="of_loading_photo"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.of_loading_photo?.message.length > 0
                      ? true
                      : false
                  }
                  id="of_loading_photo"
                  value={field.value ?? ''}
                  label="Ofloading Photos Link"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ mt: 1.5 }} size={12}>
            <Controller
              name="of_loading_video"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  type="url"
                  error={
                    form.errors.of_loading_video?.message.length > 0
                      ? true
                      : false
                  }
                  id="of_loading_video"
                  value={field.value ?? ''}
                  label="Ofloading Video Link"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
        </Grid>
        {/* end of one */}
        <Grid
          sx={{ px: 0.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="auction_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.auction_name?.message.length > 0 ? true : false
                  }
                  id="auction_name"
                  autoComplete="on"
                  value={field.value ?? ''}
                  label="Auction"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid size={12}>
            <Controller
              name="loading_city_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <AutoComplete
                    sx={error ? { mt: 1.5 } : { my: 1.5 }}
                    url="autoComplete"
                    label="Towed from city"
                    fieldName="city_name"
                    field={field}
                    error={error}
                    customeName="city_name"
                    staticOptions={false}
                    column={'city_name'}
                    modal={'loading_cities'}
                  />
                );
              }}
            />
          </Grid>
          <Grid container spacing={1}>
            <Grid size={10}>
              {isManualAuctionCity ? (
                <Controller
                  name="auction_city"
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      size="small"
                      error={!!error}
                      id="auction_city"
                      autoComplete="on"
                      value={field.value ?? ''}
                      label="Set Auction City"
                      fullWidth
                      variant="outlined"
                      onChange={(value) => field.onChange(value.target.value)}
                      inputRef={field.ref}
                      helperText={error?.message}
                    />
                  )}
                />
              ) : (
                <Controller
                  name="auction_city_id"
                  control={form.control}
                  render={({ field, fieldState: { error } }) => (
                    <AutoComplete
                      url="autoComplete"
                      label="Select Auction City"
                      fieldName="city_name"
                      field={field}
                      error={error}
                      customeName="city_name"
                      staticOptions={false}
                      column="city_name"
                      modal="loading_cities"
                    />
                  )}
                />
              )}
            </Grid>

            <Grid size={2}>
              <FormControl component="fieldset" variant="standard">
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isManualAuctionCity}
                        onChange={() => {
                          setIsManualAuctionCity(!isManualAuctionCity);
                          form.setValue('auction_city', null);
                          form.setValue('auction_city_id', null);
                        }}
                        name="toggleAuctionCity"
                      />
                    }
                    label="Manual"
                  />
                </FormGroup>
              </FormControl>
            </Grid>
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="is_title_exist"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  url={false}
                  label="Is Title Exist"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={is_title_exist}
                  column={''}
                  modal={''}
                />
              )}
            />
          </Grid>

          <Grid
            sx={{
              my: 1.5,
              display: 'flex',
              gap: 1,
              alignItems: 'center',
            }}
            size={12}
          >
            {/* <Grid container spacing={2}> */}
            {/* <Grid item xs={12} md={6} sx={{ my: 1.5 }}> */}
            <Controller
              name="halfcut_status"
              control={form.control}
              render={({ field, fieldState: { error, invalid } }) => {
                let selectedStatus = vehicles_halfcut_status2.find(
                  (row) => row.id == field.value,
                );
                if (selectedStatus === undefined) selectedStatus = null;

                // Define car states that require halfcut validation
                const restrictedCarStates = [
                  'on_hand_with_title',
                  'on_hand_no_title',
                  'shipped',
                ];
                const isRestrictedCarState =
                  isUpdate && restrictedCarStates.includes(selectedCarState);

                // Filter options for restricted car states (exclude 'unknown')
                const availableOptions = isRestrictedCarState
                  ? vehicles_halfcut_status2.filter(
                      (option) => option.id !== 'unknown',
                    )
                  : vehicles_halfcut_status2;

                return (
                  <Autocomplete
                    sx={{ width: '100%' }}
                    size="small"
                    value={selectedStatus}
                    getOptionLabel={(option) =>
                      option?.label ? option?.label : ''
                    }
                    onChange={(_event, value) => {
                      field.onChange(value?.id ?? null);
                      // onHalfcutStatusChange(value?.id);
                    }}
                    options={availableOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Halfcut Status"
                        error={invalid}
                        helperText={error?.message}
                        required={isRestrictedCarState}
                      />
                    )}
                  />
                );
              }}
            />
            <Controller
              control={form.control}
              name="is_scrap"
              render={({ field }) => (
                <FormControl component="fieldset" variant="standard">
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          inputRef={field.ref}
                          checked={field.value}
                          onChange={field.onChange}
                          name="is_scrap"
                          disabled={
                            halfcut_status !== 'completed' || !isScrapAvailable
                          }
                        />
                      }
                      label="Scrap"
                    />
                  </FormGroup>
                </FormControl>
              )}
            />
            {/* </Grid> */}
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="load_type"
              control={form.control}
              render={({ field, fieldState: { error, invalid } }) => {
                const selectedLoadType =
                  load_type_options.find((row) => row.id == field.value) ??
                  null;
                return (
                  <Autocomplete
                    size="small"
                    value={selectedLoadType}
                    getOptionLabel={(option) =>
                      option?.label ? option?.label : ''
                    }
                    onChange={(_event, value) => {
                      field.onChange(value?.id ?? null);
                    }}
                    options={load_type_options}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Load Type"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                );
              }}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="title_number"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.title_number?.message.length > 0 ? true : false
                  }
                  id="title_number"
                  value={field.value ?? ''}
                  label="Title Number"
                  autoComplete="on"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="title_state"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.title_state?.message.length > 0 ? true : false
                  }
                  autoComplete="on"
                  id="title_state"
                  value={field.value ?? ''}
                  label="Title State"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid size={12}>
            <Controller
              name="point_of_loading"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                if (error) setLoadErr(true);
                return (
                  <AutoComplete
                    sx={error ? { mt: 1.5 } : { mt: 1.5 }}
                    url={false}
                    fieldName="name"
                    label="Point of Loading"
                    field={field}
                    error={error}
                    staticOptions={locations}
                    column={''}
                    modal={''}
                  />
                );
              }}
            />
          </Grid>

          <Grid size={12}>
            <Controller
              key={renderKey + 1}
              name="yard_location_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <AutoComplete
                  sx={loadErr ? { mt: 0 } : { mt: 1.5 }}
                  url={false}
                  label="Select Yard Location"
                  fieldName=""
                  field={field}
                  error={error}
                  staticOptions={yardLocations}
                  column={''}
                  modal={''}
                  // defualtValue={yardLocations[0]}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="payment_date_to_pgl"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  disabled={
                    !hasPayments &&
                    !hasEveryPermission(VEHICLES.CAN_SET_PAYMENT_DATE_TO_PGL)
                  }
                  label={`Payment Date To PGL`}
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => field.onChange(formFormatDate(e))}
                  inputRef={field.ref}
                  readOnly={dept_id != 5}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="driver_appointment_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Driver appoinment date"
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => field.onChange(formFormatDate(e))}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="pickup_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  views={['year', 'month', 'day']}
                  label="Pickup Date"
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              control={form.control}
              name="deliver_date"
              render={({ field, fieldState: { error } }) => (
                <DatePicker
                  readOnly={
                    selectedCarState == 'on_the_way' ||
                    profile?.data?.loginable?.email == '<EMAIL>'
                      ? false
                      : true
                  }
                  views={['year', 'month', 'day']}
                  label="Deliver Date"
                  value={!field.value ? undefined : dayjs(field.value).toDate()}
                  format="yyyy/MM/dd"
                  onChange={(e) => {
                    field.onChange(formFormatDate(e));
                  }}
                  inputRef={field.ref}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      error: !!error,
                      helperText: error?.message,
                      size: 'small',
                      fullWidth: true,
                    },
                  }}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="vehicle_document_link"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.vehicle_document_link?.message.length > 0
                      ? true
                      : false
                  }
                  id="vehicle_document_link"
                  value={field.value ?? ''}
                  label="Vehicle Document Link"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="gate_pass_in"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.gate_pass_in?.message.length > 0 ? true : false
                  }
                  id="gate_pass_in"
                  value={field.value ?? ''}
                  label="Gate Pass In"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          <Grid sx={{ mt: 1.5 }} size={12}>
            <Controller
              name="buyer_number"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.buyer_number?.message.length > 0 ? true : false
                  }
                  id="buyer_number"
                  value={field.value ?? ''}
                  label="Buyer Number"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="receiver_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  size="small"
                  error={
                    form.errors.receiver_name?.message.length > 0 ? true : false
                  }
                  id="receiver_name"
                  value={field.value ?? ''}
                  label="Receiver Name"
                  fullWidth
                  variant="outlined"
                  onChange={(value) => field.onChange(value.target.value)}
                  ref={field.ref}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="fuel_type"
              control={form.control}
              render={({ field, fieldState: { error, invalid } }) => {
                const selectedFuelType =
                  fuel_type_enum.find(
                    (row) => row?.toLowerCase() == field.value?.toLowerCase(),
                  ) ?? '';
                return (
                  <Autocomplete
                    size="small"
                    value={selectedFuelType}
                    getOptionLabel={(option) => (option ? option : '')}
                    onChange={(_event, value) => {
                      field.onChange(value?.toLowerCase() ?? null);
                    }}
                    options={fuel_type_enum}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Fuel Type"
                        error={invalid}
                        helperText={error?.message}
                        className="capitalize"
                      />
                    )}
                  />
                );
              }}
            />
          </Grid>
          <Grid sx={{ my: 1.5 }} size={12}>
            <Controller
              name="vehicle_size"
              control={form.control}
              render={({ field, fieldState: { error, invalid } }) => {
                const selectedVehicle_size =
                  vehicle_size_enum.find(
                    (row) => row?.toLowerCase() == field.value?.toLowerCase(),
                  ) ?? '';
                return (
                  <Autocomplete
                    size="small"
                    value={selectedVehicle_size}
                    getOptionLabel={(option) =>
                      option ? option?.replace(/_/g, ' ') : ''
                    }
                    onChange={(_event, value) => {
                      field.onChange(value?.toLowerCase() ?? null);
                    }}
                    options={vehicle_size_enum}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Vehicle size"
                        error={invalid}
                        helperText={error?.message}
                        className="capitalize"
                      />
                    )}
                  />
                );
              }}
            />
          </Grid>
        </Grid>
        <Grid sx={{ px: 0.5 }} size={6}>
          <Controller
            name="note"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={form.errors.note?.message.length > 0 ? true : false}
                id="note"
                value={field.value ?? ''}
                label="Note"
                fullWidth
                variant="outlined"
                multiline
                rows={5}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid sx={{ px: 0.5 }} size={6}>
          <Controller
            name="loading_company_note"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.loading_company_note?.message.length > 0
                    ? true
                    : false
                }
                id="loading_company_note"
                value={field.value ?? ''}
                label="Loading Company Note"
                fullWidth
                variant="outlined"
                multiline
                rows={5}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid sx={{ px: 0.5, mb: 8 }} size={12}>
          <Controller
            name="pickup_remark"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={
                  form.errors.pickup_remark?.message.length > 0 ? true : false
                }
                id="pickup_remark"
                value={field.value ?? ''}
                label="Pickup Remark"
                fullWidth
                variant="outlined"
                multiline
                rows={5}
                onChange={(value) => field.onChange(value.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
