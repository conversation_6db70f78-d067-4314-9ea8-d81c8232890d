import { useContext, useEffect, useState } from 'react';
import { Box, Container, Tab, Tabs } from '@mui/material';
import { HeaderInfo, vehiclesTabs } from '@/configs/vehicles/vehiclesHeader';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import Head from 'next/head';
import { contextProvider } from '@/contexts/ProfileContext';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import axios from '@/lib/axios';
import ViewSingleVehicle from './vehicleComponents/ViewSingleVehicle';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { STORAGE_REPORTS, VEHICLES } from '@/configs/leftSideMenu/Permissions';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { CreateVehicles } from './CreateVehicles';
import AddToContainer from '@/Modules/vehicles/vehicleComponents/AddToContainer';
import {
  debounce,
  notPaginated,
  vehicleSearchFields,
} from '../../configs/vehicles/configs';
import {
  filterContentVehicle,
  filterContentVehicleCostAnalysis,
  filterContentVehicleStatus,
} from '@/configs/vehicles/vehicleFilterContent';
import { useRouter } from 'next/router';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import DropZoneModal from '@/components/mainComponents/cModal/DropZoneModal';
import { removeUnderScore2 } from '@/configs/common';
import { recordManager } from '@/configs/configs';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import AlertGatePass from './vehicleComponents/AlertGatePass';
import DownloadDoc from './vehicleComponents/DownloadLinkComponent ';
import PdfModalVehicle from './vehicleComponents/PdfModalVehicle';
import { useCustomColumnVehicle } from './vehicleComponents/CustomColumnVehicle';
import { AuctionButtonVehicles } from './vehicleComponents/ActionButtons';
import AddToPglU from './vehicleComponents/AddToPglU';
import UpdateTitleDate from './vehicleComponents/UpdateTitleDate';
import SimplificationTrack from './vehicleComponents/SimplificationTrack';
import AddAndCreateLoad from './vehicleComponents/AddAndCreateLoad';
import VehicleChangeStatus from './vehicleComponents/VehicleChangeStatus';
import SearchBy from '@/components/mainComponents/cComponents/SearchBy';
import AddVehicleTransaction from './vehicleComponents/AddVehicleTransaction';
import MultiEditVehicleDrawer from './MultiEditVehicleDrawer';
import AddToUnitedTrading from './vehicleComponents/AddToUnitedTrading';
import AddTitleToArrived from './vehicleComponents/AddTitleToArrived';
import AddTitleTracking from './vehicleComponents/AddTitleTracking';
import AddStorageForm from './AddStorageForm';
import ViewSinglePayment from './vehicleComponents/ViewSinglePayment';
import AddToDamage from '../vehicle_damages/Add/AddToDamage';
import React from 'react';
import { useUploadQueue } from '@/contexts/UploadQueueContext';
import UploadingCard from '@/components/mainComponents/cModal/UploadingCard';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import TransferLetterDownload from './vehicleComponents/TransferLetterDownload';

function NewVehicles({ activeTab, apiUrl, defaultHeaders }) {
  /////////////////////////////////////////// states below 👇////////////////////////////////////////////////
  const [open, setOpen] = useState(false);
  const [item, setItem] = useState(null);
  const [timeoutId, setTimeOutId] = useState(null);
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [view, setView] = useState(false);
  const [viewData, setViewData] = useState();
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState('Vehicles');
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [tableRecords, setTableRecords] = useState([]);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [multiUpdate, setMultiUpdate] = useState(false);
  const [cancelToken, setCancelToken] = useState(null);
  const [addToContainer, setAddToContainer] = useState(false);
  const [addToLoad, setAddToLoad] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [openFilter, setOpenFilter] = useState(false);
  const [showDownload, setShowDownload] = useState(false);
  const [showTransferLetterDownload, setShowTransferLetterDownload] =
    useState(false);
  const [addToPglu, setAddToPglu] = useState(false);
  const [addToDone, setAddToDone] = useState(false);
  const [addToArrived, setAddToArrived] = useState(false);
  const [addToUnitedTrading, setAddToUnitedTrading] = useState(false);
  const [addToDamage, setAddToDamage] = useState(false);
  const [pgluType, setPgluType] = useState('');
  const [updateDate, setUpdateDate] = useState(false);
  const [simplification, setSimplification] = useState(false);
  const [dateType, setDateType] = useState('');
  const [showGatePassAlter, setShowGatePassAlter] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [showGatePassId, setShowGatePassId] = useState(false);
  const [getPassData, setGetPassData] = useState(null);
  const [openTransaction, setOpenTransaction] = useState(false);
  const [showAddStorage, setShowAddStorage] = useState(false);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState(false);
  const [showUploadImageButton, setShowUploadImage] = useState(false);
  const [singleVehicle, setSingleVehicle] = useState(null);
  const {
    showUploadingCard,
    vehicleIds,
    fetchingRecords,
    setVehicleIds,
    setFetchingRecords,
  } = useUploadQueue();
  const [deleteReason, setDeleteReason] = useState('');

  const router = useRouter();
  const pageName = 'vehicles_' + activeTab;
  let tempFilterData = {};
  if (router.query.filterData) {
    //@ts-ignore
    tempFilterData = JSON.parse(router.query.filterData);
  }

  if (router.query.yard_location_id) {
    tempFilterData['yard_location_id'] = [+router.query.yard_location_id];
  }

  const tableName = 'vehicles';
  const [options, setOptions] = useState(() => {
    const storedPerPage = localStorage.getItem('vehicles_perPage');
    const defaultPerPage = 20;
    return {
      page: 1,
      perPage: storedPerPage ? parseInt(storedPerPage) : defaultPerPage,
      filterData: {
        ...tempFilterData,
      },
      tab: activeTab,
      search: '',
      exactMatch: false,
      orderBy: {
        column: 'id',
        order: 'desc',
      },
    };
  });
  const [singleFetchLoading, setSingleFetchLoading] = useState(false);

  ///////////////////////////////////////// variables below 👇 ////////////////////////////////////////////////////
  const viewShowButton = ['all', 'auction_paid', 'auction_unpaid'];

  const viewPermissions = {
    auction_paid: VEHICLES?.AUCTION_VIEW,
    auction_unpaid: VEHICLES?.AUCTION_VIEW,
    pending_auction: VEHICLES?.AUCTION_VIEW,
    on_the_way: VEHICLES?.ON_THE_WAY_VIEW,
    pending_on_the_way: VEHICLES?.VIEW,
    on_hand_no_title: VEHICLES?.ON_HAND_NO_TITLE_VIEW,
    on_hand_with_title: VEHICLES?.ON_HAND_WITH_TITLE_VIEW,
    on_hand_with_load: VEHICLES?.ON_HAND_WITH_LOAD_VIEW,
    shipped: VEHICLES?.SHIPPED_VIEW,
    pending: VEHICLES?.PENDING_VIEW,
    in_process: VEHICLES?.TITLE_TRACKING_VIEW,
    send: VEHICLES?.TITLE_TRACKING_VIEW,
    arrived: VEHICLES?.TITLE_TRACKING_VIEW,
    cost_analysis: VEHICLES?.COST_ANALYSIS_VIEW,
    datelines: VEHICLES?.DATE_LINES_VIEW,
  };

  let filteredTab = vehiclesTabs.filter((item) => {
    const requiredPermission = viewPermissions[item.value];
    return requiredPermission ? perms?.includes(requiredPermission) : true;
  });

  ////////////////////////////////////// useEffects below 👇 //////////////////////////////////////////////////////

  useEffect(() => {
    localStorage.setItem('vehicles_perPage', options.perPage.toString());
  }, [options.perPage]);

  useEffect(() => {
    if (activeTab != options.tab && activeTab == router.query.id) {
      setTotalItems(0);
      setOptions({
        ...options,
        page: 1,
        perPage: 20,
        filterData: {},
        tab: activeTab,
        search: '',
      });
    }
  }, [router]);

  useEffect(() => {
    if (activeTab == options.tab) {
      fetchRecords();
    }
  }, [
    options.page,
    options.perPage,
    options.search,
    options.filterData,
    options.tab,
    activeTab,
    router,
    JSON.stringify(router.query),
  ]);

  useEffect(() => {
    setOptions((prev) => ({
      ...prev,
      filterData: {
        ...prev.filterData,
        ...(router.query.filterData
          ? JSON.parse(router.query.filterData as string)
          : {}),
      },
    }));
  }, [JSON.stringify(router.query)]);

  useEffect(() => {
    if (profile)
      applySavedColumns({
        profile,
        pageName,
        defaultHeaders,
        setSelectedHeaders,
        setSelectedSetting,
      });
  }, [profile, activeTab]);

  useEffect(() => {
    if (fetchingRecords) {
      fetchSpecificRecords();
    }
  }, [fetchingRecords]);
  ///////////////////////////////// functions below 👇 //////////////////////////////////////////////////////////

  const getSingleRow = async (id) => {
    try {
      setView(true);
      const res = await fetchOne({ id: id });
      if (res?.status == 200) {
        setViewData(res?.data);
      }
    } catch (error) {
      console.log(error);
      setView(false);
    }
  };

  const fetchSpecificRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      let { data } = await axios.get(`${apiUrl}/get_specific_vehicles`, {
        signal: controller.signal,
        params: {
          id: vehicleIds,
        },
      });
      setTableRecords(
        tableRecords.map((vehicle) => {
          const newVehicleData = data?.data.find(
            (item) => item.id === vehicle.id,
          );
          return newVehicleData ? { ...vehicle, ...newVehicleData } : vehicle;
        }),
      );
      setVehicleIds([]);
      setFetchingRecords(false);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);

      let { data } = await axios.get(apiUrl, {
        signal: controller.signal,
        params: {
          state: activeTab !== 'all' ? activeTab : '',
          page: options.page,
          per_page: notPaginated?.includes(options.tab)
            ? 20000000
            : options.perPage,
          search: options.search,
          searchOptions:
            options.search && localStorage.getItem('searchBy-vehicle'),
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
        },
      });

      setTotalItems(data.total);
      setTableRecords(data.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const fetchOne = async ({ id }) => {
    setSingleFetchLoading(true);
    try {
      const res = await axios.get(`vehicles/${id}`);
      if (res?.status == 200) {
        setSingleFetchLoading(false);
      }
      return res;
    } catch (error) {
      setSingleFetchLoading(false);
    }
  };

  const selectFilterContent = (value) => {
    switch (value) {
      case 'cost_analysis':
        return filterContentVehicleCostAnalysis;
      case 'all':
        return filterContentVehicle();
      default:
        return filterContentVehicleStatus();
    }
  };

  const onTabChange = (val) => {
    setSelectedItems([]);
    const tabName = vehiclesTabs.find((item) => item.value == val);

    if (tabName?.value) {
      tabName?.value == 'all'
        ? setTitle('Vehicles')
        : setTitle('Vehicles  ' + tabName?.name);
    } else {
      setTitle('vehicles');
    }
    router.push(`/vehicles/${val}`);
  };

  const onDelete = () => {
    recordManager({
      data: null,
      type: 'delete',
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl: 'vehicles',
      deleteReason,
      setDeleteReason,
    });
  };

  const onAdd = () => {
    setIsUpdate(false);
    setShowCreate(true);
  };

  const onEdit = async () => {
    try {
      if (selectedItems.length > 1) {
        setMultiUpdate(true);
        return;
      }
      setIsUpdate(true);
      setShowCreate(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setSelectedItems([res?.data]);
      }
    } catch (error) {
      setIsUpdate(false);
      setShowCreate(false);
      console.log(error);
    }
  };

  const toggleUploadImages = async () => {
    setShowUploadImage(showUploadImageButton != showUploadImageButton);
  };
  const onAddStorage = async () => {
    try {
      setShowAddStorage(true);
      const res = await fetchOne({ id: selectedItems[0]?.id });
      if (res?.status == 200) {
        setSelectedItems([res?.data]);
      }
    } catch (error) {}
  };

  const customButtons = () =>
    AuctionButtonVehicles({
      tab: options.tab,
      checkStatusPerm: perms?.includes(VEHICLES?.CHANGE_STATUS),
      setAddToContainer: setAddToContainer,
      setOpenConfirm: setOpenConfirm,
      setAddToPglu: setAddToPglu,
      setAddToDone: setAddToDone,
      setAddToArrived: setAddToArrived,
      setPgluType: setPgluType,
      selectedItems: selectedItems,
      setAddToLoad: setAddToLoad,
      setOpenTransaction: setOpenTransaction,

      checkTransactionPerm: perms?.includes(VEHICLES?.CREATE_TRANSACTION),
      setAddToUnitedTrading: setAddToUnitedTrading,
      setAddToDamage: setAddToDamage,
      checkAddToUnitedTradingPerm: perms?.includes(
        VEHICLES?.ADD_TO_UNITED_TRADING,
      ),
      onAddStorage,
      showStorage: perms?.includes(STORAGE_REPORTS?.ADD),
      setShowTransferLetterDownload: setShowTransferLetterDownload,
    });

  const onRecordManager = (data, type) => {
    if (activeTab == 'auction_paid' || data.carstate == 'auction_paid')
      return () => {};
    else {
      recordManager({
        data,
        type,
        setTableRecords,
        tableRecords,
        selectedItems,
        setSelectedItems,
        setTotalItems,
        totalItems,
        apiUrl,
      });
    }
  };
  //
  const showExportButton = (currentTab) => {
    const tabPermissions = {
      all: VEHICLES?.DATA_EXPORT,
      on_the_way: VEHICLES?.ON_THE_WAY_EXPORT,
      on_hand_no_title: VEHICLES?.ON_HAND_NO_TITLE_EXPORT,
      on_hand_with_title: VEHICLES?.ON_HAND_WITH_TITLE_EXPORT,
      on_hand_with_load: VEHICLES?.ON_HAND_WITH_LOAD_EXPORT,
      shipped: VEHICLES?.SHIPPED_EXPORT,
      pending: VEHICLES?.PENDING_EXPORT,
      in_process: VEHICLES?.TITLE_TRACKING_VIEW,
      send: VEHICLES?.TITLE_TRACKING_VIEW,
      arrived: VEHICLES?.TITLE_TRACKING_VIEW,
      auction_paid: VEHICLES?.AUCTION_EXPORT,
      auction_unpaid: VEHICLES?.AUCTION_EXPORT,
      pending_auction: VEHICLES?.AUCTION_EXPORT,
      pending_on_the_way: VEHICLES?.ON_THE_WAY_EXPORT,
    };

    return perms?.includes(tabPermissions[currentTab]) || false;
  };

  const handleDownloadClick = (id) => {
    setShowGatePassAlter(true);
    setShowGatePassId(id);
  };

  const handleGatePass = async (id) => {
    try {
      let res = await axios.patch(`vehicles/gatePass/${id}`);
      if (res?.data?.result) {
        setGetPassData(res?.data?.data);
        debounce(() => setGetPassData(null), 1000, timeoutId, setTimeOutId);
      }
      if (!res?.data?.result) {
        setGetPassData(null);
        handleDownloadClick(id);
        setAlertTitle(res?.data?.message);
      }
    } catch (error) {}
  };

  const handleDeleteReason = (value) => {
    setDeleteReason(value);
  };

  ////////////////////////// Page Actions /////////////////////
  const pageAction = (
    <PageAction
      reasonOnDelete
      onRefreshPage={() => router.push('/vehicles/all')}
      showRefresh={
        router.query.company_id ||
        router.query.carStates ||
        router.query.point_of_loading
          ? true
          : false
      }
      isFilterExist={Object.keys(options.filterData).length > 0 ? false : true}
      onEnter={() => {
        if (!loading) {
          fetchRecords();
        }
      }}
      onFilterClick={() => setOpenFilter(true)}
      customActionButtons={customButtons}
      selectedItems={selectedItems}
      title={title}
      options={options}
      setOptions={setOptions}
      total={totalItems}
      showCustomizeColumn={true}
      onCustomizeColumn={() => setShowColumnDialog(true)}
      showAddButton={
        viewShowButton?.includes(options.tab) &&
        (perms?.includes(VEHICLES?.CREATE) ||
          perms?.includes(VEHICLES?.AUCTION_PAID) ||
          perms?.includes(VEHICLES?.AUCTION_UNPAID))
          ? true
          : false
      }
      onAdd={onAdd}
      onEdit={onEdit}
      showEditButton={perms?.includes(VEHICLES?.UPDATE)}
      hasMultiEdit={true}
      onDelete={onDelete}
      deleteReason={deleteReason}
      onDeleteReason={handleDeleteReason}
      showDeleteButton={perms?.includes(VEHICLES?.DELETE)}
      dialogTitle={'Delete Vehicles'}
      deleteTitle={`Are you sure you want to delete ${selectedItems.length} ${
        selectedItems.length == 1 ? 'item' : 'items'
      } ?`}
      showDownload={showExportButton(options.tab)}
      onDownload={() => setShowDownload(true)}
    />
  );

  ///////////////////////////////// jsx return below 👇 //////////////////////////////////////////////////////////
  return perms && !perms?.includes(VEHICLES?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title style={{ textTransform: 'capitalize' }}>
          Veh | {activeTab.replaceAll('_', ' ')}
        </title>
      </Head>
      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={HeaderInfo().concat({
            href: 'false',
            name: removeUnderScore2(activeTab),
            icon: <></>,
            key: '4',
          })}
        />
        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={
            options.tab == 'on_the_way_auction' ? 'on_the_way' : options.tab
          }
          onChange={(_event, val) => onTabChange(val)}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          {filteredTab.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.value}
            />
          ))}
        </Tabs>

        <DataTable3
          PageAction={pageAction}
          sortLoading={loading}
          hidePagination={notPaginated?.includes(options.tab) ? true : false}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          loading={loading}
          items={tableRecords}
          setItems={setTableRecords}
          headers={selectedHeaders}
          selectedItems={selectedItems}
          setSelectedItems={setSelectedItems}
          activeTab={activeTab}
          tableName={tableName}
          allOptions={['on_the_way', 'on_hand_no_title']}
          // custom props
          // eslint-disable-next-line react-hooks/rules-of-hooks
          {...useCustomColumnVehicle({
            activeTab: activeTab,
            getSingleRow: getSingleRow,
            setDateType: setDateType,
            setSelectedItems: setSelectedItems,
            setSimplification: setSimplification,
            setShowGatePassAlter: setShowGatePassAlter,
            setUpdateDate: setUpdateDate,
            tableRecords: tableRecords,
            setAddToPglu: setAddToPglu,
            setPgluType: setPgluType,
            setAddToUnitedTrading: setAddToUnitedTrading,
            setShowPaymentDetails,
            setPaymentDetails,
            setSingleVehicle: setSingleVehicle,
            onImageUpload: setShowUploadImage,
            setOpen: setOpen,
            setItem: setItem,
            onHandleGatePass: handleGatePass,
            setAddToDamage: setAddToDamage,
          })}
        />
      </Container>
      <AppConfirmDialog
        open={open}
        onDeny={() => setOpen(false)}
        onConfirm={() => {
          if (item) {
            handleGatePass(item.id);
          }
          setOpen(false);
        }}
        title="Are you Sure you want to Gate Pass the Vehicle?"
        dialogTitle={'Gate Pass'}
        confirmText={'Gate Pass'}
        cancelText="Cancel"
        maxWidth={'sm'}
        submitting={false}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
        SearchBy={SearchBy(vehicleSearchFields, 'vehicle')}
        showSearchBy={true}
      ></ColumnDialog>
      {view && (
        <ViewSingleVehicle
          loading={singleFetchLoading}
          data={viewData}
          setView={setView}
          show={view}
        />
      )}
      <ViewSinglePayment
        loading={singleFetchLoading}
        data={paymentDetails}
        setView={setShowPaymentDetails}
        show={showPaymentDetails}
      />
      <CreateVehicles
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems[0]}
        setSelectedItems={setSelectedItems}
        isUpdate={isUpdate}
        recordManager={onRecordManager}
        loading={singleFetchLoading}
      />
      <MultiEditVehicleDrawer
        recordManager={onRecordManager}
        show={multiUpdate}
        setShow={setMultiUpdate}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
      />
      <VehicleChangeStatus
        apiUrl={apiUrl}
        fetchRecords={fetchRecords}
        openConfirm={openConfirm}
        selectedItems={selectedItems}
        setOpenConfirm={setOpenConfirm}
        setSelectedItems={setSelectedItems}
        tab={options.tab}
      />
      <AddToContainer
        selectedItems={selectedItems}
        open={addToContainer}
        onDeny={setAddToContainer}
        fetchRecords={fetchRecords}
        setSelectedItems={setSelectedItems}
      />
      <AddToPglU
        open={addToPglu}
        onDeny={setAddToPglu}
        pgluType={pgluType}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setAddToPglu={setAddToPglu}
        setPgluType={setPgluType}
      />
      <AddTitleTracking
        open={addToDone}
        onDeny={setAddToDone}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setAddToDone={setAddToDone}
      />
      <AddTitleToArrived
        open={addToArrived}
        onDeny={setAddToArrived}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setAddToArrived={setAddToArrived}
        fetchRecors={fetchRecords}
      />
      <AddToUnitedTrading
        open={addToUnitedTrading}
        fetchRecords={fetchRecords}
        onDeny={() => {
          setSelectedItems([]);
          setAddToUnitedTrading(false);
        }}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setAddToUnitedTrading={setAddToUnitedTrading}
      />
      <AddToDamage
        open={addToDamage}
        fetchRecords={fetchRecords}
        onDeny={() => {
          setSelectedItems([]);
          setAddToDamage(false);
        }}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        setAddToDamage={setAddToDamage}
      />
      <UpdateTitleDate
        open={updateDate}
        onDeny={setUpdateDate}
        selectedItems={selectedItems}
        dateType={dateType}
        setTableRecords={setTableRecords}
        tableRecords={tableRecords}
        setSelectedItems={setSelectedItems}
        setUpdateDate={setUpdateDate}
      />
      {perms && perms?.includes(VEHICLES?.TITLE_TRACKING_ADD) && (
        <SimplificationTrack
          open={simplification}
          onDeny={setSimplification}
          singleVehicle={singleVehicle}
          setTableRecords={setTableRecords}
          setSingleVehicle={setSingleVehicle}
          setUpdateDate={setSimplification}
        />
      )}
      {addToLoad && (
        <AddAndCreateLoad
          onDeny={setAddToLoad}
          open={addToLoad}
          selectedItems={selectedItems}
          fetchTableRecord={fetchRecords}
        />
      )}
      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Vehicles"
        content={selectFilterContent(options.tab)}
      />
      {showUploadImageButton && selectedItems?.length < 2 && (
        <DropZoneModal
          open={showUploadImageButton}
          setShowUploadImage={toggleUploadImages}
          url={apiUrl}
          route={'vehicle-images'}
          singleVehicle={singleVehicle}
          perms={perms}
        />
      )}
      {showGatePassAlter && (
        <AlertGatePass
          alertTitle={alertTitle}
          itemId={showGatePassId}
          setShowGatePassAlter={setShowGatePassAlter}
          setShowGatePassId={setShowGatePassId}
        />
      )}
      <DownloadDoc data={getPassData} />
      <PdfModalVehicle
        showDownload={showDownload}
        title={title}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        options={options}
        apiUrl={apiUrl}
        totalItems={totalItems}
      />
      <TransferLetterDownload
        showTransferLetterDownload={showTransferLetterDownload}
        setShowTransferLetterDownload={setShowTransferLetterDownload}
        selectedItems={selectedItems}
      />
      <AddVehicleTransaction
        fetchRecords={fetchRecords}
        openConfirm={openTransaction}
        selectedItems={selectedItems}
        setOpenConfirm={setOpenTransaction}
        setSelectedItems={setSelectedItems}
      />
      <AddStorageForm
        show={showAddStorage}
        setShow={setShowAddStorage}
        selectedItem={selectedItems[0]}
        singleFetchLoading={singleFetchLoading}
      />
      {showUploadingCard && <UploadingCard />}
    </>
  );
}

export default NewVehicles;
