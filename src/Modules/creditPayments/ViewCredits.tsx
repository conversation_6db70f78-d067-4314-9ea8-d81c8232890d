import React from 'react';
import { Box, Grid, useTheme } from '@mui/material';

const ViewCredits = ({ credit, bgColor = false }) => {
  const theme = useTheme();

  return (
    <Grid
      container
      spacing={2}
      sx={{
        backgroundColor: bgColor
          ? theme.palette.mode == 'dark'
            ? '#3b3b3b'
            : 'lightgray'
          : 'transparent',
        m: 0,
        mb: 1,
        py: 1,
      }}
    >
      <Grid
        sx={{
          paddingTop: '8px !important',
          paddingBottom: '8px !important',
        }}
        size={{
          xs: 12,
          md: 4,
        }}
      >
        <Box sx={{ display: 'flex' }}>
          <Box sx={{ width: '45%', fontWeight: '500' }}>Vin Number:</Box>
          <Box>{credit?.vehicle?.vin}</Box>
        </Box>
      </Grid>
      <Grid
        sx={{
          paddingTop: '8px !important',
          paddingBottom: '8px !important',
          display: 'block',
        }}
        size={{
          xs: 12,
          md: 3,
        }}
      >
        <Box sx={{ display: 'flex' }}>
          <Box sx={{ width: '45%', fontWeight: '500' }}>Amount:</Box>
          <Box>${credit?.amount}</Box>
        </Box>
      </Grid>
      <Grid
        sx={{
          paddingTop: '8px !important',
          paddingBottom: '8px !important',
          display: 'block',
        }}
        size={{
          xs: 12,
          md: 5,
        }}
      >
        <Box sx={{ display: 'flex' }}>
          <Box sx={{ width: '25%', fontWeight: '500' }}>Remark:</Box>
          <Box sx={{ width: '70%' }}>{credit?.remark}</Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default ViewCredits;
