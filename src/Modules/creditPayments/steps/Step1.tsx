import React, { useContext, useEffect, useState, useMemo } from 'react';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CancelIcon from '@mui/icons-material/Cancel';
import FeedIcon from '@mui/icons-material/Feed';
import { styled, useTheme } from '@mui/material/styles';
import { Controller } from 'react-hook-form';
import Tooltip from '@mui/material/Tooltip';
import { toast } from 'react-toastify';

import {
  Grid,
  TextField,
  Typography,
  Box,
  Button,
  Paper,
  Chip,
  Autocomplete as MUIAutoComplete,
  Link,
  List,
  debounce,
} from '@mui/material';

import { getInvoiceDownloadUrl } from '@/Modules/customerPayments/CustomerPayments';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import {
  creditPaymentMethods,
  currencyOptions,
  paymentMethods,
  paymentStateOptions,
  creditPaymentTypes,
  creditCustomerPaymentMethods,
  dualCreditPaymentMethods,
} from '../../customerPayments/customerPaymentsComponent/customerPaymentHeader';
import { CUSTOMER_PAYMENTS } from '@/configs/leftSideMenu/Permissions';
import { getExchangeRate } from '../../customerPayments/customerPaymentsComponent/PaymentHelper';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { removeUnderScore2 } from '@/configs/common';
import { choseColor } from '@/configs/configs';
import removeDuplicateObjectsInArray from '@/utils/removeDuplicateObjectsInArray';
import { nanoid } from 'nanoid';

export default function Step1({
  form,
  customerPaymentSelected,
  attachments,
  setAttachments,
  deletedAttachments,
  setDeletedAttachments,
  isUpdate,
  isAddExitPaper,
  exitPaperCredits,
}) {
  const [customers, setCustomers] = useState([]);
  const [companyId, setCompanyId] = useState();
  const [compErr, setCompErr] = useState(false);
  const [renderKeyCus, setRenderKeyCus] = useState(-1);
  const [exchangeRate, setExchangeRate] = useState<number>(
    parseFloat(customerPaymentSelected?.exchange_rate) || 1,
  );
  const [transactionNumberDisabled, setTransactionNumberDisabled] =
    useState(false);
  const [containers, setContainers] = useState([]);
  const [selectedContainerIds, setSelectedContainerIds] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicleIds, setSelectedVehicleIds] = useState([]);
  const [creditFormsIds, setCreditFormsIds] = useState(
    customerPaymentSelected?.customer_credits?.map((credit) => ({
      id: credit.id,
    })) || [],
  );
  const [manualAdminUpdate, setManualAdminUpdate] = useState(false);

  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const userPermissions = profile?.data?.loginable?.permissions?.map(
    (op) => op.name,
  );
  const isAdmin = profile?.data?.department_id === 1;

  const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });

  const handleUploadedFile = (files: FileList) => {
    const myFiles = Array.from(files);
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];

    const notAllowedFile = myFiles.find(
      (file) => !allowedTypes.includes(file.type),
    );
    if (notAllowedFile) {
      return toast.error('Only PDF, PNG, JPG, and JPEG files are allowed!');
    }

    if (attachments?.length > 0) {
      setAttachments((prevAttachments) => [...prevAttachments, ...myFiles]);
      form.setValue('attachments', [...attachments, ...myFiles]);
    } else {
      setAttachments(myFiles);
      form.setValue('attachments', myFiles);
    }
  };

  const removeSingleFile = (name: string, url: string) => {
    if (disabled()) {
      return toast.error(
        'You can not remove attachments when Customer payment is Reviewed.',
      );
    }
    const newFiles = attachments.filter(
      (attachment) => attachment.name != name,
    );
    setAttachments(newFiles);
    setDeletedAttachments((prevState) => [...prevState, url]);
  };

  const hasCredits = () => {
    return creditFormsIds.length > 0;
  };

  const s_array = (data, fieldName) =>
    data &&
    Object?.values(data).map((value: any) => ({
      id: value?.id,
      label: value?.[fieldName] ? value?.[fieldName] : '',
    }));

  const getCustomers = async (companyId) => {
    try {
      const res = await axios.get(`customers/companyCustomer/${companyId}`);
      if (res.status === 200) {
        setCustomers(s_array(res.data?.data, 'fullname'));
        setRenderKeyCus((prv) => prv + -1);
      }
    } catch (error) {}
  };

  const disabled = () => {
    return (
      (customerPaymentSelected &&
        customerPaymentSelected.state === paymentStateOptions.approved) ||
      (customerPaymentSelected &&
        customerPaymentSelected.state === paymentStateOptions.final_reviewed) ||
      (customerPaymentSelected &&
        customerPaymentSelected.state === paymentStateOptions.reviewed)
    );
  };

  const getTransactionNumber = async (selectedKey) => {
    const res = await axios.get(
      `/customer-payment-transaction/get-transaction-number/${selectedKey}`,
    );
    form.setValue('transaction_number', res?.data.transaction_number, {
      shouldValidate: true,
    });
    setTransactionNumberDisabled(true);
  };

  const paymentMethod = form.watch('payment_method');
  const hasPaymentsApplied = customerPaymentSelected?.payments?.length > 0;
  const hasExitPapers = customerPaymentSelected?.exitPapers?.length > 0;
  const isDamage =
    paymentMethod === 'damage_credit' &&
    form.getValues('transaction_number').includes('PGLDM');
  // const isContainer =
  //   creditCustomerPaymentMethods.find((m) => m.id === paymentMethod)?.type ===
  //   'container';
  // const isVehicle =
  //   creditCustomerPaymentMethods.find((m) => m.id === paymentMethod)?.type ===
  //   'vehicle';
  const [isContainer, setIsContainer] = useState(
    creditCustomerPaymentMethods.find((m) => m.id === paymentMethod)?.type ===
      'container',
  );
  const [isVehicle, setIsVehicle] = useState(
    creditCustomerPaymentMethods.find((m) => m.id === paymentMethod)?.type ===
      'vehicle',
  );
  let paymentMethodOptions = Object.keys(creditPaymentTypes).map((key) => ({
    id: key,
    label: paymentMethods[key],
  }));

  // user should not be able to create new damage, mukhasa and exit paper credits from the customer credits section/module
  if (!isUpdate && !isAddExitPaper) {
    paymentMethodOptions = paymentMethodOptions.filter(
      (paymentMethod) =>
        paymentMethod?.id !== 'damage_credit' &&
        paymentMethod?.id !== 'mukhasa' &&
        paymentMethod?.id !== 'exit_paper_credit',
    );
  }

  const isAddButtonDisabled = useMemo(() => {
    const companyId = form.watch('company_id');
    if (
      !companyId ||
      !paymentMethod ||
      !userPermissions.includes(CUSTOMER_PAYMENTS.CREATE)
    ) {
      return true;
    }

    if (creditFormsIds.length === 0) {
      return false;
    }

    return creditFormsIds.some((formItem) => {
      const containerId = form.watch(`credit_form_${formItem.id}.container_id`);
      const vehicleId = form.watch(`credit_form_${formItem.id}.vehicle_id`);
      const amount = form.watch(`credit_form_${formItem.id}.amount`);
      if (isContainer) {
        return !containerId || !amount || amount <= 0;
      } else {
        return !vehicleId || !amount || amount <= 0;
      }
    });
  }, [form, userPermissions, creditFormsIds]);

  const fetchVinAndLotOfVehicles = useMemo(
    () =>
      debounce(async (companyId, search = '') => {
        try {
          const queryString = new URLSearchParams();
          if (search) queryString.set('search', search.toUpperCase());
          if (paymentMethod) queryString.set('payment_method', paymentMethod);

          const res = await axios.get(
            `/customer-payment-transaction/get-vin-lot/${companyId}?${queryString.toString()}`,
          );

          if (res.status === 200 && res.data.result) {
            const newVehicles = res.data.data.map((vehicle) => ({
              ...vehicle,
              vinLot: `Vin: ${vehicle?.vin}, Lot: ${vehicle?.lot_number}`,
            }));

            const existingVehicles = (
              form.getValues('customer_credits') ?? []
            ).reduce((acc, credit) => {
              if (
                !credit?.vehicle_id &&
                (!!credit?.container_id || credit?.container_id === undefined)
              )
                return acc;

              acc.push({
                ...credit.vehicle,
                vinLot:
                  (isAddExitPaper || hasExitPapers) &&
                  credit?.vehicle_id === null
                    ? `Custom Vin: ${credit?.vehicle?.vin}`
                    : credit?.vehicle?.lot_number === ''
                      ? `Vin: ${credit?.vehicle?.vin}`
                      : `Vin: ${credit?.vehicle?.vin}, Lot: ${credit?.vehicle?.lot_number}`,
              });

              return acc;
            }, []);

            setVehicles(
              removeDuplicateObjectsInArray([
                ...newVehicles,
                ...existingVehicles,
              ]),
            );
          } else {
            console.error('Error fetching vehicles:', res.data.error);
            setVehicles([]);
          }
        } catch (error) {
          console.error('Error fetching vehicles:', error);
          setVehicles([]);
        }
      }, 600),
    [paymentMethod],
  );

  const fetchContainerNumbers = useMemo(
    () =>
      debounce(async (companyId, search = '') => {
        try {
          const queryString = new URLSearchParams();
          if (search) queryString.set('search', search.toUpperCase());
          if (paymentMethod) queryString.set('payment_method', paymentMethod);

          const res = await axios.get(
            `/customer-payment-transaction/get-container-number/${companyId}?${queryString.toString()}`,
          );

          if (res.status === 200 && res.data.result) {
            const existingContainers = (
              form.getValues('customer_credits') ?? []
            ).reduce((acc, credit) => {
              if (!credit?.container_id) return acc;

              acc.push(credit?.container);

              return acc;
            }, []);

            setContainers(
              removeDuplicateObjectsInArray([
                ...res.data.data,
                ...existingContainers,
              ]),
            );
          }
        } catch {}
      }, 600),
    [paymentMethod],
  );

  // --- START: Calculate Total Credit
  const creditAmountFields = creditFormsIds.map(
    (formItem) => `credit_form_${formItem.id}.amount`,
  );
  const creditAmounts = form.watch(creditAmountFields);

  const totalCreditAmount = creditAmounts?.reduce(
    (sum, amount) => sum + (Number(amount) || 0),
    0,
  );

  useEffect(() => {
    if (isAddExitPaper) {
      if (
        customerPaymentSelected?.exchange_rate_for_service_charge?.currency ===
        'AED'
      ) {
        form.setValue(
          'amount',
          Number(
            (
              totalCreditAmount -
              form.getValues('service_charge') /
                customerPaymentSelected?.exchange_rate_for_service_charge?.rate
            ).toFixed(3),
          ),
        );
      } else {
        form.setValue(
          'amount',
          Number(
            (totalCreditAmount - form.getValues('service_charge')).toFixed(3),
          ),
        );
      }
    } else if (!hasExitPapers) {
      form.setValue('amount', totalCreditAmount);
    }
  }, [totalCreditAmount, manualAdminUpdate]);
  // --- END: Calculate Total Credit

  const id = form.watch('company_id');
  useEffect(() => {
    setCompanyId(id);
    if (id != undefined && companyId == id && id != '') {
      form.setValue('customer_id', null);
      getCustomers(id);

      if (isContainer) {
        fetchContainerNumbers(id);
      }

      if (isVehicle) {
        fetchVinAndLotOfVehicles(id);
      }
    }
  }, [id, companyId, isVehicle, isContainer, paymentMethod]);

  useEffect(() => {
    form.setValue(
      'customer_credits',
      creditFormsIds.map((creditForm) => ({
        ...(typeof creditForm.id === 'number' && { id: creditForm.id }),
        ...form.getValues(`credit_form_${creditForm.id}`),
      })),
    );
  }, [form]);

  useEffect(() => {
    if (deletedAttachments.length > 0) {
      form.setValue('deletedAttachments', deletedAttachments);
    }
  }, [attachments]);

  const prepareCustomerCredits = (customerPaymentSelected) => {
    if (customerPaymentSelected?.customer_credits?.length > 0) {
      const vehicleIds = [];
      const containerIds = [];

      customerPaymentSelected.customer_credits.forEach((credit) => {
        if (credit.vehicle_id) {
          vehicleIds.push(credit.vehicle_id);
        }
        if (credit.container_id) {
          containerIds.push(credit.container_id);
        }
      });

      setSelectedVehicleIds(vehicleIds);
      setSelectedContainerIds(containerIds);
      if (
        dualCreditPaymentMethods.includes(
          customerPaymentSelected?.payment_method,
        )
      ) {
        const hasContainer = customerPaymentSelected.customer_credits.some(
          (credit) => credit.container_id,
        );
        const hasVehicle = customerPaymentSelected.customer_credits.some(
          (credit) => credit.vehicle_id,
        );

        setIsContainer(hasContainer);
        setIsVehicle(hasVehicle);
      }

      // START: Handle Exit Paper Credits
      let exitPaperVehicles = [];
      if (hasExitPapers) {
        const creditsWithVehicleId = customerPaymentSelected.customer_credits
          .filter((v) => v.vehicle_id !== null)
          .sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime(),
          );

        const creditsWithoutVehicleId = customerPaymentSelected.customer_credits
          .filter((v) => v.vehicle_id === null)
          .sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime(),
          );

        customerPaymentSelected.customer_credits = [
          ...creditsWithVehicleId,
          ...creditsWithoutVehicleId,
        ];

        const exitPaperVehiclesWithVehicleId =
          customerPaymentSelected?.exitPapers
            .map((item) => item.exit_papers_vehicles)
            .flat()
            .filter((v) => v.vehicle_id !== null)
            .sort(
              (a, b) =>
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime(),
            );

        const exitPaperVehiclesWithoutVehicleId =
          customerPaymentSelected?.exitPapers
            .map((item) => item.exit_papers_vehicles)
            .flat()
            .filter((v) => v.vehicle_id === null)
            .sort(
              (a, b) =>
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime(),
            );

        exitPaperVehicles = [
          ...exitPaperVehiclesWithVehicleId,
          ...exitPaperVehiclesWithoutVehicleId,
        ].map((vehicle) => {
          const vehicleId = vehicle?.vehicle_id || nanoid();
          return {
            amount: +vehicle?.custom_duty + +vehicle?.vat,
            vehicle: {
              id: vehicleId,
              vin: !!vehicle?.custom_vin
                ? vehicle?.custom_vin
                : vehicle?.vehicles?.vin,
              lot_number: '',
            },
            vehicle_id: vehicleId,
            created_at: vehicle?.created_at,
          };
        });
      }
      // END: Handle Exit Paper Credits

      // Initialize credit forms
      const initialCustomerCreditForms =
        customerPaymentSelected.customer_credits.map((credit, index) => ({
          id: credit.id,
          vehicle_id:
            hasExitPapers &&
            credit?.vehicle_id === null &&
            exitPaperVehicles[index]?.amount === credit.amount
              ? exitPaperVehicles[index]?.vehicle_id
              : credit.vehicle_id,
          container_id: credit.container_id,
          amount: credit.amount ? +credit.amount : '',
          remark: credit.remark || '',
          vehicle:
            hasExitPapers &&
            credit?.vehicle_id === null &&
            exitPaperVehicles[index]?.amount === credit.amount
              ? exitPaperVehicles[index]?.vehicle
              : credit.vehicle,
          container: credit.container || {},
        }));

      setCreditFormsIds(
        initialCustomerCreditForms.map((form) => ({
          id: form.id,
        })),
      );

      // Set form values for each credit
      initialCustomerCreditForms.forEach((credit, index) => {
        const prefix = `credit_form_${credit.id}`;
        form.setValue(
          `${prefix}.vehicle_id`,
          hasExitPapers &&
            credit?.vehicle_id === null &&
            exitPaperVehicles[index]?.amount === credit.amount
            ? exitPaperVehicles[index]?.vehicle_id
            : credit.vehicle_id,
        );
        form.setValue(`${prefix}.container_id`, credit.container_id);
        form.setValue(`${prefix}.amount`, credit.amount);
        form.setValue(`${prefix}.remark`, credit.remark);
        form.setValue(
          `${prefix}.vehicle`,
          hasExitPapers &&
            credit?.vehicle_id === null &&
            exitPaperVehicles[index]?.amount === credit.amount
            ? exitPaperVehicles[index]?.vehicle
            : credit.vehicle,
        );
        form.setValue(`${prefix}.container`, credit.container);
      });
    }
  };

  const fetchData = async () => {
    if (customerPaymentSelected && isUpdate) {
      setAttachments(customerPaymentSelected?.attachments);
      form.setValue(
        'attachments',
        customerPaymentSelected?.attachments || null,
      );
      form.setValue('company_id', customerPaymentSelected?.company_id || null);
      form.setValue('bank_id', customerPaymentSelected?.bank_id || null);
      form.setValue(
        'customer_id',
        customerPaymentSelected?.customer_id || null,
      );
      form.setValue('state', customerPaymentSelected?.state || null);
      form.setValue(
        'payment_method',
        customerPaymentSelected?.payment_method || null,
      );
      form.setValue(
        'amount',
        parseFloat(customerPaymentSelected?.amount) || null,
      );
      form.setValue('currency', customerPaymentSelected?.currency || null);
      form.setValue('remark', customerPaymentSelected?.remark || null);
      form.setValue('link', customerPaymentSelected?.link || null);
      form.setValue(
        'credit_date',
        customerPaymentSelected?.credit_date || null,
      );
      form.setValue(
        'transaction_number',
        customerPaymentSelected?.transaction_number || '',
      );
      form.setValue(
        'transaction_fee',
        parseFloat(customerPaymentSelected?.transaction_fee) || 0,
      );
      form.setValue(
        'exchange_rate',
        parseFloat(customerPaymentSelected?.exchange_rate) || null,
      );
      form.setValue(
        'service_charge',
        parseFloat(customerPaymentSelected?.service_charge) || 0,
      );

      prepareCustomerCredits(customerPaymentSelected);
    }

    if (isAddExitPaper) {
      prepareCustomerCredits({ customer_credits: exitPaperCredits });
    }
  };

  const handleTypeToggle = (type) => {
    if (type === 'container') {
      setIsContainer(true);
      setIsVehicle(false);
    } else if (type === 'vehicle') {
      setIsContainer(false);
      setIsVehicle(true);
    }
  };

  // UseEffect for customer payment update.
  useEffect(() => {
    if (!isUpdate) {
      getTransactionNumber(form.getValues('payment_method'));
    } else {
      setTransactionNumberDisabled(true);
    }
    fetchData();
  }, [customerPaymentSelected, isUpdate]);

  useEffect(() => {
    if (form?.errors?.company_id) {
      setCompErr(true);
    } else {
      setCompErr(false);
    }
  }, [form?.errors?.company_id]);

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Grid display="flex" justifyContent="center" alignItems="center" mb={1.5}>
        <Typography variant="h5" component="div">
          <div>Customer Credit Details</div>
        </Typography>
        {customerPaymentSelected?.state && (
          <Chip
            sx={{
              fontSize: '10px',
              marginLeft: '15px',
              height: '24px',
              backgroundColor: choseColor(customerPaymentSelected?.state),
              color: 'white',
            }}
            label={removeUnderScore2(customerPaymentSelected?.state)}
          />
        )}
      </Grid>
      <Grid container spacing={2}>
        <Grid size={6}>
          <Controller
            name="company_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  sx={error ? { mt: 1.5 } : { my: 1.5 }}
                  url="autoComplete"
                  label="Select Company"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  fieldName="name"
                  disabled={isAddExitPaper || disabled() || hasCredits()}
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'companies'}
                />
              );
            }}
          />
        </Grid>
        <Grid size={6}>
          <Controller
            key={renderKeyCus}
            name="customer_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  sx={compErr ? { mt: 1.5 } : { my: 1.5 }}
                  url={false}
                  label="Select Customer"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  fieldName=""
                  disabled={isAddExitPaper || disabled() || hasCredits()}
                  field={field}
                  error={error}
                  staticOptions={customers}
                  column={''}
                  modal={''}
                  defualtValue={customers[0]}
                />
              );
            }}
          />
        </Grid>
        <Grid size={6}>
          <Controller
            name="payment_method"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              const selectedKey = field.value;
              const showCVToggle =
                dualCreditPaymentMethods.includes(selectedKey);

              return (
                <>
                  <AutoComplete
                    url={false}
                    label="Payment Method"
                    className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                    fieldName=""
                    field={field}
                    disabled={isAddExitPaper || disabled() || hasCredits()}
                    error={error}
                    staticOptions={paymentMethodOptions}
                    handleOnChangeFromStepper={true}
                    stepperHandleOnChange={async (_event, value) => {
                      const selectedKey = value?.id;

                      if (dualCreditPaymentMethods.includes(selectedKey)) {
                        setIsVehicle(true);
                        setIsContainer(false);
                      } else {
                        setIsVehicle(false);
                        setIsContainer(false);
                      }

                      if (
                        Object.keys(creditPaymentMethods).includes(selectedKey)
                      ) {
                        getTransactionNumber(selectedKey);
                      } else {
                        setTransactionNumberDisabled(false);
                        form.setValue('transaction_number', '', {
                          shouldValidate: true,
                        });
                      }

                      field.onChange(selectedKey);
                    }}
                    column=""
                    modal=""
                  />

                  {showCVToggle && (
                    <div
                      style={{
                        display: 'flex',
                        marginTop: '12px',
                        justifyContent: 'space-evenly',
                      }}
                    >
                      <button
                        type="button"
                        onClick={() => handleTypeToggle('vehicle')}
                        style={{
                          padding: '6px 16px',
                          borderRadius: '8px',
                          border: isVehicle
                            ? '2px solid #007BFF'
                            : '1px solid #ccc',
                          backgroundColor: isVehicle ? '#E3F2FD' : '#fff',
                          color: isVehicle ? '#007BFF' : '#333',
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                        }}
                        disabled={hasCredits()}
                      >
                        Vehicle
                      </button>

                      <button
                        type="button"
                        onClick={() => handleTypeToggle('container')}
                        style={{
                          padding: '6px 16px',
                          borderRadius: '8px',
                          border: isContainer
                            ? '2px solid #007BFF'
                            : '1px solid #ccc',
                          backgroundColor: isContainer ? '#E3F2FD' : '#fff',
                          color: isContainer ? '#007BFF' : '#333',
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                        }}
                        disabled={hasCredits()}
                      >
                        Container
                      </button>
                    </div>
                  )}
                </>
              );
            }}
          />
        </Grid>
        <Grid
          style={{ display: 'flex', justifyContent: 'space-between' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid container spacing={2}>
            <Grid size={hasExitPapers || isAddExitPaper ? 6 : 12}>
              <TextField
                size="small"
                error={form.errors.amount?.message.length > 0}
                id="amount"
                label="Amount"
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                type="number"
                fullWidth
                variant="outlined"
                disabled={
                  isAdmin === true
                    ? false
                    : disabled() || hasCredits() || !hasCredits()
                }
                InputProps={{ inputProps: { min: 1 } }}
                {...form.register('amount', {
                  setValueAs: (value) => (value ? +value : 0),
                })}
                helperText={
                  form.errors.amount?.message &&
                  'Enter An Amount Greater Than 0'
                }
              />
            </Grid>
            {hasExitPapers || isAddExitPaper ? (
              <Grid size={6}>
                <TextField
                  size="small"
                  id="service_charge"
                  label="Service Charge (AED)"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  type="number"
                  fullWidth
                  variant="outlined"
                  disabled={!isAdmin}
                  InputProps={{ inputProps: { min: 1 } }}
                  {...form.register('service_charge', {
                    setValueAs: (value) => (value ? +value : 0),
                  })}
                />
              </Grid>
            ) : null}
          </Grid>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name={`currency`}
            control={form.control}
            render={({ field, fieldState: { invalid, error } }) => (
              <MUIAutoComplete
                size="small"
                value={field.value}
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                getOptionLabel={(option) => option}
                onChange={async (_event: any, newValue) => {
                  form.setValue(`currency`, newValue);
                  if (newValue != 'USD') {
                    const rate = await getExchangeRate(id, newValue);
                    form.setValue(`exchange_rate`, rate);
                    setExchangeRate(rate);
                  } else {
                    form.setValue(`exchange_rate`, 1);
                    setExchangeRate(1);
                  }
                }}
                disabled={disabled() || hasCredits()}
                options={currencyOptions}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="currency"
                    error={invalid}
                    helperText={error?.message}
                  />
                )}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.exchange_rate?.message.length > 0}
            id="exchange_rate"
            label="Exchange Rate"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="number"
            fullWidth
            variant="outlined"
            disabled={disabled() || hasCredits()}
            value={exchangeRate}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('exchange_rate', {
              setValueAs: (value) => (value ? +value : null),
              onChange: (e) =>
                setExchangeRate(e.target.value ? +e.target.value : 1),
            })}
            helperText={
              form.errors.exchange_rate?.message &&
              'Exchange Rate greater than 0'
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.transaction_number?.message.length > 0}
            id="transaction_number"
            label="Transaction Number"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            value={form.getValues('transaction_number')}
            disabled={transactionNumberDisabled || disabled()}
            onChange={(e) => {
              form.setValue('transaction_number', e.target.value, {
                shouldValidate: true,
              });
            }}
            helperText={
              form.errors.transaction_number?.message &&
              'Enter Valid Transaction Number'
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 4,
          }}
        >
          <Controller
            control={form.control}
            name="credit_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                views={['year', 'month', 'day']}
                label="Credit Date"
                value={!field.value ? new Date() : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                disabled={disabled() || hasCredits()}
                onChange={(e) => {
                  const date = new Date(
                    dayjs(e).format('YYYY-MM-DD'),
                  ).toISOString();
                  form.setValue('credit_date', date);
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.link?.message.length > 0}
            id="link"
            label="Attachments Link"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            disabled={disabled()}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('link', {
              setValueAs: (value) => (value ? value : ''),
            })}
          />
        </Grid>
        <Grid
          textAlign="left"
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box display="flex" gap={1}>
            <Button
              component="label"
              role={undefined}
              variant="contained"
              tabIndex={-1}
              startIcon={<CloudUploadIcon />}
              sx={{ marginBottom: '10px' }}
              disabled={disabled()}
            >
              {customerPaymentSelected?.attachments
                ? 'Update Attachments'
                : 'Select Attachments'}
              <VisuallyHiddenInput
                type="file"
                multiple
                onChange={(e) => handleUploadedFile(e.target.files)}
              />
            </Button>
          </Box>
          <sub style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            {attachments?.map((attachment, i) => (
              <List
                key={i + 1}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  border: '1px solid gray',
                  borderRadius: '5px',
                  marginBottom: '5px',
                  padding: '8px 12px',
                  width: '230px',
                }}
              >
                <Typography fontSize={12}>
                  {attachment.name.substring(0, 18)}...
                  {attachment.deposit_date && (
                    <>
                      <br />
                      Deposit Date:{' '}
                      {dayjs(attachment.deposit_date).format('YYYY/MM/DD')}
                    </>
                  )}
                </Typography>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  gap={1}
                >
                  <Link
                    sx={{
                      width: '20px',
                      height: '20px',
                      marginBottom: '4px',
                      cursor: disabled() ? 'not-allowed' : 'pointer',
                      opacity: disabled() ? 0.5 : 1,
                    }}
                    onClick={() =>
                      removeSingleFile(attachment.name, attachment.url)
                    }
                  >
                    <CancelIcon color="error" />
                  </Link>
                  <Link
                    sx={{
                      gap: '2',
                      width: '20px',
                      height: '20px',
                      textDecoration: 'none',
                      color: 'green',
                      marginBottom: '4px',
                    }}
                    onClick={() => getInvoiceDownloadUrl(attachment.name)}
                    target="_blank"
                  >
                    <FeedIcon />
                  </Link>
                </Box>
              </List>
            ))}
          </sub>
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <TextField
            size="small"
            error={form.errors.remark?.message.length > 0}
            id="remark"
            label="Remark"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            disabled={disabled()}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('remark', {
              setValueAs: (value) => (value ? value : ''),
            })}
          />
        </Grid>

        <Grid size={12}>
          {creditFormsIds.map((formItem) => {
            return (
              <Paper
                sx={{
                  p: 2,
                  pl: 3,
                  pr: 3,
                  border: '1px solid #ccc',
                  backgroundColor: isDarkMode ? 'grey.900' : 'white',
                  mb: 2,
                }}
                key={formItem.id}
              >
                <Grid container spacing={2} alignItems="center">
                  <Grid
                    size={{
                      xs: 12,
                      md: 4,
                    }}
                  >
                    <Controller
                      name={
                        isContainer
                          ? `credit_form_${formItem.id}.container_id`
                          : `credit_form_${formItem.id}.vehicle_id`
                      }
                      control={form.control}
                      render={({ field, fieldState: { error } }) => {
                        // Get current value from form
                        const currentValue = form.watch(
                          isContainer
                            ? `credit_form_${formItem.id}.container_id`
                            : `credit_form_${formItem.id}.vehicle_id`,
                        );

                        // Find the corresponding vehicle/container object
                        const selectedOption = isContainer
                          ? containers.find((c) => c.id === currentValue)
                          : vehicles.find((v) => v.id === currentValue);

                        return (
                          <MUIAutoComplete
                            size="small"
                            options={isContainer ? containers : vehicles}
                            getOptionLabel={(option) =>
                              isContainer
                                ? option.container_number || ''
                                : option.vinLot || 'No VIN or Lot'
                            }
                            isOptionEqualToValue={(option, value) =>
                              option.id === value?.id
                            }
                            getOptionDisabled={(option) =>
                              isContainer
                                ? selectedContainerIds.includes(option.id)
                                : selectedVehicleIds.includes(option.id)
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label={
                                  isContainer
                                    ? 'Select Containers'
                                    : 'Select Vehicles'
                                }
                                error={!!error}
                                helperText={error?.message}
                                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                                onChange={(e) => {
                                  if (!isContainer && companyId) {
                                    fetchVinAndLotOfVehicles(
                                      companyId,
                                      e.target.value,
                                    );
                                  } else if (companyId) {
                                    fetchContainerNumbers(
                                      companyId,
                                      e.target.value,
                                    );
                                  }
                                }}
                              />
                            )}
                            value={selectedOption || null}
                            onChange={(_, newValue) => {
                              if (isContainer) {
                                const previousContainerId = form.getValues(
                                  `credit_form_${formItem.id}.container`,
                                )?.id;
                                setSelectedContainerIds((prev) =>
                                  [
                                    ...prev,
                                    ...(newValue ? [newValue.id] : []),
                                  ].filter((id) => id !== previousContainerId),
                                );
                                form.setValue(
                                  `credit_form_${formItem.id}.container`,
                                  newValue,
                                );
                              } else {
                                const previousVehicleId = form.getValues(
                                  `credit_form_${formItem.id}.vehicle`,
                                )?.id;
                                setSelectedVehicleIds((prev) =>
                                  [
                                    ...prev,
                                    ...(newValue ? [newValue.id] : []),
                                  ].filter((id) => id !== previousVehicleId),
                                );
                                form.setValue(
                                  `credit_form_${formItem.id}.vehicle`,
                                  newValue,
                                );
                              }
                              field.onChange(newValue ? newValue?.id : '');
                            }}
                            renderOption={(props, option) => (
                              <li {...props} key={option.id}>
                                {isContainer
                                  ? option.container_number
                                  : option.vinLot || 'No VIN or Lot'}
                              </li>
                            )}
                            disabled={
                              hasPaymentsApplied ||
                              hasExitPapers ||
                              isAddExitPaper ||
                              isDamage
                            }
                          />
                        );
                      }}
                    />
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      md: 4,
                    }}
                  >
                    <Controller
                      name={`credit_form_${formItem.id}.amount`}
                      control={form.control}
                      render={({ field, fieldState: { error } }) => (
                        <TextField
                          size="small"
                          label="Amount"
                          className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                          type="number"
                          fullWidth
                          variant="outlined"
                          {...field}
                          onChange={(e) => {
                            field.onChange(
                              e.target.value ? +e.target.value : null,
                            );
                          }}
                          error={!!error}
                          helperText={error?.message}
                          InputProps={{ inputProps: { min: 0 } }}
                        />
                      )}
                      disabled={
                        !isAdmin &&
                        (hasPaymentsApplied ||
                          hasExitPapers ||
                          isAddExitPaper ||
                          isDamage)
                      }
                    />
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      md: 3,
                    }}
                  >
                    <Controller
                      name={`credit_form_${formItem.id}.remark`}
                      control={form.control}
                      render={({ field, fieldState: { error } }) => (
                        <TextField
                          size="small"
                          label="Remark"
                          className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                          type="text"
                          fullWidth
                          variant="outlined"
                          {...field}
                          error={!!error}
                          helperText={error?.message}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    display="flex"
                    justifyContent="flex-end"
                    size={{
                      xs: 12,
                      md: 1,
                    }}
                  >
                    <Tooltip
                      title={
                        hasPaymentsApplied
                          ? 'Remove Payments First'
                          : hasExitPapers && !isAdmin
                            ? 'cannot remove individual credits from exit paper credits'
                            : isAddExitPaper && !isAdmin
                              ? 'cannot remove credits manually here'
                              : isDamage && !isAdmin
                                ? 'cannot remove individual credits from damage credits'
                                : 'Remove Credit'
                      }
                    >
                      <Button
                        color="error"
                        onClick={() => {
                          if (
                            (!hasPaymentsApplied &&
                              !hasExitPapers &&
                              !isAddExitPaper &&
                              !isDamage) ||
                            isAdmin
                          ) {
                            const containerId = form.getValues(
                              `credit_form_${formItem.id}.container_id`,
                            );
                            const vehicleId = form.getValues(
                              `credit_form_${formItem.id}.vehicle_id`,
                            );
                            if (containerId) {
                              setSelectedContainerIds((prev) =>
                                prev.filter((id) => id !== containerId),
                              );
                            }
                            if (vehicleId) {
                              setSelectedVehicleIds((prev) =>
                                prev.filter((id) => id !== vehicleId),
                              );
                            }
                            setCreditFormsIds((prev) =>
                              prev.filter((item) => item.id !== formItem.id),
                            );
                            form.unregister(`credit_form_${formItem.id}`);
                            setManualAdminUpdate(!manualAdminUpdate);
                          }
                        }}
                        sx={{
                          padding: 0,
                          minWidth: 'auto',
                          cursor:
                            (hasPaymentsApplied ||
                              hasExitPapers ||
                              isAddExitPaper ||
                              isDamage) &&
                            !isAdmin
                              ? 'not-allowed'
                              : 'pointer',
                        }}
                      >
                        <CancelIcon />
                      </Button>
                    </Tooltip>
                  </Grid>
                </Grid>
              </Paper>
            );
          })}
        </Grid>
        <Grid display="flex" justifyContent="center" size={12}>
          <Box mt={2}>
            <Tooltip
              title={
                hasPaymentsApplied
                  ? 'Remove Payments First'
                  : hasExitPapers
                    ? 'cannot add credits if the current credits are created from the exit papers module/section'
                    : isAddExitPaper
                      ? 'cannot add credits manually here'
                      : isDamage
                        ? 'cannot add credits if the current credits are created from the damage module/section'
                        : 'Add Credits'
              }
            >
              <Button
                variant="outlined"
                color="success"
                disabled={isAddButtonDisabled}
                onClick={() => {
                  if (
                    !hasPaymentsApplied &&
                    !hasExitPapers &&
                    !isAddExitPaper &&
                    !isDamage
                  ) {
                    const newId = Date.now().toString();
                    setCreditFormsIds((prev) => [...prev, { id: newId }]);
                  }
                }}
                sx={{
                  cursor:
                    hasPaymentsApplied ||
                    hasExitPapers ||
                    isAddExitPaper ||
                    isDamage
                      ? 'not-allowed'
                      : 'pointer',
                }}
              >
                {(() => {
                  const paymentMethod = form.watch('payment_method');
                  const method = creditCustomerPaymentMethods.find(
                    (m) => m.id === paymentMethod,
                  );
                  if (dualCreditPaymentMethods.includes(paymentMethod)) {
                    return isContainer
                      ? 'Add Container Credits'
                      : 'Add Vehicle Credits';
                  }
                  return method?.type === 'container'
                    ? 'Add Container Credits'
                    : method?.type === 'vehicle'
                      ? 'Add Vehicle Credits'
                      : 'Add Credits';
                })()}
              </Button>
            </Tooltip>
          </Box>
        </Grid>
        <Grid display="flex" justifyContent="flex-end" size={12}>
          <Box mt={2}>
            Total Credit/s: {form.watch('amount')} {form.watch('currency')}
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}
