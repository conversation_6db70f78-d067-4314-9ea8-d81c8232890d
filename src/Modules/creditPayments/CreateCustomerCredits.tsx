import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useRef, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import Step1 from './steps/Step1';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { schema } from '../customerPayments/customerPaymentsComponent/customerPaymentHeader';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import { isUpdateDisabled } from '../customerPayments/customerPaymentsComponent/PaymentHelper';
import { nanoid } from 'nanoid';

interface ExchangeRateType {
  currency: string;
  rate: number;
  created_at: string;
}

export const CreateCustomerCredits = ({
  show,
  setShow,
  isUpdate,
  isAddExitPaper = false,
  selectedItems,
  recordManager,
  setSelectedItems,
  loading = false,
  fetchRecords,
}) => {
  const [loadingButton, setLoadingButton] = useState(false);
  const [attachments, setAttachments] = useState(null);
  const [deletedAttachments, setDeletedAttachments] = useState([]);
  const [isDone, setIsDone] = useState(false);
  const isSubmitting = useRef(false);
  const [exitPaperCredits, setExitPaperCredits] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    setValue,
    control,
    watch,
    getValues,
    unregister,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      payment_method: 'demurrage_credit',
      amount: 0,
      amount_applied: 0,
      transaction_fee: 0,
      currency: 'USD',
      exchange_rate: 1.0,
      company_id: null,
      customer_id: null,
      transaction_number: '',
      attachments: [],
      deletedAttachments: [],
      remark: '',
      link: '',
      credit_date: new Date().toISOString(),
      customer_credits: [],
      service_charge: 0,
      inapplicable_amount: 0,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
    setValue,
    selectedItems,
    isUpdate,
    getValues,
    unregister,
  };

  const submit = async (values) => {
    if (isSubmitting.current) return; // we need to prevent multiple submissions
    isSubmitting.current = true;
    setLoadingButton(true);
    values.attachment = form.getValues('attachments');
    values.deletedAttachments = form.getValues('deletedAttachments');

    if (isAddExitPaper) {
      values.exitPapersIds = selectedItems?.map((item) => item?.id);
      values.exitPaperStatus = 'credit_given_to_customer';
      values.is_credit_given = true;
    }

    // START: remove nanoid assigned to custom exit paper vehicles
    if (values.payment_method === 'exit_paper_credit') {
      values.customer_credits = values.customer_credits.map((credit) => {
        return {
          ...credit,
          vehicle_id: /^[A-Za-z0-9_-]{21}$/.test(credit.vehicle_id)
            ? null
            : credit.vehicle_id,
        };
      });
    }
    // END: remove nanoid assigned to custom exit paper vehicles

    try {
      if (!isUpdate) {
        const { data } = await axios.post(
          '/customer-payment-transaction/credit',
          values,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        if (data.result == true) {
          recordManager(data.data, 'add');
          setIsDone(true);
          fetchRecords();
          toast.success('Done successfully!');
        } else {
          toast.error(data.message);
        }
      } else {
        const { data } = await axios.patch(
          'customer-payment-transaction/credit/' + selectedItems[0].id,
          values,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        if (data.result === true) {
          recordManager(data.data, 'update');
          setIsDone(true);
          fetchRecords();
          toast.success('Record updated successfully!');
        } else {
          toast.error(data.message);
        }
      }
    } catch (error) {
      // toast.error(error.message);
    } finally {
      setLoadingButton(false);
      isSubmitting.current = false;
    }
  };

  const steps = [
    {
      label: 'Credit Info',
      icon: <InfoIcon />,
      step: (
        <Step1
          form={form}
          customerPaymentSelected={selectedItems[0]}
          attachments={attachments}
          setAttachments={setAttachments}
          deletedAttachments={deletedAttachments}
          setDeletedAttachments={setDeletedAttachments}
          isUpdate={isUpdate}
          isAddExitPaper={isAddExitPaper}
          exitPaperCredits={exitPaperCredits}
        />
      ),
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger([
          'payment_method',
          'amount',
          'amount_applied',
          'currency',
          'exchange_rate',
          'company_id',
          'customer_id',
          'transaction_number',
        ]);
        return isValid;
      },
    },
  ];

  useEffect(() => {
    if (isAddExitPaper && selectedItems.length > 0) {
      const company_id: number = selectedItems[0]?.exit_papers_vehicles.sort(
        // Sort vehicles so that those with a non-null vehicle_id come before those with null
        (a, b) => {
          if (a.vehicle_id === null && b.vehicle_id !== null) return 1;
          if (a.vehicle_id !== null && b.vehicle_id === null) return -1;
          return 0;
        },
      )[0]?.vehicles?.companies?.id;
      const currency = selectedItems[0]?.exit_papers_vehicles?.[0]?.currency;
      const service_charge = selectedItems.reduce(
        (acc, item) => acc + item.service_charge,
        0,
      );
      const exit_paper_vehicle_credits = selectedItems
        .map((item) => item.exit_papers_vehicles)
        .flat()
        .map((vehicle) => {
          const vehicleId = nanoid();
          return {
            id: vehicle?.id,
            amount: +vehicle?.custom_duty + +vehicle?.vat,
            remark: '',
            vehicle: {
              id: vehicle?.vehicle_id || vehicleId,
              vin: !!vehicle?.custom_vin
                ? vehicle?.custom_vin
                : vehicle?.vehicles?.vin,
              lot_number: '',
            },
            vehicle_id: vehicle?.vehicle_id || vehicleId,
            containers: {},
            container_id: null,
          };
        });
      const exchange_rate =
        currency === 'USD'
          ? 1
          : selectedItems[0]?.exit_papers_vehicles[0]?.vehicles?.companies?.exchange_rates
              .filter(
                (exchange_rate: ExchangeRateType) =>
                  exchange_rate?.currency === currency,
              )
              .sort(
                (a: { created_at: string }, b: { created_at: string }) =>
                  new Date(b.created_at).getTime() -
                  new Date(a.created_at).getTime(),
              )[0]?.rate;
      const exchange_rate_for_service_charge =
        currency === 'AED'
          ? { currency: 'USD', rate: 1 }
          : selectedItems[0]?.exit_papers_vehicles[0]?.vehicles?.companies?.exchange_rates
              .filter(
                (exchange_rate: ExchangeRateType) =>
                  exchange_rate?.currency === 'AED',
              )
              .sort(
                (a: { created_at: string }, b: { created_at: string }) =>
                  new Date(b.created_at).getTime() -
                  new Date(a.created_at).getTime(),
              )[0] || { currency: 'AED', rate: 3.685 };
      form.setValue('company_id', company_id || null);
      form.setValue('currency', currency || null);
      form.setValue(
        'payment_method',
        selectedItems[0]?.is_mukhasa ? 'mukhasa' : 'exit_paper_credit',
      );
      form.setValue('service_charge', service_charge);
      form.setValue('exchange_rate', exchange_rate);
      selectedItems[0].exchange_rate = exchange_rate;
      selectedItems[0].exchange_rate_for_service_charge =
        exchange_rate_for_service_charge;
      setExitPaperCredits(exit_paper_vehicle_credits);
    }
  }, [selectedItems]);

  useEffect(() => {
    if (!show) {
      setAttachments([]);
      setDeletedAttachments([]);
    }
  }, [show]);

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={
          isAddExitPaper
            ? 'Add Exit Paper Credit/s'
            : isUpdate
              ? 'Update Credit'
              : 'Create Credit'
        }
        isUpdate={isUpdate}
        hasQuickSave={true}
        loading={loading}
        titleFontSize="15px"
        disableButton={isUpdateDisabled(selectedItems[0])}
      />
    </form>
  );
};
