import Head from 'next/head';
import { SyntheticEvent, useContext, useEffect, useRef, useState } from 'react';
import { TabPanelProps } from '@/models/profile/iTabPanel';
import {
  Box,
  Container,
  Paper,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton,
  Typography,
  Button,
  Grid,
  InputLabel,
  FormControl,
  styled,
  InputBase,
  alpha,
} from '@mui/material';

import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import axios from '@/lib/axios';
import { contextProvider } from '@/contexts/ProfileContext';
import Profile from '@/components/mainComponents/Cropper/Profile';
import { uploadApi } from '@/utils/axios';
import { toast } from 'react-toastify';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 1 }}>
          <Box>{children}</Box>
        </Box>
      )}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}
interface ErrorResponse {
  statusCode: number;
  message: string;
}

const BootstrapInput = styled(InputBase)(({ theme }) => ({
  'label + &': { marginTop: theme.spacing(2) },
  '& .MuiInputBase-input': {
    borderRadius: 4,
    position: 'relative',
    backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    border: '1px solid',
    borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    fontSize: 16,
    width: '100%',
    padding: '5px 12px',
    transition: theme.transitions.create([
      'border-color',
      'background-color',
      'box-shadow',
    ]),
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      boxShadow: `${alpha(theme.colors.primary.main, 0.25)} 0 0 0 0.2rem`,
    },
  },
}));

const UserProfile = () => {
  const [value, setValue] = useState(0);
  //@ts-ignore
  const handleChange = (event: SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  const useProfileContext = useContext(contextProvider);
  const { profile, refreshProfile } = useProfileContext;
  const [showPassword, setShowPassword] = useState(false);
  const [showRePassword, setShowRePassword] = useState(false);

  const [username, setUsername] = useState();
  const [email, setEmail] = useState();
  const [passwordValue, setPasswordValue] = useState('');
  const [rePasswordValue, setRePasswordValue] = useState('');
  const [file, setFile] = useState('');
  const profileUrl = useRef<string>('');
  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const handleClickShowRePassword = () => setShowRePassword((show) => !show);

  const handlePasswordKeypress = (e) => setPasswordValue(e.target.value);
  const handleRePasswordKeypress = (e) => setRePasswordValue(e.target.value);

  const handleUsernameKeypress = (e) => setUsername(e.target.value);
  const handleEmailKeypress = (e) => setEmail(e.target.value);

  const userType = profile?.data?.loginable?.loginable_type;
  const handleMouseDownPassword = (event) => event.preventDefault();

  const handleMouseDownRePassword = (event) => event.preventDefault();

  const handleCredentials = async () => {
    try {
      const res = await axios.patch('auth/changeCredentials', {
        username: username,
        email: email,
        new_password: rePasswordValue,
        old_password: passwordValue,
      });
      if (res?.data?.result) {
        setPasswordValue('');
        setRePasswordValue('');

        toast.success('Done successfully!');
      }
    } catch (error) {
      if (error?.response?.data?.statusCode == 403) {
        toast.error(error?.response?.data?.message);
      }
    }
  };

  const handleProfileUpload = async (): Promise<void> => {
    try {
      const formData = new FormData();
      formData.append('file', file!);
      const res = await uploadApi.post('auth/uploadProfile', formData);
      if (res?.data?.result) {
        setFile('');
        refreshProfile();

        toast.success('Done successfully!');
      }
    } catch (error) {
      const errorResponse: ErrorResponse | undefined = error?.response?.data;
      if (errorResponse) {
        toast.error(errorResponse.message);
      }
    }
  };

  useEffect(() => {
    if (profile) {
      setUsername(profile?.data?.loginable?.username);
      setEmail(profile?.data?.loginable?.email);
      profileUrl.current = `${process.env.NEXT_PUBLIC_NEST_IMAGE_URL}${profile?.data?.photo}`;
    }
  }, [profile]);

  useEffect(() => {
    if (file) {
      handleProfileUpload();
    }
  }, [file]);

  return (
    <>
      <Head>
        <title>Profile</title>
      </Head>
      <PageHeader
        breadcrumbs={[
          {
            href: 'false',
            name: 'Profile',
            icon: <ManageAccountsIcon sx={{ fontSize: '18px' }} />,
            key: '1',
          },
        ]}
      />
      <Container
        sx={{
          maxWidth: '1800px !important',
          px: 1,
          mt: 5,
          position: 'relative',
        }}
      >
        <Box
          component="div"
          sx={{
            backgroundImage:
              'url(https://latest.pglsystem.com/img/photos-1/back-login.png)',
            height: '300px',
            width: '100%',
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            zIndex: -1,
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
          }}
        ></Box>
        <Box sx={{ display: { md: 'flex' } }}>
          <Paper
            sx={{
              alignItems: 'center',
              flex: 1,
              mr: { md: 1 },
              mb: { xs: 2, md: 0 },
              height: '100%',
              textAlign: 'center',
            }}
          >
            <Box
              component="img"
              src="/static/images/auth/login_page_image.png"
              width="100%"
            />
            {userType !== 'loader' && (
              <Box sx={{ mt: '-20%' }}>
                <Profile
                  width={{ width: '175px', height: '175px' }}
                  profileUrl={profileUrl}
                  setFile={setFile}
                />
              </Box>
            )}
            <Typography variant="h5" sx={{ mt: 3, pb: { xs: 5, md: 5 } }}>
              {profile?.data?.fullname
                ? profile?.data?.fullname
                : profile?.data?.name}
            </Typography>
          </Paper>
          <Paper sx={{ flex: 2, p: 1 }}>
            <Box sx={{ width: '100%' }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={value}
                  onChange={handleChange}
                  aria-label="basic tabs example"
                >
                  {/* <Tab label="General Info" {...a11yProps(0)} /> */}
                  {userType !== 'loader' ? (
                    <Tab label="User Credential" {...a11yProps(0)} />
                  ) : (
                    <Tab label="User Profile" {...a11yProps(0)} />
                  )}
                </Tabs>
              </Box>
              {userType !== 'loader' ? (
                <TabPanel value={value} index={0}>
                  <Typography my="10px">User Credential</Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl sx={{ width: '100%' }} variant="standard">
                        <InputLabel shrink htmlFor="bootstrap-input">
                          Email
                        </InputLabel>
                        <BootstrapInput
                          id="bootstrap-input"
                          size="small"
                          value={email}
                          disabled
                          onChange={handleEmailKeypress}
                          type={'text'}
                        />
                      </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl sx={{ width: '100%' }} variant="standard">
                        <InputLabel shrink htmlFor="bootstrap-input">
                          Username
                        </InputLabel>
                        <BootstrapInput
                          id="bootstrap-input"
                          size="small"
                          value={username}
                          disabled
                          onChange={handleUsernameKeypress}
                          type={'text'}
                        />
                      </FormControl>
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        size="small"
                        id="Password"
                        label="Old Password"
                        variant="outlined"
                        value={passwordValue}
                        onChange={handlePasswordKeypress}
                        type={showPassword ? 'text' : 'password'}
                        fullWidth
                        InputProps={{
                          // <-- This is where the toggle button is added.
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle password visibility"
                                onClick={handleClickShowPassword}
                                onMouseDown={handleMouseDownPassword}
                                edge="end"
                              >
                                {showPassword ? (
                                  <Visibility />
                                ) : (
                                  <VisibilityOff />
                                )}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        size="small"
                        id="outlined-basic"
                        label="New Password"
                        variant="outlined"
                        type={showRePassword ? 'text' : 'password'}
                        value={rePasswordValue}
                        onChange={handleRePasswordKeypress}
                        fullWidth
                        InputProps={{
                          // <-- This is where the toggle button is added.
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle password visibility"
                                onClick={handleClickShowRePassword}
                                onMouseDown={handleMouseDownRePassword}
                                edge="end"
                              >
                                {showRePassword ? (
                                  <Visibility />
                                ) : (
                                  <VisibilityOff />
                                )}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid>
                  </Grid>
                  <Button
                    sx={{
                      position: 'absolute',
                      bottom: '50px',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      minWidth: '100px',
                    }}
                    variant="contained"
                    onClick={handleCredentials}
                    color="primary"
                    autoFocus
                  >
                    Change
                  </Button>
                </TabPanel>
              ) : (
                <TabPanel value={value} index={0}>
                  <Box sx={{ mt: 4 }}>
                    <Grid container>
                      <Grid size={{ xs: 4, md: 4 }}>NAME:</Grid>
                      <Grid size={{ xs: 8, md: 8 }}>{profile?.data?.name}</Grid>

                      <Grid sx={{ mt: 4 }} size={{ xs: 4, md: 4 }}>
                        EMAIL:
                      </Grid>
                      <Grid sx={{ mt: 4 }} size={{ xs: 8, md: 8 }}>
                        {profile?.data?.loginable?.email}
                      </Grid>
                    </Grid>
                  </Box>
                </TabPanel>
              )}
            </Box>
          </Paper>
        </Box>
      </Container>
    </>
  );
};

export default UserProfile;
