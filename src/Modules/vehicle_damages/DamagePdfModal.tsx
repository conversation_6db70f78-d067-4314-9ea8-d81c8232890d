import PdfModal from '@/components/mainComponents/pdf&csv/PdfModal';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import React from 'react';

const DamagePdfModal = ({
  showDownload,
  title,
  selectedHeaders,
  setShowDownload,
  apiUrl,
  tableRecords,
  options,
  totalItems,
  selectedItems,
}) => {
  const fetchDownloadRecords = async () => {
    try {
      // If there are selected items, return those instead of fetching from API
      if (selectedItems && selectedItems.length > 0) {
        return { result: true, data: selectedItems };
      }

      // Otherwise fetch all records as before
      let { data } = await axios.get(apiUrl, {
        params: {
          status: options.tab != 'all' ? options.tab : '',
          page: options.page,
          per_page: 20000,
          search: options.search,
          exactMatch: options.exactMatch,
          filterData: JSON.stringify(options.filterData),
          isExport: true,
          exportTotal: totalItems,
          exportType: `${title} CSV`,
        },
      });
      return { result: true, data: data.data };
    } catch (error) {
      return { result: false, message: error };
    }
  };

  return (
    <PdfModal
      selectedItems={selectedItems}
      options={options}
      open={showDownload}
      pdf_title={title}
      selectedHeaders={selectedHeaders}
      setShowDownload={setShowDownload}
      fetchDownloadRecords={fetchDownloadRecords}
      tableRecords={selectedItems.length > 0 ? selectedItems : tableRecords} // Use selected items if available
      headers={selectedHeaders}
      id={(item) => item.id}
      loaded_by={(item) =>
        item.vehicles?.containers?.loaders
          .map((loader) => loader.name)
          .join(', ')
      }
      email_sent_at={(item) =>
        item.email_sent_at
          ? new Date(item.email_sent_at)
              .toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: '2-digit',
              })
              .replace(/(\d+)\s(\w+)\s(\d+)/, '$3 $2 $1')
          : ''
      }
      damage_type={(item) => item.damage_type}
      case_number={(item) => `PGLDMG0${item.case_number}`}
      damage_details={(item) =>
        item.damage_details.map((damage) => damage.detail).join(', ')
      }
      auction_photos_link={(item) => item.auction_photos_link}
      photo_link={(item) => item.photo_link}
      photo_link_loading={(item) => item.photo_link_loading}
      inspection_photo={(item) => item.inspection_photo}
      damage_happened_at={(item) =>
        item.damage_happened_at.map((damage) => damage.happened_at).join(', ')
      }
      pol_locations={(item) => item.vehicles?.pol_locations?.name}
      description={(item) =>
        `${item.vehicles?.year} ${item.vehicles?.make} ${item.vehicles?.model} ${item.vehicles?.color}`
      }
      vin={(item) => item.vehicles?.vin}
      lot_number={(item) => item.vehicles?.lot_number}
      price={(item) => item.vehicles?.price}
      company={(item) => item.vehicles?.customers?.companies?.name}
      container_number={(item) => item.vehicles?.containers?.container_number}
      case_received_date={(item) => formatDate(item.case_received_date)}
      loading_date={(item) =>
        formatDate(item.vehicles?.containers?.loading_date)
      }
      eta={(item) => formatDate(item.vehicles?.containers?.bookings?.eta)}
      etd={(item) =>
        formatDate(item.vehicles?.containers?.bookings?.vessels?.etd)
      }
      claim={(item) => item.claim}
      confirmed={(item) => item.confirmed}
      credit={(item) => item.credit}
      lc_credit={(item) => item.lc_credit}
      claim_status={(item) => item.claim_status}
      damage_status={(item) => item.damage_status}
      remark={(item) => item.remark}
    />
  );
};

export default DamagePdfModal;
