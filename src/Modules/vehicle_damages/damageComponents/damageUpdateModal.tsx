import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Ty<PERSON><PERSON>,
  IconButton,
  Divider,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CardActions,
  Button,
  TextField,
  Grid,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import {
  damage_detail_options,
  damage_happened_at_options,
} from '@/configs/vehicles_damage/damageHeader';
import { Done, CloudUpload, Image } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useState, useRef } from 'react';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';

// Styled component for the upload button
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

type updateDamageModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  enumOptions: Array<{ id: string; label: string }> | any;
  modalType:
    | 'dropDown'
    | 'text'
    | 'textDropDown'
    | 'multiSelect_damage_details'
    | 'multiSelect_happened_at'
    | 'imageUpload';
  itemvalue:
    | string
    | number
    | Array<{ detail: string }>
    | Array<{ happened_at: string }>
    | File;
  setItemValue: (
    value:
      | string
      | number
      | Array<{ detail: string }>
      | Array<{ happened_at: string }>
      | File,
  ) => void;
  onSubmit: () => void;
  loading: boolean;
  numValue: number;
  setNumValue: (value: number) => void;
  value: string | number;
  field: string;
  permissions?: string[]; // Add permissions prop
};

const DamageUpdateModal: React.FC<updateDamageModalProps> = ({
  open,
  setOpen,
  title,
  enumOptions,
  itemvalue,
  setItemValue,
  onSubmit,
  loading,
  modalType,
  numValue,
  setNumValue,
  field,
  permissions = [],
  // value,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setItemValue(file);

      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle viewing current image (if exists)
  const handleViewImage = () => {
    if (
      typeof itemvalue === 'string' &&
      itemvalue !== 'null' &&
      itemvalue !== ''
    ) {
      window.open(itemvalue, '_blank');
    }
  };

  return (
    <Modal
      open={open}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box>
        <Card sx={{ width: 450 }}>
          <Box
            sx={{
              m: 0,
              p: 2,
              py: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant="h6">
              {title
                .split('_')
                .map((word, index) =>
                  index === 0
                    ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                    : word.toLowerCase(),
                )
                .join(' ')}
            </Typography>
            <IconButton
              aria-label="close"
              sx={{
                color: 'grey',
              }}
              onClick={() => setOpen(false)}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <CardContent>
            {modalType === 'dropDown' && (
              <FormControl fullWidth size="small">
                <InputLabel id="select-label">Select an Option</InputLabel>
                <Select
                  value={itemvalue}
                  label="Select Option"
                  onChange={(event) => {
                    setItemValue(event.target.value);
                  }}
                  sx={{ mb: 1.5 }}
                >
                  {field === 'damage_status'
                    ? enumOptions
                        .filter((item) => {
                          // Always exclude these statuses from dropdown
                          if (
                            [
                              'Half_Cut',
                              'Pre_Credit',
                              'Credited',
                              'Initial_Review',
                            ].includes(item.id)
                          ) {
                            return false;
                          }
                          // Check if user has permission for status changes
                          if (
                            !permissions?.includes(DAMAGE?.CHANGE_DAMAGE_STATUS)
                          ) {
                            return false;
                          }
                          return true;
                        })
                        .map((item, index) => (
                          <MenuItem key={index} value={item.id}>
                            {item.label}
                          </MenuItem>
                        ))
                    : enumOptions.map((item, index) =>
                        item.id && item.label ? (
                          <MenuItem key={index} value={item.id}>
                            {item.label}
                          </MenuItem>
                        ) : null,
                      )}
                </Select>
              </FormControl>
            )}

            {modalType === 'text' && (
              <FormControl fullWidth size="small">
                <TextField
                  type="text"
                  value={itemvalue}
                  onChange={(event) => {
                    setItemValue(event.target.value);
                  }}
                  InputProps={{
                    style: {
                      height: '40px',
                      fontSize: '12px',
                    },
                  }}
                />
              </FormControl>
            )}

            {modalType === 'textDropDown' && (
              <FormControl fullWidth size="small">
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box sx={{ width: '100%' }}>
                    <TextField
                      type="number"
                      value={numValue}
                      label="Amount"
                      onChange={(event) => {
                        setNumValue(parseFloat(event.target.value));
                      }}
                      InputProps={{
                        style: {
                          height: '40px',
                          fontSize: '12px',
                          marginBottom: '15px',
                          width: '100%',
                        },
                      }}
                    />
                  </Box>

                  <Box sx={{ width: '100%' }}>
                    <InputLabel
                      id="currency-select-label"
                      sx={{ top: '5px !important', left: '50% !important' }}
                      htmlFor="currency-select"
                    >
                      Currency
                    </InputLabel>
                    <Select
                      id="currency-select"
                      labelId="currency-select-label"
                      value={itemvalue}
                      label="Select Option"
                      onChange={(event) => {
                        setItemValue(event.target.value);
                      }}
                      sx={{ mb: 1.5, width: '100%' }}
                    >
                      {enumOptions.map((item, index) =>
                        item.id && item.label ? (
                          <MenuItem key={index} value={item.id}>
                            {item.label}
                          </MenuItem>
                        ) : null,
                      )}
                    </Select>
                  </Box>
                </Box>
              </FormControl>
            )}

            {modalType === 'imageUpload' && (
              <FormControl fullWidth>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    alignItems: 'center',
                  }}
                >
                  {/* Display current image link if it exists */}
                  {typeof itemvalue === 'string' &&
                    itemvalue !== 'null' &&
                    itemvalue !== '' && (
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          mb: 2,
                        }}
                      >
                        <Typography variant="subtitle2">
                          Current Image:
                        </Typography>
                        <Button
                          variant="outlined"
                          startIcon={<Image />}
                          onClick={handleViewImage}
                          sx={{ mt: 1 }}
                        >
                          View Image
                        </Button>
                      </Box>
                    )}

                  {/* Preview of selected image */}
                  {previewUrl && (
                    <Box sx={{ mb: 2, textAlign: 'center' }}>
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        Preview:
                      </Typography>
                      <img
                        src={previewUrl}
                        alt="Preview"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '200px',
                          objectFit: 'contain',
                          border: '1px solid #e0e0e0',
                          borderRadius: '4px',
                          padding: '4px',
                        }}
                      />
                    </Box>
                  )}

                  {/* File upload button */}
                  <Button
                    component="label"
                    variant="contained"
                    startIcon={<CloudUpload />}
                    color="primary"
                    sx={{ mb: 1 }}
                  >
                    Upload Inspection Photo
                    <VisuallyHiddenInput
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      ref={fileInputRef}
                    />
                  </Button>

                  {selectedFile && (
                    <Typography variant="caption" sx={{ textAlign: 'center' }}>
                      Selected file: {selectedFile.name}
                    </Typography>
                  )}
                </Box>
              </FormControl>
            )}

            {modalType === 'multiSelect_damage_details' && (
              <FormControl fullWidth size="small">
                <Grid>
                  <FilterAutocomplete
                    url={''}
                    label={'Damage Details'}
                    name={'damage_details'}
                    keyName={'name'}
                    values={
                      Array.isArray(itemvalue)
                        ? itemvalue.map((item) => item.detail)
                        : []
                    }
                    staticOptions={damage_detail_options.map((item) => ({
                      id: item.id,
                      name: item.label,
                    }))}
                    onChange={(newValues) => {
                      const updatedSelectedValue = newValues.map((v) =>
                        typeof v === 'string'
                          ? { detail: v }
                          : { detail: v.id },
                      );

                      setItemValue(updatedSelectedValue);
                    }}
                  />
                </Grid>
              </FormControl>
            )}

            {modalType === 'multiSelect_happened_at' && (
              <FormControl fullWidth size="small">
                <Grid>
                  <FilterAutocomplete
                    url={''}
                    label={'Damage Happened At'}
                    name={'damage_happened_at'}
                    keyName={'name'}
                    values={
                      Array.isArray(itemvalue)
                        ? itemvalue.map((item) => item.happened_at)
                        : []
                    }
                    staticOptions={damage_happened_at_options.map((item) => ({
                      id: item.id,
                      name: item.label,
                    }))}
                    onChange={(newValues) => {
                      const updatedSelectedValue = newValues.map((v) =>
                        typeof v === 'string'
                          ? { happened_at: v }
                          : { happened_at: v.id },
                      );

                      setItemValue(updatedSelectedValue);
                    }}
                  />
                </Grid>
              </FormControl>
            )}
          </CardContent>
          <CardActions
            disableSpacing
            sx={{ display: 'flex', justifyContent: 'center' }}
          >
            <Button
              loading={loading}
              variant="contained"
              loadingPosition="start"
              size="small"
              startIcon={<Done />}
              onClick={onSubmit}
            >
              Save
            </Button>
          </CardActions>
        </Card>
      </Box>
    </Modal>
  );
};

export default DamageUpdateModal;
