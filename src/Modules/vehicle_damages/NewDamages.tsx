import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  Box,
  Container,
  IconButton,
  Tab,
  Tabs,
  Tooltip,
  CircularProgress,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import Head from 'next/head';
import { useContext, useEffect, useState, useRef } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import axios from '@/lib/axios';
import ColumnDialog from '@/components/mainComponents/customizeColumn/ColumnDialog';
import { applySavedColumns } from '@/utils/columnUtils';
import FilterModal2 from '@/components/mainComponents/cModal/FilterModal2';
import { formatDate, handelColor } from '@/configs/vehicles/configs';
import { copyORViewFun, recordManager } from '@/configs/configs';
import {
  filterContentDamages,
  useDamageHeaderInfo,
  damage_happened_at_options,
  damage_type_options,
  damage_status_options,
  claim_status_options,
  damagesTabs,
  viewPermissions,
  damage_detail_options,
  financeDamageHeader,
  pol_options,
  getDamageHeadersByTab,
} from '@/configs/vehicles_damage/damageHeader';
import {
  destinationColor,
  destinationTextColor,
} from '@/configs/booking/bookingHeader';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import ViewSingleDamage from './ViewSingleDamage';
import { CreateDamage } from './CreateDamage';
import { buyerNumberColoring } from '../vehicles/vehicleComponents/CustomColumnVehicle';
import { useRouter } from 'next/router';
import ImageIcon from '@mui/icons-material/Image';
import ImageNotSupportedIcon from '@mui/icons-material/ImageNotSupported';
import {
  AddCircleRounded,
  ForwardToInboxRounded,
  AccountBalanceWalletRounded,
  PictureAsPdfRounded,
  SwapHorizRounded,
} from '@mui/icons-material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { removeUnderScore2 } from '@/configs/common';
import UpdateDamageField from './updateDamageField';
import SearchBy from '@/components/mainComponents/cComponents/SearchBy';
import { DamageSearchFields } from '@/configs/vehicles_damage/damageConfigs';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import { toast } from 'react-toastify';
import DamagePdfModal from './DamagePdfModal';
import PreCreditModal from './DamagProcess/PreCreditModal';
import VehicleImagesModal from './VehicleImagesModal';
import InitialReviewDamages from './DamagProcess/InitialReviewDamages';
import DamageProcessModal from './DamagProcess/DamageProcessModal';
import InspectionPhotoModal from './InspectionPhoto/InspectionPhotoModal';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import PreCreditDamages from './DamagProcess/PreCreditDamages';
import DamageStatusModal from './DamageStatusModal';

const NewDamage = ({ activeTab = 'all', apiUrl, defaultHeaders }) => {
  const useProfileContext = useContext(contextProvider);
  const damageHeaderInfo = useDamageHeaderInfo();
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedItems, setSelectedItems] = useState([]);
  const [cancelToken, setCancelToken] = useState(null);
  const [openFilter, setOpenFilter] = useState(false);
  const [showColumnDialog, setShowColumnDialog] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(0);
  const [showDownload, setShowDownload] = useState(false);
  const [showCreate, setShowCreate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [updateDamageTypeItem, setUpdateDamageTypeItem] = useState(null);
  const [field, setField] = useState('');
  const [value, setValue] = useState<string | number | any[]>();
  const [numValue, setNumValue] = useState<number>();
  const tableName = 'vehicle_damages';
  const initialQueryProcessed = useRef(false);
  // Add state for hierarchical

  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    tab: activeTab,
    search: '',
    exactMatch: false,
    orderBy: {
      column: 'id',
      order: 'desc',
    },
    status: activeTab,
    searchOptions: '',
  });

  const pageName = 'Damages' + activeTab;
  const [loading, setLoading] = useState(false);
  const [viewData, setViewData] = useState({});
  const [view, setView] = useState(false);
  const router = useRouter();
  const [showPreCreditModal, setShowPreCreditModal] = useState(false);
  const [selectedDamageItem, setSelectedDamageItem] = useState(null);
  const [modalMode, setModalMode] = useState<'preCredit' | 'credited'>(
    'preCredit',
  );
  const [showImagesModal, setShowImagesModal] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [showAuctionImagesModal, setShowAuctionImagesModal] = useState(false);
  const [showWarehouseImagesModal, setShowWarehouseImagesModal] =
    useState(false);
  const [showBulkEmailModal, setShowBulkEmailModal] = useState(false);
  // State for inspection photo modal
  const [showInspectionPhotoModal, setShowInspectionPhotoModal] =
    useState(false);
  const [currentInspectionDamage, setCurrentInspectionDamage] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    damageData: null,
    onConfirm: () => {},
  });
  const [pdfLoading, setPdfLoading] = useState(false);
  const [showDamageStatusModal, setShowDamageStatusModal] = useState(false);

  // Flatten hierarchical tabs to show all items in one line
  const flattenTabs = (tabs) => {
    const flattened = [];

    tabs.forEach((tab) => {
      if (tab.children) {
        // Add child tabs directly instead of parent tab
        tab.children.forEach((child) => {
          const requiredPermission = viewPermissions[child.value];
          if (requiredPermission ? perms?.includes(requiredPermission) : true) {
            flattened.push(child);
          }
        });
      } else {
        // Add regular tab
        const requiredPermission = viewPermissions[tab.value];
        if (requiredPermission ? perms?.includes(requiredPermission) : true) {
          flattened.push(tab);
        }
      }
    });

    return flattened;
  };

  let filteredTab = flattenTabs(damagesTabs);

  // Add useEffect to handle URL parameters
  useEffect(() => {
    const { query } = router;
    if (Object.keys(query).length > 0 && !initialQueryProcessed.current) {
      const newOptions = {
        ...options,
        search: typeof query.search === 'string' ? query.search : '',
        searchOptions:
          typeof query.searchOptions === 'string' ? query.searchOptions : '',
        exactMatch: query.exactMatch === 'true',
        status: typeof query.status === 'string' ? query.status : activeTab,
        filterData: query.filterData
          ? JSON.parse(decodeURIComponent(query.filterData as string))
          : {},
      };

      setOptions(newOptions);
      initialQueryProcessed.current = true;
    }
  }, [router.query]); // Removed activeTab from dependencies to prevent loops

  // Modify handleTabChange to use clean URLs like sidebar menu
  const handleTabChange = (_: React.ChangeEvent<{}>, newValue: string) => {
    setSelectedItems([]);
    const newOptions = {
      ...options,
      status: newValue,
    };
    setOptions(newOptions);

    // Use clean URL without query parameters, similar to sidebar menu
    router.push(`/vehicle_damages/${newValue}`);
  };

  const fetchRecords = async () => {
    try {
      // Prevent multiple simultaneous API calls
      if (loading) {
        return;
      }

      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setTableRecords([]);
      setSelectedItems([]);
      setLoading(true);

      // Convert 'approved' to 'all' for the API request since backend doesn't support 'approved' status
      // We'll filter the results client-side to show only 'Approved' items
      // Also convert 'audit_reviewed' to 'all' since backend might not support it directly
      const apiStatus =
        activeTab === 'approved' || activeTab === 'audit_reviewed'
          ? 'all'
          : activeTab;

      const requestParams = {
        page: options.page,
        per_page:
          ['initial_review', 'pre_credit', 'approved'].includes(activeTab) ||
          activeTab === 'audit_reviewed'
            ? 1000
            : options.perPage, // Use a large value for tabs that need client-side filtering
        search: options.search,
        exactMatch: options.exactMatch,
        column: options.orderBy.column,
        order: options.orderBy.order,
        filterData: JSON.stringify(options.filterData),
        status: apiStatus,
        searchOptions: options.searchOptions,
      };

      const { data } = await axios.get(apiUrl, { params: requestParams });

      // If we're on the approved tab, filter the results to show only items with status 'Approved'
      if (activeTab === 'approved') {
        const approvedDamages = data.data.filter(
          (item) => item.damage_status === 'Approved',
        );
        setTotalItems(approvedDamages.length);
        setTableRecords(approvedDamages);
      } else if (activeTab === 'audit_reviewed') {
        // Filter to show only damages that have been audit reviewed (have audit_reviewed_at field)
        const auditReviewedDamages = data.data.filter(
          (item) =>
            item.audit_reviewed_at !== null &&
            item.audit_reviewed_at !== undefined,
        );

        // Apply pagination to filtered results
        const startIndex = (options.page - 1) * options.perPage;
        const endIndex = startIndex + options.perPage;
        const paginatedResults = auditReviewedDamages.slice(
          startIndex,
          endIndex,
        );

        setTotalItems(auditReviewedDamages.length);
        setTableRecords(paginatedResults);
      } else {
        setTotalItems(data.total);
        setTableRecords(data.data);
      }
    } catch (error) {
      console.error('API Error:', error);
    }
    setLoading(false);
  };

  const SendEmail = ({ item }: { item: any }): React.ReactElement => {
    const handleProcessClick = () => {
      // Validate the damage item before opening the modal
      const validationResult = validateDamageItem(item);
      if (!validationResult.isValid) {
        toast.error(validationResult.error);
        return;
      }

      // All validations passed, set the item and open modal
      setSelectedItems([item]);
      setShowBulkEmailModal(true);
    };

    // Check if item has a previously rejected case number
    const hasExistingCaseNumber = !!item.case_number;
    const tooltipText = hasExistingCaseNumber
      ? `Process damage with existing case number PGLDMG0${item.case_number}`
      : 'Process damage with new case number';

    return (
      <>
        {['In_process', 'Half_Cut', 'Pending_CA'].includes(
          item.damage_status,
        ) ? (
          <AppTooltip title={tooltipText}>
            <Button
              size="small"
              variant="contained"
              color={
                item.email_sent_at
                  ? 'success'
                  : hasExistingCaseNumber
                    ? 'secondary'
                    : 'primary'
              }
              onClick={handleProcessClick}
            >
              {item.email_sent_at
                ? formatDate(item.email_sent_at, 'YYYY MMM DD')
                : hasExistingCaseNumber
                  ? `Process (Case ${item.case_number})`
                  : 'Process Damages'}
            </Button>
          </AppTooltip>
        ) : (
          ''
        )}
      </>
    );
  };

  // Add a validation helper function
  const validateDamageItem = (item) => {
    const vin = item.vehicles?.vin || `ID:${item.id}`;

    // Check for damage_happened_at values first (priority)
    if (
      !item.damage_happened_at ||
      !Array.isArray(item.damage_happened_at) ||
      item.damage_happened_at.length === 0
    ) {
      return {
        isValid: false,
        error: `Please add damage happened at information for: ${vin}`,
      };
    }

    // Check for inspection photo - could be a file URL or Google Drive link
    if (!item.inspection_photo) {
      return {
        isValid: false,
        error: `Please upload inspection photo for: ${vin}`,
      };
    }

    // If inspection_photo exists, check if it's a valid URL or Google Drive link
    const isValidPhotoUrl =
      typeof item.inspection_photo === 'string' &&
      (item.inspection_photo.startsWith('http') ||
        item.inspection_photo.startsWith('https') ||
        item.inspection_photo.includes('drive.google.com') ||
        item.inspection_photo.includes('minio'));

    if (!isValidPhotoUrl) {
      return {
        isValid: false,
        error: `Invalid inspection photo URL for: ${vin}`,
      };
    }

    // Check for damage_details values
    if (
      !item.damage_details ||
      !Array.isArray(item.damage_details) ||
      item.damage_details.length === 0
    ) {
      return {
        isValid: false,
        error: `Please add damage details for: ${vin}`,
      };
    }

    // Check credit amount is not zero
    if (!item.credit || parseFloat(item.credit) === 0) {
      return {
        isValid: false,
        error: `Please set credit amount for: ${vin}`,
      };
    }

    return { isValid: true, error: null };
  };

  // Add a function to validate if all selected damages belong to the same customer
  const validateSameCustomer = (items) => {
    // Ensure items is an array
    const damageItems = Array.isArray(items) ? items : items ? [items] : [];

    // Early return for single or no items - always valid
    if (damageItems.length <= 1) {
      return { isValid: true, error: null };
    }

    // Collect both company info and parent company info for proper validation
    const companiesInfo = damageItems
      .map((item) => ({
        id: item?.vehicles?.customers?.companies?.id,
        name: item?.vehicles?.customers?.companies?.name,
        parentId: item?.vehicles?.customers?.companies?.parent_company?.id,
        parentName: item?.vehicles?.customers?.companies?.parent_company?.name,
      }))
      .filter((company) => company.name); // Filter out items without company name

    // Check if we have any valid company info
    if (companiesInfo.length === 0) {
      return {
        isValid: false,
        error: `No valid customer names found in selected items`,
      };
    }

    // If not all items have company info, still proceed with those that do
    if (companiesInfo.length !== damageItems.length) {
    }

    // First, check if all company names are the same
    const firstCompanyName = companiesInfo[0].name;
    const allSameCompanyName = companiesInfo.every(
      (company) => company.name === firstCompanyName,
    );

    // If all company names are the same, we can return valid immediately
    if (allSameCompanyName) {
      return { isValid: true, error: null };
    }

    // Direct check: If any company name matches any parent company name
    // This is a separate check specifically for parent_company.name === company.name

    let directNameMatchFound = false;
    const companyNamesSet = new Set(companiesInfo.map((c) => c.name));
    const parentNamesSet = new Set(
      companiesInfo.filter((c) => c.parentName).map((c) => c.parentName),
    );

    // Check if any company name appears in the parent names set
    for (const companyName of companyNamesSet) {
      if (parentNamesSet.has(companyName)) {
        directNameMatchFound = true;
        break;
      }
    }

    // If direct name match found, return valid
    if (directNameMatchFound) {
      return { isValid: true, error: null };
    }

    // If no direct name match, proceed with checking parent-child relationships

    // Build a relationship map for parent-child relationships
    const parentMap = new Map();
    companiesInfo.forEach((company) => {
      if (company.parentId) {
        parentMap.set(company.id, company.parentId);
      }
    });

    // Get all unique company IDs
    const uniqueCompanyIds = [...new Set(companiesInfo.map((c) => c.id))];

    // Check if companies are related (same company or parent-child relationship)
    const firstCompanyId = uniqueCompanyIds[0];
    const relatedIds = new Set([firstCompanyId]);

    // Add parent if exists
    if (parentMap.has(firstCompanyId)) {
      relatedIds.add(parentMap.get(firstCompanyId));
    }

    // Check if all companies are related
    let areCompaniesRelated = true;

    // Check if all other companies are related to the first one
    for (let i = 1; i < uniqueCompanyIds.length; i++) {
      const currentId = uniqueCompanyIds[i];
      const currentCompany = companiesInfo.find((c) => c.id === currentId);
      const firstCompany = companiesInfo.find((c) => c.id === firstCompanyId);

      // Check if this company is already in our related set
      if (relatedIds.has(currentId)) {
        continue;
      }

      // Check if this company's parent is in our related set
      if (currentCompany.parentId && relatedIds.has(currentCompany.parentId)) {
        relatedIds.add(currentId);
        continue;
      }

      // Check if this company is a parent of any company in our related set
      let isParentOfRelated = false;
      for (const relatedId of relatedIds) {
        if (parentMap.get(relatedId) === currentId) {
          isParentOfRelated = true;
          break;
        }
      }

      if (isParentOfRelated) {
        relatedIds.add(currentId);
        continue;
      }

      // Check if parent company name matches company name (per requirement)
      // This specifically checks if company names match with parent company names
      let nameRelationshipFound = false;

      // Check current company name against first company parent name
      if (
        currentCompany.name &&
        firstCompany.parentName &&
        currentCompany.name === firstCompany.parentName
      ) {
        nameRelationshipFound = true;
      }

      // Check current company parent name against first company name
      if (
        currentCompany.parentName &&
        firstCompany.name &&
        currentCompany.parentName === firstCompany.name
      ) {
        nameRelationshipFound = true;
      }

      // Also check against other related companies
      for (const relatedId of relatedIds) {
        if (relatedId === firstCompanyId) continue; // Already checked

        const relatedCompany = companiesInfo.find((c) => c.id === relatedId);
        if (!relatedCompany) continue;

        // Check current company name against related company parent name
        if (
          currentCompany.name &&
          relatedCompany.parentName &&
          currentCompany.name === relatedCompany.parentName
        ) {
          nameRelationshipFound = true;
          break;
        }

        // Check current company parent name against related company name
        if (
          currentCompany.parentName &&
          relatedCompany.name &&
          currentCompany.parentName === relatedCompany.name
        ) {
          nameRelationshipFound = true;
          break;
        }
      }

      if (nameRelationshipFound) {
        relatedIds.add(currentId);
        continue;
      }

      // If we reach here, this company is not related
      areCompaniesRelated = false;
      break;
    }

    if (!areCompaniesRelated) {
      const uniqueCompanyNames = [...new Set(companiesInfo.map((c) => c.name))];
      const errorMsg = `Selected damages must belong to the same customer or related companies. Found: ${uniqueCompanyNames.join(', ')}`;

      return {
        isValid: false,
        error: errorMsg,
      };
    }

    // All items have related companies
    return { isValid: true, error: null };
  };

  const onDownloadDamageInspectReportPDF = async () => {
    if (selectedItems.length === 0) {
      toast.warning('Please select at least one damage record');
      return;
    }
    try {
      setPdfLoading(true);

      // Get loading date filter
      const loadingDateFilter =
        options.filterData['vehicles.containers.loading_date'] || null;

      // Get loader ID
      const filteredLoaderId =
        options.filterData['vehicles.containers.loaders.some.id']?.[0] || null;

      let filteredData = {
        containers: null,
        data: {
          id: 0,
          name: '',
          dateRange: {
            fromDate: '',
            toDate: '',
          },
        },
      };
      const params: {
        id: any;
        fromDate?: string | Date;
        toDate?: string | Date;
      } = {
        id: filteredLoaderId,
      };

      // Add date range params if loadingDateFilter exists
      if (loadingDateFilter != null && loadingDateFilter != undefined) {
        params.fromDate = loadingDateFilter.from || loadingDateFilter.fromDate;
        params.toDate = loadingDateFilter.to || loadingDateFilter.toDate;
        params.id = filteredLoaderId;

        const response = await axios.get(
          '/loaders/get-only-loader-name-by-id',
          {
            params,
          },
        );

        filteredData = response?.data;
      }

      // Import PDF libraries
      const { pdf } = await import('@react-pdf/renderer');
      const DamageReportPDF = (
        await import('./report/InspectVehicleDamageReportPDF')
      ).default;

      // Generate PDF blob
      const blob = await pdf(
        <DamageReportPDF filteredData={filteredData} damages={selectedItems} />,
      ).toBlob();

      // Create and trigger download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Damage_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      link.click();
      URL.revokeObjectURL(url);

      toast.success('Inspection Vehicle damage report downloaded successfully');
    } catch (error) {
      toast.error(`Report generation failed: ${error.message}`);
    } finally {
      setPdfLoading(false);
    }
  };

  const onBulkEmailClick = async () => {
    if (selectedItems.length === 0) {
      toast.warning('Please select at least one damage to email');
      return;
    }

    // Check if selectedItems contains objects or just IDs
    const isObjectArray =
      selectedItems.length > 0 && typeof selectedItems[0] === 'object';

    let damageItems = [];
    if (isObjectArray) {
      // selectedItems already contains the damage objects
      damageItems = selectedItems;
    } else {
      // selectedItems contains IDs, need to get the actual damage objects
      damageItems = selectedItems
        .map((id) => tableRecords.find((damage) => damage.id === id))
        .filter(Boolean);
    }

    if (damageItems.length === 0) {
      toast.warning('No valid damages selected');
      return;
    }

    // Check for items with existing case numbers first
    const itemsWithCaseNumbers = damageItems.filter((item) => item.case_number);
    if (itemsWithCaseNumbers.length > 0) {
      // Sort by case number to prioritize more recent cases (higher numbers)
      const sortedCaseItems = [...itemsWithCaseNumbers].sort((a, b) => {
        return parseInt(b.case_number) - parseInt(a.case_number);
      });

      const existingCaseNumber = sortedCaseItems[0].case_number;

      // If not all items have the same case number, inform the user
      if (itemsWithCaseNumbers.length > 1) {
        const uniqueCaseNumbers = [
          ...new Set(itemsWithCaseNumbers.map((item) => item.case_number)),
        ];
        if (uniqueCaseNumbers.length > 1) {
          toast.info(
            `Multiple case numbers detected. Using the most recent: PGLDMG0${existingCaseNumber}`,
          );
        }
      }

      // If mixing damages with case numbers and without, inform the user
      if (damageItems.length > itemsWithCaseNumbers.length) {
        toast.info(
          `Some damages don't have case numbers. They will all use case number PGLDMG0${existingCaseNumber}`,
        );
      }
    }

    // Perform pre-validation before opening modal
    const loadingToastId = toast.info('Validating selected damages...', {
      autoClose: false,
    });

    try {
      // Fetch fresh data for selected items to ensure we have latest values
      const promises = damageItems.map((item) =>
        axios
          .get(`vehicle_damages/${item.id}`)
          .then((response) => response.data.data)
          .catch((_) => null),
      );

      const updatedDamages = (await Promise.all(promises)).filter(Boolean);
      toast.dismiss(loadingToastId);

      if (updatedDamages.length === 0) {
        toast.error('Failed to retrieve damage data. Please try again.');
        return;
      }

      // Validate each damage record before opening modal
      for (const damage of updatedDamages) {
        // Use the existing validation helper function
        const validationResult = validateDamageItem(damage);
        if (!validationResult.isValid) {
          toast.error(validationResult.error);
          return;
        }
      }

      // Check if all selected items have the same credit currency
      if (updatedDamages.length > 1) {
        const currencies = updatedDamages
          .map((damage) => damage.credit_currency)
          .filter(Boolean);

        if (currencies.length > 0) {
          const firstCurrency = currencies[0];
          const hasDifferentCurrencies = currencies.some(
            (currency) => currency !== firstCurrency,
          );

          if (hasDifferentCurrencies) {
            const uniqueCurrencies = [...new Set(currencies)];
            toast.error(
              `All damages must have the same credit currency. Found: ${uniqueCurrencies.join(', ')}`,
            );
            return;
          }
        }
      }

      // Run the same customer validation
      const customerValidation = validateSameCustomer(updatedDamages);
      if (!customerValidation.isValid) {
        toast.error(customerValidation.error);
        return;
      }

      // All validations passed, now open the modal
      setSelectedItems(updatedDamages);
      setShowBulkEmailModal(true);
    } catch (error) {
      toast.dismiss(loadingToastId);
      toast.error('Error validating damages. Please try again.');
    }
  };

  const customAactionButtons = () => {
    // Define process tabs where damage status button should be available
    const processTabsWithStatusButton = [
      'under_investigation',
      'in_process',
      'pending_ca',
      'half_cut',
      'forgotten',
      'unloading',
      'dismissed',
    ];

    return (
      <>
        {/* Damage Status Button - Available in all process tabs when items are selected */}
        {processTabsWithStatusButton.includes(activeTab) &&
          perms?.includes(DAMAGE?.CHANGE_DAMAGE_STATUS) &&
          selectedItems.length > 0 && (
            <AppTooltip
              title={
                selectedItems.length === 1
                  ? 'Change Damage Status'
                  : `Change Status for ${selectedItems.length} Damages`
              }
            >
              <IconButton
                color="warning"
                onClick={() => setShowDamageStatusModal(true)}
              >
                <SwapHorizRounded />
              </IconButton>
            </AppTooltip>
          )}

        {perms?.includes(DAMAGE?.EMAIL) &&
          ['in_process', 'pending_ca'].includes(activeTab) && (
            <AppTooltip title={'Bulk Process Damages'}>
              <IconButton
                color="secondary"
                onClick={onBulkEmailClick}
                disabled={selectedItems.length === 0}
              >
                <ForwardToInboxRounded />
              </IconButton>
            </AppTooltip>
          )}

        {['in_process', 'pending_ca'].includes(activeTab) &&
          perms?.includes(DAMAGE?.UPDATE) &&
          selectedItems.length === 1 && (
            <AppTooltip title={'Move to Pre Credit'}>
              <IconButton
                color="primary"
                onClick={() => {
                  // For single item, validation will always pass but keeping the pattern consistent
                  const validation = validateSameCustomer([selectedItems[0]]);
                  if (validation.isValid) {
                    setSelectedDamageItem(selectedItems[0]);
                    setModalMode('preCredit');
                    setShowPreCreditModal(true);
                  } else {
                    toast.error(validation.error);
                  }
                }}
              >
                <AddCircleRounded />
              </IconButton>
            </AppTooltip>
          )}

        {activeTab === 'all' && (
          <AppTooltip title={'Download Inspection Damage PDF Report'}>
            <IconButton
              color="primary"
              onClick={onDownloadDamageInspectReportPDF}
              disabled={selectedItems.length === 0 || pdfLoading}
            >
              {pdfLoading ? (
                <CircularProgress size={24} />
              ) : (
                <PictureAsPdfRounded />
              )}
            </IconButton>
          </AppTooltip>
        )}

        {activeTab === 'pre_credit' &&
          perms?.includes(DAMAGE?.GIVEN_CREDIT) && (
            <>
              {/* For single damage crediting */}
              {selectedItems.length === 1 && (
                <AppTooltip title={'Credit Damage'}>
                  <IconButton
                    color="success"
                    onClick={() => {
                      // Show confirmation dialog
                      setSelectedDamageItem(selectedItems[0]);
                      showConfirmDialog({
                        title: 'Credit Damage',
                        message: `Are you sure you want to credit damage ${selectedItems[0].case_number ? 'PGLDMG0' + selectedItems[0].case_number : 'ID:' + selectedItems[0].id}?`,
                        onConfirm: async () => {
                          try {
                            const response = await axios.patch(
                              `vehicle_damages/credit/${selectedItems[0].id}`,
                            );
                            if (response.data.result) {
                              toast.success(
                                'Damage has been credited successfully',
                              );
                              // Refresh the data
                              fetchRecords();
                            } else {
                              toast.error(
                                response.data.message ||
                                  'Failed to credit damage',
                              );
                            }
                          } catch (error) {
                            toast.error(
                              'Error crediting damage: ' +
                                (error.response?.data?.message ||
                                  error.message),
                            );
                          }
                        },
                      });
                    }}
                  >
                    <AccountBalanceWalletRounded />
                  </IconButton>
                </AppTooltip>
              )}

              {/* For bulk damage crediting */}
              {selectedItems.length > 1 && (
                <AppTooltip title={'Bulk Credit Damages'}>
                  <IconButton
                    color="success"
                    onClick={() => {
                      // Show confirmation dialog for bulk credit
                      showConfirmDialog({
                        title: 'Bulk Credit Damages',
                        message: `Are you sure you want to credit ${selectedItems.length} damages?`,
                        onConfirm: async () => {
                          try {
                            const response = await axios.post(
                              `vehicle_damages/bulk-update`,
                              {
                                ids: selectedItems.map((item) => item.id),
                                field: 'damage_status',
                                value: 'Credited',
                              },
                            );

                            if (response.data.result) {
                              toast.success(
                                `${response.data.updatedCount || selectedItems.length} damages have been credited successfully`,
                              );
                              // Refresh the data
                              fetchRecords();
                            } else {
                              toast.error(
                                response.data.message ||
                                  'Failed to credit damages',
                              );
                            }
                          } catch (error) {
                            toast.error(
                              'Error crediting damages: ' +
                                (error.response?.data?.message ||
                                  error.message),
                            );
                          }
                        },
                      });
                    }}
                  >
                    <AccountBalanceWalletRounded />
                  </IconButton>
                </AppTooltip>
              )}
            </>
          )}

        {activeTab === 'credited' &&
          perms?.includes(DAMAGE?.AUDIT_REVIEW_DAMAGES) && (
            <>
              {/* For single damage approval */}
              {selectedItems.length === 1 && (
                <AppTooltip
                  title={
                    selectedItems[0]?.audit_reviewed_by &&
                    selectedItems[0]?.audit_reviewed_at
                      ? 'Already Audit Reviewed'
                      : 'Audit Review Damage'
                  }
                >
                  <IconButton
                    color="success"
                    disabled={(() => {
                      const item = selectedItems[0];
                      const isApproved =
                        item?.audit_reviewed_by && item?.audit_reviewed_at;
                      return isApproved;
                    })()}
                    onClick={() => {
                      // Show confirmation dialog
                      setSelectedDamageItem(selectedItems[0]);
                      showConfirmDialog({
                        title: 'Audit Review Damage',
                        message: 'Approved by Audit team',
                        damageData: selectedItems[0], // Pass damage data to show in table
                        onConfirm: async () => {
                          try {
                            const response = await axios.patch(
                              `vehicle_damages/${selectedItems[0].id}/audit-review`,
                            );
                            if (response.data.result) {
                              toast.success(
                                'Damage has been audit reviewed successfully',
                              );

                              // Close the modal immediately
                              setConfirmDialog({
                                ...confirmDialog,
                                open: false,
                              });

                              // Unselect the item
                              setSelectedItems([]);

                              // Refresh the data
                              fetchRecords();

                              // Don't update selectedItems - keep them unselected
                            } else {
                              toast.error(
                                response.data.message ||
                                  'Failed to audit review damage',
                              );
                            }
                          } catch (error) {
                            toast.error(
                              'Error audit reviewing damage: ' +
                                (error.response?.data?.message ||
                                  error.message),
                            );
                          }
                        },
                      });
                    }}
                  >
                    <CheckCircleIcon />
                  </IconButton>
                </AppTooltip>
              )}

              {/* For bulk damage approval */}
              {selectedItems.length > 1 &&
                perms?.includes(DAMAGE?.AUDIT_REVIEW_DAMAGES) && (
                  <AppTooltip
                    title={
                      selectedItems.some(
                        (item) =>
                          item?.audit_reviewed_by && item?.audit_reviewed_at,
                      )
                        ? 'Some items already audit reviewed'
                        : 'Bulk Audit Review Damages'
                    }
                  >
                    <IconButton
                      color="success"
                      disabled={selectedItems.some(
                        (item) =>
                          item?.audit_reviewed_by && item?.audit_reviewed_at,
                      )}
                      onClick={() => {
                        // Show confirmation dialog for bulk approval
                        showConfirmDialog({
                          title: 'Bulk Audit Review Damages',
                          message: 'Approved by Audit team',
                          damageData: selectedItems, // Pass all selected damages data
                          onConfirm: async () => {
                            try {
                              // Use individual audit review API calls for each item
                              const promises = selectedItems.map((item) =>
                                axios.patch(
                                  `vehicle_damages/${item.id}/audit-review`,
                                ),
                              );

                              const responses = await Promise.all(promises);
                              const successCount = responses.filter(
                                (response) => response.data.result,
                              ).length;

                              if (successCount > 0) {
                                toast.success(
                                  `${successCount} damages have been audit reviewed successfully`,
                                );

                                // Close the modal immediately
                                setConfirmDialog({
                                  ...confirmDialog,
                                  open: false,
                                });

                                // Unselect all items
                                setSelectedItems([]);

                                // Refresh the data
                                fetchRecords();

                                // Don't update selectedItems - keep them unselected
                              } else {
                                toast.error('Failed to audit review damages');
                              }
                            } catch (error) {
                              toast.error(
                                'Error audit reviewing damages: ' +
                                  (error.response?.data?.message ||
                                    error.message),
                              );
                            }
                          },
                        });
                      }}
                    >
                      <CheckCircleIcon />
                    </IconButton>
                  </AppTooltip>
                )}
            </>
          )}
      </>
    );
  };

  useEffect(() => {
    // Use different headers based on active tab
    let headers;
    if (activeTab === 'pre_credit' || activeTab === 'credited') {
      headers = financeDamageHeader;
    } else {
      // Use the new function to get headers with tab-specific filtering
      headers = getDamageHeadersByTab(activeTab);
    }

    // Call applySavedColumns and store the result
    let savedColumns = headers; // Default to headers
    applySavedColumns({
      profile,
      pageName,
      defaultHeaders: headers,
      setSelectedHeaders,
      setSelectedSetting,
    });

    // Set the headers
    setSelectedHeaders(savedColumns);
  }, [defaultHeaders, activeTab]);

  useEffect(() => {
    fetchRecords();
  }, [
    activeTab,
    options.page,
    options.perPage,
    options.search,
    options.searchOptions,
    options.exactMatch,
    options.orderBy.column,
    options.orderBy.order,
    JSON.stringify(options.filterData), // Use JSON.stringify for object comparison
  ]);

  // Removed this useEffect as fetchDownloadRecords should only be called when downloading
  // useEffect(() => {
  //   fetchDownloadRecords();
  // }, [activeTab]);

  const getSingleRow = async (item) => {
    setViewData(item);
    setView(true);
  };

  // Add this new function to update selected items
  const updateSelectedItems = async (selectedIds: number[]) => {
    try {
      // Fetch the latest data for selected items
      const promises = selectedIds.map((id) =>
        axios
          .get(`vehicle_damages/${id}`)
          .then((response) => response.data.data),
      );

      const updatedItems = await Promise.all(promises);
      setSelectedItems(updatedItems);
    } catch (error) {}
  };

  // Modify the recordManager function to include the update
  const handleRecordManager = (data, type) => {
    recordManager({
      data,
      type,
      setTableRecords,
      tableRecords,
      selectedItems,
      setSelectedItems,
      setTotalItems,
      totalItems,
      apiUrl,
    });

    // If items are selected, update their data
    if (selectedItems.length > 0) {
      updateSelectedItems(selectedItems.map((item) => item.id));
    }
  };

  // Add useEffect to watch for table updates
  useEffect(() => {
    if (selectedItems.length > 0) {
      updateSelectedItems(selectedItems.map((item) => item.id));
    }
  }, [tableRecords]); // This will trigger when the table data changes

  // Function to handle inspection photo update
  const handleInspectionPhotoUpdate = () => {
    // Logic for updating inspection photo
    fetchRecords();
  };

  // Add this function to show the confirmation dialog
  const showConfirmDialog = ({
    title,
    message,
    damageData = null,
    onConfirm,
  }) => {
    setConfirmDialog({
      open: true,
      title,
      message,
      damageData,
      onConfirm,
    });
  };

  // Add an effect to log selected items changes
  useEffect(() => {}, [selectedItems]);

  // Add an effect to log selected items changes
  useEffect(() => {}, [selectedItems]);

  return perms && !perms?.includes(DAMAGE?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Damage Report List</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader
          breadcrumbs={damageHeaderInfo.concat({
            href: 'false',
            name: removeUnderScore2(activeTab),
            icon: <></>,
            key: '4',
          })}
        />

        <Tabs
          sx={{ fontSize: 10, minHeight: '35px' }}
          className="customTab"
          value={
            // Find if activeTab is a child of any parent tab
            (() => {
              const parentTab = damagesTabs.find(
                (tab) =>
                  tab.children &&
                  tab.children.some((child) => child.value === activeTab),
              );
              return parentTab ? activeTab : activeTab;
            })()
          }
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="inherit"
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="full width tabs example"
        >
          {filteredTab.map((tabItem, index) => (
            <Tab
              sx={{ fontSize: 10 }}
              key={index}
              label={tabItem.name}
              value={tabItem.value}
            />
          ))}
        </Tabs>

        {activeTab === 'initial_review' ? (
          <>
            <PageAction
              selectedItems={selectedItems}
              customActionButtons={() =>
                [
                  'under_investigation',
                  'in_process',
                  'pending_ca',
                  'half_cut',
                  'forgotten',
                  'unloading',
                  'dismissed',
                  'initial_review',
                  'pre_credit',
                ].includes(activeTab)
                  ? customAactionButtons()
                  : null
              }
              title={'Damages'}
              options={{
                ...options,
                perPage: 1000, // Set a very high value to effectively remove pagination
              }}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Damages ? `}
              dialogTitle={`Delete Damage Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(DAMAGE?.CREATE)}
              showEditButton={perms?.includes(DAMAGE?.UPDATE)}
              showDeleteButton={perms?.includes(DAMAGE?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
            <InitialReviewDamages
              damages={tableRecords}
              onViewDamage={(damage) => {
                setViewData(damage);
                setView(true);
              }}
              onStatusChange={() => fetchRecords()}
              permissions={perms}
            />
          </>
        ) : activeTab === 'pre_credit' ? (
          <>
            <PageAction
              selectedItems={selectedItems}
              customActionButtons={() =>
                [
                  'under_investigation',
                  'in_process',
                  'pending_ca',
                  'half_cut',
                  'forgotten',
                  'unloading',
                  'dismissed',
                  'initial_review',
                  'pre_credit',
                ].includes(activeTab)
                  ? customAactionButtons()
                  : null
              }
              title={'Damages'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Damages ? `}
              dialogTitle={`Delete Damage Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(DAMAGE?.CREATE)}
              showEditButton={perms?.includes(DAMAGE?.UPDATE)}
              showDeleteButton={perms?.includes(DAMAGE?.DELETE)}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
            <PreCreditDamages
              damages={tableRecords}
              onViewDamage={(damage) => {
                setViewData(damage);
                setView(true);
              }}
              onStatusChange={() => fetchRecords()}
              permissions={perms}
            />
          </>
        ) : activeTab === 'credited' ? (
          <>
            <PageAction
              selectedItems={selectedItems}
              customActionButtons={() =>
                ['credited'].includes(activeTab) ? customAactionButtons() : null
              }
              title={'Damages'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Damages ? `}
              dialogTitle={`Delete Damage Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(DAMAGE?.CREATE)}
              showEditButton={false} // Disable edit for credited damages
              showDeleteButton={false} // Disable delete for credited damages
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
            <DataTable3
              sortLoading={loading}
              options={options}
              setOptions={setOptions}
              totalItems={totalItems}
              loading={loading}
              items={tableRecords}
              headers={selectedHeaders}
              selectedItems={selectedItems}
              setSelectedItems={(items) => {
                setSelectedItems(items);
              }}
              tableName={tableName}
              getSingleRow={getSingleRow}
              getRowStyle={(data) => {
                // Add light green background for approved/audit reviewed items
                if (data.audit_reviewed_at && data.audit_reviewed_by) {
                  return {
                    backgroundColor: '#e8f5e8 !important',
                    '&:hover': {
                      backgroundColor: '#d4f4d4 !important',
                    },
                  };
                }
                return {};
              }}
              // Column renderers for credited damages - matching "all" tab styling
              case_number={(data) =>
                data?.case_number ? (
                  <Box
                    sx={{
                      color: 'white',
                      textAlign: 'center',
                      padding: '3px',
                      borderRadius: '5px',
                      margin: '3px',
                    }}
                  >
                    <span style={{ color: '#3f50b5', fontWeight: 'bold' }}>
                      PGLDMG0{data?.case_number}{' '}
                    </span>
                  </Box>
                ) : (
                  ''
                )
              }
              damage_type={(data) => {
                const damage_type = `${data?.damage_type}`;
                const damageOption = damage_type_options.find(
                  (item) => item.id === damage_type,
                );
                let color = damageOption ? damageOption.color : '';
                return damage_type && damage_type !== 'null' ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        backgroundColor: color,
                        color: '#fff',
                        padding: '5px',
                        borderRadius: '4px',
                        margin: '0px',
                        minWidth: 'unset',
                        textAlign: 'center',
                        cursor: 'default', // Credited damages are not editable
                      }}
                    >
                      {damage_type}
                    </Box>
                  </Box>
                ) : null;
              }}
              damage_details={(data) => {
                const damage_details = data?.damage_details;

                return damage_details && damage_details.length > 0 ? (
                  <Box
                    sx={{
                      cursor: 'default', // Credited damages are not editable
                    }}
                  >
                    {damage_details.map((detail, index) => (
                      <Box
                        component="span"
                        sx={{
                          fontSize: '10px',
                          backgroundColor: '#3f50b5',
                          color: 'white',
                          padding: '3px',
                          borderRadius: '5px',
                          margin: '3px',
                        }}
                        key={index}
                      >
                        {detail.detail ? detail.detail.replace(/_/g, ' ') : ''}
                      </Box>
                    ))}
                  </Box>
                ) : null;
              }}
              damage_happened_at={(data) => {
                const combination_type = `${data?.vehicles?.containers?.load_combination_type}`;
                const damage_happened_at = `${data?.damage_happened_at}` || [];

                return damage_happened_at.length > 0 ? (
                  <Box
                    sx={{
                      textAlign: 'center',
                      cursor: 'default', // Credited damages are not editable
                    }}
                  >
                    {data?.damage_happened_at.map((happened_at, index) => {
                      const damageOption = damage_happened_at_options.find(
                        (item) => item.id === happened_at.happened_at,
                      );
                      let color = damageOption ? damageOption.color : '#6A5ACD';

                      return (
                        <Box
                          component="span"
                          sx={{
                            fontSize: '10px',
                            backgroundColor: color,
                            color: 'white',
                            padding: '3px',
                            borderRadius: '5px',
                            margin: '3px',
                          }}
                          key={index}
                        >
                          {combination_type?.[0]?.toUpperCase()}_
                          {happened_at.happened_at}
                        </Box>
                      );
                    })}
                  </Box>
                ) : null;
              }}
              description={(data) => (
                <Box>
                  {`${data?.vehicles?.year ?? ''} ${data?.vehicles?.make ?? ''} ${data?.vehicles?.model ?? ''} ${data?.vehicles?.color ?? ''}`}
                  <br />
                  <small style={{ color: '#888' }}>
                    {data?.description || ''}
                  </small>
                </Box>
              )}
              vin={(data) => (
                <Box sx={{ textAlign: 'center' }}>
                  <AppTooltip title={'Click to copy'}>
                    <span
                      style={{ cursor: 'pointer' }}
                      onClick={() => copyORViewFun(data?.vehicles?.vin)}
                    >
                      {data?.vehicles?.vin}
                    </span>
                  </AppTooltip>
                </Box>
              )}
              lot_number={(data) =>
                buyerNumberColoring(
                  data?.vehicles?.lot_number,
                  data?.vehicles?.buyer_number,
                )
              }
              company={(data) => (
                <AppTooltip title={data?.vehicles?.customers?.companies?.name}>
                  <span>{data?.vehicles?.customers?.companies?.name}</span>
                </AppTooltip>
              )}
              yards_location={(data) => {
                const yard_location = data?.vehicles?.yards_location?.name;
                return yard_location ? (
                  <Box
                    sx={{
                      backgroundColor: '#4caf50',
                      color: '#fff',
                      padding: '5px',
                      borderRadius: '4px',
                      margin: '0px',
                      textAlign: 'center',
                    }}
                  >
                    {yard_location}
                  </Box>
                ) : null;
              }}
              pol_locations={(item) => {
                const pol = item.vehicles?.pol_locations?.name
                  .split(',')[1]
                  .trim();

                const damageOption = pol_options.find(
                  (item) => item.id === pol,
                );
                let color = damageOption ? damageOption.color : '#fad521';

                return pol !== 'null' ? (
                  <Box
                    sx={{
                      fontSize: '10px',
                      backgroundColor: color,
                      color: 'white',
                      padding: '3px',
                      borderRadius: '5px',
                      margin: '5px',
                      textAlign: 'center',
                    }}
                  >
                    {pol}
                  </Box>
                ) : null;
              }}
              destinations={(item) => {
                const destination =
                  item.vehicles?.containers?.bookings?.destinations;

                if (destination?.name) {
                  return (
                    <Chip
                      size="small"
                      label={destination.name}
                      sx={{
                        backgroundColor:
                          destinationColor[destination.id] || '#50af55',
                        color: destinationTextColor[destination.id] || 'white',
                        height: 'auto',
                        fontSize: '12px',
                        '& .MuiChip-label': { padding: '2px 6px' },
                      }}
                    />
                  );
                }
                return null;
              }}
              claim={(data) => {
                const claimandCurrency = `${data?.claim} ${data?.claim_currency ?? ''}`;
                return claimandCurrency && claimandCurrency !== null ? (
                  <Box sx={{ minWidth: '70px' }}>{claimandCurrency}</Box>
                ) : (
                  <Box></Box>
                );
              }}
              claim_status={(data) => {
                const claim_status = `${data?.claim_status}`;
                const damageOption = claim_status_options.find(
                  (item) => item.id === claim_status,
                );
                let color = damageOption ? damageOption.color : '#6A5ACD';
                return claim_status !== 'null' ? (
                  <Box
                    sx={{
                      backgroundColor: color,
                      color: '#fff',
                      padding: '5px',
                      borderRadius: '4px',
                      margin: '0px',
                      textAlign: 'center',
                      cursor: 'default', // Credited damages are not editable
                    }}
                  >
                    {claim_status}
                  </Box>
                ) : null;
              }}
              credit={(data) => {
                const creditAndCurrency = `${data?.credit} ${data?.credit_currency ?? ''}`;
                return creditAndCurrency && creditAndCurrency !== null ? (
                  <Box sx={{ minWidth: '70px' }}>{creditAndCurrency}</Box>
                ) : (
                  <Box></Box>
                );
              }}
              credited_at={(data) => {
                return data?.credited_at ? formatDate(data?.credited_at) : '';
              }}
              credited_by={(data) => {
                return data?.users_vehicle_damages_credited_by?.fullname || '';
              }}
              damage_status={(data) => {
                const damage_status = `${data?.damage_status}`;
                const damageOption = damage_status_options.find(
                  (item) => item.id === damage_status,
                );
                let color = damageOption ? damageOption.color : '#6A5ACD';
                return damage_status !== 'null' ? (
                  <Box
                    sx={{
                      backgroundColor: color,
                      color: '#fff',
                      padding: '5px',
                      borderRadius: '4px',
                      margin: '0px',
                      textAlign: 'center',
                      cursor: 'default', // Credited damages are not editable
                    }}
                  >
                    {damage_status}
                  </Box>
                ) : null;
              }}
              auction_photos_link={(data) => {
                const vehicles = data?.vehicles;
                const hasAuctionLink = !!vehicles?.auction_photos_link;
                const hasAuctionImages =
                  (vehicles?._count?.vehicle_auction_images ?? 0) > 0;

                return (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      cursor: 'pointer',
                    }}
                    onClick={async () => {
                      setSelectedVehicle(vehicles);
                      setShowAuctionImagesModal(true);
                    }}
                  >
                    {hasAuctionLink || hasAuctionImages ? (
                      <ImageIcon
                        sx={{
                          color: '#3f50b5',
                        }}
                      />
                    ) : (
                      <ImageNotSupportedIcon
                        sx={{
                          color: '#d70505',
                        }}
                      />
                    )}
                  </Box>
                );
              }}
              inspection_photo={(data) => {
                const handleOpenInspectionPhotoModal = () => {
                  setCurrentInspectionDamage(data);
                  setShowInspectionPhotoModal(true);
                };

                return (
                  <>
                    <div onClick={(e) => e.stopPropagation()}>
                      <Tooltip title="Click to view/upload inspection photos">
                        <IconButton
                          onClick={handleOpenInspectionPhotoModal}
                          color={data?.inspection_photo ? 'primary' : 'default'}
                          size="small"
                        >
                          <ImageIcon />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </>
                );
              }}
              created_at={(data) => formatDate(data?.created_at)}
              updated_at={(data) => formatDate(data?.updated_at)}
            />
          </>
        ) : activeTab === 'audit_reviewed' ? (
          <>
            <PageAction
              selectedItems={selectedItems}
              customActionButtons={() => null}
              title={'Damages'}
              options={options}
              setOptions={setOptions}
              total={totalItems}
              showCustomizeColumn={true}
              onCustomizeColumn={() => setShowColumnDialog(true)}
              deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Damages ? `}
              dialogTitle={`Delete Damage Item`}
              showDownload={true}
              onDownload={() => setShowDownload(true)}
              onAdd={() => {
                setIsUpdate(false);
                setShowCreate(true);
              }}
              onEdit={() => {
                setIsUpdate(true);
                setShowCreate(true);
              }}
              onDelete={() => {
                recordManager({
                  data: null,
                  type: 'delete',
                  setTableRecords,
                  tableRecords,
                  selectedItems,
                  setSelectedItems,
                  setTotalItems,
                  totalItems,
                  apiUrl,
                });
              }}
              showAddButton={perms?.includes(DAMAGE?.CREATE)}
              showEditButton={false}
              showDeleteButton={false}
              onFilterClick={() => setOpenFilter(true)}
              isFilterExist={
                Object.keys(options.filterData).length > 0 ? false : true
              }
            />
            <DataTable3
              sortLoading={loading}
              options={options}
              setOptions={setOptions}
              totalItems={totalItems}
              loading={loading}
              items={tableRecords}
              headers={selectedHeaders}
              selectedItems={selectedItems}
              setSelectedItems={(items) => {
                setSelectedItems(items);
              }}
              tableName={tableName}
              getSingleRow={getSingleRow}
              // Column renderers for audit reviewed damages
              case_number={(data) => (
                <AppTooltip title={data?.id}>
                  <Box sx={{ textAlign: 'center' }}>
                    {data?.case_number ? (
                      `PGLDMG0${data?.case_number}`
                    ) : (
                      <span style={{ color: '#999' }}>Not Assigned</span>
                    )}
                  </Box>
                </AppTooltip>
              )}
              damage_type={(data) => {
                const item = damage_type_options.find(
                  (item) => item.id === data.damage_type,
                );
                return (
                  <Chip
                    sx={{
                      backgroundColor: `${item?.color}20`,
                      color: item?.color,
                      fontSize: '12px',
                      fontWeight: 'medium',
                    }}
                    label={item?.label}
                    size="small"
                  />
                );
              }}
              damage_details={(data) => {
                const detailNames = data?.damage_details?.map(
                  (detail) => detail?.detail,
                );
                return (
                  <Box sx={{ textAlign: 'center' }}>
                    {detailNames && detailNames.length > 0 ? (
                      <AppTooltip title={detailNames?.join(', ')}>
                        <span>{detailNames?.join(', ')}</span>
                      </AppTooltip>
                    ) : (
                      'No details'
                    )}
                  </Box>
                );
              }}
              damage_happened_at={(data) => {
                const happenedAtNames = data?.damage_happened_at?.map(
                  (item) => {
                    const option = damage_happened_at_options.find(
                      (opt) => opt.id === item.happened_at,
                    );
                    return option?.label || item.happened_at;
                  },
                );
                return (
                  <Box sx={{ textAlign: 'center' }}>
                    {happenedAtNames && happenedAtNames.length > 0
                      ? happenedAtNames.join(', ')
                      : 'N/A'}
                  </Box>
                );
              }}
              description={(data) => (
                <Box>
                  {`${data?.vehicles?.year ?? ''} ${data?.vehicles?.make ?? ''} ${data?.vehicles?.model ?? ''} ${data?.vehicles?.color ?? ''}`}
                  <br />
                  <small style={{ color: '#888' }}>
                    {data?.description || ''}
                  </small>
                </Box>
              )}
              vin={(data) => (
                <Box sx={{ textAlign: 'center' }}>
                  <AppTooltip title={'Click to copy'}>
                    <span
                      style={{ cursor: 'pointer' }}
                      onClick={() => copyORViewFun(data?.vehicles?.vin)}
                    >
                      {data?.vehicles?.vin}
                    </span>
                  </AppTooltip>
                </Box>
              )}
              lot_number={(data) =>
                buyerNumberColoring(
                  data?.vehicles?.lot_number,
                  data?.vehicles?.buyer_number,
                )
              }
              company={(data) => (
                <AppTooltip title={data?.vehicles?.customers?.companies?.name}>
                  <span>{data?.vehicles?.customers?.companies?.name}</span>
                </AppTooltip>
              )}
              credit={(data) => {
                return data?.credit
                  ? `${data?.credit?.toLocaleString()} ${data?.credit_currency ?? ''}`
                  : '';
              }}
              credited_at={(data) => {
                return data?.credited_at ? formatDate(data?.credited_at) : '';
              }}
              credited_by={(data) => {
                return data?.users_vehicle_damages_credited_by?.fullname || '';
              }}
              audit_reviewed_at={(data) => {
                return data?.audit_reviewed_at
                  ? formatDate(data?.audit_reviewed_at)
                  : '';
              }}
              audit_reviewed_by={(data) => {
                return (
                  data?.users_vehicle_damages_audit_reviewed_by?.fullname || ''
                );
              }}
              damage_status={(data) => {
                const item = damage_status_options.find(
                  (item) => item.id === data.damage_status,
                );
                return item ? (
                  <Chip
                    sx={{
                      backgroundColor: `${item?.color}20`,
                      color: item?.color,
                      fontSize: '12px',
                      fontWeight: 'medium',
                    }}
                    label={removeUnderScore2(item?.label)}
                    size="small"
                  />
                ) : (
                  ''
                );
              }}
            />
          </>
        ) : (
          <DataTable3
            // start default props
            sortLoading={loading}
            options={options}
            setOptions={setOptions}
            totalItems={totalItems}
            loading={loading}
            items={tableRecords}
            headers={selectedHeaders}
            selectedItems={selectedItems}
            setSelectedItems={(items) => {
              setSelectedItems(items);
            }}
            tableName={tableName}
            email_sent_at={(data) =>
              data.damage_status === 'Pre-Credit' ? (
                <Box sx={{ textAlign: 'center' }}>
                  {formatDate(data.email_sent_at, 'YYYY MMM DD')}
                </Box>
              ) : perms?.includes(DAMAGE?.EMAIL) ? (
                <SendEmail item={data} />
              ) : (
                'Send Email'
              )
            }
            case_number={(data) =>
              data?.case_number ? (
                <Box
                  sx={{
                    // backgroundColor: '#3f50b5',
                    color: 'white',
                    textAlign: 'center',
                    padding: '3px',
                    borderRadius: '5px',
                    margin: '3px',
                  }}
                >
                  {data?.case_number ? (
                    <span style={{ color: '#3f50b5', fontWeight: 'bold' }}>
                      PGLDMG0{data?.case_number}{' '}
                    </span>
                  ) : (
                    ''
                  )}
                </Box>
              ) : (
                ''
              )
            }
            // end default props
            // allOptions={['in_process', 'dismessed', 'pre_credit']}
            PageAction={
              <PageAction
                selectedItems={selectedItems}
                customActionButtons={() =>
                  [
                    'under_investigation',
                    'in_process',
                    'pending_ca',
                    'half_cut',
                    'forgotten',
                    'unloading',
                    'dismissed',
                    'initial_review',
                    'pre_credit',
                    'all',
                  ].includes(activeTab)
                    ? customAactionButtons()
                    : null
                }
                title={'Damages'}
                options={options}
                setOptions={setOptions}
                total={totalItems}
                showCustomizeColumn={true}
                onCustomizeColumn={() => setShowColumnDialog(true)}
                deleteTitle={`Are you sure to delete ${selectedItems[0]?.name} Damages ? `}
                dialogTitle={`Delete Damage Item`}
                showDownload={true}
                onDownload={() => setShowDownload(true)}
                onAdd={() => {
                  setIsUpdate(false);
                  setShowCreate(true);
                }}
                onEdit={() => {
                  setIsUpdate(true);
                  setShowCreate(true);
                }}
                onDelete={() => {
                  recordManager({
                    data: null,
                    type: 'delete',
                    setTableRecords,
                    tableRecords,
                    selectedItems,
                    setSelectedItems,
                    setTotalItems,
                    totalItems,
                    apiUrl,
                  });
                }}
                showAddButton={perms?.includes(DAMAGE?.CREATE)}
                showEditButton={perms?.includes(DAMAGE?.UPDATE)}
                showDeleteButton={perms?.includes(DAMAGE?.DELETE)}
                onFilterClick={() => setOpenFilter(true)}
                isFilterExist={
                  Object.keys(options.filterData).length > 0 ? false : true
                }
              />
            }
            hidePagination={
              ['initial_review', 'pre_credit', 'approved'].includes(
                activeTab,
              ) || activeTab === 'pre_credit'
            }
            showRestore={true}
            //start custom props

            created_by={({ users_vehicle_damages_created_by_users }) => (
              <>
                {users_vehicle_damages_created_by_users?.fullname}
                {users_vehicle_damages_created_by_users?.departments?.name &&
                  ' | ' +
                    users_vehicle_damages_created_by_users?.departments?.name}
              </>
            )}
            updated_by={({ users_vehicle_damages_updated_by_users }) => (
              <>
                {users_vehicle_damages_updated_by_users?.fullname}
                {users_vehicle_damages_updated_by_users?.departments?.name &&
                  ' | ' +
                    users_vehicle_damages_updated_by_users?.departments?.name}
              </>
            )}
            created_at={({ created_at }) => formatDate(created_at)}
            updated_at={({ updated_at }) => formatDate(updated_at)}
            loading_date={({ vehicles }) => {
              return (
                vehicles?.containers?.loading_date &&
                formatDate(vehicles.containers?.loading_date)
              );
            }}
            eta={({ vehicles }) => {
              if (vehicles?.containers?.bookings?.eta) {
                return formatDate(vehicles.containers.bookings.eta);
              }
              return null;
            }}
            etd={({ vehicles }) => {
              if (vehicles?.containers?.bookings?.vessels?.etd) {
                return formatDate(vehicles.containers.bookings.vessels.etd);
              }
              return null;
            }}
            remark={({ remark }) => {
              return remark;
            }}
            case_received_date={({ case_received_date }) =>
              formatDate(case_received_date)
            }
            damage_details={(data) => {
              const damage_details = data?.damage_details;

              return damage_details && damage_details.length > 0 ? (
                <Box
                  onClick={() => {
                    setUpdateDamageTypeItem(data);
                    setField('damage_details');
                    setValue(damage_detail_options);
                  }}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  {damage_details.map((detail, index) => (
                    <Box
                      component="span"
                      sx={{
                        fontSize: '10px',
                        backgroundColor: '#3f50b5',
                        color: 'white',
                        padding: '3px',
                        borderRadius: '5px',
                        margin: '3px',
                      }}
                      key={index}
                    >
                      {detail.detail ? detail.detail.replace(/_/g, ' ') : ''}
                    </Box>
                  ))}
                </Box>
              ) : (
                <AddCircleRounded
                  onClick={() => {
                    setUpdateDamageTypeItem(data);
                    setField('damage_details');
                    setValue(damage_detail_options);
                  }}
                  sx={{
                    color: '#d70505',
                    padding: '5px',
                    margin: '0px auto',
                    fontSize: '28px',
                    display: 'block',
                    cursor: 'pointer',
                  }}
                />
              );
            }}
            loaded_by={(item) =>
              item?.vehicles?.containers?.loaders
                ?.map((loader) => loader.name)
                .join(', ')
            }
            pol_locations={(item) => {
              const pol = item.vehicles?.pol_locations?.name
                .split(',')[1]
                .trim();

              const damageOption = pol_options.find((item) => item.id === pol);

              let color = damageOption ? damageOption.color : '#fad521';

              return pol !== 'null' ? (
                <Box
                  sx={{
                    fontSize: '10px',
                    backgroundColor: color,
                    color: 'white',
                    padding: '3px',
                    borderRadius: '5px',
                    margin: '5px',
                    textAlign: 'center',
                  }}
                >
                  {pol}
                </Box>
              ) : null;
            }}
            destinations={(item) => {
              const destination =
                item.vehicles?.containers?.bookings?.destinations;

              if (destination?.name) {
                return (
                  <Chip
                    size="small"
                    label={destination.name}
                    sx={{
                      backgroundColor:
                        destinationColor[destination.id] || '#50af55',
                      color: destinationTextColor[destination.id] || 'white',
                      height: 'auto',
                      fontSize: '12px',
                      '& .MuiChip-label': { padding: '2px 6px' },
                    }}
                  />
                );
              }
              return '-';
            }}
            claim_status={(data) => {
              const claim_status = `${data?.claim_status}`;
              const damageOption = claim_status_options.find(
                (item) => item.id === claim_status,
              );
              let color = damageOption ? damageOption.color : '#6A5ACD';
              return claim_status !== 'null' ? (
                <Box
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('claim_status');
                      setValue(claim_status_options);
                    }
                  }}
                  sx={{
                    backgroundColor: color,
                    color: '#fff',
                    padding: '5px',
                    borderRadius: '4px',
                    margin: '0px',
                    textAlign: 'center',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                >
                  {claim_status}
                </Box>
              ) : (
                <AddCircleRounded
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('claim_status');
                      setValue(claim_status_options);
                    }
                  }}
                  sx={{
                    color: '#d70505',
                    padding: '5px',
                    margin: '0px auto',
                    textAlign: 'center',
                    fontSize: '28px',
                    display: 'block',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                />
              );
            }}
            damage_status={(data) => {
              const damage_status = `${data?.damage_status}`;
              const damageOption = damage_status_options.find(
                (item) => item.id === damage_status,
              );
              let color = damageOption ? damageOption.color : '#6A5ACD';
              return damage_status !== 'null' ? (
                <Box
                  onClick={() => {
                    // Only allow editing if not in restricted statuses and user has permission
                    if (
                      damage_status !== 'Credited' &&
                      damage_status !== 'Initial_Review' &&
                      damage_status !== 'Pre_Credit' &&
                      perms?.includes(DAMAGE?.CHANGE_DAMAGE_STATUS)
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('damage_status');
                      setValue(damage_status_options);
                    }
                  }}
                  sx={{
                    backgroundColor: color,
                    color: '#fff',
                    padding: '5px',
                    borderRadius: '4px',
                    margin: '0px',
                    textAlign: 'center',
                    cursor:
                      damage_status === 'Credited' ||
                      damage_status === 'Initial_Review' ||
                      damage_status === 'Pre_Credit' ||
                      !perms?.includes(DAMAGE?.CHANGE_DAMAGE_STATUS)
                        ? 'default'
                        : 'pointer',
                  }}
                >
                  {damage_status}
                </Box>
              ) : null;
            }}
            yards_location={(data) => {
              const yard_location = data?.vehicles?.yards_location?.name;
              return yard_location ? (
                <Box
                  sx={{
                    backgroundColor: '#4caf50',
                    color: '#fff',
                    padding: '5px',
                    borderRadius: '4px',
                    margin: '0px',
                    textAlign: 'center',
                  }}
                >
                  {yard_location}
                </Box>
              ) : null;
            }}
            damage_type={(data) => {
              const damage_type = `${data?.damage_type}`;
              const damageOption = damage_type_options.find(
                (item) => item.id === damage_type,
              );
              let color = damageOption ? damageOption.color : '';
              return damage_type && damage_type !== 'null' ? (
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <Box
                    onClick={() => {
                      // Only allow editing if not in restricted statuses
                      if (
                        data?.damage_status !== 'Credited' &&
                        data?.damage_status !== 'Initial_Review' &&
                        data?.damage_status !== 'Pre_Credit'
                      ) {
                        setUpdateDamageTypeItem(data);
                        setField('damage_type');
                        setValue(damage_type_options);
                      }
                    }}
                    sx={{
                      backgroundColor: color,
                      color: '#fff',
                      padding: '5px',
                      borderRadius: '4px',
                      margin: '0px',
                      minWidth: 'unset',
                      textAlign: 'center',
                      cursor:
                        data?.damage_status === 'Credited' ||
                        data?.damage_status === 'Initial_Review' ||
                        data?.damage_status === 'Pre_Credit'
                          ? 'default'
                          : 'pointer',
                    }}
                  >
                    {damage_type}
                  </Box>
                </Box>
              ) : (
                <AddCircleRounded
                  sx={{
                    fontSize: '18px',
                    color: '#d70505',
                    margin: '0px auto',
                    textAlign: 'center',
                    display: 'block',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('damage_type');
                      setValue(damage_type_options);
                    }
                  }}
                />
              );
            }}
            damage_happened_at={(data) => {
              const combination_type = `${data?.vehicles?.containers?.load_combination_type}`;
              const damage_happened_at = `${data?.damage_happened_at}` || [];

              return damage_happened_at.length > 0 ? (
                <Box
                  sx={{
                    textAlign: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setUpdateDamageTypeItem(data);
                    setField('damage_happened_at');
                    setValue(damage_happened_at_options);
                  }}
                >
                  {data?.damage_happened_at.map((happened_at, index) => {
                    const damageOption = damage_happened_at_options.find(
                      (item) => item.id === happened_at.happened_at,
                    );
                    let color = damageOption ? damageOption.color : '#6A5ACD';

                    return (
                      <Box
                        component="span"
                        sx={{
                          fontSize: '10px',
                          backgroundColor: color,
                          color: 'white',
                          padding: '3px',
                          borderRadius: '5px',
                          margin: '3px',
                        }}
                        key={index}
                      >
                        {combination_type?.[0].toUpperCase()}_
                        {happened_at.happened_at}
                      </Box>
                    );
                  })}
                </Box>
              ) : (
                <AddCircleRounded
                  onClick={() => {
                    setUpdateDamageTypeItem(data);
                    setField('damage_happened_at');
                    setValue(damage_happened_at_options);
                  }}
                  sx={{
                    color: '#d70505',
                    padding: '5px',
                    margin: '0px auto',
                    fontSize: '28px',
                    display: 'block',
                    cursor: 'pointer',
                  }}
                />
              );
            }}
            inspection_photo={(data) => {
              const handleOpenInspectionPhotoModal = () => {
                setCurrentInspectionDamage(data);
                setShowInspectionPhotoModal(true);
              };

              return (
                <>
                  <div onClick={(e) => e.stopPropagation()}>
                    <Tooltip title="Click to view/upload inspection photos">
                      <IconButton
                        onClick={handleOpenInspectionPhotoModal}
                        color={data?.inspection_photo ? 'primary' : 'default'}
                        size="small"
                      >
                        <ImageIcon />
                      </IconButton>
                    </Tooltip>
                  </div>
                </>
              );
            }}
            // photo_link={(data) => {
            //   const photo_link = `${data?.vehicles?.photo_link}`;
            //   const hasWarehouseImages =
            //     data?.vehicles?.vehicle_images &&
            //     Array.isArray(data.vehicles.vehicle_images) &&
            //     data.vehicles.vehicle_images.some(
            //       (img) => img.type === 'warehouse',
            //     );

            //   return (
            //     <Box
            //       sx={{
            //         display: 'flex',
            //         justifyContent: 'center',
            //         alignItems: 'center',
            //         cursor: 'pointer',
            //       }}
            //       onClick={() => {
            //         setSelectedVehicle(data.vehicles);
            //         setShowWarehouseImagesModal(true);
            //       }}
            //     >
            //       {photo_link === 'null' && !hasWarehouseImages ? (
            //         <ImageNotSupportedIcon
            //           sx={{
            //             color: '#d70505',
            //           }}
            //         />
            //       ) : (
            //         <ImageIcon
            //           sx={{
            //             color: '#3f50b5',
            //           }}
            //         />
            //       )}
            //     </Box>
            //   );
            // }}
            photo_link={(data) => {
              const vehicles = data?.vehicles;
              const hasPhotoLink = !!vehicles?.photo_link;
              const hasPhotoimages =
                (vehicles?._count?.vehicle_images ?? 0) > 0;

              return (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={async () => {
                    setSelectedVehicle(vehicles);
                    setShowWarehouseImagesModal(true);
                  }}
                >
                  {hasPhotoLink || hasPhotoimages ? (
                    <ImageIcon
                      sx={{
                        color: '#3f50b5',
                      }}
                    />
                  ) : (
                    <ImageNotSupportedIcon
                      sx={{
                        color: '#d70505',
                      }}
                    />
                  )}
                </Box>
              );
            }}
            photo_link_loading={(data) => {
              const photo_link = `${data?.vehicles?.containers?.photo_link}`;
              return (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {photo_link === 'null' ? (
                    <ImageNotSupportedIcon
                      sx={{
                        color: '#d70505 ',
                      }}
                    />
                  ) : (
                    <a
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        color: '#3f50b5 ',
                      }}
                      href={photo_link}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ImageIcon />
                    </a>
                  )}
                </Box>
              );
            }}
            auction_photos_link={(data) => {
              const vehicles = data?.vehicles;
              const hasAuctionLink = !!vehicles?.auction_photos_link;
              const hasAuctionImages =
                (vehicles?._count?.vehicle_auction_images ?? 0) > 0;

              return (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={async () => {
                    setSelectedVehicle(vehicles);
                    setShowAuctionImagesModal(true);
                  }}
                >
                  {hasAuctionLink || hasAuctionImages ? (
                    <ImageIcon
                      sx={{
                        color: '#3f50b5',
                      }}
                    />
                  ) : (
                    <ImageNotSupportedIcon
                      sx={{
                        color: '#d70505',
                      }}
                    />
                  )}
                </Box>
              );
            }}
            confirmed={(data) => {
              const confirmedAndCurrency = `${data?.confirmed} ${data?.confirmed_currency ?? ''}`;
              const confirmed = confirmedAndCurrency.split(' ')[0];
              const currency = confirmedAndCurrency.split(' ')[1];

              return confirmedAndCurrency && confirmedAndCurrency !== null ? (
                <Box
                  sx={{
                    minWidth: '70px',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('confirmed');
                      setValue(currency);
                      setNumValue(Number(confirmed));
                    }
                  }}
                >
                  {confirmedAndCurrency}
                </Box>
              ) : (
                <Box
                  sx={{
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('confirmed');
                      setValue(currency);
                      setNumValue(Number(confirmed));
                    }
                  }}
                ></Box>
              );
            }}
            claim={(data) => {
              const claimandCurrency = `${data?.claim} ${data?.claim_currency ?? ''}`;
              const claim = claimandCurrency.split(' ')[0];
              const currency = claimandCurrency.split(' ')[1];
              return claimandCurrency && claimandCurrency !== null ? (
                <Box
                  sx={{
                    minWidth: '70px',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('claim');
                      setValue(currency);
                      setNumValue(Number(claim));
                    }
                  }}
                >
                  {claimandCurrency}
                </Box>
              ) : (
                <Box
                  sx={{
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('claim');
                      setValue(currency);
                      setNumValue(Number(claim));
                    }
                  }}
                ></Box>
              );
            }}
            credit={(data) => {
              const creditAndCurrency = `${data?.credit} ${data?.credit_currency ?? ''}`;
              const credit = creditAndCurrency.split(' ')[0];
              const currency = creditAndCurrency.split(' ')[1];
              return creditAndCurrency && creditAndCurrency !== null ? (
                <Box
                  sx={{
                    minWidth: '70px',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('credit');
                      setValue(currency);
                      setNumValue(Number(credit));
                    }
                  }}
                >
                  {creditAndCurrency}
                </Box>
              ) : (
                <Box
                  sx={{
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('credit');
                      setValue(currency);
                      setNumValue(Number(credit));
                    }
                  }}
                ></Box>
              );
            }}
            lc_credit={(data) => {
              const lc_creditAndCurrency = `${data?.lc_credit} ${data?.lc_credit_currency ?? ''}`;
              const lc_credit = lc_creditAndCurrency.split(' ')[0];
              const currency = lc_creditAndCurrency.split(' ')[1];
              return lc_credit && lc_credit !== null ? (
                <Box
                  sx={{
                    minWidth: '70px',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('lc_credit');
                      setValue(currency);
                      setNumValue(Number(lc_credit));
                    }
                  }}
                >
                  {lc_creditAndCurrency}
                </Box>
              ) : (
                <Box
                  sx={{
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('lc_credit');
                      setValue(currency);
                      setNumValue(Number(lc_credit));
                    }
                  }}
                ></Box>
              );
            }}
            credited_at={(data) => {
              return data?.credited_at ? formatDate(data?.credited_at) : '';
            }}
            credited_by={(data) => {
              return data?.credited_by || '';
            }}
            description={({ vehicles }) => {
              const bg = {};

              return (
                <Box
                  sx={{
                    ...bg,
                    minWidth: '200px',
                    // maxWidth: '220px',
                  }}
                >
                  {`${vehicles?.year ?? ''} ${vehicles?.make ?? ''} ${vehicles?.model ?? ''} ${
                    vehicles?.color ?? ''
                  }`}
                </Box>
              );
            }}
            id={(item) =>
              copyORViewFun({
                getSingleRow,
                copy: item?.id,
                display: item?.id,
                id: item,
              })
            }
            vin={({ vehicles }) => {
              return (
                vehicles?.vin &&
                buyerNumberColoring(vehicles?.vin, vehicles?.buyer_number)
              );
            }}
            lot_number={({ vehicles }) => {
              return vehicles?.lot_number && vehicles?.lot_number;
            }}
            price={({ vehicles }) => {
              return vehicles?.price ? (
                <Box
                  component="p"
                  style={{
                    color: 'white',
                    backgroundColor: handelColor(vehicles?.price),
                    fontSize: '10px',
                    padding: '3px',
                    borderRadius: '5px',
                    margin: '5px',
                    textAlign: 'center',
                  }}
                >
                  {vehicles?.price}
                </Box>
              ) : (
                ''
              );
            }}
            company={({ vehicles }) => {
              const customers = `${vehicles?.customers?.companies?.name}`;
              return customers && customers !== null ? (
                <Box
                  sx={{
                    width: 150,
                    whiteSpace: 'wrap',
                    backgroundColor: '#0288d1',
                    px: '3px',
                    py: '4px',
                    borderRadius: '4px',
                    fontSize: '11px',
                    color: 'white',
                  }}
                >
                  {customers}
                </Box>
              ) : (
                ''
              );
            }}
            container_number={({ vehicles }) =>
              vehicles?.containers?.container_number
                ? vehicles?.containers?.container_number
                : ''
            }
            area={(data) => {
              const area = `${data?.area}`;
              const areaOption = damage_type_options.find(
                (item) => item.id === area,
              );
              const areaBG = areaOption ? areaOption.color : '';
              const label = areaOption ? areaOption.label : '';
              return area && area !== 'null' ? (
                <Box
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('area');
                      setValue(damage_type_options);
                    }
                  }}
                  sx={{
                    width: 'auto',
                    color: '#fff',
                    backgroundColor: areaBG,
                    padding: '5px 5px',
                    borderRadius: '4px',
                    textAlign: 'center',
                    margin: '0px auto',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                >
                  {label}
                </Box>
              ) : (
                <AddCircleRounded
                  onClick={() => {
                    // Only allow editing if not in restricted statuses
                    if (
                      data?.damage_status !== 'Credited' &&
                      data?.damage_status !== 'Initial_Review' &&
                      data?.damage_status !== 'Pre_Credit'
                    ) {
                      setUpdateDamageTypeItem(data);
                      setField('area');
                      setValue(damage_type_options);
                    }
                  }}
                  sx={{
                    fontSize: '18px',
                    color: '#d70505',
                    margin: '0px auto',
                    textAlign: 'center',
                    display: 'block',
                    cursor:
                      data?.damage_status === 'Credited' ||
                      data?.damage_status === 'Initial_Review' ||
                      data?.damage_status === 'Pre_Credit'
                        ? 'default'
                        : 'pointer',
                  }}
                />
              );
            }}
          />
        )}
      </Container>

      <CreateDamage
        setSelectedItems={setSelectedItems}
        show={showCreate}
        setShow={setShowCreate}
        selectedItems={selectedItems[0]}
        isUpdate={isUpdate}
        recordManager={(data, type) => {
          recordManager({
            data,
            type,
            setTableRecords,
            tableRecords,
            selectedItems,
            setSelectedItems,
            setTotalItems,
            totalItems,
            apiUrl,
          });
        }}
      />

      {/*  UpdateDamageField component use for updating all damage filed   */}
      <UpdateDamageField
        open={updateDamageTypeItem !== null}
        setOpen={(open) => {
          if (!open) setUpdateDamageTypeItem(null);
        }}
        apiUrl={apiUrl}
        item={updateDamageTypeItem}
        field={field}
        value={value} // for dropDown
        numValue={numValue} // for number
        setIsUpdate={setIsUpdate}
        setNumValue={setNumValue}
        recordManager={handleRecordManager} // Use the modified version
        permissions={perms}
      />

      <PreCreditModal
        open={showPreCreditModal}
        setOpen={(open) => {
          setShowPreCreditModal(open);
          if (!open) {
            setSelectedDamageItem(null);
          }
        }}
        damageItems={
          Array.isArray(selectedDamageItem)
            ? selectedDamageItem
            : selectedDamageItem
              ? [selectedDamageItem]
              : []
        }
        mode={modalMode}
        onSuccess={() => {
          fetchRecords();
          setSelectedItems([]);
        }}
      />

      <ViewSingleDamage data={viewData} setView={setView} show={view} />

      <FilterModal2
        open={openFilter}
        toggleOpen={() => setOpenFilter((d) => !d)}
        options={options}
        setOptions={setOptions}
        title="Filter Damages"
        content={filterContentDamages}
      />
      <ColumnDialog
        selectedSetting={selectedSetting}
        setSelectedSetting={setSelectedSetting}
        pageName={pageName}
        selectedHeaders={selectedHeaders}
        setSelectedHeaders={setSelectedHeaders}
        setShowColumnDialog={setShowColumnDialog}
        showColumnDialog={showColumnDialog}
        defaultHeaders={defaultHeaders}
        SearchBy={SearchBy(DamageSearchFields, 'vehicle_damages')}
      ></ColumnDialog>

      <DamagePdfModal
        showDownload={showDownload}
        title={'Damages Reports'}
        selectedHeaders={selectedHeaders}
        setShowDownload={setShowDownload}
        tableRecords={tableRecords}
        options={options}
        apiUrl={apiUrl}
        totalItems={totalItems}
        selectedItems={selectedItems}
      />

      {showImagesModal && selectedVehicle && (
        <VehicleImagesModal
          open={showImagesModal}
          setOpen={setShowImagesModal}
          vehicleData={selectedVehicle}
        />
      )}

      {showAuctionImagesModal && selectedVehicle && (
        <VehicleImagesModal
          open={showAuctionImagesModal}
          setOpen={setShowAuctionImagesModal}
          vehicleData={selectedVehicle}
          imageType="auction"
        />
      )}

      {showWarehouseImagesModal && selectedVehicle && (
        <VehicleImagesModal
          open={showWarehouseImagesModal}
          setOpen={setShowWarehouseImagesModal}
          vehicleData={selectedVehicle}
          imageType="warehouse"
        />
      )}

      <DamageProcessModal
        open={showBulkEmailModal}
        onClose={() => {
          setShowBulkEmailModal(false);
        }}
        selectedItems={selectedItems}
        onSuccess={() => {
          fetchRecords();
        }}
        profile={profile}
      />

      {/* Inspection Photo Modal */}
      {showInspectionPhotoModal && currentInspectionDamage && (
        <InspectionPhotoModal
          open={showInspectionPhotoModal}
          setOpen={setShowInspectionPhotoModal}
          damageData={currentInspectionDamage}
          onPhotoUpdate={handleInspectionPhotoUpdate}
        />
      )}

      {/* Custom Audit Review Confirmation Dialog with Stylish Table */}
      {confirmDialog.damageData ? (
        <Dialog
          open={confirmDialog.open}
          onClose={() => setConfirmDialog({ ...confirmDialog, open: false })}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: '12px',
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            },
          }}
        >
          <DialogTitle
            sx={{
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              color: 'white',
              textAlign: 'center',
              fontSize: '1.25rem',
              fontWeight: 'bold',
              py: 3,
            }}
          >
            {confirmDialog.title}
          </DialogTitle>

          <DialogContent sx={{ p: 0 }}>
            <TableContainer
              component={Paper}
              sx={{
                mt: 2,
                bgcolor: 'white',
                maxHeight: '400px',
                overflow: 'auto',
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell align="center">Case Number</TableCell>
                    <TableCell align="center">Damage Type</TableCell>
                    <TableCell align="center">Damage Details</TableCell>
                    <TableCell align="center">POL</TableCell>
                    <TableCell align="center">Description</TableCell>
                    <TableCell align="center">VIN#</TableCell>
                    <TableCell align="center">LOT#</TableCell>
                    <TableCell align="center">Container#</TableCell>
                    <TableCell align="center">Credit Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(Array.isArray(confirmDialog.damageData)
                    ? confirmDialog.damageData
                    : [confirmDialog.damageData]
                  ).map((damage, index) => {
                    // Helper function to render damage_details values (matching InitialReviewDamages)
                    const renderDamageDetails = (damage) => {
                      if (
                        !damage.damage_details ||
                        !Array.isArray(damage.damage_details) ||
                        damage.damage_details.length === 0
                      ) {
                        return 'N/A';
                      }

                      return (
                        <Box
                          sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}
                        >
                          {damage.damage_details.map((detail, index) => (
                            <Chip
                              key={index}
                              label={detail.detail}
                              size="small"
                              sx={{
                                backgroundColor: '#3f50b5',
                                color: 'white',
                                fontSize: '10px',
                                fontWeight: 'bold',
                              }}
                            />
                          ))}
                        </Box>
                      );
                    };

                    // Helper function to render damage type with color (matching InitialReviewDamages)
                    const renderDamageType = (damage) => {
                      if (!damage.damage_type) return 'N/A';

                      const damageOption = damage_type_options.find(
                        (item) => item.id === damage.damage_type,
                      );
                      const color = damageOption
                        ? damageOption.color
                        : '#3f50b5';

                      return (
                        <Chip
                          label={damage.damage_type}
                          size="small"
                          sx={{
                            backgroundColor: color,
                            color: 'white',
                            fontSize: '10px',
                            fontWeight: 'bold',
                          }}
                        />
                      );
                    };

                    // Helper function to render POL with color (matching InitialReviewDamages)
                    const renderPOL = (vehicle) => {
                      if (!vehicle?.pol_locations?.name) return 'N/A';

                      try {
                        const pol = vehicle.pol_locations.name
                          .split(',')[1]
                          .trim();
                        const damageOption = pol_options.find(
                          (item) => item.id === pol,
                        );
                        const color = damageOption
                          ? damageOption.color
                          : '#fad521';

                        return (
                          <Chip
                            label={pol}
                            size="small"
                            sx={{
                              backgroundColor: color,
                              color: 'white',
                              fontSize: '10px',
                              fontWeight: 'bold',
                            }}
                          />
                        );
                      } catch (error) {
                        return vehicle.pol_locations.name || 'N/A';
                      }
                    };

                    return (
                      <TableRow
                        key={damage.id}
                        hover
                        sx={{
                          cursor: 'pointer',
                          // Add light green background for approved/audit reviewed items
                          backgroundColor:
                            damage.audit_reviewed_at && damage.audit_reviewed_by
                              ? '#e8f5e8'
                              : index % 2 === 0
                                ? 'white'
                                : 'rgba(0, 0, 0, 0.03)',
                          '&:hover': {
                            backgroundColor:
                              damage.audit_reviewed_at &&
                              damage.audit_reviewed_by
                                ? '#d4f4d4'
                                : '#f0f7ff',
                          },
                        }}
                      >
                        <TableCell align="center">
                          {damage.case_number
                            ? `PGLDMG0${damage.case_number}`
                            : `ID: ${damage.id}`}
                        </TableCell>

                        <TableCell align="center">
                          {renderDamageType(damage)}
                        </TableCell>

                        <TableCell align="center">
                          {renderDamageDetails(damage)}
                        </TableCell>

                        <TableCell align="center">
                          {renderPOL(damage.vehicles)}
                        </TableCell>

                        <TableCell align="center">
                          {`${damage.vehicles?.year || ''} ${damage.vehicles?.make || ''} ${damage.vehicles?.model || ''} ${damage.vehicles?.color || ''}`}
                        </TableCell>

                        <TableCell align="center" sx={{ fontWeight: 'medium' }}>
                          {damage.vehicles?.vin || 'N/A'}
                        </TableCell>

                        <TableCell align="center">
                          {damage.vehicles?.lot_number || 'N/A'}
                        </TableCell>

                        <TableCell align="center">
                          {damage.vehicles?.containers?.container_number ||
                            'N/A'}
                        </TableCell>

                        <TableCell align="center">
                          {damage.credit
                            ? `${damage.credit} ${damage.credit_currency || ''}`
                            : 'N/A'}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </DialogContent>

          <DialogActions
            sx={{
              p: 3,
              backgroundColor: '#f8f9fa',
              borderTop: '1px solid #e0e0e0',
              gap: 2,
            }}
          >
            <Button
              onClick={() =>
                setConfirmDialog({ ...confirmDialog, open: false })
              }
              variant="outlined"
              sx={{
                borderColor: '#d32f2f',
                color: '#d32f2f',
                fontWeight: 'bold',
                px: 3,
                '&:hover': {
                  backgroundColor: '#ffebee',
                  borderColor: '#d32f2f',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDialog.onConfirm}
              variant="contained"
              sx={{
                backgroundColor: '#2e7d32',
                fontWeight: 'bold',
                px: 3,
                '&:hover': {
                  backgroundColor: '#1b5e20',
                },
              }}
            >
              ✅ Approve Damages
            </Button>
          </DialogActions>
        </Dialog>
      ) : (
        // Standard dialog for other confirmations
        <AppConfirmDialog
          open={confirmDialog.open}
          onDeny={() => setConfirmDialog({ ...confirmDialog, open: false })}
          onConfirm={confirmDialog.onConfirm}
          dialogTitle={confirmDialog.title}
          title={confirmDialog.message}
          maxWidth="sm"
        />
      )}

      {/* Damage Status Modal */}
      <DamageStatusModal
        open={showDamageStatusModal}
        onClose={() => setShowDamageStatusModal(false)}
        selectedItems={selectedItems}
        onSuccess={() => {
          fetchRecords();
          setSelectedItems([]);
        }}
      />
    </>
  );
};

export default NewDamage;
