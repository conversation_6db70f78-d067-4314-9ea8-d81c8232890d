import DamageUpdateModal from '@/Modules/vehicle_damages/damageComponents/damageUpdateModal';

import { useEffect, useState } from 'react';
import axios from '@/lib/axios';

import { toast } from 'react-toastify';
import {
  claim_status_options,
  currency_options,
  damage_detail_options,
  damage_happened_at_options,
  damage_status_options,
  damage_type_options,
} from '@/configs/vehicles_damage/damageHeader';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';

const UpdateDamageField = ({
  open,
  setOpen,
  apiUrl,
  item,
  field,
  value,
  numValue,
  setIsUpdate,
  setNumValue,
  recordManager,
  permissions = [], // Add permissions prop
}) => {
  // const [itemvalue, setItemValue] = useState<any[]>([]);
  const [itemvalue, setItemValue] = useState<
    string | number | { detail: string }[] | { happened_at: string }[] | File
  >([]);
  // ... existing code ...
  const [enumOptions, setEnumOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalType, setModalType] = useState<
    | 'dropDown'
    | 'text'
    | 'textDropDown'
    | 'multiSelect_damage_details'
    | 'multiSelect_happened_at'
    | 'imageUpload'
  >('dropDown');

  // Upload file to server
  const uploadFile = async (file: File, damageId: number) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('id', damageId.toString());
      formData.append('type', 'inspection');

      const { data } = await axios.post(
        'vehicle_damages/upload-image',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      return data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  };

  // global method for all field
  const update = async () => {
    // Check permissions for status changes
    if (
      field === 'damage_status' &&
      !permissions?.includes(DAMAGE?.CHANGE_DAMAGE_STATUS)
    ) {
      toast.error('You do not have permission to change damage status');
      return;
    }

    // Check if trying to change status to restricted statuses when damage happened at auction
    if (field === 'damage_status') {
      const restrictedStatuses = ['In_process', 'Pending_CA', 'Forgotten'];

      if (restrictedStatuses.includes(itemvalue as string)) {
        // Check if damage happened at auction
        const hasAuctionDamage = item.damage_happened_at?.some(
          (happenedAt) =>
            happenedAt.happened_at === 'Auction' || happenedAt === 'Auction',
        );

        if (hasAuctionDamage) {
          const vin = item.vehicles?.vin || `ID:${item.id}`;
          toast.error(
            `Cannot change status to ${String(itemvalue).replace('_', ' ')} for damage that happened at Auction (VIN: ${vin})`,
          );
          return;
        }
      }
    }

    setLoading(true);

    try {
      let updatedData = null;

      // Handle file upload for inspection_photo
      if (field === 'inspection_photo' && itemvalue instanceof File) {
        try {
          const uploadResponse = await uploadFile(itemvalue, item.id);

          if (uploadResponse && uploadResponse.data) {
            const imageUrl = uploadResponse.data.url || uploadResponse.data;

            // Update the damage record with the new image URL
            const { data } = await axios.patch(
              `${apiUrl}/vehicle_damage_field/${item?.id}`,
              {
                field: 'inspection_photo',
                value: imageUrl,
              },
            );

            if (data && data.data) {
              updatedData = {
                ...item,
                ...data.data,
                inspection_photo: imageUrl,
              };

              recordManager(updatedData, 'update');
              setIsUpdate(true);
              toast.success('Image uploaded successfully!');
            }
          } else {
            toast.error('Failed to upload image.');
          }
        } catch (uploadError) {
          console.error('Upload error:', uploadError);
          toast.error('Error uploading image. Please try again.');
        }
      }
      // Special handling for damage_status change to Credited
      else if (field === 'damage_status' && value === 'Credited') {
        const { data } = await axios.patch(
          `${apiUrl}/vehicle_damage_field/${item?.id}`,
          {
            field: field,
            value: value,
          },
        );

        if (data && data.data) {
          // Use the response data to ensure we have the latest state
          updatedData = {
            ...item,
            ...data.data,
            damage_status: value,
            credited_at: new Date(),
            claim_status: 'Credited',
          };

          recordManager(updatedData, 'update');
          setIsUpdate(true);
          toast.success('Record updated successfully!');
        } else {
          toast.error('Update Status failed.');
        }
      } else {
        // Existing update logic for other fields
        let updatedItemValue;
        if (['claim', 'credit', 'confirmed', 'lc_credit'].includes(field)) {
          updatedItemValue = [numValue, itemvalue];
        } else if (field === 'damage_details') {
          // Convert selected damage details to array of strings
          updatedItemValue = Array.isArray(itemvalue)
            ? itemvalue.map((item) => item.detail || item)
            : [itemvalue];
        } else if (field === 'damage_happened_at') {
          updatedItemValue = Array.isArray(itemvalue)
            ? itemvalue.map((item) => item.happened_at || item)
            : [itemvalue];
        } else if (
          field === 'inspection_photo' &&
          typeof itemvalue === 'string'
        ) {
          // Handle case when user enters URL directly in text field (old behavior)
          updatedItemValue = itemvalue;
        } else {
          updatedItemValue = itemvalue;
        }

        const { data } = await axios.patch(
          `${apiUrl}/vehicle_damage_field/${item?.id}`,
          {
            field: field,
            value: updatedItemValue,
          },
        );

        if (data && data.data) {
          // Use the response data to ensure we have the latest state
          updatedData = {
            ...item,
            ...data.data, // This ensures we get the latest data from the server
          };

          // For damage_details, ensure the format is correct
          if (field === 'damage_details') {
            updatedData.damage_details = Array.isArray(data.data.damage_details)
              ? data.data.damage_details
              : updatedItemValue.map((detail) => ({ detail }));
          }

          recordManager(updatedData, 'update');
          setIsUpdate(true);
          toast.success('Record updated successfully!');
        } else {
          toast.error('Update Status failed.');
        }
      }
    } catch (error) {
      console.error('Update error:', error);
      toast.error('An error occurred while updating.');
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  useEffect(() => {
    if (item && field) {
      // Check if the component is still mounted item[field] = id[damage_type]

      setItemValue(item[field]);

      switch (field) {
        case 'damage_type':
          setEnumOptions(damage_type_options);
          setModalType('dropDown');

          break;
        case 'damage_details':
          setEnumOptions(damage_detail_options);
          setModalType('multiSelect_damage_details');

          break;
        case 'damage_happened_at':
          setEnumOptions(damage_happened_at_options);
          setModalType('multiSelect_happened_at');

          break;
        case 'damage_status':
          setEnumOptions(damage_status_options);
          setModalType('dropDown');
          break;

        case 'claim_status':
          setEnumOptions(claim_status_options);
          setModalType('dropDown');

          break;

        case 'inspection_photo':
          setModalType('imageUpload');
          break;

        case 'remark':
          setModalType('text');
          break;

        case 'claim':
          setItemValue(value);
          setEnumOptions(currency_options);
          setModalType('textDropDown');

          break;

        case 'confirmed':
          setEnumOptions(currency_options);
          setItemValue(value);

          setModalType('textDropDown');

          break;

        case 'credit':
          setEnumOptions(currency_options);
          setItemValue(value);

          setModalType('textDropDown');

          break;

        case 'lc_credit':
          setEnumOptions(currency_options);
          setItemValue(value);

          setModalType('textDropDown');

          break;

        default:
          console.error('Invalid field');
        // Handle the case where the field is not recognized
      }
    }
  }, [item, field]);

  return (
    <DamageUpdateModal
      open={open}
      setOpen={setOpen}
      title={`add or update ${field}`}
      enumOptions={enumOptions}
      numValue={numValue}
      value={value}
      itemvalue={itemvalue}
      setItemValue={setItemValue}
      onSubmit={update}
      loading={loading}
      modalType={modalType}
      setNumValue={setNumValue}
      field={field}
      permissions={permissions}
    />
  );
};

export default UpdateDamageField;
