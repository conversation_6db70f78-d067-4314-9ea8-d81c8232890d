import React, { useState, useEffect } from 'react';
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Grid,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Close as CloseIcon,
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  DriveFileMove as DriveFileMoveIcon,
  Visibility as VisibilityIcon,
  ContentCopy as ContentCopyIcon,
} from '@mui/icons-material';
import ImageSlideDialog from '@/components/mainComponents/cModal/ImageSlideDialog';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import { useDropzone } from 'react-dropzone';

// Helper function to check if a URL is a Google Drive link
const isGoogleDriveLink = (url) => {
  return (
    url &&
    (url.includes('drive.google.com') ||
      url.includes('docs.google.com') ||
      url.includes('sheets.google.com') ||
      url.includes('slides.google.com'))
  );
};

// Helper function to convert Google Drive link to an embeddable format
const getEmbedUrl = (driveUrl) => {
  if (!driveUrl) return '';

  try {
    // Simple approach - just modify the URL based on basic patterns
    if (driveUrl.includes('drive.google.com/file/d/')) {
      // Format: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
      const fileId = driveUrl
        .split('drive.google.com/file/d/')[1]
        .split('/')[0];
      return `https://drive.google.com/file/d/${fileId}/preview`;
    }

    if (driveUrl.includes('drive.google.com/open?id=')) {
      // Format: https://drive.google.com/open?id=FILE_ID
      const fileId = driveUrl.split('open?id=')[1].split('&')[0];
      return `https://drive.google.com/file/d/${fileId}/preview`;
    }

    if (driveUrl.includes('docs.google.com/document/d/')) {
      const parts = driveUrl.split('docs.google.com/document/d/');
      if (parts.length > 1) {
        const docId = parts[1].split('/')[0];
        return `https://docs.google.com/document/d/${docId}/preview`;
      }
    }

    if (driveUrl.includes('docs.google.com/spreadsheets/d/')) {
      const parts = driveUrl.split('docs.google.com/spreadsheets/d/');
      if (parts.length > 1) {
        const sheetId = parts[1].split('/')[0];
        return `https://docs.google.com/spreadsheets/d/${sheetId}/preview`;
      }
    }

    if (driveUrl.includes('docs.google.com/presentation/d/')) {
      const parts = driveUrl.split('docs.google.com/presentation/d/');
      if (parts.length > 1) {
        const presentationId = parts[1].split('/')[0];
        return `https://docs.google.com/presentation/d/${presentationId}/preview`;
      }
    }

    // If no conversion could be done, return original URL
    return driveUrl;
  } catch (error) {
    return driveUrl; // Return original URL on error
  }
};

// Function to determine if a URL is a Google Drive folder
// const isGoogleDriveFolder = (url) => {
//   return url && url.includes('drive.google.com/drive/folders/');
// };

// Define TypeScript interfaces
interface UploadProgressItem {
  name: string;
  preview: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

// Define the global container interface
interface GlobalUploadContainer {
  instance: HTMLDivElement | null;
  styleElement: HTMLStyleElement | null;
  isUploading: boolean;
}

// Create proper interface for the UploadProgressWrapper props
interface UploadProgressWrapperProps {
  uploadProgress: Record<string, UploadProgressItem>;
  showProgress: boolean;
  onClose: () => void;
}

// Create a global variable for the upload container so it stays alive
// even when the React component unmounts
const globalUploadContainer: GlobalUploadContainer = {
  instance: null,
  styleElement: null,
  isUploading: false,
};

// Create global variable for each upload's completion status and timers
interface UploadStatus {
  [key: string]: { completionTimer?: NodeJS.Timeout; isComplete: boolean };
}

const globalUploadStatus: UploadStatus = {};

// Completely revamped createProgressContainer with standalone DOM management
const createProgressContainer = (
  initialFiles: Record<string, UploadProgressItem>,
) => {
  // Remove any existing container to avoid duplicates
  if (globalUploadContainer.instance) {
    return globalUploadContainer.instance;
  }

  // Create a style element for animations
  const styleEl = document.createElement('style');
  styleEl.id = 'progress-styles';
  styleEl.textContent = `
    @keyframes progress-pulse {
      0% { opacity: 0.8; }
      50% { opacity: 1; }
      100% { opacity: 0.8; }
    }

    @keyframes progress-shine {
      0% {
        background-position: -200px;
      }
      100% {
        background-position: 600px;
      }
    }

    .upload-progress-bar {
      transition: width 0.5s ease-in-out !important;
      background-image: linear-gradient(
        90deg,
        rgba(255,255,255,0) 0%,
        rgba(255,255,255,0.5) 50%,
        rgba(255,255,255,0) 100%
      );
      background-size: 600px 100%;
      background-repeat: no-repeat;
      animation: progress-shine 3s infinite linear;
      height: 10px !important;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1) inset;
    }

    /* Additional style to force width transitions to work */
    .upload-progress-bar-force {
      transition: width 0.5s ease-in-out !important;
      will-change: width;
    }

    /* Animation for fade out */
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }

    .fade-out {
      animation: fadeOut 0.5s ease-in-out forwards;
    }
  `;
  document.head.appendChild(styleEl);
  globalUploadContainer.styleElement = styleEl;

  // Create a new container
  const container = document.createElement('div');
  container.id = 'upload-progress-container';
  container.style.position = 'fixed';
  container.style.bottom = '24px';
  container.style.right = '24px';
  container.style.zIndex = '9999';
  container.style.backgroundColor = 'white';
  container.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
  container.style.borderRadius = '8px';
  container.style.padding = '16px';
  container.style.minWidth = '320px';
  container.style.maxWidth = '400px';
  container.style.maxHeight = '80vh';
  container.style.overflow = 'auto';

  // Add a title div
  const titleDiv = document.createElement('div');
  titleDiv.style.display = 'flex';
  titleDiv.style.justifyContent = 'space-between';
  titleDiv.style.alignItems = 'center';
  titleDiv.style.marginBottom = '12px';

  const title = document.createElement('div');
  title.textContent = 'Uploading Files';
  title.style.fontWeight = '500';
  title.style.fontSize = '16px';

  const closeBtn = document.createElement('button');
  closeBtn.textContent = '×';
  closeBtn.id = 'close-progress';
  closeBtn.style.border = 'none';
  closeBtn.style.background = 'none';
  closeBtn.style.cursor = 'pointer';
  closeBtn.style.color = '#666';
  closeBtn.style.fontSize = '20px';

  // Add close button event handler that uses a standalone function reference
  closeBtn.addEventListener('click', function handleCloseClick() {
    // Reset the global state
    globalUploadContainer.isUploading = false;

    // Only remove elements if they exist
    if (globalUploadContainer.instance) {
      document.body.removeChild(globalUploadContainer.instance);
      globalUploadContainer.instance = null;
    }

    if (globalUploadContainer.styleElement) {
      document.head.removeChild(globalUploadContainer.styleElement);
      globalUploadContainer.styleElement = null;
    }
  });

  titleDiv.appendChild(title);
  titleDiv.appendChild(closeBtn);

  // Create progress items container
  const progressItems = document.createElement('div');
  progressItems.id = 'progress-items';

  // Add to container
  container.appendChild(titleDiv);
  container.appendChild(progressItems);

  // Store in global variable to keep it alive between renders
  globalUploadContainer.instance = container;
  globalUploadContainer.isUploading = true;

  // Add to body
  document.body.appendChild(container);

  // Pre-create upload items for better performance and immediate visual feedback
  if (initialFiles && Object.keys(initialFiles).length > 0) {
    const itemsContainer = document.getElementById('progress-items');

    if (itemsContainer) {
      // Loop through all initial files
      (Object.entries(initialFiles) as [string, UploadProgressItem][]).forEach(
        ([id, item]) => {
          // Create container for this file's progress
          const itemEl = document.createElement('div');
          itemEl.id = `progress-item-${id}`;
          itemEl.style.marginBottom = '15px';

          // Item container with image and name
          const itemContent = document.createElement('div');
          itemContent.style.display = 'flex';
          itemContent.style.alignItems = 'center';
          itemContent.style.marginBottom = '6px';

          // Image
          const img = document.createElement('img');
          img.src = item.preview;
          img.alt = item.name;
          img.style.width = '36px';
          img.style.height = '36px';
          img.style.objectFit = 'cover';
          img.style.borderRadius = '4px';
          img.style.marginRight = '8px';

          // File name
          const nameDiv = document.createElement('div');
          nameDiv.textContent = item.name;
          nameDiv.style.flexGrow = '1';
          nameDiv.style.overflow = 'hidden';
          nameDiv.style.textOverflow = 'ellipsis';
          nameDiv.style.whiteSpace = 'nowrap';
          nameDiv.style.fontSize = '14px';

          // Add elements to item content
          itemContent.appendChild(img);
          itemContent.appendChild(nameDiv);

          // Add item content to the item element
          itemEl.appendChild(itemContent);

          // Progress bar container
          const progressContainer = document.createElement('div');
          progressContainer.style.height = '14px';
          progressContainer.style.borderRadius = '7px';
          progressContainer.style.backgroundColor = '#eeeeee';
          progressContainer.style.width = '100%';
          progressContainer.style.marginTop = '6px';
          progressContainer.style.overflow = 'hidden';
          progressContainer.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';

          // Create the progress bar element with explicit initial width of 0%
          const progressBar = document.createElement('div');
          progressBar.id = `upload-progress-bar-${id}`;
          progressBar.className =
            'upload-progress-bar upload-progress-bar-force';
          progressBar.style.height = '100%';
          progressBar.style.width = '0%'; // Start at 0%
          progressBar.style.backgroundColor = '#1976d2'; // Progress blue
          progressBar.style.borderRadius = '7px';
          progressBar.style.transition =
            'width 0.5s ease-in-out, background-color 0.5s ease';

          // Add percentage text
          const percentText = document.createElement('div');
          percentText.id = `progress-percent-${id}`;
          percentText.style.fontSize = '12px';
          percentText.style.textAlign = 'right';
          percentText.style.marginTop = '4px';
          percentText.textContent = '0%';
          percentText.style.color = '#666';

          // Append the progress bar and percentage text
          progressContainer.appendChild(progressBar);
          itemEl.appendChild(progressContainer);
          itemEl.appendChild(percentText);

          // Add to the container
          itemsContainer.appendChild(itemEl);
        },
      );
    }
  }

  return container;
};

// Create a global function to update the progress directly on the DOM
const updateGlobalProgress = (
  uploadProgressData: Record<string, UploadProgressItem>,
) => {
  if (!globalUploadContainer.instance || !globalUploadContainer.isUploading) {
    return;
  }

  const progressItems = document.getElementById('progress-items');
  if (!progressItems) {
    return;
  }

  // Don't clear existing items, just update them as needed
  // This prevents DOM thrashing and maintains individual progress elements

  // Calculate completion stats
  const items = Object.values(uploadProgressData) as UploadProgressItem[];
  const completedCount = items.filter(
    (item) => item.status === 'completed' || item.status === 'error',
  ).length;
  const totalCount = items.length;

  // Update title without recreating all DOM elements
  const titleElement = document.querySelector(
    '#upload-progress-container > div:first-child > div:first-child',
  );
  if (titleElement) {
    if (completedCount === totalCount && totalCount > 0) {
      titleElement.textContent = 'Upload Complete ✓';
      (titleElement as HTMLElement).style.color = '#2e7d32';
      (titleElement as HTMLElement).style.fontWeight = 'bold';
    } else {
      titleElement.textContent = `Uploading ${completedCount}/${totalCount} Files`;
    }
  }

  // Update each individual progress item directly
  (
    Object.entries(uploadProgressData) as [string, UploadProgressItem][]
  ).forEach(([id, item]) => {
    // Find existing progress bar and percentage elements
    const progressBarEl = document.getElementById(`upload-progress-bar-${id}`);
    const percentTextEl = document.getElementById(`progress-percent-${id}`);

    if (progressBarEl) {
      // Update existing progress bar
      progressBarEl.style.width = `${item.progress}%`;

      // Set styles based on status
      if (item.status === 'completed') {
        progressBarEl.style.backgroundColor = '#4caf50'; // Success green
        progressBarEl.style.animation = 'none';
        progressBarEl.style.backgroundImage = 'none';
      } else if (item.status === 'error') {
        progressBarEl.style.backgroundColor = '#f44336'; // Error red
        progressBarEl.style.animation = 'none';
        progressBarEl.style.backgroundImage = 'none';
      } else {
        progressBarEl.style.backgroundColor = '#1976d2'; // Progress blue
      }
    }

    if (percentTextEl) {
      // Update percentage text
      if (item.status === 'completed') {
        percentTextEl.textContent = 'Complete';
        percentTextEl.style.color = '#4caf50';
        percentTextEl.style.fontWeight = 'bold';
      } else if (item.status === 'error') {
        percentTextEl.textContent = 'Failed';
        percentTextEl.style.color = '#f44336';
      } else {
        percentTextEl.textContent = `${item.progress}%`;
      }
    }
  });
};

// Replace existing UploadProgressWrapper with this simpler approach
const UploadProgressWrapper: React.FC<UploadProgressWrapperProps> = ({
  uploadProgress,
  showProgress,
  onClose,
}) => {
  // Install the progress indicator directly into the DOM when showProgress becomes true
  useEffect(() => {
    if (!showProgress || Object.keys(uploadProgress).length === 0) {
      return;
    }

    // Update immediately with current progress data
    updateGlobalProgress(uploadProgress);

    // Add event listener to close button
    const closeButton = document.getElementById('close-progress');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        onClose();
      });
    }

    // Clean up function
    return () => {};
  }, [showProgress, uploadProgress, onClose]);

  return null; // This component doesn't render anything in React, it works directly with the DOM
};

// Define interface for component props
interface InspectionPhotoModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  damageData: any;
  onPhotoUpdate?: (photoUrl?: string) => void;
  readonly?: boolean;
}

export default function InspectionPhotoModal({
  open,
  setOpen,
  damageData,
  onPhotoUpdate,
  readonly = false,
}: InspectionPhotoModalProps) {
  const [value, setValue] = useState(readonly ? '2' : '1');
  const [openSlider, setOpenSlider] = useState(false);
  const [imageIndex, setImageIndex] = useState(0);
  const [fullSizeImages, setFullSizeImages] = useState([]);
  const [inspectionImages, setInspectionImages] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [driveLink, setDriveLink] = useState('');
  const [newDriveLink, setNewDriveLink] = useState('');
  const [uploadQueue, setUploadQueue] = useState([]);
  const [loading, setLoading] = useState(false);
  const iframeRef = React.useRef(null);
  const [uploadProgress, setUploadProgress] = useState<
    Record<string, UploadProgressItem>
  >({});
  const [showProgress, setShowProgress] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [imageToDelete, setImageToDelete] = useState(null);
  const [shareableLinks, setShareableLinks] = useState<{
    [key: number]: string;
  }>({});
  const [loadingShareLinks, setLoadingShareLinks] = useState(false);
  const [linkCopied, setLinkCopied] = useState<{ [key: number]: boolean }>({});

  // Ensure cleanup when component unmounts
  useEffect(() => {
    return () => {
      // Progress indicator remains visible until manually closed by the user
    };
  }, []);

  // Fetch inspection photos when the modal opens
  useEffect(() => {
    if (open && damageData?.id) {
      fetchInspectionPhotos();
    }
  }, [open, damageData?.id]);

  // This effect ensures the iframe loads correctly whenever the tab value changes to '2'
  useEffect(() => {
    if (value === '2' && driveLink && iframeRef.current) {
      const iframe = iframeRef.current;
      iframe.src = getEmbedUrl(driveLink);
    }
  }, [value, driveLink]);

  // Fetch all inspection photos
  const fetchInspectionPhotos = async () => {
    if (!damageData?.id) return;

    setLoading(true);
    try {
      const { data } = await axios.get(
        `vehicle_damages/inspection-photos/${damageData.id}`,
      );

      if (data?.result && data?.data) {
        // Check if main photo is a Google Drive link
        if (data.data.mainPhoto && isGoogleDriveLink(data.data.mainPhoto)) {
          setDriveLink(data.data.mainPhoto);
          // Still load images but don't set them as main
          setInspectionImages(data.data.photos || []);
        } else {
          setInspectionImages(data.data.photos || []);
          setDriveLink('');
        }
      } else {
        setInspectionImages([]);
        setDriveLink('');
      }
    } catch (error) {
      toast.error('Failed to load inspection photos');
      setInspectionImages([]);
    } finally {
      setLoading(false);
    }
  };

  // const handleChange = (event: React.SyntheticEvent, newValue: string) => {
  //   setValue(newValue);
  // };

  const handleClose = () => {
    setSelectedImage(null);
    setOpen(false);
  };

  const handleDoubleClick = (index: number) => {
    if (index < 0 || !inspectionImages || index >= inspectionImages.length) {
      return;
    }

    setImageIndex(index);

    // Create properly formatted images for the slider
    const validImages = inspectionImages
      .filter((img) => img && img.url)
      .map((img) => ({
        ...img,
        // Ensure the URL is prepared for the ImageSlideDialog
        url: getImageUrl(img),
        name: img.original_name || `Image-${img.id}`,
      }));

    setFullSizeImages(validImages);
    setOpenSlider(true);
  };

  // Get image URL for production
  const getImageUrl = (image: any): string => {
    if (!image || !image.url) return '';

    try {
      // For inspection photos that are already full URLs
      if (typeof image.url === 'string' && image.url.startsWith('http')) {
        return image.url;
      }

      // For images stored on the server
      const url = `${process.env.NEXT_PUBLIC_MINIO_ENDPOINT || ''}${image.url}`;
      return url;
    } catch (error) {
      return '';
    }
  };

  // Handle file drop for uploads
  const onDrop = (acceptedFiles: File[]) => {
    // Check for oversized files (>6MB)
    const oversizedFiles = acceptedFiles.filter((file) => file.size > 6291456);
    if (oversizedFiles.length > 0) {
      toast.error(
        `${oversizedFiles.length} file(s) exceed the 6MB size limit and will be skipped.`,
      );
    }

    // Filter valid files and add them to the queue
    const validFiles = acceptedFiles
      .filter((file) => file.size <= 6291456)
      .map((file) =>
        Object.assign(file, {
          preview: URL.createObjectURL(file),
          id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        }),
      );

    if (validFiles.length > 0) {
      // Replace existing queue with the new single image instead of appending
      setUploadQueue(validFiles);
    }
  };

  // Drop zone configuration
  const { getRootProps, getInputProps } = useDropzone({
    accept: { 'image/*': [] },
    onDrop,
    multiple: false,
  });

  // Remove a file from the upload queue
  const removeFileFromQueue = (fileId: string) => {
    setUploadQueue((prevQueue) =>
      prevQueue.filter((file) => file.id !== fileId),
    );
  };

  // Handle the upload process with proper progress tracking
  const handleUploadAll = async () => {
    if (uploadQueue.length === 0) return;

    // Set initial progress for all files
    const initialProgress = uploadQueue.reduce(
      (acc, file) => {
        acc[file.id] = {
          name: file.name,
          preview: file.preview,
          progress: 0,
          status: 'uploading',
        };
        return acc;
      },
      {} as Record<string, UploadProgressItem>,
    );

    // First, create the standalone progress container directly on document body
    // This will remain even after the modal is closed
    createProgressContainer(initialProgress);

    // Set progress data in state
    setUploadProgress(initialProgress);
    setShowProgress(true);

    // Wait to make sure the DOM is updated with our progress container
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Start uploads in the background - don't wait for modal to close
    uploadAllFiles();

    // Close modal AFTER uploads have started
    setOpen(false);
  };

  // Create a global function to check if uploads are complete and handle container cleanup
  // WARNING: This uses direct DOM manipulation which could conflict with React 19's concurrent features
  // TODO: Refactor to use React state management instead of direct DOM manipulation
  const checkAllUploadsComplete = () => {
    // Check if all items have been removed from the DOM
    const remainingItems = document.querySelectorAll('#progress-items > div');
    if (remainingItems.length === 0 && globalUploadContainer.instance) {
      // Add fade-out animation
      globalUploadContainer.instance.classList.add('fade-out');

      // Remove container after animation finishes
      setTimeout(() => {
        if (globalUploadContainer.instance) {
          document.body.removeChild(globalUploadContainer.instance);
          globalUploadContainer.instance = null;
        }

        if (globalUploadContainer.styleElement) {
          document.head.removeChild(globalUploadContainer.styleElement);
          globalUploadContainer.styleElement = null;
        }

        globalUploadContainer.isUploading = false;

        // Reset React state too
        setShowProgress(false);
        setUploadProgress({});
      }, 500); // Match duration with fadeOut animation
    } else {
    }
  };

  // Update the uploadAllFiles function to handle individual uploads with separate hiding
  const uploadAllFiles = async () => {
    if (uploadQueue.length === 0) return;

    setUploading(true);
    setUploadError(''); // Reset any previous errors

    try {
      let completedUploads = 0;
      const totalUploads = uploadQueue.length;

      // Process each file
      for (const file of uploadQueue) {
        try {
          // Initialize this file's status
          globalUploadStatus[file.id] = {
            isComplete: false,
            completionTimer: undefined,
          };

          // Set initial progress in state
          setUploadProgress((prev) => {
            const newState = {
              ...prev,
              [file.id]: {
                ...prev[file.id],
                progress: 0,
                status: 'uploading' as const,
                name: file.name,
                preview: file.preview,
              },
            };

            // Update the global DOM display immediately
            updateGlobalProgress(newState);

            return newState;
          });

          // DIRECT DOM UPDATE - Set progress to 0 explicitly
          const progressBarEl = document.getElementById(
            `upload-progress-bar-${file.id}`,
          );
          if (progressBarEl) {
            progressBarEl.style.width = '0%';

            // Also explicitly update the percentage text for initial state
            const percentTextEl = document.getElementById(
              `progress-percent-${file.id}`,
            );
            if (percentTextEl) {
              percentTextEl.textContent = '0%';
            }
          }

          // Create form data for upload
          const formData = new FormData();
          formData.append('file', file);
          formData.append('id', damageData.id.toString());
          formData.append('type', 'inspection');

          try {
            // Make the API request immediately, but don't wait for completion
            const uploadPromise = axios.post(
              'vehicle_damages/upload-image',
              formData,
              { headers: { 'Content-Type': 'multipart/form-data' } },
            );

            // Define slower progress steps that look more realistic
            const progressSteps = [
              { value: 5, delay: 700 },
              { value: 15, delay: 700 },
              { value: 25, delay: 700 },
              { value: 40, delay: 700 },
              { value: 55, delay: 700 },
              { value: 70, delay: 700 },
              { value: 85, delay: 700 },
            ];

            // Show the progress animation (up to 85%)
            for (const step of progressSteps) {
              // Update React state
              setUploadProgress((prev) => {
                const newState = {
                  ...prev,
                  [file.id]: {
                    ...prev[file.id],
                    progress: step.value,
                    status: 'uploading' as const,
                  },
                };

                // Update the global DOM display
                updateGlobalProgress(newState);

                return newState;
              });

              // DIRECT DOM UPDATE - Force progress bar width using direct DOM manipulation
              const progressBarEl = document.getElementById(
                `upload-progress-bar-${file.id}`,
              );
              if (progressBarEl) {
                progressBarEl.style.width = `${step.value}%`;

                // Also update the percentage text
                const percentTextEl = document.getElementById(
                  `progress-percent-${file.id}`,
                );
                if (percentTextEl) {
                  percentTextEl.textContent = `${step.value}%`;
                }
              }

              // Log the progress update

              // Allow time for visual feedback
              await new Promise((resolve) => setTimeout(resolve, step.delay));
            }

            // Now wait for the actual upload to complete
            await uploadPromise;

            // Update the completed count for title display
            completedUploads++;

            // Show completion (100%)
            setUploadProgress((prev) => {
              const newState = {
                ...prev,
                [file.id]: {
                  ...prev[file.id],
                  progress: 100,
                  status: 'completed' as const,
                },
              };

              // Update the global title to show correct count
              const titleElement = document.querySelector(
                '#upload-progress-container > div:first-child > div:first-child',
              );
              if (titleElement) {
                if (completedUploads === totalUploads) {
                  titleElement.textContent = 'Upload Complete ✓';
                  (titleElement as HTMLElement).style.color = '#2e7d32';
                  (titleElement as HTMLElement).style.fontWeight = 'bold';
                } else {
                  titleElement.textContent = `Uploading ${completedUploads}/${totalUploads} Files`;
                }
              }

              // Update the global DOM display
              updateGlobalProgress(newState);

              return newState;
            });

            // DIRECT DOM UPDATE - Force 100% completion with direct DOM manipulation
            const completedBarEl = document.getElementById(
              `upload-progress-bar-${file.id}`,
            );
            if (completedBarEl) {
              completedBarEl.style.width = '100%';
              completedBarEl.style.backgroundColor = '#4caf50'; // Success green
              completedBarEl.style.animation = 'none';
              completedBarEl.style.backgroundImage = 'none';

              // Also update the percentage text for completion
              const percentTextEl = document.getElementById(
                `progress-percent-${file.id}`,
              );
              if (percentTextEl) {
                percentTextEl.textContent = 'Complete';
                percentTextEl.style.color = '#4caf50';
                percentTextEl.style.fontWeight = 'bold';
              }
            }

            // Mark this upload as complete
            globalUploadStatus[file.id].isComplete = true;

            // Set individual hide timer for this completed upload
            const itemEl = document.getElementById(`progress-item-${file.id}`);
            if (itemEl) {
              // Set timeout to auto-hide this specific item
              globalUploadStatus[file.id].completionTimer = setTimeout(() => {
                // Add fade-out animation to this item only
                itemEl.classList.add('fade-out');

                // Remove the item from DOM after animation
                setTimeout(() => {
                  if (itemEl && itemEl.parentNode) {
                    itemEl.parentNode.removeChild(itemEl);

                    // Check if all items are now gone and hide container if needed
                    checkAllUploadsComplete();
                  }
                }, 500); // Match duration with fadeOut animation
              }, 2000); // 2 seconds after individual completion
            }

            // Add a longer pause to show the 100% state before starting next file
            await new Promise((resolve) => setTimeout(resolve, 1000));
          } catch (error) {
            setUploadError(`Failed to upload ${file.name}. Please try again.`);
            toast.error(`Failed to upload ${file.name}`);

            // Mark as error and update completed count
            completedUploads++;

            // Mark as error
            setUploadProgress((prev) => {
              const newState = {
                ...prev,
                [file.id]: { ...prev[file.id], status: 'error' as const },
              };

              // Update the global title to show correct count
              const titleElement = document.querySelector(
                '#upload-progress-container > div:first-child > div:first-child',
              );
              if (titleElement) {
                if (completedUploads === totalUploads) {
                  titleElement.textContent = 'Upload Complete ✓';
                  (titleElement as HTMLElement).style.color = '#2e7d32';
                  (titleElement as HTMLElement).style.fontWeight = 'bold';
                } else {
                  titleElement.textContent = `Uploading ${completedUploads}/${totalUploads} Files`;
                }
              }

              // Update the global DOM display
              updateGlobalProgress(newState);

              return newState;
            });

            // DIRECT DOM UPDATE - Force error state with direct DOM manipulation
            const errorBarEl = document.getElementById(
              `upload-progress-bar-${file.id}`,
            );
            if (errorBarEl) {
              errorBarEl.style.backgroundColor = '#f44336'; // Error red
              errorBarEl.style.animation = 'none';
              errorBarEl.style.backgroundImage = 'none';

              // Also update the percentage text for error
              const percentTextEl = document.getElementById(
                `progress-percent-${file.id}`,
              );
              if (percentTextEl) {
                percentTextEl.textContent = 'Failed';
                percentTextEl.style.color = '#f44336';
              }
            }

            // Set individual hide timer for this failed upload
            const itemEl = document.getElementById(`progress-item-${file.id}`);
            if (itemEl) {
              // Set timeout to auto-hide this specific error item
              setTimeout(() => {
                // Add fade-out animation to this item only
                itemEl.classList.add('fade-out');

                // Remove the item from DOM after animation
                setTimeout(() => {
                  if (itemEl && itemEl.parentNode) {
                    itemEl.parentNode.removeChild(itemEl);

                    // Check if all items are now gone and hide container if needed
                    checkAllUploadsComplete();
                  }
                }, 500);
              }, 3000); // Slightly longer display for errors (3 seconds)
            }

            throw error;
          }

          await new Promise((resolve) => setTimeout(resolve, 500)); // Shorter delay between files
        } catch (error) {}
      }

      // All uploads completed - ensure container is properly cleaned up

      // Set a final timeout to ensure the container is removed if it hasn't been already
      setTimeout(() => {
        // Check if container still exists after all uploads are done
        if (globalUploadContainer.instance) {
          checkAllUploadsComplete();
        }
      }, 5000); // 5 seconds after all uploads complete - final fallback

      // Clean up upload queue and notify parent
      setUploadQueue([]);
      fetchInspectionPhotos();

      // Notify parent component of update
      if (onPhotoUpdate) {
        onPhotoUpdate();
      }
    } catch (error) {
      setUploadError(
        'There was an error uploading some files. Please try again.',
      );
      toast.error('There was an error uploading some files');
    } finally {
      setUploading(false);
    }
  };

  // Clean up previews when unmounting
  useEffect(() => {
    return () => {
      uploadQueue.forEach((file) => {
        if (file.preview) URL.revokeObjectURL(file.preview);
      });
    };
  }, [uploadQueue]);

  // Handle image deletion
  const handleDeleteImage = async (photoId: number | string) => {
    if (!photoId) return;

    try {
      setUploading(true);

      // Call API to delete the inspection photo
      const { data } = await axios.delete(
        `vehicle_damages/inspection-photo/${photoId}`,
      );

      if (data && data.result) {
        toast.success('Inspection photo deleted successfully');

        // Reset the selected image
        setSelectedImage(null);

        // Refresh the photos list
        fetchInspectionPhotos();

        // Notify parent component of update
        if (onPhotoUpdate) {
          onPhotoUpdate();
        }
      } else {
        toast.error(data?.message || 'Failed to delete the photo');
      }
    } catch (error) {
      toast.error('Error removing inspection photo. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Determine which tab to show initially
  useEffect(() => {
    if (readonly) {
      // If readonly and there's a drive link, show drive link tab
      if (driveLink) {
        setValue('2');
      }
      // If readonly and there are inspection images, show the view images tab
      else if (inspectionImages.length > 0) {
        setValue('3');
      }
    }
  }, [readonly, driveLink, inspectionImages]);

  // Additional safety check: if Google Drive tab is selected but no link exists, switch to another tab
  useEffect(() => {
    if (value === '2' && !driveLink) {
      // Switch to View Images if available
      if (inspectionImages.length > 0) {
        setValue('3');
      }
      // Otherwise switch to Upload (if not readonly)
      else if (!readonly) {
        setValue('1');
      }
    }
  }, [value, driveLink, inspectionImages, readonly]);

  // Reset selected image when tab changes
  useEffect(() => {
    setSelectedImage(null);
  }, [value]);

  // Set the new drive link input field value when driveLink changes
  useEffect(() => {
    setNewDriveLink(driveLink || '');
  }, [driveLink]);

  // Handle drive link validation
  // const validateDriveLink = (link: string) => {
  //   if (!link) {
  //     return true; // Empty link is valid (will clear the existing link)
  //   }
  //   return isGoogleDriveLink(link);
  // };

  // Handle drive link change
  // const handleDriveLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const newLink = e.target.value;
  //   setNewDriveLink(newLink);
  //   setDriveLinkError(validateDriveLink(newLink) ? '' : 'Please enter a valid Google Drive link');
  // };

  // Save the drive link
  // const saveDriveLink = async () => {
  //   if (!validateDriveLink(newDriveLink)) {
  //     // setDriveLinkError('Please enter a valid Google Drive link');
  //     return;
  //   }

  //   if (!damageData?.id) {
  //     toast.error('Cannot save: Vehicle damage data is missing');
  //     return;
  //   }

  //   setSavingDriveLink(true);
  //   setDriveLinkError('');

  //   try {
  //     // Use the correct endpoint with the patch method
  //     const { data } = await axios.patch(
  //       `vehicle_damages/vehicle_damage_field/${damageData.id}`,
  //       {
  //         field: 'inspection_photo',
  //         value: newDriveLink
  //       }
  //     );

  //     if (data?.result || data?.data) {
  //       setDriveLink(newDriveLink);
  //       toast.success('Google Drive link saved successfully');

  //       // Notify parent component that photo was updated
  //       if (onPhotoUpdate) {
  //         onPhotoUpdate(newDriveLink);
  //       }
  //     } else {
  //       toast.error('Failed to save Google Drive link');
  //     }
  //   } catch (error) {
  //     console.error('Error saving drive link:', error);
  //     toast.error('Failed to save Google Drive link');
  //   } finally {
  //     setSavingDriveLink(false);
  //   }
  // };

  // Generate shareable links for all images
  const generateShareableLinks = async () => {
    if (!inspectionImages || inspectionImages.length === 0) return;

    setLoadingShareLinks(true);

    try {
      // Create a temporary object to store all generated links
      const links: { [key: number]: string } = {};

      // Generate links for all images in sequence
      for (const image of inspectionImages) {
        if (!image.id) continue;

        try {
          const { data } = await axios.post(
            `vehicle_damages/generate-shareable-link/${image.id}`,
          );

          if (data && data.result && data.data && data.data.url) {
            links[image.id] = data.data.url;
          }
        } catch (error) {
          console.error(`Error generating link for image ${image.id}:`, error);
          // Continue with other images even if one fails
        }
      }

      setShareableLinks(links);
    } catch (error) {
      console.error('Error generating shareable links:', error);
      toast.error('Failed to generate all shareable links');
    } finally {
      setLoadingShareLinks(false);
    }
  };

  // Copy link to clipboard
  const copyToClipboard = (imageId: number, link: string) => {
    navigator.clipboard.writeText(link).then(() => {
      setLinkCopied((prev) => ({ ...prev, [imageId]: true }));

      setTimeout(() => {
        setLinkCopied((prev) => ({ ...prev, [imageId]: false }));
      }, 2000);

      toast.success('Link copied to clipboard');
    });
  };

  // When tab changes to View Images, generate links
  useEffect(() => {
    if (value === '3' && inspectionImages.length > 0) {
      generateShareableLinks();
    }
  }, [value, inspectionImages]);

  // Copy Google Drive link to clipboard
  const copyDriveLinkToClipboard = () => {
    if (!newDriveLink) return;

    navigator.clipboard.writeText(newDriveLink).then(() => {
      toast.success('Google Drive link copied to clipboard');
    });
  };

  return (
    <>
      <Dialog
        onClose={handleClose}
        aria-labelledby="inspection-photo-dialog-title"
        open={open}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            m: 0,
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h6" component="div">
            {readonly ? 'View Inspection Photo' : 'Inspection Photo'}
          </Typography>
          <Box>
            {!readonly && selectedImage && value === '3' && (
              <IconButton
                aria-label="delete"
                onClick={() => {
                  if (selectedImage) {
                    setImageToDelete(selectedImage);
                    setDeleteConfirmOpen(true);
                  }
                }}
                sx={{ mr: 1, color: 'error.main' }}
              >
                <DeleteIcon />
              </IconButton>
            )}
            <IconButton
              aria-label="close"
              onClick={handleClose}
              sx={{ color: 'grey' }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent dividers>
          <Box sx={{ width: '100%', typography: 'body1' }}>
            {/* Tab Headers */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Box sx={{ display: 'flex' }}>
                {!readonly && (
                  <Box
                    onClick={() => setValue('1')}
                    sx={{
                      py: 2,
                      px: 3,
                      cursor: 'pointer',
                      borderBottom:
                        value === '1'
                          ? '2px solid #1976d2'
                          : '2px solid transparent',
                      color: value === '1' ? '#1976d2' : 'inherit',
                      fontWeight: value === '1' ? 'bold' : 'normal',
                    }}
                  >
                    Upload Photos
                  </Box>
                )}
                {driveLink && (
                  <Box
                    onClick={() => setValue('2')}
                    sx={{
                      py: 2,
                      px: 3,
                      cursor: 'pointer',
                      borderBottom:
                        value === '2'
                          ? '2px solid #1976d2'
                          : '2px solid transparent',
                      color: value === '2' ? '#1976d2' : 'inherit',
                      fontWeight: value === '2' ? 'bold' : 'normal',
                    }}
                  >
                    Google Drive Link
                  </Box>
                )}
                {inspectionImages.length > 0 && (
                  <Box
                    onClick={() => setValue('3')}
                    sx={{
                      py: 2,
                      px: 3,
                      cursor: 'pointer',
                      borderBottom:
                        value === '3'
                          ? '2px solid #1976d2'
                          : '2px solid transparent',
                      color: value === '3' ? '#1976d2' : 'inherit',
                      fontWeight: value === '3' ? 'bold' : 'normal',
                    }}
                  >
                    View Images
                  </Box>
                )}
              </Box>
            </Box>

            {/* Upload Photos Content */}
            {value === '1' && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Drag and drop area */}
                <Box
                  {...getRootProps()}
                  sx={{
                    border: '2px dashed #cccccc',
                    borderRadius: '8px',
                    padding: '20px',
                    textAlign: 'center',
                    cursor: 'pointer',
                    mb: 2,
                    backgroundColor: '#f9f9fa',
                    '&:hover': { backgroundColor: '#f0f0f0' },
                  }}
                >
                  <input {...getInputProps()} />
                  <CloudUploadIcon
                    sx={{ fontSize: 40, color: '#888', mb: 1 }}
                  />
                  <Typography variant="body1">
                    Drag and drop an image here, or click to select a file
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Maximum file size: 6MB
                  </Typography>
                </Box>

                {/* Upload queue */}
                {uploadQueue.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 2,
                      }}
                    >
                      <Typography variant="subtitle1">
                        Selected image
                      </Typography>
                      <Box>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<CloudUploadIcon />}
                          onClick={handleUploadAll}
                          disabled={uploading || uploadQueue.length === 0}
                          sx={{ mr: 1 }}
                        >
                          {uploading ? 'Uploading...' : 'Upload Image'}
                        </Button>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={() => setUploadQueue([])}
                          disabled={uploading}
                        >
                          Clear
                        </Button>
                      </Box>
                    </Box>

                    <Grid container spacing={2}>
                      {uploadQueue.map((file) => (
                        <Grid key={file.id} size={12}>
                          <Box
                            sx={{
                              position: 'relative',
                              borderRadius: '8px',
                              overflow: 'hidden',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            }}
                          >
                            <Box
                              component="img"
                              src={file.preview}
                              sx={{
                                width: '100%',
                                height: 240,
                                objectFit: 'contain',
                                backgroundColor: '#f5f5f5',
                              }}
                              alt={file.name}
                            />
                            <IconButton
                              size="small"
                              onClick={() => removeFileFromQueue(file.id)}
                              sx={{
                                position: 'absolute',
                                top: 4,
                                right: 4,
                                backgroundColor: 'rgba(255,255,255,0.8)',
                                '&:hover': {
                                  backgroundColor: 'rgba(255,255,255,0.95)',
                                },
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                            <Box
                              sx={{
                                p: 1,
                                backgroundColor: 'white',
                                borderTop: '1px solid #eee',
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{
                                  display: 'block',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {file.name}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="textSecondary"
                              >
                                {(file.size / 1024 / 1024).toFixed(2)} MB
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}

                {uploading && (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      my: 2,
                    }}
                  >
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    <Typography>Uploading images...</Typography>
                  </Box>
                )}

                {loading && (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      my: 2,
                    }}
                  >
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    <Typography>Loading images...</Typography>
                  </Box>
                )}

                {uploadError && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {uploadError}
                  </Alert>
                )}
              </Box>
            )}

            {/* Google Drive Link Content */}
            {value === '2' && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 3,
                  padding: '20px',
                }}
              >
                {/* Drive Link Display Section (Read-only) */}
                <Box sx={{ mb: 2 }}>
                  {driveLink ? (
                    <>
                      <Typography variant="subtitle1" sx={{ mb: 1 }}>
                        Google Drive Link
                      </Typography>
                      <TextField
                        fullWidth
                        value={driveLink}
                        variant="outlined"
                        InputProps={{
                          readOnly: true,
                          startAdornment: (
                            <DriveFileMoveIcon color="primary" sx={{ mr: 1 }} />
                          ),
                          endAdornment: driveLink ? (
                            <InputAdornment position="end">
                              <IconButton
                                edge="end"
                                onClick={copyDriveLinkToClipboard}
                                size="small"
                              >
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            </InputAdornment>
                          ) : null,
                        }}
                        sx={{ mb: 1 }}
                      />
                    </>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <DriveFileMoveIcon
                        sx={{ fontSize: 48, color: '#ccc', mb: 2 }}
                      />
                      <Typography
                        variant="h6"
                        color="textSecondary"
                        gutterBottom
                      >
                        No Google Drive Link Available
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Please use the "Upload Photos" tab to add images
                        instead.
                      </Typography>
                    </Box>
                  )}
                </Box>

                {driveLink && (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      p: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '8px',
                      backgroundColor: '#f8f9fa',
                      mb: 2,
                    }}
                  >
                    {/* Preview Image and Link */}
                    <Box
                      sx={{
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        p: 3,
                        mb: 2,
                        border: '1px solid #e0e0e0',
                        borderRadius: '4px',
                        backgroundColor: '#fff',
                      }}
                    >
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<VisibilityIcon />}
                        size="large"
                        onClick={() =>
                          window.open(getEmbedUrl(driveLink), '_blank')
                        }
                        sx={{ mb: 1 }}
                      >
                        View in Google Drive
                      </Button>
                    </Box>

                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        Note: Adding new Google Drive links has been disabled.
                        Please use the "Upload Photos" tab to add images.
                      </Typography>
                    </Alert>
                  </Box>
                )}
              </Box>
            )}

            {/* View Images Content */}
            {value === '3' && inspectionImages.length > 0 && (
              <Box
                sx={{ display: 'flex', flexDirection: 'column', gap: 2, p: 2 }}
              >
                {loadingShareLinks && (
                  <Box
                    sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}
                  >
                    <CircularProgress size={24} sx={{ mr: 1 }} />
                    <Typography>Generating shareable links...</Typography>
                  </Box>
                )}

                <Grid container spacing={2}>
                  {inspectionImages.map((image) => {
                    const isSelected =
                      selectedImage && selectedImage.id === image.id;
                    const hasShareableLink = shareableLinks[image.id];

                    return (
                      <Grid key={image.id} size={12}>
                        <Box
                          sx={{
                            border: isSelected
                              ? '2px solid #1976d2'
                              : '1px solid #e0e0e0',
                            borderRadius: '4px',
                            overflow: 'hidden',
                            display: 'flex',
                            flexDirection: 'column',
                            position: 'relative',
                            cursor: 'pointer',
                            boxShadow: isSelected
                              ? '0 0 5px rgba(25, 118, 210, 0.5)'
                              : 'none',
                          }}
                        >
                          <Box
                            onClick={() => {
                              setSelectedImage(image);
                            }}
                            onDoubleClick={() => {
                              if (image && inspectionImages) {
                                const index = inspectionImages.findIndex(
                                  (img) => img.id === image.id,
                                );
                                if (index !== -1) {
                                  handleDoubleClick(index);
                                }
                              }
                            }}
                            sx={{
                              position: 'relative',
                              paddingTop: '40%', // Wider aspect ratio
                              width: '100%',
                            }}
                          >
                            <Box
                              component="img"
                              src={getImageUrl(image)}
                              alt={image.original_name || 'Inspection photo'}
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                backgroundColor: '#f5f5f5',
                              }}
                            />
                          </Box>

                          {/* Shareable link for this image */}
                          <Box
                            sx={{
                              p: 2,
                              borderTop: '1px solid #e0e0e0',
                              backgroundColor: '#f8f9fa',
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 1,
                            }}
                          >
                            {hasShareableLink ? (
                              <TextField
                                fullWidth
                                value={hasShareableLink}
                                variant="outlined"
                                size="small"
                                InputProps={{
                                  readOnly: true,
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton
                                        edge="end"
                                        onClick={() =>
                                          copyToClipboard(
                                            image.id,
                                            hasShareableLink,
                                          )
                                        }
                                        color={
                                          linkCopied[image.id]
                                            ? 'success'
                                            : 'default'
                                        }
                                        size="small"
                                      >
                                        <ContentCopyIcon fontSize="small" />
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            ) : (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                Link generation in progress...
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            )}
          </Box>
        </DialogContent>

        {/* Add Dialog Actions for cancel button */}
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      {openSlider && (
        <ImageSlideDialog
          url="vehicle_damages"
          files={fullSizeImages}
          setFullSizeImages={setFullSizeImages}
          imageIndex={imageIndex}
          setOpen={setOpenSlider}
          open={openSlider}
          type={'all'}
          getVehicleImages={fetchInspectionPhotos}
          setFetchingRecords={setLoading}
          isDeleteImage={!readonly}
        />
      )}
      {/* Use portal to render upload progress indicator directly to document body */}
      {showProgress && (
        <div style={{ marginTop: '16px' }}>
          <UploadProgressWrapper
            uploadProgress={uploadProgress}
            showProgress={showProgress}
            onClose={() => {
              setShowProgress(false);
              setUploadProgress({});
            }}
          />
          {uploadError && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {uploadError}
            </Alert>
          )}
        </div>
      )}
      {deleteConfirmOpen && (
        <Dialog
          open={deleteConfirmOpen}
          onClose={() => setDeleteConfirmOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">
            {'Confirm Image Deletion'}
          </DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete this image? This action cannot be
              undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteConfirmOpen(false)} color="primary">
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (imageToDelete) {
                  handleDeleteImage(imageToDelete.id);
                  setDeleteConfirmOpen(false);
                }
              }}
              color="primary"
              autoFocus
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
}
