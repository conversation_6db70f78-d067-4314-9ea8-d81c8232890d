import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import FilterAutocomplete from '@/components/mainComponents/filterComponents/FilterAutocomplete';
import { formFormatDate } from '@/configs/configs';
import {
  claim_status_options,
  currency_options,
  damage_detail_options,
  damage_happened_at_options,
  damage_status_options,
  damage_type_options,
} from '@/configs/vehicles_damage/damageHeader';
// import carNames from '@/Modules/vehicles/vehicleComponents/carNames';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

export default function Step1({ form, isUpdate, data }) {
  const { watch, setValue } = useForm();

  const vehicle_name =
    data?.vehicles?.make +
    ' ' +
    data?.vehicles?.model +
    ' ' +
    data?.vehicles?.year +
    ' ' +
    data?.vehicles?.color +
    ' ' +
    data?.vehicles?.lot_number;

  // Watch for changes in damage_happened_at and damage_status
  const damageHappenedAt = form.watch('damage_happened_at') || [];
  const currentDamageStatus = form.watch('damage_status');

  useEffect(() => {
    const watchedValue = watch('vehicle_id');
    if (watchedValue && watchedValue?.value) {
      setValue('vehicle_id', watchedValue);
    }
  }, [watch, setValue]);

  // Effect to handle status validation when damage_happened_at changes
  useEffect(() => {
    const restrictedStatuses = ['In_process', 'Pending_CA', 'Forgotten'];
    const hasAuctionDamage = damageHappenedAt.some(
      (item) => item.happened_at === 'Auction' || item === 'Auction',
    );

    // If auction is selected and current status is restricted, clear the status
    if (hasAuctionDamage && restrictedStatuses.includes(currentDamageStatus)) {
      const vin = data?.vehicles?.vin || `ID:${data?.id || 'Unknown'}`;
      toast.warning(
        `Status "${currentDamageStatus.replace('_', ' ')}" is not allowed for auction damages. Status has been cleared. (VIN: ${vin})`,
      );
      form.setValue('damage_status', '');
    }
  }, [damageHappenedAt, currentDamageStatus, data, form]);

  return isUpdate == true ? (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <TextField
            sx={{ mt: 1.5, mb: 1.5 }}
            size="small"
            id="vehicle_name"
            label="Vehicle Name"
            fullWidth
            disabled
            variant="outlined"
            value={vehicle_name}
          ></TextField>
        </Grid>
        {/* Damage Type */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box sx={{ mt: 1.5 }}>
            <Controller
              name="damage_type"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <FormControl sx={{ minWidth: '100%' }} size="small">
                  <InputLabel id="damage_type">Damage Type</InputLabel>
                  <Select
                    labelId="damage_type"
                    label="damage_type"
                    fullWidth
                    variant="outlined"
                    size="small"
                    value={field.value || ''}
                    onChange={(event) => field.onChange(event.target.value)}
                    error={error ? true : false}
                    sx={{ textAlign: 'start' }}
                  >
                    {damage_type_options.map((item, i) => (
                      <MenuItem key={i} value={item.id}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Box>
        </Grid>
        {/* damage_details  */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="damage_details"
            control={form.control}
            render={({ field }) => {
              return (
                <FilterAutocomplete
                  url={''}
                  label={'Damage Details'}
                  name={'damage_details'}
                  keyName={'name'}
                  values={field.value.map((item) => item.detail)}
                  staticOptions={damage_detail_options.map((item) => ({
                    id: item.id,
                    name: item.label,
                  }))}
                  onChange={(newValues) => {
                    // Map newValues to the expected object structure
                    const updatedSelectedValue = newValues.map(
                      (item, index) => ({
                        detail: item,
                        id: index,
                      }),
                    );
                    field.onChange(updatedSelectedValue);
                  }}
                />
              );
            }}
          />
        </Grid>

        {/* inspection image */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            sx={{ mt: 1.5, mb: 1.5 }}
            size="small"
            error={
              form.errors.inspection_photo?.message.length > 0 ? true : false
            }
            id="inspection_photo"
            label="Inspection Image"
            fullWidth
            variant="outlined"
            {...form.register('inspection_photo')}
            helperText={form.errors.inspection_photo?.message}
          ></TextField>
        </Grid>

        {/* damage happened at */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="damage_happened_at"
            control={form.control}
            render={({ field }) => {
              return (
                <FilterAutocomplete
                  url={''}
                  label={'Damage Happened At'}
                  name={'damage_happened_at'}
                  keyName={'name'}
                  values={field.value.map((item) => item.happened_at)}
                  staticOptions={damage_happened_at_options.map((item) => ({
                    id: item.id,
                    name: item.label,
                  }))}
                  onChange={(newValues) => {
                    // Map newValues to the expected object structure
                    const updatedSelectedValue = newValues.map(
                      (item, index) => ({
                        happened_at: item,
                        id: index,
                      }),
                    );
                    field.onChange(updatedSelectedValue);
                  }}
                />
              );
            }}
          />
        </Grid>

        {/* case Received at */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="case_received_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Case Received Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>

        {/* Claim  */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
              type="number"
              size="small"
              error={form.errors.claim?.message.length > 0 ? true : false}
              id="claim"
              label="Claim"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('claim')}
              helperText={form.errors.claim?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="claim_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="claim_currency">Currency</InputLabel>
                    <Select
                      labelId="claim_currency"
                      label="claim_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Credit */}
        <Grid
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5 }}
              type="number"
              size="small"
              error={form.errors.credit?.message.length > 0 ? true : false}
              id="credit"
              label="Credit"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('credit')}
              helperText={form.errors.credit?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="credit_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="credit_currency">Currency</InputLabel>
                    <Select
                      labelId="credit_currency"
                      label="credit_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* confirmed */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
              type="number"
              size="small"
              error={form.errors.confirmed?.message.length > 0 ? true : false}
              id="confirmed"
              label="Confirmed"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('confirmed')}
              helperText={form.errors.confirmed?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="confirmed_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="confirmed_currency">Currency</InputLabel>
                    <Select
                      labelId="confirmed_currency"
                      label="confirmed_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* lc_credit */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5 }}
              type="number"
              size="small"
              error={form.errors.lc_credit?.message.length > 0 ? true : false}
              id="lc_credit"
              label="loading Company Credit"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('lc_credit')}
              helperText={form.errors.lc_credit?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="lc_credit_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="confirmed_currency">Currency</InputLabel>
                    <Select
                      labelId="confirmed_currency"
                      label="confirmed_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Claim Status */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box sx={{}}>
            <Controller
              name="claim_status"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <FormControl sx={{ minWidth: '100%' }} size="small">
                  <InputLabel id="claim_status">Claim Status</InputLabel>
                  <Select
                    labelId="claim_status"
                    label="claim_status"
                    fullWidth
                    variant="outlined"
                    size="small"
                    value={field.value || ''}
                    onChange={(event) => field.onChange(event.target.value)}
                    error={error ? true : false}
                    sx={{ textAlign: 'start' }}
                  >
                    {claim_status_options.map((item, i) => (
                      <MenuItem key={i} value={item.id}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Box>
        </Grid>

        {/* Damage Status */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box>
            <Controller
              name="damage_status"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                // Get current damage_happened_at values
                const damageHappenedAt = form.watch('damage_happened_at') || [];

                // Check if damage happened at auction
                const hasAuctionDamage = damageHappenedAt.some(
                  (item) =>
                    item.happened_at === 'Auction' || item === 'Auction',
                );

                // Restricted statuses for auction damages
                const restrictedForAuction = [
                  'In_process',
                  'Pending_CA',
                  'Forgotten',
                ];

                return (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="damage_status">Damage Status</InputLabel>
                    <Select
                      labelId="damage_status"
                      label="damage_status"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => {
                        const selectedValue = event.target.value;

                        // Check if trying to select restricted status for auction damage
                        if (
                          hasAuctionDamage &&
                          restrictedForAuction.includes(selectedValue)
                        ) {
                          const vin =
                            data?.vehicles?.vin ||
                            `ID:${data?.id || 'Unknown'}`;
                          toast.error(
                            `Cannot set status to ${selectedValue.replace('_', ' ')} for damage that happened at Auction (VIN: ${vin})`,
                          );
                          return;
                        }

                        field.onChange(selectedValue);
                      }}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {damage_status_options
                        .filter(
                          (item) =>
                            ![
                              'Half_Cut',
                              'Pre_Credit',
                              'Credited',
                              'Initial_Review',
                            ].includes(item.id),
                        )
                        .map((item, i) => {
                          // Check if this option should be disabled for auction damages
                          const isDisabled =
                            hasAuctionDamage &&
                            restrictedForAuction.includes(item.id);

                          return (
                            <MenuItem
                              key={i}
                              value={item.id}
                              disabled={isDisabled}
                              sx={{
                                opacity: isDisabled ? 0.5 : 1,
                                '&.Mui-disabled': {
                                  opacity: 0.5,
                                },
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                }}
                              >
                                {item.label}
                                {isDisabled && (
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ ml: 1 }}
                                  >
                                    (Not allowed for auction damages)
                                  </Typography>
                                )}
                              </Box>
                            </MenuItem>
                          );
                        })}
                    </Select>
                  </FormControl>
                );
              }}
            />
          </Box>
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
            type="string"
            size="small"
            error={form.errors.remark?.message.length > 0 ? true : false}
            id="remark"
            label="Remark"
            fullWidth
            variant="outlined"
            {...form.register('remark')}
            helperText={form.errors.remark?.message}
          ></TextField>
        </Grid>
      </Grid>
    </Box>
  ) : (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Grid container spacing={2}>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="vehicle_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  sx={error ? { mt: 1.5 } : { my: 1.5 }}
                  url="vehicle_damages/search-vehicles-autocomplete"
                  label="Select Vehicle"
                  fieldName="name"
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'vehicles'}
                  isOptionEqualToValue={(option, value) =>
                    option.id === value.value
                  }
                />
              );
            }}
          />
        </Grid>
        {/* Damage Type */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box sx={{ mt: 1.5 }}>
            <Controller
              name="damage_type"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <FormControl sx={{ minWidth: '100%' }} size="small">
                  <InputLabel id="damage_type">Damage Type</InputLabel>
                  <Select
                    labelId="damage_type"
                    label="damage_type"
                    fullWidth
                    variant="outlined"
                    size="small"
                    value={field.value || ''}
                    onChange={(event) => field.onChange(event.target.value)}
                    error={error ? true : false}
                    sx={{ textAlign: 'start' }}
                  >
                    {damage_type_options.map((item, i) => (
                      <MenuItem key={i} value={item.id}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Box>
        </Grid>
        {/* damage_details  */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="damage_details"
            control={form.control}
            render={({ field }) => {
              return (
                <FilterAutocomplete
                  url={''}
                  label={'Damage Details'}
                  name={'damage_details'}
                  keyName={'name'}
                  values={field.value.map((item) => item.detail)}
                  staticOptions={damage_detail_options.map((item) => ({
                    id: item.id,
                    name: item.label,
                  }))}
                  onChange={(newValues) => {
                    // Map newValues to the expected object structure
                    const updatedSelectedValue = newValues.map(
                      (item, index) => ({
                        detail: item,
                        id: index,
                      }),
                    );
                    field.onChange(updatedSelectedValue);
                  }}
                />
              );
            }}
          />
        </Grid>

        {/* inspection image */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            sx={{ mt: 1.5, mb: 1.5 }}
            size="small"
            error={
              form.errors.inspection_photo?.message.length > 0 ? true : false
            }
            id="inspection_photo"
            label="Inspection Image"
            fullWidth
            variant="outlined"
            {...form.register('inspection_photo')}
            helperText={form.errors.inspection_photo?.message}
          ></TextField>
        </Grid>

        {/* damage happened at */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="damage_happened_at"
            control={form.control}
            render={({ field }) => {
              return (
                <FilterAutocomplete
                  url={''}
                  label={'Damage Happened At'}
                  name={'damage_happened_at'}
                  keyName={'name'}
                  values={field.value.map((item) => item.happened_at)}
                  staticOptions={damage_happened_at_options.map((item) => ({
                    id: item.id,
                    name: item.label,
                  }))}
                  onChange={(newValues) => {
                    // Map newValues to the expected object structure
                    const updatedSelectedValue = newValues.map(
                      (item, index) => ({
                        happened_at: item,
                        id: index,
                      }),
                    );
                    field.onChange(updatedSelectedValue);
                  }}
                />
              );
            }}
          />
        </Grid>

        {/* case Received at */}
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            control={form.control}
            name="case_received_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                views={['year', 'month', 'day']}
                label="Case Received Date"
                value={!field.value ? null : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                onChange={(e) => {
                  field.onChange(formFormatDate(e));
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>

        {/* Claim  */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
              type="number"
              size="small"
              error={form.errors.claim?.message.length > 0 ? true : false}
              id="claim"
              label="Claim"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('claim')}
              helperText={form.errors.claim?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="claim_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="claim_currency">Currency</InputLabel>
                    <Select
                      labelId="claim_currency"
                      label="claim_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Credit */}
        <Grid
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5 }}
              type="number"
              size="small"
              error={form.errors.credit?.message.length > 0 ? true : false}
              id="credit"
              label="Credit"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('credit')}
              helperText={form.errors.credit?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="credit_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="credit_currency">Currency</InputLabel>
                    <Select
                      labelId="credit_currency"
                      label="credit_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* confirmed */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
              type="number"
              size="small"
              error={form.errors.confirmed?.message.length > 0 ? true : false}
              id="confirmed"
              label="Confirmed"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('confirmed')}
              helperText={form.errors.confirmed?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="confirmed_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="confirmed_currency">Currency</InputLabel>
                    <Select
                      labelId="confirmed_currency"
                      label="confirmed_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* lc_credit */}
        <Grid
          // spacing={2}
          style={{ display: 'flex', alignItems: 'center' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid style={{ width: '65%' }}>
            <TextField
              sx={{ mt: 1.5, mb: 1.5 }}
              type="number"
              size="small"
              error={form.errors.lc_credit?.message.length > 0 ? true : false}
              id="lc_credit"
              label="loading Company Credit"
              defaultValue={0}
              fullWidth
              variant="outlined"
              {...form.register('lc_credit')}
              helperText={form.errors.lc_credit?.message}
            ></TextField>
          </Grid>
          <Grid style={{ width: '35%', marginLeft: '16px' }}>
            <Box sx={{}}>
              <Controller
                name="lc_credit_currency"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="confirmed_currency">Currency</InputLabel>
                    <Select
                      labelId="confirmed_currency"
                      label="confirmed_currency"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => field.onChange(event.target.value)}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {currency_options.map((item, i) => (
                        <MenuItem key={i} value={item.id}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Claim Status */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box sx={{}}>
            <Controller
              name="claim_status"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <FormControl sx={{ minWidth: '100%' }} size="small">
                  <InputLabel id="claim_status">Claim Status</InputLabel>
                  <Select
                    labelId="claim_status"
                    label="claim_status"
                    fullWidth
                    variant="outlined"
                    size="small"
                    value={field.value || ''}
                    onChange={(event) => field.onChange(event.target.value)}
                    error={error ? true : false}
                    sx={{ textAlign: 'start' }}
                  >
                    {claim_status_options.map((item, i) => (
                      <MenuItem key={i} value={item.id}>
                        {item.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Box>
        </Grid>

        {/* Damage Status */}
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box>
            <Controller
              name="damage_status"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                // Restricted statuses for auction damages
                const restrictedForAuction = [
                  'In_process',
                  'Pending_CA',
                  'Forgotten',
                ];

                return (
                  <FormControl sx={{ minWidth: '100%' }} size="small">
                    <InputLabel id="damage_status">Damage Status</InputLabel>
                    <Select
                      labelId="damage_status"
                      label="damage_status"
                      fullWidth
                      variant="outlined"
                      size="small"
                      value={field.value || ''}
                      onChange={(event) => {
                        const selectedValue = event.target.value;

                        // Check if trying to select restricted status for auction damage
                        if (
                          damageHappenedAt.some(
                            (item) =>
                              item.happened_at === 'Auction' ||
                              item === 'Auction',
                          ) &&
                          restrictedForAuction.includes(selectedValue)
                        ) {
                          toast.error(
                            `Cannot set status to ${selectedValue.replace('_', ' ')} for damage that happened at Auction`,
                          );
                          return;
                        }

                        field.onChange(selectedValue);
                      }}
                      error={error ? true : false}
                      sx={{ textAlign: 'start' }}
                    >
                      {damage_status_options
                        .filter(
                          (item) =>
                            ![
                              'Half_Cut',
                              'Pre_Credit',
                              'Credited',
                              'Initial_Review',
                            ].includes(item.id),
                        )
                        .map((item, i) => {
                          // Check if this option should be disabled for auction damages
                          const hasAuctionDamage = damageHappenedAt.some(
                            (happenedAtItem) =>
                              happenedAtItem.happened_at === 'Auction' ||
                              happenedAtItem === 'Auction',
                          );
                          const isDisabled =
                            hasAuctionDamage &&
                            restrictedForAuction.includes(item.id);

                          return (
                            <MenuItem
                              key={i}
                              value={item.id}
                              disabled={isDisabled}
                              sx={{
                                opacity: isDisabled ? 0.5 : 1,
                                '&.Mui-disabled': {
                                  opacity: 0.5,
                                },
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                }}
                              >
                                {item.label}
                                {isDisabled && (
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ ml: 1 }}
                                  >
                                    (Not allowed for auction damages)
                                  </Typography>
                                )}
                              </Box>
                            </MenuItem>
                          );
                        })}
                    </Select>
                  </FormControl>
                );
              }}
            />
          </Box>
        </Grid>

        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            sx={{ mt: 1.5, mb: 1.5, marginRight: '16px' }}
            type="string"
            size="small"
            error={form.errors.remark?.message.length > 0 ? true : false}
            id="remark"
            label="Remark"
            fullWidth
            variant="outlined"
            {...form.register('remark')}
            helperText={form.errors.remark?.message}
          ></TextField>
        </Grid>
      </Grid>
    </Box>
  );
}
