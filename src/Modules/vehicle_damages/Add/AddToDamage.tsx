import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import React, { useState } from 'react';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

const AddToDamage = ({
  open,
  onDeny,
  selectedItems,
  setSelectedItems,
  setAddToDamage,
  fetchRecords,
}) => {
  //////////////////////////////////////////////////////////////////////
  const [loading, setLoading] = useState(false);
  const onConfirm = async () => {
    try {
      setLoading(true);
      const { data } = await axios.post('vehicle_damages/addToDamage', {
        vehicles_ids: selectedItems.map((item) => item?.id),
        halfcut_status: selectedItems[0]?.halfcut_status,
      });
      if (data?.result) {
        setAddToDamage(false);
        setSelectedItems([]);
        fetchRecords();
        toast.success(data?.message || 'Successfully added to damage');
      } else {
        toast.error(data?.message);
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message ||
          'An error occurred while adding to damage',
      );
    }
    setLoading(false);
  };

  return (
    <AppConfirmDialog
      open={open}
      onDeny={onDeny}
      submitting={loading}
      onConfirm={onConfirm}
      title={'Are you sure you want to add this vehicle to damage case?'}
      dialogTitle={`Add vehicle to Damage`}
      confirmText={'Add'}
      cancelText="Cancel"
      maxWidth={'sm'}
    />
  );
};

export default AddToDamage;
