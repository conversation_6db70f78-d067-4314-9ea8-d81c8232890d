import React, { useEffect, useState } from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Link,
} from '@react-pdf/renderer';
import moment from 'moment';
import axios from '@/lib/axios';

// Custom date formatter for the report to handle timezone properly
const formatReportDate = (date) => {
  if (!date) return '';

  // Parse the date, ensuring it's treated as a local date
  const parsedDate = new Date(date);
  // Use moment to format it without applying UTC transformation
  return moment(parsedDate).format('YYYY-MM-DD');
};

// Create styles with premium design
const styles = StyleSheet.create({
  page: {
    padding: 40,
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica',
  },
  coverPage: {
    padding: 0,
    backgroundColor: '#fff5f5',
  },
  coverContent: {
    height: '100%',
    padding: 40,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  coverHeader: {
    marginBottom: 60,
  },
  coverLogo: {
    width: 180,
    height: 60,
    marginBottom: 40,
    alignSelf: 'center',
  },
  coverTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 10,
    textTransform: 'uppercase',
  },
  coverSubtitle: {
    fontSize: 20,
    color: '#777777',
    textAlign: 'center',
    marginBottom: 15,
  },
  coverSubtitle2: {
    fontSize: 16,
    color: '#777777',
    textAlign: 'center',
    marginBottom: 15,
  },
  coverDate: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
  },
  coverFooter: {
    marginTop: 60,
    borderTopWidth: 1,
    borderTopColor: '#dddddd',
    paddingTop: 20,
  },
  coverStats: {
    textAlign: 'center',
    fontSize: 16,
    color: '#d32f2f',
    fontWeight: 'bold',
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1,
  },
  pageHeader: {
    flexDirection: 'row',
    borderBottomWidth: 2,
    borderBottomColor: '#d32f2f',
    paddingBottom: 15,
    marginBottom: 18,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#d32f2f',
  },
  headerBadge: {
    fontSize: 12,
    backgroundColor: '#ffebee',
    color: '#d32f2f',
    padding: 6,
    borderRadius: 4,
  },
  heading: {
    fontSize: 24,
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: 'bold',
    color: '#d32f2f',
  },
  subheading: {
    fontSize: 18,
    marginBottom: 18,
    textAlign: 'center',
    color: '#555555',
  },
  sectionTitle: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: 'bold',
    backgroundColor: '#ffebee',
    padding: 8,
    borderRadius: 4,
    color: '#d32f2f',
    textTransform: 'uppercase',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 6,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    borderBottomStyle: 'solid',
    paddingBottom: 10,
    paddingTop: 10,
  },
  label: {
    width: '40%',

    fontWeight: 'bold',
    fontSize: 12,
    color: '#555555',
  },
  value: {
    width: '60%',
    fontSize: 12,
  },
  labelFull: {
    width: '19%',
    fontWeight: 'bold',
    fontSize: 12,
    color: '#555555',
  },
  valueHighlight: {
    fontSize: 12,
    color: '#d32f2f',
    fontWeight: 'bold',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 5,
    paddingLeft: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    borderBottomStyle: 'solid',
    paddingBottom: 5,
  },
  detailLabel: {
    width: '70%',
    fontSize: 10,
    color: '#555555',
  },
  detailCount: {
    width: '30%',
    fontSize: 12,
    fontWeight: 'bold',
    color: '#d32f2f',
  },
  totalRow: {
    flexDirection: 'row',
    marginBottom: 15,
    marginTop: 13,
    fontWeight: 'bold',
    fontSize: 14,
    backgroundColor: '#ffebee',
    padding: 12,
    borderRadius: 4,
    color: '#d32f2f',
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    fontSize: 10,
    textAlign: 'center',
    color: '#aaaaaa',
  },
  summaryBox: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 20,
    marginTop: 30,
    backgroundColor: 'white',
  },
  reportHeader: {
    borderBottomWidth: 2,
    borderBottomColor: '#d32f2f',
    paddingBottom: 20,
    marginBottom: 25,
  },
  idBadge: {
    backgroundColor: '#d32f2f',
    color: 'white',
    padding: 8,
    textAlign: 'center',
    borderRadius: 4,
    marginBottom: 15,
    fontSize: 12,
    fontWeight: 'bold',
  },
  damageDetailBadge: {
    backgroundColor: '#ffcdd2',
    padding: 4,
    margin: 3,
    borderRadius: 3,
    fontSize: 10,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  infoCard: {
    flex: 1,
    padding: 10,
    height: 50,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    marginHorizontal: 5,
    backgroundColor: 'white',
  },
  infoLabel: {
    fontSize: 10,
    color: '#777777',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  chartContainer: {
    marginTop: 15,
    marginBottom: 15,
    height: 180,
    flexDirection: 'column',
  },
  chartBar: {
    height: 22,
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  chartLabel: {
    width: '30%',
    fontSize: 10,
    paddingRight: 10,
  },
  chartBarContainer: {
    width: '60%',
    height: 18,
    backgroundColor: '#ffebee',
    borderRadius: 9,
  },
  chartBarFill: {
    backgroundColor: '#d32f2f',
    opacity: 0.9,
    height: '100%',
    borderRadius: 9,
  },
  chartValue: {
    width: '10%',
    fontSize: 10,
    textAlign: 'right',
    paddingLeft: 5,
    fontWeight: 'bold',
  },
  footer: {
    position: 'absolute',
    bottom: 20,
    left: 40,
    right: 40,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 5,
    fontSize: 8,
    color: '#999999',
    textAlign: 'center',
  },
  damagePhotoContainer: {
    marginTop: 2,
    marginBottom: 2,
  },
  damagePhoto: {
    maxWidth: '100%',
    maxHeight: 320,
    objectFit: 'contain',
    marginTop: 10,
    borderRadius: 5,
    border: '1px solid #e0e0e0',
  },
  noPhotoContainer: {
    marginTop: 2,
    width: '100%',
    height: 300,
    backgroundColor: '#ffebee',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    border: '1px solid #e0e0e0',
  },
  noPhotoText: {
    color: '#d32f2f',
    fontSize: 12,
    fontStyle: 'italic',
  },
  imageContainer: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  twoColumnContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  columnLeft: {
    width: '48%',
    marginRight: '2%',
  },
  columnRight: {
    width: '48%',
    marginLeft: '2%',
  },
  hyperlink: {
    textDecoration: 'underline',
    color: '#0066cc',
    fontSize: 12,
  },
});

// Helper function to count damage details
const countDamageDetails = (damages) => {
  const counts = {};

  damages.forEach((damage) => {
    if (damage.damage_details && Array.isArray(damage.damage_details)) {
      damage.damage_details.forEach((detail) => {
        if (detail.detail) {
          const detailName = detail.detail;
          counts[detailName] = (counts[detailName] || 0) + 1;
        }
      });
    }
  });

  // Sort by count in descending order
  return Object.entries(counts)
    .sort((a, b) => Number(b[1]) - Number(a[1]))
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});
};

// Function to get max value from object
const getMaxValue = (obj) => {
  return Math.max(...Object.values(obj).map((val) => Number(val)));
};

// Helper function to convert Google Drive URL to direct image URL
const convertGoogleDriveUrl = (url) => {
  if (!url || typeof url !== 'string') return '';

  // Check if it's a Google Drive URL
  if (url.includes('drive.google.com')) {
    // Extract file ID from various Google Drive URL formats
    let fileId = '';

    // Format: https://drive.google.com/file/d/FILE_ID/view
    const fileMatch = url.match(/\/file\/d\/([^\/]+)/);
    if (fileMatch && fileMatch[1]) {
      fileId = fileMatch[1];
    }

    // Format: https://drive.google.com/uc?id=FILE_ID
    const ucMatch = url.match(/[?&]id=([^&]+)/);
    if (!fileId && ucMatch && ucMatch[1]) {
      fileId = ucMatch[1];
    }

    if (fileId) {
      return `${process.env.NEXT_PUBLIC_NEST_BASE_URL}/api/proxy/google-drive-public-image?url=${encodeURIComponent(`https://drive.google.com/uc?export=view&id=${fileId}`)}`;
    }
  }

  return url;
};

// Helper function to get image URL from MinIO path
const getMinioImageUrl = (url) => {
  if (!url) return '';

  // If URL already starts with http, use it directly
  if (url.startsWith('http')) {
    return url;
  }

  // Otherwise, prepend the MINIO_ENDPOINT
  return `${process.env.NEXT_PUBLIC_MINIO_ENDPOINT}${url}`;
};

interface filteredDataType {
  containers: number;
  data: {
    id: number;
    name: string;
    dateRange: {
      fromDate: string;
      toDate: string;
    };
  };
}

const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return 'https://admin.pglsystem.com/'; // fallback for SSR
};

// Main component
const InspectVehicleDamageReportPDF = ({
  filteredData = {} as filteredDataType,
  damages = [],
}) => {
  const [damagesWithImages, setDamagesWithImages] = useState(damages);
  const detailCounts = countDamageDetails(damages);
  const maxCount = getMaxValue(detailCounts);

  // Fetch uploaded images for all damages
  useEffect(() => {
    const fetchUploadedImages = async () => {
      try {
        const enhancedDamages = [...damages];

        for (let i = 0; i < enhancedDamages.length; i++) {
          const damage = enhancedDamages[i];

          // Only fetch images if there's no Google Drive link
          if (
            !damage.inspection_photo ||
            damage.inspection_photo === 'N/A' ||
            damage.inspection_photo === null
          ) {
            try {
              const { data } = await axios.get(
                `vehicle_damages/inspection-photos/${damage.id}`,
              );

              if (
                data?.result &&
                data?.data?.photos &&
                data?.data?.photos.length > 0
              ) {
                // Store the first uploaded image URL
                enhancedDamages[i] = {
                  ...damage,
                  uploaded_image: data.data.photos[0].url,
                };
              }
            } catch (error) {
              console.error(
                `Error fetching images for damage ${damage.id}:`,
                error,
              );
            }
          }
        }

        setDamagesWithImages(enhancedDamages);
      } catch (error) {
        console.error('Error fetching uploaded images:', error);
        setDamagesWithImages(damages);
      }
    };

    fetchUploadedImages();
  }, [damages]);

  // Helper function to get the loader name consistently
  const getLoaderName = (damages) => {
    if (
      damages &&
      damages.length == 1 &&
      damages[0]?.vehicles?.containers?.loaders?.[0]?.name
    ) {
      const loaders = damages[0]?.vehicles?.containers?.loaders
        ?.map((loader) => loader.name)
        .join(', ');
      return `Loader: ${loaders}`;
    }
    return '';
  };

  // Always render the document regardless of loading state
  return (
    <Document>
      {/* Cover Page - Only rendered once */}
      <Page size="A4" style={styles.coverPage}>
        <View style={styles.coverContent}>
          <View style={styles.coverHeader}>
            {/* PGL Logo */}
            <Image src="/images/logo-5001.png" style={styles.coverLogo} />
            <Text style={styles.coverTitle}>Damage Report</Text>
            <Text style={styles.coverSubtitle}>Manual Damages</Text>
            {filteredData?.data?.dateRange?.fromDate &&
              filteredData?.data?.dateRange?.toDate && (
                <Text style={styles.coverSubtitle2}>
                  {filteredData?.data?.dateRange?.fromDate} To{' '}
                  {filteredData?.data?.dateRange?.toDate}
                </Text>
              )}

            {filteredData?.data?.name ? (
              <Text style={styles.coverSubtitle2}>
                Loader: {filteredData?.data?.name}
              </Text>
            ) : (
              <Text style={styles.coverSubtitle2}>
                {getLoaderName(damages)}
              </Text>
            )}
          </View>

          <View style={styles.summaryBox}>
            {/* Visual Chart */}
            <Text style={styles.sectionTitle}>Top Damage Types</Text>
            <View style={styles.chartContainer}>
              {Object.entries(detailCounts)
                .slice(0, 5)
                .map(([detail, count], index) => (
                  <View key={index} style={styles.chartBar}>
                    <Text style={styles.chartLabel}>{detail}</Text>
                    <View style={styles.chartBarContainer}>
                      <View
                        style={{
                          ...styles.chartBarFill,
                          width: `${(Number(count) / maxCount) * 100}%`,
                        }}
                      />
                    </View>
                    <Text style={styles.chartValue}>{String(count)}</Text>
                  </View>
                ))}
            </View>

            <View style={styles.infoRow}>
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Damaged Vehicles</Text>
                <Text style={styles.infoValue}>{damages.length}</Text>
              </View>
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Containers Count</Text>
                <Text style={styles.infoValue}>{filteredData?.containers}</Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Damage Percentage</Text>
                <Text style={styles.infoValue}>
                  {filteredData?.containers > 0
                    ? `${Math.round((damages.length / filteredData?.containers) * 100)}%`
                    : '0%'}
                </Text>
              </View>

              <View style={styles.infoCard}>
                <Text style={styles.infoLabel}>Total loss due to damage</Text>
                <Text style={styles.infoValue}>
                  {damages.reduce((acc, damage) => acc + damage.claim, 0)}{' '}
                  {damages[0]?.claim_currency}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.coverFooter}>
            <Text
              style={{
                textAlign: 'center',
                fontSize: 10,
                color: '#999999',
                marginTop: 10,
              }}
            >
              This report contains detailed information about vehicle damages
            </Text>
          </View>
        </View>
      </Page>

      {/* Detailed Summary Page  */}
      <Page size="A4" style={styles.page}>
        <View style={styles.pageHeader}>
          <Text style={styles.headerText}>Damage Summary</Text>
          {filteredData?.data?.dateRange?.fromDate &&
            filteredData?.data?.dateRange?.toDate && (
              <Text style={styles.headerBadge}>
                {filteredData?.data?.dateRange?.fromDate} To{' '}
                {filteredData?.data?.dateRange?.toDate}
              </Text>
            )}
        </View>

        <View style={styles.card}>
          <Text style={styles.sectionTitle}>All Damage Details</Text>
          {Object.entries(detailCounts).map(([detail, count], index) => (
            <View key={index} style={styles.detailRow}>
              <Text style={styles.detailLabel}>{detail}</Text>
              <Text style={styles.detailCount}>{String(count)}</Text>
            </View>
          ))}
        </View>

        <View style={styles.infoRow}>
          <View style={styles.infoCard}>
            <Text style={styles.infoLabel}>Total Vehicles</Text>
            <Text style={styles.infoValue}>{damages.length}</Text>
          </View>
          <View style={styles.infoCard}>
            <Text style={styles.infoLabel}>Damage Types</Text>
            <Text style={styles.infoValue}>
              {Object.keys(detailCounts).length}
            </Text>
          </View>
          <View style={styles.infoCard}>
            <Text style={styles.infoLabel}>Most Common</Text>
            <Text style={styles.infoValue}>
              {Object.entries(detailCounts)[0]?.[0] || 'N/A'}
            </Text>
          </View>
        </View>

        <Text
          style={styles.footer}
          render={({ pageNumber, totalPages }) =>
            `Vehicle Damages Report • Page ${pageNumber} of ${totalPages}`
          }
        />
      </Page>

      {/* Individual Damage Pages */}
      {damagesWithImages.map((damage, index) => (
        <Page key={index} size="A4" style={styles.page}>
          <View style={styles.pageHeader}>
            <Text style={styles.headerText}>Vehicle Damage Record</Text>
            <Text style={styles.headerBadge}>
              {damage?.vehicles?.containers?.loaders
                ?.map((loader) => loader.name)
                .join(', ')}
            </Text>
          </View>

          <View style={styles.card}>
            <Text style={styles.sectionTitle}>Vehicle Information</Text>

            <View style={styles.row}>
              <Text style={styles.labelFull}>Description :</Text>
              <Text style={styles.valueHighlight}>
                {damage.vehicles
                  ? `${damage.vehicles.year || ''} ${damage.vehicles.make || ''} ${damage.vehicles.model || ''} ${damage.vehicles.color || ''}`
                  : 'N/A'}
              </Text>
            </View>

            <View style={styles.twoColumnContainer}>
              <View style={styles.columnLeft}>
                <View style={styles.row}>
                  <Text style={styles.label}> VIN # :</Text>
                  <View style={styles.value}>
                    {damage.vehicles?.vin ? (
                      <Link
                        src={
                          damage.vehicles?.photo_link ||
                          `${getBaseUrl()}/vehicle_damages/all`
                        }
                        style={styles.hyperlink}
                      >
                        {damage.vehicles.vin}
                      </Link>
                    ) : (
                      <Text>N/A</Text>
                    )}
                  </View>
                </View>

                <View style={styles.row}>
                  <Text style={styles.label}>Lot Number :</Text>
                  <Text style={styles.value}>
                    {damage.vehicles?.lot_number || 'N/A'}
                  </Text>
                </View>

                <View style={styles.row}>
                  <Text style={styles.label}>Credit Amount :</Text>
                  <Text style={styles.value}>
                    {damage.claim} {damage.claim_currency}
                  </Text>
                </View>
              </View>

              <View style={styles.columnRight}>
                <View style={styles.row}>
                  <Text style={styles.label}>Container # :</Text>
                  <View style={styles.value}>
                    {damage.vehicles?.containers?.container_number ? (
                      <Link
                        src={
                          damage.vehicles?.containers?.photo_link ||
                          `${getBaseUrl()}/vehicle_damages/all`
                        }
                        style={styles.hyperlink}
                      >
                        {damage.vehicles.containers.container_number}
                      </Link>
                    ) : (
                      <Text>N/A</Text>
                    )}
                  </View>
                </View>

                <View style={styles.row}>
                  <Text style={styles.label}>Received Date :</Text>
                  <Text style={styles.value}>
                    {damage.case_received_date
                      ? formatReportDate(damage.case_received_date)
                      : 'N/A'}
                  </Text>
                </View>

                <View style={styles.row}>
                  <Text style={styles.label}>Loading Date :</Text>
                  <Text style={styles.value}>
                    {damage.vehicles?.containers?.loading_date
                      ? formatReportDate(
                          damage.vehicles.containers.loading_date,
                        )
                      : 'N/A'}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.row}>
              <Text style={styles.label}>Damage Details :</Text>
              <View style={styles.value}>
                {damage.damage_details && damage.damage_details.length > 0 ? (
                  damage.damage_details.map((detail, i) => (
                    <Text key={i} style={styles.damageDetailBadge}>
                      {detail.detail}
                    </Text>
                  ))
                ) : (
                  <Text>N/A</Text>
                )}
              </View>
            </View>

            {/* Photo Section - Updated to show uploaded images when no Google Drive link exists */}
            {damage.inspection_photo &&
            damage.inspection_photo !== 'N/A' &&
            damage.inspection_photo !== null ? (
              <View>
                <Image
                  src={convertGoogleDriveUrl(damage?.inspection_photo)}
                  style={styles.damagePhoto}
                />
              </View>
            ) : damage.uploaded_image ? (
              <View>
                <Image
                  src={getMinioImageUrl(damage?.uploaded_image)}
                  style={styles.damagePhoto}
                />
              </View>
            ) : (
              <View style={styles.noPhotoContainer}>
                <Text style={styles.noPhotoText}>
                  No inspection photo available
                </Text>
              </View>
            )}
          </View>

          <Text
            style={styles.footer}
            render={({ pageNumber, totalPages }) =>
              `Vehicle Damages Report • Page ${pageNumber} of ${totalPages} `
            }
          />
        </Page>
      ))}
    </Document>
  );
};

export default InspectVehicleDamageReportPDF;
