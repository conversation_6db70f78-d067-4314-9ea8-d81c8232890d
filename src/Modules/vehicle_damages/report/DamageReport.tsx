import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import { Box, Container, Tab, Tabs } from '@mui/material';
import Head from 'next/head';
import { useContext, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import { useDamageHeaderInfo } from '@/configs/vehicles_damage/damageHeader';
import BonusReport from './BonusReport';
// import { useRouter } from 'next/router';

const DamageReport = () => {
  const useProfileContext = useContext(contextProvider);
  const damageHeaderInfo = useDamageHeaderInfo();
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  // const router = useRouter();
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  // Check if user has report view permission
  if (!perms?.includes(DAMAGE?.REPORT_VIEW)) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="80vh"
        fontWeight="bold"
        fontSize="20px"
      >
        Unauthorized - You need report view permission to access this page
      </Box>
    );
  }

  return (
    <>
      <Head>
        <title>Damages Report</title>
      </Head>
      <PageHeader breadcrumbs={damageHeaderInfo} />
      <Container maxWidth="xl">
        <Box sx={{ width: '100%', mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            textColor="primary"
            indicatorColor="primary"
            aria-label="Damage reports tabs"
          >
            <Tab label="All" />
            <Tab label="Bonus Reports" />
            <Tab label="Damaged Parts" />
            <Tab label="Damages Received" />
            <Tab label="Per POL" />
            <Tab label="Loaded Comparison" />
            <Tab label="Customers" />
            <Tab label="Overall Customers" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
          {activeTab === 0 && (
            <Box>
              <h3>All Damages Report</h3>
              <p>Reports dashboard will be implemented here.</p>
            </Box>
          )}
          {activeTab === 1 && (
            <Box>
              <BonusReport />
            </Box>
          )}
          {activeTab === 2 && (
            <Box>
              <h3>Damaged Parts</h3>
              <p>Damaged parts statistics will be displayed here.</p>
            </Box>
          )}
          {activeTab === 3 && (
            <Box>
              <h3>Damages Received</h3>
              <p>Damages received reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 4 && (
            <Box>
              <h3>Per POL</h3>
              <p>POL-based damage reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 5 && (
            <Box>
              <h3>Loaded Comparison</h3>
              <p>Loaded comparison reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 6 && (
            <Box>
              <h3>Customers</h3>
              <p>Customer-based damage reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 7 && (
            <Box>
              <h3>Overall Customers</h3>
              <p>Overall customer damage reports will be displayed here.</p>
            </Box>
          )}
        </Box>
      </Container>
    </>
  );
};

export default DamageReport;
