import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Font,
} from '@react-pdf/renderer';

// Register Roboto font
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 'normal',
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 'bold',
    },
  ],
});

// Define styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Roboto',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 10,
  },
  logo: {
    width: 100,
    marginRight: 15,
  },
  headerRight: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1976D2',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 5,
    color: '#666666',
  },
  dateRange: {
    marginTop: 5,
    fontSize: 12,
    color: '#666666',
  },
  polInfo: {
    marginTop: 5,
    fontSize: 12,
    color: '#666666',
  },
  tableContainer: {
    marginTop: 15,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderBottomWidth: 1,
    borderBottomColor: '#CCCCCC',
    padding: 8,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    padding: 8,
    minHeight: 24,
  },
  tableCell: {
    flex: 1,
    fontSize: 10,
    textAlign: 'left',
  },
  tableCellRight: {
    flex: 1,
    fontSize: 10,
    textAlign: 'right',
  },
  summaryContainer: {
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  summaryRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  summaryLabel: {
    flex: 1,
    fontSize: 12,
  },
  summaryValue: {
    flex: 1,
    fontSize: 12,
    textAlign: 'right',
    fontWeight: 'bold',
  },
  finalTotal: {
    flexDirection: 'row',
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  finalTotalLabel: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
  },
  finalTotalValue: {
    flex: 1,
    fontSize: 14,
    textAlign: 'right',
    fontWeight: 'bold',
    color: '#1976D2',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    fontSize: 10,
    textAlign: 'center',
    color: '#666666',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    fontSize: 10,
    color: '#666666',
  },
});

// Define the PDF Component
const BonusReportPDF = ({ reportData, filters, totals }) => {
  // Format currency
  const formatCurrency = (value) => {
    return `$${value.toFixed(2)}`;
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header with Logo and Title */}
        <View style={styles.header}>
          <Image src="/images/logo-5001.png" style={styles.logo} />
          <View style={styles.headerRight}>
            <Text style={styles.title}>Loader Bonus Report</Text>
            <Text style={styles.subtitle}>Performance and Bonus Summary</Text>
            <Text style={styles.dateRange}>
              Period: {formatDate(filters.startDate)} to{' '}
              {formatDate(filters.endDate)}
            </Text>
            <Text style={styles.polInfo}>
              Port of Loading: {filters.polName || 'All Ports'}
              {filters.loaderName ? ` | Loader: ${filters.loaderName}` : ''}
            </Text>
          </View>
        </View>

        {/* Table Header */}
        <View style={styles.tableContainer}>
          <View style={styles.tableHeader}>
            <Text style={styles.tableCell}>Loader Name</Text>
            <Text style={styles.tableCell}>Month</Text>
            <Text style={styles.tableCell}>POL</Text>
            <Text style={styles.tableCellRight}>Containers</Text>
            <Text style={styles.tableCellRight}>Damages</Text>
            <Text style={styles.tableCellRight}>Bonus Rate</Text>
            <Text style={styles.tableCellRight}>Bonus Amount</Text>
            <Text style={styles.tableCellRight}>Deductions</Text>
            <Text style={styles.tableCellRight}>Net Bonus</Text>
          </View>

          {/* Table Rows */}
          {reportData.map((item, index) => (
            <View
              key={`row-${index}`}
              style={[
                styles.tableRow,
                { backgroundColor: index % 2 === 0 ? '#FFFFFF' : '#F9F9F9' },
              ]}
            >
              <Text style={styles.tableCell}>{item.loader_name}</Text>
              <Text style={styles.tableCell}>{item.month}</Text>
              <Text style={styles.tableCell}>{item.pol_name}</Text>
              <Text style={styles.tableCellRight}>
                {item.loaded_containers}
              </Text>
              <Text style={styles.tableCellRight}>{item.damaged_vehicles}</Text>
              <Text style={styles.tableCellRight}>
                {formatCurrency(item.bonus_per_container)}
              </Text>
              <Text style={styles.tableCellRight}>
                {formatCurrency(item.bonus_without_damages)}
              </Text>
              <Text style={styles.tableCellRight}>
                {formatCurrency(item.total_deducted)}
              </Text>
              <Text style={styles.tableCellRight}>
                {formatCurrency(item.net_bonus)}
              </Text>
            </View>
          ))}
        </View>

        {/* Summary Section */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Summary</Text>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Containers Loaded:</Text>
            <Text style={styles.summaryValue}>{totals.containers}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Damaged Vehicles:</Text>
            <Text style={styles.summaryValue}>{totals.damages}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Bonus Amount:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(totals.bonus)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Deductions:</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(totals.deductions)}
            </Text>
          </View>

          <View style={styles.finalTotal}>
            <Text style={styles.finalTotalLabel}>Net Bonus Amount:</Text>
            <Text style={styles.finalTotalValue}>
              {formatCurrency(totals.netBonus)}
            </Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text>
            This report was generated on {new Date().toLocaleDateString()} at{' '}
            {new Date().toLocaleTimeString()}
          </Text>
        </View>

        {/* Page Number */}
        <Text
          style={styles.pageNumber}
          render={({ pageNumber, totalPages }) =>
            `${pageNumber} / ${totalPages}`
          }
          fixed
        />
      </Page>
    </Document>
  );
};

export default BonusReportPDF;
