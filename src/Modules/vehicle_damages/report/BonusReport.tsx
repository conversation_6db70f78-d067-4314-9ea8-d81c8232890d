import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Button,
  CircularProgress,
  Divider,
  ButtonGroup,
  Container,
  Tab,
  Tabs,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import axios from '@/lib/axios';
import toast from 'react-hot-toast';
import { format, startOfMonth, endOfMonth } from 'date-fns';
import { pdf } from '@react-pdf/renderer';
import BonusReportPDF from './BonusReportPDF';
import { CsvDownload } from '@/components/mainComponents/pdf&csv/CsvDownload';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import Head from 'next/head';
import { contextProvider } from '@/contexts/ProfileContext';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import { useDamageHeaderInfo } from '@/configs/vehicles_damage/damageHeader';

type LoaderBonusData = {
  loader_id: number;
  loader_name: string;
  month: string;
  pol_id: number;
  pol_name: string;
  loaded_containers: number;
  damaged_vehicles: number;
  bonus_per_container: number;
  bonus_without_damages: number;
  total_deducted: number;
  net_bonus: number;
};

const BonusReport = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const damageHeaderInfo = useDamageHeaderInfo();
  const [activeTab, setActiveTab] = useState(1); // Set default to Bonus Reports (index 1)

  // State variables
  const [loading, setLoading] = useState<boolean>(false);
  const [pdfLoading, setPdfLoading] = useState<boolean>(false);
  const [locations, setLocations] = useState<any[]>([]);
  const [loaders, setLoaders] = useState<any[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<number | ''>('');
  const [selectedLocationName, setSelectedLocationName] = useState<string>('');
  const [selectedLoader, setSelectedLoader] = useState<number | ''>('');
  const [selectedLoaderName, setSelectedLoaderName] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(
    startOfMonth(new Date()),
  );
  const [endDate, setEndDate] = useState<Date | null>(endOfMonth(new Date()));
  const [reportData, setReportData] = useState<LoaderBonusData[]>([]);
  const [totals, setTotals] = useState({
    containers: 0,
    damages: 0,
    bonus: 0,
    deductions: 0,
    netBonus: 0,
  });

  // Handle tab change
  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  // Fetch locations and loaders on component mount
  useEffect(() => {
    fetchLocations();
    fetchLoaders();
  }, []);

  // Fetch POL locations
  const fetchLocations = async () => {
    try {
      const response = await axios.get('/location/getall');
      if (response.data.status === 'success') {
        setLocations(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast.error('Failed to load locations');
    }
  };

  // Fetch loaders
  const fetchLoaders = async () => {
    try {
      const response = await axios.get('/loaders/getall');
      if (response.data.status === 'success') {
        setLoaders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching loaders:', error);
      toast.error('Failed to load loaders');
    }
  };

  // Handle location change
  const handleLocationChange = (event) => {
    const locationId = event.target.value;
    setSelectedLocation(locationId);

    if (locationId) {
      const location = locations.find((loc) => loc.id === locationId);
      setSelectedLocationName(location?.name || '');
    } else {
      setSelectedLocationName('');
    }
  };

  // Handle loader change
  const handleLoaderChange = (event) => {
    const loaderId = event.target.value;
    setSelectedLoader(loaderId);

    if (loaderId) {
      const loader = loaders.find((l) => l.id === loaderId);
      setSelectedLoaderName(loader?.name || '');
    } else {
      setSelectedLoaderName('');
    }
  };

  // Generate report
  const generateReport = async () => {
    if (!selectedLocation) {
      toast.error('Please select a Port of Loading');
      return;
    }

    if (!startDate || !endDate) {
      toast.error('Please select start and end dates');
      return;
    }

    setLoading(true);
    try {
      const params: any = {
        startDate: format(startDate, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd'),
        polId: selectedLocation,
      };

      if (selectedLoader) {
        params.loaderId = selectedLoader;
      }

      const response = await axios.get(
        '/vehicle_damages/reports/loader-bonus',
        { params },
      );

      if (response.data.status === 'success') {
        setReportData(response.data.data);
        calculateTotals(response.data.data);
      } else {
        toast.error('Failed to fetch report data');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals for summary
  const calculateTotals = (data: LoaderBonusData[]) => {
    const newTotals = data.reduce(
      (acc, item) => {
        return {
          containers: acc.containers + item.loaded_containers,
          damages: acc.damages + item.damaged_vehicles,
          bonus: acc.bonus + item.bonus_without_damages,
          deductions: acc.deductions + item.total_deducted,
          netBonus: acc.netBonus + item.net_bonus,
        };
      },
      { containers: 0, damages: 0, bonus: 0, deductions: 0, netBonus: 0 },
    );

    setTotals(newTotals);
  };

  // Export to CSV
  const exportToCSV = () => {
    if (reportData.length === 0) {
      toast.error('No data to export');
      return;
    }

    const headers = [
      'Loader Name',
      'Month',
      'POL',
      'Loaded Containers',
      'Damaged Vehicles',
      'Bonus Per Container',
      'Bonus Without Damages',
      'Total Deducted',
      'Net Bonus',
    ];

    const data = reportData.map((item) => [
      item.loader_name,
      item.month,
      item.pol_name,
      item.loaded_containers,
      item.damaged_vehicles,
      `$${item.bonus_per_container}`,
      `$${item.bonus_without_damages}`,
      `$${item.total_deducted}`,
      `$${item.net_bonus}`,
    ]);

    CsvDownload(
      [headers, ...data],
      `Loader_Bonus_Report_${format(new Date(), 'yyyy-MM-dd')}.csv`,
    );
  };

  // Generate and download PDF
  const generatePDF = async () => {
    if (reportData.length === 0) {
      toast.error('No data to export to PDF');
      return;
    }

    setPdfLoading(true);
    try {
      const filters = {
        startDate: startDate || new Date(),
        endDate: endDate || new Date(),
        polId: selectedLocation,
        polName: selectedLocationName,
        loaderId: selectedLoader,
        loaderName: selectedLoaderName,
      };

      const blob = await pdf(
        <BonusReportPDF
          reportData={reportData}
          filters={filters}
          totals={totals}
        />,
      ).toBlob();

      // Create a URL for the blob and trigger download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Loader_Bonus_Report_${format(new Date(), 'yyyy-MM-dd')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF');
    } finally {
      setPdfLoading(false);
    }
  };

  // Format month string for display
  const formatMonth = (monthString: string) => {
    const [year, month] = monthString.split('-');
    return `${year}-${month}`;
  };

  // Check if user has report view permission
  if (!perms?.includes(DAMAGE?.REPORT_VIEW)) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="80vh"
        fontWeight="bold"
        fontSize="20px"
      >
        Unauthorized - You need report view permission to access this page
      </Box>
    );
  }

  const renderBonusReport = () => {
    return (
      <>
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Loader Bonus Report Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid
              size={{
                xs: 12,
                md: 3,
              }}
            >
              <FormControl fullWidth>
                <InputLabel id="location-select-label">
                  Port of Loading
                </InputLabel>
                <Select
                  labelId="location-select-label"
                  value={selectedLocation}
                  label="Port of Loading"
                  onChange={handleLocationChange}
                >
                  {locations.map((location) => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 3,
              }}
            >
              <FormControl fullWidth>
                <InputLabel id="loader-select-label">
                  Loader (Optional)
                </InputLabel>
                <Select
                  labelId="loader-select-label"
                  value={selectedLoader}
                  label="Loader (Optional)"
                  onChange={handleLoaderChange}
                >
                  <MenuItem value="">All Loaders</MenuItem>
                  {loaders.map((loader) => (
                    <MenuItem key={loader.id} value={loader.id}>
                      {loader.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 2,
              }}
            >
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={startDate}
                  onChange={(newValue) => setStartDate(newValue)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 2,
              }}
            >
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="End Date"
                  value={endDate}
                  onChange={(newValue) => setEndDate(newValue)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid
              sx={{ display: 'flex', alignItems: 'center' }}
              size={{
                xs: 12,
                md: 2,
              }}
            >
              <Button
                variant="contained"
                fullWidth
                onClick={generateReport}
                disabled={loading}
              >
                {loading ? <CircularProgress size={24} /> : 'Generate Report'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
        {reportData.length > 0 && (
          <Paper elevation={3} sx={{ p: 3 }}>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}
            >
              <Typography variant="h6">Loader Bonus Report Results</Typography>
              <ButtonGroup>
                <Button
                  variant="outlined"
                  startIcon={<FileDownloadIcon />}
                  onClick={exportToCSV}
                >
                  Export to CSV
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<PictureAsPdfIcon />}
                  onClick={generatePDF}
                  disabled={pdfLoading}
                >
                  {pdfLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    'Export to PDF'
                  )}
                </Button>
              </ButtonGroup>
            </Box>

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                    <TableCell>Loader Name</TableCell>
                    <TableCell>Month</TableCell>
                    <TableCell>POL</TableCell>
                    <TableCell align="right">Loaded Containers</TableCell>
                    <TableCell align="right">Damaged Vehicles</TableCell>
                    <TableCell align="right">Bonus Per Container</TableCell>
                    <TableCell align="right">Bonus Without Damages</TableCell>
                    <TableCell align="right">Total Deducted</TableCell>
                    <TableCell align="right">Net Bonus</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {reportData.map((row, index) => (
                    <TableRow
                      key={index}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        },
                      }}
                    >
                      <TableCell>{row.loader_name}</TableCell>
                      <TableCell>{formatMonth(row.month)}</TableCell>
                      <TableCell>{row.pol_name}</TableCell>
                      <TableCell align="right">
                        {row.loaded_containers}
                      </TableCell>
                      <TableCell align="right">
                        {row.damaged_vehicles}
                      </TableCell>
                      <TableCell align="right">
                        ${row.bonus_per_container}
                      </TableCell>
                      <TableCell align="right">
                        ${row.bonus_without_damages}
                      </TableCell>
                      <TableCell align="right">${row.total_deducted}</TableCell>
                      <TableCell align="right">${row.net_bonus}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 3 }} />

            <Box sx={{ backgroundColor: '#f9f9f9', p: 2, borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid
                  size={{
                    xs: 12,
                    md: 2.4,
                  }}
                >
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      Total Containers
                    </Typography>
                    <Typography variant="h6">{totals.containers}</Typography>
                  </Paper>
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 2.4,
                  }}
                >
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      Total Damages
                    </Typography>
                    <Typography variant="h6">{totals.damages}</Typography>
                  </Paper>
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 2.4,
                  }}
                >
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      Total Bonus
                    </Typography>
                    <Typography variant="h6">${totals.bonus}</Typography>
                  </Paper>
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 2.4,
                  }}
                >
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      Total Deductions
                    </Typography>
                    <Typography variant="h6">${totals.deductions}</Typography>
                  </Paper>
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 2.4,
                  }}
                >
                  <Paper
                    elevation={2}
                    sx={{
                      p: 2,
                      textAlign: 'center',
                      backgroundColor: '#e3f2fd',
                    }}
                  >
                    <Typography variant="body2" color="textSecondary">
                      Net Bonus Amount
                    </Typography>
                    <Typography variant="h6" color="primary">
                      ${totals.netBonus}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}
        {reportData.length === 0 && !loading && (
          <Paper elevation={3} sx={{ p: 5, textAlign: 'center' }}>
            <Typography variant="body1" color="textSecondary">
              No data available. Please select filters and generate the report.
            </Typography>
          </Paper>
        )}
        {loading && (
          <Paper elevation={3} sx={{ p: 5, textAlign: 'center' }}>
            <CircularProgress />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Loading report data...
            </Typography>
          </Paper>
        )}
      </>
    );
  };

  return (
    <>
      <Head>
        <title>Damages Report</title>
      </Head>
      <PageHeader breadcrumbs={damageHeaderInfo} />
      <Container maxWidth="xl">
        <Box sx={{ width: '100%', mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            textColor="primary"
            indicatorColor="primary"
            aria-label="Damage reports tabs"
          >
            <Tab label="All" />
            <Tab label="Bonus Reports" />
            <Tab label="Damaged Parts" />
            <Tab label="Damages Received" />
            <Tab label="Per POL" />
            <Tab label="Loaded Comparison" />
            <Tab label="Customers" />
            <Tab label="Overall Customers" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
          {activeTab === 0 && (
            <Box>
              <h3>All Damages Report</h3>
              <p>Reports dashboard will be implemented here.</p>
            </Box>
          )}
          {activeTab === 1 && renderBonusReport()}
          {activeTab === 2 && (
            <Box>
              <h3>Damaged Parts</h3>
              <p>Damaged parts statistics will be displayed here.</p>
            </Box>
          )}
          {activeTab === 3 && (
            <Box>
              <h3>Damages Received</h3>
              <p>Damages received reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 4 && (
            <Box>
              <h3>Per POL</h3>
              <p>POL-based damage reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 5 && (
            <Box>
              <h3>Loaded Comparison</h3>
              <p>Loaded comparison reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 6 && (
            <Box>
              <h3>Customers</h3>
              <p>Customer-based damage reports will be displayed here.</p>
            </Box>
          )}
          {activeTab === 7 && (
            <Box>
              <h3>Overall Customers</h3>
              <p>Overall customer damage reports will be displayed here.</p>
            </Box>
          )}
        </Box>
      </Container>
    </>
  );
};

export default BonusReport;
