import React, { useState, useEffect } from 'react';
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Tab,
  Tooltip,
} from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import ImageSlideDialog from '@/components/mainComponents/cModal/ImageSlideDialog';
import { ImageNotSupported } from '@mui/icons-material';
import axios from '@/lib/axios';

export default function VehicleImagesModal({
  open,
  setOpen,
  vehicleData,
  imageType = 'all', // Default to 'all' if not specified
}) {
  const [value, setValue] = useState('1');
  const [openSlider, setOpenSlider] = useState(false);
  const [imageIndex, setImageIndex] = useState(0);
  const [fullSizeImages, setFullSizeImages] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [filteredImages, setFilteredImages] = useState([]);

  // Filter images based on imageType
  useEffect(() => {
    const fetchImages = async () => {
      if (!vehicleData?.id || !imageType) return;

      if (imageType === 'auction' || imageType === 'warehouse') {
        try {
          const { data } = await axios.get(
            `/vehicles/vehicle-images?id=${vehicleData.id}&type=${imageType}`,
          );
          setFilteredImages(data ?? []);
        } catch (error) {
          console.error('Error fetching vehicle images:', error);
          setFilteredImages([]);
        }
      }
      setCurrentIndex(0);
    };

    fetchImages();
  }, [vehicleData, imageType]);

  const handleChange = (_event, newValue) => {
    setValue(newValue);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDoubleClick = (index) => {
    setImageIndex(index);

    // Convert thumbnail URLs to full-size URLs for the slider
    const fullSizeImagesWithUpdatedUrls = filteredImages.map((image) => ({
      ...image,
      url: image.url.replace(/\/250\//, '/1024/'), // Convert thumbnail to full-size
    }));

    setFullSizeImages(fullSizeImagesWithUpdatedUrls);
    setOpenSlider(true);
  };

  const handleImageLoad = () => {
    if (currentIndex < (filteredImages.length || 0) - 1) {
      setCurrentIndex((prev) => prev + 1);
    }
  };

  const handleImageError = (index, event) => {
    console.error('Image failed to load:', index, event);
  };

  // Determine if we should show Google Drive tab based on imageType
  const vehicleHasGoogleDriveImages =
    (imageType === 'auction' && vehicleData?.auction_photos_link) ||
    (imageType === 'warehouse' && vehicleData?.photo_link) ||
    (imageType === 'all' &&
      (vehicleData?.auction_photos_link || vehicleData?.photo_link));

  // Get the appropriate Google Drive link based on imageType
  const getGoogleDriveLink = () => {
    if (imageType === 'auction') return vehicleData?.auction_photos_link;
    if (imageType === 'warehouse') return vehicleData?.photo_link;
    return vehicleData?.auction_photos_link || vehicleData?.photo_link;
  };

  // Get the modal title based on imageType
  const getModalTitle = () => {
    if (imageType === 'auction') return 'Auction Images';
    if (imageType === 'warehouse') return 'Warehouse Images';
    return 'Vehicle Images';
  };

  // Get image URL for production
  const getImageUrl = (image) => {
    // Check if the URL already has the MINIO_ENDPOINT
    if (image.url.startsWith('http')) {
      return image.url;
    }

    const url = `${process.env.NEXT_PUBLIC_MINIO_ENDPOINT}${image.url}`;
    return url;
  };

  // Debug the environment variable
  useEffect(() => {}, []);

  return (
    <>
      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ m: 0, p: 2 }}>
          {getModalTitle()} ({filteredImages.length} images)
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: 'grey',
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ width: '100%', typography: 'body1' }}>
            <TabContext value={value}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <TabList onChange={handleChange} aria-label="image tabs">
                  <Tab
                    label={`Vehicle Images (${filteredImages.length})`}
                    value="1"
                  />
                  {vehicleHasGoogleDriveImages && (
                    <Tab label="Google Drive" value="2" />
                  )}
                </TabList>
              </Box>
              <TabPanel value="1">
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    justifyContent: 'flex-start',
                    gap: 1,
                  }}
                >
                  {filteredImages.length > 0 ? (
                    filteredImages?.map((image, index) => (
                      <Tooltip
                        key={image.id}
                        title={`${image.name} (${image.type})`}
                      >
                        <Box
                          component="img"
                          id={`image-${image.id}`}
                          src={getImageUrl(image)}
                          alt={image.name}
                          onDoubleClick={() => handleDoubleClick(index)}
                          onLoad={() => handleImageLoad()}
                          onError={(e) => handleImageError(index, e)}
                          sx={{
                            width: '100px',
                            height: '100px',
                            objectFit: 'cover',
                            borderRadius: '8px',
                            margin: '5px',
                            cursor: 'pointer',
                            aspectRatio: '1/1', // Ensure 1:1 aspect ratio
                          }}
                        />
                      </Tooltip>
                    ))
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '100%',
                        height: '200px',
                      }}
                    >
                      <ImageNotSupported sx={{ fontSize: 48, color: '#ccc' }} />
                      <Typography variant="body1" color="textSecondary">
                        No images available for {imageType} type
                      </Typography>
                    </Box>
                  )}
                </Box>
              </TabPanel>
              {vehicleHasGoogleDriveImages && (
                <TabPanel value="2">
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: 'auto',
                      padding: '20px',
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Google Drive Images
                    </Typography>

                    <a
                      href={getGoogleDriveLink()}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        textDecoration: 'none',
                        color: '#3f50b5',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        marginTop: '20px',
                        padding: '20px',
                        border: '2px solid #e0e0e0',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                      }}
                      onMouseOver={(e) => {
                        e.currentTarget.style.boxShadow =
                          '0 4px 8px rgba(0,0,0,0.2)';
                        e.currentTarget.style.borderColor = '#3f50b5';
                      }}
                      onMouseOut={(e) => {
                        e.currentTarget.style.boxShadow =
                          '0 2px 4px rgba(0,0,0,0.1)';
                        e.currentTarget.style.borderColor = '#e0e0e0';
                      }}
                    >
                      <Box
                        component="img"
                        src="/images/google-drive.png"
                        alt="Google Drive"
                        sx={{ width: 80, height: 80, mb: 2 }}
                      />
                      <Typography
                        variant="body1"
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        View in Google Drive
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Click to open the images in Google Drive
                      </Typography>
                    </a>
                  </Box>
                </TabPanel>
              )}
            </TabContext>
          </Box>
        </DialogContent>
      </Dialog>

      {openSlider && (
        <ImageSlideDialog
          url=""
          files={fullSizeImages}
          setFullSizeImages={setFullSizeImages}
          imageIndex={imageIndex}
          setOpen={setOpenSlider}
          open={openSlider}
          type={imageType}
          getVehicleImages={() => {}}
          setFetchingRecords={() => {}}
          isDeleteImage={false}
        />
      )}
    </>
  );
}
