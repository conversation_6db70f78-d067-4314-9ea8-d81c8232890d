import React, { useState, useMemo } from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  useTheme,
  Link,
  Tooltip,
  Pagination,
  PaginationItem,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddCardIcon from '@mui/icons-material/AddCard';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import {
  damage_type_options,
  damage_happened_at_options,
} from '@/configs/vehicles_damage/damageHeader';
import Skeleton from '@mui/material/Skeleton';
import ImageIcon from '@mui/icons-material/Image';
import { ContentCopy as ContentCopyIcon } from '@mui/icons-material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { formatIDWithPattern } from '@/lib/utils';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';

// Format currency helper function since it's not imported
const formatMoney = (amount, currency = 'USD') => {
  if (!amount) return '0';
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
  });
  return formatter.format(amount);
};

// Simple skeleton component for loading state
const InitialReviewSkeleton = () => (
  <Box sx={{ width: '100%', mt: 2 }}>
    <Skeleton variant="rectangular" height={60} />
    <Box sx={{ mt: 1 }}>
      <Skeleton variant="rectangular" height={40} />
      <Skeleton variant="rectangular" height={40} sx={{ mt: 1 }} />
      <Skeleton variant="rectangular" height={40} sx={{ mt: 1 }} />
    </Box>
  </Box>
);

interface PreCreditDamagesProps {
  damages: any[];
  onViewDamage: (damage: any) => void;
  onStatusChange?: () => void; // Callback to refresh data after status change
  permissions?: string[]; // User permissions
}

interface ExchangeRateType {
  currency: string;
  rate: number;
  created_at: string;
}

const PreCreditDamages: React.FC<PreCreditDamagesProps> = ({
  damages,
  onViewDamage,
  onStatusChange,
  permissions = [],
}) => {
  const theme = useTheme();
  const [expandedPanels, setExpandedPanels] = useState<Record<string, boolean>>(
    {},
  );
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [currentGroup, setCurrentGroup] = useState<any>(null);
  const [creditedDate, setCreditedDate] = useState<Date | null>(new Date());
  const [loading, setLoading] = useState(false);
  const [isCopied, setIsCopied] = useState<{ [key: string]: boolean }>({});
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleAccordionToggle = (caseNumber: string) => {
    setExpandedPanels((prev) => ({
      ...prev,
      [caseNumber]: !prev[caseNumber],
    }));
  };

  // Open confirmation modal
  const handleConfirmClick = (group: any, event: React.MouseEvent) => {
    // Prevent accordion from toggling when clicking the button
    event.stopPropagation();
    setCurrentGroup(group);
    setCreditedDate(new Date()); // Reset to current date when opening modal
    setConfirmModalOpen(true);
  };

  // Close confirmation modal
  const handleCloseModal = () => {
    setConfirmModalOpen(false);
    setCurrentGroup(null);
    setCreditedDate(new Date());
  };

  // Handle confirmation to change status to Credited
  const handleConfirmStatus = async () => {
    if (!currentGroup) return;

    setLoading(true);
    try {
      const damageIds = currentGroup.items.map((damage) => damage.id);

      // START: handle adding customer credit record
      const damageCredits = {
        company_id: currentGroup?.items[0]?.vehicles?.customers?.companies?.id,
        customer_id: currentGroup?.items[0]?.vehicles?.customers?.id,
        payment_method: 'damage_credit',
        amount: currentGroup?.totalCredit,
        service_charge: 0,
        exchange_rate:
          currentGroup?.creditCurrency === 'USD'
            ? 1
            : currentGroup?.items[0]?.vehicles?.customers?.companies?.exchange_rates
                .filter(
                  (exchange_rate: ExchangeRateType) =>
                    exchange_rate?.currency === currentGroup?.creditCurrency,
                )
                .sort(
                  (a: { created_at: string }, b: { created_at: string }) =>
                    new Date(b.created_at).getTime() -
                    new Date(a.created_at).getTime(),
                )[0]?.rate,
        currency: currentGroup?.creditCurrency,
        transaction_number: formatIDWithPattern(
          'PGLDMG',
          currentGroup.caseNumber,
          4,
        ),
        credit_date: creditedDate
          ? creditedDate.toISOString()
          : new Date().toISOString(),
        customer_credits: currentGroup?.items.map((item) => {
          return {
            id: item?.id,
            vehicle_id: item?.vehicle_id,
            amount: item?.credit,
            remark: item?.remark,
          };
        }),
      };
      // END: handle adding customer credit record

      await axios.post(`vehicle_damages/bulk-update`, {
        ids: damageIds,
        field: 'damage_status',
        value: 'Credited',
        credited_at: creditedDate
          ? creditedDate.toISOString()
          : new Date().toISOString(),
        damage_credits: damageCredits,
      });

      toast.success(
        `Successfully moved ${currentGroup.items.length} damages to Credited status with date ${creditedDate ? creditedDate.toLocaleDateString() : 'today'}. You can now view them in the Credited tab.`,
      );

      setConfirmModalOpen(false);
      setCurrentGroup(null);

      // Refresh data if callback provided
      if (onStatusChange) {
        onStatusChange();
      }
    } catch (error) {
      toast.error(error.response?.data?.message);
      setConfirmModalOpen(false);
      setCurrentGroup(null);
    } finally {
      setLoading(false);
    }
  };

  // Group damages by case_number
  const groupedDamages = useMemo(() => {
    const groups: Record<string, any[]> = {};

    // First, group all damages by case_number
    damages.forEach((damage) => {
      const caseNumber = damage.case_number || 'No Case Number';
      if (!groups[caseNumber]) {
        groups[caseNumber] = [];
      }
      groups[caseNumber].push(damage);
    });

    // Convert to array for rendering
    return Object.entries(groups)
      .map(([caseNumber, items]) => {
        // Calculate total credit amount for this group
        const totalCredit = items.reduce((sum, damage) => {
          const creditAmount = parseFloat(damage.credit) || 0;
          return sum + creditAmount;
        }, 0);

        // Get currency from the first item that has one
        const creditCurrency =
          items.find((item) => item.credit_currency)?.credit_currency || '';

        return {
          caseNumber,
          items,
          // Get customer name from the first item
          customerName:
            items[0]?.vehicles?.customers?.companies?.name ||
            'Unknown Customer',
          // Count total damages in this case
          count: items.length,
          totalCredit,
          creditCurrency,
        };
      })
      .sort((a, b) => {
        // Sort by case number in descending order (latest first)
        if (a.caseNumber === 'No Case Number') return 1;
        if (b.caseNumber === 'No Case Number') return -1;
        return parseInt(b.caseNumber) - parseInt(a.caseNumber);
      });
  }, [damages]);

  // Helper function to render damage_happened_at values
  const renderDamageHappenedAt = (damage: any) => {
    if (
      !damage.damage_happened_at ||
      !Array.isArray(damage.damage_happened_at) ||
      damage.damage_happened_at.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_happened_at.map((happened_at, index) => {
          const damageOption = damage_happened_at_options.find(
            (item) => item.id === happened_at.happened_at,
          );
          const color = damageOption ? damageOption.color : '#6A5ACD';

          return (
            <Chip
              key={index}
              label={happened_at.happened_at}
              size="small"
              sx={{
                backgroundColor: color,
                color: 'white',
                fontSize: '10px',
                fontWeight: 'bold',
              }}
            />
          );
        })}
      </Box>
    );
  };

  // Helper function to render damage_details values
  const renderDamageDetails = (damage: any) => {
    if (
      !damage.damage_details ||
      !Array.isArray(damage.damage_details) ||
      damage.damage_details.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_details.map((detail, index) => (
          <Chip
            key={index}
            label={detail.detail}
            size="small"
            sx={{
              backgroundColor: '#3f50b5',
              color: 'white',
              fontSize: '10px',
              fontWeight: 'bold',
            }}
          />
        ))}
      </Box>
    );
  };

  // Helper function to render damage type with color
  const renderDamageType = (damage: any) => {
    if (!damage.damage_type) return 'N/A';

    const damageOption = damage_type_options.find(
      (item) => item.id === damage.damage_type,
    );
    const color = damageOption ? damageOption.color : '#3f50b5';

    return (
      <Chip
        label={damage.damage_type}
        size="small"
        sx={{
          backgroundColor: color,
          color: 'white',
          fontSize: '10px',
          fontWeight: 'bold',
        }}
      />
    );
  };

  // Helper function to render POL
  const renderPOL = (vehicle: any) => {
    return vehicle?.pol_locations?.name || 'N/A';
  };

  // New helper function to copy text to clipboard
  const copyToClipboard = (text: string, type: string, caseNumber: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Set the copied state for this specific item
        setIsCopied({
          ...isCopied,
          [`${caseNumber}_${type}`]: true,
        });

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied((prev) => ({
            ...prev,
            [`${caseNumber}_${type}`]: false,
          }));
        }, 2000);

        toast.success(`${type} copied to clipboard`);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
        toast.error('Failed to copy to clipboard');
      });
  };

  // Handle page change
  const handlePageChange = (_: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  if (!damages || damages.length === 0) {
    return <InitialReviewSkeleton />;
  }

  return (
    <Box
      sx={{
        my: 3,
        display: 'flex',
        flexDirection: 'column',
        minHeight: 'calc(100vh - 200px)',
        position: 'relative',
      }}
    >
      {groupedDamages.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">No pre-credit damages found.</Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ flex: '1 0 auto', mb: 8 }}>
            {groupedDamages
              .slice((page - 1) * rowsPerPage, page * rowsPerPage)
              .map((group) => (
                <Accordion
                  key={group.caseNumber}
                  expanded={!!expandedPanels[group.caseNumber]}
                  onChange={() => handleAccordionToggle(group.caseNumber)}
                  sx={{
                    mb: 1,
                    bgcolor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'transparent',
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`panel-${group.caseNumber}-content`}
                    id={`panel-${group.caseNumber}-header`}
                    sx={{
                      bgcolor: 'rgba(46, 125, 50, 0.12)',
                      '&:hover': { bgcolor: 'rgba(46, 125, 50, 0.20)' },
                    }}
                  >
                    <Grid container alignItems="center" spacing={1}>
                      <Grid
                        size={{
                          xs: 6,
                          sm: 5,
                          md: 3,
                          lg: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                            flexWrap: { xs: 'nowrap', sm: 'nowrap' },
                            width: '100%',
                          }}
                        >
                          <Typography
                            fontWeight={600}
                            sx={{
                              whiteSpace: 'nowrap',
                              mr: 0.5,
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              const caseNumberText =
                                group.caseNumber !== 'No Case Number'
                                  ? formatIDWithPattern(
                                      'PGLDMG',
                                      group.caseNumber,
                                      4,
                                    )
                                  : 'No Case #';
                              copyToClipboard(
                                caseNumberText,
                                'Case number',
                                group.caseNumber,
                              );
                            }}
                          >
                            {group.caseNumber !== 'No Case Number'
                              ? formatIDWithPattern(
                                  'PGLDMG',
                                  group.caseNumber,
                                  4,
                                )
                              : 'No Case #'}
                            <Tooltip
                              title={
                                isCopied[`${group.caseNumber}_Case number`]
                                  ? 'Copied!'
                                  : 'Copy to clipboard'
                              }
                            >
                              <ContentCopyIcon
                                sx={{
                                  ml: 0.5,
                                  fontSize: '0.8rem',
                                  color: isCopied[
                                    `${group.caseNumber}_Case number`
                                  ]
                                    ? 'success.main'
                                    : 'action.active',
                                  opacity: isCopied[
                                    `${group.caseNumber}_Case number`
                                  ]
                                    ? 1
                                    : 0.7,
                                  '&:hover': { opacity: 1 },
                                }}
                              />
                            </Tooltip>
                          </Typography>
                          <Chip
                            label={group.count}
                            size="small"
                            color="primary"
                            sx={{ height: 20, minWidth: 24 }}
                          />
                        </Box>
                      </Grid>
                      <Grid
                        size={{
                          xs: 6,
                          sm: 7,
                          md: 4,
                          lg: 3,
                        }}
                      >
                        <Typography
                          noWrap
                          sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            width: '100%',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(
                              group.customerName,
                              'Customer name',
                              group.caseNumber,
                            );
                          }}
                        >
                          {group.customerName}
                          <Tooltip
                            title={
                              isCopied[`${group.caseNumber}_Customer name`]
                                ? 'Copied!'
                                : 'Copy to clipboard'
                            }
                          >
                            <ContentCopyIcon
                              sx={{
                                ml: 0.5,
                                fontSize: '0.8rem',
                                color: isCopied[
                                  `${group.caseNumber}_Customer name`
                                ]
                                  ? 'success.main'
                                  : 'action.active',
                                opacity: isCopied[
                                  `${group.caseNumber}_Customer name`
                                ]
                                  ? 1
                                  : 0.7,
                                '&:hover': { opacity: 1 },
                              }}
                            />
                          </Tooltip>
                        </Typography>
                      </Grid>
                      <Grid
                        size={{
                          xs: 6,
                          sm: 6,
                          md: 2,
                          lg: 2,
                        }}
                      >
                        {group.totalCredit > 0 ? (
                          <Chip
                            icon={<CreditScoreIcon />}
                            label={`${group.totalCredit.toFixed(2)} ${group.creditCurrency}`}
                            color="success"
                            size="small"
                            sx={{
                              fontWeight: 'bold',
                              fontSize: { xs: '0.75rem', sm: '0.85rem' },
                            }}
                          />
                        ) : (
                          <Typography variant="body2">No credit</Typography>
                        )}
                      </Grid>
                      <Grid
                        sx={{
                          display: 'flex',
                          justifyContent: { xs: 'flex-start', md: 'flex-end' },
                          mt: { xs: 1, sm: 0 },
                        }}
                        size={{
                          xs: 6,
                          sm: 6,
                          md: 3,
                          lg: 5,
                        }}
                      >
                        {/* Only show Credit button if not already Credited and user has permission */}
                        {!group.items.every(
                          (item) => item.damage_status === 'Credited',
                        ) &&
                          permissions?.includes(DAMAGE?.GIVEN_CREDIT) && (
                            <Button
                              variant="contained"
                              size="small"
                              color="success"
                              onClick={(e) => handleConfirmClick(group, e)}
                              startIcon={<AddCardIcon />}
                            >
                              Credit
                            </Button>
                          )}
                      </Grid>
                    </Grid>
                  </AccordionSummary>
                  <AccordionDetails>
                    <TableContainer
                      component={Paper}
                      sx={{
                        mt: 2,
                        bgcolor:
                          theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'white',
                      }}
                    >
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell align="center">#</TableCell>
                            <TableCell align="center">Damage Type</TableCell>
                            <TableCell align="center">Damage Details</TableCell>
                            <TableCell align="center">Photos</TableCell>
                            <TableCell align="center">Happened At</TableCell>
                            <TableCell align="center">POL</TableCell>
                            <TableCell align="center">Description</TableCell>
                            <TableCell align="center">VIN#</TableCell>
                            <TableCell align="center">LOT#</TableCell>
                            <TableCell align="center">Container#</TableCell>
                            <TableCell align="center">Credit Amount</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {group.items.map((damage, index) => (
                            <TableRow
                              key={damage.id}
                              hover
                              onClick={() => onViewDamage(damage)}
                              sx={{
                                cursor: 'pointer',
                                '&:hover': { backgroundColor: '#f0f7ff' },
                                '&:nth-of-type(odd)': {
                                  bgcolor:
                                    theme.palette.mode === 'dark'
                                      ? 'rgba(255, 255, 255, 0.03)'
                                      : 'rgba(0, 0, 0, 0.03)',
                                },
                              }}
                            >
                              <TableCell align="center">{index + 1}</TableCell>
                              <TableCell align="center">
                                {renderDamageType(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {renderDamageDetails(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {damage.inspection_photo ? (
                                  <Link
                                    href={damage.inspection_photo}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <ImageIcon color="primary" />
                                  </Link>
                                ) : (
                                  'N/A'
                                )}
                              </TableCell>
                              <TableCell align="center">
                                {renderDamageHappenedAt(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {renderPOL(damage.vehicles)}
                              </TableCell>
                              <TableCell align="center">
                                {`${damage.vehicles?.year || ''} ${damage.vehicles?.make || ''} ${damage.vehicles?.model || ''} ${damage.vehicles?.color || ''}`}
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'medium' }}
                              >
                                {damage.vehicles?.vin || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.vehicles?.lot_number || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.vehicles?.containers
                                  ?.container_number || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.credit
                                  ? `${damage.credit} ${damage.credit_currency || ''}`
                                  : 'N/A'}
                              </TableCell>
                            </TableRow>
                          ))}
                          {/* Summary Row */}
                          <TableRow
                            sx={{
                              backgroundColor:
                                theme.palette.mode === 'dark'
                                  ? 'rgba(255, 255, 255, 0.1)'
                                  : '#f9f9f9',
                            }}
                          >
                            <TableCell
                              colSpan={10}
                              align="right"
                              sx={{ fontWeight: 'bold' }}
                            >
                              Total Credit:
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ fontWeight: 'bold' }}
                            >
                              {group.totalCredit.toFixed(2)}{' '}
                              {group.creditCurrency}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </AccordionDetails>
                </Accordion>
              ))}
          </Box>

          {/* Pagination component - sticky at the bottom */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              py: 1,
              px: 2,
              borderTop: '1px solid',
              borderColor:
                theme.palette.mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.12)'
                  : 'rgba(0, 0, 0, 0.12)',
              bgcolor:
                theme.palette.mode === 'dark'
                  ? 'rgba(30, 30, 30, 0.9)'
                  : 'rgba(255, 255, 255, 0.9)',
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: 10,
              boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '0.75rem', mr: 1 }}>
              Rows per page:
            </Typography>
            <FormControl size="small">
              <Select
                value={rowsPerPage}
                onChange={(e) => {
                  setRowsPerPage(Number(e.target.value));
                  setPage(1);
                }}
                variant="standard"
                sx={{ mx: 1, fontSize: '0.75rem' }}
              >
                {[10, 20, 50, 100, 150, 200, 500].map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Typography variant="body2" sx={{ fontSize: '0.75rem', pr: 1 }}>
              {(page - 1) * rowsPerPage + 1}-
              {Math.min(page * rowsPerPage, groupedDamages.length)} of{' '}
              {groupedDamages.length}
            </Typography>
            <Pagination
              size="small"
              count={Math.ceil(groupedDamages.length / rowsPerPage)}
              page={page}
              onChange={handlePageChange}
              color="primary"
              renderItem={(item) => (
                <PaginationItem
                  slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
                  {...item}
                />
              )}
            />
          </Box>
        </>
      )}
      {/* Confirmation Modal */}
      <Dialog
        open={confirmModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="confirmation-dialog-title"
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor:
              theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'white',
          },
        }}
      >
        <DialogTitle id="confirmation-dialog-title">Confirm Credit</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to credit {currentGroup?.count || 0} damage
            {currentGroup?.count !== 1 ? 's' : ''} for{' '}
            {currentGroup?.customerName || ''}?
          </Typography>
          <Typography sx={{ mt: 2, fontWeight: 'bold' }}>
            Total credit amount:{' '}
            {currentGroup?.totalCredit > 0
              ? formatMoney(
                  currentGroup?.totalCredit,
                  currentGroup?.creditCurrency,
                )
              : 'No credit'}
          </Typography>

          {/* Date picker for credited_at */}
          <Box sx={{ mt: 3, mb: 2 }}>
            <Typography
              variant="subtitle1"
              sx={{ mb: 1, fontWeight: 'medium' }}
            >
              Set Credited Date:
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Credited Date"
                value={creditedDate}
                onChange={(newValue) => setCreditedDate(newValue)}
                slotProps={{
                  textField: {
                    size: 'small',
                    fullWidth: true,
                    helperText: "Default: Today's date",
                  },
                }}
              />
            </LocalizationProvider>
          </Box>

          <Typography sx={{ mt: 2, color: 'text.secondary' }}>
            This will change the status to Credited and cannot be reversed.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} color="inherit" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmStatus}
            color="success"
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? <CircularProgress size={20} /> : <AddCardIcon />
            }
          >
            {loading ? 'Processing...' : 'Confirm Credit'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PreCreditDamages;
