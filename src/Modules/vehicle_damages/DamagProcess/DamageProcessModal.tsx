import React, { useEffect, useState, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Modal,
  Card,
  IconButton,
  Divider,
  CardContent,
  CardActions,
  Grid,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ImageIcon from '@mui/icons-material/Image';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import {
  pol_options,
  damage_type_options,
  damage_happened_at_options,
} from '@/configs/vehicles_damage/damageHeader';
import InspectionPhotoModal from '../InspectionPhoto/InspectionPhotoModal';

interface DamageProcessModalProps {
  open: boolean;
  onClose: () => void;
  selectedItems: any[];
  onSuccess: () => void;
  profile?: any;
}

const DamageProcessModal: React.FC<DamageProcessModalProps> = ({
  open,
  onClose,
  selectedItems,
  onSuccess,
  // profile
}) => {
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [damages, setDamages] = useState<any[]>([]);
  const [formattedCaseNumber, setFormattedCaseNumber] = useState<string>('');
  const [maxCaseNumber, setMaxCaseNumber] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // States for inspection photo modal
  const [showInspectionPhotoModal, setShowInspectionPhotoModal] =
    useState(false);
  const [currentInspectionDamage, setCurrentInspectionDamage] = useState(null);
  // const [showVehicleImagesModal, setShowVehicleImagesModal] = useState(false);
  // const [showAuctionImagesModal, setShowAuctionImagesModal] = useState(false);
  // const [selectedVehicle, setSelectedVehicle] = useState(null);

  useEffect(() => {
    if (open && selectedItems && selectedItems.length > 0) {
      fetchDamageDetails();
      fetchMaxCaseNumber();
    } else if (open) {
    }
  }, [open, selectedItems]);

  const fetchMaxCaseNumber = async () => {
    try {
      // First check if selected damages already have a case number
      if (selectedItems && selectedItems.length > 0) {
        // Filter items that have case numbers
        const itemsWithCaseNumbers = selectedItems.filter(
          (item) => item.case_number,
        );

        // If any items have case numbers, use the first one
        if (itemsWithCaseNumbers.length > 0) {
          // Sort by case number to prioritize more recent cases (higher numbers)
          const sortedItems = [...itemsWithCaseNumbers].sort((a, b) => {
            return parseInt(b.case_number) - parseInt(a.case_number);
          });

          const existingCaseNumber = sortedItems[0].case_number;
          setMaxCaseNumber(existingCaseNumber);
          setFormattedCaseNumber(`PGLDMG0${existingCaseNumber}`);

          // Show info toast if we're reusing a case number
          if (selectedItems.length > itemsWithCaseNumbers.length) {
            toast.info(
              `Reusing existing case number PGLDMG0${existingCaseNumber} for all selected damages`,
            );
          }
          return;
        }
      }

      // If no existing case numbers, fetch a new one
      const { data } = await axios.get('vehicle_damages/max-case-number');
      const nextCaseNumber = data + 1;
      setMaxCaseNumber(nextCaseNumber);
      setFormattedCaseNumber(`PGLDMG0${nextCaseNumber}`);
    } catch (error) {
      // Fallback
      const newCaseNumber = Math.floor(Math.random() * 10000).toString();
      setMaxCaseNumber(parseInt(newCaseNumber));
      setFormattedCaseNumber(`PGLDMG0${newCaseNumber}`);
    }
  };

  const fetchDamageDetails = async () => {
    setLoading(true);

    try {
      // First, get all the damage details from the API
      const damageIds = selectedItems.map((item) => item.id);

      if (damageIds.length === 0) {
        toast.error('No valid items selected');
        setLoading(false);
        onClose();
        return;
      }

      try {
        const response = await axios.get(
          `vehicle_damages/multiple/${damageIds.join(',')}`,
        );

        if (!response.data.result || !response.data.data) {
          toast.error('Failed to retrieve damage details from server.');
          setLoading(false);
          onClose();
          return;
        }

        // Process and normalize the data
        let processedDamages = response.data.data;

        // Find a reference customer to use for damages without company info
        const referenceCustomer = processedDamages.find(
          (damage) =>
            damage?.vehicles?.customers?.companies?.id &&
            damage?.vehicles?.customers?.companies?.name,
        );

        const referenceCustomerId =
          referenceCustomer?.vehicles?.customers?.companies?.id ||
          'virtual-batch-' + Date.now();
        const referenceCustomerName =
          referenceCustomer?.vehicles?.customers?.companies?.name || 'Unknown';

        // Ensure every damage has customer and company information
        processedDamages = processedDamages.map((damage) => {
          // Log complete company details for debugging

          // If customers or companies is missing, add a placeholder
          if (!damage.vehicles.customers) {
            return {
              ...damage,
              vehicles: {
                ...damage.vehicles,
                customers: {
                  companies: {
                    id: referenceCustomerId,
                    name: referenceCustomerName,
                  },
                },
              },
            };
          }

          // If companies is missing, add a placeholder
          if (!damage.vehicles.customers.companies) {
            return {
              ...damage,
              vehicles: {
                ...damage.vehicles,
                customers: {
                  ...damage.vehicles.customers,
                  companies: {
                    id: referenceCustomerId,
                    name: referenceCustomerName,
                  },
                },
              },
            };
          }

          // If companies exists but id is missing
          if (!damage.vehicles.customers.companies.id) {
            return {
              ...damage,
              vehicles: {
                ...damage.vehicles,
                customers: {
                  ...damage.vehicles.customers,
                  companies: {
                    ...damage.vehicles.customers.companies,
                    id: referenceCustomerId,
                    name:
                      damage.vehicles.customers.companies.name ||
                      referenceCustomerName,
                  },
                },
              },
            };
          }

          // No changes needed
          return damage;
        });

        // Verify customer data after transformation

        // Immediately validate if items are from the same company
        if (processedDamages.length > 1) {
          // First, collect all company information including parent companies
          const companyInfo = processedDamages
            .map((damage) => ({
              id: damage?.vehicles?.customers?.companies?.id,
              name: damage?.vehicles?.customers?.companies?.name,
              parentId:
                damage?.vehicles?.customers?.companies?.parent_company?.id,
              parentName:
                damage?.vehicles?.customers?.companies?.parent_company?.name,
            }))
            .filter(
              (company) =>
                company.id &&
                !company.id.toString().startsWith('virtual-batch-'),
            );

          if (companyInfo.length > 1) {
            // First, check if all company names are the same
            const firstCompanyName = companyInfo[0].name;
            const allSameCompanyName = companyInfo.every(
              (company) => company.name === firstCompanyName,
            );

            // If all company names are the same, we can proceed immediately
            if (allSameCompanyName) {
              // Continue to process damages
            } else {
              // Direct check: If any company name matches any parent company name

              let directNameMatchFound = false;
              const companyNamesSet = new Set(companyInfo.map((c) => c.name));
              const parentNamesSet = new Set(
                companyInfo
                  .filter((c) => c.parentName)
                  .map((c) => c.parentName),
              );

              // Check if any company name appears in the parent names set
              for (const companyName of companyNamesSet) {
                if (parentNamesSet.has(companyName)) {
                  directNameMatchFound = true;
                  break;
                }
              }

              // If direct name match found, we can proceed
              if (directNameMatchFound) {
                // Continue to process damages
              } else {
                // If no direct name match, proceed with checking parent-child relationships

                // Create a map of company ID -> parent ID for quick lookup
                const parentMap = new Map();
                companyInfo.forEach((company) => {
                  if (company.parentId) {
                    parentMap.set(company.id, company.parentId);
                  }
                });

                // Check if all companies are related (same company or parent-child relationship)
                let areCompaniesRelated = true;

                // Get all unique company IDs
                const uniqueCompanyIds = [
                  ...new Set(companyInfo.map((c) => c.id)),
                ];

                if (uniqueCompanyIds.length > 1) {
                  // If we have more than one company, check if they're related
                  const firstCompanyId = uniqueCompanyIds[0];
                  const relatedIds = new Set([firstCompanyId]);

                  // Add parent if exists
                  if (parentMap.has(firstCompanyId)) {
                    relatedIds.add(parentMap.get(firstCompanyId));
                  }

                  // Check if all other companies are related to the first one
                  for (let i = 1; i < uniqueCompanyIds.length; i++) {
                    const currentId = uniqueCompanyIds[i];
                    const currentParentId = parentMap.get(currentId);

                    // Check if this company is already in our related set
                    if (relatedIds.has(currentId)) {
                      continue;
                    }

                    // Check if this company's parent is in our related set
                    if (currentParentId && relatedIds.has(currentParentId)) {
                      relatedIds.add(currentId);
                      continue;
                    }

                    // Check if this company is a parent of any company in our related set
                    let isParentOfRelated = false;
                    for (const relatedId of relatedIds) {
                      if (parentMap.get(relatedId) === currentId) {
                        isParentOfRelated = true;
                        break;
                      }
                    }

                    if (isParentOfRelated) {
                      relatedIds.add(currentId);
                      continue;
                    }

                    // If we reach here, this company is not related
                    areCompaniesRelated = false;
                    break;
                  }
                }

                if (!areCompaniesRelated) {
                  // Try one more approach - check if any company names are similar
                  const companyNames = companyInfo
                    .filter((company) => company.name)
                    .map((company) => company.name.toLowerCase().trim());

                  let similarNamesFound = false;

                  for (let i = 0; i < companyNames.length; i++) {
                    for (let j = i + 1; j < companyNames.length; j++) {
                      // Check if one name contains the other
                      if (
                        companyNames[i].includes(companyNames[j]) ||
                        companyNames[j].includes(companyNames[i])
                      ) {
                        similarNamesFound = true;

                        break;
                      }
                    }
                    if (similarNamesFound) break;
                  }

                  if (similarNamesFound) {
                    areCompaniesRelated = true;
                  } else {
                    // Get company names for better error reporting
                    const uniqueCompanies = [
                      ...new Set(
                        processedDamages
                          .map(
                            (damage) =>
                              damage?.vehicles?.customers?.companies?.name,
                          )
                          .filter(Boolean),
                      ),
                    ];

                    const errorMsg = `Selected damages must belong to the same company or related companies. Found: ${uniqueCompanies.join(', ')}`;
                    toast.error(errorMsg);
                    setLoading(false);
                    onClose(); // Close the modal immediately
                    return;
                  }
                }
              }
            }
          }
        }

        setDamages(processedDamages);
      } catch (apiError) {
        toast.error(
          'Failed to retrieve damage details from server. Please try again.',
        );
        onClose(); // Close the modal since we can't proceed
      }

      setLoading(false);
    } catch (error) {
      toast.error('An unexpected error occurred. Please try again.');
      setLoading(false);
      onClose(); // Close the modal since we can't proceed
    }
  };

  const handleSubmit = async () => {
    try {
      // Clear previous validation errors
      setValidationErrors([]);

      // 1. First validate that each damage has a non-zero credit
      const creditsValidation = damages.map((damage) => {
        if (!damage.credit || parseFloat(damage.credit) === 0) {
          return {
            isValid: false,
            vin: damage.vehicles?.vin || 'unknown',
            error: `Please set credit amount for: ${damage.vehicles?.vin || 'unknown'}`,
          };
        }
        return { isValid: true };
      });

      const creditErrors = creditsValidation
        .filter((result) => !result.isValid)
        .map((result) => result.error);
      if (creditErrors.length > 0) {
        setValidationErrors(creditErrors);
        toast.error(creditErrors[0]);
        return;
      }

      // 2. Next validate all damages belong to the same customer
      if (damages.length > 1) {
        // First, collect all company information including parent companies
        const companyInfo = damages
          .map((damage) => ({
            id: damage?.vehicles?.customers?.companies?.id,
            name: damage?.vehicles?.customers?.companies?.name,
            parentId:
              damage?.vehicles?.customers?.companies?.parent_company?.id,
            parentName:
              damage?.vehicles?.customers?.companies?.parent_company?.name,
          }))
          .filter(
            (company) =>
              company.id && !company.id.toString().startsWith('virtual-batch-'),
          );

        if (companyInfo.length > 1) {
          // First, check if all company names are the same
          const firstCompanyName = companyInfo[0].name;
          const allSameCompanyName = companyInfo.every(
            (company) => company.name === firstCompanyName,
          );

          // If all company names are the same, we can proceed immediately
          if (allSameCompanyName) {
            // Continue processing
          } else {
            // Direct check: If any company name matches any parent company name

            let directNameMatchFound = false;
            const companyNamesSet = new Set(companyInfo.map((c) => c.name));
            const parentNamesSet = new Set(
              companyInfo.filter((c) => c.parentName).map((c) => c.parentName),
            );

            // Check if any company name appears in the parent names set
            for (const companyName of companyNamesSet) {
              if (parentNamesSet.has(companyName)) {
                directNameMatchFound = true;
                break;
              }
            }

            // If direct name match found, we can proceed
            if (directNameMatchFound) {
              // Continue processing
            } else {
              // If no direct name match, proceed with checking parent-child relationships

              // Create a map of company ID -> parent ID for quick lookup
              const parentMap = new Map();
              companyInfo.forEach((company) => {
                if (company.parentId) {
                  parentMap.set(company.id, company.parentId);
                }
              });

              // Check if all companies are related (same company or parent-child relationship)
              let areCompaniesRelated = true;

              // Get all unique company IDs
              const uniqueCompanyIds = [
                ...new Set(companyInfo.map((c) => c.id)),
              ];

              if (uniqueCompanyIds.length > 1) {
                // If we have more than one company, check if they're related
                const firstCompanyId = uniqueCompanyIds[0];
                const relatedIds = new Set([firstCompanyId]);

                // Add parent if exists
                if (parentMap.has(firstCompanyId)) {
                  relatedIds.add(parentMap.get(firstCompanyId));
                }

                // Check if all other companies are related to the first one
                for (let i = 1; i < uniqueCompanyIds.length; i++) {
                  const currentId = uniqueCompanyIds[i];
                  const currentParentId = parentMap.get(currentId);

                  // Check if this company is already in our related set
                  if (relatedIds.has(currentId)) {
                    continue;
                  }

                  // Check if this company's parent is in our related set
                  if (currentParentId && relatedIds.has(currentParentId)) {
                    relatedIds.add(currentId);
                    continue;
                  }

                  // Check if this company is a parent of any company in our related set
                  let isParentOfRelated = false;
                  for (const relatedId of relatedIds) {
                    if (parentMap.get(relatedId) === currentId) {
                      isParentOfRelated = true;
                      break;
                    }
                  }

                  if (isParentOfRelated) {
                    relatedIds.add(currentId);
                    continue;
                  }

                  // If we reach here, this company is not related
                  areCompaniesRelated = false;
                  break;
                }
              }

              if (!areCompaniesRelated) {
                // Try one more approach - check if any company names are similar
                const companyNames = companyInfo
                  .filter((company) => company.name)
                  .map((company) => company.name.toLowerCase().trim());

                let similarNamesFound = false;

                for (let i = 0; i < companyNames.length; i++) {
                  for (let j = i + 1; j < companyNames.length; j++) {
                    // Check if one name contains the other
                    if (
                      companyNames[i].includes(companyNames[j]) ||
                      companyNames[j].includes(companyNames[i])
                    ) {
                      similarNamesFound = true;

                      break;
                    }
                  }
                  if (similarNamesFound) break;
                }

                if (similarNamesFound) {
                  areCompaniesRelated = true;
                } else {
                  // Get company names for better error reporting
                  const uniqueCompanies = [
                    ...new Set(
                      damages
                        .map(
                          (damage) =>
                            damage?.vehicles?.customers?.companies?.name,
                        )
                        .filter(Boolean),
                    ),
                  ];

                  const errorMsg = `Selected damages must belong to the same company or related companies. Found: ${uniqueCompanies.join(', ')}`;
                  setValidationErrors([errorMsg]);
                  toast.error(errorMsg);
                  return;
                }
              }
            }
          }
        }
      }

      // 3. Now validate all required fields for each damage

      let validationErrors = [];

      for (const damage of damages) {
        const vin = damage.vehicles?.vin || `ID:${damage.id}`;

        // Damage happened at validation - check this first (priority)
        if (
          !damage.damage_happened_at ||
          !Array.isArray(damage.damage_happened_at) ||
          damage.damage_happened_at.length === 0
        ) {
          validationErrors.push(
            `Please add damage happened at information for: ${vin}`,
          );
          continue; // Skip other checks for this damage item
        }

        // Damage details validation
        if (
          !damage.damage_details ||
          !Array.isArray(damage.damage_details) ||
          damage.damage_details.length === 0
        ) {
          validationErrors.push(`Please add damage details for: ${vin}`);
          continue;
        }

        // Inspection photo validation
        if (!damage.inspection_photo) {
          validationErrors.push(`Please upload inspection photo for: ${vin}`);
          continue;
        } else {
          // Validate that it's either a URL or a Google Drive link
          const isValidPhotoUrl =
            typeof damage.inspection_photo === 'string' &&
            (damage.inspection_photo.startsWith('http') ||
              damage.inspection_photo.startsWith('https') ||
              damage.inspection_photo.includes('drive.google.com') ||
              damage.inspection_photo.includes('minio'));

          if (!isValidPhotoUrl) {
            validationErrors.push(`Invalid inspection photo URL for: ${vin}`);
          }
        }
      }

      if (validationErrors.length > 0) {
        setValidationErrors(validationErrors);
        toast.error(validationErrors[0]);
        return;
      }

      // 4. Check for credit currency consistency
      const currencies = damages
        .map((damage) => damage.credit_currency)
        .filter(Boolean);

      if (currencies.length === 0) {
        const errorMsg =
          'No valid credit currency found. Please ensure all damages have a credit currency.';
        setValidationErrors([errorMsg]);
        toast.error(errorMsg);
        return;
      }

      // Get the first currency as reference
      const referenceCurrency = currencies[0];

      // Check if any currency is different from the reference
      const hasDifferentCurrencies = currencies.some(
        (currency) => currency !== referenceCurrency,
      );

      if (hasDifferentCurrencies) {
        const uniqueCurrencies = [...new Set(currencies)];
        const errorMsg = `All damages must have the same credit currency. Found: ${uniqueCurrencies.join(', ')}`;
        setValidationErrors([errorMsg]);
        toast.error(errorMsg);
        return;
      }

      setSubmitting(true);

      try {
        // Get all damage IDs
        const damageIds = damages.map((damage) => damage.id);

        // Use the updateMany API instead of individual field updates
        const response = await axios.post(
          'vehicle_damages/process-without-email',
          {
            ids: JSON.stringify(damageIds),
            case_number: maxCaseNumber,
            preserve_existing_case_numbers: true, // Always preserve existing case numbers
          },
        );

        if (response.data.result) {
          toast.success(
            'Damages processed successfully! Status updated to Initial Review.',
          );
          setSubmitting(false);
          onSuccess();
          onClose();
        } else {
          toast.error(
            'Failed to process damages: ' +
              (response.data.message || 'Unknown error'),
          );
          setSubmitting(false);
        }
      } catch (error) {
        toast.error('Failed to process damages. Please try again.');
        setSubmitting(false);
      }
    } catch (error) {
      toast.error('Failed to process damages');
      setSubmitting(false);
    }
  };

  // Helper function to render damage_happened_at values
  const renderDamageHappenedAt = (damage: any) => {
    if (
      !damage.damage_happened_at ||
      !Array.isArray(damage.damage_happened_at) ||
      damage.damage_happened_at.length === 0
    ) {
      return (
        <Box
          sx={{
            color: 'error.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="error" variant="caption">
            Missing
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_happened_at.map((happened_at, index) => {
          const damageOption = damage_happened_at_options.find(
            (item) => item.id === happened_at.happened_at,
          );
          const color = damageOption ? damageOption.color : '#6A5ACD';

          return (
            <Chip
              key={index}
              label={happened_at.happened_at}
              size="small"
              sx={{
                backgroundColor: color,
                color: 'white',
                fontSize: '10px',
                fontWeight: 'bold',
              }}
            />
          );
        })}
      </Box>
    );
  };

  // Helper function to render damage_details values
  const renderDamageDetails = (damage: any) => {
    if (
      !damage.damage_details ||
      !Array.isArray(damage.damage_details) ||
      damage.damage_details.length === 0
    ) {
      return (
        <Box
          sx={{
            color: 'error.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="error" variant="caption">
            Missing
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_details.map((detail, index) => (
          <Chip
            key={index}
            label={detail.detail}
            size="small"
            sx={{
              backgroundColor: '#673ab7',
              color: 'white',
              fontSize: '10px',
              fontWeight: 'bold',
            }}
          />
        ))}
      </Box>
    );
  };

  // Helper function to render damage type with color
  const renderDamageType = (damage: any) => {
    if (!damage.damage_type) return 'N/A';

    const damageOption = damage_type_options.find(
      (item) => item.id === damage.damage_type,
    );
    const color = damageOption ? damageOption.color : '#3f50b5';

    return (
      <Chip
        label={damage.damage_type}
        size="small"
        sx={{
          backgroundColor: color,
          color: 'white',
          fontSize: '10px',
          fontWeight: 'bold',
        }}
      />
    );
  };

  // Helper function to render POL with color
  const renderPOL = (vehicle: any) => {
    if (!vehicle?.pol_locations?.name) return 'N/A';

    try {
      const pol = vehicle.pol_locations.name.split(',')[1].trim();
      const damageOption = pol_options.find((item) => item.id === pol);
      const color = damageOption ? damageOption.color : '#fad521';

      return (
        <Chip
          label={pol}
          size="small"
          sx={{
            backgroundColor: color,
            color: 'white',
            fontSize: '10px',
            fontWeight: 'bold',
          }}
        />
      );
    } catch (error) {
      return vehicle.pol_locations.name || 'N/A';
    }
  };

  // Calculate total credit amount
  const totalCredit = useMemo(() => {
    return damages.reduce((sum, damage) => {
      const creditAmount = parseFloat(damage.credit) || 0;
      return sum + creditAmount;
    }, 0);
  }, [damages]);

  // Get credit currency
  const creditCurrency = useMemo(() => {
    return damages.find((item) => item.credit_currency)?.credit_currency || '';
  }, [damages]);

  // Get customer name
  const customerName = useMemo(() => {
    // If no damages, return default value
    if (damages.length === 0) return 'Customer';

    // Check if any customer can be found in the damages
    const firstCustomerWithInfo = damages.find(
      (item) => item?.vehicles?.customers?.companies?.name,
    );

    if (firstCustomerWithInfo) {
      const company = firstCustomerWithInfo.vehicles.customers.companies;
      const companyName = company.name;
      const parentCompany = company.parent_company;

      // If there's a parent company, include its name
      if (parentCompany && parentCompany.name) {
        return `${companyName} (Parent: ${parentCompany.name})`;
      }

      return companyName;
    }

    // If no customers found, return placeholder
    return 'Unknown Customer';
  }, [damages]);

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '90%',
    maxWidth: 1200,
    maxHeight: '90vh',
    overflow: 'hidden',
    bgcolor: 'background.paper',
    boxShadow: 24,
    borderRadius: 1,
  };

  return (
    <>
      <Modal open={open} onClose={onClose}>
        <Card sx={style}>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography variant="h5">Process Damages</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />

          <CardContent sx={{ height: 'calc(90vh - 140px)', overflow: 'auto' }}>
            {/* Display validation errors if any */}
            {validationErrors.length > 0 && (
              <Grid sx={{ mb: 2 }} size={12}>
                <Card
                  sx={{
                    backgroundColor: '#fdeded',
                    borderLeft: '4px solid #f44336',
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" sx={{ color: '#f44336', mb: 1 }}>
                      Validation Errors
                    </Typography>
                    {validationErrors.map((error, index) => (
                      <Typography
                        key={index}
                        sx={{ fontSize: '0.9rem', mb: 0.5 }}
                      >
                        • {error}
                      </Typography>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            )}

            <Grid container spacing={3}>
              {/* Summary Info */}
              <Grid size={12}>
                <Card elevation={1}>
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid
                        size={{
                          xs: 12,
                          md: 4,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 'bold' }}
                        >
                          Customer:
                        </Typography>
                        {damages.length > 0 && (
                          <>
                            <Typography sx={{ fontWeight: 'medium' }}>
                              {customerName}
                            </Typography>
                            {damages.some(
                              (d) =>
                                d?.vehicles?.customers?.companies?.id !==
                                damages[0]?.vehicles?.customers?.companies?.id,
                            ) && (
                              <Typography
                                variant="caption"
                                color="text.secondary"
                                sx={{ display: 'block', mt: 0.5 }}
                              >
                                Multiple related companies detected
                              </Typography>
                            )}
                          </>
                        )}
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          md: 4,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 'bold' }}
                        >
                          Case Number:
                        </Typography>
                        <Typography
                          sx={{ fontWeight: 'bold', color: 'primary.main' }}
                        >
                          {formattedCaseNumber}
                        </Typography>
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          md: 4,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 'bold' }}
                        >
                          Total Credit:
                        </Typography>
                        <Typography
                          sx={{ color: 'success.main', fontWeight: 'bold' }}
                        >
                          {totalCredit.toFixed(2)} {creditCurrency}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Damages Table Section */}
              <Grid size={12}>
                <Card elevation={1}>
                  <CardContent>
                    <Box
                      sx={{
                        mb: 2,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <Typography variant="h6">
                        Selected Damages ({damages.length})
                      </Typography>
                      <Chip
                        label={`Total Credit: ${totalCredit.toFixed(2)} ${creditCurrency}`}
                        color="success"
                        sx={{ fontWeight: 'bold' }}
                      />
                    </Box>

                    {loading ? (
                      <Box
                        sx={{ display: 'flex', justifyContent: 'center', p: 3 }}
                      >
                        <CircularProgress />
                      </Box>
                    ) : (
                      <TableContainer
                        component={Paper}
                        elevation={0}
                        sx={{ border: '1px solid #e0e0e0' }}
                      >
                        <Table size="small">
                          <TableHead>
                            <TableRow sx={{ backgroundColor: '#f0f0f0' }}>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold', width: '40px' }}
                              >
                                #
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Damage Type
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Damage Details
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Inspection Photo
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Happened At
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                POL
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Description
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                VIN#
                              </TableCell>
                              <TableCell
                                align="left"
                                sx={{ fontWeight: 'bold', width: '200px' }}
                              >
                                Company
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Credit Amount
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {damages.map((damage, index) => (
                              <TableRow
                                key={damage.id}
                                hover
                                sx={{
                                  '&:hover': { backgroundColor: '#f0f7ff' },
                                }}
                              >
                                <TableCell align="center">
                                  {index + 1}
                                </TableCell>
                                <TableCell align="center">
                                  {renderDamageType(damage)}
                                </TableCell>
                                <TableCell align="center">
                                  {renderDamageDetails(damage)}
                                </TableCell>
                                <TableCell align="center">
                                  <Tooltip title="View/Edit Inspection Photo">
                                    <IconButton
                                      size="small"
                                      color={
                                        damage.inspection_photo
                                          ? 'primary'
                                          : 'error'
                                      }
                                      onClick={() => {
                                        setCurrentInspectionDamage(damage);
                                        setShowInspectionPhotoModal(true);
                                      }}
                                    >
                                      <ImageIcon />
                                    </IconButton>
                                  </Tooltip>
                                </TableCell>
                                <TableCell align="center">
                                  {renderDamageHappenedAt(damage)}
                                </TableCell>
                                <TableCell align="center">
                                  {renderPOL(damage.vehicles)}
                                </TableCell>
                                <TableCell align="center">
                                  {`${damage.vehicles?.year || ''} ${damage.vehicles?.make || ''} ${damage.vehicles?.model || ''} ${damage.vehicles?.color || ''}`}
                                </TableCell>
                                <TableCell
                                  align="center"
                                  sx={{ fontWeight: 'medium' }}
                                >
                                  {damage.vehicles?.vin || 'N/A'}
                                </TableCell>
                                <TableCell align="left">
                                  {damage?.vehicles?.customers?.companies ? (
                                    <Box
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          backgroundColor: '#e3f2fd',
                                          borderRadius: '4px',
                                          padding: '4px 8px',
                                          fontSize: '0.75rem',
                                          fontWeight: 'bold',
                                        }}
                                      >
                                        {
                                          damage.vehicles.customers.companies
                                            .name
                                        }
                                      </Box>

                                      {damage.vehicles.customers.companies
                                        .parent_company && (
                                        <Box
                                          sx={{
                                            backgroundColor: '#e8f5e9',
                                            color: '#2e7d32',
                                            borderRadius: '4px',
                                            padding: '2px 6px',
                                            fontSize: '0.7rem',
                                            fontWeight: 'medium',
                                            display: 'flex',
                                            alignItems: 'center',
                                          }}
                                        >
                                          {
                                            damage.vehicles.customers.companies
                                              .parent_company.name
                                          }
                                        </Box>
                                      )}
                                    </Box>
                                  ) : (
                                    'N/A'
                                  )}
                                </TableCell>
                                <TableCell align="center">
                                  {damage.credit ? (
                                    <Typography
                                      sx={{
                                        fontWeight: 'bold',
                                        color:
                                          parseFloat(damage.credit) === 0
                                            ? 'error.main'
                                            : 'success.main',
                                      }}
                                    >
                                      {damage.credit}{' '}
                                      {damage.credit_currency || ''}
                                      {parseFloat(damage.credit) === 0 && (
                                        <Box
                                          sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mt: 0.5,
                                          }}
                                        >
                                          <Typography
                                            color="error"
                                            variant="caption"
                                            sx={{ fontWeight: 'bold' }}
                                          >
                                            Credit cannot be 0
                                          </Typography>
                                        </Box>
                                      )}
                                    </Typography>
                                  ) : (
                                    <Box
                                      sx={{
                                        color: 'error.main',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                      }}
                                    >
                                      <Typography
                                        color="error"
                                        variant="caption"
                                      >
                                        Missing
                                      </Typography>
                                    </Box>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                            {/* Summary Row */}
                            <TableRow sx={{ backgroundColor: '#f9f9f9' }}>
                              <TableCell
                                colSpan={8}
                                align="right"
                                sx={{ fontWeight: 'bold' }}
                              >
                                Total Credit:
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'bold' }}
                              >
                                {totalCredit.toFixed(2)} {creditCurrency}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </CardContent>

          <Divider />

          <CardActions sx={{ justifyContent: 'flex-end', p: 2 }}>
            <Button
              variant="outlined"
              color="inherit"
              onClick={onClose}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              color="primary"
              loading={submitting}
              startIcon={<CheckCircleIcon />}
            >
              Process Damages
            </Button>
          </CardActions>
        </Card>
      </Modal>
      {/* Inspection Photo Modal */}
      {showInspectionPhotoModal && currentInspectionDamage && (
        <InspectionPhotoModal
          open={showInspectionPhotoModal}
          setOpen={setShowInspectionPhotoModal}
          damageData={currentInspectionDamage}
          readonly={true}
          onPhotoUpdate={() => {
            // Refresh the damages data when a photo is updated
            if (currentInspectionDamage?.id) {
              axios
                .get(`vehicle_damages/${currentInspectionDamage.id}`)
                .then((response) => {
                  if (response.data?.data) {
                    // Update the damage item in the damages array
                    const updatedDamages = damages.map((damage) =>
                      damage.id === currentInspectionDamage.id
                        ? response.data.data
                        : damage,
                    );
                    setDamages(updatedDamages);
                  }
                })
                .catch((error) => {
                  console.error(error);
                });
            }
          }}
        />
      )}
    </>
  );
};

export default DamageProcessModal;
