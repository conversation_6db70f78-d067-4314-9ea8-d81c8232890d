import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  Typography,
  IconButton,
  Divider,
  CardContent,
  CardActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Alert,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import CloseIcon from '@mui/icons-material/Close';
import { Done } from '@mui/icons-material';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';

type PreCreditModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  damageItems: any[]; // Changed from damageItem to damageItems array
  onSuccess: () => void;
  mode: 'preCredit' | 'credited';
};

const PreCreditModal: React.FC<PreCreditModalProps> = ({
  open,
  setOpen,
  damageItems,
  onSuccess,
  mode = 'preCredit',
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedDamages, setSelectedDamages] = useState<any[]>([]);
  const [customerError, setCustomerError] = useState<string | null>(null);
  const [creditedDate, setCreditedDate] = useState<Date | null>(new Date());

  // Initialize selected damages when damageItems changes
  useEffect(() => {
    if (damageItems && damageItems.length > 0) {
      // Initially select all damages
      setSelectedDamages([...damageItems]);
      validateCustomers(damageItems);
    }
  }, [damageItems]);

  // Reset creditedDate to current date when modal opens
  useEffect(() => {
    if (open) {
      setCreditedDate(new Date());
    }
  }, [open]);

  // Validate that all selected damages belong to the same customer
  const validateCustomers = (damages: any[]) => {
    if (!damages || damages.length <= 1) {
      setCustomerError(null);
      return true;
    }

    // Collect company info with parent company details
    const companiesInfo = damages
      .map((damage) => ({
        id: damage?.vehicles?.customers?.companies?.id,
        name: damage?.vehicles?.customers?.companies?.name,
        parentId: damage?.vehicles?.customers?.companies?.parent_company?.id,
        parentName:
          damage?.vehicles?.customers?.companies?.parent_company?.name,
      }))
      .filter((company) => company.id);

    // First, check if all company names are the same
    const firstCompanyName = companiesInfo[0].name;
    const allSameCompanyName = companiesInfo.every(
      (company) => company.name === firstCompanyName,
    );

    // If all company names are the same, we can return valid immediately
    if (allSameCompanyName) {
      setCustomerError(null);
      return true;
    }

    // Direct check: If any company name matches any parent company name
    // This is a separate check specifically for parent_company.name === company.name

    let directNameMatchFound = false;
    const companyNamesSet = new Set(companiesInfo.map((c) => c.name));
    const parentNamesSet = new Set(
      companiesInfo.filter((c) => c.parentName).map((c) => c.parentName),
    );

    // Check if any company name appears in the parent names set
    for (const companyName of companyNamesSet) {
      if (parentNamesSet.has(companyName)) {
        directNameMatchFound = true;
        break;
      }
    }

    // If direct name match found, return valid
    if (directNameMatchFound) {
      setCustomerError(null);
      return true;
    }

    // If no direct name match, proceed with checking parent-child relationships

    // Create a map of company ID -> parent ID for quick lookup
    const parentMap = new Map();
    companiesInfo.forEach((company) => {
      if (company.parentId) {
        parentMap.set(company.id, company.parentId);
      }
    });

    // Get all unique company IDs
    const uniqueCompanyIds = [...new Set(companiesInfo.map((c) => c.id))];

    // Check if companies are related (same company or parent-child relationship)
    const firstCompanyId = uniqueCompanyIds[0];
    const firstCompany = companiesInfo.find((c) => c.id === firstCompanyId);
    const relatedIds = new Set([firstCompanyId]);

    // Add parent if exists
    if (parentMap.has(firstCompanyId)) {
      relatedIds.add(parentMap.get(firstCompanyId));
    }

    // Check if all companies are related
    let areCompaniesRelated = true;

    // Check if all other companies are related to the first one
    for (let i = 1; i < uniqueCompanyIds.length; i++) {
      const currentId = uniqueCompanyIds[i];
      const currentCompany = companiesInfo.find((c) => c.id === currentId);

      // Check if this company is already in our related set
      if (relatedIds.has(currentId)) {
        continue;
      }

      // Check if this company's parent is in our related set
      if (currentCompany.parentId && relatedIds.has(currentCompany.parentId)) {
        relatedIds.add(currentId);
        continue;
      }

      // Check if this company is a parent of any company in our related set
      let isParentOfRelated = false;
      for (const relatedId of relatedIds) {
        if (parentMap.get(relatedId) === currentId) {
          isParentOfRelated = true;
          break;
        }
      }

      if (isParentOfRelated) {
        relatedIds.add(currentId);
        continue;
      }

      // Check if parent company name matches company name (per requirement)
      // This specifically checks if company names match with parent company names
      let nameRelationshipFound = false;

      // Check current company name against first company parent name
      if (
        currentCompany.name &&
        firstCompany.parentName &&
        currentCompany.name === firstCompany.parentName
      ) {
        nameRelationshipFound = true;
      }

      // Check current company parent name against first company name
      if (
        currentCompany.parentName &&
        firstCompany.name &&
        currentCompany.parentName === firstCompany.name
      ) {
        nameRelationshipFound = true;
      }

      // Also check against other related companies
      for (const relatedId of relatedIds) {
        if (relatedId === firstCompanyId) continue; // Already checked

        const relatedCompany = companiesInfo.find((c) => c.id === relatedId);
        if (!relatedCompany) continue;

        // Check current company name against related company parent name
        if (
          currentCompany.name &&
          relatedCompany.parentName &&
          currentCompany.name === relatedCompany.parentName
        ) {
          nameRelationshipFound = true;
          break;
        }

        // Check current company parent name against related company name
        if (
          currentCompany.parentName &&
          relatedCompany.name &&
          currentCompany.parentName === relatedCompany.name
        ) {
          nameRelationshipFound = true;
          break;
        }
      }

      if (nameRelationshipFound) {
        relatedIds.add(currentId);
        continue;
      }

      // If we reach here, this company is not related
      areCompaniesRelated = false;
      break;
    }

    if (!areCompaniesRelated) {
      const uniqueCompanyNames = [...new Set(companiesInfo.map((c) => c.name))];
      setCustomerError(
        `Selected damages must belong to the same customer or related companies. Found: ${uniqueCompanyNames.join(', ')}`,
      );
      return false;
    }

    setCustomerError(null);
    return true;
  };

  // Handle checkbox selection
  const handleSelectDamage = (damage: any) => {
    const isSelected = selectedDamages.some((d) => d.id === damage.id);
    let newSelected: any[];

    if (isSelected) {
      newSelected = selectedDamages.filter((d) => d.id !== damage.id);
    } else {
      newSelected = [...selectedDamages, damage];
    }

    setSelectedDamages(newSelected);
    validateCustomers(newSelected);
  };

  // Check if a damage is selected
  const isSelected = (id: number) => selectedDamages.some((d) => d.id === id);

  // Calculate total credit amount
  const getTotalCreditAmount = () => {
    return selectedDamages.reduce(
      (total, damage) => total + (damage.credit || 0),
      0,
    );
  };

  const handleSubmit = async () => {
    if (customerError) {
      toast.error('Cannot proceed: ' + customerError);
      return;
    }

    if (selectedDamages.length === 0) {
      toast.error('Please select at least one damage item');
      return;
    }

    try {
      setLoading(true);

      // Different approach based on mode
      if (mode === 'preCredit') {
        // Move to Pre_Credit status
        const payload = {
          ids: selectedDamages.map((damage) => damage.id),
          field: 'damage_status',
          value: 'Pre_Credit',
        };

        const response = await axios.post(
          'vehicle_damages/bulk-update',
          payload,
        );

        if (response.data.result) {
          toast.success(
            `${selectedDamages.length} damages moved to Pre Credit successfully`,
          );
          onSuccess();
          setOpen(false);
        } else {
          toast.error(response.data.message || 'Failed to update damages');
        }
      } else {
        // Move to Credited status with date
        const payload = {
          ids: selectedDamages.map((damage) => damage.id),
          field: 'damage_status',
          value: 'Credited',
          credited_at: creditedDate
            ? creditedDate.toISOString()
            : new Date().toISOString(),
        };

        const response = await axios.post(
          'vehicle_damages/bulk-update',
          payload,
        );

        if (response.data.result) {
          toast.success(
            `${selectedDamages.length} damages moved to Credited successfully`,
          );
          onSuccess();
          setOpen(false);
        } else {
          toast.error(response.data.message || 'Failed to update damages');
        }
      }
    } catch (error) {
      toast.error('An error occurred while updating the damages');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // Get customer name from the first selected damage
  const getCustomerName = () => {
    if (selectedDamages.length > 0) {
      return (
        selectedDamages[0]?.vehicles?.customers?.companies?.name ||
        'Customer Name Not Available'
      );
    }
    return (
      damageItems[0]?.vehicles?.customers?.companies?.name ||
      'Customer Name Not Available'
    );
  };

  return (
    <Modal
      open={open}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box>
        {damageItems && damageItems.length > 0 ? (
          <Card sx={{ width: 1000, maxHeight: '90vh', overflow: 'auto' }}>
            <Box
              sx={{
                m: 0,
                p: 2,
                py: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <Typography variant="h6">
                {mode === 'preCredit' ? 'Move to Pre Credit' : 'Credit Damages'}
              </Typography>
              <IconButton
                aria-label="close"
                sx={{
                  color: 'grey',
                }}
                onClick={() => setOpen(false)}
              >
                <CloseIcon />
              </IconButton>
            </Box>
            <Divider />
            <CardContent>
              <Typography
                variant="h5"
                sx={{ mb: 2, textAlign: 'center', fontWeight: 'bold' }}
              >
                {getCustomerName()}
              </Typography>

              {customerError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {customerError}
                </Alert>
              )}

              {/* Date picker for credited_at - only show when mode is 'credited' */}
              {mode === 'credited' && (
                <Box
                  sx={{
                    mb: 3,
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ mr: 2, fontWeight: 'medium' }}
                  >
                    Set Credited Date:
                  </Typography>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Credited Date"
                      value={creditedDate}
                      onChange={(newValue) => setCreditedDate(newValue)}
                      slotProps={{
                        textField: {
                          size: 'small',
                          sx: { width: 200 },
                          helperText: "Default: Today's date",
                        },
                      }}
                    />
                  </LocalizationProvider>
                </Box>
              )}

              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#0073b7' }}>
                      <TableCell sx={{ color: 'white', width: '50px' }}>
                        Select
                      </TableCell>
                      <TableCell sx={{ color: 'white' }}>ID</TableCell>
                      <TableCell sx={{ color: 'white' }}>Type</TableCell>
                      <TableCell sx={{ color: 'white' }}>Detail</TableCell>
                      <TableCell sx={{ color: 'white' }}>Insp Photo</TableCell>
                      <TableCell sx={{ color: 'white' }}>Happened at</TableCell>
                      <TableCell sx={{ color: 'white' }}>Case #</TableCell>
                      <TableCell sx={{ color: 'white' }}>POL</TableCell>
                      <TableCell sx={{ color: 'white' }}>VIN#</TableCell>
                      <TableCell sx={{ color: 'white' }}>LOT#</TableCell>
                      <TableCell sx={{ color: 'white' }}>Container#</TableCell>
                      <TableCell sx={{ color: 'white' }}>Credit Amt</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {damageItems.map((damage, index) => {
                      const isItemSelected = isSelected(damage.id);
                      return (
                        <TableRow
                          key={damage.id}
                          hover
                          onClick={() => handleSelectDamage(damage)}
                          role="checkbox"
                          aria-checked={isItemSelected}
                          selected={isItemSelected}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              color="primary"
                              checked={isItemSelected}
                              onChange={() => handleSelectDamage(damage)}
                            />
                          </TableCell>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{damage.damage_type}</TableCell>
                          <TableCell>
                            {damage.damage_detail ||
                              (damage.damage_details
                                ? damage.damage_details
                                    .map((d) => d.damage_type)
                                    .join(', ')
                                : 'N/A')}
                          </TableCell>
                          <TableCell>
                            {damage.inspection_photo ? (
                              <a
                                href={damage.inspection_photo}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: '#0073b7' }}
                              >
                                Look Into
                              </a>
                            ) : (
                              'N/A'
                            )}
                          </TableCell>
                          <TableCell>
                            {damage.damage_happened_at
                              ?.map((d) => d.happened_at)
                              .join(', ') || 'N/A'}
                          </TableCell>
                          <TableCell>{damage.case_number || 'N/A'}</TableCell>
                          <TableCell>
                            {damage.vehicles?.pol_locations?.name || 'N/A'}
                          </TableCell>
                          <TableCell>{damage.vehicles?.vin || 'N/A'}</TableCell>
                          <TableCell>
                            {damage.vehicles?.lot_number || 'N/A'}
                          </TableCell>
                          <TableCell>
                            {damage.vehicles?.containers?.container_number ||
                              'N/A'}
                          </TableCell>
                          <TableCell>
                            {damage.credit_currency} {damage.credit}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow sx={{ fontWeight: 'bold' }}>
                      <TableCell
                        colSpan={11}
                        align="right"
                        sx={{
                          fontWeight: 'bold',
                          borderBottom: '1px solid #ddd',
                        }}
                      >
                        Total Credit Amount:
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          borderBottom: '1px solid #ddd',
                        }}
                      >
                        {selectedDamages.length > 0
                          ? selectedDamages[0].credit_currency
                          : ''}{' '}
                        {getTotalCreditAmount()}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
            <CardActions
              sx={{ px: 2, pb: 2, display: 'flex', justifyContent: 'end' }}
            >
              <Button
                onClick={() => setOpen(false)}
                variant="text"
                color="primary"
                size="small"
                sx={{ marginRight: '5px' }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
                size="small"
                sx={{ marginRight: '5px' }}
                startIcon={<Done />}
                loading={loading}
                disabled={
                  customerError !== null || selectedDamages.length === 0
                }
              >
                {mode === 'preCredit'
                  ? 'Move to Pre Credit'
                  : 'Move to Credited'}
              </Button>
            </CardActions>
          </Card>
        ) : null}
      </Box>
    </Modal>
  );
};

export default PreCreditModal;
