import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  Link,
  Tooltip,
  Pagination,
  PaginationItem,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import EmailIcon from '@mui/icons-material/Email';
import ImageIcon from '@mui/icons-material/Image';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import {
  damage_happened_at_options,
  damage_type_options,
} from '@/configs/vehicles_damage/damageHeader';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import moment from 'moment';

// Format currency helper function
const formatMoney = (amount, currency = 'USD') => {
  if (!amount) return '$0.00';
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
  });
  return formatter.format(amount);
};

interface CreditedDamagesProps {
  damages: any[];
  onViewDamage: (damage: any) => void;
  onStatusChange?: () => void; // Callback to refresh data after status change
  permissions?: string[]; // User permissions
}

const CreditedDamages: React.FC<CreditedDamagesProps> = ({
  damages,
  onViewDamage,
  onStatusChange,
  permissions = [],
}) => {
  const theme = useTheme();
  // State to track which accordions are expanded
  const [expandedPanels, setExpandedPanels] = useState<Record<string, boolean>>(
    {},
  );
  // State for email modal
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  // State for approval modal
  const [approvalModalOpen, setApprovalModalOpen] = useState(false);
  // State to store the current damage being emailed
  const [selectedDamage, setSelectedDamage] = useState<any>(null);
  // Loading state for API calls
  const [loading, setLoading] = useState(false);
  const [isCopied, setIsCopied] = useState<{ [key: string]: boolean }>({});

  // Pagination states
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Toggle accordion expansion
  const handleAccordionToggle = (caseNumber: string) => {
    setExpandedPanels((prev) => ({
      ...prev,
      [caseNumber]: !prev[caseNumber],
    }));
  };

  // Close email modal
  const handleCloseModal = () => {
    setEmailModalOpen(false);
    setApprovalModalOpen(false);
    setSelectedDamage(null);
  };

  // Handle sending credit notification email
  const handleSendEmail = async () => {
    if (!selectedDamage) return;

    setLoading(true);
    try {
      await axios.post(`vehicle_damages/send-email/${selectedDamage.id}`, {
        emailType: 'credited',
      });

      toast.success('Credit notification email sent successfully');

      // Close the modal
      setEmailModalOpen(false);
      setSelectedDamage(null);

      // Refresh data if callback provided
      if (onStatusChange) {
        onStatusChange();
      }
    } catch (error: any) {
      console.error('Error sending email:', error);

      // Show more specific error message to the user
      const errorMessage =
        error.response?.data?.message ||
        'Failed to send email. Please try again.';
      toast.error(errorMessage);

      // Close the modal even on error to prevent being stuck
      setEmailModalOpen(false);
      setSelectedDamage(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle audit review of a credited damage
  const handleAuditReview = async () => {
    if (!selectedDamage) return;

    setLoading(true);
    try {
      // Find the case number to get all damages in the same case
      const caseNumber = selectedDamage.case_number;
      const damagesInCase = damages.filter(
        (d) => d.case_number === caseNumber && d.damage_status === 'Credited',
      );

      // Create an array of promises for each damage audit review
      const auditReviewPromises = damagesInCase.map((damage) =>
        axios.patch(`vehicle_damages/${damage.id}/audit-review`),
      );

      // Execute all promises
      const results = await Promise.all(auditReviewPromises);

      // Check if all audit reviews were successful
      const allSuccessful = results.every((res) => res.data.result);

      if (allSuccessful) {
        toast.success(
          `${damagesInCase.length} damages audit reviewed successfully`,
        );

        // Refresh data if callback provided
        if (onStatusChange) {
          onStatusChange();
        }
      } else {
        toast.error('Some damages failed to audit review. Please try again.');
      }

      // Close the modal
      setApprovalModalOpen(false);
      setSelectedDamage(null);
    } catch (error: any) {
      console.error('Error audit reviewing damage:', error);

      // Show more specific error message to the user
      const errorMessage =
        error.response?.data?.message ||
        'Failed to audit review damage. Please try again.';
      toast.error(errorMessage);

      // Close the modal even on error to prevent being stuck
      setApprovalModalOpen(false);
      setSelectedDamage(null);
    } finally {
      setLoading(false);
    }
  };

  // Group damages by case_number
  const groupedDamages = useMemo(() => {
    const groups: Record<string, any[]> = {};

    // Filter only CREDITED damages
    const creditedDamages = damages.filter(
      (damage) => damage.damage_status === 'Credited',
    );

    // First, group all damages by case_number
    creditedDamages.forEach((damage) => {
      const caseNumber = damage.case_number || 'No Case Number';
      if (!groups[caseNumber]) {
        groups[caseNumber] = [];
      }
      groups[caseNumber].push(damage);
    });

    // Convert to array for rendering
    return Object.entries(groups)
      .map(([caseNumber, items]) => {
        // Calculate total credit amount for this group
        const totalCredit = items.reduce((sum, damage) => {
          const creditAmount = parseFloat(damage.credit) || 0;
          return sum + creditAmount;
        }, 0);

        // Get currency from the first item that has one
        const creditCurrency =
          items.find((item) => item.credit_currency)?.credit_currency || '';

        return {
          caseNumber,
          items,
          // Get customer name from the first item
          customerName:
            items[0]?.vehicles?.customers?.companies?.name ||
            'Unknown Customer',
          // Count total damages in this case
          count: items.length,
          totalCredit,
          creditCurrency,
        };
      })
      .sort((a, b) => {
        // Sort by case number in descending order (latest first)
        if (a.caseNumber === 'No Case Number') return 1;
        if (b.caseNumber === 'No Case Number') return -1;
        return parseInt(b.caseNumber) - parseInt(a.caseNumber);
      });
  }, [damages]);

  // Helper function to render damage_happened_at values
  const renderDamageHappenedAt = (damage: any) => {
    if (
      !damage.damage_happened_at ||
      !Array.isArray(damage.damage_happened_at) ||
      damage.damage_happened_at.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_happened_at.map((happened_at, index) => {
          const damageOption = damage_happened_at_options.find(
            (item) => item.id === happened_at.happened_at,
          );
          const color = damageOption ? damageOption.color : '#6A5ACD';

          return (
            <Chip
              key={index}
              label={happened_at.happened_at}
              size="small"
              sx={{
                backgroundColor: color,
                color: 'white',
                fontSize: '10px',
                fontWeight: 'bold',
              }}
            />
          );
        })}
      </Box>
    );
  };

  // Helper function to render damage_details values
  const renderDamageDetails = (damage: any) => {
    if (
      !damage.damage_details ||
      !Array.isArray(damage.damage_details) ||
      damage.damage_details.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_details.map((detail, index) => (
          <Chip
            key={index}
            label={detail.detail}
            size="small"
            sx={{
              backgroundColor: '#3f50b5',
              color: 'white',
              fontSize: '10px',
              fontWeight: 'bold',
            }}
          />
        ))}
      </Box>
    );
  };

  // Helper function to render damage type with color
  const renderDamageType = (damage: any) => {
    if (!damage.damage_type) return 'N/A';

    const damageOption = damage_type_options.find(
      (item) => item.id === damage.damage_type,
    );
    const color = damageOption ? damageOption.color : '#3f50b5';

    return (
      <Chip
        label={damage.damage_type}
        size="small"
        sx={{
          backgroundColor: color,
          color: 'white',
          fontSize: '10px',
          fontWeight: 'bold',
        }}
      />
    );
  };

  // New helper function to copy text to clipboard
  const copyToClipboard = (text: string, type: string, caseNumber: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Set the copied state for this specific item
        setIsCopied({
          ...isCopied,
          [`${caseNumber}_${type}`]: true,
        });

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied((prev) => ({
            ...prev,
            [`${caseNumber}_${type}`]: false,
          }));
        }, 2000);

        toast.success(`${type} copied to clipboard`);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
        toast.error('Failed to copy to clipboard');
      });
  };

  // Handle page change
  const handlePageChange = (_: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  if (groupedDamages.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No credited damages found.</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        mt: 2,
        display: 'flex',
        flexDirection: 'column',
        minHeight: 'calc(100vh - 200px)',
        position: 'relative',
      }}
    >
      {/* Group by case number */}
      <Box sx={{ flex: '1 0 auto', mb: 8 }}>
        {groupedDamages
          .slice((page - 1) * rowsPerPage, page * rowsPerPage)
          .map((group) => (
            <Accordion
              key={group.caseNumber}
              expanded={!!expandedPanels[group.caseNumber]}
              onChange={() => handleAccordionToggle(group.caseNumber)}
              sx={{
                mb: 1,
                bgcolor:
                  theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'transparent',
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`panel-${group.caseNumber}-content`}
                id={`panel-${group.caseNumber}-header`}
                sx={{
                  bgcolor: 'rgba(46, 125, 50, 0.4)',
                  '&:hover': { bgcolor: 'rgba(46, 125, 50, 0.5)' },
                }}
              >
                <Grid container alignItems="center" spacing={1}>
                  <Grid
                    size={{
                      xs: 6,
                      sm: 5,
                      md: 3,
                      lg: 2,
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        flexWrap: { xs: 'nowrap', sm: 'nowrap' },
                        width: '100%',
                      }}
                    >
                      <Typography
                        fontWeight={600}
                        sx={{
                          whiteSpace: 'nowrap',
                          mr: 0.5,
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          const caseNumberText =
                            group.caseNumber !== 'No Case Number'
                              ? `PGLDMG0${group.caseNumber}`
                              : 'No Case #';
                          copyToClipboard(
                            caseNumberText,
                            'Case number',
                            group.caseNumber,
                          );
                        }}
                      >
                        {group.caseNumber !== 'No Case Number'
                          ? `PGLDMG0${group.caseNumber}`
                          : 'No Case #'}
                        <Tooltip
                          title={
                            isCopied[`${group.caseNumber}_Case number`]
                              ? 'Copied!'
                              : 'Copy to clipboard'
                          }
                        >
                          <ContentCopyIcon
                            sx={{
                              ml: 0.5,
                              fontSize: '0.8rem',
                              color: isCopied[`${group.caseNumber}_Case number`]
                                ? 'success.main'
                                : 'action.active',
                              opacity: isCopied[
                                `${group.caseNumber}_Case number`
                              ]
                                ? 1
                                : 0.7,
                              '&:hover': { opacity: 1 },
                            }}
                          />
                        </Tooltip>
                      </Typography>
                      <Chip
                        label={group.count}
                        size="small"
                        color="primary"
                        sx={{ height: 20, minWidth: 24 }}
                      />
                    </Box>
                  </Grid>
                  <Grid
                    size={{
                      xs: 6,
                      sm: 7,
                      md: 5,
                      lg: 4,
                    }}
                  >
                    <Typography
                      noWrap
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                        width: '100%',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        copyToClipboard(
                          group.customerName,
                          'Customer name',
                          group.caseNumber,
                        );
                      }}
                    >
                      {group.customerName}
                      <Tooltip
                        title={
                          isCopied[`${group.caseNumber}_Customer name`]
                            ? 'Copied!'
                            : 'Copy to clipboard'
                        }
                      >
                        <ContentCopyIcon
                          sx={{
                            ml: 0.5,
                            fontSize: '0.8rem',
                            color: isCopied[`${group.caseNumber}_Customer name`]
                              ? 'success.main'
                              : 'action.active',
                            opacity: isCopied[
                              `${group.caseNumber}_Customer name`
                            ]
                              ? 1
                              : 0.7,
                            '&:hover': { opacity: 1 },
                          }}
                        />
                      </Tooltip>
                    </Typography>
                  </Grid>
                  <Grid
                    size={{
                      xs: 12,
                      sm: 12,
                      md: 4,
                      lg: 6,
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: { xs: 'flex-start', md: 'flex-end' },
                        alignItems: 'center',
                        mt: { xs: 1, md: 0 },
                        gap: 1,
                      }}
                    >
                      {group.totalCredit > 0 ? (
                        <Chip
                          icon={<CreditScoreIcon />}
                          label={`${group.totalCredit.toFixed(2)} ${group.creditCurrency}`}
                          color="success"
                          size="small"
                          sx={{
                            fontWeight: 'bold',
                            fontSize: { xs: '0.75rem', sm: '0.85rem' },
                          }}
                        />
                      ) : (
                        <Typography variant="body2">No credit</Typography>
                      )}

                      {permissions?.includes(DAMAGE?.AUDIT_REVIEW_DAMAGES) && (
                        <Button
                          size="small"
                          variant="contained"
                          color="success"
                          startIcon={<CheckCircleIcon />}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent accordion toggle
                            // Get the first damage from the group
                            if (group.items.length > 0) {
                              setSelectedDamage(group.items[0]);
                              setApprovalModalOpen(true);
                            }
                          }}
                          sx={{
                            py: 0.5,
                            ml: 1,
                          }}
                        >
                          Audit Review
                        </Button>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </AccordionSummary>
              <AccordionDetails
                sx={{
                  bgcolor: 'rgba(46, 125, 50, 0.04)',
                }}
              >
                <TableContainer
                  component={Paper}
                  sx={{
                    mt: 2,
                    bgcolor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'white',
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell align="center">#</TableCell>
                        <TableCell align="center">Damage Type</TableCell>
                        <TableCell align="center">Damage Details</TableCell>
                        <TableCell align="center">Photos</TableCell>
                        <TableCell align="center">Happened At</TableCell>
                        <TableCell align="center">POL</TableCell>
                        <TableCell align="center">Description</TableCell>
                        <TableCell align="center">VIN#</TableCell>
                        <TableCell align="center">LOT#</TableCell>
                        <TableCell align="center">Container#</TableCell>
                        <TableCell align="center">Credit Amount</TableCell>
                        <TableCell align="center">Credited At</TableCell>
                        <TableCell align="center">Credited By</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {group.items.map((damage, index) => (
                        <TableRow
                          key={damage.id}
                          hover
                          onClick={() => onViewDamage(damage)}
                          sx={{
                            cursor: 'pointer',
                            '&:hover': { backgroundColor: '#f0f7ff' },
                            '&:nth-of-type(odd)': {
                              bgcolor:
                                theme.palette.mode === 'dark'
                                  ? 'rgba(255, 255, 255, 0.03)'
                                  : 'rgba(0, 0, 0, 0.03)',
                            },
                          }}
                        >
                          <TableCell align="center">{index + 1}</TableCell>
                          <TableCell align="center">
                            {renderDamageType(damage)}
                          </TableCell>
                          <TableCell align="center">
                            {renderDamageDetails(damage)}
                          </TableCell>
                          <TableCell align="center">
                            {damage.inspection_photo ? (
                              <Link
                                href={damage.inspection_photo}
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <ImageIcon color="primary" />
                              </Link>
                            ) : (
                              'N/A'
                            )}
                          </TableCell>
                          <TableCell align="center">
                            {renderDamageHappenedAt(damage)}
                          </TableCell>
                          <TableCell align="center">
                            {damage.vehicles?.pol_locations?.name || 'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {`${damage.vehicles?.year || ''} ${damage.vehicles?.make || ''} ${damage.vehicles?.model || ''} ${damage.vehicles?.color || ''}`}
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ fontWeight: 'medium' }}
                          >
                            {damage.vehicles?.vin || 'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {damage.vehicles?.lot_number || 'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {damage.vehicles?.containers?.container_number ||
                              'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {damage.credit
                              ? `${damage.credit} ${damage.credit_currency || ''}`
                              : 'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {damage.credited_at
                              ? moment(damage.credited_at).format(
                                  'YYYY-MM-DD HH:mm',
                                )
                              : 'N/A'}
                          </TableCell>
                          <TableCell align="center">
                            {damage.users_vehicle_damages_credited_by
                              ?.fullname || 'N/A'}
                          </TableCell>
                        </TableRow>
                      ))}
                      {/* Summary Row */}
                      <TableRow
                        sx={{
                          backgroundColor:
                            theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.1)'
                              : '#f9f9f9',
                          fontWeight: 'bold',
                        }}
                      >
                        <TableCell
                          colSpan={10}
                          align="right"
                          sx={{ fontWeight: 'bold' }}
                        >
                          Total Credit:
                        </TableCell>
                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                          {group.totalCredit.toFixed(2)} {group.creditCurrency}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          ))}
      </Box>
      {/* Pagination component - fixed at the bottom */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          py: 1,
          px: 2,
          borderTop: '1px solid',
          borderColor:
            theme.palette.mode === 'dark'
              ? 'rgba(255, 255, 255, 0.12)'
              : 'rgba(0, 0, 0, 0.12)',
          bgcolor:
            theme.palette.mode === 'dark'
              ? 'rgba(30, 30, 30, 0.9)'
              : 'rgba(255, 255, 255, 0.9)',
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 10,
          boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Typography variant="body2" sx={{ fontSize: '0.75rem', mr: 1 }}>
          Rows per page:
        </Typography>
        <FormControl size="small">
          <Select
            value={rowsPerPage}
            onChange={(e) => {
              setRowsPerPage(Number(e.target.value));
              setPage(1);
            }}
            variant="standard"
            sx={{ mx: 1, fontSize: '0.75rem' }}
          >
            {[10, 20, 50, 100, 150, 200, 500].map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Typography variant="body2" sx={{ fontSize: '0.75rem', pr: 1 }}>
          {groupedDamages.length > 0
            ? `${(page - 1) * rowsPerPage + 1}-${Math.min(page * rowsPerPage, groupedDamages.length)} of ${groupedDamages.length}`
            : '0-0 of 0'}
        </Typography>
        <Pagination
          size="small"
          count={Math.ceil(groupedDamages.length / rowsPerPage)}
          page={page}
          onChange={handlePageChange}
          color="primary"
          renderItem={(item) => (
            <PaginationItem
              slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
              {...item}
            />
          )}
        />
      </Box>
      {/* Email Notification Dialog */}
      <Dialog
        open={emailModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="email-dialog-title"
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor:
              theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'white',
          },
        }}
      >
        <DialogTitle id="email-dialog-title">
          Send Credit Notification Email
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to send a credit notification email for this
            damage?
          </Typography>
          {selectedDamage && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>ID:</strong> {selectedDamage.id}
              </Typography>
              <Typography variant="body2">
                <strong>Dealer:</strong>{' '}
                {selectedDamage.vehicles?.customers?.companies?.name || 'N/A'}
              </Typography>
              <Typography variant="body2">
                <strong>Vehicle:</strong>{' '}
                {`${selectedDamage.vehicles?.year || ''} ${selectedDamage.vehicles?.make || ''} ${selectedDamage.vehicles?.model || ''}`}
              </Typography>
              <Typography variant="body2">
                <strong>Total Cost:</strong>{' '}
                {formatMoney(
                  selectedDamage.credit,
                  selectedDamage.credit_currency,
                )}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} color="inherit" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSendEmail}
            color="primary"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <EmailIcon />}
          >
            {loading ? 'Sending...' : 'Send Email'}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Approval Dialog */}
      <Dialog
        open={approvalModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="approval-dialog-title"
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor:
              theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'white',
          },
        }}
      >
        <DialogTitle id="approval-dialog-title">
          Audit Review Credited Damages
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to audit review all credited damages for this
            case?
          </Typography>
          {selectedDamage && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Case Number:</strong>{' '}
                {selectedDamage.case_number
                  ? `PGLDMG0${selectedDamage.case_number}`
                  : 'No Case Number'}
              </Typography>
              <Typography variant="body2">
                <strong>Dealer:</strong>{' '}
                {selectedDamage.vehicles?.customers?.companies?.name || 'N/A'}
              </Typography>
              <Typography variant="body2">
                <strong>Number of Damages:</strong>{' '}
                {
                  damages.filter(
                    (d) =>
                      d.case_number === selectedDamage.case_number &&
                      d.damage_status === 'Credited',
                  ).length
                }
              </Typography>
              <Typography variant="body2">
                <strong>Total Credit Amount:</strong>{' '}
                {formatMoney(
                  damages
                    .filter(
                      (d) =>
                        d.case_number === selectedDamage.case_number &&
                        d.damage_status === 'Credited',
                    )
                    .reduce((sum, d) => sum + (parseFloat(d.credit) || 0), 0),
                  selectedDamage.credit_currency,
                )}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} color="inherit" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleAuditReview}
            color="success"
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? <CircularProgress size={20} /> : <CheckCircleIcon />
            }
          >
            {loading ? 'Audit Reviewing...' : 'Audit Review All'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreditedDamages;
