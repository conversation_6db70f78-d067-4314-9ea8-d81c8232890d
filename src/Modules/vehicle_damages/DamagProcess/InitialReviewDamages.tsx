import React, { useMemo, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Link,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Grid,
  useTheme,
  Tooltip,
  Stack,
  Pagination,
  PaginationItem,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CancelIcon from '@mui/icons-material/Cancel';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import {
  pol_options,
  damage_type_options,
  damage_happened_at_options,
} from '@/configs/vehicles_damage/damageHeader';
import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import { handelColor } from '@/configs/vehicles/configs';

interface InitialReviewDamagesProps {
  damages: any[];
  onViewDamage: (damage: any) => void;
  onStatusChange?: () => void; // Callback to refresh data after status change
  permissions?: string[]; // User permissions
}

const InitialReviewDamages: React.FC<InitialReviewDamagesProps> = ({
  damages,
  onViewDamage,
  onStatusChange,
  permissions = [],
}) => {
  const theme = useTheme();
  // State to track which accordions are expanded
  const [expandedPanels, setExpandedPanels] = useState<Record<string, boolean>>(
    {},
  );
  // State for confirmation modal
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  // State for reject modal
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  // State to store the current group being confirmed
  const [currentGroup, setCurrentGroup] = useState<any>(null);
  // Loading state for API calls
  const [loading, setLoading] = useState(false);
  const [isCopied, setIsCopied] = useState<{ [key: string]: boolean }>({});

  // Pagination states
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Toggle accordion expansion
  const handleAccordionToggle = (caseNumber: string) => {
    setExpandedPanels((prev) => ({
      ...prev,
      [caseNumber]: !prev[caseNumber],
    }));
  };

  // Open confirmation modal
  const handleConfirmClick = (group: any, event: React.MouseEvent) => {
    // Prevent accordion from toggling when clicking the button
    event.stopPropagation();
    setCurrentGroup(group);
    setConfirmModalOpen(true);
  };

  // Open reject modal
  const handleRejectClick = (group: any, event: React.MouseEvent) => {
    // Prevent accordion from toggling when clicking the button
    event.stopPropagation();
    setCurrentGroup(group);
    setRejectModalOpen(true);
  };

  // Close confirmation modal
  const handleCloseModal = () => {
    setConfirmModalOpen(false);
    setRejectModalOpen(false);
    setCurrentGroup(null);
  };

  // Handle confirmation to change status to pre_credit
  const handleConfirmStatus = async () => {
    if (!currentGroup) return;

    setLoading(true);
    try {
      // Extract all damage IDs in this group
      const damageIds = currentGroup.items.map((damage) => damage.id);

      // Option 1: Use a custom endpoint for bulk updates if it exists
      try {
        // Try to use a bulk update endpoint
        await axios.post(`vehicle_damages/bulk-update`, {
          ids: damageIds,
          field: 'damage_status',
          value: 'Pre_Credit',
        });
      } catch (bulkError) {
        // Option 2: If bulk update fails, use individual updates as fallback
        const updatePromises = currentGroup.items.map((damage) =>
          axios.patch(`vehicle_damages/vehicle_damage_field/${damage.id}`, {
            field: 'damage_status',
            value: 'Pre_Credit',
          }),
        );

        await Promise.all(updatePromises);
      }

      toast.success(
        `Successfully moved ${currentGroup.items.length} damages to Pre Credit status. You can now view them in the Pre Credit tab.`,
      );

      // Close the modal
      setConfirmModalOpen(false);
      setCurrentGroup(null);

      // Refresh data if callback provided
      if (onStatusChange) {
        onStatusChange();
      }
    } catch (error: any) {
      // Show more specific error message to the user
      const errorMessage =
        error.response?.data?.message ||
        'Failed to update damage status. Please try again.';
      toast.error(errorMessage);

      // Close the modal even on error to prevent being stuck
      setConfirmModalOpen(false);
      setCurrentGroup(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle reject status - Send to In Process
  const handleRejectStatus = async () => {
    try {
      setLoading(true);

      if (!currentGroup) {
        toast.error('No damages selected for rejection');
        setLoading(false);
        return;
      }

      const damageIds = currentGroup.items.map((damage) => damage.id);

      // First update status to In_process while preserving the case number
      // This is important: we want to keep the case number even when rejecting
      // so that later when these are reprocessed, they can be grouped together again
      const response = await axios.post('vehicle_damages/bulk-update', {
        ids: damageIds,
        field: 'damage_status',
        value: 'In_process',
      });

      if (response.data.result) {
        toast.success(
          'Damages successfully moved to In Process with preserved case numbers',
        );
        setLoading(false);
        handleCloseModal();
        if (onStatusChange) onStatusChange();
      } else {
        toast.error(
          'Failed to update damages: ' +
            (response.data.message || 'Unknown error'),
        );
        setLoading(false);
      }
    } catch (error) {
      console.error('Error rejecting damages:', error);
      toast.error('Failed to update damages. Please try again.');
      setLoading(false);
    }
  };

  // Group damages by case_number
  const groupedDamages = useMemo(() => {
    const groups: Record<string, any[]> = {};

    // First, group all damages by case_number
    damages.forEach((damage) => {
      const caseNumber = damage.case_number || 'No Case Number';
      if (!groups[caseNumber]) {
        groups[caseNumber] = [];
      }
      groups[caseNumber].push(damage);
    });

    // Convert to array for rendering
    return Object.entries(groups)
      .map(([caseNumber, items]) => {
        // Calculate total credit amount for this group
        const totalCredit = items.reduce((sum, damage) => {
          const creditAmount = parseFloat(damage.credit) || 0;
          return sum + creditAmount;
        }, 0);

        // Calculate total vehicle price for this group
        const totalVehiclePrice = items.reduce((sum, damage) => {
          const vehiclePrice = damage.vehicles?.price || 0;
          return sum + vehiclePrice;
        }, 0);

        // Get currency from the first item that has one
        const creditCurrency =
          items.find((item) => item.credit_currency)?.credit_currency || '';

        return {
          caseNumber,
          items,
          // Get customer name from the first item
          customerName:
            items[0]?.vehicles?.customers?.companies?.name ||
            'Unknown Customer',
          // Count total damages in this case
          count: items.length,
          totalCredit,
          totalVehiclePrice,
          creditCurrency,
        };
      })
      .sort((a, b) => {
        // Sort by case number in descending order (latest first)
        if (a.caseNumber === 'No Case Number') return 1;
        if (b.caseNumber === 'No Case Number') return -1;
        return parseInt(b.caseNumber) - parseInt(a.caseNumber);
      });
  }, [damages]);

  // Helper function to render damage_happened_at values
  const renderDamageHappenedAt = (damage: any) => {
    if (
      !damage.damage_happened_at ||
      !Array.isArray(damage.damage_happened_at) ||
      damage.damage_happened_at.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_happened_at.map((happened_at, index) => {
          const damageOption = damage_happened_at_options.find(
            (item) => item.id === happened_at.happened_at,
          );
          const color = damageOption ? damageOption.color : '#6A5ACD';

          return (
            <Chip
              key={index}
              label={happened_at.happened_at}
              size="small"
              sx={{
                backgroundColor: color,
                color: 'white',
                fontSize: '10px',
                fontWeight: 'bold',
              }}
            />
          );
        })}
      </Box>
    );
  };

  // Helper function to render damage_details values
  const renderDamageDetails = (damage: any) => {
    if (
      !damage.damage_details ||
      !Array.isArray(damage.damage_details) ||
      damage.damage_details.length === 0
    ) {
      return 'N/A';
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {damage.damage_details.map((detail, index) => (
          <Chip
            key={index}
            label={detail.detail}
            size="small"
            sx={{
              backgroundColor: '#3f50b5',
              color: 'white',
              fontSize: '10px',
              fontWeight: 'bold',
            }}
          />
        ))}
      </Box>
    );
  };

  // Helper function to render damage type with color
  const renderDamageType = (damage: any) => {
    if (!damage.damage_type) return 'N/A';

    const damageOption = damage_type_options.find(
      (item) => item.id === damage.damage_type,
    );
    const color = damageOption ? damageOption.color : '#3f50b5';

    return (
      <Chip
        label={damage.damage_type}
        size="small"
        sx={{
          backgroundColor: color,
          color: 'white',
          fontSize: '10px',
          fontWeight: 'bold',
        }}
      />
    );
  };

  // Helper function to render POL with color
  const renderPOL = (vehicle: any) => {
    if (!vehicle?.pol_locations?.name) return 'N/A';

    try {
      const pol = vehicle.pol_locations.name.split(',')[1].trim();
      const damageOption = pol_options.find((item) => item.id === pol);
      const color = damageOption ? damageOption.color : '#fad521';

      return (
        <Chip
          label={pol}
          size="small"
          sx={{
            backgroundColor: color,
            color: 'white',
            fontSize: '10px',
            fontWeight: 'bold',
          }}
        />
      );
    } catch (error) {
      return vehicle.pol_locations.name || 'N/A';
    }
  };

  // New helper function to copy text to clipboard
  const copyToClipboard = (text: string, type: string, caseNumber: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Set the copied state for this specific item
        setIsCopied({
          ...isCopied,
          [`${caseNumber}_${type}`]: true,
        });

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied((prev) => ({
            ...prev,
            [`${caseNumber}_${type}`]: false,
          }));
        }, 2000);

        toast.success(`${type} copied to clipboard`);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
        toast.error('Failed to copy to clipboard');
      });
  };

  // Handle page change
  const handlePageChange = (_: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  return (
    <Box
      sx={{
        my: 3,
        display: 'flex',
        flexDirection: 'column',
        minHeight: 'calc(100vh - 200px)',
        position: 'relative',
      }}
    >
      {groupedDamages.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            No initial review damages found.
          </Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ flex: '1 0 auto', mb: 8 }}>
            {/* Slice the grouped damages array for pagination */}
            {groupedDamages
              .slice((page - 1) * rowsPerPage, page * rowsPerPage)
              .map((group) => (
                <Accordion
                  key={group.caseNumber}
                  expanded={!!expandedPanels[group.caseNumber]}
                  onChange={() => handleAccordionToggle(group.caseNumber)}
                  sx={{
                    mb: 1,
                    bgcolor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'transparent',
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`panel-${group.caseNumber}-content`}
                    id={`panel-${group.caseNumber}-header`}
                    sx={{
                      bgcolor: 'rgba(0, 0, 0, 0.1)',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.15)' },
                    }}
                  >
                    <Grid
                      container
                      alignItems="center"
                      spacing={0.5}
                      sx={{ flexWrap: { xs: 'wrap', sm: 'nowrap' } }}
                    >
                      <Grid
                        size={{
                          xs: 12,
                          sm: 2.5,
                          md: 2,
                          lg: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                            flexWrap: 'nowrap',
                            width: '100%',
                            justifyContent: {
                              xs: 'flex-start',
                              sm: 'flex-start',
                            },
                          }}
                        >
                          <Typography
                            fontWeight={600}
                            sx={{
                              whiteSpace: 'nowrap',
                              mr: 0.5,
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              fontSize: { xs: '0.75rem', sm: '1rem' },
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              const caseNumberText =
                                group.caseNumber !== 'No Case Number'
                                  ? `PGLDMG0${group.caseNumber}`
                                  : 'No Case #';
                              copyToClipboard(
                                caseNumberText,
                                'Case number',
                                group.caseNumber,
                              );
                            }}
                          >
                            {group.caseNumber !== 'No Case Number'
                              ? `PGLDMG0${group.caseNumber}`
                              : 'No Case #'}
                            <Tooltip
                              title={
                                isCopied[`${group.caseNumber}_Case number`]
                                  ? 'Copied!'
                                  : 'Copy to clipboard'
                              }
                            >
                              <ContentCopyIcon
                                sx={{
                                  ml: 0.5,
                                  fontSize: { xs: '0.6rem', sm: '0.7rem' },
                                  color: isCopied[
                                    `${group.caseNumber}_Case number`
                                  ]
                                    ? 'success.main'
                                    : 'action.active',
                                  opacity: isCopied[
                                    `${group.caseNumber}_Case number`
                                  ]
                                    ? 1
                                    : 0.7,
                                  '&:hover': { opacity: 1 },
                                }}
                              />
                            </Tooltip>
                          </Typography>
                          <Chip
                            label={group.count}
                            size="small"
                            color="primary"
                            sx={{
                              height: { xs: 16, sm: 18 },
                              minWidth: 20,
                              fontSize: { xs: '0.6rem', sm: '0.7rem' },
                            }}
                          />
                        </Box>
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          sm: 3,
                          md: 2.5,
                          lg: 2.5,
                        }}
                      >
                        <Typography
                          noWrap
                          sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            cursor: 'pointer',
                            fontSize: { xs: '0.75rem', sm: '1rem' },
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(
                              group.customerName,
                              'Customer name',
                              group.caseNumber,
                            );
                          }}
                        >
                          {group.customerName}
                          <Tooltip
                            title={
                              isCopied[`${group.caseNumber}_Customer name`]
                                ? 'Copied!'
                                : 'Copy to clipboard'
                            }
                          >
                            <ContentCopyIcon
                              sx={{
                                ml: 0.5,
                                fontSize: { xs: '0.6rem', sm: '0.7rem' },
                                color: isCopied[
                                  `${group.caseNumber}_Customer name`
                                ]
                                  ? 'success.main'
                                  : 'action.active',
                                opacity: isCopied[
                                  `${group.caseNumber}_Customer name`
                                ]
                                  ? 1
                                  : 0.7,
                                '&:hover': { opacity: 1 },
                              }}
                            />
                          </Tooltip>
                        </Typography>
                      </Grid>
                      <Grid
                        size={{
                          xs: 12,
                          sm: 4.5,
                          md: 5,
                          lg: 5,
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: { xs: 0.5, sm: 1 },
                            flexWrap: { xs: 'wrap', sm: 'nowrap' },
                            justifyContent: {
                              xs: 'flex-start',
                              sm: 'flex-start',
                            },
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5,
                            }}
                          >
                            <Typography
                              variant="caption"
                              sx={{
                                fontWeight: 'bold',
                                fontSize: { xs: '0.65rem', sm: '0.75rem' },
                              }}
                            >
                              Total Credit:
                            </Typography>
                            {group.totalCredit > 0 ? (
                              <Chip
                                icon={
                                  <CreditScoreIcon
                                    sx={{
                                      fontSize: { xs: '0.7rem', sm: '0.8rem' },
                                    }}
                                  />
                                }
                                label={`${group.totalCredit.toFixed(2)} ${group.creditCurrency}`}
                                color="success"
                                size="small"
                                sx={{
                                  fontWeight: 'bold',
                                  fontSize: { xs: '0.6rem', sm: '0.75rem' },
                                  height: { xs: 18, sm: 24 },
                                }}
                              />
                            ) : (
                              <Typography
                                variant="body2"
                                sx={{
                                  fontSize: { xs: '0.65rem', sm: '0.875rem' },
                                }}
                              >
                                No credit
                              </Typography>
                            )}
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5,
                            }}
                          >
                            <Typography
                              variant="caption"
                              sx={{
                                fontWeight: 'bold',
                                fontSize: { xs: '0.65rem', sm: '0.75rem' },
                              }}
                            >
                              Total V.Price:
                            </Typography>
                            <Chip
                              label={`$${group.totalVehiclePrice.toLocaleString()} USD`}
                              color="info"
                              size="small"
                              sx={{
                                fontWeight: 'bold',
                                fontSize: { xs: '0.6rem', sm: '0.75rem' },
                                height: { xs: 18, sm: 24 },
                              }}
                            />
                          </Box>
                        </Box>
                      </Grid>
                      <Grid
                        sx={{
                          display: 'flex',
                          justifyContent: { xs: 'flex-start', sm: 'flex-end' },
                          gap: 0.5,
                          mt: { xs: 0.5, sm: 0 },
                        }}
                        size={{
                          xs: 12,
                          sm: 2,
                          md: 2.5,
                          lg: 2.5,
                        }}
                      >
                        {/* Only show buttons if not already in Pre_Credit or Credited status and user has permission */}
                        {!group.items.every(
                          (item) =>
                            item.damage_status === 'Pre_Credit' ||
                            item.damage_status === 'Credited',
                        ) &&
                          permissions?.includes(
                            DAMAGE?.CONFIRM_INITIAL_REVIEW,
                          ) && (
                            <Stack direction="row" spacing={{ xs: 0.5, sm: 1 }}>
                              <Button
                                variant="contained"
                                size="small"
                                color="error"
                                onClick={(e) => handleRejectClick(group, e)}
                                startIcon={
                                  <CancelIcon
                                    sx={{
                                      fontSize: { xs: '0.8rem', sm: '1rem' },
                                    }}
                                  />
                                }
                                sx={{
                                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                                  px: { xs: 1, sm: 2 },
                                  py: { xs: 0.5, sm: 1 },
                                  minWidth: { xs: 'auto', sm: '64px' },
                                }}
                              >
                                Reject
                              </Button>
                              <Button
                                variant="contained"
                                size="small"
                                color="success"
                                onClick={(e) => handleConfirmClick(group, e)}
                                startIcon={
                                  <CheckCircleIcon
                                    sx={{
                                      fontSize: { xs: '0.8rem', sm: '1rem' },
                                    }}
                                  />
                                }
                                sx={{
                                  fontSize: { xs: '0.7rem', sm: '0.875rem' },
                                  px: { xs: 1, sm: 2 },
                                  py: { xs: 0.5, sm: 1 },
                                  minWidth: { xs: 'auto', sm: '64px' },
                                }}
                              >
                                Confirm
                              </Button>
                            </Stack>
                          )}
                      </Grid>
                    </Grid>
                  </AccordionSummary>
                  <AccordionDetails>
                    <TableContainer
                      component={Paper}
                      sx={{
                        mt: 2,
                        bgcolor:
                          theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'white',
                      }}
                    >
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell align="center">#</TableCell>
                            <TableCell align="center">Damage Type</TableCell>
                            <TableCell align="center">Damage Details</TableCell>
                            <TableCell align="center">Photos</TableCell>
                            <TableCell align="center">Happened At</TableCell>
                            <TableCell align="center">POL</TableCell>
                            <TableCell align="center">V.Price</TableCell>
                            <TableCell align="center">Description</TableCell>
                            <TableCell align="center">VIN#</TableCell>
                            <TableCell align="center">LOT#</TableCell>
                            <TableCell align="center">Container#</TableCell>
                            <TableCell align="center">Credit Amount</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {group.items.map((damage, index) => (
                            <TableRow
                              key={damage.id}
                              hover
                              onClick={() => onViewDamage(damage)}
                              sx={{
                                cursor: 'pointer',
                                '&:hover': { backgroundColor: '#f0f7ff' },
                                '&:nth-of-type(odd)': {
                                  bgcolor:
                                    theme.palette.mode === 'dark'
                                      ? 'rgba(255, 255, 255, 0.03)'
                                      : 'rgba(0, 0, 0, 0.03)',
                                },
                              }}
                            >
                              <TableCell align="center">{index + 1}</TableCell>
                              <TableCell align="center">
                                {renderDamageType(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {renderDamageDetails(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {damage.inspection_photo ? (
                                  <Link
                                    href={damage.inspection_photo}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <ImageIcon color="primary" />
                                  </Link>
                                ) : (
                                  'N/A'
                                )}
                              </TableCell>
                              <TableCell align="center">
                                {renderDamageHappenedAt(damage)}
                              </TableCell>
                              <TableCell align="center">
                                {renderPOL(damage.vehicles)}
                              </TableCell>
                              <TableCell align="center">
                                {damage.vehicles?.price ? (
                                  <Box
                                    component="p"
                                    style={{
                                      color: 'white',
                                      backgroundColor: handelColor(
                                        damage.vehicles?.price,
                                      ),
                                      fontSize: '10px',
                                      padding: '3px',
                                      borderRadius: '5px',
                                      margin: '5px',
                                      textAlign: 'center',
                                    }}
                                  >
                                    {damage.vehicles?.price.toLocaleString()}{' '}
                                    USD
                                  </Box>
                                ) : (
                                  'N/A'
                                )}
                              </TableCell>
                              <TableCell align="center">
                                {`${damage.vehicles?.year || ''} ${damage.vehicles?.make || ''} ${damage.vehicles?.model || ''} ${damage.vehicles?.color || ''}`}
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ fontWeight: 'medium' }}
                              >
                                {damage.vehicles?.vin || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.vehicles?.lot_number || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.vehicles?.containers
                                  ?.container_number || 'N/A'}
                              </TableCell>
                              <TableCell align="center">
                                {damage.credit
                                  ? `${damage.credit} ${damage.credit_currency || ''}`
                                  : 'N/A'}
                              </TableCell>
                            </TableRow>
                          ))}
                          {/* Summary Row */}
                          <TableRow
                            sx={{
                              backgroundColor:
                                theme.palette.mode === 'dark'
                                  ? 'rgba(255, 255, 255, 0.1)'
                                  : '#f9f9f9',
                            }}
                          >
                            <TableCell
                              colSpan={11}
                              align="right"
                              sx={{ fontWeight: 'bold' }}
                            >
                              Total Credit:
                            </TableCell>
                            <TableCell
                              align="center"
                              sx={{ fontWeight: 'bold' }}
                            >
                              {group.totalCredit.toFixed(2)}{' '}
                              {group.creditCurrency}
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </AccordionDetails>
                </Accordion>
              ))}
          </Box>

          {/* Pagination component - fixed at the bottom */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              py: 1,
              px: 2,
              borderTop: '1px solid',
              borderColor:
                theme.palette.mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.12)'
                  : 'rgba(0, 0, 0, 0.12)',
              bgcolor:
                theme.palette.mode === 'dark'
                  ? 'rgba(30, 30, 30, 0.9)'
                  : 'rgba(255, 255, 255, 0.9)',
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: 10,
              boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Typography variant="body2" sx={{ fontSize: '0.75rem', mr: 1 }}>
              Rows per page:
            </Typography>
            <FormControl size="small">
              <Select
                value={rowsPerPage}
                onChange={(e) => {
                  setRowsPerPage(Number(e.target.value));
                  setPage(1);
                }}
                variant="standard"
                sx={{ mx: 1, fontSize: '0.75rem' }}
              >
                {[10, 20, 50, 100, 150, 200, 500].map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Typography variant="body2" sx={{ fontSize: '0.75rem', pr: 1 }}>
              {(page - 1) * rowsPerPage + 1}-
              {Math.min(page * rowsPerPage, groupedDamages.length)} of{' '}
              {groupedDamages.length}
            </Typography>
            <Pagination
              size="small"
              count={Math.ceil(groupedDamages.length / rowsPerPage)}
              page={page}
              onChange={handlePageChange}
              color="primary"
              renderItem={(item) => (
                <PaginationItem
                  slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
                  {...item}
                />
              )}
            />
          </Box>
        </>
      )}
      {/* Confirm Modal - Move to Pre Credit */}
      <Dialog
        open={confirmModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor:
              theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'white',
          },
        }}
      >
        <DialogTitle id="confirm-dialog-title">
          Confirm Status Change
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            Are you sure you want to change the status of{' '}
            {currentGroup?.items.length || 0} damages for case{' '}
            {currentGroup?.caseNumber !== 'No Case Number'
              ? `PGLDMG0${currentGroup?.caseNumber}`
              : 'Ungrouped Damages'}{' '}
            to Pre Credit? This will move them to the Pre Credit tab.
            <Box sx={{ mt: 2, fontWeight: 'bold' }}>
              Total Credit Amount:{' '}
              {currentGroup?.totalCredit.toFixed(2) || '0.00'}{' '}
              {currentGroup?.creditCurrency || ''}
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} color="primary" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmStatus}
            color="success"
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? <CircularProgress size={20} /> : <CheckCircleIcon />
            }
          >
            {loading ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Reject Modal - Send back to In Process */}
      <Dialog
        open={rejectModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="reject-dialog-title"
        aria-describedby="reject-dialog-description"
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor:
              theme.palette.mode === 'dark'
                ? 'rgba(30, 30, 30, 0.95)'
                : 'white',
          },
        }}
      >
        <DialogTitle id="reject-dialog-title">
          Reject and Move to In Process
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="reject-dialog-description">
            Are you sure you want to reject {currentGroup?.items.length || 0}{' '}
            damages for case{' '}
            {currentGroup?.caseNumber !== 'No Case Number'
              ? `PGLDMG0${currentGroup?.caseNumber}`
              : 'Ungrouped Damages'}
            ? This will move them to the In Process tab.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} color="primary" disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleRejectStatus}
            color="error"
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? <CircularProgress size={20} /> : <CancelIcon />
            }
          >
            {loading ? 'Processing...' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InitialReviewDamages;
