import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Chip,
} from '@mui/material';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';

interface DamageStatusModalProps {
  open: boolean;
  onClose: () => void;
  selectedItems: any[];
  onSuccess: () => void;
}

// Process status options only
const processStatusOptions = [
  { id: 'Under_Investigation', label: 'Under Investigation', color: '#FFA500' },
  { id: 'In_process', label: 'In Process', color: '#6B9FEA' },
  { id: 'Pending_CA', label: 'Pending CA', color: '#FFD700' },
  { id: 'Half_Cut', label: 'Half Cut', color: '#E55A5F' },
  { id: 'Forgotten', label: 'Forgotten', color: '#808080' },
  { id: 'UnLoading', label: 'UnLoading', color: '#E55A5F' },
  { id: 'Dismissed', label: 'Dismissed', color: '#E55A5F' },
];

const DamageStatusModal: React.FC<DamageStatusModalProps> = ({
  open,
  onClose,
  selectedItems,
  onSuccess,
}) => {
  const [selectedStatus, setSelectedStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    setSelectedStatus('');
    onClose();
  };

  const handleStatusChange = (event: any) => {
    setSelectedStatus(event.target.value);
  };

  const handleConfirm = async () => {
    if (!selectedStatus) {
      toast.error('Please select a damage status');
      return;
    }

    // Check if trying to change status to restricted statuses when damage happened at auction
    const restrictedStatuses = ['In_process', 'Pending_CA', 'Forgotten'];

    if (restrictedStatuses.includes(selectedStatus)) {
      // Check if any selected items have damage that happened at auction
      const itemsWithAuctionDamage = selectedItems.filter((item) => {
        return item.damage_happened_at?.some(
          (happenedAt) =>
            happenedAt.happened_at === 'Auction' || happenedAt === 'Auction',
        );
      });

      if (itemsWithAuctionDamage.length > 0) {
        const vins = itemsWithAuctionDamage
          .map((item) => item.vehicles?.vin || `ID:${item.id}`)
          .join(', ');

        toast.error(
          `Cannot change status to ${selectedStatus.replace('_', ' ')} for damage(s) that happened at Auction. Affected VINs: ${vins}`,
        );
        return;
      }
    }

    setLoading(true);

    try {
      const selectedStatusOption = processStatusOptions.find(
        (option) => option.id === selectedStatus,
      );

      if (selectedItems.length === 1) {
        // Single item update
        const response = await axios.patch(
          `vehicle_damages/vehicle_damage_field/${selectedItems[0].id}`,
          {
            field: 'damage_status',
            value: selectedStatus,
          },
        );

        if (response.data.result) {
          toast.success(
            `Damage status updated to "${selectedStatusOption?.label}" successfully`,
          );
          onSuccess();
          handleClose();
        } else {
          toast.error('Failed to update damage status');
        }
      } else {
        // Bulk update
        const response = await axios.post('vehicle_damages/bulk-update', {
          ids: selectedItems.map((item) => item.id),
          field: 'damage_status',
          value: selectedStatus,
        });

        if (response.data.result) {
          toast.success(
            `${selectedItems.length} damages status updated to "${selectedStatusOption?.label}" successfully`,
          );
          onSuccess();
          handleClose();
        } else {
          toast.error('Failed to update damage statuses');
        }
      }
    } catch (error: any) {
      toast.error(
        'Error updating damage status: ' +
          (error.response?.data?.message || error.message),
      );
    } finally {
      setLoading(false);
    }
  };

  const getItemDescription = (item: any) => {
    const vin = item.vehicles?.vin || 'N/A';
    const caseNumber = item.case_number
      ? `PGLDMG0${item.case_number}`
      : `ID: ${item.id}`;
    return `${caseNumber} (${vin})`;
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Change Damage Status
        {selectedItems.length > 1 && (
          <Typography variant="subtitle2" color="text.secondary">
            {selectedItems.length} items selected
          </Typography>
        )}
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Selected Damage{selectedItems.length > 1 ? 's' : ''}:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedItems.slice(0, 5).map((item, index) => (
              <Chip
                key={index}
                label={getItemDescription(item)}
                size="small"
                variant="outlined"
              />
            ))}
            {selectedItems.length > 5 && (
              <Chip
                label={`+${selectedItems.length - 5} more`}
                size="small"
                variant="outlined"
                color="primary"
              />
            )}
          </Box>
        </Box>

        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel>New Damage Status</InputLabel>
          <Select
            value={selectedStatus}
            label="New Damage Status"
            onChange={handleStatusChange}
          >
            {processStatusOptions.map((option) => {
              const restrictedStatuses = [
                'In_process',
                'Pending_CA',
                'Forgotten',
              ];
              const isRestricted = restrictedStatuses.includes(option.id);

              // Check if any selected items have auction damage
              const hasAuctionDamage = selectedItems.some((item) => {
                return item.damage_happened_at?.some(
                  (happenedAt) =>
                    happenedAt.happened_at === 'Auction' ||
                    happenedAt === 'Auction',
                );
              });

              const isDisabled = isRestricted && hasAuctionDamage;

              return (
                <MenuItem
                  key={option.id}
                  value={option.id}
                  disabled={isDisabled}
                  sx={{
                    opacity: isDisabled ? 0.5 : 1,
                    '&.Mui-disabled': {
                      opacity: 0.5,
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: isDisabled ? '#ccc' : option.color,
                      }}
                    />
                    {option.label}
                    {isDisabled && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ ml: 1 }}
                      >
                        (Not allowed for auction damages)
                      </Typography>
                    )}
                  </Box>
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>

        {selectedStatus && (
          <Box
            sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}
          >
            <Typography variant="body2" color="text.secondary">
              <strong>Confirmation:</strong> You are about to change the status
              of{' '}
              {selectedItems.length === 1
                ? 'this damage'
                : `${selectedItems.length} damages`}{' '}
              to "
              {
                processStatusOptions.find((opt) => opt.id === selectedStatus)
                  ?.label
              }
              ".
              {selectedItems.length > 1 && (
                <> The records will move to the appropriate tab after update.</>
              )}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          loading={loading}
          variant="contained"
          disabled={!selectedStatus}
        >
          Update Status
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DamageStatusModal;
