import CStepper from '@/components/mainComponents/stepper/CStepper';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import InfoIcon from '@mui/icons-material/Info';
import Step1 from './Add/Step1';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import {
  claim_status_options,
  currency_options,
  damage_status_options,
  DamageSchema,
} from '@/configs/vehicles_damage/damageHeader';

export const CreateDamage = ({
  show,
  setShow,
  // add,
  // update,
  isUpdate,
  selectedItems,
  recordManager,
  setSelectedItems,
}) => {
  const [isDone, setIsDone] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { isValid, errors },
    reset,
    trigger,
    clearErrors,
    watch,
    setValue,
    control,
  } = useForm({
    mode: 'onChange',
    resolver: zodResolver(DamageSchema),
    defaultValues: {
      vehicle_id: null,
      damage_type: null,
      damage_details: [],
      case_number: null,
      inspection_photo: null,
      damage_happened_at: [],
      case_received_date: new Date().toISOString().split('T')[0], // Set current date as default,
      claim_currency: currency_options[0].id,
      claim: 0,
      confirmed_currency: currency_options[0].id,
      confirmed: 0,
      credit: 0,
      email_sent_at: new Date().toISOString().split('T')[0],
      credit_currency: currency_options[0].id,
      lc_credit: 0,
      lc_credit_currency: currency_options[0].id,
      claim_status: claim_status_options[0].id,
      remark: null,
      damage_status: damage_status_options[0].id,
      credited_at: null,
    },
  });

  const form = {
    register,
    errors,
    reset,
    clearErrors,
    isValid,
    trigger,
    watch,
    control,
  };
  const [loadingButton, setLoadingButton] = useState(false);

  const submit = async (values) => {
    try {
      setLoadingButton(true);

      // Validate damage status for auction damages before submission
      const restrictedStatuses = ['In_process', 'Pending_CA', 'Forgotten'];

      if (restrictedStatuses.includes(values.damage_status)) {
        // Check if damage happened at auction
        const hasAuctionDamage = values.damage_happened_at?.some(
          (item) => item.happened_at === 'Auction' || item === 'Auction',
        );

        if (hasAuctionDamage) {
          const vin =
            selectedItems?.vehicles?.vin ||
            `ID:${selectedItems?.id || 'Unknown'}`;
          toast.error(
            `Cannot set status to ${values.damage_status.replace('_', ' ')} for damage that happened at Auction (VIN: ${vin})`,
          );
          setLoadingButton(false);
          return;
        }
      }

      const response = await axios[isUpdate ? 'patch' : 'post'](
        isUpdate ? `vehicle_damages/${selectedItems?.id}` : 'vehicle_damages',
        values,
      );

      if (response.data.result === true) {
        recordManager(response.data.data, isUpdate ? 'update' : 'add');
        setIsDone(true);
        setLoadingButton(false);
        toast.success(
          `Damage Case ${isUpdate ? 'updated' : 'added'} successfully!`,
        );
      } else {
        toast.error(`Failed to ${isUpdate ? 'update' : 'add'} Damage Case.`);
      }
    } catch (error) {
      // More specific error handling
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (
        error.response?.status === 409 ||
        error.message?.includes('unique constraint')
      ) {
        // Only show "already exists" message for actual constraint violations
        toast.error(
          isUpdate
            ? 'Cannot update: Another damage record already exists for this vehicle'
            : 'Vehicle already exists in the Damage',
        );
      } else if (error.response?.status === 400) {
        toast.error(
          'Invalid data provided. Please check all fields and try again.',
        );
      } else if (error.response?.status === 403) {
        toast.error('You do not have permission to perform this action.');
      } else if (error.response?.status === 404 && isUpdate) {
        toast.error('Damage record not found. It may have been deleted.');
      } else {
        toast.error(
          `Failed to ${isUpdate ? 'update' : 'add'} Damage Case. Please try again.`,
        );
      }
    } finally {
      setLoadingButton(false);
    }
  };
  const steps = [
    {
      label: 'General Info',
      icon: <InfoIcon />,
      step: <Step1 form={form} isUpdate={isUpdate} data={selectedItems} />,
      props: {
        isUpdate,
      },
      async validate() {
        const isValid = await trigger([
          'vehicle_id',
          'damage_type',
          'damage_details',
          'inspection_photo',
          'damage_happened_at',
          'case_received_date',
          'claim_currency',
          'claim',
          'confirmed_currency',
          'confirmed',
          'credit_currency',
          'credit',
          'lc_credit_currency',
          'lc_credit',
          'claim_status',
          'damage_status',
          'remark',
          'email_sent_at',
        ]);
        return isValid;
      },
    },
  ];

  useEffect(() => {
    if (selectedItems) {
      setValue('vehicle_id', selectedItems?.vehicle_id);
      setValue('damage_type', selectedItems?.damage_type);
      setValue('damage_details', selectedItems?.damage_details);
      setValue('inspection_photo', selectedItems?.inspection_photo);
      setValue('damage_happened_at', selectedItems?.damage_happened_at);
      setValue('remark', selectedItems?.remark);
      setValue('case_received_date', selectedItems?.case_received_date);
      setValue('claim', selectedItems?.claim);
      setValue('claim_currency', selectedItems?.claim_currency);
      setValue('credit', selectedItems?.credit);
      setValue('credit_currency', selectedItems?.credit_currency);
      setValue('lc_credit', selectedItems?.lc_credit);
      setValue('lc_credit_currency', selectedItems?.lc_credit_currency);
      setValue('confirmed', selectedItems?.confirmed);
      setValue('confirmed_currency', selectedItems?.confirmed_currency);
      setValue('claim_status', selectedItems?.claim_status);
      setValue('damage_status', selectedItems?.damage_status);
      setValue('email_sent_at', selectedItems?.email_sent_at);
      setValue('credited_at', selectedItems?.credited_at);
    }
  }, [selectedItems]);

  return (
    <form>
      <CStepper
        loadingButton={loadingButton}
        setSelectedItems={setSelectedItems}
        show={show}
        setShow={setShow}
        steps={steps}
        form={form}
        submit={handleSubmit(submit)}
        done={isDone}
        setDone={setIsDone}
        title={isUpdate ? 'Update Damage' : 'Create Damage'}
        isUpdate={isUpdate}
      />
    </form>
  );
};
