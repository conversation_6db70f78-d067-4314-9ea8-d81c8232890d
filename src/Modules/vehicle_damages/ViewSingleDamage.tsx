import ProfileCustomRow from '@/components/mainComponents/ProfileCustomRow';
import ViewModal from '@/components/mainComponents/ViewModal';
import TabPanel from '@mui/lab/TabPanel';
import { Box, Chip } from '@mui/material';
import { ImageIcon } from 'lucide-react';
import moment from 'moment';
import {
  damage_happened_at_options,
  damage_type_options,
  pol_options,
  claim_status_options,
  damage_status_options,
} from '@/configs/vehicles_damage/damageHeader';
import { ImageNotSupported } from '@mui/icons-material';
import { useState } from 'react';
import VehicleImagesModal from './VehicleImagesModal';
import {
  destinationColor,
  destinationTextColor,
} from '@/configs/booking/bookingHeader';
// import customers from '@/pages/general/customers';

const ViewSingleDamage = ({
  loading = false,
  show,
  setView,
  data,
}): React.ReactNode => {
  const [showAuctionImagesModal, setShowAuctionImagesModal] = useState(false);
  const [showWarehouseImagesModal, setShowWarehouseImagesModal] =
    useState(false);

  const tabs = [
    { label: 'Vehicle Info', value: 'general_info' },
    { label: 'Damage Info', value: 'damage_info' },
  ];

  if (!data) {
    return null;
  }

  const hasAuctionImages =
    data?.vehicles?.vehicle_images &&
    Array.isArray(data.vehicles.vehicle_images) &&
    data.vehicles.vehicle_images.some((img) => img.type === 'auction');

  const hasWarehouseImages =
    data?.vehicles?.vehicle_images &&
    Array.isArray(data.vehicles.vehicle_images) &&
    data.vehicles.vehicle_images.some((img) => img.type === 'warehouse');

  return (
    <>
      <ViewModal
        createdBy={`${data?.users_vehicle_damages_created_by_users?.fullname} | ${data?.users_vehicle_damages_created_by_users?.departments?.name}`}
        name={data?.name}
        created_at={data?.created_at}
        updated_at={data?.updated_at}
        onClose={() => setView(false)}
        show={show}
        tabs={tabs}
        title={`Damage Case Profile ${data?.id || ''}`}
        loading={loading}
        entity="vehicle_damages"
        entity_id={data?.id}
      >
        <Box sx={{ height: '250px', overflow: 'auto', overflowX: 'hidden' }}>
          <TabPanel value="general_info" sx={{ px: 0, py: 1 }}>
            <ProfileCustomRow
              itemName="Vehicle Description:"
              itemText={`${data?.vehicles?.year ?? ''} ${data?.vehicles?.make ?? ''} ${data?.vehicles?.model ?? ''}`}
              itemName2="Company:"
              itemText2={data?.vehicles?.customers?.companies?.name ?? '-'}
            />
            <ProfileCustomRow
              bgColor
              itemName="Container Number:"
              itemText={data?.vehicles?.containers?.container_number ?? '-'}
              itemName2="VIN Number:"
              itemText2={data?.vehicles?.vin ?? '-'}
            />
            <ProfileCustomRow
              itemName="Loading Date:"
              itemText={
                data?.vehicles?.containers?.loading_date
                  ? moment(data?.vehicles?.containers?.loading_date).format(
                      'YYYY MMMM DD',
                    )
                  : '-'
              }
              itemName2="ETA:"
              itemText2={
                data?.vehicles?.containers?.bookings?.eta
                  ? moment(data?.vehicles?.containers?.bookings?.eta).format(
                      'YYYY MMMM DD',
                    )
                  : '-'
              }
            />
            <ProfileCustomRow
              bgColor
              itemName="ETD:"
              itemText={
                data?.vehicles?.containers?.bookings?.vessels?.etd
                  ? moment(
                      data?.vehicles?.containers?.bookings?.vessels?.etd,
                    ).format('YYYY MMMM DD')
                  : '-'
              }
              itemName2="Vehicle Price:"
              itemText2={
                data?.vehicles?.price
                  ? `$${data?.vehicles?.price.toLocaleString()}`
                  : '-'
              }
            />
            <ProfileCustomRow
              itemName="Loaded By:"
              itemText={
                data?.vehicles?.containers?.loaders?.length
                  ? data?.vehicles?.containers?.loaders
                      ?.map((loader) => loader?.name ?? '')
                      .join(', ')
                  : '-'
              }
              itemName2="Container Number:"
              itemText2={data?.vehicles?.containers?.container_number ?? '-'}
            />
            <ProfileCustomRow
              bgColor
              itemName="POL:"
              itemText={
                data?.vehicles?.pol_locations?.name ? (
                  <Chip
                    size="small"
                    label={
                      data?.vehicles?.pol_locations?.name?.split(',')[1] ?? ''
                    }
                    sx={{
                      backgroundColor:
                        pol_options.find(
                          (item) =>
                            item.id ===
                            data?.vehicles?.pol_locations?.name
                              ?.split(',')[1]
                              ?.trim(),
                        )?.color ?? '#6A5ACD',
                      color: '#fff',
                      height: 'auto',
                      fontSize: '12px',
                      '& .MuiChip-label': { padding: '2px 6px' },
                    }}
                  />
                ) : (
                  '-'
                )
              }
              itemName2=""
              itemText2=""
            />
            <ProfileCustomRow
              itemName="POD:"
              itemText={
                data?.vehicles?.containers?.bookings?.destinations?.name ? (
                  <Chip
                    size="small"
                    label={
                      data?.vehicles?.containers?.bookings?.destinations?.name
                    }
                    sx={{
                      backgroundColor:
                        destinationColor[
                          data?.vehicles?.containers?.bookings?.destinations?.id
                        ],
                      color:
                        destinationTextColor[
                          data?.vehicles?.containers?.bookings?.destinations?.id
                        ] ?? 'white',
                      height: 'auto',
                      fontSize: '12px',
                      '& .MuiChip-label': { padding: '2px 6px' },
                    }}
                  />
                ) : (
                  '-'
                )
              }
              itemName2=""
              itemText2=""
            />
            <ProfileCustomRow
              bgColor
              itemName="Auction Photo:"
              itemText={
                data?.vehicles?.auction_photos_link || hasAuctionImages ? (
                  <Box
                    component="a"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowAuctionImagesModal(true);
                    }}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      cursor: 'pointer',
                      color: '#3f50b5',
                    }}
                  >
                    <ImageIcon style={{ color: '#3f50b5' }} size={24} />
                  </Box>
                ) : (
                  <span style={{ color: '#999' }}>
                    {' '}
                    <ImageNotSupported style={{ color: '#3f50b5' }} /> No photos
                  </span>
                )
              }
              itemName2="Loading Photo:"
              itemText2={
                data?.photo_link_loading ? (
                  <a
                    href={data?.photo_link_loading}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                    }}
                  >
                    <ImageIcon style={{ color: '#3f50b5' }} size={24} />
                  </a>
                ) : (
                  <span style={{ color: '#999' }}>
                    {' '}
                    <ImageNotSupported style={{ color: '#3f50b5' }} /> No photos
                  </span>
                )
              }
            />
            <ProfileCustomRow
              itemName="R.Photo:"
              itemText={
                data?.vehicles?.photo_link || hasWarehouseImages ? (
                  <Box
                    component="a"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowWarehouseImagesModal(true);
                    }}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      cursor: 'pointer',
                      color: '#3f50b5',
                    }}
                  >
                    <ImageIcon style={{ color: '#3f50b5' }} size={24} />
                  </Box>
                ) : (
                  <span style={{ color: '#999' }}>
                    {' '}
                    <ImageNotSupported style={{ color: '#3f50b5' }} /> No photos
                  </span>
                )
              }
              itemName2="Inspection Photo:"
              itemText2={
                data?.inspection_photo ? (
                  <a
                    href={data?.inspection_photo}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                    }}
                  >
                    <ImageIcon style={{ color: '#3f50b5' }} size={24} />
                  </a>
                ) : (
                  <span style={{ color: '#999' }}>
                    {' '}
                    <ImageNotSupported style={{ color: '#3f50b5' }} /> No photos
                  </span>
                )
              }
            />
          </TabPanel>
          <TabPanel value="damage_info" sx={{ px: 0, py: 1 }}>
            <ProfileCustomRow
              itemName="Damage Type:"
              itemText={
                <span
                  style={{
                    backgroundColor:
                      damage_type_options.find(
                        (option) => option.name === data?.damage_type,
                      )?.color ?? '#fff',
                    padding: '4px 6px',
                    color: '#fff',
                    fontSize: '12px',
                    borderRadius: '4px',
                  }}
                >
                  {data?.damage_type ?? '-'}
                </span>
              }
              itemName2="Damage Details:"
              itemText2={
                Array.isArray(data?.damage_details) &&
                data?.damage_details?.length > 0
                  ? data?.damage_details.map((d) => (
                      <span
                        key={d?.detail ?? 'unknown'}
                        style={{
                          backgroundColor: '#3f50b5',
                          margin: '0 4px',
                          padding: '2px 6px',
                          color: '#fff',
                          fontSize: '12px',
                          borderRadius: '4px',
                        }}
                      >
                        {d?.detail ?? '-'}
                      </span>
                    ))
                  : (data?.damage_case_detail ?? '-')
              }
            />
            <ProfileCustomRow
              bgColor
              itemName="Email Sent At:"
              itemText={
                data?.email_sent_at
                  ? moment(data?.email_sent_at).format('YYYY MMMM DD hh:mm A')
                  : '-'
              }
              itemName2="Case Number:"
              itemText2={data?.case_number || '-'}
            />
            <ProfileCustomRow
              itemName="Damage Happened at:"
              itemText={
                Array.isArray(data?.damage_happened_at) &&
                data?.damage_happened_at?.length > 0
                  ? data?.damage_happened_at.map((d) => {
                      const damageOption = damage_happened_at_options.find(
                        (item) => item?.id === d?.happened_at,
                      );
                      const color = damageOption?.color ?? '#6A5ACD';
                      return (
                        <span
                          key={d?.happened_at ?? 'unknown'}
                          style={{
                            backgroundColor: color,
                            margin: '0 4px',
                            padding: '2px 6px',
                            color: '#fff',
                            fontSize: '12px',
                            borderRadius: '4px',
                          }}
                        >
                          {`${d?.happened_at?.charAt(0) ?? ''}_${d?.happened_at ?? ''}`}
                        </span>
                      );
                    })
                  : '-'
              }
              itemName2="Case Received Date:"
              itemText2={
                data?.case_received_date
                  ? moment(data?.case_received_date).format('YYYY MMMM DD')
                  : '-'
              }
            />
            <ProfileCustomRow
              bgColor
              itemName="Claim:"
              itemText={
                data?.claim
                  ? `${data?.claim?.toLocaleString()} ${data?.claim_currency ?? ''}`
                  : '-'
              }
              itemName2="Confirmed:"
              itemText2={
                data?.confirmed
                  ? `${data?.confirmed?.toLocaleString()} ${data?.confirmed_currency ?? ''}`
                  : '-'
              }
            />
            <ProfileCustomRow
              itemName="Credited:"
              itemText={
                data?.credit
                  ? `${data?.credit?.toLocaleString()} ${data?.credit_currency ?? ''}`
                  : '-'
              }
              itemName2="LC.Credit:"
              itemText2={
                data?.lc_credit
                  ? `${data?.lc_credit?.toLocaleString()} ${data?.lc_credit_currency ?? ''}`
                  : '-'
              }
            />
            <ProfileCustomRow
              bgColor
              itemName="Claim Status:"
              itemText={
                data?.claim_status ? (
                  <span
                    style={{
                      backgroundColor:
                        claim_status_options.find(
                          (option) => option?.id === data?.claim_status,
                        )?.color ?? '#fff',
                      padding: '4px 6px',
                      color: '#fff',
                      fontSize: '12px',
                      borderRadius: '4px',
                    }}
                  >
                    {data?.claim_status ?? '-'}
                  </span>
                ) : (
                  '-'
                )
              }
              itemName2="Damage Status:"
              itemText2={
                data?.damage_status ? (
                  <span
                    style={{
                      backgroundColor:
                        damage_status_options.find(
                          (option) => option?.id === data?.damage_status,
                        )?.color ?? '#fff',
                      padding: '4px 6px',
                      color: '#fff',
                      fontSize: '12px',
                      borderRadius: '4px',
                    }}
                  >
                    {data?.damage_status ?? '-'}
                  </span>
                ) : (
                  '-'
                )
              }
            />
            <ProfileCustomRow
              itemName="Credited At:"
              itemText={
                data?.credited_at
                  ? moment(data?.credited_at).format('YYYY MMMM DD hh:mm A')
                  : '-'
              }
              itemName2="Credited By:"
              itemText2={
                data?.users_vehicle_damages_credited_by?.fullname || '-'
              }
            />
            <ProfileCustomRow
              itemName="Remark:"
              itemText={data?.remark ?? '-'}
              itemName2=""
              itemText2=""
            />
            <ProfileCustomRow
              bgColor
              itemName="Created At"
              itemText={
                data?.created_at
                  ? moment(data?.created_at).format('YYYY MMMM DD hh:mm A')
                  : '-'
              }
              itemName2="Updated At"
              itemText2={
                data?.updated_at
                  ? moment(data?.updated_at).format('YYYY MMMM DD hh:mm A')
                  : '-'
              }
            />
          </TabPanel>
          <TabPanel value="logs" sx={{ px: 0, py: 1 }}></TabPanel>
        </Box>
      </ViewModal>

      {showAuctionImagesModal && data?.vehicles && (
        <VehicleImagesModal
          open={showAuctionImagesModal}
          setOpen={setShowAuctionImagesModal}
          vehicleData={data.vehicles}
          imageType="auction"
        />
      )}

      {showWarehouseImagesModal && data?.vehicles && (
        <VehicleImagesModal
          open={showWarehouseImagesModal}
          setOpen={setShowWarehouseImagesModal}
          vehicleData={data.vehicles}
          imageType="warehouse"
        />
      )}
    </>
  );
};

export default ViewSingleDamage;
