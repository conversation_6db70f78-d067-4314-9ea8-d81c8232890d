import {
  Autocomplete,
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import { pageTitle, currencies } from './ExchangeRatesHeader';
export default function ExchangeRateStep1({ form }) {
  //@ts-ignore
  const [activeCollapes, setActiveCollapes] = useState(0);
  const theme = useTheme();
  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        {pageTitle} Details
      </Typography>
      <Card
        sx={{
          mb: 1,
          backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
        }}
      >
        <CardContent>
          <Grid container spacing={2} sx={{ paddingBottom: '10px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`currency`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={field.value}
                    getOptionLabel={(option) => (option ? option : '')}
                    onChange={(_event: any, newValue) => {
                      form.setValue(`currency`, newValue);
                    }}
                    options={currencies}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Currency"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="rate"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.rate?.message.length > 0 ? true : false}
                    id="rate"
                    value={field.value ?? ''}
                    label="Rate"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) => field.onChange(+value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}
