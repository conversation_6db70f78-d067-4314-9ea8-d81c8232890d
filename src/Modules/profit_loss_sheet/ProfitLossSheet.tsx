import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import {
  Box,
  Button,
  Container,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  Tooltip,
  useTheme,
} from '@mui/material';
import Head from 'next/head';
import React, { useContext, useEffect, useState } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import {
  breadcrumbs,
  PLSheet_url,
  pageTitle,
  tableOptions,
} from './ProfitLossSheetHeaders';
import { CONTAINERS } from '@/configs/leftSideMenu/Permissions';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import BasicEditingGrid from './GridTable';
import { Controller, useForm } from 'react-hook-form';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { toast } from 'react-toastify';
import AddIcon from '@mui/icons-material/Add';
import {
  updatePLProfiles,
  viewPLProfiles,
} from '@/configs/shipment/ProfitLost';
import { Close, Delete } from '@mui/icons-material';
const BankAccounts = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableRecords, setTableRecords] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [cancelToken, setCancelToken] = useState(null);
  const [options, setOptions] = useState(tableOptions);
  const [isOpen, setIsOpen] = useState(false);
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const lowerCaseEmail = profile?.data?.loginable?.email.toLowerCase();
  if (viewPLProfiles.includes(lowerCaseEmail)) {
    perms.push('view_profit_loss');
  }
  if (updatePLProfiles.includes(lowerCaseEmail)) {
    perms.push('update_profit_loss');
  }
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      const params = {
        page: options.page,
        per_page: options.perPage,
        search: options.search,
      };
      let { data } = await axios.get(PLSheet_url, {
        signal: controller.signal,
        params,
      });
      setTotalItems(data.total);
      setTableRecords(data.data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, [options.page, options.perPage, options.search, options.filterData]);
  const { control, handleSubmit } = useForm({
    defaultValues: {
      container_id: null,
    },
    mode: 'onChange',
  });
  const onSubmit = async (values: any) => {
    if (!values?.container_id) return toast.error('Please Select a Container!');
    await axios
      .post(PLSheet_url, {
        container_id: values.container_id,
      })
      .then((res) => {
        if (!res.data.result) {
          return toast.error(res?.data?.message);
        }
        toast.success('Record Registered Successfully');
        setIsOpen(false);
        fetchRecords();
      })
      .catch(() => {
        toast.error('Something went wrong!');
      });
  };
  const deleteRecords = async () => {
    setLoading(true);
    await axios
      .delete(`${PLSheet_url}/remove`, {
        data: { ids: selectedRows },
      })
      .then((res) => {
        if (res.data) {
          setSelectedRows([]);
          fetchRecords();
          toast.success(res.data?.message);
        }
      });
    setLoading(false);
  };
  return perms && !perms?.includes(CONTAINERS?.VIEW_PROFIT_LOSS) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>{pageTitle}</title>
      </Head>

      <Container
        sx={{ maxWidth: 'unset !important', padding: '0 10px !important' }}
      >
        <PageHeader breadcrumbs={breadcrumbs} />
        {perms?.includes(CONTAINERS?.UPDATE_PROFIT_LOSS) ? (
          <Box className="m-2 flex justify-end">
            {selectedRows.length ? (
              <Tooltip title="Delete">
                <Button disabled={loading} onClick={() => deleteRecords()}>
                  <Delete color="error" />
                </Button>
              </Tooltip>
            ) : null}
            <Tooltip title="Add New">
              <Button>
                <AddIcon color="success" onClick={() => setIsOpen(true)} />
              </Button>
            </Tooltip>
          </Box>
        ) : null}
        <BasicEditingGrid
          fetchData={fetchRecords}
          data={tableRecords}
          options={options}
          setOptions={setOptions}
          totalItems={totalItems}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
        />
        <Dialog open={isOpen} onClose={() => setIsOpen(false)} maxWidth="md">
          <DialogTitle id="responsive-dialog-title w-full">
            <div className="flex justify-between">
              <h3>Add New PL Sheet Record</h3>
              <Button size="small">
                <Close color="error" onClick={() => setIsOpen(false)} />
              </Button>
            </div>
          </DialogTitle>
          <DialogContent>
            <Grid size={12}>
              <Controller
                control={control}
                name="container_id"
                render={({ field, fieldState: { error } }) => {
                  return (
                    <AutoComplete
                      style={{ width: '300px' }}
                      sx={error ? { mt: 1.5 } : { my: 1.5 }}
                      url="autoComplete"
                      label="Select Container"
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                      fieldName="container_number"
                      field={field}
                      error={error}
                      staticOptions={false}
                      column={'container_number'}
                      modal={'containers'}
                    />
                  );
                }}
              />
            </Grid>
            <Button
              variant="contained"
              color="success"
              onClick={handleSubmit(onSubmit)}
            >
              Save
            </Button>
          </DialogContent>
        </Dialog>
      </Container>
    </>
  );
};
export default BankAccounts;
