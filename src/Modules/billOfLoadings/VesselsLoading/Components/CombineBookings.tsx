import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { Box, Button, CircularProgress, Grid, TextField } from '@mui/material';
import { Fragment, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import CloseIcon from '@mui/icons-material/Close';
import { CombineSchema } from '../Header';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from '@/lib/axios';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import JoinFullIcon from '@mui/icons-material/JoinFull';
import { toast } from 'react-toastify';

const CombineBooking = ({
  selectedBookings,
  setSelectBooking,
  setBillOfLoading,
  vessels,
  setReload,
}) => {
  const [loadingButton, setLoadingButton] = useState(false);
  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(CombineSchema),
    defaultValues: {
      master_id: null,
      master_bk: null,
    },
  });

  const master_id = form.watch('master_id');
  useEffect(() => {
    form.setValue(
      'master_bk',
      selectedBookings.find((item) => item.id == master_id)?.label,
    );
  }, [master_id]);

  const handleSubmit = async (params) => {
    setLoadingButton(true);

    try {
      // Change the size to hc40 or hc45
      const normalizedSizes = new Set(
        selectedBookings.map((obj) => {
          let size = obj.size.replace(/\s+/g, '').toLowerCase(); // "40hc" or "45hc"
          return size.replace(/(\d+)(hc)/, 'hc$1'); // Convert "40hc" to "hc40", etc.
        }),
      );

      // Return single size if all are the same, otherwise return "hc40_hc45"
      const size =
        normalizedSizes.size === 1 ? [...normalizedSizes][0] : 'hc40_hc45';

      const childIds = selectedBookings
        .filter((ob) => ob.id !== params?.master_id)
        .map((item) => item.id);

      const formData = {
        ...params,
        type: vessels?.type,
        vessel_id:
          vessels?.port_of_loading == 1
            ? vessels?.item?.ga_vid
            : vessels?.vessel_id,
        port_of_loading: vessels?.port_of_loading,
        childIds,
        size,
        qty: selectedBookings.reduce(
          (sum, obj) => sum + obj.containers?.length,
          0,
        ),
      };

      const { data } = await axios.post(
        '/vessels-loading/combineBooking',
        formData,
      );
      setLoadingButton(false);
      setBillOfLoading(0);
      setSelectBooking([]);
      setReload(Math.random());
      if (data?.result) {
        toast.success('Done successfully!');
      }
    } catch (error) {
      setLoadingButton(false);
      setSelectBooking([]);
      const errorMessage =
        error.response?.data?.message || 'Something went wrong';
      toast.error(errorMessage);
    }
  };

  return (
    <Fragment>
      <Box
        sx={{
          mx: 2,
          textTransform: 'uppercase',
        }}
      >
        Combine These Bookings
      </Box>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Box
          sx={{
            mx: 2,
            p: 1,
            borderRadius: '10px',
            border: '1px solid gray',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              borderBottom: 1,
              mb: 1,
              pb: 1,
              flexWrap: 'wrap',
              columnGap: 2,
            }}
          >
            {selectedBookings.map((item, index) => (
              <Box
                key={index}
                sx={{
                  py: 1,
                  px: 2,
                  m: '5px',
                  width: '18%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  borderRadius: '10px',
                  fontSize: '12px',
                  border: '1px solid gray',
                  background: 'rgba(26, 102, 214, 0.1)',
                }}
              >
                <Box>
                  <Box>Booking #: {item?.label}</Box>
                  <Box>Loads : {item?.containers?.length}</Box>
                </Box>

                <Box>
                  <CheckCircleIcon color="success" />
                </Box>
              </Box>
            ))}
          </Box>

          <Grid container spacing={2}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="master_id"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Master Booking #"
                    fieldName=""
                    field={field}
                    error={error}
                    staticOptions={selectedBookings}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="master_bk"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error?.message.length > 0 ? true : false}
                    id="master_bk"
                    value={field.value ?? ''}
                    label="Bill Of Loading Number"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            {/* <Grid item xs={12} md={6}>
              <Controller
                name="shipline_bl_number"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error?.message.length > 0 ? true : false}
                    id="shipline_bl_number"
                    value={field.value}
                    label="Shipline BL Number"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="freight_amount"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error?.message.length > 0 ? true : false}
                    id="freight_amount"
                    value={field.value}
                    label="Freight Amount"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="freight_AED"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error?.message.length > 0 ? true : false}
                    id="freight_AED"
                    value={field.value}
                    label="Freight AED"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="cnee"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error?.message.length > 0 ? true : false}
                    id="cnee"
                    value={field.value}
                    label="CNEE"
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="paymentType"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Payment Type"
                    fieldName=""
                    field={field}
                    error={error}
                    staticOptions={PaymentType}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="paymentStatus"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <AutoComplete
                    url={false}
                    label="Payment Status"
                    fieldName=""
                    field={field}
                    error={error}
                    staticOptions={PaymentStatus}
                    column={''}
                    modal={''}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={form.control}
                name="payment_date"
                render={({ field, fieldState: { error } }) => (
                  <DatePicker
                    views={['year', 'month', 'day']}
                    label="Payment Date"
                    value={!field.value ? null : dayjs(field.value).toDate()}
                    format="yyyy/MM/dd"
                    onChange={(e) => field.onChange(formFormatDate(e))}
                    inputRef={field.ref}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !!error,
                        helperText: error?.message,
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={12}>
              <Controller
                control={form.control}
                name="remarks"
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    error={error?.message.length > 0 ? true : false}
                    id="remarks"
                    label="Remarks"
                    multiline
                    fullWidth
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid> */}
          </Grid>
        </Box>
        <Box
          sx={{
            my: 1,
            mx: 2,
            display: 'flex',
            justifyContent: 'end',
            gap: 1,
          }}
        >
          <Button
            variant="contained"
            onClick={() => setBillOfLoading(0)}
            color="error"
            sx={{ color: 'white' }}
            startIcon={<CloseIcon />}
            size="small"
          >
            Cancel
          </Button>

          <Button
            variant="contained"
            type="submit"
            color="success"
            sx={{ color: 'white' }}
            startIcon={
              loadingButton ? (
                <CircularProgress size={20} sx={{ color: 'white' }} />
              ) : (
                <JoinFullIcon />
              )
            }
            size="small"
            disabled={loadingButton}
          >
            {loadingButton ? 'Combining...' : 'Combine Bookings'}
          </Button>
        </Box>
      </form>
    </Fragment>
  );
};

export default CombineBooking;
