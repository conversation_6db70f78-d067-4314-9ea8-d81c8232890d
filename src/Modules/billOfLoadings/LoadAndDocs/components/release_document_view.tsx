import { Box } from '@mui/system';
import { forwardRef } from 'react';
import {
  Table,
  TableCell,
  TableRow,
  TableBody,
  Typography,
} from '@mui/material';
import Image from 'next/image';
import logo from '@/public/static/images/logo-500.webp';
import moment from 'moment';
import Head from 'next/head';

const ReleaseDocumentView = forwardRef<HTMLDivElement, any>(
  (props: any, ref) => {
    const { data } = props;
    const isMultiContainer = Array.isArray(data);
    const containers = isMultiContainer ? data : [data];
    const primaryContainer = containers[0];

    if (!data || (Array.isArray(data) && data.length === 0)) {
      return (
        <>
          <Head>
            <title>PGL Bill of Loading / Shipping Instruction</title>
          </Head>
          <Box
            ref={ref}
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            No Content to print
          </Box>
        </>
      );
    }
    const printStyles: { [key: string]: any } = {
      table: {
        fontFamily: '"Trebuchet MS", Arial, Helvetica, sans-serif',
        borderSpacing: '0',
        fontSize: '11px',
        width: '21cm',
        color: 'black',
      },
      td1: {
        boxSizing: 'border-box',
        border: '2px solid #0a0a0a',
        textAlign: 'left',
        paddingLeft: '3px',
        padding: '3px',
        paddingRight: '3px',
        fontSize: '11px',
        borderCollapse: 'collapse',
        color: 'black',
      },
      p: { color: 'blue', fontWeight: 'bold', fontSize: '11px' },
    };
    const cDate = (d) =>
      moment(d).isValid() ? moment(d).utc().format('YYYY-MM-DD') : '';

    const getTemp = (name) => {
      if (!name) return 'XX';
      if (name.includes('ONE')) return 'OL';
      if (name.includes('MEARSK') || name.includes('MAERSK')) return 'MK';
      if (name.includes('HAPAG')) return 'HG';
      if (name.includes('YANGMING')) return 'YM';
      if (name.includes('MSC')) return 'MC';
      return name.substring(0, 2);
    };

    const bolNumber = isMultiContainer
      ? containers
          .map((c) => {
            const bookingNumber = c?.bookings?.booking_number?.slice(-5);

            const invNumber =
              c?.invoice_number &&
              c?.invoice_number?.replace(/-/g, '').slice(-4);
            const temp = getTemp(c?.bookings?.vessels?.steamshiplines?.name);
            return `AE${bookingNumber}${temp}${invNumber}`;
          })
          .join(', ')
      : (() => {
          const bookingNumber =
            primaryContainer?.bookings?.parent_id != undefined
              ? primaryContainer?.bookings?.parent?.booking_number?.slice(-5)
              : primaryContainer?.bookings?.booking_number?.slice(-5) ||
                '00000';

          const invNumber =
            primaryContainer?.invoice_number?.slice(-4) || '0000';
          const temp = getTemp(
            primaryContainer?.bookings?.vessels?.steamshiplines?.name,
          );
          return `AE${bookingNumber}${temp}${invNumber}`;
        })();
    const a4Width = '21cm';
    const a4Height = '29.7cm';

    const PrintRow = ({ cells }) => (
      <TableRow>
        {cells.map((row, index) => (
          <TableCell
            key={index}
            sx={printStyles.td1}
            rowSpan={row.rowSpan}
            colSpan={row.colSpan}
            style={row.center ? { textAlign: 'center' } : {}}
          >
            <Typography
              sx={printStyles.p}
              style={row.style ? { fontSize: 11 } : {}}
            >
              {row.label}
            </Typography>
            {row?.value}
          </TableCell>
        ))}
      </TableRow>
    );

    return (
      <>
        <Head>
          <title>PGL Bill of Loading / Shipping Instruction</title>
        </Head>
        <Box
          ref={ref}
          width={a4Width}
          height={a4Height}
          p={5}
          pl={2.6}
          id="print"
          sx={{ background: 'white' }}
        >
          <Box
            sx={{
              fontSize: '16px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
            }}
            mb="30px"
          >
            <Image src={logo} height="40" alt="logo" />
            &nbsp;&nbsp;&nbsp;{' '}
            <Box component="p" sx={{ color: 'blue' }}>
              PGL Bill of Loading / Shipping Instruction
            </Box>
          </Box>
          <Table>
            <TableBody>
              <PrintRow
                cells={[
                  {
                    label: 'SHIPPER / EXPORTER',
                    value: (
                      <>
                        {primaryContainer?.shipping_documents?.shipper_exporter}
                        <br />
                        {primaryContainer?.shipping_documents
                          ?.shipper_street_address ?? ''}
                        <br />
                        {primaryContainer?.shipping_documents?.shipper_city ??
                          ''}
                        ,
                        {primaryContainer?.shipping_documents?.shipper_state ??
                          ''}
                        ,
                        {primaryContainer?.shipping_documents
                          ?.shipper_zip_code ?? ''}
                        <br />
                        {primaryContainer?.shipping_documents
                          ?.shipper_email_address ?? ''}
                        <br />
                        Phone:
                        {primaryContainer?.shipping_documents
                          ?.shipper_phone_number ?? ''}
                        <br />
                        Fax:
                        {primaryContainer?.shipping_documents
                          ?.shipper_fax_number ?? ''}
                      </>
                    ),
                    rowSpan: 3,
                    colSpan: 4,
                  },
                  {
                    style: true,
                    label: 'BOOKING NUMBER',
                    value: bolNumber,
                    colSpan: 2,
                  },
                  {
                    style: true,
                    label: 'BILL OF LOADING NO.',
                    value: primaryContainer?.bill_of_loading_number,
                    colSpan: 4,
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    rowSpan: 1,
                    colSpan: 6,
                    label: (
                      <Box component="span" sx={{ fontSize: 11 }}>
                        VESSEL / VOYAGE# / STEAMSHIP LINE / FLAG
                      </Box>
                    ),
                    value: `${primaryContainer?.bookings?.vessels?.name ?? ''}/${primaryContainer?.voyage_number ?? ''}#/
                  ${primaryContainer?.bookings?.vessels?.steamshiplines?.name ?? ''}/${primaryContainer?.flag ?? ''}`,
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    colSpan: 2,
                    value: (
                      <>
                        COUNTRY OF ORIGIN
                        <br /> {primaryContainer?.place_receipt ?? ''}
                      </>
                    ),
                  },
                  {
                    colSpan: 4,
                    value: (
                      <>
                        PLACE OF RECEIPT
                        <br />
                        {primaryContainer?.country_origin ?? ''}
                      </>
                    ),
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    label: 'CONSIGNEE',
                    value: (
                      <>
                        {primaryContainer?.companies?.consignee ?? ''} <br />
                        {primaryContainer?.companies?.consignee_street ?? ''}
                        <br />
                        P. O. Box Number:
                        {primaryContainer?.companies?.consignee_box ?? ''}
                        <br />
                        {primaryContainer?.companies?.consignee_poc ?? ''},
                        {primaryContainer?.companies?.consignee_email ?? ''}
                        <br />
                        Phone:
                        {primaryContainer?.companies?.consignee_phone ?? ''}
                        <br />
                        {primaryContainer?.companies?.consignee_city ?? ''},
                        {primaryContainer?.companies?.consignee_zip_code ?? ''},
                        {primaryContainer?.companies?.consignee_country ?? ''}
                      </>
                    ),
                    rowSpan: 3,
                    colSpan: 3,
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    rowSpan: 1,
                    colSpan: 7,
                    label: (
                      <Box component="span" sx={{ fontSize: 11 }}>
                        CONTACT FOR CARGO RELEASE
                      </Box>
                    ),
                    value: (
                      <>
                        P G L C SHIPPING L.L.C
                        <br />
                        AL Qusais Industrial Area 4, AL Saoud
                        <br />
                        Office 804
                        <br />
                        UAE, Dubai
                        <br />
                        Phone: +971 045488069
                        <br />
                        Email: <EMAIL>
                      </>
                    ),
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    colSpan: 2,
                    label: (
                      <Box component="span" sx={{ fontSize: 11 }}>
                        ETD AT PORT OF LOADING
                      </Box>
                    ),
                    value: cDate(primaryContainer?.bookings?.vessels?.etd),
                  },
                  {
                    colSpan: 5,
                    label: (
                      <Box component="span" sx={{ fontSize: 11 }}>
                        ETA AT PORT OF DISCHARGE
                      </Box>
                    ),
                    value: cDate(primaryContainer?.bookings?.eta),
                  },
                ]}
              />

              <PrintRow
                cells={[
                  {
                    value: (
                      <>
                        NOTIFY PARTY
                        <br />
                        {primaryContainer?.companies?.notify_party ?? ''}
                        <br />
                        {primaryContainer?.companies?.notify_street ?? ''}
                        <br />
                        P. O. Box Number:{' '}
                        {primaryContainer?.companies?.notify_box ?? ''}
                        <br />
                        {primaryContainer?.companies?.notify_city ?? ''},
                        {primaryContainer?.companies?.notify_zip ?? ''},
                        {primaryContainer?.notify_country ?? ''}
                        <br />
                        {primaryContainer?.companies?.notify_poc ?? ''},
                        {primaryContainer?.companies?.notify_email ?? ''} -
                        Phone:$
                        {primaryContainer?.companies?.notify_phone ?? ''}
                      </>
                    ),
                    colSpan: 4,
                  },
                  {
                    style: true,
                    value: (
                      <>
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          VESSEL:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {primaryContainer?.bookings?.vessels?.name ?? ''}
                          </Typography>
                        </Typography>
                        <hr />
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          VOYAGE:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {primaryContainer?.voyage_number ?? ''}
                          </Typography>
                        </Typography>
                        <hr />
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          FLAG:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {primaryContainer?.flag ?? ''}
                          </Typography>
                        </Typography>
                        <hr />
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          PORT OF LOADING:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {primaryContainer?.bookings?.vessels?.locations
                              ?.name ?? ''}
                          </Typography>
                        </Typography>
                        <hr />
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          PORT OF DISCHARGE:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {primaryContainer?.bookings?.destinations?.name ??
                              ''}
                          </Typography>
                        </Typography>
                        <hr />
                        <Typography
                          sx={{ ...printStyles.p, display: 'inline' }}
                        >
                          Date:{' '}
                          <Typography
                            component="span"
                            sx={{ color: 'black', fontSize: '11px' }}
                          >
                            {new Date().toLocaleDateString()}
                          </Typography>
                        </Typography>
                      </>
                    ),
                    colSpan: 6,
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    colSpan: 10,
                    rowSpan: 1,
                    label: 'PARTICULARS FURNISHED BY SHIPPER',
                    center: true,
                  },
                ]}
              />

              <PrintRow
                cells={[
                  { colSpan: 2, label: 'MARKS & NUMBERS' },
                  {
                    colSpan: 5,
                    center: true,
                    label: `Description of Packages and Goods`,
                  },
                  { colSpan: 3, center: true, label: `Gross Weight KGs` },
                ]}
              />

              {containers.map((container, containerIndex) => {
                let containerWeight = 0;
                console.log('this is container List');
                console.log(container);
                const sumContainerWeight = (d) => {
                  containerWeight += +d;
                };
                return (
                  <>
                    <PrintRow
                      key={`container-${containerIndex}`}
                      cells={[
                        {
                          colSpan: 2,
                          rowSpan: 3,
                          value: (
                            <>
                              CONTAINER NO.: <br />
                              {container?.container_number ?? ''}
                              <br />
                              Container Size/Type:
                              <br />
                              {container?.bookings?.size ?? ''}
                              <br />
                              SEAL Number:
                              <br />
                              {container?.seal_number ?? ''}
                              <br />
                              AES ITN Number:
                              {container?.aes_itn_number ?? ''}
                              <br />
                              SCAC Code:
                              <br />
                              {container?.bookings?.vessels?.scac_code ?? ''}
                              <br />
                            </>
                          ),
                        },
                      ]}
                    />

                    <PrintRow
                      key={`container-${containerIndex}`}
                      cells={[
                        {
                          colSpan: 5,
                          value: (
                            <>
                              <Typography
                                sx={{ fontSize: '11px' }}
                                align="center"
                              >
                                {container?.no_units_load
                                  ? `${container?.no_units_load} Listed below`
                                  : ''}
                              </Typography>
                              <br />
                              {container?.vehicles?.map((vehicle, index) => (
                                <Typography
                                  key={index}
                                  sx={{ fontSize: '11px' }}
                                >
                                  {`${index + 1} ) ${vehicle?.year ?? ''} ${vehicle?.make ?? ''} ${vehicle?.model ?? ''} ${vehicle?.color ?? ''} VIN# ${vehicle?.vin ?? ''}`}
                                  {index < container?.vehicles?.length - 1 && (
                                    <br />
                                  )}
                                </Typography>
                              ))}
                            </>
                          ),
                        },
                        {
                          colSpan: 3,
                          value: (
                            <>
                              {container?.vehicles?.map((vehicle, index) => (
                                <Typography
                                  key={index}
                                  style={{
                                    margin: '7px 0px',
                                    fontSize: '11px',
                                  }}
                                >
                                  {index + 1 + ' ) '}
                                  {vehicle?.weight
                                    ? sumContainerWeight(vehicle.weight)
                                    : null}
                                  {vehicle?.weight ?? ''}
                                </Typography>
                              ))}
                            </>
                          ),
                        },
                      ]}
                    />

                    <PrintRow
                      key={`container-${containerIndex}`}
                      cells={[
                        { colSpan: 5, value: 'TOTAL KGs' },
                        { center: true, value: containerWeight },
                      ]}
                    />
                  </>
                );
              })}

              <PrintRow
                cells={[
                  {
                    colSpan: 10,
                    rowSpan: 1,
                    label: 'Basic Instructions:',
                    center: true,
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    colSpan: 10,
                    rowSpan: 1,
                    value: (
                      <>
                        <Typography sx={{ ...printStyles.p, fontSize: '10px' }}>
                          IN ACCEPTING THIS BILL OF LADING
                        </Typography>
                        <Typography sx={{ fontSize: '10px', padding: '2px' }}>
                          The Shipper, Consignee, Holder hereof, and Owner of
                          the goods, agree to be bound by all of its
                          stipulations, exceptions, and conditions whether
                          written, printed, or stamped on the front or back
                          hereof, as well as the provisions of the above
                          Carrier's published Tariff Rules and Regulations, as
                          fully as if they were all signed by such Shipper,
                          Consignee, Holder, or Owner, and it is further agreed
                          that containers are stowed on Deck, as per Clause 6.
                          <br />
                          IN WITNESS WHEREOF, the Master of the said vessel has
                          affirmed this Bill of Lading and authorized signature.
                          <br />
                          <Box
                            component="h4"
                            sx={{
                              marginLeft: '30%',
                              marginTop: '5px',
                              fontSize: '10px',
                            }}
                          >
                            <b>By</b>: PEACE GLOBAL LOGISTICS LLC
                          </Box>
                          <Box
                            component="span"
                            sx={{
                              marginLeft: '30%',
                              fontSize: '10px',
                              fontWeight: 'bold',
                            }}
                          >
                            AS AGENT:
                          </Box>
                        </Typography>
                      </>
                    ),
                  },
                ]}
              />
              <PrintRow
                cells={[
                  {
                    colSpan: 10,
                    rowSpan: 1,
                    value: (
                      <Typography sx={{ fontSize: '10px', padding: '2px' }}>
                        Hereby certify having received the above described
                        shipment in outwardly good condition from the shipper
                        shown in section (Shipper/Exporter) for forwarding to
                        the ultimate consignee shown in the section (Consignee)
                        above in witness whereof, the ______________
                        nonnegotiable FCRs have been signed, and if one (1) is
                        accomplished by Peace Global Logistics as agent,
                        issuance of a delivery order or by some other means, the
                        others shall be voided if required by the freight
                        forwarder. One (1) original FCR must be surrendered.
                      </Typography>
                    ),
                  },
                ]}
              />
            </TableBody>
          </Table>
          <style>
            {`
            @media print {
              @page {
                size: A4; /* DIN A4 standard, Europe */
                margin: 0;
              }
              html, body {
                font-size: 11px;
              }
            }
        `}
          </style>
        </Box>
      </>
    );
  },
);
export default ReleaseDocumentView;
