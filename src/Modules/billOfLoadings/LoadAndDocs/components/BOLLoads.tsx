import {
  Box,
  Card,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  SelectChangeEvent,
  Tooltip,
  useTheme,
  LinearProgress,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import DataTable3 from '@/components/mainComponents/datatable/DataTables3';
import PageAction from '@/components/mainComponents/PageAction/PageAction';
import { bolLoadsHeader } from './Header';
import CloseIcon from '@mui/icons-material/Close';
import { useState, useRef, forwardRef } from 'react';
import CBasicTooltip from '@/components/mainComponents/datatable/cBasicTooltip';
import PublishedWithChangesIcon from '@mui/icons-material/PublishedWithChanges';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import VerifiedIcon from '@mui/icons-material/Verified';
import DescriptionIcon from '@mui/icons-material/Description';
import DocumentScannerIcon from '@mui/icons-material/DocumentScanner';
import AppTooltip from '@/components/mainComponents/cComponents/AppTooltip';
import PrintIcon from '@mui/icons-material/Print';
import { pdf } from '@react-pdf/renderer';
import { LoadAndDocsBOLStatus as Options } from './Header';
import { removeUnderScore } from '@/configs/common';
import { ReleaseDocument } from './release_document';
import ReleaseDocumentPrint from './release_document_view';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { useReactToPrint } from 'react-to-print';

// function to group containers
const groupContainers = (items, multiReleaseCompanyIds) => {
  const groupedByCompany = {};
  items.forEach((item) => {
    if (!groupedByCompany[item.company_id]) {
      groupedByCompany[item.company_id] = [];
    }
    groupedByCompany[item.company_id].push(item);
  });

  const groups = [];
  Object.keys(groupedByCompany).forEach((companyId) => {
    const containers = groupedByCompany[companyId];
    if (multiReleaseCompanyIds.includes(parseInt(companyId))) {
      for (let i = 0; i < containers.length; i += 3) {
        groups.push(containers.slice(i, i + 3));
      }
    } else {
      containers.forEach((container) => {
        groups.push([container]);
      });
    }
  });
  return groups;
};

export default function BOLLoads({ show, setShow, loads, apiUrl, activeTab }) {
  const theme = useTheme();
  const [selectedItems, setSelectedItems] = useState([]);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [status, setStatus] = useState();
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [currentDocIndex, setCurrentDocIndex] = useState(0);
  const [documentGroups, setDocumentGroups] = useState([]);
  const [options, setOptions] = useState({
    page: 1,
    perPage: 20,
    filterData: {},
    search: '',
    exactMatch: false,
    tab: activeTab,
    orderBy: { column: 'id', order: 'desc' },
  });
  const printRef = useRef<HTMLDivElement>(null);

  const multiReleaseCompanyIds = [
    158, // UNITED UNSTOPPABLE CAR AUCTION L L C
    577, // UNITED UNSTOPPABLE CAR AUCTION L L C OMAN
    806, // UNITED UNSTOPPABLE CAR AUCTION L L C Poti Georgia
    1078, // Galaxy Worldwide Shipping - Jebel Ali
    1173, // Galaxy Worldwide Shipping - Other destinations
  ];

  const PrintComponent = forwardRef<HTMLDivElement, any>((_props, ref) => (
    <ReleaseDocumentPrint data={documentGroups[currentDocIndex]} ref={ref} />
  ));

  const markAsIssue = async () => {
    try {
      setSubmitting(true);
      const data = await axios.patch(`${apiUrl}/markAsIssue`, {
        loadIds: JSON.stringify(selectedItems?.map((item) => item?.id)),
        bolId: selectedItems[0]?.bill_of_loading_id,
      });

      if (data.status === 200) {
        setSelectedItems([]);
        setOpenConfirm(false);
        toast.success('Marked as issue successfully.');
      } else {
        toast.warn('Update failed, please try again.');
      }
    } catch (error) {
      toast.error('Oops! Something went wrong.');
    } finally {
      setSubmitting(false);
    }
  };

  const markAsIssueFixed = async () => {
    const issueCount =
      loads?.containers?.filter((item) => item.is_issue_for_bol === true)
        ?.length || 0;

    try {
      setSubmitting(true);
      const data = await axios.patch(`${apiUrl}/markAsFixed`, {
        loadIds: JSON.stringify(selectedItems?.map((item) => item?.id)),
        ...(issueCount === selectedItems?.length
          ? { bolId: selectedItems[0]?.bill_of_loading_id, status }
          : {}),
      });

      if (data.status === 200) {
        setStatus(null);
        setSelectedItems([]);
        setOpenConfirm(false);
        toast.success('Marked as fixed successfully.');
      } else {
        toast.warn('Update failed, please try again.');
      }
    } catch (error) {
      toast.error('Oops! Something went wrong.');
    } finally {
      setStatus(null);
      setSubmitting(false);
    }
  };

  const ConfirmationDialog = ({ setOpenConfirm, openConfirm, submitting }) => {
    return (
      <AppConfirmDialog
        maxWidth={'sm'}
        open={openConfirm}
        onDeny={() => setOpenConfirm(false)}
        onConfirm={markAsIssue}
        title="Are you sure you want to mark this as issue?"
        dialogTitle={'Attention'}
        confirmText={'Yes, Mark'}
        cancelText="Cancel"
        submitting={submitting}
      />
    );
  };

  const confirmDialogStatusBOL = () => {
    return (
      <FormControl fullWidth size="small">
        <InputLabel id="demo-select-small-label">Status</InputLabel>
        <Select
          labelId="demo-select-small-label"
          id="demo-select-small"
          value={status || ''}
          label="Status"
          onChange={(event) => setStatusFunction(event, setStatus)}
        >
          {Options.filter((item) => item !== selectedItems[0]?.status).map(
            (item, index) => (
              <MenuItem key={index} value={item}>
                {removeUnderScore(item)}
              </MenuItem>
            ),
          )}
        </Select>
      </FormControl>
    );
  };

  const setStatusFunction = (event: SelectChangeEvent, setStatus) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      setStatus(newValue);
    } else {
      setStatus('');
    }
  };

  const MarkFixedIssue = ({ setOpenConfirm, openConfirm, submitting }) => {
    return (
      <AppConfirmDialog
        maxWidth={'sm'}
        open={openConfirm}
        onDeny={() => setOpenConfirm(false)}
        onConfirm={markAsIssueFixed}
        title={confirmDialogStatusBOL()}
        dialogTitle={'Attention'}
        confirmText={'Yes, Mark'}
        cancelText="Cancel"
        submitting={submitting}
      />
    );
  };

  const handleView = () => {
    if (selectedItems.length === 0) {
      toast.warn('Please select at least one container to view.');
      return;
    }
    const groups = groupContainers(selectedItems, multiReleaseCompanyIds);
    setDocumentGroups(groups);
    setCurrentDocIndex(0);
    setViewDialogOpen(true);
  };

  const handleNext = () => {
    if (currentDocIndex < documentGroups.length - 1) {
      setCurrentDocIndex(currentDocIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentDocIndex > 0) {
      setCurrentDocIndex(currentDocIndex - 1);
    }
  };

  const handlePrintDocument = useReactToPrint({ contentRef: printRef });

  const generateContainerPDF = async (container) => {
    const pdfDoc = pdf(<ReleaseDocument container={container} />);
    return await pdfDoc.toBlob();
  };

  const handleGeneratePDFs = async (requireSelection = false) => {
    if (requireSelection && selectedItems.length === 0) {
      toast.warn('Please select at least one container to generate PDFs.');
      return;
    }
    setIsGenerating(true);
    setProgress(0);
    const zip = new JSZip();

    const containersToProcess = requireSelection
      ? selectedItems
      : selectedItems.length > 0
        ? selectedItems
        : loads.containers || [];
    const groups = groupContainers(containersToProcess, multiReleaseCompanyIds);

    const total = groups.length;
    const batchSize = 10;
    for (let i = 0; i < total; i += batchSize) {
      const batch = groups.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (group, index) => {
          const pdfDoc = await generateContainerPDF(group);
          const fileName =
            group.length > 1
              ? `release_document_${group.map((c) => c.container_number).join('_')}.pdf`
              : `release_document_${group[0].container_number || `container_${i + index + 1}`}.pdf`;
          zip.file(fileName, pdfDoc);
        }),
      );
      setProgress(((i + Math.min(batchSize, total - i)) / total) * 100);
    }

    try {
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      saveAs(zipBlob, `release_documents_${loads.vessels?.name || 'bol'}.zip`);
    } catch (error) {
      console.error('Error generating ZIP:', error);
      toast.error('Failed to generate ZIP file.');
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handlePrint = () => {
    handleGeneratePDFs(false);
  };

  const handleCloseAndCleanup = () => {
    setViewDialogOpen(false);
    setCurrentDocIndex(0);
    setSelectedItems([]);
    setIsGenerating(false);
    setProgress(0);
    setDocumentGroups([]);
    setShow(false);
  };

  const hasBolIssue = selectedItems.some(
    (item) => item.is_issue_for_bol === true,
  );

  const allBolIssue =
    selectedItems.length > 0 &&
    selectedItems.every((item) => item.is_issue_for_bol === true);

  return (
    <Modal
      onClose={handleCloseAndCleanup}
      aria-labelledby="customized-dialog-title"
      open={show}
    >
      <Card
        style={{
          height: '75vh',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          letterSpacing: '1px',
          backgroundColor: theme.colors.alpha.white[100],
          border: `2px solid ${theme.colors.alpha.black[50]}`,
          boxShadow: '24',
          borderRadius: '10px',
          width: '70%',
        }}
      >
        <Box sx={{ width: '100%', px: 2, pt: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <h3>{loads?.vessels?.name}, Loads</h3>
            <Box sx={{ display: 'flex' }}>
              <IconButton
                sx={{ mr: 1 }}
                onClick={handleCloseAndCleanup}
                aria-label="delete"
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
          <Box sx={{ height: '64vh', overflowY: 'auto' }}>
            <DataTable3
              PageAction={
                <PageAction
                  showAddButton={false}
                  showDeleteButton={false}
                  showEditButton={false}
                  showDownload={false}
                  showSearch={false}
                  hideFilter={true}
                  showCustomizeColumn={false}
                  title={'Loads'}
                  setOptions={() => {}}
                  options={[]}
                  total={loads?.containers?.length}
                  selectedItems={selectedItems}
                  customActionButtons={() => (
                    <>
                      {isGenerating && (
                        <Box sx={{ textAlign: 'center', py: 1 }}>
                          Generating ZIP file, please wait...
                          <LinearProgress
                            variant="determinate"
                            value={progress}
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      )}
                      {!isGenerating && selectedItems.length > 0 && (
                        <Box sx={{ display: 'flex' }}>
                          <AppTooltip
                            key="release_document_view"
                            title="Release Document View"
                          >
                            <IconButton
                              sx={{ mr: 1, color: '#07bc0c' }}
                              onClick={handleView}
                              aria-label="generate release documents"
                            >
                              <DocumentScannerIcon />
                            </IconButton>
                          </AppTooltip>
                          <AppTooltip
                            key="release_document_print"
                            title="Release Document Print"
                          >
                            <IconButton
                              sx={{ mr: 1, color: '#3498db' }}
                              onClick={handlePrint}
                              aria-label="print release documents"
                            >
                              <PrintIcon />
                            </IconButton>
                          </AppTooltip>
                        </Box>
                      )}
                      {activeTab !== 'pending_issue' &&
                        !hasBolIssue &&
                        selectedItems?.length > 0 && (
                          <CBasicTooltip
                            title={'Mark As Issue!'}
                            onClick={() => setOpenConfirm(true)}
                            icon={<VerifiedIcon />}
                          />
                        )}
                      {allBolIssue && (
                        <CBasicTooltip
                          title={'Mark As Fixed!'}
                          onClick={() => setOpenConfirm(true)}
                          icon={<PublishedWithChangesIcon />}
                        />
                      )}
                      {selectedItems?.length > 0 && (
                        <CBasicTooltip
                          title={'Generate BOL'}
                          onClick={() => handleGeneratePDFs(true)}
                          icon={<DescriptionIcon />}
                        />
                      )}
                    </>
                  )}
                />
              }
              items={loads?.containers}
              headers={bolLoadsHeader}
              tableName="bol_loads"
              hidePagination={true}
              hideSelectAll={false}
              hideCheckBox={false}
              setOptions={setOptions}
              options={options}
              height="auto"
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
              columnsForMergingRows={['companies.name']}
              name={({ companies }) => companies?.name}
              container_number={(item) => {
                const style = item?.is_issue_for_bol
                  ? {
                      backgroundColor: '#e65100',
                      p: 1,
                      borderRadius: '10px',
                      color: '#fff',
                    }
                  : {};
                return (
                  <Tooltip
                    title={
                      item?.is_issue_for_bol
                        ? 'Marked As Issue!'
                        : item?.container_number
                    }
                    key={item?.id}
                  >
                    <Box sx={style}>{item?.container_number}</Box>
                  </Tooltip>
                );
              }}
            />
            <ConfirmationDialog
              setOpenConfirm={setOpenConfirm}
              openConfirm={openConfirm}
              submitting={submitting}
            />
            <MarkFixedIssue
              setOpenConfirm={setOpenConfirm}
              openConfirm={openConfirm}
              submitting={submitting}
            />
          </Box>
        </Box>
        <Dialog
          open={viewDialogOpen}
          onClose={handleCloseAndCleanup}
          fullScreen
          sx={{
            '& .MuiDialog-paper': {
              backgroundColor: theme.colors.alpha.black[70],
            },
          }}
        >
          <DialogActions>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
                px: 2,
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  bottom: '5%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  boxShadow: '0px 3px 6px #707070',
                  backgroundColor: '#707070cc',
                  padding: '1% 7%',
                }}
              >
                <Button
                  onClick={handlePrevious}
                  disabled={currentDocIndex === 0}
                  variant="contained"
                  sx={{
                    mr: 1,
                    backgroundColor: '#3f50b5',
                    color: 'white',
                    '&:hover': { backgroundColor: '#2a3f8c' },
                    '&.Mui-disabled': {
                      backgroundColor: '#808080 !important',
                      opacity: 0.8,
                      color: 'white !important',
                    },
                  }}
                >
                  Previous
                </Button>
                <Button
                  onClick={handleNext}
                  disabled={currentDocIndex === documentGroups.length - 1}
                  variant="contained"
                  sx={{
                    mr: 1,
                    backgroundColor: '#3f50b5',
                    color: 'white',
                    '&:hover': { backgroundColor: '#2a3f8c' },
                    '&.Mui-disabled': {
                      backgroundColor: '#808080 !important',
                      opacity: 0.8,
                      color: 'white !important',
                    },
                  }}
                >
                  Next
                </Button>
                <Button
                  onClick={handlePrintDocument}
                  variant="contained"
                  startIcon={<PrintIcon />}
                >
                  Print
                </Button>
                <Box sx={{ ml: 2, color: 'white', display: 'inline' }}>
                  Document {currentDocIndex + 1} of {documentGroups.length}
                </Box>
              </Box>
              <IconButton
                onClick={handleCloseAndCleanup}
                aria-label="close"
                sx={{
                  mr: 1,
                  position: 'absolute',
                  right: '2%',
                  color: 'white',
                  '&:hover': { backgroundColor: '#2a3f8c' },
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogActions>
          <DialogContent
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              overflowY: 'auto',
            }}
          >
            <PrintComponent ref={printRef} />
          </DialogContent>
        </Dialog>
      </Card>
    </Modal>
  );
}
