import { removeUnderScore2 } from '@/configs/common';
import Chip from '@mui/material/Chip';
import { LoadAndDocsBOLStatusColor } from './Header';
import {
  Box,
  IconButton,
  Link,
  Tooltip,
  CircularProgress,
  Button,
} from '@mui/material';
import LinkIcon from '@mui/icons-material/Link';
import DocumentScannerIcon from '@mui/icons-material/DocumentScanner';
import { useState, useCallback } from 'react';
import axios from '@/lib/axios';
import JSZip from 'jszip';
import { formatDate } from '@/configs/vehicles/configs';

const multiReleaseCompanyIds = [
  158, // UNITED UNSTOPPABLE CAR AUCTION L L C
  577, // UNITED UNSTOPPABLE CAR AUCTION L L C OMAN
  806, // UNITED UNSTOPPABLE CAR AUCTION L L C Poti Georgia
  1078, // Galaxy Worldwide Shipping - Jebel Ali
  1173, // Galaxy Worldwide Shipping - Other destinations
];

export function ReleaseDocButton({ id }) {
  const [isLoading, setIsLoading] = useState(false);

  const fetchReleaseDocuments = useCallback(async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);

      const containerListResponse = await axios.get(
        `bill-of-loadings/get-containers-list/${id}`,
      );

      if (
        containerListResponse?.status !== 200 ||
        !containerListResponse?.data
      ) {
        throw new Error('Failed to fetch container list');
      }

      const containers = containerListResponse.data;
      if (!containers || containers.length === 0) {
        throw new Error('No containers found for this Bill of Lading');
      }

      const eligibleContainerIds = [];
      const nonEligibleContainerIds = [];

      containers.forEach((container) => {
        const { company_id, container_id } = container;
        if (multiReleaseCompanyIds.includes(company_id)) {
          eligibleContainerIds.push(container_id);
        } else {
          nonEligibleContainerIds.push(container_id);
        }
      });

      const allDocuments = [];

      if (eligibleContainerIds.length > 0) {
        const idsString = eligibleContainerIds.join(',');

        const res = await axios.get(
          `containers/release-base-company/${idsString}`,
        );
        if (res?.status === 200 && Array.isArray(res.data)) {
          allDocuments.push(...res.data);
        } else {
          throw new Error(
            'Invalid response from release documents API (multi-release)',
          );
        }
      }

      if (nonEligibleContainerIds.length > 0) {
        const idsString = nonEligibleContainerIds.join(',');
        const res = await axios.get(`containers/release-document/${idsString}`);
        if (res?.status === 200 && Array.isArray(res.data)) {
          allDocuments.push(...res.data);
        } else {
          throw new Error(
            'Invalid response from release documents API (single-release)',
          );
        }
      }

      if (allDocuments.length > 0) {
        const zip = new JSZip();
        allDocuments.forEach((fileData) => {
          if (fileData?.buffer?.data && fileData?.name) {
            const uint8Array = new Uint8Array(fileData.buffer.data);
            zip.file(fileData.name, uint8Array);
          }
        });

        const blob = await zip.generateAsync({ type: 'blob' });
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = blobUrl;
        a.download = `Release_Document_BOL_${id}_${formatDate(new Date())}.zip`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(blobUrl);
        document.body.removeChild(a);
      } else {
        throw new Error('No documents available to download');
      }
    } catch (error) {
      console.error('Error fetching or processing release documents:', error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    }
  }, [id, isLoading]);

  return (
    <Box>
      <Tooltip title={isLoading ? 'Downloading...' : 'Download documents'}>
        <span>
          <Button
            loading={isLoading}
            disabled={isLoading}
            onClick={fetchReleaseDocuments}
            sx={{
              minWidth: 'auto',
              padding: '4px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {isLoading ? (
              <CircularProgress
                size={16}
                sx={{
                  color: '#1976d2',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '100%',
                }}
              />
            ) : (
              <DocumentScannerIcon sx={{ display: 'block' }} />
            )}
          </Button>
        </span>
      </Tooltip>
    </Box>
  );
}

export const LoadAndDocsBOLCustomColumns = ({ handleShowLoads }) => {
  return {
    name: ({ vessels }) => (
      <Box className="flex items-center justify-center">{vessels?.name}</Box>
    ),
    shipline_bl_link: ({ shipline_bl_link }) => (
      <Box className="flex items-center justify-center">
        {shipline_bl_link ? (
          <IconButton size="small">
            <Link href={shipline_bl_link} target="_blank">
              <LinkIcon fontSize="medium" color="success" />
            </Link>
          </IconButton>
        ) : (
          <LinkIcon fontSize="medium" color="error" />
        )}
      </Box>
    ),

    master_bk: (item) => (
      <Box
        className="flex cursor-pointer items-center justify-center"
        sx={{ color: 'rgb(131 146 235)' }}
        onClick={() => handleShowLoads(item?.id)}
        title={'View Loads'}
      >
        {item?.master_bk}
      </Box>
    ),

    status: ({ status }) => (
      <Chip
        size="small"
        sx={{
          fontSize: '11px',
          backgroundColor: LoadAndDocsBOLStatusColor(status),
          color: 'white',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        label={removeUnderScore2(status)}
      />
    ),
    release_doc: ({ id }) => <ReleaseDocButton id={id} />,

    freight_amount: (item) => {
      const billOfLoadingCharges = item?.billOfLoadingCharges;
      if (billOfLoadingCharges?.length > 0) {
        const originCharges = billOfLoadingCharges?.filter(
          (charge) => charge.freight_rate_category === 'origin',
        );

        const totalOrigin = originCharges?.reduce(
          (sum, charge) => sum + Number(charge.rate_value || 0),
          0,
        );
        return (
          <Tooltip
            title={
              <div style={{ color: '#fff' }}>
                {originCharges?.map((obj, index) => (
                  <div key={index}>
                    <strong>{obj?.rate_name || 'Unknown Key'}:</strong>
                    {obj.rate_value}
                  </div>
                ))}
              </div>
            }
          >
            <Box>{totalOrigin}</Box>
          </Tooltip>
        );
      } else {
        return <>{item?.freight_amount}</>;
      }
    },

    freight_AED: (item) => {
      const billOfLoadingCharges = item?.billOfLoadingCharges;
      if (billOfLoadingCharges?.length > 0) {
        const destinationCharges = billOfLoadingCharges?.filter(
          (charge) => charge.freight_rate_category === 'destination',
        );

        const totalDestination = destinationCharges?.reduce(
          (sum, charge) => sum + Number(charge.rate_value || 0),
          0,
        );

        return totalDestination > 0 ? (
          <Tooltip
            title={
              <div style={{ color: '#fff' }}>
                {destinationCharges?.map((obj, index) => (
                  <div key={index}>
                    <strong>{obj?.rate_name || 'Unknown Key'}:</strong>
                    {obj.rate_value}
                  </div>
                ))}
              </div>
            }
          >
            <Box>{totalDestination}</Box>
          </Tooltip>
        ) : (
          <Box>{totalDestination}</Box>
        );
      } else {
        return <>{item?.freight_AED}</>;
      }
    },

    total_freight_amount: (item) => {
      const billOfLoadingCharges = item?.billOfLoadingCharges;
      const qty = Number(item?.qty);
      if (billOfLoadingCharges?.length > 0) {
        const totalOrigin = billOfLoadingCharges
          ?.filter((charge) => charge.freight_rate_category === 'origin')
          ?.reduce((sum, charge) => sum + Number(charge.rate_value || 0), 0);
        return <>{totalOrigin * qty}</>;
      } else {
        return <>{item?.freight_amount * qty || ''}</>;
      }
    },

    total_freight_aed: (item) => {
      const billOfLoadingCharges = item?.billOfLoadingCharges;
      const qty = Number(item?.qty);
      if (billOfLoadingCharges?.length > 0) {
        const totalDestination = billOfLoadingCharges
          ?.filter((charge) => charge.freight_rate_category === 'destination')
          ?.reduce((sum, charge) => sum + Number(charge.rate_value || 0), 0);

        return <>{totalDestination * qty}</>;
      } else {
        return <>{item?.freight_AED * qty || ''}</>;
      }
    },

    updated_by: ({ updatedByUser }) => (
      <>
        {updatedByUser?.fullname}
        {updatedByUser?.departments?.name &&
          ' | ' + updatedByUser?.departments?.name}
      </>
    ),
    created_by: ({ createdByUser }) => (
      <>
        {createdByUser?.fullname}
        {createdByUser?.departments?.name &&
          ' | ' + createdByUser?.departments?.name}
      </>
    ),
    checked_by: ({ checkedByUser }) => (
      <>
        {checkedByUser?.fullname}
        {checkedByUser?.departments?.name &&
          ' | ' + checkedByUser?.departments?.name}
      </>
    ),
    payment_date: (row) => (
      <Box>{formatDate(row?.payment_date, 'YYYY-MM-DD HH:MM:SS')}</Box>
    ),
    created_at: (row) => (
      <Box>{formatDate(row?.created_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
    ),
    deleted_at: (row) => (
      <Box>{formatDate(row?.deleted_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
    ),
    updated_at: (row) => (
      <Box>{formatDate(row?.updated_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
    ),
    checked_at: (row) => (
      <Box>{formatDate(row?.checked_at, 'YYYY-MM-DD HH:MM:SS')}</Box>
    ),
  };
};
