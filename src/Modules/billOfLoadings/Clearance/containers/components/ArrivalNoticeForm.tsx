import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Divider,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import { useEffect, useState } from 'react';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import moment from 'moment';
import Grid from '@mui/material/Grid';

function ArrivalNoticeForm({
  containers,
  openConfirm,
  setOpenConfirm,
  refetchData,
}) {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [donwloadLoading, setDownloadLoading] = useState(false);

  const groupedContainers = containers.reduce((acc, container) => {
    const companyId = container.companies.id;
    console.log('this group companyId');
    console.log(companyId);
    if (!acc[companyId]) {
      acc[companyId] = { containers: [], company: container.companies };
    }
    acc[companyId].containers.push(container);
    return acc;
  }, {});
  const sendInventory = async (values) => {
    try {
      setSubmitLoading(true);

      const areAllCompaniesSame = containers.every(
        (item) => item?.companies?.id === containers[0]?.companies?.id,
      );

      if (!areAllCompaniesSame) {
        toast.error('All containers must belong to the same company!');
        setSubmitLoading(false);
        return;
      }

      const containerIds = containers.map((item) => item.id);

      let { data } = await axios.post('containers/arrival-notices', {
        ...values,
        free_days: containers.map((item) => {
          return { id: item.id, value: item?.bookings.free_days };
        }),
        container_ids: containerIds,
        company_id: containers[0]?.companies?.id,
      });
      if (data?.result) toast.success('Arrival Notice Sent successfully!');
      else {
        toast.error(data?.message);
      }
      setSubmitLoading(false);
      setOpenConfirm(false);
      refetchData();
    } catch (error) {
      setSubmitLoading(false);
    }
  };

  const downloadPdf = async () => {
    try {
      setDownloadLoading(true);
      const values = form.getValues();
      const containerIds = containers.map((item) => item.id);
      const response = await axios.post(
        `containers/arrival-notices/get-pdf`,
        {
          ...values,
          free_days: containers.map((item) => {
            return { id: item.id, value: item?.bookings.free_days };
          }),
          container_ids: containerIds,
          company_id: containers.map((item) => item.companies.id),
        },
        { responseType: 'blob' },
      );
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = `Arrival Notice ${moment(new Date()).format(
        'YYYY-MM-DD',
      )}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      setDownloadLoading(false);
    } catch (err) {
      setDownloadLoading(false);
    }
  };

  const schema = z.object({
    subject: z.string({ required_error: 'Subject is required' }),
    emails: z.array(z.string().email()).nullable(),
    cc: z.array(z.string().email()).nullable(),
    description: z
      .string({ required_error: 'Description is required' })
      .nullable(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: '',
      description: `<p>In the meantime, For release issues you may keep contact with this group to get it soon to avoid any delay "the payment must be cleared" In addition, once you get the release from our team for clearance procedure you can email to <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><strong><EMAIL></strong></a> to proceed with further clearance.</p><p>Note: Also 7 days free assigned from the shipping line for your container (s) that are attached in the below screenshot</p>`,
      cc: [],
      emails: [],
    },
  });

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: 1200,
    minWidth: '60%',
    bgcolor: 'background.paper',
  };

  useEffect(() => {
    form.setValue(
      'subject',
      `Arrival Notice for CNT# ` +
        containers.map((item) => item.container_number).join(', '),
    );
  }, [containers]);

  useEffect(() => {
    if (containers.length > 0) {
      console.log('this is useEffect and container');
      console.log(groupedContainers);
      const customer = containers[0]?.companies?.customers[0];
      const primaryEmail = customer?.loginable?.email;
      const secondaryEmail = customer?.secondary_email || '';
      const concatenatedEmails = [];
      concatenatedEmails.push(primaryEmail);
      if (secondaryEmail) {
        concatenatedEmails.push(secondaryEmail);
      }
      form.setValue('emails', concatenatedEmails);

      const hasCompany813 = Object.values(groupedContainers).some(
        (group: any) => group.company.id === 813,
      );

      if (hasCompany813) {
        form.setValue('cc', [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]);
      }
    } else {
      form.setValue('cc', []);
    }
  }, [containers, form, groupedContainers]);

  return (
    <Modal open={openConfirm}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Arrival Notice</Typography>
          <IconButton
            aria-label="close"
            sx={{ color: 'grey' }}
            onClick={() => setOpenConfirm(false)}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendInventory)}>
          <CardContent style={{ height: '55vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid size={12}>
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => (
                          <TextField
                            size="small"
                            error={error ? true : false}
                            id="subject"
                            value={field.value}
                            label="Subject"
                            fullWidth
                            variant="outlined"
                            onChange={(value) =>
                              field.onChange(value.target.value)
                            }
                            helperText={error?.message}
                          />
                        )}
                      />
                    </Grid>
                    <Grid size={12}>
                      <Controller
                        name="emails"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) =>
                                field.onChange(newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Emails"
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid size={12}>
                      <Controller
                        name="cc"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => {
                          return (
                            <Autocomplete
                              multiple
                              id="tags-filled"
                              options={[]}
                              value={field.value}
                              freeSolo
                              renderTags={(
                                value: readonly string[],
                                getTagProps,
                              ) =>
                                value.map((option: string, index: number) => (
                                  <Chip
                                    key={index}
                                    variant="outlined"
                                    label={option}
                                    {...getTagProps({ index })}
                                  />
                                ))
                              }
                              onChange={(_event: any, newValue) =>
                                field.onChange(newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  variant="outlined"
                                  label="Cc "
                                  placeholder="Type email and press enter..."
                                  size="small"
                                  error={invalid}
                                  helperText={error?.[0]?.message}
                                />
                              )}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid size={12}>
                      <Controller
                        name="description"
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => {
                                field.onChange(value);
                              }}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={() => setOpenConfirm(false)}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              color="info"
              onClick={downloadPdf}
              loading={donwloadLoading}
            >
              Download
            </Button>
            <Button
              size="small"
              variant="contained"
              type="submit"
              loading={submitLoading}
              onClick={() => {}}
            >
              Send
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}

export default ArrivalNoticeForm;
