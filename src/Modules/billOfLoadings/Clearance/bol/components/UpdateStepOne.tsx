import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  Grid,
  Link,
  List,
  ListItem,
  styled,
  TextField,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { toast } from 'react-toastify';
import DeleteIcon from '@mui/icons-material/Delete';

const UpdateStepOne = ({
  form,
  attachment,
  setAttachment,
  selectedItems,
  setDeletedAttachment,
  setSelectedItems,
  isChecked,
  setIsChecked,
}) => {
  const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });

  const handleUploadedFile = (files: FileList) => {
    if (files.length === 0) return;

    const file = files[0];
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];

    if (!allowedTypes.includes(file.type)) {
      return toast.error('Only PDF, PNG, JPG, and JPEG files are allowed!');
    }

    setAttachment(file);
    form.setValue('attachment', file);
  };

  const handleRemoveAttachment = () => {
    setAttachment(null);
    form.setValue('attachment', null);
  };

  const handleDeleteAttachment = () => {
    if (selectedItems[0]?.attachment) {
      setDeletedAttachment(selectedItems[0].attachment);
      setAttachment(null);
      form.setValue('attachment', null);
      setSelectedItems((prevItems) => {
        const updated = [...prevItems];
        updated[0] = { ...updated[0], attachment: null };
        return updated;
      });
    }
  };

  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">Bill Of Loadings Details</Typography>
      <Grid container mt={1} spacing={2}>
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="shipline_bl_number"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={Boolean(error?.message)}
                id="shipline_bl_number"
                value={field.value ?? ''}
                label="Shipline BL Number"
                fullWidth
                variant="outlined"
                onChange={(e) => field.onChange(e.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          sx={{ my: 1.5 }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name="shipline_bl_link"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={Boolean(error?.message)}
                id="shipline_bl_link"
                value={field.value ?? ''}
                label="Shipline BL Link"
                fullWidth
                variant="outlined"
                onChange={(e) => field.onChange(e.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
        <Grid
          textAlign="left"
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Button
            fullWidth
            component="label"
            variant="contained"
            startIcon={<CloudUploadIcon />}
            sx={{ marginBottom: '10px' }}
          >
            {'Select Attachment'}
            <VisuallyHiddenInput
              type="file"
              onChange={(e) => handleUploadedFile(e.target.files)}
            />
          </Button>
          {attachment && (
            <List
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                border: '1px solid gray',
                borderRadius: '5px',
                marginTop: '10px',
                padding: '8px 12px',
                width: 'full',
              }}
            >
              <Typography fontSize={12}>
                {attachment.name.substring(0, 60)}...
              </Typography>
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                gap={1}
              >
                <Link
                  sx={{
                    width: '20px',
                    height: '20px',
                    marginBottom: '4px',
                  }}
                  onClick={handleRemoveAttachment}
                >
                  <DeleteIcon color="error" />
                </Link>
              </Box>
            </List>
          )}
          {selectedItems[0]?.attachment && (
            <List>
              <ListItem
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  border: '1px solid gray',
                  borderRadius: '5px',
                  marginTop: '10px',
                  padding: '8px 12px',
                  width: 'full',
                }}
              >
                <Typography fontSize={12}>
                  {selectedItems[0].attachment.substring(0, 60)}...
                </Typography>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  gap={1}
                >
                  <Link
                    sx={{
                      width: '20px',
                      height: '20px',
                      marginBottom: '4px',
                    }}
                    onClick={handleDeleteAttachment}
                  >
                    <DeleteIcon color="error" />
                  </Link>
                </Box>
              </ListItem>
            </List>
          )}
        </Grid>
        <Grid
          textAlign="left"
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Typography>
            By reviewing this Bill of Lading, you acknowledge that it has been
            verified, and its status will be updated to "To Be Checked."!
          </Typography>
          <FormControlLabel
            sx={{
              flex: '1 0 48%',
            }}
            label="Confirm"
            control={
              <Checkbox
                size="small"
                name={isChecked}
                value={isChecked}
                checked={isChecked}
                onChange={(event) => setIsChecked(event.target.checked)}
              />
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <Controller
            name="remarks"
            control={form.control}
            render={({ field, fieldState: { error } }) => (
              <TextField
                size="small"
                error={Boolean(error?.message)}
                id="remarks"
                value={field.value ?? ''}
                label="Notes"
                fullWidth
                multiline
                rows={3}
                variant="outlined"
                onChange={(e) => field.onChange(e.target.value)}
                ref={field.ref}
                helperText={error?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default UpdateStepOne;
