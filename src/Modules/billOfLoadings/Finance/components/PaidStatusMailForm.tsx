import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Divider,
  IconButton,
  Modal,
  TextField,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import Grid from '@mui/material/Grid';
import QuillEditor from '@/components/mainComponents/MuiEditor';
import { useState } from 'react';
import axios from '@/lib/axios';
import { toast } from 'react-toastify';
import BOLAttachments from './BOLAttachments';

export default function PaidStatusMailForm({
  apiUrl,
  openConfirm,
  setOpenConfirm,
  selectedItems,
  setSelectedItems,
  setReLoads,
}) {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: 1200,
    minWidth: '60%',
    bgcolor: 'background.paper',
  };

  const schema = z.object({
    subject: z.string({ required_error: 'Subject is required' }),
    emails_to: z
      .array(z.string().email())
      .min(1, 'At least one email is required'),
    cc: z.array(z.string().email()).nullable(),
    description: z
      .string({ required_error: 'Description is required' })
      .nullable(),
  });

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(schema),
    defaultValues: {
      subject: 'Payment Confirmation',
      emails_to: [
        /* '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>', */
      ],
      cc: [
        /* '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>', */
      ],
      description: `
      <p>
        We have completed the payment for the subject booking(s)/Bill(s) of Loading. Please find attached the payment summary and proof of payment for your
        reference. Kindly note that all charges have been fully settled. We request your assistance in ensuring there are no issues at the POL/POD when obtaining the NOC/DO.
      </p>
      <p> </br> </p>
      <p>
        We would appreciate your prompt response.
      </p>`,
    },
  });

  const sendPaymentEmail = async (values) => {
    setSubmitLoading(true);
    try {
      const formData = new FormData();
      for (var i = 0; i < attachments.length; i++) {
        formData.append('files', attachments[i]);
      }

      formData.append('subject', values.subject);
      formData.append('description', values.description);
      formData.append('cc', JSON.stringify(values.cc));
      formData.append('emails_to', JSON.stringify(values.emails_to));
      formData.append(
        'bolIds',
        JSON.stringify(selectedItems.map((item) => item.id)),
      );

      let { data } = await axios.post(
        `${apiUrl}/paidStatusSendEmail`,
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } },
      );

      setSubmitLoading(false);
      if (data?.result) toast.success('Payment email sent successfully!');
      else {
        toast.error(data?.message);
      }
      setSelectedItems([]);
      setAttachments([]);
      setOpenConfirm(false);
      setReLoads(Math.random());
    } catch (error) {
      setSubmitLoading(false);
      toast.error(error);
      setAttachments([]);
      setSelectedItems([]);
      setOpenConfirm(false);
    }
  };

  return (
    <Modal open={openConfirm}>
      <Card sx={style}>
        <Box
          sx={{
            m: 0,
            p: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography variant="h5">Send Payment Email</Typography>
          <IconButton
            aria-label="close"
            sx={{ color: 'grey' }}
            onClick={() => setOpenConfirm(false)}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <form onSubmit={form.handleSubmit(sendPaymentEmail)}>
          <CardContent style={{ height: '75vh', overflowY: 'auto' }}>
            <Card elevation={0}>
              <CardContent sx={{ pt: 0 }}>
                <Box>
                  <Grid container spacing={2} sx={{ pt: 2, pb: 1 }}>
                    <Grid size={12}>
                      <Controller
                        name="subject"
                        control={form.control}
                        render={({ field, fieldState: { error } }) => (
                          <TextField
                            size="small"
                            error={error ? true : false}
                            id="subject"
                            value={field.value}
                            label="Subject"
                            fullWidth
                            variant="outlined"
                            onChange={(value) =>
                              field.onChange(value.target.value)
                            }
                            helperText={error?.message}
                          />
                        )}
                      />
                    </Grid>

                    <Grid size={12}>
                      <Controller
                        name="emails_to"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => (
                          <Autocomplete
                            multiple
                            id="tags-filled"
                            options={[]}
                            value={field.value}
                            freeSolo
                            renderTags={(
                              value: readonly string[],
                              getTagProps,
                            ) =>
                              value.map((option: string, index: number) => (
                                <Chip
                                  key={index}
                                  variant="outlined"
                                  label={option}
                                  {...getTagProps({ index })}
                                />
                              ))
                            }
                            onChange={(_event: any, newValue) =>
                              field.onChange(newValue)
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                variant="outlined"
                                label="Emails To"
                                placeholder="Type email and press enter..."
                                size="small"
                                error={invalid}
                                helperText={error?.[0]?.message}
                              />
                            )}
                          />
                        )}
                      />
                    </Grid>
                    <Grid size={12}>
                      <Controller
                        name="cc"
                        control={form.control}
                        render={({ field, fieldState: { invalid, error } }) => (
                          <Autocomplete
                            multiple
                            id="tags-filled"
                            options={[]}
                            value={field.value}
                            freeSolo
                            renderTags={(
                              value: readonly string[],
                              getTagProps,
                            ) =>
                              value.map((option: string, index: number) => (
                                <Chip
                                  key={index}
                                  variant="outlined"
                                  label={option}
                                  {...getTagProps({ index })}
                                />
                              ))
                            }
                            onChange={(_event: any, newValue) =>
                              field.onChange(newValue)
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                variant="outlined"
                                label="Cc "
                                placeholder="Type email and press enter..."
                                size="small"
                                error={invalid}
                                helperText={error?.[0]?.message}
                              />
                            )}
                          />
                        )}
                      />
                    </Grid>
                    <Grid size={12}>
                      <Controller
                        name="description"
                        control={form.control}
                        defaultValue=""
                        render={({ field }) => (
                          <>
                            <Typography sx={{ fontSize: '14px' }}>
                              Description
                            </Typography>
                            <QuillEditor
                              height={150}
                              onChange={(value) => field.onChange(value)}
                              value={field.value}
                            />
                          </>
                        )}
                      />
                    </Grid>
                    <Grid size={12}>
                      <BOLAttachments
                        attachments={attachments}
                        setAttachments={setAttachments}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={() => setOpenConfirm(false)}
            >
              Cancel
            </Button>

            <Button
              size="small"
              variant="contained"
              type="submit"
              loading={submitLoading}
              onClick={() => {}}
            >
              Send
            </Button>
          </CardActions>
        </form>
      </Card>
    </Modal>
  );
}
