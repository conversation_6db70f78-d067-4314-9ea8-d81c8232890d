import { Box, Typography, TextField } from '@mui/material';
import BOL<PERSON>hargesItem from './components/BOLChargesItem';
import Grid from '@mui/material/Grid';
import { Controller } from 'react-hook-form';

const AddPriceStepOne = ({ form }) => {
  return (
    <Box sx={{ py: 2, ml: 2, textAlign: 'center' }}>
      <Typography variant="h5">Bill Of Loading Charges</Typography>
      <Box component={'hr'} sx={{ my: 1 }}></Box>

      <Box
        sx={{
          py: 2,
          px: 2,
          pr: 6,
          mb: 1,
          border: '1px solid gray',
          borderRadius: '6px',
          position: 'relative',
          marginBottom: 2,
        }}
      >
        <Box>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid size={6}>
              <Controller
                name={`free_days`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="Free Days"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={6}>
              <Controller
                name={`noc_charge_AED`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="NOC Charge"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Typography sx={{ pb: 1, mt: 2 }}>Invoice Amounts</Typography>
          <Grid container spacing={2}>
            <Grid size={6}>
              <Controller
                name={`invoice_freight_AED`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="Origin"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid size={6}>
              <Controller
                name={`invoice_freight_amount`}
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={error ? true : false}
                    value={field.value ?? 0}
                    label="Destination"
                    fullWidth
                    type="number"
                    variant="outlined"
                    onChange={(value) =>
                      field.onChange(
                        value.target.value !== '' ? +value.target.value : '',
                      )
                    }
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
          <Box component={'hr'} sx={{ my: 1 }}></Box>
          <Typography sx={{ pb: 1 }}>40HC</Typography>
          <Grid container spacing={2}>
            <Grid size={6}>
              <Box
                sx={{
                  border: '1px solid #cccccc',
                  borderRadius: '6px',
                  paddingTop: '2px',
                  px: 1,
                  pb: 1,
                }}
              >
                <Typography sx={{ pb: 1 }}>Origin</Typography>
                <BOLChargesItem
                  control={form.control}
                  type={'origin_rates'}
                  rateType={'rate_40hc'}
                />
              </Box>
            </Grid>
            <Grid size={6}>
              <Box
                sx={{
                  border: '1px solid #cccccc',
                  borderRadius: '6px',
                  paddingTop: '2px',
                  px: 1,
                  pb: 1,
                }}
              >
                <Typography sx={{ pb: 1 }}>Destination</Typography>
                <BOLChargesItem
                  control={form.control}
                  type={'destination_rates'}
                  rateType={'rate_40hc'}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Box>
          <Typography sx={{ pb: 1 }}>45HC</Typography>
          <Grid container spacing={2}>
            <Grid size={6}>
              <Box
                sx={{
                  border: '1px solid #cccccc',
                  borderRadius: '6px',
                  paddingTop: '2px',
                  px: 1,
                  pb: 1,
                }}
              >
                <Typography sx={{ pb: 1 }}>Origin</Typography>
                <BOLChargesItem
                  control={form.control}
                  type={'origin_rates'}
                  rateType={'rate_45hc'}
                />
              </Box>
            </Grid>
            <Grid size={6}>
              <Box
                sx={{
                  border: '1px solid #cccccc',
                  borderRadius: '6px',
                  paddingTop: '2px',
                  px: 1,
                  pb: 1,
                }}
              >
                <Typography sx={{ pb: 1 }}>Destination</Typography>
                <BOLChargesItem
                  control={form.control}
                  type={'destination_rates'}
                  rateType={'rate_45hc'}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Box>
  );
};

export default AddPriceStepOne;
