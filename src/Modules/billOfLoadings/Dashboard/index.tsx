import { useContext, useState, useEffect } from 'react';
import { BILL_OF_LOADINGS } from '@/configs/leftSideMenu/Permissions';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { contextProvider } from '@/contexts/ProfileContext';
import Link from 'next/link';
import {
  Box,
  Container,
  Grid,
  Typography,
  Paper,
  CircularProgress,
} from '@mui/material';
import Head from 'next/head';
import PageHeader from '@/components/mainComponents/pageHeader/PageHeader';
import ModeStandbyIcon from '@mui/icons-material/ModeStandby';
import axios from '@/lib/axios';

const BillOfLoadingDashboard = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const summaryData = [
    {
      label: 'Vessels Departed (Last 10 Days)',
      urlFilter: `past_10_days`,
    },
    {
      label: 'Vessels Departed (11-17 Days Ago)',
      urlFilter: `past_17_days`,
    },
    {
      label: 'Vessels ETA (20-30 Days Ahead)',
      urlFilter: `upcoming_20_30_days`,
    },
  ];

  // useStates
  const [cancelToken, setCancelToken] = useState(null);
  const [loading, setLoading] = useState(false);
  const [analytics, setAnalytics] = useState([]);

  //get Data from Server
  const fetchRecords = async () => {
    try {
      if (cancelToken) {
        await cancelToken.abort();
      }
      const controller = new AbortController();
      setCancelToken(controller);
      setLoading(true);
      const { data } = await axios.get('bill-of-loadings/getDashboardSummary');

      setAnalytics(data);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  //getting data
  const currentDate = new Date();

  const tenDaysAgo = new Date(currentDate);
  tenDaysAgo.setDate(currentDate.getDate() - 10);

  ///useEffects
  useEffect(() => {
    fetchRecords();
  }, []);

  return perms && !perms?.includes(BILL_OF_LOADINGS.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized
    </Box>
  ) : (
    <>
      <Head>
        <title>Bill Of Loading Dashboard</title>
      </Head>
      <Container
        maxWidth={false}
        sx={{
          px: { xs: 2, sm: 3 },
          py: 4,
        }}
      >
        <PageHeader
          breadcrumbs={[
            {
              href: '/bill_of_loadings/dashboard',
              name: 'Dashboard',
              icon: <ModeStandbyIcon sx={{ fontSize: '1.125rem' }} />,
              key: '1',
            },
          ]}
        />
        <Typography
          variant="h5"
          component="h1"
          fontWeight={600}
          sx={{ pt: 3, pb: 4 }}
        >
          Bill of Loading Analytics
        </Typography>

        {loading ? (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height="200px"
          >
            <CircularProgress />
          </Box>
        ) : (
          <Paper
            elevation={3}
            sx={{
              width: { xs: '100%', sm: '350px' },
              p: 2,
              borderRadius: 2,
            }}
          >
            <Typography variant="h6" component="h2" fontWeight={500} mb={2}>
              Vessels Loading Summary
            </Typography>
            <Grid
              container
              spacing={2}
              sx={{
                p: 1,
              }}
            >
              {analytics?.map((item, index) => (
                <Grid key={index} size={12}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      {summaryData[index].label}
                    </Typography>
                    <Link
                      href={`/bill_of_loadings/vessels_loading/all?filterData=${summaryData[index].urlFilter}`}
                      style={{
                        paddingTop: 1,
                        color: '#60a87a',
                        fontSize: '36px',
                        fontWeight: 'bold',
                      }}
                    >
                      {item.vessel_count}
                    </Link>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        )}
      </Container>
    </>
  );
};

export default BillOfLoadingDashboard;
