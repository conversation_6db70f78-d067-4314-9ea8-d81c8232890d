import {
  Autocomplete,
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import {
  brandTypeOptions,
  companyOptions,
  equipmentTypeOptions,
  fuelTypeOptions,
  insuranceTypeOptions,
  operationStateOptions,
  pageTitle,
  selectedItem,
  yearOptions,
} from './options';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
export default function LoadingEquipmentsfieldStep1({ form }) {
  //@ts-ignore
  const [activeCollapes, setActiveCollapes] = useState(0);
  const [equipmentType, setEquipmentType] = useState(
    form.getValues('equipment_type') || '',
  );
  const theme = useTheme();

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Typography variant="h5" sx={{ pb: 3 }}>
        {pageTitle} Details
      </Typography>
      <Card
        sx={{
          mb: 1,
          backgroundColor: theme.palette.mode === 'dark' ? '' : '#f3f6f9',
        }}
      >
        <CardContent>
          <Grid container spacing={2} sx={{ paddingBottom: '10px' }}>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`company_name`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={selectedItem(companyOptions, field.value)}
                    getOptionLabel={(option) =>
                      option?.name ? option?.name : ''
                    }
                    onChange={(_event: any, newValue) => {
                      form.setValue(`company_name`, newValue.id);
                    }}
                    options={companyOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Compnay Name"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`equipment_type`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={selectedItem(equipmentTypeOptions, field.value)}
                    getOptionLabel={(option) =>
                      option?.name ? option?.name : ''
                    }
                    onChange={(_event: any, newValue) => {
                      form.setValue(`equipment_type`, newValue.id);
                      setEquipmentType(newValue.id);
                    }}
                    options={equipmentTypeOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Equipment Type"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="equipment_id"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.equipment_id?.message.length > 0
                        ? true
                        : false
                    }
                    id="equipment_id"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="Equipment ID"
                    fullWidth
                    multiline
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`operation_state`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={field.value}
                    getOptionLabel={(option) => (option ? option : '')}
                    onChange={(_event: any, newValue) => {
                      form.setValue(`operation_state`, newValue);
                    }}
                    options={operationStateOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Operation State"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`insurance`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={selectedItem(insuranceTypeOptions, field.value)}
                    getOptionLabel={(option) =>
                      option.name ? option.name : ''
                    }
                    onChange={(_event: any, newValue) => {
                      form.setValue(`insurance`, newValue.id);
                    }}
                    options={insuranceTypeOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Insurance"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="vin"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.vin?.message.length > 0 ? true : false}
                    id="vin"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="VIN"
                    fullWidth
                    multiline
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="plate_number"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.plate_number?.message.length > 0
                        ? true
                        : false
                    }
                    id="plate_number"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="Plate Number"
                    fullWidth
                    multiline
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>

            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="model"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={form.errors.model?.message.length > 0 ? true : false}
                    id="model"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="Model Name"
                    fullWidth
                    multiline
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>

            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name={`make_year`}
                control={form.control}
                render={({ field, fieldState: { invalid, error } }) => (
                  <Autocomplete
                    size="small"
                    value={field.value}
                    getOptionLabel={(option) => option || ''}
                    onChange={(_event: any, newValue) => {
                      form.setValue(`make_year`, newValue);
                    }}
                    options={yearOptions}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Make Year"
                        error={invalid}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                name="manufacturer_name"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    size="small"
                    error={
                      form.errors.manufacturer_name?.message.length > 0
                        ? true
                        : false
                    }
                    id="manufacturer_name"
                    autoComplete="on"
                    value={field.value ?? ''}
                    label="Manufacturer Name"
                    fullWidth
                    multiline
                    variant="outlined"
                    onChange={(value) => field.onChange(value.target.value)}
                    ref={field.ref}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid
              size={{
                xs: 12,
                md: 6,
              }}
            >
              <Controller
                control={form.control}
                name="reg_exp_date"
                render={({ field, fieldState: { error } }) => (
                  <DatePicker
                    views={['year', 'month', 'day']}
                    label="Reg Expiry Date"
                    value={!field.value ? null : dayjs(field.value).toDate()}
                    format="yyyy/MM/dd"
                    onChange={(e) => {
                      form.setValue('reg_exp_date', e.toISOString());
                    }}
                    inputRef={field.ref}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !!error,
                        helperText: error?.message,
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                  />
                )}
              />
            </Grid>
            {equipmentType === 'forklift' ? (
              <>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name="forklift_serial_number"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_serial_number?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_serial_number"
                        autoComplete="on"
                        value={field.value ?? ''}
                        label="Forklift Serial Number"
                        fullWidth
                        multiline
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name={`forklift_brand`}
                    control={form.control}
                    render={({ field, fieldState: { invalid, error } }) => (
                      <Autocomplete
                        size="small"
                        value={field.value}
                        getOptionLabel={(option) => (option ? option : '')}
                        onChange={(_event: any, newValue) => {
                          form.setValue(`forklift_brand`, newValue);
                        }}
                        options={brandTypeOptions}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Forklift Brand"
                            error={invalid}
                            helperText={error?.message}
                          />
                        )}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name="forklift_model"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_model?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_model"
                        autoComplete="on"
                        value={field.value ?? ''}
                        label="Forklift Model"
                        fullWidth
                        multiline
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name="forklift_capacity"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_capacity?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_capacity"
                        value={field.value ?? ''}
                        label="ForkLift Capacity"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(+value.target.value)
                        }
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name="forklift_stage"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_stage?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_stage"
                        value={field.value ?? ''}
                        label="ForkLift Stage"
                        fullWidth
                        type="number"
                        variant="outlined"
                        onChange={(value) =>
                          field.onChange(+value.target.value)
                        }
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name="forklift_fork_size"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_fork_size?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_fork_size"
                        autoComplete="on"
                        value={field.value ?? ''}
                        label="Forklift Fork Size"
                        fullWidth
                        multiline
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 6,
                  }}
                >
                  <Controller
                    name={`forklift_fuelType`}
                    control={form.control}
                    render={({ field, fieldState: { invalid, error } }) => (
                      <Autocomplete
                        size="small"
                        value={field.value}
                        getOptionLabel={(option) => (option ? option : '')}
                        onChange={(_event: any, newValue) => {
                          form.setValue(`forklift_fuelType`, newValue);
                        }}
                        options={fuelTypeOptions}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Forklift Fuel Type"
                            error={invalid}
                            helperText={error?.message}
                          />
                        )}
                      />
                    )}
                  />
                </Grid>
                <Grid
                  size={{
                    xs: 12,
                    md: 12,
                  }}
                >
                  <Controller
                    name="forklift_remark"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        size="small"
                        error={
                          form.errors.forklift_remark?.message.length > 0
                            ? true
                            : false
                        }
                        id="forklift_remark"
                        autoComplete="on"
                        value={field.value ?? ''}
                        label="Forklift Remark"
                        fullWidth
                        multiline
                        variant="outlined"
                        onChange={(value) => field.onChange(value.target.value)}
                        ref={field.ref}
                        helperText={error?.message}
                      />
                    )}
                  />
                </Grid>
              </>
            ) : null}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
}
