import React, { useContext } from 'react';
import { Grid } from '@mui/material';
import Head from 'next/head';
import { useRouter } from 'next/router';
import CardState from '@/components/mainComponents/CarState';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import { Box } from '@mui/system';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { contextProvider } from '@/contexts/ProfileContext';
import { DASHBOARD } from '@/configs/leftSideMenu/Permissions';
import VehicleSummaryDashboard from './vehicleSummaryDashboard';
import ShipmentSummaryDashboard from './shipmentSummaryDashboard';
import YardLocationSummaryDashboard from './yardLocationSummaryDashboard';
// import VesselSummaryDashboard from './vesselSummaryDashboard';
import { SidebarContext } from 'src/contexts/SidebarContext';

const DashboardTwo = () => {
  const router = useRouter();
  const useProfileContext = useContext(contextProvider);
  const { sidebarCounts } = useContext(SidebarContext);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  const handleClick = (href) => {
    router.push(href);
  };

  return perms && !perms?.includes(DASHBOARD?.VIEW) ? (
    <Box
      display={'flex'}
      justifyContent={'center'}
      alignItems={'center'}
      height="100vh"
      fontWeight="bold"
      fontSize="20px"
    >
      Unauthorized to view dashboard
    </Box>
  ) : (
    <>
      <Head>
        <title>Dashboard</title>
      </Head>

      <Box sx={{ mx: '2px' }}>
        <Grid container spacing={1} mt="0px">
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Vehicles',
                count: sidebarCounts?.dashboard_vehicles,
                color: '#9E49E6',
                icon: <DirectionsCarIcon />,
              }}
              onClick={() => {
                handleClick(`/vehicles/all`);
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Shipments',
                count: sidebarCounts?.dashboard_containers,
                color: '#ff9800',
                icon: <DirectionsBoatIcon />,
              }}
              onClick={() => {
                handleClick('/shipment/all');
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Invoices',
                count: sidebarCounts?.dashboard_invoices,
                color: '#4caf50',
                icon: <ReceiptIcon />,
              }}
              onClick={() => {
                handleClick('/invoices/all');
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Customers',
                count: sidebarCounts?.dashboard_customers,
                color: '#b23c17',
                icon: <PeopleAltIcon />,
              }}
              onClick={() => {
                handleClick('/general/customers');
              }}
            />
          </Grid>
        </Grid>

        <Grid container spacing={0.5} sx={{ height: '100%' }}>
          <Grid
            sx={{ height: '100%' }}
            size={{
              lg: 12,
              sm: 12,
            }}
          >
            <Grid container direction="column" spacing={0.5}>
              <Grid>
                <VehicleSummaryDashboard />
              </Grid>
              <Grid>
                <ShipmentSummaryDashboard />
              </Grid>
              <Grid>
                <YardLocationSummaryDashboard />
              </Grid>
            </Grid>
          </Grid>
          {/* <Grid item lg={5.5} sm={12} sx={{ height: '100%' }}>
            <VesselSummaryDashboard />
          </Grid> */}
        </Grid>
      </Box>
    </>
  );
};

export default DashboardTwo;
