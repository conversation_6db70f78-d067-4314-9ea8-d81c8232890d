import React, { useEffect } from 'react';
import { Container, Grid } from '@mui/material';
import Head from 'next/head';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import CardState from '@/components/mainComponents/CarState';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DirectionsBoatIcon from '@mui/icons-material/DirectionsBoat';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const Dashboard = () => {
  useEffect(() => {}, []);

  const options = {
    chart: {
      id: 'basic-bar',
    },
    xaxis: {
      categories: [1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998],
    },
  };
  const series = [
    {
      name: 'series-1',
      data: [30, 40, 45, 50, 49, 60, 70, 91],
    },
  ];

  const bar = {
    options: {},
    series: [44, 55, 41, 17, 15],
    labels: ['A', 'B', 'C', 'D', 'E'],
  };

  const rangeSeries = [
    {
      data: [
        {
          x: 'TEAM A',
          y: [65, 96],
        },
        {
          x: 'TEAM B',
          y: [55, 78],
        },

        {
          x: 'TEAM C',
          y: [95, 186],
        },
      ],
    },
  ];
  const router = useRouter();

  const handleClick = (href) => {
    router.push(href);
  };

  return (
    <>
      <Head>
        <title>Dashboard</title>
      </Head>
      <Container
        sx={{ maxWidth: '1400px !important', padding: '0 10px !important' }}
      >
        <Grid container spacing={1} sx={{ my: 3 }}>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Vehicles',
                count: 94492,
                color: '#9E49E6',
                icon: <DirectionsCarIcon sx={{ height: 40, width: 40 }} />,
              }}
              onClick={() => {
                handleClick(`/vehicles/all`);
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Shipments',
                count: 21985,
                color: '#ff9800',
                icon: <DirectionsBoatIcon sx={{ height: 40, width: 40 }} />,
              }}
              onClick={() => {
                handleClick('/shipment/all');
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Invoices',
                count: 21519,
                color: '#4caf50',
                icon: <ReceiptIcon sx={{ height: 40, width: 40 }} />,
              }}
              onClick={() => {
                handleClick('/invoices/all');
              }}
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 3,
            }}
          >
            <CardState
              item={{
                title: 'Customers',
                count: 378,
                color: '#b23c17',
                icon: <PeopleAltIcon sx={{ height: 40, width: 40 }} />,
              }}
              onClick={() => {
                handleClick('/general/customers');
              }}
            />
          </Grid>
        </Grid>

        <Grid container>
          <Grid size={12}>
            <ApexChart
              options={options}
              series={series}
              type="area"
              height="350"
            />
          </Grid>
        </Grid>
        <Grid container sx={{ mt: 2 }}>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <ApexChart
              options={options}
              series={series}
              type="bar"
              height="350"
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <ApexChart
              options={bar.options}
              series={bar.series}
              type="donut"
              height="350"
            />
          </Grid>
        </Grid>
        <Grid container sx={{ mt: 2 }}>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <ApexChart
              options={options}
              series={rangeSeries}
              type="rangeBar"
              height="350"
            />
          </Grid>
          <Grid
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <ApexChart
              options={options}
              series={series}
              type="line"
              height="350"
            />
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

export default Dashboard;
