import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Controller } from 'react-hook-form';
import { allocationTypes } from './customerPaymentHeader';
import CancelIcon from '@mui/icons-material/Cancel';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import {
  handleAddPaymentAllocation,
  handlePaymentAllocationChange,
  handleRemoveAllocation,
  totalAmountAllocation,
} from './PaymentHelper';
import CloseIcon from '@mui/icons-material/Close';
import { useEffect, useState } from 'react';
import { removeUnderScore } from '@/configs/common';
import { toast } from 'react-toastify';

export const AllocationPopup = ({
  allocations,
  open,
  setOpen,
  form,
  cardId,
  paymentId,
  setUpdatedPayments,
  setAllocationsUI,
  limits,
  sums,
  handleSumChange,
  viewOnly,
  setViewOnly,
  totalAmount,
  exchangeRate,
  customerAmountApplied,
  setCustomerAmountApplied,
  isDarkMode,
  cardType,
}) => {
  const [totalAllocationsAmount, setTotalAllocationsAmount] = useState(0);
  useEffect(() => {
    totalAmountAllocation(allocations, setTotalAllocationsAmount);
  }, [allocations]);
  return (
    <Dialog
      open={open}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      onClose={() => {
        setOpen(false);
        setAllocationsUI([]);
        setViewOnly(false);
      }}
    >
      <DialogTitle id="alert-dialog-title">{'Payment Allocations'}</DialogTitle>
      <IconButton
        aria-label="close"
        onClick={() => {
          const check = allocations.find((op) => !op.amount || !op.type);
          if (check) {
            return toast.error(
              'All Allocations must contain amount and type both!',
            );
          }
          setOpen(false);
          setAllocationsUI([]);
          setViewOnly(false);
        }}
        sx={{
          position: 'absolute',
          right: 8,
          top: 8,
        }}
      >
        <CloseIcon />
      </IconButton>
      <DialogContent style={{ width: '600px' }}>
        <Grid size={12}>
          <Grid size={12}>
            {allocations &&
              allocations?.map((allocation) => {
                return (
                  <Grid
                    container
                    key={allocation.id}
                    spacing={2}
                    sx={{ marginBottom: '1rem', width: '100%' }}
                  >
                    <Grid size={5.5}>
                      <Controller
                        name={`allocation_type_${paymentId}_${allocation.id}`}
                        defaultValue={removeUnderScore(allocation?.type)}
                        control={form.control}
                        render={({ field, fieldState: { error } }) => {
                          const updatedField = {
                            ...field,
                            value: removeUnderScore(allocation?.type),
                            name: `allocation_type_${paymentId}_${allocation.id}`,
                          };
                          return (
                            <AutoComplete
                              className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                              url={false}
                              sx={{
                                minWidth: '250px !important',
                                padding: '0px 18px 0px 0px',
                              }}
                              label="Allocation Type"
                              fieldName="type"
                              field={updatedField}
                              error={error}
                              staticOptions={allocationTypes.map((op) =>
                                removeUnderScore(op),
                              )}
                              column={''}
                              modal={''}
                              disableAutoComplete={viewOnly}
                              handleOnChangeFromStepper={true}
                              stepperHandleOnChange={(_e, val) => {
                                const value = val?.replaceAll(' ', '_');
                                field.onChange(value);
                                handlePaymentAllocationChange(
                                  cardId,
                                  cardType,
                                  paymentId,
                                  allocation.id,
                                  'type',
                                  value,
                                  setUpdatedPayments,
                                  setAllocationsUI,
                                  limits,
                                  sums,
                                  handleSumChange,
                                  totalAmount,
                                  exchangeRate,
                                  customerAmountApplied,
                                  setCustomerAmountApplied,
                                  // allocation
                                  allocations,
                                );
                              }}
                            />
                          );
                        }}
                      />
                    </Grid>
                    <Grid size={5.5}>
                      <TextField
                        className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                        sx={{
                          minWidth: '250px !important',
                          padding: '0px 18px 0px 0px',
                        }}
                        size="small"
                        fullWidth
                        label="Amount"
                        type="number"
                        disabled={viewOnly}
                        value={allocation?.amount}
                        InputProps={{ inputProps: { min: 0 } }}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value);
                          if (!isNaN(value)) {
                            handlePaymentAllocationChange(
                              cardId,
                              cardType,
                              paymentId,
                              allocation.id,
                              'amount',
                              value,
                              setUpdatedPayments,
                              setAllocationsUI,
                              limits,
                              sums,
                              handleSumChange,
                              totalAmount,
                              exchangeRate,
                              customerAmountApplied,
                              setCustomerAmountApplied,
                              allocations,
                            );
                          }
                          totalAmountAllocation(
                            allocations,
                            setTotalAllocationsAmount,
                          );
                        }}
                        helperText={
                          isNaN(allocation?.amount) && (
                            <Typography
                              sx={{ fontSize: 10, color: '#D32F2F' }}
                              component="div"
                            >
                              Enter An Amount{' '}
                            </Typography>
                          )
                        }
                      />
                    </Grid>
                    <Grid
                      sx={{
                        display: viewOnly ? 'none' : 'block',
                      }}
                      size={0.2}
                    >
                      <Tooltip title="Remove Allocation">
                        <Button
                          variant="text"
                          color="error"
                          onClick={() => {
                            handleRemoveAllocation(
                              cardId,
                              cardType,
                              paymentId,
                              allocation.id,
                              setUpdatedPayments,
                              sums,
                              handleSumChange,
                              customerAmountApplied,
                              setCustomerAmountApplied,
                              exchangeRate,
                            );
                            totalAmountAllocation(
                              allocations,
                              setTotalAllocationsAmount,
                            );
                          }}
                          sx={{ padding: 0, minWidth: 'auto' }}
                        >
                          <CancelIcon />
                        </Button>
                      </Tooltip>
                    </Grid>
                  </Grid>
                );
              })}
            {
              <Grid
                sx={{
                  display: viewOnly ? 'none' : 'flex',
                }}
                size={12}
              >
                <Grid size={5.8}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => {
                      handleAddPaymentAllocation(
                        cardType,
                        paymentId,
                        setUpdatedPayments,
                        setAllocationsUI,
                      );
                    }}
                  >
                    Add Allocation
                  </Button>
                </Grid>
                <Grid
                  sx={{
                    position: 'relative',
                    height: '40px',
                    display: 'flex',
                  }}
                  size={6.2}
                >
                  <Grid
                    sx={{
                      marginTop: '-2%',
                      width: '70%',
                      height: '100%',
                      flexDirection: 'column',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: 10,
                        color: '#07bc0c',
                        zIndex: '100000000',
                        backgroundColor: isDarkMode ? '#191919' : 'white',
                        position: 'relative',
                        width: '35%',
                        paddingLeft: '1%',
                      }}
                      component="div"
                    >
                      Total Amount
                    </Typography>

                    <Typography
                      sx={{ fontSize: 17, color: '#07bc0c' }}
                      component="div"
                    >
                      {Number(totalAllocationsAmount).toLocaleString('en-US', {
                        minimumFractionDigits: 3,
                        maximumFractionDigits: 3,
                      })}
                    </Typography>
                  </Grid>
                  <hr
                    style={{
                      position: 'absolute',
                      width: '70%',
                      height: '100%',
                      top: '0',
                      borderRadius: '5px',
                      border: '1px solid #07bc0c',
                      margin: 0,
                    }}
                  />
                </Grid>
              </Grid>
            }
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
};
