import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Slide from '@mui/material/Slide';
import { TransitionProps } from '@mui/material/transitions';
import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import { Grid, TextField, Typography } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { styled, useTheme } from '@mui/material/styles';
import { toast } from 'react-toastify';
const Transition = React.forwardRef<
  unknown,
  TransitionProps & { children: React.ReactElement<any, any> }
>(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function RejectionModal({ open, setOpen, onConfirm }) {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [reason, setReason] = React.useState<string>('');
  const [file, setFile] = React.useState<any>();
  const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });

  const handleClose = () => {
    setOpen(false);
    setFile(null);
    setReason('');
  };
  const onSubmit = () => {
    if (!reason || !file) {
      return toast.error('Reason & Attachment both are Required!');
    }
    onConfirm({ reason, file });
  };
  return (
    <Dialog
      fullWidth
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={handleClose}
      aria-describedby="alert-dialog-slide-description"
    >
      <DialogTitle>Payment Rejection</DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-slide-description" component="div">
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 12 }}>
              <TextField
                size="small"
                id="reason"
                label="Reason"
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                fullWidth
                variant="outlined"
                value={reason}
                InputProps={{ inputProps: { min: 1 } }}
                onChange={(e) => setReason(e.target.value)}
              />
            </Grid>
            <Grid className="text-center" size={{ xs: 12, md: 12 }}>
              <Button
                component="label"
                variant="contained"
                startIcon={<CloudUploadIcon />}
              >
                Select Attachments
                <VisuallyHiddenInput
                  type="file"
                  onChange={(e) => {
                    setFile(e.target.files[0]);
                  }}
                />
              </Button>
              {file ? <Typography>{file?.name}</Typography> : null}
            </Grid>
          </Grid>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button variant="contained" color="primary" onClick={onSubmit}>
          <ThumbUpAltIcon /> Save
        </Button>
        <Button variant="outlined" color="error" onClick={handleClose}>
          <ThumbDownAltIcon /> Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}
