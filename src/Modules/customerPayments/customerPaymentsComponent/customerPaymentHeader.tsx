import { contextProvider } from '@/contexts/ProfileContext';
import InterestsIcon from '@mui/icons-material/Interests';
import { useContext } from 'react';
import DashboardIcon from '@mui/icons-material/Dashboard';
import * as z from 'zod';
import { permittedMenu } from '@/components/app/menu/MainMenu';
import { DASHBOARD } from '../../../configs/leftSideMenu/Permissions';

export const auctionCategories: { id: number; label: string }[] = [
  { id: 3, label: 'IAAI Payment' },
  { id: 9, label: 'Copart Payment' },
  { id: 25, label: 'Menheim' },
  { id: 124, label: 'Local' },
];

export const paymentTypes: any = [
  { value: 'shipment', label: 'Shipment' },
  { value: 'mix', label: 'MIX' },
  { value: 'single_vcc', label: 'Single VCC' },
  { value: 'clearance', label: 'Clearance (Combine Booking)' },
  { value: 'clear_log', label: 'Clear Log' },
  { value: 'delivery_charge', label: 'Delivery Charge' },
  { value: 'exit_claim_charge', label: 'Exit Claim Charge' },
  { value: 'detention_charge', label: 'Detention Charge' },
  { value: 'auction', label: 'Auction' },
  { value: 'cash', label: 'Cash' },
  { value: 'non_allocated', label: 'Non Allocated' },
];

export const allocationTypes: [string, ...string[]] = [
  'towing',
  'shipping',
  'title',
  'storage_clr',
  'storage_do',
  'charge',
  'other',
  'storage',
  'dismantle',
  'clearance',
  'vat_custom',
  'freight',
  'attestation_fee',
  'inspection_charges',
  'auction_storage',
  'sharjah_yard_storage',
  'fed_ex_or_mailing_fee',
  'recovery_fee',
  'custom_hold',
  'relist_fee',
  'detention_charges',
  'shortage',
  'suv_charges',
  'tds_charges',
  'registration_fee',
  'transportation_fee',
  'office_fee_and_bank',
  'empty_containers',
  'vehicle_price',
  'previous_payment',
  'coo_charges',
  'cash',
  'late_fee',
  'hybrid_charges',
];

export const shipmentAllocationTypes: [string, ...string[]] = [
  'dismantal_cost',
  'other_cost',
  'ship_cost',
  'pgl_storage_costs',
  'towing_cost',
  'title_charge',
];

export const sumTypes = {
  dismantle: 'dismantal_cost',
  other: 'other_cost',
  shipping: 'ship_cost',
  storage: 'pgl_storage_costs',
  towing: 'towing_cost',
  title: 'title_charge',
};

export const sumTypesMix = {
  clearance: 'clearance',
  towing: 'tow_amount',
  vat_custom: 'vat_and_custom',
  freight: 'freight',
  attestation_fee: 'attestation_fee',
  inspection_charges: 'inspection_charges',
  title: 'title_charges',
  auction_storage: 'auction_storage',
  sharjah_yard_storage: 'sharjah_yard_storage',
  fed_ex_or_mailing_fee: 'fed_ex_or_mailing_fee',
  recovery_fee: 'recovery_fee',
  custom_hold: 'custom_hold',
  relist_fee: 'relist_fee',
  detention_charges: 'detention_charges',
  shortage: 'shortage',
  suv_charges: 'suv_charges',
  tds_charges: 'tds_charges',
  registration_fee: 'registration_fee',
  transportation_fee: 'transportation_fee',
  office_fee_and_bank: 'office_fee_and_bank',
  empty_containers: 'empty_containers',
  other: 'other_charges',
  coo_charges: 'coo_charges',
  hybrid_charges: 'hybrid_charges',
};

export const excludedPaymentMethods = [
  'sales_tax',
  'zelle',
  'online_payment',
  'delivery_order',
];

export const paymentMethods = {
  cash: 'Cash',
  cashbook: 'Cashbook',
  wire: 'Wire',
  check: 'Check',
  kabul_cash: 'Kabul Cash',
  mukhasa: 'Mukhasa',
  sales_tax: 'Sales tax',
  zelle: 'Zelle',
  buyer_fee_discount: 'Buyer Fee Discount',
  damage_credit: 'Damage Credit',
  demurrage_credit: 'Demurrage Credit',
  storage_credit: 'Storage Credit',
  exit_paper_credit: 'Exit Paper Credit',
  loading_amendment_credit: 'Loading Amendment Credit',
  missing_loading_credit: 'Missing Loading Credit',
  clearance_credit: 'Clearance Credit',
  shortage_section: 'Shortage Section',
  online_payment: 'Online Payment (No confirmations #)',
  refund_of_suspended: 'Refund of Suspended Account',
  delivery_order: 'Delivery Order',
  extra_payment_copart_iaa: 'Extra Payment in Copart IAA',
  customer_purchased_vehicle_removed:
    'Customer Purchased Vehicle, Removed from Account & Auction',
  delivery_order_credit: 'Delivery Order Credit',
  commission: 'Commission',
  discount_credit: 'Discount Credit',
  sale_tax_credit: 'Sale Tax Credit',
  port_storage_and_demurrage_credit: 'Port Storage and Demurrage Credit',
  mailing_fee_credit: 'Mailing Fee Credit',
  relist_fee_credit: 'Relist Fee Credit',
  other_credit: 'Other Credit',
  hawala: 'Hawala',
};

export const creditPaymentTypes = {
  damage_credit: 'Damage Credit',
  demurrage_credit: 'Demurrage Credit',
  storage_credit: 'Storage Credit',
  exit_paper_credit: 'Exit Paper Credit',
  loading_amendment_credit: 'Loading Amendment Credit',
  missing_loading_credit: 'Missing Loading Credit',
  clearance_credit: 'Clearance Credit',
  shortage_section: 'Shortage Section',
  mukhasa: 'Mukhasa',
  buyer_fee_discount: 'Buyer Fee Discount',
  refund_of_suspended: 'Refund of Suspended Account',
  extra_payment_copart_iaa: 'Extra Payment in Copart IAA',
  customer_purchased_vehicle_removed:
    'Customer Purchased Vehicle, Removed from Account & Auction',
  delivery_order_credit: 'Delivery Order Credit',
  commission: 'Commission',
  discount_credit: 'Discount Credit',
  sale_tax_credit: 'Sale Tax Credit',
  port_storage_and_demurrage_credit: 'Port Storage and Demurrage Credit',
  mailing_fee_credit: 'Mailing Fee Credit',
  relist_fee_credit: 'Relist Fee Credit',
  other_credit: 'Other Credit',
};

export const generalPaymentTypes = Object.keys(paymentMethods).filter(
  (op) => !Object.keys(creditPaymentTypes).includes(op),
);

const paymentMethodsArray = Object.keys(paymentMethods) as [
  string,
  ...string[],
];

export const creditPaymentMethods = {
  mukhasa: 'Mukhasa',
  buyer_fee_discount: 'Buyer Fee Discount',
  damage_credit: 'Damage Credit',
  demurrage_credit: 'Demurrage Credit',
  storage_credit: 'Storage Credit',
  exit_paper_credit: 'Exit Paper Credit',
  loading_amendment_credit: 'Loading Amendment Credit',
  missing_loading_credit: 'Missing Loading Credit',
  clearance_credit: 'Clearance Credit',
  shortage_section: 'Shortage Section',
  discount_credit: 'Discount Credit',
  sale_tax_credit: 'Sale Tax Credit',
  port_storage_and_demurrage_credit: 'Port Storage and Demurrage Credit',
  mailing_fee_credit: 'Mailing Fee Credit',
  relist_fee_credit: 'Relist Fee Credit',
  other_credit: 'Other Credit',
  refund_of_suspended: 'Refund of Suspended Account',
  extra_payment_copart_iaa: 'Extra Payment in Copart IAA',
  customer_purchased_vehicle_removed:
    'Customer Purchased Vehicle, Removed from Account & Auction',
  delivery_order_credit: 'Delivery Order Credit',
  commission: 'Commission',
};

export const bankApprovalRequiredMethods: [string, ...string[]] = [
  'wire',
  'check',
  'sales_tax',
  'zelle',
  'online_payment',
  'delivery_order',
  'kabul_cash',
  'hawala',
];

export const stateOptions: [string, ...string[]] = [
  'pending',
  'approved',
  'reviewed',
  'final_reviewed',
  'bank_approve',
];

export const currencyOptions: [string, ...string[]] = [
  'USD',
  'AED',
  'OMR',
  'GEL',
];

export const HeaderInfo = () => {
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const perms = permittedMenu(profile?.data);

  let breadcrumbs = [
    {
      href: 'false',
      name: 'Customer Payments',
      icon: <InterestsIcon sx={{ fontSize: '18px' }} />,
      key: '2',
    },
  ];

  if (perms?.includes(DASHBOARD?.VIEW)) {
    breadcrumbs.unshift({
      href: '/',
      name: 'Dashboard',
      icon: <DashboardIcon sx={{ fontSize: '18px' }} />,
      key: '1',
    });
  }

  return breadcrumbs;
};

export const paymentCardSchema = z.object({
  id: z.any(),
  rec_id: z.any(),
  type: z.enum(paymentTypes.map((op) => op.value)).optional(),
  limit: z
    .number()
    .nonnegative()
    .transform((value) => +value),
  exchange_rate: z
    .number()
    .nonnegative()
    .transform((value) => +value),
  short_amount: z
    .number()
    .nonnegative()
    .transform((value) => +value),
});

export const schema = z.object({
  payment_method: z.enum(paymentMethodsArray),

  amount: z
    .number()
    .min(1)
    .nonnegative()
    .transform((value) => +value),

  transaction_fee: z
    .number()
    .min(0)
    .nonnegative()
    .transform((value) => +value),

  inapplicable_amount: z
    .number()
    .min(0)
    .nonnegative()
    .transform((value) => +value),

  amount_applied: z
    .number()
    .nonnegative()
    .optional()
    .transform((value) => +value),

  currency: z.enum(currencyOptions),

  exchange_rate: z
    .number()
    .nonnegative()
    .transform((value) => +value),

  transaction_number: z.string().optional(),
  remark: z.string().optional(),
  link: z.string().optional(),
  payment_date: z.string().optional(),

  company_id: z.number({
    required_error: 'Company is required',
    invalid_type_error: 'Company must be one of the Options',
  }),
  bank_id: z.any().optional(),

  customer_id: z.number({
    required_error: 'Customer is required',
    invalid_type_error: 'Customer must be one of the Options',
  }),
  payment_cards: z.array(paymentCardSchema).optional(),

  customer_transaction_bank_details: z
    .array(
      z.object({
        id: z.any(),
        title: z.string(),
        reference_number: z.string(),
        remark: z.string(),
        attachment: z.any(),
        deposit_date: z.string().optional().nullable(),
      }),
    )
    .optional(),
  customer_credits: z
    .array(
      z.object({
        id: z.union([z.number(), z.string()]).nullable().optional(),
        vehicle_id: z.union([z.number(), z.string()]).nullable().optional(),
        container_id: z.number().nullable().optional(),
        amount: z.number(),
        remark: z.string().nullable().optional(),
      }),
    )
    .nullable()
    .optional(),
  credit_date: z.string().optional(),
  service_charge: z.number().optional(),
  payments: z
    .array(
      z.object({
        id: z.any(),
        amount_applied: z
          .number()
          .nonnegative()
          .optional()
          .transform((value) => +value),
        transaction_number: z.string().nullable().optional(),
        state: z.enum(stateOptions).optional(),
        type: z
          .enum(
            paymentTypes
              .filter((pop) => pop.value !== 'non_allocated')
              .map((op) => op.value),
          )
          .optional(),
        payment_remark: z.string().optional(),
        payment_date: z.string().optional(),
        exchange_rate: z
          .number()
          .nonnegative()
          .transform((value) => +value)
          .optional(),
        auction_category_id: z.number().nullable().optional(),
        customer_tran_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        mix_shipping_vehicle_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        shipment_invoice_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        clearance_invoice_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        log_invoice_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        single_vcc_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        exit_claim_charge_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        detention_charge_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        delivery_charge_invoice_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        vehicle_id: z
          .nullable(
            z
              .number()
              .nonnegative()
              .transform((value) => +value),
          )
          .optional(),
        payment_allocations: z
          .array(
            z.object({
              id: z.any(),
              amount: z
                .number()
                .nonnegative()
                .optional()
                .transform((value) => +value),
              type: z.enum(allocationTypes).optional(),
              payment_id: z.number().optional(),
            }),
          )
          .optional(),
      }),
    )
    .optional(),
});
import AttachmentIcon from '@mui/icons-material/Attachment';
export const SummaryHeaders = [
  { id: 'company_name', label: 'Company' },

  {
    id: 'pending',
    label: 'Pending',
  },
  {
    id: 'reviewed',
    label: 'Reviewed',
  },

  {
    id: 'final_reviewed',
    label: 'Final Reviewed',
  },
  {
    id: 'approved',
    label: 'Approved',
  },
  {
    id: 'total',
    label: 'Total',
  },
];
const commonFields = [
  { id: 'id', label: 'Payment Number', pdfWidth: 50 },
  { id: 'attachment', label: <AttachmentIcon /> },
  { id: 'link', label: 'Link' },
  { id: 'transaction_number', label: 'Transaction #' },
  { id: 'company_name', label: 'Company' },
  { id: 'customer_name', label: 'Customer' },
  { id: 'amount_1', label: 'Amount', align: 'right', pdfWidth: 120 },
  {
    id: 'amount_applied_1',
    label: 'Amount Applied',
    align: 'right',
    pdfWidth: 150,
  },
  {
    id: 'transaction_fee',
    label: 'Transaction Fee',
    align: 'right',
    pdfWidth: 150,
  },
  {
    id: 'inapplicable_amount',
    label: 'Inapplicable Amount',
    align: 'right',
    pdfWidth: 150,
  },
  { id: 'remaining', label: 'Remaining Amount', align: 'right', pdfWidth: 150 },
  { id: 'currency', label: 'Currency', pdfWidth: 120, align: 'left' },
  { id: 'state', label: 'State' },
  {
    id: 'exchange_rate',
    label: 'Exchange Rate',
    align: 'right',
    pdfWidth: 100,
  },
  { id: 'payment_date', label: 'Payment Date', pdfWidth: 100 },
  {
    id: 'created_byToUsers',
    label: 'Created By',
    sortColumnName: 'created_by',
  },
  { id: 'created_at', label: 'Created At' },
  {
    id: 'updated_byToUsers',
    label: 'Updated By',
    sortColumnName: 'updated_by',
  },
  { id: 'updated_at', label: 'Updated At' },
];
export const PaymentsHeader = [
  commonFields[0],
  commonFields[1],
  commonFields[2],
  { id: 'payment_types', label: 'Payments' },
  commonFields[3],
  commonFields[4],
  commonFields[5],
  {
    id: 'payment_method',
    label: 'Payment Method',
    pdfWidth: 120,
    align: 'left',
  },
  ...commonFields.slice(6),
];
export const CustomerCreditsHeader = [
  commonFields[0],
  commonFields[1],
  commonFields[2],
  {
    id: 'payment_method',
    label: 'Credit Category',
    pdfWidth: 120,
    align: 'left',
  },
  commonFields[3],
  commonFields[4],
  commonFields[5],
  { id: 'payment_types', label: 'Applied Section' },
  ...commonFields.slice(6),
];
export const GeneralPaymentsHeaderPrint = [
  {
    id: 'id',
    label: 'Payment Number',
    pdfWidth: 50,
  },
  {
    id: 'attachment',
    label: 'Attachment Link',
  },
  {
    id: 'link',
    label: 'Link',
  },
  // {
  //   id: 'payment_types',
  //   label: 'Payments',
  // },
  {
    id: 'transaction_number',
    label: 'Transaction #',
  },
  {
    id: 'company_name',
    label: 'Company',
  },
  {
    id: 'customer_name',
    label: 'Customer',
  },
  // {
  //   id: 'payment_method',
  //   label: 'Payment Method',
  //   pdfWidth: 80,
  //   align: 'center',
  // },
  {
    id: 'amount',
    label: 'Amount',
    align: 'right',
    pdfWidth: 120,
  },
  {
    id: 'amount_applied',
    label: 'Amount Applied',
    align: 'right',
    pdfWidth: 150,
  },
  {
    id: 'remaining',
    label: 'Remaining Amount',
    align: 'right',
    pdfWidth: 150,
  },
  {
    id: 'currency',
    label: 'Currency',
    pdfWidth: 120,
    align: 'left',
  },
  {
    id: 'state',
    label: 'State',
  },
  {
    id: 'exchange_rate',
    label: 'Exchange Rate',
    align: 'right',
    pdfWidth: 100,
  },
  {
    id: 'payment_date',
    label: 'Payment Date',
    pdfWidth: 100,
  },
  {
    id: 'created_byToUsers',
    label: 'Created By',
    sortColumnName: 'created_by',
  },

  {
    id: 'created_at',
    label: 'Created At',
  },

  {
    id: 'updated_byToUsers',
    label: 'Updated By',
    sortColumnName: 'updated_by',
  },
  {
    id: 'updated_at',
    label: 'Updated At',
  },
];
export const PaymentsHeaderPrint = [
  GeneralPaymentsHeaderPrint[0],
  GeneralPaymentsHeaderPrint[1],
  GeneralPaymentsHeaderPrint[2],
  { id: 'payment_types', label: 'Payments' },
  GeneralPaymentsHeaderPrint[3],
  GeneralPaymentsHeaderPrint[4],
  GeneralPaymentsHeaderPrint[5],
  {
    id: 'payment_method',
    label: 'Payment Method',
    pdfWidth: 120,
    align: 'left',
  },
  ...GeneralPaymentsHeaderPrint.slice(6),
];
export const CustomerCreditsHeaderPrint = [
  GeneralPaymentsHeaderPrint[0],
  GeneralPaymentsHeaderPrint[1],
  GeneralPaymentsHeaderPrint[2],
  {
    id: 'payment_method',
    label: 'Credit Methods',
    pdfWidth: 120,
    align: 'left',
  },
  GeneralPaymentsHeaderPrint[3],
  GeneralPaymentsHeaderPrint[4],
  GeneralPaymentsHeaderPrint[5],
  { id: 'payment_types', label: 'Category' },
  ...GeneralPaymentsHeaderPrint.slice(6),
];
export const filterContentCustomerPayments = [
  {
    title: 'ID Filtering',
    items: [
      {
        name: 'payments.some.shipmentInvoice.container_id',
        label: 'Container',
        type: 'autocomplete',
        url: '/autoComplete?column=container_number&modal=containers&ids=',
        keyName: 'container_number',
      },
      {
        name: 'company_id',
        label: 'Company',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=companies&ids=',
        keyName: 'name',
      },
      {
        name: 'created_byToUsers.department_id',
        label: 'Departments',
        type: 'autocomplete',
        url: '/autoComplete?column=name&modal=departments&ids=',
        keyName: 'name',
      },
      {
        name: 'created_by',
        label: 'Created By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&ids=',
        keyName: 'fullname',
      },
      {
        name: 'updated_by',
        label: 'Updated By',
        type: 'autocomplete',
        url: '/autoComplete?column=fullname&modal=users&ids=',
        keyName: 'fullname',
      },
      {
        name: 'payment_method',
        label: 'Payment Method',
        type: 'autocomplete',
        url: false,
        options: Object.keys(paymentMethods).map((key: any) => ({
          id: key,
          payment_method: paymentMethods[key],
        })),
        keyName: 'payment_method',
      },
      {
        name: 'many@@payments.type',
        label: 'Payment Types',
        type: 'autocomplete',
        url: false,
        options: paymentTypes.map((op: any) => ({
          id: op.value,
          type: op.label,
        })),
        keyName: 'type',
      },
    ],
  },
  ,
  // {
  //   title: 'Data',
  //   items: [],
  // },
  {
    title: 'Date Range',
    items: [
      {
        name: 'created_at',
        label: 'Created At',
        type: 'date_range',
      },
      {
        name: 'updated_at',
        label: 'Updated At',
        type: 'date_range',
      },
      {
        name: 'payment_date',
        label: 'Payment Date',
        type: 'date_range',
      },
    ],
  },
];

export const paymentTypeDisplayNames = {
  shipment: 'Shipment',
  mix: 'Mix',
  clearance: 'Combine Booking',
  clear_log: 'Clear Log',
  single_vcc: 'Single VCC',
  exit_claim_charge: 'Exit Claim Charge',
  delivery_charge: 'Delivery Charge',
  detention_charge: 'Detention Charge',
  auction: 'Auction',
};

export const allocationTypeDisplayNames = {
  towing: 'Towing',
  shipping: 'Shipping',
  title: 'Title',
  storage_clr: 'Storage Clearance',
  storage_do: 'Storage Delivery Order',
  charge: 'Charge',
  other: 'Other',
  storage: 'Storage',
  dismantle: 'Dismantle',
  clearance: 'Clearance',
  vat_custom: 'VAT Custom',
  freight: 'Freight',
  attestation_fee: 'Attestation Fee',
  inspection_charges: 'Inspection Charges',
  auction_storage: 'Auction Storage',
  sharjah_yard_storage: 'Sharjah Yard Storage',
  fed_ex_or_mailing_fee: 'Fed Ex or Mailing Fee',
  recovery_fee: 'Recovery Fee',
  custom_hold: 'Custom Hold',
  relist_fee: 'Relist Fee',
  detention_charges: 'Detention Charges',
  shortage: 'Shortage',
  suv_charges: 'SUV Charges',
  tds_charges: 'TDS Charges',
  registration_fee: 'Registration Fee',
  transportation_fee: 'Transportation Fee',
  office_fee_and_bank: 'Office Fee and Bank',
  empty_containers: 'Empty Containers',
  other_charges: 'Other Charges',
  coo_charges: 'COO Charges',
  previous_payment: 'Previous Payment',
  vehicle_price: 'Vehicle Price',
  cash: 'Cash',
  late_fee: 'Late Fee',
  hybrid_charges: 'Hybrid Charges',
};

export const paymentTypeOptions = {
  shipment: 'shipmentInvoice',
  mix: 'mixShippingVehicle',
  clearance: 'clearanceInvoice',
  clear_log: 'logInvoice',
  single_vcc: 'singleVcc',
  exit_claim_charge: 'exitClaimCharge',
  detention_charge: 'detentionCharge',
  delivery_charge: 'deliveryChargeInvoice',
  auction: 'vehicle',
};

export const paymentNameOptions = {
  shipment: 'shipment_invoice_id',
  mix: 'mix_shipping_vehicle_id',
  clearance: 'clearance_invoice_id',
  clear_log: 'log_invoice_id',
  single_vcc: 'single_vcc_id',
  exit_claim_charge: 'exit_claim_charge_id',
  detention_charge: 'detention_charge_id',
  delivery_charge: 'delivery_charge_invoice_id',
  auction: 'vehicle_id',
};

export const paymentModalOptions = {
  shipment: 'invoices',
  mix: 'mix_shipping_vehicles',
  clearance: 'clearance_combine_booking_invocies',
  clear_log: 'log_invoices',
  single_vcc: 'single_vcc',
  exit_claim_charge: 'exit_claim_charge',
  detention_charge: 'detention_charge',
  delivery_charge: 'delivery_charge_invoice',
  auction: 'vehicles',
};

export const paymentStateOptions = {
  bank_approve: 'bank_approve',
  pending: 'pending',
  reviewed: 'reviewed',
  final_reviewed: 'final_reviewed',
  approved: 'approved',
  bank_reject: 'bank_reject',
};

export const restrictedPaymentMethods = ['cash', 'cashbook'];

export const noneBankPaymentMethods = ['cash', 'cashbook'];

export const creditCustomerPaymentMethods = [
  { id: 'mukhasa', label: 'Mukhasa', type: 'vehicle' },
  { id: 'buyer_fee_discount', label: 'Buyer Fee Discount', type: 'container' },
  { id: 'damage_credit', label: 'Damage Credit', type: 'vehicle' },
  { id: 'demurrage_credit', label: 'Demurrage Credit', type: 'vehicle' },
  { id: 'storage_credit', label: 'Storage Credit', type: 'vehicle' },
  { id: 'exit_paper_credit', label: 'Exit Paper Credit', type: 'vehicle' },
  {
    id: 'loading_amendment_credit',
    label: 'Loading Amendment Credit',
    type: 'vehicle',
  },
  {
    id: 'missing_loading_credit',
    label: 'Missing Loading Credit',
    type: 'container',
  },
  { id: 'clearance_credit', label: 'Clearance Credit', type: 'vehicle' },
  { id: 'shortage_section', label: 'Shortage Section', type: 'container' },
  { id: 'other_credit', label: 'Other Credit', type: 'vehicle' },
  {
    id: 'refund_of_suspended',
    label: 'Refund of Suspended Account',
    type: 'container',
  },
  {
    id: 'extra_payment_copart_iaa',
    label: 'Extra Payment in Copart IAA',
    type: 'vehicle',
  },
  {
    id: 'customer_purchased_vehicle_removed',
    label: 'Customer Purchased Vehicle, Removed from Account & Auction',
    type: 'vehicle',
  },
  { id: 'discount_credit', label: 'Discount Credit', type: 'vehicle' },
  {
    id: 'delivery_order_credit',
    label: 'Delivery Order Credit',
    type: 'vehicle',
  },
  {
    id: 'port_storage_and_demurrage_credit',
    label: 'Port Storage & Demurrage Credit',
    type: 'vehicle',
  },
];

export const dualCreditPaymentMethods = [
  'storage_credit',
  'demurrage_credit',
  'delivery_order_credit',
  'port_storage_and_demurrage_credit',
  'discount_credit',
];
