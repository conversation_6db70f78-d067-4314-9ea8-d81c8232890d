import React, { useState } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { Box, Button, Chip, Grid, TextField, Tooltip } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DoneAllIcon from '@mui/icons-material/DoneAll';
import RotateLeftIcon from '@mui/icons-material/RotateLeft';
import { AllocationPopup } from './AllocationsPopup';
import { Controller } from 'react-hook-form';
import {
  approveRevertAllPayments,
  approvePayment,
  getPaymentCurrency,
  handlePaymentChange,
  handlePaymentCashRemove,
  getConfirmBoxDialogTitle,
  getConfirmBoxTitle,
  hasPermission,
} from './PaymentHelper';
import { removeUnderScore } from '@/configs/common';
import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import { SettingsBackupRestore } from '@mui/icons-material';
import { FormatAmount } from '@/components/mainComponents/FormatAmount';
import { CUSTOMER_PAYMENTS } from '@/configs/leftSideMenu/Permissions';
import {
  paymentStateOptions,
  auctionCategories,
} from './customerPaymentHeader';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import CancelIcon from '@mui/icons-material/Cancel';
import { choseColor } from '@/configs/configs';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';

export default function PaymentTable({
  form,
  cardId,
  type,
  updatedPayments,
  setUpdatedPayments,
  limits,
  sums,
  handleSumChange,
  confirmBox,
  setConfirmBox,
  selectedCustomerPayment,
  setSelectedCustomerPayment,
  totalAmount,
  exchangeRate,
  customerAmountApplied,
  setCustomerAmountApplied,
  currentPayment,
  setCurrentPayment,
  userPermissions,
  isDarkMode,
  revert,
  setRevert,
  allPaymentStateUpdateTo,
  setAllPaymentStateUpdateTo,
  allPaymentCurrentState,
  setAllPaymentCurrentState,
  customerAmountAppliedRef,
  AllUpdatedPayments,
}) {
  const [open, setOpen] = useState<boolean>(false);
  const [viewOnly, setViewOnly] = useState<boolean>(false);
  const [paymentId, setPaymentId] = useState<number>(0);
  const [allocationsUI, setAllocationsUI] = useState<any>(null);
  const [allConfirmBox, setAllConfirmBox] = useState<boolean>(false);
  const hasValidPayments = updatedPayments?.every(
    (payment) => typeof payment?.id === 'number',
  );

  const disabled = (payment: any) => {
    return (
      payment?.state === paymentStateOptions.reviewed ||
      payment?.state === paymentStateOptions.approved ||
      payment?.state === paymentStateOptions.final_reviewed
    );
  };

  const hasNewlyAddedPayments = (state: string) => {
    return updatedPayments.some((payment: any) => payment?.state === state);
  };

  const getDefaultCategoryValue = (payment: any) => {
    let categoryId = payment?.auction_category_id ?? null;

    if (categoryId === 66 || categoryId === 78) {
      categoryId = 9;
    } else if (categoryId === 67 || categoryId === 79) {
      categoryId = 3;
    } else if (categoryId === 124 || categoryId === 125) {
      categoryId = 123;
    }

    const label = auctionCategories.find(
      (category) => category.id === categoryId,
    )?.label;

    return label ? { id: categoryId, label } : null;
  };
  return (
    <>
      {updatedPayments && updatedPayments?.length > 0 && hasValidPayments && (
        <Grid container alignItems="end" justifyContent="end" paddingRight={1}>
          {userPermissions.includes(CUSTOMER_PAYMENTS.REVIEW_SINGLE) &&
            selectedCustomerPayment?.state === paymentStateOptions.pending && (
              <>
                <Tooltip title="Review All Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(false);
                      setAllPaymentCurrentState(paymentStateOptions.pending);
                      setAllPaymentStateUpdateTo(paymentStateOptions.reviewed);
                    }}
                    sx={{ padding: 0, minWidth: 'auto', marginRight: 1 }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.reviewed ||
                      !hasPermission(type, userPermissions)
                    }
                  >
                    <DoneAllIcon color="success" />
                  </Button>
                </Tooltip>
                <Tooltip title="Revert All Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(true);
                      setAllPaymentCurrentState(paymentStateOptions.reviewed);
                      setAllPaymentStateUpdateTo(paymentStateOptions.pending);
                    }}
                    sx={{ padding: 0, minWidth: 'auto' }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.reviewed ||
                      !hasPermission(type, userPermissions)
                    }
                  >
                    <RotateLeftIcon color="info" />
                  </Button>
                </Tooltip>
              </>
            )}
          {userPermissions.includes(CUSTOMER_PAYMENTS.FINAL_REVIEW_SINGLE) &&
            selectedCustomerPayment?.state === paymentStateOptions.reviewed &&
            !hasNewlyAddedPayments(paymentStateOptions.pending) && (
              <>
                <Tooltip title="Final Review All Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(false);
                      setAllPaymentCurrentState(paymentStateOptions.reviewed);
                      setAllPaymentStateUpdateTo(
                        paymentStateOptions.final_reviewed,
                      );
                    }}
                    sx={{ padding: 0, minWidth: 'auto', marginRight: 1 }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.final_reviewed ||
                      !hasPermission(type, userPermissions)
                    }
                  >
                    <DoneAllIcon color="success" />
                  </Button>
                </Tooltip>
                <Tooltip title="Revert All Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(true);
                      setAllPaymentCurrentState(
                        paymentStateOptions.final_reviewed,
                      );
                      setAllPaymentStateUpdateTo(paymentStateOptions.reviewed);
                    }}
                    sx={{ padding: 0, minWidth: 'auto' }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.final_reviewed ||
                      !hasPermission(type, userPermissions)
                    }
                  >
                    <RotateLeftIcon color="info" />
                  </Button>
                </Tooltip>
              </>
            )}

          {userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE_SINGLE) &&
            selectedCustomerPayment?.state ===
              paymentStateOptions.final_reviewed &&
            !hasNewlyAddedPayments(paymentStateOptions.reviewed) && (
              <>
                <Tooltip title="Approve All the Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(false);
                      setAllPaymentCurrentState(
                        paymentStateOptions.final_reviewed,
                      );
                      setAllPaymentStateUpdateTo(paymentStateOptions.approved);
                    }}
                    sx={{ padding: 0, minWidth: 'auto', marginRight: 1 }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.approved ||
                      !hasPermission(type, userPermissions, true)
                    }
                  >
                    <DoneAllIcon color="success" />
                  </Button>
                </Tooltip>
                <Tooltip title="Revert All Payments">
                  <Button
                    color="success"
                    onClick={() => {
                      setAllConfirmBox(true);
                      setRevert(true);
                      setAllPaymentCurrentState(paymentStateOptions.approved);
                      setAllPaymentStateUpdateTo(
                        paymentStateOptions.final_reviewed,
                      );
                    }}
                    sx={{ padding: 0, minWidth: 'auto' }}
                    disabled={
                      selectedCustomerPayment?.state ===
                        paymentStateOptions.approved ||
                      !hasPermission(type, userPermissions, true)
                    }
                  >
                    <RotateLeftIcon color="info" />
                  </Button>
                </Tooltip>
              </>
            )}
        </Grid>
      )}
      <TableContainer component={Paper} key={cardId}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              {type !== 'cash' && (
                <>
                  <TableCell>INV #</TableCell>
                  {type === 'auction' && (
                    <TableCell align="center">Category</TableCell>
                  )}
                  <TableCell align="center">Invoice Amt</TableCell>
                </>
              )}
              <TableCell align="center">Payment Applied</TableCell>
              {type != 'shipment' && type != 'mix' && type != 'auction' && (
                <TableCell align="center">Exchange Rate</TableCell>
              )}
              <TableCell align="center">Allocations</TableCell>
              <TableCell align="center">Date</TableCell>
              {type === 'cash' && <TableCell align="center">T/No.</TableCell>}
              <TableCell align="center">Remark</TableCell>
              <TableCell align="center">Status</TableCell>
              {type !== 'cash' && <TableCell align="right">Action</TableCell>}
              <TableCell align="right"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {updatedPayments?.map((payment: any) => (
              <Tooltip
                key={payment?.id}
                title={
                  payment?.show_remaining_amount
                    ? 'This payment has a remaining Credit visible to the customer in the portal.'
                    : ''
                }
                placement="bottom"
                arrow
                disableHoverListener={!payment?.show_remaining_amount}
              >
                <TableRow
                  key={payment?.id}
                  sx={{
                    '&:last-child td, &:last-child th': { border: 0 },
                    backgroundColor: payment?.show_remaining_amount
                      ? `${isDarkMode ? '#363b37ff' : '#b5dbb2ff'}`
                      : '',
                  }}
                >
                  {type !== 'cash' && (
                    <>
                      <TableCell
                        component="th"
                        scope="row"
                        sx={{ fontSize: '12px' }}
                      >
                        {payment?.invoice_number}
                      </TableCell>
                      {type === 'auction' && (
                        <TableCell align="center">
                          <Controller
                            name={`auction_category_id_${payment?.id}`}
                            control={form.control}
                            render={({ field, fieldState: { error } }) => {
                              const currentValue = field.value;

                              const selectedOption = auctionCategories.find(
                                (opt) => opt.id === currentValue,
                              );

                              return (
                                <AutoComplete
                                  url={false}
                                  label="Auction Category"
                                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                                  fieldName=""
                                  field={field}
                                  disabled={disabled(payment)}
                                  error={error}
                                  staticOptions={auctionCategories}
                                  handleOnChangeFromStepper={true}
                                  stepperHandleOnChange={(_event, value) => {
                                    field.onChange(value?.id);
                                    handlePaymentChange(
                                      type,
                                      payment?.id,
                                      'auction_category_id',
                                      value?.id,
                                      setUpdatedPayments,
                                    );
                                  }}
                                  column=""
                                  modal=""
                                  value={selectedOption}
                                  defualtValue={getDefaultCategoryValue(
                                    payment,
                                  )}
                                />
                              );
                            }}
                          />
                        </TableCell>
                      )}

                      <TableCell align="center">
                        {`${payment?.invoice_amt_show ?? ''}`}
                        {payment?.invoice_discount
                          ? ` | ${payment?.invoice_discount}`
                          : ''}
                        {` | `}
                        <Button
                          disabled={
                            payment?.invoice_previous_allocations?.length == 0
                          }
                          onClick={() => {
                            setOpen(true);
                            setViewOnly(true);
                            setPaymentId(payment?.id);
                            setAllocationsUI(
                              payment?.invoice_previous_allocations,
                            );
                          }}
                        >
                          {payment?.invoice_paid_show}
                        </Button>
                      </TableCell>
                    </>
                  )}
                  <TableCell align="center">
                    {
                      <FormatAmount
                        amount={payment?.amount_applied}
                        currency={getPaymentCurrency(
                          payment?.type,
                          form.getValues('currency'),
                        )}
                      />
                    }
                  </TableCell>
                  {type != 'shipment' && type != 'mix' && type != 'auction' && (
                    <TableCell align="center">
                      <FormatAmount
                        amount={payment?.exchange_rate}
                        currency="none"
                      />
                    </TableCell>
                  )}
                  <TableCell align="center">
                    <Button
                      onClick={() => {
                        setOpen(true);
                        setPaymentId(payment?.id);
                        if (
                          payment?.state === paymentStateOptions.approved ||
                          payment?.state === paymentStateOptions.reviewed ||
                          payment?.state ===
                            paymentStateOptions.final_reviewed ||
                          !hasPermission(type, userPermissions)
                        ) {
                          setViewOnly(true);
                        }
                        setAllocationsUI(payment?.payment_allocations);
                      }}
                    >
                      {payment?.payment_allocations
                        ?.map((e) => removeUnderScore(e.type))
                        .join(' | ')}
                    </Button>
                  </TableCell>
                  <TableCell align="center">
                    <DatePicker
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                      sx={{ maxWidth: 200, width: '100%' }}
                      views={['year', 'month', 'day']}
                      format="yyyy/MM/dd"
                      defaultValue={new Date(payment?.payment_date)}
                      disabled={
                        disabled(payment) ||
                        !hasPermission(type, userPermissions)
                      }
                      onChange={(e) => {
                        const date = new Date(
                          dayjs(e).format('YYYY-MM-DD'),
                        ).toISOString();
                        handlePaymentChange(
                          type,
                          payment?.id,
                          'payment_date',
                          date,
                          setUpdatedPayments,
                        );
                      }}
                    />
                  </TableCell>
                  {type === 'cash' && (
                    <TableCell
                      align="center"
                      sx={{
                        width: 120,
                        fontWeight: 'bold',
                        border: '1px solid #ccc',
                        color: '#333',
                      }}
                    >
                      {payment?.transaction_number}
                    </TableCell>
                  )}
                  <TableCell align="center">
                    <TextField
                      key={payment?.id}
                      fullWidth
                      variant="standard"
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                      value={payment?.payment_remark}
                      disabled={disabled(payment)}
                      onChange={(e) =>
                        handlePaymentChange(
                          type,
                          payment?.id,
                          'payment_remark',
                          e.target.value,
                          setUpdatedPayments,
                        )
                      }
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      size="small"
                      label={removeUnderScore(payment?.state.toUpperCase())}
                      sx={{
                        backgroundColor: choseColor(payment?.state),
                        color: 'white',
                      }}
                    />
                  </TableCell>
                  {type == 'cash' && (
                    <TableCell align="center">
                      <Tooltip title="Remove Cash">
                        <Button
                          color="error"
                          disabled={
                            disabled(payment) ||
                            !hasPermission(type, userPermissions)
                          }
                          onClick={() =>
                            handlePaymentCashRemove(
                              cardId,
                              type,
                              sums,
                              customerAmountApplied,
                              setCustomerAmountApplied,
                              payment?.id,
                              AllUpdatedPayments,
                              setUpdatedPayments,
                              exchangeRate,
                              customerAmountAppliedRef,
                              handleSumChange,
                            )
                          }
                          sx={{ padding: 0, minWidth: 'auto' }}
                        >
                          <CancelIcon />
                        </Button>
                      </Tooltip>
                    </TableCell>
                  )}
                  {typeof payment?.id === 'number' &&
                  (userPermissions.includes(CUSTOMER_PAYMENTS.REVIEW_SINGLE) ||
                    userPermissions.includes(
                      CUSTOMER_PAYMENTS.APPROVE_SINGLE,
                    ) ||
                    userPermissions.includes(
                      CUSTOMER_PAYMENTS.FINAL_REVIEW_SINGLE,
                    )) ? (
                    <TableCell align="center">
                      <Box display="flex" justifyContent="center" gap={1}>
                        {userPermissions.includes(
                          CUSTOMER_PAYMENTS.REVIEW_SINGLE,
                        ) && (
                          <>
                            {payment?.state === paymentStateOptions.pending && (
                              <Tooltip title="Review Payment">
                                <Button
                                  color="success"
                                  disabled={
                                    !hasPermission(type, userPermissions)
                                  }
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(false);
                                  }}
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <CheckCircleIcon color="success" />
                                </Button>
                              </Tooltip>
                            )}
                            {payment?.state ===
                              paymentStateOptions.reviewed && (
                              <Tooltip title="Revert Payment">
                                <Button
                                  color="warning"
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(true);
                                  }}
                                  disabled={
                                    selectedCustomerPayment?.state ===
                                      paymentStateOptions.reviewed ||
                                    !hasPermission(type, userPermissions)
                                  }
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <SettingsBackupRestore color="info" />
                                </Button>
                              </Tooltip>
                            )}
                          </>
                        )}
                        {userPermissions.includes(
                          CUSTOMER_PAYMENTS.FINAL_REVIEW_SINGLE,
                        ) && (
                          <>
                            {payment?.state ===
                              paymentStateOptions.reviewed && (
                              <Tooltip title="Final Review Payment">
                                <Button
                                  color="success"
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(false);
                                  }}
                                  disabled={
                                    !hasPermission(type, userPermissions)
                                  }
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <CheckCircleIcon color="success" />
                                </Button>
                              </Tooltip>
                            )}
                            {payment?.state ===
                              paymentStateOptions.final_reviewed && (
                              <Tooltip title="Revert Payment">
                                <Button
                                  color="warning"
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(true);
                                  }}
                                  disabled={
                                    selectedCustomerPayment?.state ===
                                      paymentStateOptions.final_reviewed ||
                                    !hasPermission(type, userPermissions)
                                  }
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <SettingsBackupRestore color="info" />
                                </Button>
                              </Tooltip>
                            )}
                          </>
                        )}

                        {userPermissions.includes(
                          CUSTOMER_PAYMENTS.APPROVE_SINGLE,
                        ) && (
                          <>
                            {payment?.state ===
                              paymentStateOptions.final_reviewed && (
                              <Tooltip title="Approve Payment">
                                <Button
                                  color="success"
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(false);
                                  }}
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <CheckCircleIcon color="success" />
                                </Button>
                              </Tooltip>
                            )}
                            {payment?.state ===
                              paymentStateOptions.approved && (
                              <Tooltip title="Revert Payment">
                                <Button
                                  color="warning"
                                  onClick={() => {
                                    setConfirmBox(true);
                                    setCurrentPayment(payment);
                                    setRevert(true);
                                  }}
                                  disabled={
                                    selectedCustomerPayment?.state ===
                                    paymentStateOptions.approved
                                  }
                                  sx={{ padding: 0, minWidth: 'auto' }}
                                >
                                  <SettingsBackupRestore color="info" />
                                </Button>
                              </Tooltip>
                            )}
                          </>
                        )}

                        <AppConfirmDialog
                          maxWidth="md"
                          open={confirmBox}
                          onDeny={() => {
                            setConfirmBox(false);
                            setRevert(false);
                            setCurrentPayment(null);
                          }}
                          onConfirm={async () => {
                            await approvePayment(
                              currentPayment,
                              selectedCustomerPayment,
                              setSelectedCustomerPayment,
                              setCurrentPayment,
                              revert,
                              setRevert,
                            );
                            setConfirmBox(false);
                          }}
                          dialogTitle={
                            revert
                              ? 'Revert Payment'
                              : getConfirmBoxDialogTitle(payment?.state)
                          }
                          title={
                            revert
                              ? 'Are you sure you want to revert this payment?'
                              : getConfirmBoxTitle(payment?.state)
                          }
                        />
                      </Box>
                    </TableCell>
                  ) : null}
                </TableRow>
              </Tooltip>
            ))}
          </TableBody>
        </Table>
        <AllocationPopup
          open={open}
          setOpen={setOpen}
          allocations={allocationsUI}
          setAllocationsUI={setAllocationsUI}
          form={form}
          cardId={cardId}
          paymentId={paymentId}
          setUpdatedPayments={setUpdatedPayments}
          limits={limits}
          sums={sums}
          handleSumChange={handleSumChange}
          viewOnly={viewOnly}
          setViewOnly={setViewOnly}
          totalAmount={totalAmount}
          exchangeRate={exchangeRate}
          customerAmountApplied={customerAmountApplied}
          setCustomerAmountApplied={setCustomerAmountApplied}
          isDarkMode={isDarkMode}
          cardType={type}
        />
      </TableContainer>
      <AppConfirmDialog
        maxWidth="md"
        dialogTitle={
          revert
            ? 'Revert All Payments'
            : getConfirmBoxDialogTitle(allPaymentCurrentState, true)
        }
        title={
          revert
            ? 'Are you sure to revert all payments?'
            : getConfirmBoxTitle(allPaymentCurrentState, true)
        }
        open={allConfirmBox}
        onDeny={() => {
          setAllConfirmBox(false);
        }}
        onConfirm={async () => {
          await approveRevertAllPayments(
            allPaymentStateUpdateTo,
            type,
            selectedCustomerPayment,
            setSelectedCustomerPayment,
            userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE),
          );
          setAllConfirmBox(false);
        }}
      />
    </>
  );
}
