import { nanoid } from 'nanoid';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import {
  CUSTOMER_PAYMENTS,
  PAYMENT_TYPES,
} from '@/configs/leftSideMenu/Permissions';
import { countVehicleCosts } from '@/configs/configs';
import {
  bankApprovalRequiredMethods,
  paymentNameOptions,
  paymentStateOptions,
  paymentTypes,
  sumTypes,
  sumTypesMix,
} from './customerPaymentHeader';
import { Box, Chip } from '@mui/material';

export type allocationType = {
  id: number | string;
  amount: number;
  type: string;
};

export function handlePaymentCardChange(
  cardId: string,
  cardType: string,
  field: string,
  value: any,
  setPaymentsCards: any,
  setUpdatedPayments: any,
  handleSumChange: any,
  customerAmountApplied: any,
  setCustomerAmountApplied: any,
  exchangeRate: number,
) {
  let currCustomerAmountApplied: number = customerAmountApplied;
  handleSumChange(`sum_${cardId}`, 0);
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    if (updatedPayments[cardIndex] && updatedPayments[cardIndex].length > 0) {
      updatedPayments[cardIndex].forEach((payment) => {
        currCustomerAmountApplied -= fixNumberTwoDigit(
          (payment.amount_applied / payment.exchange_rate) * exchangeRate,
        );
      });
      setCustomerAmountApplied(currCustomerAmountApplied);
      updatedPayments.splice(cardIndex, 1);
    }
    return updatedPayments;
  });
  setPaymentsCards((prevCard) => {
    const updatedCard = [...prevCard];
    const cardIndex = updatedCard.findIndex((card) => card.id === cardId);
    updatedCard[cardIndex][field] = value;
    return updatedCard;
  });
}

export function handlePaymentAllocationChange(
  cardId: string,
  cardType: string,
  paymentId: number | string,
  allocationId: number | string,
  field: string,
  value: any,
  setUpdatedPayments: any,
  setAllocationsUI: any,
  limits: any,
  sums: any,
  handleSumChange: any,
  totalAmount: number,
  exchangeRate: number,
  customerAmountApplied: number,
  setCustomerAmountApplied: any,
  allocations: any,
) {
  const exists = allocations?.find((allocation) => allocation.type === value);
  const allocation_sum = allocations?.reduce(
    (acc: number, current: any) => acc + current.amount,
    0,
  );
  if (exists && field === 'type') {
    return toast.error('Please Select Another Type');
  }
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    const paymentIndex = updatedPayments[cardIndex].findIndex(
      (payment) => payment.id === paymentId,
    );
    const allocationIndex = updatedPayments[cardIndex][
      paymentIndex
    ].payment_allocations.findIndex((aloc) => aloc.id === allocationId);
    if (field === 'amount') {
      const changeAmount = fixNumberTwoDigit(
        value -
          updatedPayments[cardIndex][paymentIndex].payment_allocations[
            allocationIndex
          ].amount,
      );
      const currentCustomerAmountApplied: number =
        fixNumberTwoDigit(
          customerAmountApplied +
            (changeAmount /
              updatedPayments[cardIndex][paymentIndex].exchange_rate) *
              exchangeRate,
        ) || 0.0;
      const currentSum: number = sums[`sum_${cardId}`] + changeAmount || 0;
      const payment = updatedPayments[cardIndex][paymentIndex];
      const allocation = payment.payment_allocations[allocationIndex];
      if (
        allocation_sum + changeAmount + payment.invoice_paid >
          payment.invoice_amt &&
        allocationTypeNotStrgLateFee(allocation.type)
      ) {
        toast.error('Paid amount can not be greater than Invoice amount!.');
        return prevPayments;
      }
      if (
        islimitHasReached(
          null,
          totalAmount,
          currentCustomerAmountApplied,
          false,
          'grand_limit_allocations',
        ) ||
        islimitHasReached(cardId, limits, currentSum, false, 'allocation')
      ) {
        return prevPayments;
      }
      setCustomerAmountApplied(currentCustomerAmountApplied);
      handleSumChange(`sum_${cardId}`, currentSum);
    }
    updatedPayments[cardIndex][paymentIndex].payment_allocations[
      allocationIndex
    ][field] = value;
    updatedPayments[cardIndex][paymentIndex].amount_applied = getAmountApplied(
      updatedPayments[cardIndex][paymentIndex].payment_allocations,
    );
    setAllocationsUI(
      updatedPayments[cardIndex][paymentIndex].payment_allocations,
    );
    return updatedPayments;
  });
}

export function getAmountApplied(paymentAllocations: any) {
  return paymentAllocations
    ?.map((allocation) => allocation?.amount || 0)
    ?.reduce((accumulator, current) => accumulator + current, 0);
}

export function handleRemoveCard(
  cardId: string,
  cardType: string,
  setPaymentsCards: any,
  setUpdatedPayments: any,
  customerAmountApplied: any,
  setCustomerAmountApplied: any,
  exchangeRate: number,
) {
  let currCustomerAmountApplied: number = customerAmountApplied;
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    if (cardIndex !== -1 && updatedPayments[cardIndex].length > 0) {
      updatedPayments[cardIndex].forEach((payment) => {
        currCustomerAmountApplied -= fixNumberTwoDigit(
          (payment.amount_applied / payment.exchange_rate) * exchangeRate,
        );
      });
      setCustomerAmountApplied(fixNumberTwoDigit(currCustomerAmountApplied));
      updatedPayments.splice(cardIndex, 1);
    }

    return updatedPayments;
  });
  setPaymentsCards((prevCards) => {
    const updatedCards = [...prevCards];
    const cardIndex = updatedCards.findIndex((card) => card.id === cardId);
    updatedCards.splice(cardIndex, 1);
    return updatedCards;
  });
}

export async function handleRemovePayment(
  cardType: string,
  paymentId: number | string,
  previousSum: any,
  setUpdatedPayments: any,
  updatedPayments: any,
  customerAmountApplied: number,
  exchangeRate: number,
) {
  let previousCardSum: number = previousSum;
  let totalSum: number = customerAmountApplied;
  // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
  const cardIndex = updatedPayments.findIndex(
    (group) => group.length > 0 && group[0].type === cardType,
  );
  const paymentIndex = updatedPayments[cardIndex].findIndex(
    (payment) => payment.id === paymentId,
  );
  const cExchangeRate = updatedPayments[cardIndex][paymentIndex].exchange_rate;
  previousCardSum -= updatedPayments[cardIndex][paymentIndex].amount_applied;
  totalSum -=
    (updatedPayments[cardIndex][paymentIndex].amount_applied / cExchangeRate) *
    exchangeRate;
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    updatedPayments[cardIndex].splice(paymentIndex, 1);
    return updatedPayments;
  });
  return { previousCardSum, totalSum };
}

export function handleRemoveAllocation(
  cardId: string,
  cardType: string,
  paymentId: number | string,
  allocationId: number | string,
  setUpdatedPayments: any,
  sums: any,
  handleSumChange: any,
  customerAmountApplied: any,
  setCustomerAmountApplied: any,
  exchangeRate: number,
) {
  let previousSum: number = sums[`sum_${cardId}`] || 0;
  let allocationAmount: number = 0;
  let changedPaymentAmount: number = 0;
  let currCustomerAmountApplied: number = customerAmountApplied;
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    const paymentIndex = updatedPayments[cardIndex].findIndex(
      (payment) => payment.id === paymentId,
    );
    const allocationIndex = updatedPayments[cardIndex][
      paymentIndex
    ].payment_allocations.findIndex((aloc) => aloc.id === allocationId);
    allocationAmount =
      updatedPayments[cardIndex][paymentIndex].payment_allocations[
        allocationIndex
      ].amount;
    currCustomerAmountApplied -=
      allocationAmount > 0
        ? fixNumberTwoDigit(
            (allocationAmount /
              updatedPayments[cardIndex][paymentIndex].exchange_rate) *
              exchangeRate,
          )
        : 0.0;
    changedPaymentAmount =
      updatedPayments[cardIndex][paymentIndex].amount_applied -
      allocationAmount;
    updatedPayments[cardIndex][paymentIndex].payment_allocations.splice(
      allocationIndex,
      1,
    );
    return updatedPayments;
  });
  handlePaymentChange(
    cardType,
    paymentId,
    'amount_applied',
    changedPaymentAmount,
    setUpdatedPayments,
  );
  setCustomerAmountApplied(currCustomerAmountApplied);
  handleSumChange(`sum_${cardId}`, previousSum - allocationAmount);
}

// export function handleAddCard(
//   paymentsCards: any,
//   setPaymentsCards: any,
//   handleLimitChange: any,
//   handleSumChange: any,
//   handleCardExchangeRateChange: any,
//   handleShortAmountsChange: any,
// ) {
//   const dynamicType = paymentTypes.find(
//     (paymentType) =>
//       !paymentsCards.map((card) => card.type).includes(paymentType.value),
//   )?.value;
//   if (!dynamicType) {
//     return toast.error('All payment types are already in use.');
//   }
//   const newPaymentCard = {
//     id: nanoid(),
//     type: dynamicType,
//   };
//   handleLimitChange(`limit_${newPaymentCard.id}`, 0);
//   handleSumChange(`sum_${newPaymentCard.id}`, 0);
//   handleCardExchangeRateChange(`exchange_rate_${newPaymentCard.id}`, 1);
//   handleShortAmountsChange(`short_amount_${newPaymentCard.id}`, 0);
//   setPaymentsCards((prevCards) => [...prevCards, newPaymentCard]);
// }
export function handleAddCard(
  paymentsCards: any,
  setPaymentsCards: any,
  handleLimitChange: any,
  handleSumChange: any,
  handleCardExchangeRateChange: any,
  handleShortAmountsChange: any,
  userPermissions: any,
) {
  const availableTypes = paymentTypes.filter(
    (paymentType) =>
      !paymentsCards.map((card) => card.type)?.includes(paymentType.value) &&
      userPermissions.includes(
        PAYMENT_TYPES[paymentType.value.toLocaleUpperCase()],
      ),
  );
  const dynamicType = availableTypes.find((paymentType) =>
    userPermissions.includes(
      PAYMENT_TYPES[paymentType.value.toLocaleUpperCase()],
    ),
  )?.value;

  if (!dynamicType) {
    return toast.error(
      'All payment types are already in use or you do not have permission.',
    );
  }
  const newPaymentCard = {
    id: nanoid(),
    type: dynamicType,
    limit: 0,
    exchange_rate: 1,
    short_amount: 0,
  };
  handleLimitChange(`limit_${newPaymentCard.id}`, 0);
  handleSumChange(`sum_${newPaymentCard.id}`, 0);
  handleCardExchangeRateChange(`exchange_rate_${newPaymentCard.id}`, 1);
  handleShortAmountsChange(`short_amount_${newPaymentCard.id}`, 0);
  setPaymentsCards((prevCards) => [...prevCards, newPaymentCard]);
}

export function handleAddPayment(
  cardType: string,
  setUpdatedPayments: any,
  newPayment: any,
  setInvoiceLoader: any,
) {
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
    const existingGroupIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );

    if (existingGroupIndex !== -1) {
      updatedPayments[existingGroupIndex].push(newPayment);
    } else {
      updatedPayments.push([newPayment]);
    }
    return updatedPayments;
  });
  setInvoiceLoader(false);
}

export function handleAddPaymentAllocation(
  cardType: string,
  paymentId: number | string,
  setUpdatedPayments: any,
  setAllocationsUI: any,
  allocation: allocationType = null,
) {
  const newAllocation: allocationType = {
    id: nanoid(),
    amount: 0,
    type: '',
  };
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    const paymentIndex = updatedPayments[cardIndex].findIndex(
      (payment) => payment.id === paymentId,
    );
    updatedPayments[cardIndex][paymentIndex].payment_allocations.push(
      allocation ? allocation : newAllocation,
    );
    setAllocationsUI(
      updatedPayments[cardIndex][paymentIndex].payment_allocations,
    );
    return updatedPayments;
  });
}

export function handlePaymentChange(
  cardType: string,
  paymentId: number | string,
  field: string,
  value: any,
  setUpdatedPayments: any,
) {
  setUpdatedPayments((prevPayments) => {
    const updatedPayments = [...prevPayments];
    // const cardIndex = paymentsCards.findIndex((card) => card.id === cardId);
    const cardIndex = updatedPayments.findIndex(
      (group) => group.length > 0 && group[0].type === cardType,
    );
    const paymentIndex = updatedPayments[cardIndex].findIndex(
      (payment) => payment.id === paymentId,
    );
    updatedPayments[cardIndex][paymentIndex][field] = value;
    return updatedPayments;
  });
}

export async function approveRevertAllPayments(
  state: string,
  type: string,
  selectedCustomerPayment: any,
  setSelectedCustomerPayment: any,
  isAuditTeam: boolean,
) {
  let endpoint = 'reviewRevertAllPayments';
  if (isAuditTeam) {
    endpoint = 'approveRevertAllPayments';
  }

  await axios.patch(
    `/customer-payment-transaction/${endpoint}/${selectedCustomerPayment?.id}`,
    {
      state,
      type,
    },
  );
  await axios
    .get(`customer-payment-transaction/${selectedCustomerPayment?.id}`)
    .then((res) => {
      if (res.data.result) {
        toast.success('Payment Updated Successfully');
        setSelectedCustomerPayment(res.data.data);
      } else {
        toast.error('Something went wrong with the payment update');
      }
    })
    .catch((error) => toast.error(error));
}

export async function approvePayment(
  currentPayment: any,
  selectedCustomerPayment: any,
  setSelectedCustomerPayment: any,
  setCurrentPayment: any,
  isRevert: boolean,
  setRevert: any,
) {
  const { id, state } = currentPayment;

  //Prevent action if the customer payment is already approved
  if (
    selectedCustomerPayment?.state === paymentStateOptions.approved &&
    state === paymentStateOptions.approved
  )
    return toast.error('Please Revert the Customer Payment First');

  let endpoint = 'reviewRevertSinglePayment';
  let updateState = paymentStateOptions.pending;

  if (state === paymentStateOptions.pending) {
    if (!isRevert) {
      endpoint = 'reviewRevertSinglePayment';
      updateState = paymentStateOptions.reviewed;
    }
  }

  if (state === paymentStateOptions.reviewed) {
    if (!isRevert) {
      endpoint = 'reviewRevertSinglePayment';
      updateState = paymentStateOptions.final_reviewed;
    }
  }

  if (state === paymentStateOptions.final_reviewed) {
    if (!isRevert) {
      endpoint = 'approveRevertSinglePayment';
      updateState = paymentStateOptions.approved;
    } else {
      endpoint = 'reviewRevertSinglePayment';
      updateState = paymentStateOptions.reviewed;
    }
  }

  if (state === paymentStateOptions.approved) {
    if (isRevert) {
      endpoint = 'approveRevertSinglePayment';
      updateState = paymentStateOptions.final_reviewed;
    }
  }

  try {
    await axios
      .patch(`/customer-payment-transaction/${endpoint}`, {
        id,
        state: updateState,
      })
      .then((_res) => {
        setCurrentPayment(null);
      })
      .catch((error) => toast.error(error));

    await axios
      .get(`customer-payment-transaction/${selectedCustomerPayment?.id}`)
      .then((res) => {
        if (res.data.result) {
          toast.success('Payment Updated Successfully');
          setSelectedCustomerPayment(res.data.data);
        } else {
          toast.error('Something went wrong with the payment update');
        }
      })
      .catch((error) => toast.error(error));
    setRevert(false);
  } catch (error) {
    toast.error(
      error.message || 'An error occurred while updating the payment',
    );
  }
}

export async function approveCustomerPayment(
  selectedItem: any,
  setSelectedCustomerPayment: any,
  recordManager: any,
  isRevert: boolean,
  setRevert: any,
  isAuditTeam: boolean,
) {
  const { id, state, payments, payment_method, bank_id } = selectedItem;

  const subPaymentPending = payments?.filter(
    (op) => op.state === paymentStateOptions.pending,
  );
  const subPaymentReview = payments?.filter(
    (op) => op.state === paymentStateOptions.reviewed,
  );
  const subPaymentFinalReview = payments?.filter(
    (op) => op.state === paymentStateOptions.final_reviewed,
  );

  if (
    !isRevert &&
    bankApprovalRequiredMethods.includes(payment_method) &&
    state === paymentStateOptions.bank_approve
  ) {
    if (!bank_id) {
      toast.error('Select Bank First, Add bank details and update record.');
      return;
    }

    const bankDetails = selectedItem.customer_transaction_bank_details || [];
    const hasAttachments = bankDetails.some((item) => item?.attachment);

    if (hasAttachments) {
      const hasMissingDepositDate = bankDetails.some(
        (item) => item?.attachment && !item.deposit_date,
      );

      if (hasMissingDepositDate) {
        toast.error('Deposit date is required for attached bank details.');
        return;
      }
    } else {
      return toast.error(
        'Bank approval should only be done after the payment slip or receipt has been attached.',
      );
    }

    const isValidBankDetails =
      bankDetails.length > 0 &&
      bankDetails.every(
        (item) => item.title?.trim() && item.reference_number?.trim(),
      );

    if (!isValidBankDetails) {
      toast.error('Bank details is required.');
      return;
    }
  }

  if (
    !isRevert &&
    subPaymentPending.length > 0 &&
    state === paymentStateOptions.pending
  )
    return toast.error('Please make sure all the payments are Reviewed');

  if (
    !isRevert &&
    subPaymentReview.length > 0 &&
    state === paymentStateOptions.reviewed
  ) {
    return toast.error('Please make sure all the payments are Final Reviewed');
  }

  if (
    !isRevert &&
    subPaymentFinalReview.length > 0 &&
    state === paymentStateOptions.final_reviewed
  ) {
    return toast.error('Please make sure all the payments are Approved');
  }

  // if (
  //   !isRevert &&
  //   state === paymentStateOptions.bank_approve &&
  //   (!attachments || attachments.length === 0)
  // ) {
  //   return toast.error(
  //     'Bank approval should only be done after the payment slip or receipt has been attached.',
  //   );
  // }

  // if (state === paymentStateOptions.bank_approve) {
  //   setShowDepositDate(false);
  //   form.setValue('deposit_date', null);
  // }

  let endpoint = 'customerPaymentStateToReview';
  let updateState = paymentStateOptions.pending;
  if (!isRevert) {
    if (state === paymentStateOptions.pending) {
      endpoint = 'customerPaymentStateToReview';
      updateState = paymentStateOptions.reviewed;
    } else if (state === paymentStateOptions.reviewed) {
      endpoint = 'customerPaymentStateToReview';
      updateState = paymentStateOptions.final_reviewed;
    } else if (state === paymentStateOptions.final_reviewed) {
      endpoint = 'customerPaymentStateToApprove';
      updateState = paymentStateOptions.approved;
    }
  }
  if (isRevert) {
    if (isAuditTeam) {
      endpoint = 'customerPaymentStateToApprove';
    }
    if (state === paymentStateOptions.pending) {
      updateState = paymentStateOptions.bank_approve;
    }
    if (state === paymentStateOptions.approved) {
      updateState = paymentStateOptions.final_reviewed;
    } else if (state === paymentStateOptions.final_reviewed) {
      updateState = paymentStateOptions.reviewed;
    } else if (state === paymentStateOptions.reviewed) {
      updateState = paymentStateOptions.pending;
    }
  }
  try {
    await axios
      .patch(`/customer-payment-transaction/${endpoint}`, {
        id,
        state: updateState,
        isRevert,
      })
      .then((_res) => {})
      .catch((error) => toast.error(error));

    await axios
      .get(`customer-payment-transaction/${id}`)
      .then((res) => {
        if (res.data.result) {
          toast.success('Customer Payment Updated Successfully');
          setSelectedCustomerPayment(res.data.data);
          recordManager(res.data.data, 'update');
        } else {
          toast.error('Something went wrong with the Customer payment update');
        }
      })
      .catch((error) => toast.error(error));
    setRevert(false);
  } catch (error) {
    toast.error(
      error.message || 'An error occurred while updating the payment',
    );
  }
}

export function paymentTypePermit(value: string, userPermissions: any) {
  if (value === 'clearance')
    return userPermissions.includes(PAYMENT_TYPES.CLEARANCE);
  if (value === 'mix') return userPermissions.includes(PAYMENT_TYPES.MIX);
  if (value === 'shipment')
    return userPermissions.includes(PAYMENT_TYPES.SHIPMENT);
  if (value === 'clear_log')
    return userPermissions.includes(PAYMENT_TYPES.CLEAR_LOG);
  if (value === 'single_vcc')
    return userPermissions.includes(PAYMENT_TYPES.SINGLE_VCC);
  if (value === 'delivery_charge')
    return userPermissions.includes(PAYMENT_TYPES.DELIVERY_CHARGE);
  if (value === 'exit_claim_charge')
    return userPermissions.includes(PAYMENT_TYPES.EXIT_CLAIM_CHARGE);
  if (value === 'detention_charge')
    return userPermissions.includes(PAYMENT_TYPES.DETENTION_CHARGE);
  if (value === 'auction')
    return userPermissions.includes(PAYMENT_TYPES.AUCTION);
  if (value === 'cash') return userPermissions.includes(PAYMENT_TYPES.CASH);
  else true;
}

const typeMapping: Record<string, string> = {
  shipment: 'shipment_invoice_id',
  single_vcc: 'single_vcc_id',
  clearance: 'clearance_invoice_id',
  clear_log: 'log_invoice_id',
  delivery_charge: 'delivery_charge_invoice_id',
  exit_claim_charge: 'exit_claim_charge_id',
  detention_charge: 'detention_charge_id',
  mix: 'mix_shipping_vehicle_id',
  auction: 'vehicle_id',
};
export function filterInvoiceIds(
  updatedPayments: any,
  ids: number[],
  type: string,
) {
  const invoiceField = typeMapping[type];
  if (invoiceField) {
    ids = ids.filter((id) => {
      return !updatedPayments?.some((card) =>
        card?.some(
          (payment) => +payment[invoiceField] === +id && payment.type === type,
        ),
      );
    });
  }

  return ids;
}

export async function fetchExistingInvoicesRemoved(
  updatedPayments: any,
  ids: number[],
  type: string,
) {
  const removedInvoices = [];

  const invoiceField = typeMapping[type];

  if (invoiceField) {
    updatedPayments.forEach((card) => {
      card?.forEach((payment) => {
        if (!ids.includes(payment[invoiceField]) && payment.type === type) {
          removedInvoices.push({
            paymentId: payment.id,
          });
        }
      });
    });
  }
  return removedInvoices;
}

export async function handlePaymentInvoiceChange(
  txn_id: number,
  cardId: string,
  ids: number[],
  type: string,
  updatedPayments: any,
  setUpdatedPayments: any,
  limits: any,
  sums: any,
  handleSumChange: any,
  totalAmount: number,
  customerAmountApplied: number,
  setCustomerAmountApplied: any,
  exchangeRate: number,
  cardExchangeRate: any,
  shortAmounts: any,
  setInvoiceLoader: any,
  payment_date: string,
  _e: any,
  fieldOnChangeCallback: (updatedValue: any) => void,
  customerAmountAppliedRef: any,
) {
  const filteredIds = filterInvoiceIds(updatedPayments, ids, type);
  const existingRemovedIds = await fetchExistingInvoicesRemoved(
    updatedPayments,
    ids,
    type,
  );
  if (existingRemovedIds.length > 0) {
    let previousCardSum: number = sums[`sum_${cardId}`];
    let totalSum: number = customerAmountApplied;
    for (const element of existingRemovedIds.slice().reverse()) {
      const result = await handleRemovePayment(
        type,
        element.paymentId,
        previousCardSum,
        setUpdatedPayments,
        updatedPayments,
        totalSum,
        exchangeRate,
      );
      previousCardSum = result.previousCardSum;
      totalSum = result.totalSum;
    }

    setCustomerAmountApplied(fixNumberTwoDigit(totalSum));

    await new Promise<void>((resolve) => {
      const checkState = () => {
        if (customerAmountAppliedRef.current === fixNumberTwoDigit(totalSum)) {
          resolve();
        } else {
          setTimeout(checkState, 100);
        }
      };
      checkState();
    });

    handleSumChange(`sum_${cardId}`, fixNumberTwoDigit(previousCardSum));
    setInvoiceLoader(false);
    fieldOnChangeCallback(_e);
  }
  try {
    if (filteredIds.length > 0) {
      const { data } = await axios.get(
        '/customer-payment-transaction/fetchInvoice',
        {
          params: {
            ids: filteredIds,
            type,
          },
        },
      );
      await generatePayments(
        txn_id,
        cardId,
        data.data,
        type,
        setUpdatedPayments,
        limits,
        sums,
        handleSumChange,
        totalAmount,
        customerAmountApplied,
        setCustomerAmountApplied,
        exchangeRate,
        cardExchangeRate,
        shortAmounts,
        setInvoiceLoader,
        payment_date,
        _e,
        fieldOnChangeCallback,
        customerAmountAppliedRef,
      );
    }
    setInvoiceLoader(false);
  } catch (error) {
    toast.error(error);
  }
}
export async function handlePaymentCashChange(
  cardId: string,
  cardType: string,
  cashAmount: number,
  cashTransactionNumber: string,
  setUpdatedPayments: any,
  limits: any,
  sums: any,
  handleSumChange: any,
  totalAmount: number,
  customerAmountApplied: number,
  setCustomerAmountApplied: any,
  exchangeRate: number,
  cardExchangeRate: any,
  shortAmounts: any,
  setInvoiceLoader: any,
  payment_date: string,
  fieldOnChangeCallback: (updatedValue: any) => void,
  customerAmountAppliedRef: any,
) {
  let currentCardSum: number = sums[`sum_${cardId}`];
  const sumsTypesCash = {
    cash: 'cash',
  };
  const sumsForCash = {
    cash: 0,
  };
  if (cashAmount > 0) {
    sumsForCash.cash = Number(cashAmount);
  }

  const { allocations, cardSumAmount } = await allocationHelper(
    cardId,
    [],
    sumsForCash,
    sumsTypesCash,
    limits,
    currentCardSum,
    totalAmount,
    customerAmountApplied,
    setCustomerAmountApplied,
    exchangeRate,
    cardExchangeRate,
    shortAmounts,
    customerAmountAppliedRef,
  );

  let newPayment: any = {
    id: nanoid(),
    type: 'cash',
    exchange_rate: exchangeRate,
    payment_allocations: allocations,
    payment_remark: '',
    state: paymentStateOptions.pending,
    payment_date,
    transaction_number: cashTransactionNumber,
  };
  newPayment.amount_applied = getAmountApplied(newPayment?.payment_allocations);

  await generatePaymentsHelper(
    cardId,
    cardType,
    newPayment,
    setUpdatedPayments,
    setInvoiceLoader,
    0,
    fieldOnChangeCallback,
  );
  handleSumChange(`sum_${cardId}`, fixNumberTwoDigit(cardSumAmount));
}
export async function handlePaymentCashRemove(
  cardId: string,
  cardType: string,
  sums: any,
  customerAmountApplied: number,
  setCustomerAmountApplied: any,
  paymentId: number | string,
  updatedPayments: any,
  setUpdatedPayments: any,
  exchangeRate: number,
  customerAmountAppliedRef: any,
  handleSumChange: any,
) {
  let previousCardSum: number = sums[`sum_${cardId}`];
  let totalSum: number = customerAmountApplied;
  const result = await handleRemovePayment(
    cardType,
    paymentId,
    previousCardSum,
    setUpdatedPayments,
    updatedPayments,
    totalSum,
    exchangeRate,
  );
  previousCardSum = result.previousCardSum;
  totalSum = result.totalSum;

  setCustomerAmountApplied(fixNumberTwoDigit(totalSum));

  await new Promise<void>((resolve) => {
    const checkState = () => {
      if (customerAmountAppliedRef.current === fixNumberTwoDigit(totalSum)) {
        resolve();
      } else {
        setTimeout(checkState, 100);
      }
    };
    checkState();
  });

  handleSumChange(`sum_${cardId}`, fixNumberTwoDigit(previousCardSum));
}
export const getExchangeRate = async (id, currency) => {
  try {
    const { data } = await axios.get(
      `exchange_rates/company-rate?company_id=${id}&currency=${currency}`,
    );
    return data?.rate ?? 1;
  } catch (error) {
    console.error(error);
    return 1;
  }
};

export const generatePayments = async (
  txn_id: number,
  cardId: string,
  invoices: any,
  type: string,
  setUpdatedPayments: any,
  limits: any,
  sums: any,
  handleSumChange: any,
  totalAmount: number,
  customerAmountApplied: number,
  setCustomerAmountApplied: any,
  exchangeRate: number,
  cardExchangeRate: any,
  shortAmounts: any,
  setInvoiceLoader: any,
  payment_date: string,
  _e: any,
  fieldOnChangeCallback: (updatedValue: any) => void,
  customerAmountAppliedRef: any,
) => {
  let currentCustomerAmountApplied: number = customerAmountApplied;
  let currentCardSum: number = sums[`sum_${cardId}`];
  if (type == 'mix') {
    for (const vehicle of invoices) {
      const {
        amtCharge,
        amtReturn,
        paidAmt,
        paidReturn,
        invoiceNumberReturn,
        previousAllocationsReturn,
        showDiscount,
        discountReturn,
        paymentRemarkReturn,
      } = await getInvoiceInfo(
        txn_id,
        type,
        vehicle,
        vehicle?.mix_shipping_invoices,
      );
      const { allocations, cardSumAmount, totalSumAmount } =
        await generateAllocations(
          cardId,
          vehicle,
          type,
          vehicle?.payments,
          limits,
          currentCardSum,
          totalAmount,
          currentCustomerAmountApplied,
          setCustomerAmountApplied,
          exchangeRate,
          cardExchangeRate,
          shortAmounts,
          customerAmountAppliedRef,
          vehicle?.mix_shipping_invoices?.type,
        );
      currentCustomerAmountApplied = totalSumAmount;
      currentCardSum = cardSumAmount;
      let newPayment: any = {
        id: nanoid(),
        invoice_number: invoiceNumberReturn,
        invoice_amt: amtCharge,
        invoice_amt_show: amtReturn,
        invoice_discount: showDiscount ? discountReturn : 0,
        invoice_paid: paidAmt,
        invoice_paid_show: paidReturn,
        invoice_previous_allocations: previousAllocationsReturn,
        type,
        exchange_rate: cardExchangeRate[`exchange_rate_${cardId}`],
        mix_shipping_vehicle_id: vehicle.id,
        payment_allocations: allocations,
        payment_remark: paymentRemarkReturn,
        state: paymentStateOptions.pending,
        payment_date,
      };
      newPayment.amount_applied = getAmountApplied(
        newPayment?.payment_allocations,
      );
      await generatePaymentsHelper(
        cardId,
        type,
        newPayment,
        setUpdatedPayments,
        setInvoiceLoader,
        _e,
        fieldOnChangeCallback,
      );
    }
  } else {
    for (const invoice of invoices) {
      const {
        amtCharge,
        amtReturn,
        paidAmt,
        paidReturn,
        invoiceNumberReturn,
        previousAllocationsReturn,
        showDiscount,
        discountReturn,
        paymentRemarkReturn,
        auctionName,
      } = await getInvoiceInfo(txn_id, type, invoice);
      const { allocations, cardSumAmount, totalSumAmount } =
        await generateAllocations(
          cardId,
          invoice,
          type,
          invoice?.payments,
          limits,
          currentCardSum,
          totalAmount,
          currentCustomerAmountApplied,
          setCustomerAmountApplied,
          exchangeRate,
          cardExchangeRate,
          shortAmounts,
          customerAmountAppliedRef,
          invoice?.mix_shipping_invoices?.type, //this was't added
        );
      currentCustomerAmountApplied = totalSumAmount;
      currentCardSum = cardSumAmount;
      let newPayment: any = {
        id: nanoid(),
        invoice_number: invoiceNumberReturn,
        invoice_amt: amtCharge,
        invoice_amt_show: amtReturn,
        invoice_discount: showDiscount ? discountReturn : 0,
        invoice_paid: paidAmt,
        invoice_paid_show: paidReturn,
        invoice_previous_allocations: previousAllocationsReturn,
        type,
        exchange_rate: cardExchangeRate[`exchange_rate_${cardId}`],
        [paymentNameOptions[type]]: invoice.id,
        payment_allocations: allocations,
        payment_remark: paymentRemarkReturn,
        state: paymentStateOptions.pending,
        payment_date,
      };
      if (type === 'auction') {
        const auction = auctionName?.toUpperCase();
        const auctionCategories: Record<string, number> = {
          COPART: 9,
          IAAI: 3,
          LOCAL: 124,
          MENHEIM: 25,
        };
        newPayment.auction_category_id = auctionCategories[auction] ?? null;
      }

      newPayment.amount_applied =
        getAmountApplied(newPayment?.payment_allocations) - invoice?.discount;
      await generatePaymentsHelper(
        cardId,
        type,
        newPayment,
        setUpdatedPayments,
        setInvoiceLoader,
        _e,
        fieldOnChangeCallback,
      );
    }
  }
  handleSumChange(`sum_${cardId}`, fixNumberTwoDigit(currentCardSum));
};

const generatePaymentsHelper = async (
  cardId: string,
  cardType: string,
  newPayment: any,
  setUpdatedPayments: any,
  setInvoiceLoader: any,
  _e: any,
  fieldOnChangeCallback: (updatedValue: any) => void,
) => {
  if (
    !newPayment?.payment_allocations ||
    newPayment.payment_allocations.length === 0
  ) {
    islimitHasReached(cardId, 0, 0, true, 'payment');
    setInvoiceLoader(false);
    return;
  } else {
    handleAddPayment(
      cardType,
      setUpdatedPayments,
      newPayment,
      setInvoiceLoader,
    );
    fieldOnChangeCallback(_e);
  }
};

const generateAllocations = async (
  cardId: string,
  invoice: any,
  type: string,
  payments: any,
  limits: any,
  currentSum: any,
  totalAmount: number,
  currentCustomerAmountApplied: number,
  setCustomerAmountApplied: any,
  exchangeRate: number,
  cardExchangeRate: any,
  shortAmounts: any,
  customerAmountAppliedRef: any,
  mixInvoiceType: string = '',
) => {
  let allocations: any = {};

  if (type == 'shipment') {
    const { towing_charge_visible, title_charge_visible, containers } = invoice;
    const sumsForShipment = {
      ship_cost: 0,
      title_charge: 0,
      towing_cost: 0,
      pgl_storage_costs: 0,
      dismantal_cost: 0,
      other_cost: 0,
    };
    if (containers && containers.vehicles) {
      for (const vehicle of containers.vehicles) {
        const costs = vehicle.vehicle_costs || {};
        sumsForShipment.ship_cost += costs.ship_cost || 0;
        sumsForShipment.title_charge += title_charge_visible
          ? costs.title_charge || 0
          : 0;
        sumsForShipment.towing_cost += towing_charge_visible
          ? costs.towing_cost || 0
          : 0;
        sumsForShipment.pgl_storage_costs += costs.pgl_storage_costs || 0;
        sumsForShipment.dismantal_cost += costs.dismantal_cost || 0;
        sumsForShipment.other_cost += costs.other_cost || 0;
      }
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForShipment,
      sumTypes,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'single_vcc') {
    const sumTypesVcc = {
      charge: 'vcc',
    };
    const sumsForVcc = {
      vcc: 0,
    };
    if (Number(invoice.vcc) > 0) {
      sumsForVcc.vcc = Number(invoice.vcc);
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForVcc,
      sumTypesVcc,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'clearance') {
    const sumTypesCombine = {
      charge: 'invoice_amount',
    };
    const sumsForCombine = {
      invoice_amount: 0,
    };
    if (Number(invoice.invoice_amount) > 0) {
      sumsForCombine.invoice_amount = Number(invoice.invoice_amount);
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForCombine,
      sumTypesCombine,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'clear_log') {
    const totalLogInvoice = await getLogInvoiceAmount(invoice);
    const sumTypesLog = {
      charge: 'invoice_amount',
    };
    const sumsForLog = {
      invoice_amount: 0,
    };
    if (totalLogInvoice > 0) {
      sumsForLog.invoice_amount = totalLogInvoice;
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForLog,
      sumTypesLog,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'delivery_charge') {
    const totalDeliveryCharge = parseFloat(invoice.delivery_charges);
    const sumTypesDelivery = {
      charge: 'delivery_charges',
    };
    const sumsForDelivery = {
      delivery_charges: 0,
    };
    if (totalDeliveryCharge > 0) {
      sumsForDelivery.delivery_charges = Number(invoice.delivery_charges);
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForDelivery,
      sumTypesDelivery,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'exit_claim_charge') {
    const totalExitClaim = Number(invoice.exit_charges - invoice.discount);
    const sumTypesExitClaim = {
      charge: 'exit_claim_charge',
    };
    const sumsForExitClaim = {
      exit_claim_charge: 0,
    };
    if (totalExitClaim > 0) {
      sumsForExitClaim.exit_claim_charge = Number(invoice.exit_charges);
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForExitClaim,
      sumTypesExitClaim,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'detention_charge') {
    const totalDetentionCharge = Number(
      invoice.detention_charges - invoice.discount,
    );
    const sumTypesDetention = {
      charge: 'detention_charge',
    };
    const sumsForDetention = {
      detention_charge: 0,
    };
    if (totalDetentionCharge > 0) {
      sumsForDetention.detention_charge = Number(invoice.detention_charges);
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForDetention,
      sumTypesDetention,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }

  if (type == 'mix') {
    if (mixInvoiceType == 'mix') {
      const sumsForMix = {
        tow_amount: 0,
        freight: 0,
        clearance: 0,
        vat_and_custom: 0,
        attestation_fee: 0,
        inspection_charges: 0,
        title_charges: 0,
        auction_storage: 0,
        sharjah_yard_storage: 0,
        fed_ex_or_mailing_fee: 0,
        recovery_fee: 0,
        custom_hold: 0,
        relist_fee: 0,
        detention_charges: 0,
        shortage: 0,
        suv_charges: 0,
        tds_charges: 0,
        registration_fee: 0,
        transportation_fee: 0,
        office_fee_and_bank: 0,
        empty_containers: 0,
        other_charges: 0,
        coo_charges: 0,
        hybrid_charges: 0,
      };

      sumsForMix.clearance = invoice.clearance || 0;
      sumsForMix.freight = invoice.freight || 0;
      sumsForMix.tow_amount = invoice.tow_amount || 0;
      sumsForMix.vat_and_custom = invoice.vat_and_custom || 0;
      invoice.mix_shipping_vehicle_charges.forEach((charge) => {
        sumsForMix[`${charge.name}`] = charge.value;
      });
      allocations = await allocationHelper(
        cardId,
        payments,
        sumsForMix,
        sumTypesMix,
        limits,
        currentSum,
        totalAmount,
        currentCustomerAmountApplied,
        setCustomerAmountApplied,
        exchangeRate,
        cardExchangeRate,
        shortAmounts,
        customerAmountAppliedRef,
      );
    } else {
      const sumsForMixShipment = {
        ship_cost: 0,
        title_charge: 0,
        towing_cost: 0,
        dismantal_cost: 0,
        other_cost: 0,
        pgl_storage_costs: 0,
      };
      if (invoice.vehicles) {
        const costs = invoice?.vehicles?.vehicle_costs || {};
        sumsForMixShipment.dismantal_cost += costs.dismantal_cost || 0;
        sumsForMixShipment.other_cost += costs.other_cost || 0;
        sumsForMixShipment.ship_cost += costs.ship_cost || 0;
        sumsForMixShipment.title_charge += costs.title_charge || 0;
        sumsForMixShipment.towing_cost += costs.towing_cost || 0;
        sumsForMixShipment.pgl_storage_costs += costs.pgl_storage_costs || 0;
      }
      allocations = await allocationHelper(
        cardId,
        payments,
        sumsForMixShipment,
        sumTypes,
        limits,
        currentSum,
        totalAmount,
        currentCustomerAmountApplied,
        setCustomerAmountApplied,
        exchangeRate,
        cardExchangeRate,
        shortAmounts,
        customerAmountAppliedRef,
      );
    }
  }

  if (type == 'auction') {
    // const totalAuctionPrice = Number(invoice.vehicle_costs.vehicle_price);
    const totalAuctionPrice = Number(invoice.price);
    const sumsTypesAuction = {
      vehicle_price: 'auction',
    };
    const sumsForAuction = {
      auction: 0,
    };
    if (totalAuctionPrice > 0) {
      sumsForAuction.auction = totalAuctionPrice;
    }
    allocations = await allocationHelper(
      cardId,
      payments,
      sumsForAuction,
      sumsTypesAuction,
      limits,
      currentSum,
      totalAmount,
      currentCustomerAmountApplied,
      setCustomerAmountApplied,
      exchangeRate,
      cardExchangeRate,
      shortAmounts,
      customerAmountAppliedRef,
    );
  }
  return allocations;
};

const allocationHelper = async (
  cardId: string,
  payments: any,
  invoiceCharge: any,
  chargeTypes: any,
  limits: any,
  currentSum: any,
  totalAmount: number,
  currentCustomerAmountApplied: number,
  setCustomerAmountApplied: any,
  exchangeRate: number,
  cardExchangeRate: any,
  shortAmounts: any,
  customerAmountAppliedRef: any,
) => {
  const cExchangeRate = cardExchangeRate[`exchange_rate_${cardId}`];
  const applicableAmount = fixNumberTwoDigit(
    (totalAmount / exchangeRate) * cExchangeRate,
  );
  let totalSumAmount: number = fixNumberTwoDigit(currentCustomerAmountApplied);
  let cardSumAmount: number = currentSum || 0;
  let allocations = [];
  for (const payment of payments) {
    for (const allocation of payment.payment_allocations) {
      const sumType = chargeTypes[allocation.type];
      if (sumType) {
        invoiceCharge[sumType] -= +allocation.amount || 0;
      }
    }
  }
  const swappedChargeTypes = Object.fromEntries(
    Object.entries(chargeTypes).map(([key, value]) => [value, key]),
  );

  for (const [key] of Object.entries(invoiceCharge)) {
    const remainingSum = invoiceCharge[key];
    if (remainingSum > 0) {
      let allocationAmount = remainingSum;
      const currentTotalSum: number =
        totalSumAmount > 0
          ? fixNumberTwoDigit((totalSumAmount / exchangeRate) * cExchangeRate)
          : 0.0;
      const currentSum: number = cardSumAmount;

      if (currentTotalSum + allocationAmount > applicableAmount) {
        allocationAmount = fixNumberTwoDigit(
          applicableAmount - currentTotalSum,
        );
      }
      if (limits[`limit_${cardId}`] > 0) {
        if (currentSum + allocationAmount > limits[`limit_${cardId}`]) {
          allocationAmount = limits[`limit_${cardId}`] - currentSum;
        }
      }
      if (shortAmounts[`short_amount_${cardId}`] > 0) {
        allocationAmount -= shortAmounts[`short_amount_${cardId}`];
      }
      if (Math.floor(allocationAmount) > 0) {
        allocations.push({
          id: nanoid(),
          type: swappedChargeTypes[key],
          amount: fixNumberTwoDigit(allocationAmount),
        });
        totalSumAmount += (allocationAmount / cExchangeRate) * exchangeRate;
        totalSumAmount = fixNumberTwoDigit(totalSumAmount);
        cardSumAmount += fixNumberTwoDigit(allocationAmount);
      }
    }
  }
  setCustomerAmountApplied(totalSumAmount);

  await new Promise<void>((resolve) => {
    const checkState = () => {
      if (Math.abs(customerAmountAppliedRef.current - totalSumAmount) < 0.01) {
        resolve();
      } else {
        setTimeout(checkState, 100);
      }
    };
    checkState();
  });

  return { allocations, cardSumAmount, totalSumAmount };
};

export const fixNumberTwoDigit = (number: number): number => {
  return parseFloat(number.toFixed(3));
};

export const counterAmountRoundUp = (number: number): number => {
  if (
    (fixNumberTwoDigit(number) >= -0.005 &&
      fixNumberTwoDigit(number) <= -0.001) ||
    (fixNumberTwoDigit(number) >= 0.001 && fixNumberTwoDigit(number) <= 0.005)
  ) {
    return 0.0;
  } else {
    return number;
  }
};

export function islimitHasReached(
  cardId: string | null,
  limits: any,
  sumAmount: any,
  doesNotHaveCharges: boolean = false,
  type: string,
) {
  if (
    (type === 'grand_limit' && sumAmount >= limits) ||
    (type === 'grand_limit_allocations' && sumAmount > limits)
  ) {
    toast.error('No amount remaining!.');
    return true;
  }
  if (limits[`limit_${cardId}`] || doesNotHaveCharges) {
    if (
      type === 'payment' &&
      (sumAmount >= limits[`limit_${cardId}`] || doesNotHaveCharges)
    ) {
      toast.error(
        'Either the Limit has been reached or the selected invoice does not have any charges due.',
      );
      return true;
    }

    if (type === 'allocation' && sumAmount > limits[`limit_${cardId}`]) {
      toast.error('The Limit has been reached.');
      return true;
    }
  }
  return false;
}

export async function getMixVehicleInvoiceAmount(
  mixVehicle: any,
  type: string,
) {
  let totalInvoiceAmount: any = 0;
  if (type == 'full') {
    totalInvoiceAmount = getInvoiceTotal(mixVehicle?.vehicles);
  } else {
    const chargesFromTypes = mixInvoiceCharges?.reduce(
      (chargeTotal, chargeType) => {
        const charge = mixVehicle?.[chargeType] ?? 0;
        return chargeTotal + parseFloat(charge);
      },
      0,
    );
    const chargesFromVehicle = mixVehicle?.mix_shipping_vehicle_charges?.reduce(
      (totalVehicleCharges, charge) => {
        if (charge.deleted_at) {
          return totalVehicleCharges;
        }
        return totalVehicleCharges + charge.value;
      },
      0,
    );

    totalInvoiceAmount = chargesFromTypes + chargesFromVehicle;
  }
  return parseFloat(totalInvoiceAmount).toFixed(3);
}

export function getInvoiceTotal(items: any) {
  let total = 0;
  if (items) {
    let sTow = items?.vehicle_costs?.towing_cost || 0;
    let sDismantle = items?.vehicle_costs?.dismantal_cost || 0;
    let sShipping = items?.vehicle_costs?.ship_cost || 0;
    let sStorage = items?.vehicle_costs?.title_charge || 0;
    let sOther = items?.vehicle_costs?.other_cost || 0;
    let sStorage_fee = items?.vehicle_costs?.pgl_storage_costs || 0;
    total +=
      +sTow + +sDismantle + +sShipping + +sStorage + +sOther + +sStorage_fee;
  }
  return total;
}

export async function getInvoiceInfo(
  txn_id: number,
  type: string,
  invoice: any,
  mixInvoice: any = null,
  isFirstVehicle: boolean = true,
) {
  let amtCharge: number = 0;
  let amtReturn: string = '';
  let paidReturn: string = '';
  let invoiceNumberReturn: any = '';
  let discountReturn: string = '';
  let auctionName: string = '';
  let showDiscount: boolean = false;
  let paymentRemarkReturn: string = '';
  const previousAllocationsReturn = getInvoicePreviousAllocations(
    invoice?.payments,
  );
  const paidAmt: number =
    type === 'cash' ? 1 : await getInvoicePayments(txn_id, invoice?.payments);
  switch (type) {
    case 'shipment':
      let invoiceAmt = 0;
      for (const vehicle of invoice.containers?.vehicles) {
        invoiceAmt += countVehicleCosts(
          {
            ...vehicle?.vehicle_costs,
            vehicle_charges: vehicle?.vehicle_charges,
          },
          invoice.title_charge_visible,
          invoice.towing_charge_visible,
        );
      }
      showDiscount = invoice?.discount > 0;
      // invoiceNumberReturn = getInvoiceNumberChip(invoice.invoice_number);
      invoiceNumberReturn = getInvoiceNumberChip(
        `${invoice.invoice_number} | ${invoice?.containers?.container_number}`,
      );
      amtCharge = invoiceAmt;
      amtReturn = `Amt: $${invoiceAmt}`;
      discountReturn = `Dis: $${showDiscount ? invoice.discount : 0}`;
      paidReturn = `Paid: $${paidAmt}`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'single_vcc':
      showDiscount = invoice?.discount > 0;
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLV${invoice?.id.toString()} | ${(invoice?.clear_logs?.container_number || invoice?.clear_logs?.containers?.container_number) ?? ''}`,
      );
      amtCharge = invoice.vcc;
      amtReturn = `Amt: ${invoice.vcc} AED`;
      discountReturn = `Dis: ${showDiscount ? invoice.discount : 0} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'clearance':
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLV${invoice?.id.toString()} | ${invoice?.containers?.container_number ?? ''}`,
      );
      amtCharge = invoice.invoice_amount;
      amtReturn = `Amt: ${invoice.invoice_amount} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'clear_log':
      showDiscount = invoice?.discount > 0;
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLC${invoice?.id.toString()} | ${(invoice?.clear_logs?.container_number || invoice?.clear_logs?.containers?.container_number) ?? ''}`,
      );
      amtCharge = await getLogInvoiceAmount(invoice);
      amtReturn = `Amt: ${amtCharge} AED`;
      discountReturn = `Dis: ${showDiscount ? invoice.discount : 0} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'delivery_charge':
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLDO${invoice?.id.toString()}`,
      );
      amtCharge = parseFloat(invoice.delivery_charges);
      amtReturn = `Amt: ${parseFloat(invoice.delivery_charges)} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'exit_claim_charge':
      showDiscount = invoice?.discount > 0;
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLE${invoice?.id.toString()} | ${(invoice?.clear_logs?.container_number || invoice?.clear_logs?.containers?.container_number) ?? ''}`,
      );
      amtCharge = invoice.exit_charges;
      amtReturn = `Amt: ${invoice.exit_charges} AED`;
      discountReturn = `Dis: ${showDiscount ? invoice.discount : 0} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'detention_charge':
      invoiceNumberReturn = getInvoiceNumberChip(
        `PGLD${invoice?.id.toString()} | ${(invoice?.clear_logs?.container_number || invoice?.clear_logs?.containers?.container_number) ?? ''}`,
      );
      showDiscount = invoice?.discount > 0;
      amtCharge = invoice.detention_charges;
      amtReturn = `Amt: ${invoice.detention_charges} AED`;
      discountReturn = `Dis: ${showDiscount ? invoice.discount : 0} AED`;
      paidReturn = `Paid: ${paidAmt} AED`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'mix':
      const invoiceAmtMix = await getMixVehicleInvoiceAmount(
        invoice,
        mixInvoice?.type,
      );
      const discount =
        (invoice?.discount || 0) + (invoice?.irrecoverable_debt || 0);
      showDiscount = discount > 0;
      invoiceNumberReturn = isFirstVehicle ? (
        <Box display={'flex'} flexDirection={'column'}>
          {getInvoiceNumberChip(`PGLMS${mixInvoice?.id.toString()}`)}
          {`Vin: ${invoice?.vehicles.vin} | lot#: ${invoice?.vehicles.lot_number}`}
        </Box>
      ) : (
        `Vin: ${invoice?.vehicles.vin} | lot#: ${invoice?.vehicles.lot_number}`
      );
      amtCharge = parseFloat(invoiceAmtMix);
      amtReturn = `Amt: $${invoiceAmtMix}`;
      discountReturn = `Dis: $${showDiscount ? fixNumberTwoDigit(discount / mixInvoice?.exchange_rate) : 0}`;
      paidReturn = `Paid: $${paidAmt}`;
      paymentRemarkReturn = invoice?.description ?? '';
      break;

    case 'auction':
      // amtCharge = parseFloat(invoice?.vehicle_costs?.vehicle_price);
      amtCharge = parseFloat(invoice?.price);
      invoiceNumberReturn = getInvoiceNumberChip(
        `Vin: ${invoice?.vin} | lot#: ${invoice?.lot_number}`,
      );
      amtReturn = `Amt: $${amtCharge}`;
      paidReturn = `Paid: $${paidAmt}`;
      auctionName = invoice?.auction_name;
      break;
    default:
      break;
  }
  return {
    amtCharge,
    amtReturn,
    paidAmt,
    paidReturn,
    invoiceNumberReturn,
    previousAllocationsReturn,
    showDiscount,
    discountReturn,
    paymentRemarkReturn,
    auctionName,
  };
}

const getInvoiceNumberChip = (invoiceNumber: string) => {
  const [vin, lot] = invoiceNumber.split(' | ').map((text) => text.trim());

  return (
    <Chip
      sx={{
        fontSize: '12px',
        borderRadius: 2,
        color: 'white',
        width: '100%',
        lineHeight: 1.2,
        py: 1,
      }}
      size="medium"
      label={
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span>{vin}</span>
          <span>{lot}</span>
        </div>
      }
      color="secondary"
    />
  );
};

const getLogInvoiceAmount = async (logInvoice: any): Promise<number> => {
  return (
    logInvoice.custom_clearing_balance +
    parseFloat(logInvoice.custom_duty) +
    parseFloat(logInvoice.vat) +
    parseFloat(logInvoice.port_handling) +
    parseFloat(logInvoice.vcc) +
    parseFloat(logInvoice.transporter_charges) +
    parseFloat(logInvoice.e_token) +
    parseFloat(logInvoice.local_service_charges) +
    parseFloat(logInvoice.bill_of_entry) +
    parseFloat(logInvoice.handling_fee) +
    parseFloat(logInvoice.demurrage) +
    parseFloat(logInvoice.unloading) +
    parseFloat(logInvoice.consignee_charges) +
    parseFloat(logInvoice.damage_charges) +
    parseFloat(logInvoice.wash_fine_charges) +
    parseFloat(logInvoice.repairing_cost_charges) +
    parseFloat(logInvoice.export_services_fees) +
    parseFloat(logInvoice.detention_charges) +
    parseFloat(logInvoice.port_storage) +
    parseFloat(logInvoice.hazardous_handling_import_charges) +
    parseFloat(logInvoice.terminal_handling_charges) +
    parseFloat(logInvoice.inspection_charges) +
    parseFloat(logInvoice.country_of_origin_charges) +
    (logInvoice.delivery_order_amount
      ? parseFloat(logInvoice.delivery_order_amount)
      : 0) +
    parseFloat(logInvoice.additional_charges) +
    parseFloat(logInvoice.crune_charges) +
    parseFloat(logInvoice.doc_attestation) +
    parseFloat(logInvoice.other_charges) +
    (5 / 100) * parseFloat(logInvoice.local_service_charges) -
    parseFloat(logInvoice.discount)
  );
};

export const mixInvoiceCharges = [
  'clearance',
  'freight',
  'tow_amount',
  'vat_and_custom',
];

const getInvoicePreviousAllocations = (payments: any) => {
  return payments?.flatMap((payment) => payment?.payment_allocations || []);
};

const getInvoicePayments = async (txn_id, payments) => {
  let totalAmountApplied = 0;
  for (const payment of payments) {
    if (payment.customer_payment_transactions?.id !== txn_id) {
      totalAmountApplied += parseFloat(payment.amount_applied);
    }
  }
  return totalAmountApplied;
};

export const getPaymentCurrency = (paymentType: string, cashCurrency: any) => {
  if (
    paymentType == 'shipment' ||
    paymentType == 'mix' ||
    paymentType == 'auction' ||
    paymentType == 'cash'
  ) {
    if (paymentType == 'cash') return cashCurrency;
    return 'USD';
  } else {
    return 'AED';
  }
};
export const totalAmountAllocation = (
  allocations,
  setTotalAllocationsAmount,
) => {
  if (allocations) {
    let total = 0;
    allocations.forEach((element) => {
      total += parseFloat(element.amount);
    });
    setTotalAllocationsAmount(total);
  }
};

export const hasRemaining = (
  totalAmount: number,
  customerAmountApplied: number,
) => {
  return totalAmount - customerAmountApplied > 0;
};

export const isNewlyAddedCard = (cardId: string) => {
  return !cardId.toString().includes('card');
};

export const paymentsDisabledId = (
  updatedPayments: any[],
  paymentsCards: any[],
  cardId: string,
) => {
  const cardIndex = paymentsCards.findIndex((c) => c.id === cardId);
  if (cardIndex === -1) {
    return [];
  }

  const payments = updatedPayments[cardIndex] || [];

  if (!Array.isArray(payments)) {
    return [];
  }

  return payments
    .filter(
      (payment) =>
        payment.state === paymentStateOptions.reviewed ||
        payment.state === paymentStateOptions.approved ||
        payment.state === paymentStateOptions.final_reviewed,
    )
    .map((payment) => payment[paymentNameOptions[payment?.type]]);
};

export const getConfirmBoxDialogTitle = (
  state: string,
  all: boolean = false,
) => {
  let dialogTitle = all ? 'Review All Payments' : 'Review the Payment';

  switch (state) {
    case paymentStateOptions.bank_approve:
      dialogTitle = all
        ? 'Bank Approve All Payments'
        : 'Bank Approve the Payment';
      break;
    case paymentStateOptions.pending:
      dialogTitle = all ? 'Review All Payments' : 'Review the Payment';
      break;
    case paymentStateOptions.reviewed:
      dialogTitle = all
        ? 'Final Review All Payments'
        : 'Final Review the Payment';
      break;
    case paymentStateOptions.final_reviewed:
      dialogTitle = all ? 'Approve All Payments' : 'Approve the Payment';
      break;
    default:
      break;
  }

  return dialogTitle;
};

export const getConfirmBoxTitle = (state: string, all: boolean = false) => {
  let title = all
    ? 'Are You Sure to Review All Payments on this Card?'
    : 'Are You Sure to Review?';

  switch (state) {
    case paymentStateOptions.bank_approve:
      title = all
        ? 'Are You Sure to Bank Approve All Payments on this Card?'
        : 'Are You Sure to Bank Approve?';
      break;
    case paymentStateOptions.pending:
      title = all
        ? 'Are You Sure to Review All Payments on this Card?'
        : 'Are You Sure to Review?';
      break;
    case paymentStateOptions.reviewed:
      title = all
        ? 'Are You Sure to Final Review All Payments on this Card?'
        : 'Are You Sure to Final Review?';
      break;
    case paymentStateOptions.final_reviewed:
      title = all
        ? 'Are You Sure to Approve All Payments on this Card?'
        : 'Are You Sure to Approve?';
      break;
    default:
      break;
  }

  return title;
};

const allocationTypeNotStrgLateFee = (allocationType: string) => {
  return (
    allocationType !== 'auction_storage' &&
    allocationType !== 'late_fee' &&
    allocationType !== 'relist_fee'
  );
};
export const getNormalizedLimits = (
  // to handle the limit in differenct currencies
  cards: any,
  currency: string,
  ex_rate: number,
  limits: any,
  cardExchangeRate: any,
) => {
  const usdCards = ['shipment', 'mix', 'auction'];
  const normalized = { ...limits };
  for (const card of cards) {
    const rate =
      ex_rate !== 1 ? ex_rate : cardExchangeRate[`exchange_rate_${card.id}`];
    const key = `limit_${card.id}`;
    if (currency !== 'USD' && usdCards.includes(card.type) && normalized[key]) {
      normalized[key] = normalized[key] / rate;
    } else if (
      currency === 'USD' &&
      !usdCards.includes(card.type) &&
      normalized[key]
    ) {
      normalized[key] = normalized[key] * rate;
    } else if (
      currency !== 'USD' &&
      currency !== 'AED' &&
      !usdCards.includes(card.type)
    ) {
      normalized[key] =
        (normalized[key] / ex_rate) *
        cardExchangeRate[`exchange_rate_${card.id}`];
    }
  }
  return normalized;
};
export const getAddPaymentButtonTitle = (state: string) => {
  let title = 'Add Payment';

  switch (state) {
    case paymentStateOptions.bank_approve:
      title = 'Can not Allocate Payments until Bank Approved.';
      break;
    case paymentStateOptions.bank_reject:
      title = 'Can not Allocate Payments to Rejected Payments.';
      break;
    default:
      break;
  }

  return title;
};

export const isUpdateDisabled = (selectedItem: any): boolean => {
  if (!selectedItem) return false;

  const {
    amount = 0,
    amount_applied = 0,
    transaction_fee = 0,
    inapplicable_amount = 0,
    state,
    payments = [],
  } = selectedItem ?? {};

  let calculatedAmount =
    amount - amount_applied - transaction_fee - inapplicable_amount;
  calculatedAmount = Math.abs(calculatedAmount) <= 0.009 ? 0 : calculatedAmount;
  const allPaymentsApproved = payments.every(
    (p: any) => p?.state === paymentStateOptions.approved,
  );
  return (
    state === paymentStateOptions.approved &&
    calculatedAmount === 0 &&
    allPaymentsApproved
  );
};

export const hasPermission = (
  type: string,
  userPermissions: any,
  auditTeamFlag: boolean = false,
) => {
  if (auditTeamFlag) {
    return userPermissions?.includes(CUSTOMER_PAYMENTS.APPROVE);
  }
  return userPermissions.includes(PAYMENT_TYPES[type?.toLocaleUpperCase()]);
};
