import React, { useContext, useEffect, useState, useRef } from 'react';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CancelIcon from '@mui/icons-material/Cancel';
import AddIcon from '@mui/icons-material/Add';
import { styled, useTheme } from '@mui/material/styles';
import { Controller, useFieldArray } from 'react-hook-form';
import Tooltip from '@mui/material/Tooltip';
import { toast } from 'react-toastify';

import {
  Grid,
  TextField,
  Typography,
  Box,
  Button,
  Paper,
  Chip,
  Autocomplete as MUIAutoComplete,
  Stack,
} from '@mui/material';

import AppConfirmDialog from '@/components/mainComponents/cComponents/AppConfirmDialog';
import AutoComplete from '@/components/mainComponents/cComponents/AutoComplete';
import { contextProvider } from '@/contexts/ProfileContext';
import axios from '@/lib/axios';
import {
  creditPaymentMethods,
  currencyOptions,
  paymentMethods,
  paymentModalOptions,
  paymentNameOptions,
  paymentStateOptions,
  paymentTypeOptions,
  paymentTypes,
  creditPaymentTypes,
  bankApprovalRequiredMethods,
  restrictedPaymentMethods,
  noneBankPaymentMethods,
  generalPaymentTypes,
  excludedPaymentMethods,
} from '../customerPaymentsComponent/customerPaymentHeader';
import { CUSTOMER_PAYMENTS } from '@/configs/leftSideMenu/Permissions';
import PaymentTable from '../customerPaymentsComponent/PaymentTable';
import {
  handlePaymentInvoiceChange,
  approveCustomerPayment,
  handlePaymentCardChange,
  handleAddCard,
  handleRemoveCard,
  islimitHasReached,
  getInvoiceInfo,
  fixNumberTwoDigit,
  counterAmountRoundUp,
  getExchangeRate,
  hasRemaining,
  isNewlyAddedCard,
  paymentsDisabledId,
  handlePaymentCashChange,
  getConfirmBoxDialogTitle,
  getConfirmBoxTitle,
  getNormalizedLimits,
  hasPermission,
} from '../customerPaymentsComponent/PaymentHelper';
import PaymentAutocomplete from '../customerPaymentsComponent/PaymentAutocomplete';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { removeUnderScore2 } from '@/configs/common';
import { choseColor } from '@/configs/configs';
import { MyDocument } from './PDFView';
import RejectionModal from '../customerPaymentsComponent/RejectionModal';
import { uploadFile } from '../commonActions';
import FileView from '../customerPaymentsComponent/FileView';
import { getInvoiceDownloadUrl } from '../CustomerPayments';

export default function Step1({
  form,
  customerPaymentSelected,
  attachments,
  setAttachments,
  deletedAttachments,
  setDeletedAttachments,
  isUpdate,
  recordManager,
  revert,
  setRevert,
  isCredit,
}) {
  const [customers, setCustomers] = useState([]);
  const [companyId, setCompanyId] = useState();
  const [compErr, setCompErr] = useState(false);
  const [showFile, setShowFile] = useState(false);
  const [selectedFileURL, setSelectedFileURL] = useState('');
  const [commentsFiles, setCommentsFiles] = useState([]);
  const [selectedCustomerPayment, setSelectedCustomerPayment] = useState<any>(
    customerPaymentSelected,
  );
  const [customerAmountApplied, setCustomerAmountApplied] = useState<number>(
    Number(selectedCustomerPayment?.amount_applied) || 0.0,
  );

  const [cashAmount, setCashAmount] = useState<number>(0);
  const [cashTransactionNumber, setCashTransactionNumber] =
    useState<string>('');
  const customerAmountAppliedRef = useRef(customerAmountApplied);
  const [totalAmount, setTotalAmount] = useState<number>(
    Number(
      selectedCustomerPayment?.amount -
        selectedCustomerPayment?.transaction_fee -
        selectedCustomerPayment?.inapplicable_amount,
    ) || 0.0,
  );
  const [confirmBox, setConfirmBox] = useState(false);
  const [customerConfirmBox, setCustomerConfirmBox] = useState(false);
  const [isReject, setIsReject] = useState(false);
  const [renderKeyCus, setRenderKeyCus] = useState(-1);
  const [limits, setLimits] = useState(
    selectedCustomerPayment?.payment_cards?.reduce((acc: any, item: any) => {
      acc[`limit_${item.type}_card`] = item.limit;
      return acc;
    }, {}) || {},
  );
  const [exchangeRate, setExchangeRate] = useState<number>(
    parseFloat(selectedCustomerPayment?.exchange_rate) || 1,
  );
  const [cardExchangeRate, setCardExchangeRate] = useState({});
  const [sums, setSums] = useState({});
  const [transactionNumberDisabled, setTransactionNumberDisabled] =
    useState(false);
  const [shortAmounts, setShortAmounts] = useState({});
  const [updatedPayments, setUpdatedPayments] = useState([]);
  const [currentPayment, setCurrentPayment] = useState<any>(null);
  const [allPaymentCurrentState, setAllPaymentCurrentState] = useState<string>(
    paymentStateOptions.pending,
  );
  const [allPaymentStateUpdateTo, setAllPaymentStateUpdateTo] =
    useState<string>(paymentStateOptions.reviewed);
  const [invoiceLoader, setInvoiceLoader] = useState(false);
  // const [showDepositDate, setShowDepositDate] = useState(
  //   !isCredit && !isUpdate ? true : false,
  // );

  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const userPermissions = profile?.data?.loginable?.permissions?.map(
    (op) => op.name,
  );
  const isAdmin = profile?.data?.department_id === 1;
  const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });

  const handleUploadedFile = (files: FileList) => {
    const myFiles = Array.from(files);
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];

    const notAllowedFile = myFiles.find(
      (file) => !allowedTypes.includes(file.type),
    );
    if (notAllowedFile) {
      return toast.error('Only PDF, PNG, JPG, and JPEG files are allowed!');
    }

    if (attachments?.length > 0) {
      setAttachments((prevAttachments) => [...prevAttachments, ...myFiles]);
      form.setValue('attachments', [...attachments, ...myFiles]);
    } else {
      setAttachments(myFiles);
      form.setValue('attachments', myFiles);
    }
  };
  const uploadCommentsFile = (files: FileList, fieldName: string) => {
    const myFiles = Array.from(files);
    const allowedTypes = [
      'application/pdf',
      'image/png',
      'image/jpg',
      'image/jpeg',
    ];

    const notAllowedFile = myFiles.find(
      (file) => !allowedTypes.includes(file.type),
    );
    if (notAllowedFile) {
      return toast.error('Only PDF, PNG, JPG, and JPEG files are allowed!');
    }
    setCommentsFiles((prev) => ({
      ...prev,
      [fieldName]: myFiles,
    }));
    form.setValue(fieldName, myFiles);
  };

  const removeSingleFile = (name: string, url: string) => {
    if (disabled()) {
      return toast.error(
        'You can not remove attachments when Customer payment is Reviewed.',
      );
    }
    const newFiles = attachments.filter(
      (attachment) => attachment.name != name,
    );
    setAttachments(newFiles);
    setDeletedAttachments((prevState) => [...prevState, url]);
  };

  const hasPayments = () => {
    return (
      selectedCustomerPayment?.payments.length > 0 ||
      updatedPayments.some((paymentArray) => paymentArray?.length > 0)
    );
  };

  const s_array = (data, fieldName) =>
    data &&
    Object?.values(data).map((value: any) => ({
      id: value?.id,
      label: value?.[fieldName] ? value?.[fieldName] : '',
    }));

  const getCustomers = async () => {
    try {
      const res = await axios.get(`customers/companyCustomer/${companyId}`);
      if (res.status === 200) {
        setCustomers(s_array(res.data?.data, 'fullname'));
        setRenderKeyCus((prv) => prv + -1);
      }
    } catch (error) {}
  };

  const handleLimitChange = (name: string, value: number) => {
    const old_val = limits[name];
    const newLimits = {
      ...limits,
      [name]: +value,
    };
    const limit_sum: any =
      Object.values(newLimits).reduce(
        (acc: number, curr: number) => acc + curr,
        0,
      ) || 0;
    const formAmnt =
      form.getValues('amount') - form.getValues('transaction_fee');
    const selectedAmnt =
      Number(selectedCustomerPayment?.amount) -
      Number(selectedCustomerPayment?.transaction_fee);
    const amt = formAmnt || selectedAmnt;
    if (limit_sum > amt) {
      setLimits((prevLimits) => ({
        ...prevLimits,
        [name]: old_val,
      }));
      // return toast.error('Limit must be greated then current applied amount!');
    }
    setLimits(newLimits);
  };

  const handleSumChange = (name: string, value: number) => {
    setSums((prevSums) => ({
      ...prevSums,
      [name]: +value,
    }));
  };

  const handleCardExchangeRateChange = (name: string, value: number) => {
    setCardExchangeRate((prevCardExchangeRate) => ({
      ...prevCardExchangeRate,
      [name]: +value,
    }));
  };

  const handleShortAmountsChange = (name: string, value: number) => {
    setShortAmounts((prevShortAmounts) => ({
      ...prevShortAmounts,
      [name]: +value,
    }));
  };

  const [paymentsCards, setPaymentsCards] = useState(() => {
    let paymentCards = [];

    if (selectedCustomerPayment?.payment_cards) {
      paymentCards = Array.isArray(selectedCustomerPayment.payment_cards)
        ? selectedCustomerPayment.payment_cards
        : [];
    }

    if (paymentCards.length > 0) {
      for (const card of paymentCards) {
        handleLimitChange(`limit_${card.type}_card`, Number(card?.limit));
        handleShortAmountsChange(
          `short_amount_${card.type}_card`,
          Number(card?.short_amount),
        );
        handleCardExchangeRateChange(
          `exchange_rate_${card.type}_card`,
          card?.exchange_rate,
        );
      }
      return paymentCards.map((card) => ({
        id: `${card.type}_card`,
        rec_id: card.id,
        type: card.type,
        limit: Number(card.limit),
        short_amount: Number(card.short_amount),
        exchange_rate: Number(card.exchange_rate),
      }));
    } else if (selectedCustomerPayment?.payments?.length > 0) {
      return [
        ...new Set(
          selectedCustomerPayment.payments.map((payment: any) => payment?.type),
        ),
      ].map((type) => ({
        id: `${type}_card`,
        rec_id: '',
        type,
        limit: 0,
        short_amount: 0,
        exchange_rate: 1,
      }));
    }
    return [];
  });

  const getInvoiceNumberUrl = (values: any) => {
    let url = `autoComplete/paymentInvoices?customer=${
      selectedCustomerPayment?.company_id || form.getValues('company_id')
    }`;
    if (values && values.length > 0) {
      const idsParam = values.join(',');

      url += `&ids=${idsParam}`;
    }
    return url;
  };

  const isCreditApplyPayment = () => {
    if (isCredit && isUpdate) {
      return true;
    }
    return false;
  };
  const getPaymentByType = (type: string) => {
    const item = updatedPayments?.flat().filter((item) => item?.type === type);
    return item?.length ? true : false;
  };

  const disabled = () => {
    return (
      (selectedCustomerPayment &&
        selectedCustomerPayment.state === paymentStateOptions.bank_reject) ||
      (selectedCustomerPayment &&
        selectedCustomerPayment.state === paymentStateOptions.approved) ||
      (selectedCustomerPayment &&
        selectedCustomerPayment.state === paymentStateOptions.final_reviewed) ||
      (selectedCustomerPayment &&
        selectedCustomerPayment.state === paymentStateOptions.reviewed)
    );
  };

  const disabledAfterBankApproved = () => {
    return (
      selectedCustomerPayment &&
      selectedCustomerPayment.state !== paymentStateOptions.bank_approve &&
      bankApprovalRequiredMethods.includes(
        selectedCustomerPayment?.payment_method,
      )
    );
  };

  const getTransactionNumber = async (selectedKey) => {
    const res = await axios.get(
      `/customer-payment-transaction/get-transaction-number/${selectedKey}`,
    );
    form.setValue('transaction_number', res?.data.transaction_number, {
      shouldValidate: true,
    });
    setTransactionNumberDisabled(true);
  };
  const generalPaymentMethods = generalPaymentTypes
    .map((key) => {
      if (
        (!restrictedPaymentMethods.includes(
          selectedCustomerPayment?.payment_method,
        ) &&
          restrictedPaymentMethods.includes(key) &&
          !isAdmin) ||
        excludedPaymentMethods.includes(key)
      ) {
        return null;
      }
      return {
        id: key,
        label: paymentMethods[key],
      };
    })
    .filter(Boolean);

  const paymentMethodOptions = isCredit
    ? Object.keys(creditPaymentTypes).map((key) => ({
        id: key,
        label: paymentMethods[key],
      }))
    : generalPaymentMethods;

  const id = form.watch('company_id');
  useEffect(() => {
    setCompanyId(id);
    if (id != undefined && companyId == id && id != '') {
      form.setValue('customer_id', null);
      getCustomers();
    }
    form.setValue(
      'customer_transaction_bank_details',
      selectedCustomerPayment?.customer_transaction_bank_details,
    );
  }, [id, companyId]);

  useEffect(() => {
    const payments = updatedPayments.flatMap((cardPayments) => {
      return cardPayments?.map((payment) => {
        const {
          invoice_amt,
          invoice_amt_show,
          invoice_discount,
          invoice_paid,
          invoice_paid_show,
          invoice_number,
          invoice_previous_allocations,
          ...rest
        } = payment;
        return {
          ...rest,
        };
      });
    });
    form.setValue('payments', payments);

    form.setValue('payment_cards', paymentsCards);
  }, [updatedPayments, paymentsCards]);

  useEffect(() => {
    if (
      (fixNumberTwoDigit(totalAmount - customerAmountApplied) >= -0.005 &&
        fixNumberTwoDigit(totalAmount - customerAmountApplied) <= -0.001) ||
      (fixNumberTwoDigit(totalAmount - customerAmountApplied) >= 0.001 &&
        fixNumberTwoDigit(totalAmount - customerAmountApplied) <= 0.005)
    ) {
      setCustomerAmountApplied(totalAmount);
      form.setValue('amount_applied', totalAmount);
    } else {
      setCustomerAmountApplied(counterAmountRoundUp(customerAmountApplied));
      form.setValue(
        'amount_applied',
        counterAmountRoundUp(customerAmountApplied),
      );
    }
    // This useeffect is to update the customerAmountAppliedRef when customerAmountApplied changes to tackle slow internet connection issue
    customerAmountAppliedRef.current = customerAmountApplied;
  }, [customerAmountApplied]);

  useEffect(() => {
    if (deletedAttachments.length > 0) {
      form.setValue('deletedAttachments', deletedAttachments);
    }
  }, [attachments]);

  useEffect(() => {
    if (form?.errors?.company_id) {
      setCompErr(true);
    } else {
      setCompErr(false);
    }
  }, [form?.errors?.company_id]);

  const fetchData = async () => {
    if (selectedCustomerPayment && isUpdate) {
      setAttachments(selectedCustomerPayment?.attachments);
      form.setValue(
        'attachments',
        selectedCustomerPayment?.attachments || null,
      );
      form.setValue('company_id', selectedCustomerPayment?.company_id || null);
      form.setValue('bank_id', selectedCustomerPayment?.bank_id || null);
      form.setValue(
        'customer_id',
        selectedCustomerPayment?.customer_id || null,
      );
      form.setValue('state', selectedCustomerPayment?.state || null);
      form.setValue(
        'payment_method',
        selectedCustomerPayment?.payment_method || null,
      );
      form.setValue(
        'amount',
        parseFloat(selectedCustomerPayment?.amount) || null,
      );
      form.setValue('currency', selectedCustomerPayment?.currency || null);
      form.setValue('remark', selectedCustomerPayment?.remark || null);
      form.setValue('link', selectedCustomerPayment?.link || null);
      form.setValue(
        'payment_date',
        selectedCustomerPayment?.payment_date || null,
      );
      form.setValue(
        'transaction_number',
        selectedCustomerPayment?.transaction_number || '',
      );
      form.setValue(
        'transaction_fee',
        parseFloat(selectedCustomerPayment?.transaction_fee) || 0,
      );
      form.setValue(
        'inapplicable_amount',
        parseFloat(selectedCustomerPayment?.inapplicable_amount) || 0,
      );
      form.setValue(
        'exchange_rate',
        parseFloat(selectedCustomerPayment?.exchange_rate) || null,
      );
      // if (selectedCustomerPayment?.payments.length > 0) {
      //   const mergedArray = mergePaymentsAndCards(
      //     selectedCustomerPayment?.payments,
      //     selectedCustomerPayment?.payment_cards,
      //   );
      //   const preparePaymentsForUpdate = await Promise.all(
      //     selectedCustomerPayment?.payments?.map(async (payment) => {
      //       const isMixType = payment?.type === 'mix';
      //       const paymentType = payment?.[paymentTypeOptions[payment?.type]];
      //       const {
      //         amtCharge,
      //         amtReturn,
      //         paidAmt,
      //         paidReturn,
      //         invoiceNumberReturn,
      //         previousAllocationsReturn,
      //         showDiscount,
      //         discountReturn,
      //       } = await getInvoiceInfo(
      //         customerPaymentSelected.id,
      //         payment?.type,
      //         paymentType,
      //         isMixType ? paymentType.mix_shipping_invoices : null,
      //       );
      //       return {
      //         ...payment,
      //         amount_applied: parseFloat(payment?.amount_applied),
      //         payment_remark: payment?.payment_remark || '',
      //         payment_date: payment?.payment_date || new Date(),
      //         exchange_rate: payment?.exchange_rate || '',
      //         payment_allocations: payment?.payment_allocations.map(
      //           (allocation) => ({
      //             ...allocation,
      //             amount: parseFloat(allocation?.amount),
      //           }),
      //         ),
      //         [paymentNameOptions[payment?.type]]: paymentType?.id || null,
      //         invoice_amt: amtCharge,
      //         invoice_amt_show: amtReturn,
      //         invoice_discount: showDiscount ? discountReturn : 0,
      //         invoice_paid: paidAmt,
      //         invoice_paid_show: paidReturn,
      //         invoice_number: invoiceNumberReturn,
      //         invoice_previous_allocations: previousAllocationsReturn,
      //       };
      //     }) || [],
      //   );

      //   const groupedPaymentsByType: [][] = Object.values(
      //     preparePaymentsForUpdate.reduce((acc, payment) => {
      //       if (!acc[payment?.type]) {
      //         acc[payment?.type] = [];
      //       }
      //       acc[payment?.type].push(payment);
      //       return acc;
      //     }, {}),
      //   );
      //   setUpdatedPayments(groupedPaymentsByType);

      //   const types = [
      //     ...new Set(preparePaymentsForUpdate.map((payment) => payment?.type)),
      //   ];
      //   types.forEach((type) => {
      //     const invoiceIds = [];
      //     const cardSums = [];
      //     const exchangeRate = [];
      //     preparePaymentsForUpdate.map((payment) => {
      //       if (payment?.type == type) {
      //         invoiceIds.push(payment[paymentNameOptions[payment?.type]]);
      //         cardSums.push(payment?.amount_applied);
      //         if (exchangeRate.length == 0)
      //           exchangeRate.push(payment?.exchange_rate);
      //       }
      //     });
      //     form.setValue(`invoice_number_${type}_card`, invoiceIds);
      //     const cardSum = cardSums.reduce((acc, sum) => (acc += sum), 0);
      //     handleSumChange(`sum_${type}_card`, cardSum);
      //     handleCardExchangeRateChange(
      //       `exchange_rate_${type}_card`,
      //       exchangeRate[0],
      //     );
      //     handleLimitChange(`limit_${type}_card`, 0);
      //     handleShortAmountsChange(`short_amount_${type}_card`, 0);
      //   });
      // }
      if (selectedCustomerPayment?.payments.length > 0) {
        const preparePaymentsForUpdate = await Promise.all(
          selectedCustomerPayment?.payments?.map(async (payment) => {
            const isMixType = payment?.type === 'mix';
            const paymentType = payment?.[paymentTypeOptions[payment?.type]];
            const {
              amtCharge,
              amtReturn,
              paidAmt,
              paidReturn,
              invoiceNumberReturn,
              previousAllocationsReturn,
              showDiscount,
              discountReturn,
            } = await getInvoiceInfo(
              customerPaymentSelected.id,
              payment?.type,
              paymentType,
              isMixType ? paymentType.mix_shipping_invoices : null,
            );
            return {
              ...payment,
              amount_applied: parseFloat(payment?.amount_applied),
              payment_remark: payment?.payment_remark || '',
              payment_date: payment?.payment_date || new Date(),
              exchange_rate: payment?.exchange_rate || '',
              payment_allocations: payment?.payment_allocations.map(
                (allocation) => ({
                  ...allocation,
                  amount: parseFloat(allocation?.amount),
                }),
              ),
              [paymentNameOptions[payment?.type]]: paymentType?.id || null,
              invoice_amt: amtCharge,
              invoice_amt_show: amtReturn,
              invoice_discount: showDiscount ? discountReturn : 0,
              invoice_paid: paidAmt,
              invoice_paid_show: paidReturn,
              invoice_number: invoiceNumberReturn,
              invoice_previous_allocations: previousAllocationsReturn,
            };
          }) || [],
        );

        const groupedPaymentsByType: [][] = Object.values(
          preparePaymentsForUpdate.reduce((acc, payment) => {
            if (!acc[payment?.type]) {
              acc[payment?.type] = [];
            }
            acc[payment?.type].push(payment);
            return acc;
          }, {}),
        );
        setUpdatedPayments(groupedPaymentsByType);

        const types = [
          ...new Set(preparePaymentsForUpdate.map((payment) => payment?.type)),
        ];
        types.forEach((type) => {
          const invoiceIds = [];
          const cardSums = [];
          const exchangeRate = [];
          preparePaymentsForUpdate.map((payment) => {
            if (payment?.type == type) {
              invoiceIds.push(payment[paymentNameOptions[payment?.type]]);
              cardSums.push(payment?.amount_applied);
              if (exchangeRate.length == 0)
                exchangeRate.push(payment?.exchange_rate);
            }
          });
          form.setValue(`invoice_number_${type}_card`, invoiceIds);
          const cardSum = cardSums.reduce((acc, sum) => (acc += sum), 0);
          handleSumChange(`sum_${type}_card`, cardSum);
          handleCardExchangeRateChange(
            `exchange_rate_${type}_card`,
            exchangeRate[0],
          );
          handleLimitChange(
            `limit_${type}_card`,
            limits[`limit_${type}_card`] || 0,
          );
          handleShortAmountsChange(`short_amount_${type}_card`, 0);
        });
      }
      // if (
      //   selectedCustomerPayment?.state === paymentStateOptions.bank_approve &&
      //   bankApprovalRequiredMethods.includes(
      //     selectedCustomerPayment?.payment_method,
      //   )
      // ) {
      //   setShowDepositDate(true);
      // }
    }
  };

  useEffect(() => {
    setPaymentsCards((prevCards) =>
      prevCards.map((card) => ({
        ...card,
        limit: limits[`limit_${card.id}`] || 0,
        short_amount: shortAmounts[`short_${card.id}`] || 0,
        exchange_rate: cardExchangeRate[`exchange_${card.id}`] || 1,
      })),
    );
  }, [limits, shortAmounts]);

  // UseEffect for customer payment update.
  useEffect(() => {
    if (isCredit && !isUpdate) {
      getTransactionNumber(form.getValues('payment_method'));
    }
    fetchData();
  }, [selectedCustomerPayment, isUpdate]);

  const showPDFViewer = async (name: string) => {
    const url = await getInvoiceDownloadUrl(name, true);
    setShowFile(true);
    setSelectedFileURL(url);
  };

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'customer_transaction_bank_details',
  });

  const rejectPayment = async (values: { reason: string; file: File }) => {
    await uploadFile(values.file).then(async (url) => {
      if (url) {
        const attachment = {
          customer_payment_transactionId: selectedCustomerPayment?.id,
          name: 'bank_reject',
          url,
          deposit_date: new Date().toISOString(),
        };
        await Promise.all([
          axios
            .post(
              '/customer-payment-transaction/createAttachmentRecord',
              attachment,
            )
            .then((_res) => {})
            .catch((error) => toast.error(error)),
          axios
            .patch('/customer-payment-transaction/rejectCustomerPayment', {
              id: selectedCustomerPayment?.id,
              state: 'bank_reject',
              rejection_reason: values.reason,
            })
            .then(async (_res) => {
              await axios
                .get(
                  `customer-payment-transaction/${selectedCustomerPayment?.id}`,
                )
                .then((res) => {
                  if (res.data.result) {
                    toast.success('Customer Payment Updated Successfully');
                    setSelectedCustomerPayment(res.data.data);
                    recordManager(res.data.data, 'update');
                  } else {
                    toast.error(
                      'Something went wrong with the Customer payment update',
                    );
                  }
                })
                .catch((error) => toast.error(error));
            })
            .catch((error) => toast.error(error)),
        ]).then(() => {
          setPaymentsCards([]);
          setUpdatedPayments([]);
          setIsReject(false);
        });
      }
    });
  };

  return (
    <Box sx={{ py: 2, textAlign: 'center' }}>
      <Grid
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={1.5}
      >
        <Grid size={2}></Grid>
        <Grid container justifyContent="center" alignItems="center" size={8}>
          <Typography variant="h5" component="div">
            <div>Customer Payment Details</div>
          </Typography>
          {selectedCustomerPayment?.state && (
            <Chip
              sx={{
                fontSize: '10px',
                marginLeft: '15px',
                height: '24px',
                backgroundColor: choseColor(selectedCustomerPayment?.state),
                color: 'white',
              }}
              label={removeUnderScore2(selectedCustomerPayment?.state)}
            />
          )}
        </Grid>
        <Grid container justifyContent="flex-end" size={2}>
          {selectedCustomerPayment?.amount_applied && (
            <Chip
              sx={{
                fontSize: '12px',
                borderRadius: 2,
              }}
              size="medium"
              label={`Amount Applied: ${fixNumberTwoDigit(customerAmountApplied)}`}
              color="info"
            />
          )}
        </Grid>
      </Grid>
      {selectedCustomerPayment && selectedCustomerPayment?.rejection_reason ? (
        <Typography color="red" component="div">
          <div>{selectedCustomerPayment?.rejection_reason}</div>
        </Typography>
      ) : null}
      <Grid container spacing={2}>
        <RejectionModal
          open={isReject}
          setOpen={setIsReject}
          onConfirm={rejectPayment}
        />
        <AppConfirmDialog
          maxWidth={'sm'}
          open={customerConfirmBox}
          onDeny={() => {
            setCustomerConfirmBox(false);
            setRevert(false);
          }}
          onConfirm={() => {
            approveCustomerPayment(
              selectedCustomerPayment,
              setSelectedCustomerPayment,
              recordManager,
              revert,
              setRevert,
              userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE),
            );
            setCustomerConfirmBox(false);
          }}
          dialogTitle={
            revert
              ? 'Revert the Payment'
              : getConfirmBoxDialogTitle(selectedCustomerPayment?.state)
          }
          title={
            revert === true
              ? 'Are You Sure to Revert?'
              : getConfirmBoxTitle(selectedCustomerPayment?.state)
          }
        />
        <Grid size={6}>
          <Controller
            name="company_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  sx={error ? { mt: 1.5 } : { my: 1.5 }}
                  url="autoComplete"
                  label="Select Company"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  fieldName="name"
                  disabled={
                    isCreditApplyPayment() ||
                    disabled() ||
                    hasPayments() ||
                    (isUpdate &&
                      restrictedPaymentMethods.includes(
                        selectedCustomerPayment?.payment_method,
                      ) &&
                      !isAdmin)
                  }
                  field={field}
                  error={error}
                  staticOptions={false}
                  column={'name'}
                  modal={'companies'}
                />
              );
            }}
          />
        </Grid>
        <Grid size={6}>
          <Controller
            key={renderKeyCus}
            name="customer_id"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  sx={compErr ? { mt: 1.5 } : { my: 1.5 }}
                  url={false}
                  label="Select Customer"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  fieldName=""
                  disabled={
                    isCreditApplyPayment() ||
                    disabled() ||
                    hasPayments() ||
                    (isUpdate &&
                      restrictedPaymentMethods.includes(
                        selectedCustomerPayment?.payment_method,
                      ))
                  }
                  field={field}
                  error={error}
                  staticOptions={customers}
                  column={''}
                  modal={''}
                  defualtValue={customers[0]}
                />
              );
            }}
          />
        </Grid>
        <Grid size={6}>
          <Controller
            name="payment_method"
            control={form.control}
            render={({ field, fieldState: { error } }) => {
              return (
                <AutoComplete
                  url={false}
                  label="Payment Method"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  fieldName=""
                  field={field}
                  disabled={
                    isCreditApplyPayment() ||
                    disabled() ||
                    (isUpdate &&
                      restrictedPaymentMethods.includes(
                        selectedCustomerPayment?.payment_method,
                      ) &&
                      !isAdmin)
                  }
                  error={error}
                  staticOptions={paymentMethodOptions}
                  handleOnChangeFromStepper={true}
                  stepperHandleOnChange={async (_event, value) => {
                    const selectedKey = value?.id;
                    if (
                      Object.keys(creditPaymentMethods).includes(selectedKey)
                    ) {
                      getTransactionNumber(selectedKey);
                    } else {
                      setTransactionNumberDisabled(false);
                      form.setValue('transaction_number', '', {
                        shouldValidate: true,
                      });
                    }
                    // if (bankApprovalRequiredMethods.includes(selectedKey)) {
                    //   if (
                    //     !isUpdate ||
                    //     selectedCustomerPayment?.state ===
                    //       paymentStateOptions.bank_approve
                    //   ) {
                    //     setShowDepositDate(true);
                    //   }
                    // }
                    field.onChange(selectedKey);
                  }}
                  column=""
                  modal=""
                />
              );
            }}
          />
        </Grid>
        <Grid
          style={{ display: 'flex', justifyContent: 'space-between' }}
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Grid
            size={{
              xs: isCredit ? 12 : 7.6,
              md: isCredit ? 12 : 7.6,
            }}
          >
            <TextField
              size="small"
              error={form.errors.amount?.message.length > 0}
              id="amount"
              label="Amount"
              className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
              type="number"
              fullWidth
              variant="outlined"
              disabled={
                isCreditApplyPayment() ||
                disabled() ||
                hasPayments() ||
                (isUpdate &&
                  restrictedPaymentMethods.includes(
                    selectedCustomerPayment?.payment_method,
                  ))
              }
              InputProps={{ inputProps: { min: 1 } }}
              {...form.register('amount', {
                setValueAs: (value) => (value ? +value : 0),
                onChange: (e) =>
                  setTotalAmount(
                    e.target.value
                      ? +e.target.value -
                          form.getValues('transaction_fee') -
                          form.getValues('inapplicable_amount')
                      : 0,
                  ),
              })}
              helperText={
                form.errors.amount?.message && 'Enter An Amount Greater Than 0'
              }
            />
          </Grid>
          {!isCredit ? (
            <>
              <Grid
                marginX={0.5}
                size={{
                  xs: 4,
                  md: 4,
                }}
              >
                <TextField
                  size="small"
                  error={form.errors.transaction_fee?.message.length > 0}
                  id="transaction_fee"
                  label="Transaction Fee"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  type="number"
                  fullWidth
                  variant="outlined"
                  value={form.getValues('transaction_fee')}
                  InputProps={{
                    inputProps: {
                      min: 1,
                    },
                  }}
                  onChange={(e) => {
                    const diff =
                      +e.target.value - form.getValues('transaction_fee');
                    const limit = totalAmount - customerAmountApplied;
                    if (limit - +diff < 0) {
                      toast.error('No amount remaining!.');
                      return;
                    }
                    form.setValue(
                      'transaction_fee',
                      e.target.value ? +e.target.value : 0,
                    );
                    setTotalAmount(form.getValues('amount') - +e.target.value);
                  }}
                />
              </Grid>
              <Grid
                size={{
                  xs: 4,
                  md: 4,
                }}
              >
                <TextField
                  size="small"
                  error={form.errors.inapplicable_amount?.message.length > 0}
                  id="inapplicable_amount"
                  label="Inapplicable Amount"
                  className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                  type="number"
                  fullWidth
                  variant="outlined"
                  value={form.getValues('inapplicable_amount')}
                  InputProps={{
                    inputProps: {
                      min: 1,
                    },
                  }}
                  onChange={(e) => {
                    const diff =
                      +e.target.value - form.getValues('inapplicable_amount');
                    const limit = totalAmount - customerAmountApplied;
                    if (limit - +diff < 0) {
                      toast.error('No amount remaining!.');
                      return;
                    }
                    form.setValue(
                      'inapplicable_amount',
                      e.target.value ? +e.target.value : 0,
                    );
                    setTotalAmount(form.getValues('amount') - +e.target.value);
                  }}
                />
              </Grid>
            </>
          ) : null}
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Controller
            name={`currency`}
            control={form.control}
            render={({ field, fieldState: { invalid, error } }) => (
              <MUIAutoComplete
                size="small"
                value={field.value}
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                getOptionLabel={(option) => option}
                onChange={async (_event: any, newValue) => {
                  form.setValue(`currency`, newValue);
                  if (newValue != 'USD') {
                    const rate = await getExchangeRate(id, newValue);
                    form.setValue(`exchange_rate`, rate);
                    setExchangeRate(rate);
                  } else {
                    form.setValue(`exchange_rate`, 1);
                    setExchangeRate(1);
                  }
                }}
                disabled={
                  isCreditApplyPayment() ||
                  disabled() ||
                  hasPayments() ||
                  (isUpdate &&
                    restrictedPaymentMethods.includes(
                      selectedCustomerPayment?.payment_method,
                    ) &&
                    !isAdmin)
                }
                options={currencyOptions}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="currency"
                    error={invalid}
                    helperText={error?.message}
                  />
                )}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.exchange_rate?.message.length > 0}
            id="exchange_rate"
            label="Exchange Rate"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="number"
            fullWidth
            variant="outlined"
            disabled={disabled() || hasPayments()}
            value={exchangeRate}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('exchange_rate', {
              setValueAs: (value) => (value ? +value : null),
              onChange: (e) =>
                setExchangeRate(e.target.value ? +e.target.value : 1),
            })}
            helperText={
              form.errors.exchange_rate?.message &&
              'Exchange Rate greater than 0'
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.transaction_number?.message.length > 0}
            id="transaction_number"
            label="Transaction Number"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            value={form.getValues('transaction_number')}
            disabled={
              isCreditApplyPayment() ||
              transactionNumberDisabled ||
              disabled() ||
              (isUpdate &&
                restrictedPaymentMethods.includes(
                  selectedCustomerPayment?.payment_method,
                ) &&
                !isAdmin)
            }
            onChange={(e) => {
              form.setValue('transaction_number', e.target.value, {
                shouldValidate: true,
              });
            }}
            helperText={
              form.errors.transaction_number?.message &&
              'Enter Valid Transaction Number'
            }
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 4,
          }}
        >
          <Controller
            control={form.control}
            name="payment_date"
            render={({ field, fieldState: { error } }) => (
              <DatePicker
                className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                views={['year', 'month', 'day']}
                label="Payment Date"
                value={!field.value ? new Date() : dayjs(field.value).toDate()}
                format="yyyy/MM/dd"
                disabled={isCreditApplyPayment() || disabled()}
                onChange={(e) => {
                  const date = new Date(
                    dayjs(e).format('YYYY-MM-DD'),
                  ).toISOString();
                  form.setValue('payment_date', date);
                }}
                inputRef={field.ref}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error: !!error,
                    helperText: error?.message,
                    size: 'small',
                    fullWidth: true,
                  },
                }}
              />
            )}
          />
        </Grid>
        <Grid
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <TextField
            size="small"
            error={form.errors.link?.message.length > 0}
            id="link"
            label="Attachments Link"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            disabled={disabled()}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('link', {
              setValueAs: (value) => (value ? value : ''),
            })}
          />
        </Grid>
        <Grid
          textAlign="left"
          size={{
            xs: 12,
            md: 6,
          }}
        >
          <Box display="flex" gap={1}>
            <Button
              component="label"
              role={undefined}
              variant="contained"
              tabIndex={-1}
              startIcon={<CloudUploadIcon />}
              sx={{ marginBottom: '10px' }}
              disabled={disabled()}
            >
              {selectedCustomerPayment?.attachments
                ? 'Update Attachments'
                : 'Select Attachments'}
              <VisuallyHiddenInput
                type="file"
                multiple
                onChange={(e) => handleUploadedFile(e.target.files)}
              />
            </Button>
            {/* {showDepositDate && (
              <Grid item xs={12} md={4} >
                <Controller
                  control={form.control}
                  name="deposit_date"
                  render={({ field, fieldState: { error } }) => (
                    <DatePicker
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}` }
                      views={['year', 'month', 'day']}
                      label="Deposit Date"
                      value={!field.value ? null : dayjs(field.value).toDate()}
                      format="yyyy/MM/dd"
                      disabled={disabled() || hasPayments()}
                      onChange={(e) => {
                        const date = new Date(
                          dayjs(e).format('YYYY-MM-DD'),
                        ).toISOString();
                        form.setValue('deposit_date', date);
                      }}
                      inputRef={field.ref}
                      slotProps={{
                        textField: {
                          variant: 'outlined',
                          error: !!error,
                          helperText: error?.message,
                          size: 'small',
                          fullWidth: true,
                        },
                      }}
                    />
                  )}
                />
              </Grid>
            )} */}
          </Box>
          {/* <sub style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            {attachments?.map((attachment, i) => (
              <List
                key={i + 1}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  border: '1px solid gray',
                  borderRadius: '5px',
                  marginBottom: '5px',
                  padding: '8px 12px',
                  width: '230px',
                }}
              >
                <Typography fontSize={12}>
                  {attachment.name.substring(0, 18)}...
                  {attachment.deposit_date && (
                    <>
                      <br />
                      Deposit Date:{' '}
                      {dayjs(attachment.deposit_date).format('YYYY/MM/DD')}
                    </>
                  )}
                </Typography>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  gap={1}
                >
                  <Link
                    sx={{
                      width: '20px',
                      height: '20px',
                      marginBottom: '4px',
                      cursor: disabled() ? 'not-allowed' : 'pointer',
                      opacity: disabled() ? 0.5 : 1,
                    }}
                    onClick={() =>
                      removeSingleFile(attachment.name, attachment.url)
                    }
                  >
                    <CancelIcon color="error" />
                  </Link>
                  <Link
                    sx={{
                      gap: '2',
                      width: '20px',
                      height: '20px',
                      textDecoration: 'none',
                      color: 'green',
                      marginBottom: '4px',
                    }}
                    onClick={() => getInvoiceDownloadUrl(attachment.name)}
                    target="_blank"
                  >
                    <FeedIcon />
                  </Link>
                  <Link
                    sx={{
                      gap: '2',
                      width: '20px',
                      height: '20px',
                      textDecoration: 'none',
                      color: 'green',
                      marginBottom: '4px',
                    }}
                    onClick={() => showPDFViewer(attachment.name)}
                  >
                    <ViewIcon />
                  </Link>
                  <MyDocument
                    url={selectedFileURL}
                    showFile={showFile}
                    setShowFile={setShowFile}
                  />
                </Box>
              </List>
            ))}
          </sub> */}
          <sub style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            {attachments?.map((op: any, i: number) => (
              <FileView
                key={i}
                file={op}
                removeFile={removeSingleFile}
                disabled={disabled}
                showPDFViewer={() => showPDFViewer(op?.name)}
              />
            ))}
          </sub>
          <MyDocument
            url={selectedFileURL}
            showFile={showFile}
            setShowFile={setShowFile}
          />
        </Grid>
        {!noneBankPaymentMethods.includes(form.getValues('payment_method')) &&
          !isCredit && (
            <Grid size={12}>
              <Paper sx={{ padding: 2, border: '1px solid #ccc' }}>
                <Grid size={12}>
                  <Controller
                    name="bank_id"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => {
                      return (
                        <AutoComplete
                          sx={error ? { mt: 1.5 } : { my: 1.5 }}
                          url="autoComplete"
                          label="Select Bank"
                          className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                          fieldName="bank_name"
                          customeName="bank_name"
                          field={field}
                          disabled={disabledAfterBankApproved()}
                          error={error}
                          staticOptions={false}
                          column={'bank_name'}
                          modal={'bank_accounts'}
                        />
                      );
                    }}
                  />
                </Grid>
                {fields.map((field: any, index) => (
                  <Grid key={field.id} mb={2} size={12}>
                    <Paper sx={{ padding: 2, border: '1px solid #ccc' }}>
                      <Grid container spacing={2}>
                        <Grid
                          size={{
                            xs: 12,
                            md: 6,
                          }}
                        >
                          <TextField
                            size="small"
                            label="Title"
                            fullWidth
                            disabled={disabledAfterBankApproved()}
                            variant="outlined"
                            {...form.register(
                              `customer_transaction_bank_details.${index}.title`,
                              {
                                setValueAs: (v) => v || '',
                              },
                            )}
                          />
                        </Grid>
                        <Grid
                          p={2}
                          size={{
                            xs: 12,
                            md: 6,
                          }}
                        >
                          <TextField
                            size="small"
                            label="Reference Number"
                            fullWidth
                            disabled={disabledAfterBankApproved()}
                            variant="outlined"
                            {...form.register(
                              `customer_transaction_bank_details.${index}.reference_number`,
                              {
                                setValueAs: (v) => v || '',
                              },
                            )}
                          />
                        </Grid>
                        <Box display={'flex'} sx={{ width: '100%' }}>
                          <Grid
                            style={{ paddingLeft: '18px' }}
                            size={{
                              xs: 12,
                              md: 6,
                            }}
                          >
                            <TextField
                              size="small"
                              label="Remark"
                              fullWidth
                              disabled={disabledAfterBankApproved()}
                              variant="outlined"
                              {...form.register(
                                `customer_transaction_bank_details.${index}.remark`,
                                {
                                  setValueAs: (v) => v || '',
                                },
                              )}
                            />
                          </Grid>
                          <Grid
                            textAlign="left"
                            sx={{ paddingLeft: 2 }}
                            size={{
                              xs: 12,
                              md: 6,
                            }}
                          >
                            <Box display="flex" gap={1}>
                              <Button
                                component="label"
                                variant="contained"
                                disabled={disabledAfterBankApproved()}
                                startIcon={<CloudUploadIcon />}
                              >
                                {field?.attachment
                                  ? 'Update Attachments'
                                  : 'Select Attachments'}
                                <VisuallyHiddenInput
                                  type="file"
                                  onChange={(e) => {
                                    const val1 = form.getValues(
                                      `customer_transaction_bank_details.${index}.title`,
                                    );
                                    const val2 = form.getValues(
                                      `customer_transaction_bank_details.${index}.reference_number`,
                                    );
                                    if (!val1 || !val2) {
                                      setCommentsFiles([]);
                                      return toast.error(
                                        'Title & Reference number both are Requied',
                                      );
                                    }
                                    uploadCommentsFile(
                                      e.target.files,
                                      `customer_transaction_bank_details.${index}.attachment`,
                                    );
                                  }}
                                />
                              </Button>
                              <Grid
                                size={{
                                  xs: 12,
                                  md: 4,
                                }}
                              >
                                <Controller
                                  control={form.control}
                                  name={`customer_transaction_bank_details.${index}.deposit_date`}
                                  render={({
                                    field,
                                    fieldState: { error },
                                  }) => (
                                    <DatePicker
                                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                                      views={['year', 'month', 'day']}
                                      label="Deposit Date"
                                      value={
                                        !field.value
                                          ? null
                                          : dayjs(field.value).toDate()
                                      }
                                      format="yyyy/MM/dd"
                                      disabled={disabledAfterBankApproved()}
                                      onChange={(e) => {
                                        const date = new Date(
                                          dayjs(e).format('YYYY-MM-DD'),
                                        ).toISOString();
                                        form.setValue(
                                          `customer_transaction_bank_details.${index}.deposit_date`,
                                          date,
                                        );
                                      }}
                                      inputRef={field.ref}
                                      slotProps={{
                                        textField: {
                                          variant: 'outlined',
                                          error: !!error,
                                          helperText: error?.message,
                                          size: 'small',
                                          fullWidth: true,
                                        },
                                      }}
                                    />
                                  )}
                                />
                              </Grid>
                            </Box>
                            {field.attachment &&
                              form.getValues(
                                `customer_transaction_bank_details.${index}.attachment`,
                              ) && (
                                <sub
                                  style={{
                                    display: 'flex',
                                    gap: '10px',
                                    flexWrap: 'wrap',
                                  }}
                                >
                                  <FileView
                                    removable={false}
                                    file={{
                                      name: field.attachment_name,
                                      url: field.attachment,
                                      deposit_date: field.deposit_date,
                                    }}
                                    disabled={disabledAfterBankApproved()}
                                  />
                                </sub>
                              )}
                            {commentsFiles[
                              `customer_transaction_bank_details.${index}.attachment`
                            ]?.length ? (
                              <Box
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                gap={1}
                              >
                                <Typography component="div">
                                  <div>
                                    {
                                      commentsFiles[
                                        `customer_transaction_bank_details.${index}.attachment`
                                      ][0]?.name
                                    }
                                  </div>
                                </Typography>
                                <Button
                                  color="error"
                                  onClick={() =>
                                    setCommentsFiles((prev) => {
                                      const updated = { ...prev };
                                      delete updated[
                                        `customer_transaction_bank_details.${index}.attachment`
                                      ];
                                      form.setValue(
                                        `customer_transaction_bank_details.${index}.attachment`,
                                        '',
                                      );
                                      return updated;
                                    })
                                  }
                                >
                                  <CancelIcon color="error" />
                                </Button>
                              </Box>
                            ) : null}
                          </Grid>
                        </Box>
                        <Grid size={12}>
                          <Button
                            color="error"
                            disabled={disabledAfterBankApproved()}
                            onClick={() => remove(index)}
                          >
                            Remove
                          </Button>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Grid>
                ))}
                <Button
                  variant="outlined"
                  color="primary"
                  sx={{ mt: 3 }}
                  disabled={disabledAfterBankApproved()}
                  onClick={() => {
                    append({
                      id: new Date().toISOString(),
                      title: '',
                      reference_number: '',
                      bank_remark: '',
                      attachments: null,
                    });
                  }}
                >
                  Add New Record
                </Button>
              </Paper>
            </Grid>
          )}
        <Grid
          size={{
            xs: 12,
            md: 12,
          }}
        >
          <TextField
            size="small"
            error={form.errors.remark?.message.length > 0}
            id="remark"
            label="Remark"
            className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
            type="text"
            fullWidth
            variant="outlined"
            // disabled={disabled()}
            InputProps={{ inputProps: { min: 0 } }}
            {...form.register('remark', {
              setValueAs: (value) => (value ? value : ''),
            })}
          />
        </Grid>
        {paymentsCards.map((card: any) => (
          <Grid key={card?.id} size={12}>
            <Paper sx={{ padding: 2, border: '1px solid #ccc' }}>
              <Grid
                container
                alignItems="center"
                justifyContent="space-between"
                paddingBottom={0.5}
              >
                <Grid size="grow">
                  {!hasPermission(card?.type, userPermissions) && (
                    <Typography
                      textAlign="left"
                      variant="caption"
                      display="block"
                      color="orange"
                      mt={0.5}
                      fontSize={14}
                      fontWeight={500}
                      component="div"
                    >
                      <div>
                        You have only View Access to{' '}
                        {removeUnderScore2(card?.type)} Card Payments
                      </div>
                    </Typography>
                  )}
                </Grid>

                <Grid>
                  <Chip
                    sx={{
                      fontSize: '12px',
                      borderRadius: 2,
                      marginBottom: '15px',
                    }}
                    size="medium"
                    label={`Remaining Amount: ${fixNumberTwoDigit(totalAmount - customerAmountApplied)}`}
                    color="info"
                  />
                </Grid>
              </Grid>
              <Grid
                container
                alignItems="center"
                justifyContent="space-between"
              >
                <Grid size={2}>
                  <Controller
                    name={`payment_type_${card?.id}`}
                    control={form.control}
                    defaultValue={card?.type}
                    render={({ field, fieldState: { error } }) => {
                      const updatedField = {
                        ...field,
                        value: card?.type,
                        name: `payment_type_${card?.id}`,
                      };
                      return (
                        <AutoComplete
                          url={false}
                          label="Payment Type"
                          fieldName="type"
                          className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                          defualtValue={field.value}
                          field={updatedField}
                          error={error}
                          staticOptions={paymentTypes.filter((op: any) => {
                            const exclude = isCredit
                              ? ['non_allocated']
                              : ['cash', 'non_allocated'];
                            return !exclude.includes(op.value);
                          })}
                          column={''}
                          modal={''}
                          disabled={
                            getPaymentByType(field.value) ||
                            (disabled() && !isNewlyAddedCard(card?.id))
                          }
                          handleOnChangeFromStepper={true}
                          stepperHandleOnChange={async (_e, selection) => {
                            const exists = paymentsCards.find(
                              (card) => card.type === selection?.value,
                            );
                            if (exists) {
                              return toast.error(
                                `Payment Type ${selection?.label} is already Selected.`,
                              );
                            }

                            if (
                              selection?.value != 'shipment' &&
                              selection?.value != 'mix' &&
                              selection?.value != 'auction' &&
                              selection?.value != 'cash'
                            ) {
                              const uaeRate = await getExchangeRate(id, 'AED');
                              handleCardExchangeRateChange(
                                `exchange_rate_${card?.id}`,
                                uaeRate,
                              );
                            } else {
                              if (selection?.value == 'cash') {
                                handleCardExchangeRateChange(
                                  `exchange_rate_${card?.id}`,
                                  exchangeRate,
                                );
                              } else {
                                handleCardExchangeRateChange(
                                  `exchange_rate_${card?.id}`,
                                  1,
                                );
                              }
                            }
                            field.onChange(selection?.value);
                            if (selection?.value) {
                              handlePaymentCardChange(
                                card?.id,
                                card?.type,
                                'type',
                                selection?.value,
                                setPaymentsCards,
                                setUpdatedPayments,
                                handleSumChange,
                                customerAmountApplied,
                                setCustomerAmountApplied,
                                exchangeRate,
                              );
                            }
                          }}
                        />
                      );
                    }}
                  />
                </Grid>
                <>
                  <Grid size={1}>
                    <TextField
                      size="small"
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                      id={`limit_${card?.id}`}
                      name={`limit_${card?.id}`}
                      label={`Limit (${form.getValues('currency')})`}
                      type="number"
                      fullWidth
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                      value={limits[`limit_${card?.id}`]}
                      disabled={
                        !userPermissions.includes(
                          CUSTOMER_PAYMENTS.CHANGE_LIMIT,
                        ) ||
                        !hasPermission(card?.type, userPermissions) ||
                        disabled()
                      }
                      onChange={(e) => {
                        handleLimitChange(
                          e.target.name,
                          parseFloat(e.target.value),
                        );
                      }}
                    />
                  </Grid>
                  <Grid size={1}>
                    <TextField
                      size="small"
                      className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                      sx={{ color: 'black !important' }}
                      id={`sum_${card?.id}`}
                      label="Sum"
                      type="number"
                      name={`sum_${card?.id}`}
                      disabled
                      fullWidth
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                      value={sums[`sum_${card?.id}`] ?? 0}
                    />
                  </Grid>
                  {card.type != 'shipment' &&
                    card.type != 'mix' &&
                    card.type != 'auction' &&
                    card.type != 'cash' && (
                      <Grid size={1}>
                        <TextField
                          size="small"
                          className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                          id={`exchange_rate_${card?.id}`}
                          name={`exchange_rate_${card?.id}`}
                          label="UAE Rate"
                          type="number"
                          value={cardExchangeRate[`exchange_rate_${card?.id}`]}
                          fullWidth
                          variant="outlined"
                          disabled={
                            (disabled() &&
                              !hasRemaining(
                                totalAmount,
                                customerAmountApplied,
                              )) ||
                            !hasPermission(card?.type, userPermissions)
                          }
                          InputProps={{ inputProps: { min: 0 } }}
                          onChange={(e) =>
                            handleCardExchangeRateChange(
                              e.target.name,
                              parseFloat(e.target.value),
                            )
                          }
                        />
                      </Grid>
                    )}
                  {card.type == 'auction' && (
                    <Grid size={1}>
                      <TextField
                        size="small"
                        className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                        id={`short_amount_${card?.id}`}
                        name={`short_amount_${card?.id}`}
                        label="Short Amount"
                        type="number"
                        fullWidth
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0 } }}
                        value={shortAmounts[`short_amount_${card?.id}`]}
                        disabled={
                          (disabled() &&
                            !hasRemaining(
                              totalAmount,
                              customerAmountApplied,
                            )) ||
                          !hasPermission(card?.type, userPermissions)
                        }
                        onChange={(e) =>
                          handleShortAmountsChange(
                            e.target.name,
                            parseFloat(e.target.value),
                          )
                        }
                      />
                    </Grid>
                  )}
                  {card.type !== 'cash' && (
                    <Grid size={4}>
                      <Stack spacing={0.5}>
                        <Controller
                          name={`invoice_number_${card?.id}`}
                          defaultValue={
                            card[paymentTypeOptions[card?.type]]?.ids
                          }
                          control={form.control}
                          render={({ field }) => {
                            return (
                              <PaymentAutocomplete
                                url={getInvoiceNumberUrl(field.value)}
                                label={`Select ${
                                  card?.type
                                    ? card?.type
                                        .split('_')
                                        .map(
                                          (word) =>
                                            word.charAt(0).toUpperCase() +
                                            word.slice(1),
                                        )
                                        .join(' ')
                                    : ''
                                } ${'Invoice'}`}
                                name={
                                  card?.type
                                    ? paymentModalOptions[card?.type]
                                    : ''
                                }
                                keyName={'invoice_number'}
                                values={field.value}
                                isPayment={true}
                                loading={invoiceLoader}
                                disabled={
                                  ((!card?.type ||
                                    !companyId ||
                                    invoiceLoader) &&
                                    !isNewlyAddedCard(card?.id) &&
                                    ((isUpdate &&
                                      selectedCustomerPayment?.state ===
                                        paymentStateOptions.bank_approve) ||
                                      (!isUpdate &&
                                        bankApprovalRequiredMethods.includes(
                                          form.getValues('payment_method'),
                                        )))) ||
                                  !hasPermission(card?.type, userPermissions)
                                }
                                disabledId={paymentsDisabledId(
                                  updatedPayments,
                                  paymentsCards,
                                  card?.id,
                                )}
                                onChange={(_e, _value) => {
                                  setInvoiceLoader(true);
                                  const fieldLength = field?.value?.length || 0;
                                  const isValueAdded = _e?.length > fieldLength;
                                  if (isValueAdded) {
                                    if (
                                      !islimitHasReached(
                                        null,
                                        totalAmount,
                                        customerAmountApplied,
                                        false,
                                        'grand_limit',
                                      )
                                    ) {
                                      if (
                                        !islimitHasReached(
                                          card?.id,
                                          getNormalizedLimits(
                                            paymentsCards,
                                            form.getValues('currency'),
                                            form.getValues('exchange_rate'),
                                            limits,
                                            cardExchangeRate,
                                          ),
                                          sums[`sum_${card?.id}`],
                                          false,
                                          'payment',
                                        )
                                      ) {
                                        handlePaymentInvoiceChange(
                                          selectedCustomerPayment?.id,
                                          card?.id,
                                          _e,
                                          card?.type,
                                          updatedPayments,
                                          setUpdatedPayments,
                                          getNormalizedLimits(
                                            paymentsCards,
                                            form.getValues('currency'),
                                            form.getValues('exchange_rate'),
                                            limits,
                                            cardExchangeRate,
                                          ),
                                          sums,
                                          handleSumChange,
                                          totalAmount,
                                          customerAmountApplied,
                                          setCustomerAmountApplied,
                                          exchangeRate,
                                          cardExchangeRate,
                                          shortAmounts,
                                          setInvoiceLoader,
                                          form.getValues('payment_date'),
                                          _e,
                                          (updatedValue) => {
                                            field.onChange(updatedValue);
                                          },
                                          customerAmountAppliedRef,
                                        );
                                      } else {
                                        setInvoiceLoader(false);
                                      }
                                    } else {
                                      setInvoiceLoader(false);
                                    }
                                  } else {
                                    handlePaymentInvoiceChange(
                                      selectedCustomerPayment?.id,
                                      card.id,
                                      _e,
                                      card?.type,
                                      updatedPayments,
                                      setUpdatedPayments,
                                      getNormalizedLimits(
                                        paymentsCards,
                                        form.getValues('currency'),
                                        form.getValues('exchange_rate'),
                                        limits,
                                        cardExchangeRate,
                                      ),
                                      sums,
                                      handleSumChange,
                                      totalAmount,
                                      customerAmountApplied,
                                      setCustomerAmountApplied,
                                      exchangeRate,
                                      cardExchangeRate,
                                      shortAmounts,
                                      setInvoiceLoader,
                                      form.getValues('payment_date'),
                                      _e,
                                      (updatedValue) => {
                                        field.onChange(updatedValue);
                                      },
                                      customerAmountAppliedRef,
                                    );
                                  }
                                }}
                              />
                            );
                          }}
                        />
                      </Stack>
                    </Grid>
                  )}
                </>
                {card.type == 'cash' && (
                  <>
                    <Grid
                      sx={{
                        display: 'flex',
                      }}
                      gap={3}
                    >
                      <TextField
                        size="small"
                        className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                        id={`transaction_number_${card?.id}`}
                        name={`transaction_number_${card?.id}`}
                        label="Transaction Number"
                        fullWidth
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0 } }}
                        value={cashTransactionNumber}
                        disabled={disabled()}
                        onChange={(e) =>
                          setCashTransactionNumber(e.target.value)
                        }
                      />
                      <TextField
                        size="small"
                        className={`iceberg ${isDarkMode ? 'dark-mode' : 'light-mode'}`}
                        id={`amount_${card?.id}`}
                        name={`amount_${card?.id}`}
                        label="Amount"
                        type="number"
                        fullWidth
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0 } }}
                        value={cashAmount}
                        disabled={
                          !hasPermission(card?.type, userPermissions) ||
                          disabled()
                        }
                        onChange={(e) => {
                          const diff = +e.target.value - cashAmount;
                          const limit = totalAmount - customerAmountApplied;
                          if (limit - +diff < 0) {
                            toast.error('No amount remaining!.');
                            return;
                          }
                          setCashAmount(+e.target.value);
                        }}
                      />
                      <Tooltip title="Add Payment">
                        <Button
                          color="success"
                          disabled={disabled()}
                          onClick={() => {
                            if (!cashAmount) {
                              return;
                            }
                            if (
                              !islimitHasReached(
                                null,
                                totalAmount,
                                customerAmountApplied,
                                false,
                                'grand_limit',
                              )
                            ) {
                              if (
                                !islimitHasReached(
                                  card?.id,
                                  getNormalizedLimits(
                                    paymentsCards,
                                    form.getValues('currency'),
                                    form.getValues('exchange_rate'),
                                    limits,
                                    cardExchangeRate,
                                  ),
                                  sums[`sum_${card?.id}`],
                                  false,
                                  'payment',
                                )
                              ) {
                                handlePaymentCashChange(
                                  card.id,
                                  card.type,
                                  cashAmount,
                                  cashTransactionNumber,
                                  setUpdatedPayments,
                                  getNormalizedLimits(
                                    paymentsCards,
                                    form.getValues('currency'),
                                    form.getValues('exchange_rate'),
                                    limits,
                                    cardExchangeRate,
                                  ),
                                  sums,
                                  handleSumChange,
                                  totalAmount,
                                  customerAmountApplied,
                                  setCustomerAmountApplied,
                                  exchangeRate,
                                  cardExchangeRate,
                                  shortAmounts,
                                  setInvoiceLoader,
                                  form.getValues('payment_date'),
                                  (updateValue) => setCashAmount(updateValue),
                                  customerAmountAppliedRef,
                                );
                              }
                            }
                          }}
                          sx={{ padding: 0, minWidth: 'auto' }}
                        >
                          <AddIcon />
                        </Button>
                      </Tooltip>
                    </Grid>
                    {/* <Grid xs={1}></Grid> */}
                  </>
                )}
                <Tooltip title="Remove Payments">
                  <Button
                    color="error"
                    disabled={
                      disabled() ||
                      !hasPermission(card?.type, userPermissions) ||
                      (isUpdate &&
                        selectedCustomerPayment?.state ===
                          paymentStateOptions.bank_approve)
                    }
                    onClick={() =>
                      handleRemoveCard(
                        card.id,
                        card.type,
                        setPaymentsCards,
                        setUpdatedPayments,
                        customerAmountApplied,
                        setCustomerAmountApplied,
                        exchangeRate,
                      )
                    }
                    sx={{ padding: 0, minWidth: 'auto' }}
                  >
                    <CancelIcon />
                  </Button>
                </Tooltip>
              </Grid>
              <Grid
                marginTop={2}
                key={card?.id}
                container
                spacing={2}
                sx={{ paddingLeft: 'inherit' }}
              >
                <PaymentTable
                  form={form}
                  cardId={card?.id}
                  type={card?.type}
                  setUpdatedPayments={setUpdatedPayments}
                  updatedPayments={
                    // updatedPayments[
                    // paymentsCards.findIndex((c) => c.id === card?.id)
                    // ]
                    updatedPayments.find(
                      (group) =>
                        group.length > 0 && group[0].type === card?.type,
                    )
                  }
                  limits={limits}
                  sums={sums}
                  handleSumChange={handleSumChange}
                  confirmBox={confirmBox}
                  setConfirmBox={setConfirmBox}
                  selectedCustomerPayment={selectedCustomerPayment}
                  setSelectedCustomerPayment={setSelectedCustomerPayment}
                  totalAmount={totalAmount}
                  exchangeRate={exchangeRate}
                  customerAmountApplied={customerAmountApplied}
                  setCustomerAmountApplied={setCustomerAmountApplied}
                  currentPayment={currentPayment}
                  setCurrentPayment={setCurrentPayment}
                  userPermissions={userPermissions}
                  isDarkMode={isDarkMode}
                  revert={revert}
                  setRevert={setRevert}
                  allPaymentStateUpdateTo={allPaymentStateUpdateTo}
                  setAllPaymentStateUpdateTo={setAllPaymentStateUpdateTo}
                  allPaymentCurrentState={allPaymentCurrentState}
                  setAllPaymentCurrentState={setAllPaymentCurrentState}
                  customerAmountAppliedRef={customerAmountAppliedRef}
                  AllUpdatedPayments={updatedPayments}
                />
              </Grid>
            </Paper>
          </Grid>
        ))}
        <Grid size={12}>
          <Box mt={2} display="flex" justifyContent="space-between">
            <Box width="33.33%">
              {selectedCustomerPayment &&
                userPermissions.includes(CUSTOMER_PAYMENTS.BANK_APPROVE) &&
                selectedCustomerPayment.state !==
                  paymentStateOptions.bank_reject &&
                bankApprovalRequiredMethods.includes(
                  selectedCustomerPayment?.payment_method,
                ) && (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Tooltip title="Rejecting the customer payment will unallocate any applied invoice payments and notify the customer via email.">
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={() => {
                          setIsReject(true);
                        }}
                      >
                        Reject
                      </Button>
                    </Tooltip>
                  </Box>
                )}
            </Box>
            <Tooltip title={'Add Payment'}>
              <span>
                <Button
                  variant="outlined"
                  color="primary"
                  disabled={
                    !hasRemaining(totalAmount, customerAmountApplied) &&
                    (disabled() ||
                      !userPermissions.includes(CUSTOMER_PAYMENTS.CREATE))
                  }
                  onClick={() =>
                    handleAddCard(
                      paymentsCards,
                      setPaymentsCards,
                      handleLimitChange,
                      handleSumChange,
                      handleCardExchangeRateChange,
                      handleShortAmountsChange,
                      userPermissions,
                    )
                  }
                >
                  Add Payment
                </Button>
              </span>
            </Tooltip>
            <Box width="33.33%" display="flex" justifyContent="end">
              {selectedCustomerPayment &&
              (userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE) ||
                userPermissions.includes(CUSTOMER_PAYMENTS.REVIEW) ||
                userPermissions.includes(CUSTOMER_PAYMENTS.FINAL_REVIEW) ||
                userPermissions.includes(CUSTOMER_PAYMENTS.BANK_APPROVE)) ? (
                <>
                  {userPermissions.includes(CUSTOMER_PAYMENTS.BANK_APPROVE) &&
                    selectedCustomerPayment.state ===
                      paymentStateOptions.bank_approve && (
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <Tooltip title="Bank Approve Customer Payment">
                          <Button
                            variant="outlined"
                            color="secondary"
                            onClick={() => {
                              setRevert(false);
                              setCustomerConfirmBox(true);
                            }}
                          >
                            Bank Approve
                          </Button>
                        </Tooltip>
                      </Box>
                    )}
                  {(userPermissions.includes(CUSTOMER_PAYMENTS.REVIEW) ||
                    userPermissions.includes(CUSTOMER_PAYMENTS.BANK_APPROVE)) &&
                    selectedCustomerPayment.state ===
                      paymentStateOptions.pending && (
                      <>
                        {bankApprovalRequiredMethods.includes(
                          selectedCustomerPayment?.payment_method,
                        ) &&
                          userPermissions.includes(
                            CUSTOMER_PAYMENTS.BANK_APPROVE,
                          ) && (
                            <Tooltip title="Bank Unapprove Customer Payment">
                              <Button
                                variant="outlined"
                                color="error"
                                style={{ marginRight: 5 }}
                                onClick={async () => {
                                  setRevert(true);
                                  setCustomerConfirmBox(true);
                                }}
                              >
                                Bank Unapprove
                              </Button>
                            </Tooltip>
                          )}
                        {userPermissions.includes(CUSTOMER_PAYMENTS.REVIEW) && (
                          <Tooltip title="Review Customer Payment">
                            <Button
                              variant="outlined"
                              color="secondary"
                              onClick={() => {
                                setRevert(false);
                                setCustomerConfirmBox(true);
                              }}
                            >
                              Review
                            </Button>
                          </Tooltip>
                        )}
                      </>
                    )}
                  {selectedCustomerPayment.state ===
                    paymentStateOptions.reviewed && (
                    <>
                      <Tooltip title="Revert Customer Payment">
                        <Button
                          variant="outlined"
                          color="error"
                          style={{ marginRight: 5 }}
                          onClick={async () => {
                            setRevert(true);
                            setCustomerConfirmBox(true);
                          }}
                        >
                          Revert
                        </Button>
                      </Tooltip>
                      {userPermissions.includes(
                        CUSTOMER_PAYMENTS.FINAL_REVIEW,
                      ) && (
                        <Tooltip title="Final Review Customer Payment">
                          <Button
                            variant="outlined"
                            color="secondary"
                            onClick={() => {
                              setRevert(false);
                              setCustomerConfirmBox(true);
                            }}
                          >
                            Final Review
                          </Button>
                        </Tooltip>
                      )}
                    </>
                  )}
                  {selectedCustomerPayment.state ===
                    paymentStateOptions.final_reviewed && (
                    <>
                      <Tooltip title="Revert Customer Payment">
                        <Button
                          variant="outlined"
                          color="error"
                          style={{ marginRight: 5 }}
                          onClick={async () => {
                            setRevert(true);
                            setCustomerConfirmBox(true);
                          }}
                        >
                          Revert
                        </Button>
                      </Tooltip>
                      {userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE) && (
                        <Tooltip title="Approve Customer Payment">
                          <Button
                            variant="outlined"
                            color="secondary"
                            onClick={() => {
                              setRevert(false);
                              setCustomerConfirmBox(true);
                            }}
                          >
                            Approve
                          </Button>
                        </Tooltip>
                      )}
                    </>
                  )}
                  {userPermissions.includes(CUSTOMER_PAYMENTS.APPROVE) &&
                    selectedCustomerPayment.state ===
                      paymentStateOptions.approved && (
                      <Tooltip title="Revert Customer Payment">
                        <Button
                          variant="outlined"
                          color="error"
                          onClick={async () => {
                            setRevert(true);
                            setCustomerConfirmBox(true);
                          }}
                        >
                          Revert
                        </Button>
                      </Tooltip>
                    )}
                </>
              ) : null}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}
