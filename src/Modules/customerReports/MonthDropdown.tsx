import { Button, Menu, MenuItem } from '@mui/material';
import { useState, useMemo } from 'react';
import { format, subMonths, startOfMonth, addDays } from 'date-fns';

interface MonthOption {
  label: string;
  value: string | null;
}

interface MonthDropdownProps {
  selectedMonth: string | null;
  onSelect: (date: string | null) => void;
}

const MonthDropdown = ({ selectedMonth, onSelect }: MonthDropdownProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getOneWeekBeforeFirstMonday = (date: Date): string => {
    const firstDay = startOfMonth(date);
    let firstMonday = firstDay;

    while (firstMonday.getDay() !== 1) {
      firstMonday = addDays(firstMonday, 1);
    }

    return format(firstMonday, 'yyyy-MM-dd');
  };

  const months = useMemo(() => {
    const monthsList: MonthOption[] = [];
    const currentDate = new Date();

    monthsList.push({
      label: 'This Month',
      value: null,
    });

    for (let i = 0; i < 13; i++) {
      const date = subMonths(currentDate, i);
      monthsList.push({
        label: format(date, 'MMMM yyyy'),
        value: getOneWeekBeforeFirstMonday(date),
      });
    }

    return monthsList;
  }, []);

  return (
    <>
      <Button
        variant="outlined"
        onClick={handleClick}
        sx={{
          textTransform: 'none',
          minWidth: 150,
          justifyContent: 'space-between',
          borderColor: 'divider',
          '&:hover': {
            borderColor: 'primary.main',
          },
        }}
        endIcon={
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M7 10l5 5 5-5H7z" fill="currentColor" />
          </svg>
        }
      >
        {selectedMonth
          ? format(new Date(selectedMonth), 'MMMM yyyy')
          : 'This Month'}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            maxHeight: 300,
            minWidth: 200,
          },
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        {months.map((monthOption, i) => (
          <MenuItem
            key={i}
            selected={selectedMonth === monthOption.value}
            onClick={() => {
              onSelect(monthOption.value);
              handleClose();
            }}
            sx={{
              fontWeight: selectedMonth === monthOption.value ? 600 : 'normal',
            }}
          >
            {monthOption.label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default MonthDropdown;
