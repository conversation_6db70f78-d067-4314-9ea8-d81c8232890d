import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ApexOptions } from 'apexcharts';
import { format, parse } from 'date-fns';
import { GitCompareIcon, OctagonAlert } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useContext, useMemo } from 'react';
import { ThemeContext } from '@/theme/ThemeProvider';
const ReactApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
  loading: () => (
    <div className="text-center text-gray-500">Loading chart...</div>
  ),
});

type ComparisonChartProps = {
  disabled: boolean;
  data: any[];
};

export default function ComparisonChart({
  data,
  disabled,
}: ComparisonChartProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipContent>
          <p>Compare the companies</p>
        </TooltipContent>
        {data?.length > 10 && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <TooltipTrigger asChild>
                <Button variant="link" disabled={disabled} size="icon">
                  <GitCompareIcon className="text-black dark:text-white" />
                </Button>
              </TooltipTrigger>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex flex-row gap-x-2">
                  <OctagonAlert /> Limit Reached
                </AlertDialogTitle>
                <AlertDialogDescription>
                  You can only select up to 10 items.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction>Continue</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
        {data?.length <= 10 && (
          <Dialog modal={true}>
            <DialogTrigger asChild>
              <TooltipTrigger asChild>
                <Button variant="link" disabled={disabled} size="icon">
                  <GitCompareIcon className="text-black dark:text-white" />
                </Button>
              </TooltipTrigger>
            </DialogTrigger>
            <DialogContent className="grid h-[calc(100vh-5vh)] max-w-[calc(100vw-5vw)] grid-rows-[auto_1fr]">
              <DialogHeader className="h-auto">
                <DialogTitle>Comparesion of company </DialogTitle>
              </DialogHeader>
              <ApexChart data={data} />
            </DialogContent>
          </Dialog>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}

const ApexChart = ({ data }) => {
  const { dates, seriesData } = transformData(data);
  const { mode } = useContext(ThemeContext);
  const chartConfig: {
    series: { name: string; data: number[] }[];
    options: ApexOptions;
  } = {
    series: seriesData,
    options: {
      chart: {
        height: 350,
        type: 'line',
        zoom: {
          enabled: false,
        },
      },
      plotOptions: {
        bar: {
          borderRadius: 10,
        },
      },
      theme: {
        mode: mode === 'dark' ? 'dark' : 'light',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [5, 7, 5],
        curve: 'smooth',
        dashArray: [0, 8, 5],
      },
      title: {
        text: 'Page Statistics',
        align: 'left',
      },
      legend: {
        tooltipHoverFormatter: function (val, opts) {
          return (
            val +
            ' - <strong>' +
            opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] +
            '</strong>'
          );
        },
      },
      markers: {
        size: 0,
        hover: {
          sizeOffset: 6,
        },
      },
      xaxis: {
        categories: dates,
      },
      tooltip: {
        y: [
          {
            title: {
              formatter: function (val) {
                return val;
              },
            },
          },
          {
            title: {
              formatter: function (val) {
                return val;
              },
            },
          },
          {
            title: {
              formatter: function (val) {
                return val;
              },
            },
          },
        ],
      },
      grid: {
        borderColor: '#f1f1f1',
      },
    },
  };

  const { series, options } = useMemo(() => chartConfig, [dates, seriesData]);

  return (
    <div className="h-full w-full rounded-lg p-4 shadow-md">
      <ReactApexChart
        options={options}
        series={series}
        type="line"
        height="100%"
      />
    </div>
  );
};

const transformData = (input) => {
  const rawDates = input[0]?.counts.map((c) => c.fromDate) || [];
  const formattedDates = rawDates.map((dateStr) => {
    const parsedDate = parse(dateStr, 'yyyy_MM_dd', new Date());
    return format(parsedDate, 'd MMMM');
  });
  const seriesData = input.map((item) => ({
    name: item.company,
    data: item.counts.map((c) => c.count),
  }));

  return { seriesData, dates: formattedDates };
};
