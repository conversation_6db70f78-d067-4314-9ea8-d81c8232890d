import { alpha, lighten, darken } from '@mui/material';
import { createTheme, Theme } from '@mui/material/styles';
import { darkColors, lightColors } from './colors';
import { MyPaletteMode } from '@/models/theme/iBase';

export function themeCreator(theme: string, paletteMode: MyPaletteMode): Theme {
  const paletteColor = paletteMode === 'light' ? lightColors : darkColors;

  const defaultTheme = {
    spacing: 9,
    typography: {
      fontFamily:
        'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    },
    colors: {
      alpha: {
        white: {
          5: alpha(paletteColor.alpha.white[5], 0.02),
          10: alpha(paletteColor.alpha.white[10], 0.1),
          30: alpha(paletteColor.alpha.white[30], 0.3),
          50: alpha(paletteColor.alpha.white[50], 0.5),
          70: alpha(paletteColor.alpha.white[70], 0.7),
          100: paletteColor.alpha.white[100],
        },
        trueWhite: {
          5: alpha(paletteColor.alpha.white[5], 0.02),
          10: alpha(paletteColor.alpha.white[10], 0.1),
          30: alpha(paletteColor.alpha.white[30], 0.3),
          50: alpha(paletteColor.alpha.white[30], 0.5),
          70: alpha(paletteColor.alpha.white[70], 0.7),
          100: paletteColor.alpha.white[100],
        },
        black: {
          5: alpha(paletteColor.alpha.black[5], 0.02),
          10: alpha(paletteColor.alpha.black[10], 0.1),
          30: alpha(paletteColor.alpha.black[30], 0.3),
          50: alpha(paletteColor.alpha.black[50], 0.5),
          70: alpha(paletteColor.alpha.black[70], 0.7),
          100: paletteColor.alpha.black[100],
        },
      },
      secondary: {
        lighter: alpha(paletteColor.secondary.main, 0.1),
        light: lighten(paletteColor.secondary.main, 0.3),
        main: paletteColor.secondary.main,
        dark: darken(paletteColor.secondary.main, 0.2),
      },
      primary: {
        lighter: alpha(paletteColor.primary.main, 0.1),
        light: lighten(paletteColor.primary.main, 0.3),
        main: paletteColor.primary.main,
        dark: darken(paletteColor.primary.main, 0.2),
      },
    },
    sidebar: {
      boxShadow:
        '2px 0 3px rgba(159, 162, 191, .18), 1px 0 1px rgba(159, 162, 191, 0.32)',
      width: '200px',
    },
    header: {
      height: '60px',
      background: paletteColor.alpha.white[100],
      boxShadow:
        '2px 0 3px rgba(159, 162, 191, .18), 1px 0 1px rgba(159, 162, 191, 0.32)',
      textColor: paletteColor.secondary.main,
    },

    palette: {
      common: {
        black: paletteColor.alpha.black[100],
        white: paletteColor.alpha.white[100],
      },
      mode: paletteMode,
      primary: {
        light: paletteColor.primary.light,
        main: paletteColor.primary.main,
        dark: paletteColor.primary.dark,
      },
      secondary: {
        light: paletteColor.secondary.light,
        main: paletteColor.secondary.main,
        dark: paletteColor.secondary.dark,
        contrastText: paletteColor.alpha.black[70],
      },
      text: {
        primary: paletteColor.alpha.black[100],
        secondary: paletteColor.alpha.black[70],
        disabled: paletteColor.alpha.black[50],
      },
      background: { paper: paletteColor.alpha.white[100] },
      action: {
        active: paletteColor.alpha.black[100],
        hover: paletteColor.alpha.black[5],
        hoverOpacity: 0.1,
        selected: paletteColor.alpha.black[10],
        selectedOpacity: 0.1,
        disabled: paletteColor.alpha.black[50],
        disabledBackground: paletteColor.alpha.black[5],
        disabledOpacity: 0.38,
        focus: paletteColor.alpha.black[10],
        focusOpacity: 0.05,
        activatedOpacity: 0.12,
      },
      tonalOffset: 0.5,
    },
    // ... other theme properties
  };

  switch (theme) {
    case 'DefaultTheme':
      return createTheme(defaultTheme);
    // ... other cases
    default:
      return createTheme(); // fallback theme
  }
}
