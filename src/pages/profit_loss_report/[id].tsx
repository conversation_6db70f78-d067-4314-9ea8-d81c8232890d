import ProfitLostReport from '@/Modules/shipment/ProfitLostReport/ProfitLostReport';
// import { CONTAINERS } from '@/configs/leftSideMenu/Permissions';
import {
  mixNewColumns,
  ProfitLossReportType,
  TabFilterBy,
  tableColumns,
} from '@/configs/shipment/ProfitLost';
import SidebarLayout from '@/layouts/SidebarLayout';
// import { checkPagePermission } from '@/lib/permission-server-side';
import React from 'react';

function index({ params }) {
  const { id } = params;
  let apiUrl = '/containers/profitLoss';
  let reportType: ProfitLossReportType = ProfitLossReportType.OVERALL;
  let isMixTab: Boolean = false;
  let filterNoneClearance: Boolean = false;
  let tabFilterBy: TabFilterBy = {
    allUsaFull: false,
    allUsaMix: false,
    allUaeFull: false,
    allUaeMix: false,
    haveBookLoadCost: false,
    haveBookLoadCostReviewed: false,
    onlyMixContainers: false,
    onlyMixContainersReviewed: false,
    beforeEtd2025: false,
  };

  switch (id) {
    case 'overall_full':
      tabFilterBy.allUaeFull = true;
      break;
    case 'overall_mix':
      tabFilterBy.allUaeMix = true;
      isMixTab = true;
      break;
    case 'have_book_load':
      tabFilterBy.haveBookLoadCost = true;
      break;
    case 'have_book_load_reviewed':
      tabFilterBy.haveBookLoadCost = true;
      tabFilterBy.haveBookLoadCostReviewed = true;
      break;
    case 'only_mix_containers':
      tabFilterBy.onlyMixContainers = true;
      isMixTab = true;
      break;
    case 'only_mix_containers_reviewed':
      tabFilterBy.onlyMixContainers = true;
      tabFilterBy.onlyMixContainersReviewed = true;
      isMixTab = true;
      break;
    case 'overall_before_etd_2025':
      tabFilterBy.beforeEtd2025 = true;
      break;

    case 'all_usa_full':
      tabFilterBy.allUsaFull = true;
      reportType = ProfitLossReportType.USA;
      break;
    case 'all_usa_mix':
      tabFilterBy.allUsaMix = true;
      reportType = ProfitLossReportType.USA;
      isMixTab = true;
      break;
    case 'have_book_load_usa':
      tabFilterBy.haveBookLoadCost = true;
      reportType = ProfitLossReportType.USA;
      break;
    case 'have_book_load_reviewed_usa':
      tabFilterBy.haveBookLoadCost = true;
      tabFilterBy.haveBookLoadCostReviewed = true;
      reportType = ProfitLossReportType.USA;
      break;
    case 'only_mix_containers_usa':
      tabFilterBy.onlyMixContainers = true;
      isMixTab = true;
      reportType = ProfitLossReportType.USA;
      break;
    case 'only_mix_containers_reviewed_usa':
      tabFilterBy.onlyMixContainers = true;
      tabFilterBy.onlyMixContainersReviewed = true;
      isMixTab = true;
      reportType = ProfitLossReportType.USA;
      break;
    case 'before_etd_2025_usa':
      tabFilterBy.beforeEtd2025 = true;
      reportType = ProfitLossReportType.USA;
      break;

    case 'all_uae_full':
      tabFilterBy.allUaeFull = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      break;
    case 'all_uae_mix':
      tabFilterBy.allUaeMix = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      isMixTab = true;
      break;
    case 'have_book_load_uae':
      tabFilterBy.haveBookLoadCost = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      break;
    case 'have_book_load_reviewed_uae':
      tabFilterBy.haveBookLoadCost = true;
      tabFilterBy.haveBookLoadCostReviewed = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      break;
    case 'only_mix_containers_uae':
      tabFilterBy.onlyMixContainers = true;
      isMixTab = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      break;
    case 'only_mix_containers_reviewed_uae':
      tabFilterBy.onlyMixContainers = true;
      tabFilterBy.onlyMixContainersReviewed = true;
      isMixTab = true;
      reportType = ProfitLossReportType.UAE;
      filterNoneClearance = true;
      break;
    case 'before_etd_2025_uae':
      tabFilterBy.beforeEtd2025 = true;
      reportType = ProfitLossReportType.UAE;
      break;
    default:
      break;
  }

  const fixTableColums = tableColumns(reportType);
  let tableColumnsUpd;
  if (!isMixTab) {
    tableColumnsUpd = fixTableColums.filter(
      (column) =>
        !['additional_ch', 'additional_co', 'additional_pl'].includes(
          column.id,
        ),
    );
  } else {
    tableColumnsUpd = fixTableColums.filter(
      (column) =>
        ![
          'clearance_costs_ch',
          'clearance_costs_pl',
          'delivery_order_ch',
          'delivery_order_pl',
          'tran_ch',
          'tran_pl',
          'gross_pl_uae',
        ].includes(column.id),
    );
    const index = tableColumnsUpd.findIndex(
      (column) => column.id === 'tran_cost',
    );

    // If 'tran_cost' column is found, insert the new columns after it
    if (index !== -1) {
      mixNewColumns.forEach((newColumn, i) => {
        tableColumnsUpd.splice(index + i + 1, 0, newColumn);
      });
    }
  }

  return (
    <ProfitLostReport
      defaultHeaders={tableColumnsUpd}
      activeTab={id}
      apiUrl={apiUrl}
      reportType={reportType}
      isMixTab={isMixTab}
      filterNoneClearance={filterNoneClearance}
      tabFilterBy={tabFilterBy}
    />
  );
}

index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export async function getServerSideProps({ params }) {
  return {
    props: {
      params,
    },
  };
}

export default index;
