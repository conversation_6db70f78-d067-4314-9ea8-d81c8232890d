import TowingRates from '@/Modules/GeneralSetting/towing_rates/TowingRates';
import { towingRatesHeader } from '@/configs/general_setting/towingRatesHeader';
import { TOWING_RATES } from '@/configs/leftSideMenu/Permissions';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';

const TowingRatesPage = ({ params }) => {
  const { id } = params;

  // Validate the tab parameter
  const validTabs = ['all', 'pending', 'active', 'inactive'];
  const activeTab = validTabs.includes(id) ? id : 'all';

  let apiUrl = '/towing-rates';

  return (
    <TowingRates
      apiUrl={apiUrl}
      activeTab={activeTab}
      defaultHeaders={towingRatesHeader}
    />
  );
};

TowingRatesPage.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ params, req }) => {
  const result = await checkPagePermission({
    permission: TOWING_RATES?.VIEW,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  return {
    props: {
      params,
    },
  };
};

export default TowingRatesPage;
