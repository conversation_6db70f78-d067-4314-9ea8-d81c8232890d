import Consignees from '@/Modules/GeneralSetting/Consignees/Consignees';
import { companyConsigneesHeader } from '@/Modules/GeneralSetting/Consignees/consigneeComponents/consigneesHeader';
import { COMPANY_CONSIGNEES } from '@/configs/leftSideMenu/Permissions';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';

const CompanyConsignees = () => {
  let apiUrl = '/consignees';
  return (
    <Consignees apiUrl={apiUrl} defaultHeaders={companyConsigneesHeader} />
  );
};

CompanyConsignees.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ req }) => {
  const result = await checkPagePermission({
    permission: COMPANY_CONSIGNEES?.VIEW,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  return {
    props: {},
  };
};

export default CompanyConsignees;
