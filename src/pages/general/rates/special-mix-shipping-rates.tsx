import SpecialMixShippingRates from '@/Modules/GeneralSetting/mix_shipping_rates/SpecialMixShippingRates';
import { MIX_SHIPPING_RATES } from '@/configs/leftSideMenu/Permissions';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';
import { SpecialMixShippingRatesHeader } from '@/Modules/GeneralSetting/mix_shipping_rates/config/special_mix_shipping_rate_header';

const index = () => {
  let apiUrl = '/companies/special-mix-shipping-rates';
  return (
    <SpecialMixShippingRates
      apiUrl={apiUrl}
      defaultHeaders={SpecialMixShippingRatesHeader}
    />
  );
};

index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ req }) => {
  const result = await checkPagePermission({
    permission: MIX_SHIPPING_RATES?.VIEW,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  return {
    props: {},
  };
};

export default index;
