import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import { damageHeader } from '@/configs/vehicles_damage/damageHeader';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';
import NewDamage from '@/Modules/vehicle_damages/NewDamages';
import { useRouter } from 'next/router';

const Index = ({ params }) => {
  const router = useRouter();
  const { id } = router.query;

  // Use router.query.id as a fallback if params.id is undefined
  const activeTab = params?.id || id || 'all';

  let apiUrl = '/vehicle_damages';

  return (
    <NewDamage
      activeTab={activeTab}
      apiUrl={apiUrl}
      defaultHeaders={damageHeader}
    />
  );
};

Index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ req, params }) => {
  const result = await checkPagePermission({
    permission: DAMAGE?.VIEW,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  const { id } = params || {};
  let apiUrl = 'vehicle_damages';

  return {
    props: {
      params: {
        id: id || 'all',
      },
      apiUrl,
    },
  };
};

export default Index;
