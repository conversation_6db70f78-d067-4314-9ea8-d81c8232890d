import { DAMAGE } from '@/configs/leftSideMenu/Permissions';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';
import DamageReport from '@/Modules/vehicle_damages/report/DamageReport';

const Index = () => {
  return <DamageReport />;
};

Index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ req }) => {
  const result = await checkPagePermission({
    permission: DAMAGE?.REPORT_VIEW,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  return {
    props: {},
  };
};

export default Index;
