// // import NewDispatch from '@/Modules/vehicles/dispatch/NewDispatch';
// import { VEHICLES } from '@/configs/leftSideMenu/Permissions';
// import { dispatchHeader } from '@/configs/vehicles/dispatchHeader';
// import SidebarLayout from '@/layouts/SidebarLayout';
// import { checkPagePermission } from '@/lib/permission-server-side';

// const index = ({ params, apiUrl }) => {
//   const { id } = params;

//   // return <NewDispatch defaultHeaders={dispatchHeader} activeTab={id} apiUrl={apiUrl} />;
// };

// index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

// export async function getServerSideProps({ params, req }) {
//   const result = await checkPagePermission({
//     permission: VEHICLES.VIEW,
//     key: req.cookies.userKey,
//     token: req.cookies.token,
//   });

//   if (result) {
//     return result.response;
//   }

//   let apiUrl = 'vehicles/dispatch';

//   return {
//     props: {
//       params,
//       apiUrl,
//     },
//   };
// }

// export default index;

import React from 'react';

const DynamicPage = () => {
  return <div>[id]</div>;
};

export default DynamicPage;
