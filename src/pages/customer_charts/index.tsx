import CustomerReportChartDashboard from '@/Modules/customerReportChart/CustomerReportChartDashboard';
import { REPORTS } from '@/configs/leftSideMenu/Permissions';
import SidebarLayout from '@/layouts/SidebarLayout';
import { checkPagePermission } from '@/lib/permission-server-side';
import React from 'react';

const index = () => {
  let apiUrl = '/statistics/customerVolumeChart';
  return <CustomerReportChartDashboard apiUrl={apiUrl} />;
};

index.getLayout = (page) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = async ({ req }) => {
  const result = await checkPagePermission({
    permission: REPORTS?.VIEW_CHART,
    key: req.cookies.userKey,
    token: req.cookies.token,
  });

  if (result) {
    return result.response;
  }

  return {
    props: {},
  };
};

export default index;
