import Button from '@mui/material/Button';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import {
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Fade,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  OutlinedInput,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import LockResetIcon from '@mui/icons-material/LockReset';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/router';
import axios from '@/lib/axios';
import { useContext, useState } from 'react';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { contextProvider } from '@/contexts/ProfileContext';
import { contextProvider as locationContext } from '@/contexts/LocationsContext';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { setCookie } from '@/utils/cookieHelper';

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const usernameRegex = /^[\w-]+$/;

const schema = z.object({
  email: z
    .string()
    .transform((value) => value.trim())
    .refine(
      (value) => {
        return emailRegex.test(value) || usernameRegex.test(value);
      },
      {
        message: 'Valid email address or a string greater then 6 characters.',
      },
    ),
  password: z
    .string()
    .nonempty()
    .refine((val) => val.length > 0, {
      message: 'Password must Not be Empty',
    }),
});

type FormData = z.infer<typeof schema>;
export default function SignInSide() {
  const useProfileContext = useContext(contextProvider);
  const { refreshProfile } = useProfileContext;
  const useLocationContext = useContext(locationContext);
  const { refreshLocations } = useLocationContext;
  const [open, setOpen] = useState(false);
  const [forgotEmail, setForgotEmail] = useState('');
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_NEST_URL;
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      const res = await axios.post(`${baseUrl}auth/login`, {
        email_username: data.email,
        password: data.password,
      });

      if (res.status == 201) {
        refreshProfile();
        refreshLocations();
        localStorage.setItem('token', res.data.access_token);
        setCookie('token', res.data.access_token, 180 * 24 * 60 * 60);
        localStorage.setItem('userKey', res.data.id);
        setCookie('userKey', res.data.id, 180 * 24 * 60 * 60);
        const type = res.data.user_type;
        router.push(type == 'loader' ? '/loading-company' : '/profile');
      }
    } catch (e) {
      console.log(e);
      toast.error(e?.response?.data?.message);
    }
  };

  const handleForgotPassword = async () => {
    try {
      await axios.post(`${baseUrl}auth/forgot-password`, {
        email: forgotEmail,
      });
      toast.success('If the email exists, a reset link has been sent.');
      setOpen(false);
      setForgotEmail('');
    } catch (e) {
      toast.error('An error occurred. Please try again.');
    }
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  const theme = useTheme();
  const matches = useMediaQuery('(max-width:550px)');

  return (
    <Container
      component="main"
      maxWidth="lg"
      sx={{ height: '100vh', display: 'flex', alignItems: 'center' }}
    >
      <Box sx={{ width: '100%' }}>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            mb: 2,
            height: '130px',
          }}
        >
          <img
            style={{ width: !matches ? 'auto' : '230px' }}
            src={'/static/images/logo-500.webp'}
            alt={'PGL Logo'}
            loading="lazy"
          />
        </Box>
        <Grid container>
          <CssBaseline />
          <Grid
            sx={{
              backgroundImage: 'url(/static/images/auth/login_page_image.png)',
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              boxShadow:
                '0px 9px 16px rgba(159, 162, 191, .18), 0px 2px 2px rgba(159, 162, 191, 0.32)',
            }}
            size={{
              xs: false,
              sm: false,
              md: 7,
            }}
          />
          <Grid
            component={Paper}
            elevation={6}
            square
            size={{
              xs: 12,
              sm: 12,
              md: 5,
            }}
          >
            <Box sx={{ my: 8, mx: 4 }}>
              <Typography
                sx={{ mt: 3, mb: 2, textAlign: 'center' }}
                component="h1"
                variant="h2"
              >
                Sign In
              </Typography>
              <Typography sx={{ mb: 2, textAlign: 'center' }} component="h2">
                Welcome back to Admin PGL System 👋
              </Typography>
              <Box
                component="form"
                onSubmit={handleSubmit(onSubmit)}
                sx={{ mt: 1 }}
              >
                <TextField
                  sx={{ mb: 2 }}
                  size="small"
                  margin="normal"
                  fullWidth
                  placeholder="Email Address or Username"
                  autoFocus
                  {...register('email')}
                  error={!!errors.email}
                  helperText={errors.email?.message}
                />
                <FormControl variant="outlined" fullWidth>
                  <OutlinedInput
                    id="outlined-adornment-password"
                    placeholder="Password"
                    {...register('password')}
                    error={!!errors.password}
                    aria-describedby="outlined-weight-helper-text"
                    size="small"
                    type={showPassword ? 'text' : 'password'}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={() => setShowPassword((show) => !show)}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                  <FormHelperText
                    id="outlined-weight-helper-text"
                    sx={{ color: 'red' }}
                  >
                    {errors.password?.message}
                  </FormHelperText>
                </FormControl>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  sx={{ mt: 3, mb: 2 }}
                >
                  Sign In
                </Button>
                <Grid container>
                  <Grid size="grow">
                    <Box
                      sx={{ color: '#1a66d6', cursor: 'pointer' }}
                      onClick={() => setOpen(true)}
                    >
                      Forgot Password
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        aria-labelledby="forgot-password-dialog-title"
        aria-describedby="forgot-password-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: 2,
            width: '100%',
            maxWidth: '450px',
            borderTop: '4px solid #2e9e5b',
          },
        }}
      >
        <DialogTitle
          id="forgot-password-dialog-title"
          sx={{
            pt: 3,
            pb: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <LockResetIcon sx={{ fontSize: 40, color: '#2e9e5b', mb: 2 }} />
          <Typography variant="h5" component="div" fontWeight={600}>
            Forgot Password
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            textAlign="center"
            sx={{ mt: 1 }}
          >
            Enter your email address below and we'll send you instructions to
            reset your password
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 2, pb: 1, px: 3 }}>
          <TextField
            autoFocus
            margin="normal"
            id="email"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={forgotEmail}
            onChange={(e) => setForgotEmail(e.target.value)}
            sx={{ my: 1 }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {forgotEmail && emailRegex.test(forgotEmail) && (
                    <Fade in={true}>
                      <CheckCircleOutlineIcon sx={{ color: '#2e9e5b' }} />
                    </Fade>
                  )}
                </InputAdornment>
              ),
            }}
            placeholder="<EMAIL>"
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 3, flexDirection: 'column' }}>
          <Button
            onClick={handleForgotPassword}
            variant="contained"
            disabled={!forgotEmail || !emailRegex.test(forgotEmail)}
            fullWidth
            sx={{
              py: 1.5,
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              bgcolor: '#2e9e5b',
              '&:hover': { bgcolor: '#267c47' },
              mb: 2,
            }}
          >
            Send Reset Link
          </Button>
          <Button
            onClick={() => setOpen(false)}
            variant="text"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              color: 'text.secondary',
              '&:hover': {
                bgcolor: 'transparent',
                textDecoration: 'underline',
              },
            }}
          >
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme={theme.palette.mode}
      />
    </Container>
  );
}
