import { ReactElement, ReactNode, useEffect } from 'react';
import type { NextPage } from 'next';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import Router from 'next/router';
import nProgress from 'nprogress';
import 'nprogress/nprogress.css';
import '@/app/tailwind-global.css';
import ThemeProvider from 'src/theme/ThemeProvider';
import CssBaseline from '@mui/material/CssBaseline';
import createCache, { EmotionCache } from '@emotion/cache';
import { CacheProvider } from '@emotion/react';
import { SidebarProvider } from 'src/contexts/SidebarContext';
import { PBProvider } from 'src/contexts/PBProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers';
import '@/assets/css/global.css';
import { appWithTranslation } from 'next-i18next';
import { I18nextProvider } from 'react-i18next';
import i18n from 'i18n';
import LocationsProvider from '@/contexts/LocationsContext';
import ProfileProvider from '@/contexts/ProfileContext';
import { SocketContextProvider } from '@/contexts/SocketContext';
import { RemindersContextProvider } from '@/contexts/RemindersContext';
import { ThemeProvider as ShadcnThemeProvider } from '@/components/app/provoders/theme-context';
import { UploadQueueProvider } from '@/contexts/UploadQueueContext';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'], display: 'swap' });

const cache = createCache({ key: 'next' });

type NextPageWithLayout = NextPage & {
  getLayout?: (page: ReactElement) => ReactNode;
};

interface PglProps extends AppProps {
  emotionCache?: EmotionCache;
  Component: NextPageWithLayout;
}

function Pgl(props: PglProps) {
  const { Component, emotionCache = cache, pageProps } = props;
  const getLayout = Component.getLayout ?? ((page) => page);

  Router.events.on('routeChangeStart', nProgress.start);
  Router.events.on('routeChangeError', nProgress.done);
  Router.events.on('routeChangeComplete', nProgress.done);

  useEffect(() => {
    const dir = i18n.dir();
    document.documentElement.setAttribute('dir', dir);
  }, [i18n.language]);

  return (
    <div className={inter.className}>
      <ShadcnThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <PBProvider>
          <CacheProvider value={emotionCache}>
            <Head>
              <title>PGL System Admin Dashboard</title>
              <meta
                name="viewport"
                content="width=device-width, initial-scale=1, shrink-to-fit=no"
              />
            </Head>
            <UploadQueueProvider>
              <ProfileProvider>
                <SocketContextProvider>
                  <RemindersContextProvider>
                    <LocationsProvider>
                      <SidebarProvider>
                        <ThemeProvider>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <I18nextProvider i18n={i18n}>
                              <CssBaseline />
                              {getLayout(<Component {...pageProps} />)}
                            </I18nextProvider>
                          </LocalizationProvider>
                        </ThemeProvider>
                      </SidebarProvider>
                    </LocationsProvider>
                  </RemindersContextProvider>
                </SocketContextProvider>
              </ProfileProvider>
            </UploadQueueProvider>
          </CacheProvider>
        </PBProvider>
      </ShadcnThemeProvider>
    </div>
  );
}

export default appWithTranslation(Pgl);
