import { useContext, useEffect } from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';

import Header from './Header';
import 'react-toastify/dist/ReactToastify.css';
import withAuth from '../auth/AuthWrapper';
import { SidebarContext } from 'src/contexts/SidebarContext';

import { ToastContainer } from 'react-toastify';
import { contextProvider } from '@/contexts/ProfileContext';
import { AppSidebarCp } from '@/components/app/page-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

interface SidebarLayoutProps {
  children?: React.ReactNode;
}

const SidebarLayout = ({ children }: SidebarLayoutProps) => {
  const { getSidebarCounts } = useContext(SidebarContext);
  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;

  const type = profile?.data?.loginable?.loginable_type;

  const theme = useTheme();

  const matches = useMediaQuery('(max-width:900px)');
  useEffect(() => {
    if (type) {
      getSidebarCounts({ key: 'all' });
    }
  }, [type]);

  return (
    <>
      <SidebarProvider>
        <AppSidebarCp />
        <SidebarInset>
          <Header />
          <Box
            sx={{
              background: theme.colors.alpha.white[100],
              color: theme.colors.alpha.white,
              position: 'absolute',
              zIndex: 5,
              display: 'block',
              flex: 1,
              pt: `${matches ? theme.header.height : '0px'}`,
              inset: 0,
              bottom: '10px',
            }}
          >
            <Box display="block">{children}</Box>
          </Box>
        </SidebarInset>

        <ToastContainer
          position="bottom-right"
          autoClose={1500}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme={theme.palette.mode}
        />
      </SidebarProvider>
    </>
  );
};

export default withAuth(SidebarLayout);
