import * as moment from 'moment';

const excludedKeys = [
  'dispatch_department',
  'receiver_name',
  'ach',
  'is_key_present',
  'vehicles_with_cover_photos',
  'vehicles_with_photos',
  'vehicles_with_auction_photos',
  'past_due_days',
  'vehicles_with_aes_filling_link',
  'vehicles_with_auction_invoice_link',
  'vehicles_with_delivery_photos',
];

export async function searchFilter(
  query: any,
  searchColumns: Array<any>,
  forCount: boolean = false,
  isArrayField: (fieldName: string) => boolean = () => false,
  modelName: string = '',
  state: string = '',
) {
  const searchConds = await search(
    query,
    searchColumns,
    forCount,
    isArrayField,
    modelName,
    state,
  );
  const filterConds = await filter(query.filterData, modelName, state);

  if (filterConds.length > 0 && searchConds?.length > 0) {
    filterConds.push({ OR: searchConds });
    return { deleted_at: null, deleted_by: null, AND: filterConds };
  } else if (searchConds.length > 0) {
    return { deleted_at: null, deleted_by: null, OR: searchConds };
  } else if (filterConds.length > 0) {
    return { deleted_at: null, deleted_by: null, AND: filterConds };
  } else {
    return { deleted_at: null, deleted_by: null };
  }
}

const filter = (
  filterData: any,
  modelName: string = '',
  state: string = '',
) => {
  const filterConds: any[] = [];
  if (filterData) {
    const filterParams = JSON.parse(filterData);
    if (filterParams.eta_status) delete filterParams.eta_status; // Remove eta_status
    if (filterParams.eta) delete filterParams.eta; // Remove eta
    const companyIds = filterParams?.company_id;
    const departmentIds =
      filterParams?.['users_vehicles_created_byTousers.department_id'];
    if (filterParams?.include_exclude_company) {
      delete filterParams?.company_id;
    }
    if (filterParams?.include_exclude_department) {
      delete filterParams?.['users_vehicles_created_byTousers.department_id'];
    }
    let key: string;
    let value: any = null;
    for ([key, value] of Object.entries(filterParams)) {
      if (excludedKeys.includes(key)) continue;
      if (key === 'include_exclude_company') {
        const includeExcludeCompanyValue = value[0];
        if (includeExcludeCompanyValue === 'exclude') {
          filterConds.push({ company_id: { notIn: companyIds } });
        } else {
          filterConds.push({ company_id: { in: companyIds } });
        }
        continue;
      }
      if (key === 'include_exclude_department') {
        const includeExcludeDepartmentValue = value[0];
        if (includeExcludeDepartmentValue === 'exclude') {
          filterConds.push({
            users_vehicles_created_byTousers: {
              department_id: { notIn: departmentIds },
            },
          });
        } else {
          filterConds.push({
            users_vehicles_created_byTousers: {
              department_id: { in: departmentIds },
            },
          });
        }
        continue;
      }

      const cols = key.split('.').reverse();
      const col = cols[0];
      const colName = getColName(col);
      cols.shift();
      if (col.startsWith('int')) value = +value;
      else if (col.startsWith('bool')) value = checkBool(value);
      else if (col.startsWith('null')) {
        const boolValue = checkBool(value);
        value = boolValue ? { not: null } : null;
      } else if (col.startsWith('date')) {
        const d = new Date(value);
        d.setDate(d.getDate() + 1);
        value = { gte: new Date(value), lt: new Date(d) };
      } else if (Array.isArray(value)) {
        if (colName == 'OR') {
          const temp = [];
          value.forEach(async (item) => {
            const items = filter(JSON.stringify(item));
            temp.push(...items);
          });
          value = temp;
        } else {
          const v = value.filter((e) => e !== 'null' && e !== '' && e !== null);
          value = v.length > 0 ? { in: v } : null;
        }
      } else if (['is_null', 'not_null'].includes(value)) {
        value = value == 'not_null' ? { not: null } : null;
      } else if (typeof value === 'object' && value === null) value = null;
      else if (typeof value === 'object' && 'min' in value && 'max' in value)
        value = { gte: +value.min, lte: +value.max };
      else if (typeof value === 'object' && 'max' in value)
        value = { lte: +value.max };
      else if (typeof value === 'object' && 'min' in value)
        value = { gte: +value.min };
      else if (typeof value === 'object' && 'from' in value && 'to' in value) {
        const startOfDay = toUTCWithTime(value.from, 0, 0, 0, 0);
        const endOfDay = toUTCWithTime(value.to, 23, 59, 59, 999);
        value = {
          gte: startOfDay,
          lte: endOfDay,
        };
      } else if (typeof value === 'object' && 'from' in value) {
        // Set the 'from' time to the start of the day
        const startOfDay = new Date(value.from);
        startOfDay.setHours(0, 0, 0, 0);
        value = {
          gte: new Date(value.from).toISOString(),
        };
      } else if (typeof value === 'object' && 'to' in value) {
        // Set the 'to' time to the end of the day
        const endOfDay = new Date(value.to);
        endOfDay.setHours(23, 59, 59, 999);
        value = {
          lte: endOfDay.toISOString(),
        };
      } else if (
        typeof value === 'object' &&
        colName == 'OR' &&
        'and' in value
      ) {
        if (Array.isArray(value.value1)) {
          const orCond = [
            {
              AND: [
                { [value.col_name1]: { in: value.value1 } },
                { [value.and.col_name]: value.and.value },
              ],
            },
            {
              [value.col_name2]: { in: value.value2 },
            },
          ];
          value = orCond;
        } else {
          value = null;
        }
      } else if (
        typeof value === 'object' &&
        colName == 'OR' &&
        'in' in value
      ) {
        if (Array.isArray(value.in)) {
          const v = value.in.filter(
            (e) => e !== 'null' && e !== '' && e !== null,
          );
          const orCond = [
            {
              [value.col_name]: { in: v },
            },
          ];
          if (value.in.includes(null)) {
            orCond.push({
              [value.col_name]: null,
            });
          }
          value = orCond;
        } else {
          value = null;
        }
      }
      let conds = { [colName]: value };

      if (cols.length > 0) {
        cols.forEach((e: any) => {
          if (e.startsWith('many')) {
            conds = {
              [getColName(e)]: {
                some: {
                  ...conds,
                  ...(modelName === 'payments' && state !== 'trash'
                    ? { deleted_at: null, deleted_by: null }
                    : {}),
                },
              },
            };
          } else {
            conds = { [e]: conds };
          }
        });
      }
      filterConds.push(conds);
    }
  }

  const finalConds = filterConds.flatMap((condition) => {
    const payments = condition?.payments?.some?.type?.in;
    if (payments) {
      return [
        {
          OR: [
            condition,
            {
              payment_cards: {
                some: {
                  type: {
                    in: payments,
                  },
                },
              },
            },
            payments.includes('non_allocated')
              ? {
                  payments: {
                    none: {},
                  },
                  payment_cards: {
                    none: {},
                  },
                }
              : {},
          ],
        },
      ];
    }
    return [condition];
  });
  return finalConds;
};

const search = async (
  query: any,
  searchColumns?: Array<any>,
  forCount: boolean = false,
  isArrayField: (fieldName: string) => boolean = () => false,
  modelName: string = '',
  state: string = '',
) => {
  const searchWord = query.search;
  const exactMatch = query.exactMatch;
  const searchConds = [];

  if (searchWord) {
    await searchColumns.forEach((c) => {
      const cols = c.split('.').reverse();
      const col = cols[0];
      const colName = getColName(col);

      let value: any;
      if (
        col.startsWith('int') &&
        checkInt(searchWord) &&
        searchWord.length < 10
      ) {
        value = +searchWord;
      } else if (
        col.startsWith('date') &&
        moment(searchWord, 'YYYY-MM-DD', true).isValid()
      ) {
        value = new Date(searchWord);
      } else if (!col.includes('@@')) {
        if (isArrayField(colName)) {
          value = { has: searchWord };
        } else if (forCount) {
          value = searchWord;
        } else {
          value =
            exactMatch == true || exactMatch == 'true'
              ? searchWord
              : { contains: searchWord, mode: 'insensitive' };
        }
      }

      if (value != undefined) {
        let conds = { [colName]: value };
        cols.shift();
        cols.forEach((e: any) => {
          if (e.startsWith('many')) {
            conds = {
              [getColName(e)]: {
                some: {
                  ...conds,
                  ...(modelName === 'payments' && state !== 'trash'
                    ? { deleted_at: null, deleted_by: null }
                    : {}),
                },
              },
            };
          } else {
            conds = { [e]: conds };
          }
        });
        searchConds.push(conds);
      }
    });
  }

  return searchConds;
};

const getColName = (col: string) => {
  if (col.includes('@@')) return col.slice(col.indexOf('@@') + 2);
  else return col;
};
const checkInt = (intString: string) => Number.isInteger(+intString);
const checkBool = (boolString: string) => (boolString == 'true' ? true : false);

function toUTCWithTime(
  input: string | Date,
  hour = 0,
  minute = 0,
  second = 0,
  ms = 0,
): string {
  const date = new Date(input);
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date input');
  }
  return new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      hour,
      minute,
      second,
      ms,
    ),
  ).toISOString();
}
