body {
  font-size: '12px !important';
}

.htmlDescription p,
h1,
h2,
h3 {
  margin: 0 !important;
}
.htmlDescription img {
  max-width: 100% !important;
}
a {
  text-decoration: none;
  width: 100%;
}
a:hover {
  cursor: pointer;
}
.custom .MuiTableCell-root.MuiTableCell-head {
  padding: 0px 0px !important;
  background-color: #3f50b5 !important;
  color: azure !important;
}
.custom.dark .MuiTableRow-hover:hover {
  background-color: rgba(250, 250, 250, 0.077) !important;
}
.custom .MuiTableRow-hover:hover {
  background-color: rgba(160, 160, 160, 0.1) !important;
}

.tss-1fbujeu-MUIDataTableHeadCell-toolButton {
  margin: 0 !important;
  padding: 5px 16px !important;
}
[data-description='row-select-header'] {
  color: azure !important;
}
.MuiStepLabel-label {
  margin-top: 0 !important;
  font-size: 12px !important;
}
.scrollSideBar {
  height: 93vh !important;
}
.MuiCardContent-root:last-child {
  padding-bottom: 9px !important;
}
.transform-component-module_wrapper__SPB86 {
  width: 100% !important;
  height: 95vh !important;
  background-color: #000;
}
.transform-component-module_content__FBWxo {
  width: fit-content !important;
}
.swiper-pagination-fraction {
  color: #fff !important;
}
.next-1kalzck-MuiTableCell-root {
  text-align: center !important;
}
/* .custom-modal .mantine-rtl-Modal-inner {
  z-index: 10000;
  position: fixed;
}

.custom-modal .mantine-rtl-Modal-modal {
  padding: 0;
  background: transparent;
}

.custom-modal .mantine-rtl-Modal-close {
  margin-right: 20px;
  margin-top: 20px;
}

.custom-modal .mantine-rtl-Stepper-stepBody {
  margin-top: 14px;
} */

/* .custom-modal .close-btn {
  position: absolute;
  top: 16px;
  left: 16px;
} */

.scrollBar ::-webkit-scrollbar {
  width: 10px;
}

/* Track */
.scrollBar ::-webkit-scrollbar-track {
  background: inherit;
}

/* Handle */
.scrollBar ::-webkit-scrollbar-thumb {
  transition: all ease-in 0.5s;
  border-radius: 5px;
  background: #888;
}

/* Handle on hover */
.scrollBar ::-webkit-scrollbar-thumb:hover {
  transition: all ease-in 0.5s;
  background: #555;
}

::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: inherit;
}

/* Handle */
::-webkit-scrollbar-thumb {
  transition: all ease-in 0.5s;
  border-radius: 5px;
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  transition: all ease-in 0.5s;
  background: #555;
}

.stpper-sidebar {
  background: rgba(200, 200, 200, 0.7);
  backdrop-filter: blur(10px);
  transition: all 0.5s !important;
}

.stpper-sidebar.dark {
  background: rgba(0, 0, 0, 0.3);
}

.stepper-body-footer {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
}

.stepper-item {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  transform: rotateX(0);
  opacity: 1;
  transition: all 0.7s;
  z-index: 1;
}

.hide {
  transform: rotateX(180deg);
  opacity: 0;
  z-index: 0;
}
.stepper-content .Mui-disabled.MuiStepConnector-root,
.stepper-content .Mui-active.MuiStepConnector-root,
.stepper-content .Mui-completed.MuiStepConnector-root {
  display: none !important;
}

.nasim .Mui-disabled {
  display: unset !important;
}
.nasim .MuiAutocomplete-input {
  min-width: 250px !important;
}

.MuiStepConnector-line.MuiStepConnector-lineVertical {
  min-height: 35px;
}
.MuiStep-vertical .MuiStepContent-root {
  margin-left: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  border-left: none !important;
}
.MuiStepConnector-vertical.Mui-disabled,
.MuiStepConnector-vertical.Mui-active,
.MuiStepConnector-vertical.Mui-completed {
  margin-left: 9px !important;
}
.MuiOutlinedInput-root.MuiInputBase-sizeSmall,
.MuiInputLabel-formControl.MuiInputLabel-animated.MuiInputLabel-outlined.MuiFormLabel-colorPrimary.MuiInputLabel-root.MuiInputLabel-formControl.MuiInputLabel-animated.MuiInputLabel-outlined {
  font-size: 12px;
}
.MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputAdornedEnd[inputmode='tel'],
.MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputAdornedEnd[inputmode='text'] {
  padding: 6px 0 10px 16px !important;
}

.DatePickerCustom
  .MuiFormLabel-root.MuiInputLabel-root.MuiInputLabel-formControl.MuiInputLabel-animated.MuiInputLabel-outlined.MuiFormLabel-colorPrimary.MuiInputLabel-root.MuiInputLabel-formControl.MuiInputLabel-animated.MuiInputLabel-outlined {
  top: -6px !important;
}

.DatePickerCustom {
  margin-top: 10px !important;
}
.myTableSortLable.Mui-active,
.myTableSortLable.Mui-active .MuiTableSortLabel-icon {
  color: white !important;
}
.mySwitch .MuiSwitch-track {
  background-color: gray !important;
}
.mySwitch .Mui-checked + .MuiSwitch-track {
  background-color: #3f50b5 !important;
}
.MuiBox-root:focus-visible {
  outline: unset !important;
}
.customTab .MuiTabs-hideScrollbar.MuiTabs-scrollableX {
  height: 35px;
}
.customTab .MuiTabs-flexContainer {
  height: 35px;
}
.customTab .MuiTab-root.MuiTab-textColorInherit[type='button'] {
  min-height: 35px !important;
}

.ListItemCustom .MuiListItemSecondaryAction-root {
  right: 0 !important;
}
.MuiFormHelperText-root.Mui-error.MuiFormHelperText-sizeSmall.MuiFormHelperText-contained {
  margin-top: 0 !important;
  font-size: 10px !important;
}
.cBadge span {
  font-size: 7px !important;
  bottom: 12px;
  right: 6px;
  height: 15px;
}

.quill > .ql-container > .ql-editor.ql-blank::before {
  font-size: 14px;
  font-style: normal;
  color: #ddddddb3;
}
.MuiTableCell-root.MuiTableCell-body.MuiTableCell-sizeSmall {
  line-height: 1 !important;
  padding: 5px 3px !important;
  font-size: 12px !important;
}

.my-custom-table .MuiTableCell-root.MuiTableCell-body.MuiTableCell-sizeSmall {
  padding: 5px 0px !important; /* Overrides only inside .my-custom-table */
}

.iceberg.light-mode .Mui-disabled {
  -webkit-text-fill-color: rgba(0, 0, 0, 1) !important;
}

.iceberg.dark-mode .Mui-disabled {
  -webkit-text-fill-color: rgba(255, 255, 255, 1) !important;
}
