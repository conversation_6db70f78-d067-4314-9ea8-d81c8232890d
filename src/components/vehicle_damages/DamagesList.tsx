import DamageFields from './DamageFields';
import { Button, Box } from '@mui/material';

// Simple PageActions component as a replacement
const PageActions = () => (
  <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
    <Button variant="contained" color="primary">
      Save
    </Button>
    <Button variant="outlined">Cancel</Button>
  </Box>
);

interface DamagesListProps {
  tabs: { label: string; value: string }[];
  activeTab: string;
  disableEditing?: boolean;
  hideActionButtons?: boolean;
}

const DamagesList: React.FC<DamagesListProps> = ({
  // tabs,
  // activeTab,
  disableEditing = false,
  hideActionButtons = false,
}) => {
  return (
    <div>
      {!hideActionButtons && <PageActions />}

      <DamageFields disabled={disableEditing} />
    </div>
  );
};

export default DamagesList;
