'use client';

import { cn } from '@/lib/utils';
import { Command as CommandPrimitive } from 'cmdk';
import { Check, X } from 'lucide-react';
import { useEffect, useRef, useState, useCallback } from 'react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverAnchor,
  PopoverContent,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from './badge';
import { useDebouncedState } from '@/hooks/use-debounced-state';

type Item = {
  value: string;
  label: string;
};

type Props<T extends string> = {
  selectedValues: T[];
  onSelectedValuesChange: (values: T[]) => void;
  getData: (search: string) => Promise<Item[]>;
  emptyMessage?: string;
  placeholder?: string;
  className?: string;
  defaultValue?: Item[];
};

export function MultiSelect<T extends string>({
  selectedValues,
  onSelectedValuesChange,
  emptyMessage = 'No items.',
  placeholder = 'Search...',
  className = '',
  getData,
  defaultValue = [],
}: Props<T>) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useDebouncedState('', 500);
  const [searchValue, setSearchValue] = useState('');
  const [items, setItems] = useState<Item[]>(defaultValue);
  const [loading, setLoading] = useState(false);

  const labelCache = useRef<Record<string, string>>({});

  useEffect(() => {
    if (defaultValue && defaultValue.length > 0) {
      setItems((prev) => {
        const existing = new Set(prev.map((i) => i.value));
        const combined = [...prev];
        defaultValue.forEach((item) => {
          if (!existing.has(item.value)) {
            combined.push(item);
          }
        });
        return combined;
      });
    }
  }, [defaultValue]);

  useEffect(() => {
    if (!search) return;
    setLoading(true);
    getData(search)
      .then((data) => setItems(data))
      .finally(() => setLoading(false));
  }, [search, getData]);

  useEffect(() => {
    items.forEach((item) => {
      labelCache.current[item.value] = item.label;
    });
  }, [items]);

  useEffect(() => {
    setSearch(searchValue);
  }, [searchValue]);

  const onSelectItem = useCallback(
    (inputValue: string) => {
      const value = inputValue as T;
      const newValues = selectedValues.includes(value)
        ? selectedValues.filter((v) => v !== value)
        : [...selectedValues, value];

      onSelectedValuesChange(newValues);
      setSearchValue('');
    },
    [selectedValues, onSelectedValuesChange],
  );

  return (
    <div className={cn('flex w-full flex-col gap-2', className)}>
      <Popover open={open} onOpenChange={setOpen} modal>
        <Command shouldFilter={false}>
          <PopoverAnchor asChild>
            <div
              className={cn(
                'flex max-h-32 min-h-10 flex-wrap items-center gap-1 overflow-auto rounded-lg border bg-background px-2 py-1 ring-1 ring-muted',
                className,
              )}
              onClick={() => setOpen(true)}
            >
              {selectedValues.map((val) => (
                <Badge
                  key={val}
                  className="flex items-center gap-1 rounded-full px-2 py-1 text-sm"
                  variant="secondary"
                >
                  {labelCache.current[val] ?? val}
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectedValuesChange(
                        selectedValues.filter((v) => v !== val),
                      );
                    }}
                  >
                    <X className="h-4 w-4 hover:stroke-destructive" />
                  </button>
                </Badge>
              ))}
              <CommandPrimitive.Input asChild value={searchValue}>
                <input
                  placeholder={placeholder}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (
                      e.key === 'Backspace' &&
                      searchValue === '' &&
                      selectedValues.length > 0
                    ) {
                      onSelectedValuesChange(selectedValues.slice(0, -1));
                    }
                    if (e.key === 'Escape') {
                      setOpen(false);
                    }
                  }}
                  className="flex-grow bg-transparent p-1 text-sm outline-none placeholder:text-muted-foreground"
                />
              </CommandPrimitive.Input>
            </div>
          </PopoverAnchor>

          <PopoverContent
            asChild
            onOpenAutoFocus={(e) => e.preventDefault()}
            onInteractOutside={(e) => {
              if (
                e.target instanceof Element &&
                e.target.hasAttribute('cmdk-input')
              ) {
                e.preventDefault();
              }
            }}
            avoidCollisions={false}
            className="pointer-events-auto z-50 w-[--radix-popover-trigger-width] p-0"
            side="bottom"
            align="start"
          >
            <CommandList className="max-h-60 overflow-y-auto p-2">
              {loading ? (
                <CommandPrimitive.Loading>
                  <div className="p-1">
                    <Skeleton className="h-6 w-full" />
                  </div>
                </CommandPrimitive.Loading>
              ) : items.length > 0 ? (
                <CommandGroup>
                  {items.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={onSelectItem}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          selectedValues.includes(option.value as T)
                            ? 'opacity-100'
                            : 'opacity-0',
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              ) : (
                <CommandEmpty>{emptyMessage}</CommandEmpty>
              )}
            </CommandList>
          </PopoverContent>
        </Command>
      </Popover>
    </div>
  );
}
