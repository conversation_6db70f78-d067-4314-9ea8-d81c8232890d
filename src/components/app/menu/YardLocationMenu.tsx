import { YARD_LOCATIONS } from '@/configs/leftSideMenu/Permissions';
import DescriptionIcon from '@mui/icons-material/Description';
import PaidIcon from '@mui/icons-material/Paid';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import LibraryAddCheckIcon from '@mui/icons-material/LibraryAddCheck';
import ArchiveIcon from '@mui/icons-material/Archive';
import WarehouseIcon from '@mui/icons-material/Warehouse';
const YardLocationMenu = () => {
  return {
    title: 'Yard Location',
    permission: [YARD_LOCATIONS.VIEW],
    mainMenu: [
      {
        name: 'Yard Location',
        key: 'yard_location',
        to: '/yard_location/all',
        permission: [YARD_LOCATIONS.VIEW],
        icon: <DescriptionIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Profiles',
            key: 'yardLocationProfiles',
            to: '/yard_locations',
            permission: [YARD_LOCATIONS.VIEW],
            icon: <WarehouseIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Rates',
            key: 'yard_location_rate',
            permission: [YARD_LOCATIONS.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Pendings',
                key: 'pending_rate',
                to: '/yard_locations/rates/pending',
                permission: [YARD_LOCATIONS.VIEW],
                icon: <AutoModeIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Actives',
                key: 'active_rate',
                to: '/yard_locations/rates/active',
                permission: [YARD_LOCATIONS.VIEW],
                icon: <LibraryAddCheckIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Archived',
                key: 'archived_rate',
                to: '/yard_locations/rates/archived',
                permission: [YARD_LOCATIONS.VIEW],
                icon: <ArchiveIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
        ],
      },
    ],
  };
};

export default YardLocationMenu;
