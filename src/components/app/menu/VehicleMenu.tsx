import { DAMAGE, VEHICLES } from '@/configs/leftSideMenu/Permissions';
import CarRepairIcon from '@mui/icons-material/CarRepair';
import DepartureBoardIcon from '@mui/icons-material/DepartureBoard';
import CarRentalIcon from '@mui/icons-material/CarRental';
import MinorCrashIcon from '@mui/icons-material/MinorCrash';
import InventoryIcon from '@mui/icons-material/Inventory';
import TaxiAlertIcon from '@mui/icons-material/TaxiAlert';
import PlaylistAddCheckCircleIcon from '@mui/icons-material/PlaylistAddCheckCircle';
import PriceChangeIcon from '@mui/icons-material/PriceChange';
import ManageHistoryIcon from '@mui/icons-material/ManageHistory';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import PaidIcon from '@mui/icons-material/Paid';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';
import AssessmentIcon from '@mui/icons-material/Assessment';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
import FmdGoodIcon from '@mui/icons-material/FmdGood';
import GavelIcon from '@mui/icons-material/Gavel';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import { locations } from './MainMenu';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import TaskAltIcon from '@mui/icons-material/TaskAlt';
import QueryStatsIcon from '@mui/icons-material/QueryStats';
import AddTaskIcon from '@mui/icons-material/AddTask';
import {
  Cached,
  CarCrash,
  CreditCardOff,
  MinorCrash,
  CreditScore,
  AddCard,
  CancelPresentation,
  RotateLeft,
  PendingActions,
  WorkHistory,
} from '@mui/icons-material';
import AssignmentIcon from '@mui/icons-material/Assignment';
//import FlightLandIcon from '@mui/icons-material/FlightLand';
//import DockIcon from '@mui/icons-material/Dock';
import PendingActionsIcon from '@mui/icons-material/PendingActions';

const VehicleMenu = ({ data }) => {
  const vehicleSummarylocations = (item, to) => {
    return item
      ? {
          name: item?.name,
          key: item?.name,
          icon: <FmdGoodIcon sx={{ fontSize: '16px' }} />,
          to: `${to}${item?.name}`,
          ...(item?.yards_locations?.length > 0 && {
            subMenu: [
              {
                name: 'All',
                key: item?.name,
                icon: <FmdGoodIcon sx={{ fontSize: '16px' }} />,
                to: `${to}${item?.name}`,
              },
            ].concat(
              item?.yards_locations?.map((item2) =>
                locations2(
                  item2,
                  `/vehicles/vehicle_summary/${item.name}?yard_location=${item2.id}`,
                ),
              ),
            ),
          }),
        }
      : {
          name: '',
          key: '',
          icon: <FmdGoodIcon sx={{ fontSize: '16px' }} />,
          to: '/',
        };
  };

  const locations2 = (item, to) => {
    return item
      ? {
          name: item?.name,
          key: item?.name,
          icon: <FmdGoodIcon sx={{ fontSize: '16px' }} />,
          to: `${to}`,
        }
      : {
          name: '',
          key: '',
          icon: <FmdGoodIcon sx={{ fontSize: '16px' }} />,
          to: '/',
        };
  };

  return {
    title: 'Vehicles',
    permission: [VEHICLES.VIEW, DAMAGE.VIEW],
    mainMenu: [
      {
        name: 'Vehicles',
        key: 'vehicles',
        to: '/vehicles/all',
        permission: [VEHICLES.VIEW],
        icon: <DirectionsCarIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Tow Cost Report',
            key: 'tow_cost_report',
            to: '/vehicles/tow_cost_report',
            permission: [VEHICLES.VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'All',
            key: 'vehicles',
            to: '/vehicles/all',
            permission: [VEHICLES.VIEW],
            icon: <DirectionsCarIcon sx={{ fontSize: '16px' }} />,
          },

          {
            name: 'Auction',
            key: 'auction',
            to: '/vehicles/auction_paid',
            permission: [VEHICLES.AUCTION_VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Pending Auction',
                key: 'pending_auction',
                to: '/vehicles/pending_auction',
                permission: [VEHICLES.PENDING_AUCTION_VIEW],
                icon: <GavelIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Auction Unpaid',
                key: 'auction_unpaid',
                to: '/vehicles/auction_unpaid',
                permission: [VEHICLES.AUCTION_VIEW],
                icon: <MoneyOffIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Auction Paid',
                key: 'auction_paid',
                to: '/vehicles/auction_paid',
                permission: [VEHICLES.AUCTION_VIEW],
                icon: <PaidIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },

          // {
          //   name: 'Dispatch Report',
          //   key: 'dispatch',
          //   permission: [VEHICLES.AUCTION_VIEW],
          //   icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          //   subMenu: [
          //     {
          //       name: 'All',
          //       key: 'dispatches',
          //       to: '/vehicles/dispatch/all',
          //       permission: [VEHICLES.AUCTION_VIEW],
          //       icon: <DirectionsCarIcon sx={{ fontSize: '16px' }} />,
          //     },
          //     {
          //       name: 'Picked Up',
          //       key: 'picked_up',
          //       to: '/vehicles/dispatch/picked_up',
          //       permission: [VEHICLES.PENDING_AUCTION_VIEW],
          //       icon: <GavelIcon sx={{ fontSize: '16px' }} />,
          //     },
          //     {
          //       name: 'Dispatched',
          //       key: 'dispatched',
          //       to: '/vehicles/dispatch/dispatched',
          //       permission: [VEHICLES.AUCTION_VIEW],
          //       icon: <MoneyOffIcon sx={{ fontSize: '16px' }} />,
          //     },
          //     {
          //       name: 'Delivered',
          //       key: 'delivered',
          //       to: '/vehicles/dispatch/delivered',
          //       permission: [VEHICLES.AUCTION_VIEW],
          //       icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          //     },
          //     {
          //       name: 'List On CD',
          //       key: 'list_on_cd',
          //       to: '/vehicles/dispatch/list_on_cd',
          //       permission: [VEHICLES.AUCTION_VIEW],
          //       icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          //     },
          //     {
          //       name: 'Cancelled by Carrier',
          //       key: 'cancelled_by_carrier',
          //       to: '/vehicles/dispatch/cancelled_by_carrier',
          //       permission: [VEHICLES.AUCTION_VIEW],
          //       icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          //     },
          //   ],
          // },
          {
            name: 'Pending on the way',
            key: 'pending_on_the_way',
            to: '/vehicles/pending_on_the_way',
            permission: [VEHICLES.PENDING_ON_THE_WAY_VIEW],
            icon: <PendingActionsIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On The Way',
            key: 'on_the_way',
            to: '/vehicles/on_the_way',
            permission: [VEHICLES.ON_THE_WAY_VIEW],
            icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'All',
                key: 'all',
                to: '/vehicles/on_the_way',
                permission: [VEHICLES.ON_THE_WAY_VIEW],
                icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Dispatch',
                key: 'dispatch',
                to: '/vehicles/on_the_way?filterData={"dispatch_type":"dispatch"}',
                permission: [VEHICLES.ON_THE_WAY_VIEW],
                icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Self Pickup Not PGL',
                key: 'self_pickup_not_delivered_to_pgl',
                to: '/vehicles/on_the_way?filterData={"dispatch_type":"self_pickup_not_delivered_to_pgl"}',
                permission: [VEHICLES.ON_THE_WAY_VIEW],
                icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Self Pickup PGL',
                key: 'self_pickup_delivered_to_pgl',
                to: '/vehicles/on_the_way?filterData={"dispatch_type":"self_pickup_delivered_to_pgl"}',
                permission: [VEHICLES.ON_THE_WAY_VIEW],
                icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
          {
            name: 'Pending',
            key: 'pending',
            to: '/vehicles/pending',
            permission: [VEHICLES.PENDING_VIEW],
            icon: <DepartureBoardIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On Hand No Title',
            key: 'on_hand_no_title',
            to: '/vehicles/on_hand_no_title',
            permission: [VEHICLES.ON_HAND_NO_TITLE_VIEW],
            icon: <CarRentalIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On Hand With Title',
            key: 'on_hand_with_title',
            to: '/vehicles/on_hand_with_title',
            permission: [VEHICLES.ON_HAND_WITH_TITLE_VIEW],
            icon: <MinorCrashIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On Hand with Load',
            key: 'on_hand_with_load',
            to: '/vehicles/on_hand_with_load',
            permission: [VEHICLES.ON_HAND_WITH_LOAD_VIEW],
            icon: <MinorCrashIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Shipped',
            key: 'shipped',
            to: '/vehicles/shipped',
            permission: [VEHICLES.SHIPPED_VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          /* {
            name: 'At Port',
            key: 'at_port',
            to: '/vehicles/at_port',
            permission: [VEHICLES.VIEW],
            icon: <DockIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Arrived',
            key: 'arrived',
            to: '/vehicles/arrived',
            permission: [VEHICLES.VIEW],
            icon: <FlightLandIcon sx={{ fontSize: '16px' }} />,
          }, */
          {
            name: 'Inventory',
            key: 'inventory',
            to: '/vehicles/inventory/all',
            permission: [VEHICLES.VIEW],
            icon: <InventoryIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'All',
                key: 'inventory',
                to: '/vehicles/inventory/all',
                icon: <InventoryIcon sx={{ fontSize: '16px' }} />,
              },
            ].concat(
              data?.map((item) =>
                locations(item, '/vehicles/inventory/', 'inventory'),
              ),
            ),
          },
          {
            name: 'HalfCut',
            key: 'halfcut',
            to: '/vehicles/halfcut/all',
            permission: [VEHICLES.VIEW],
            icon: <TaxiAlertIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'All',
                key: 'halfcut',
                to: '/vehicles/halfcut/all',
                icon: <TaxiAlertIcon sx={{ fontSize: '16px' }} />,
              },
            ]
              .concat(
                data?.map((item) =>
                  locations(item, '/vehicles/halfcut/', 'halfcut'),
                ),
              )
              .concat([
                {
                  name: 'Halfcut Summary',
                  key: 'halfcut_summary',
                  to: '/vehicles/halfcut/halfcut_summary/all',
                  icon: (
                    <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />
                  ),
                  //@ts-ignore
                  subMenu: [
                    {
                      name: 'All',
                      key: 'halfcut_summary',
                      to: '/vehicles/halfcut/halfcut_summary/all',
                      icon: (
                        <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />
                      ),
                    },
                  ].concat(
                    data?.map((item) =>
                      locations(
                        item,
                        '/vehicles/halfcut/halfcut_summary/',
                        'halfcut_summary',
                      ),
                    ),
                  ),
                },
              ]),
          },
          {
            name: 'Title Tracking',
            key: 'total_in_process_send_arrived',
            to: '/vehicles/in_process',
            permission: [VEHICLES.TITLE_TRACKING_VIEW],
            icon: <QueryStatsIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'In Process',
                key: 'in_process',
                to: '/vehicles/in_process',
                permission: [VEHICLES.TITLE_TRACKING_VIEW],
                icon: <AutorenewIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Send',
                key: 'send',
                to: '/vehicles/send',
                permission: [VEHICLES.TITLE_TRACKING_VIEW],
                icon: <TaskAltIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Arrived',
                key: 'arrived',
                to: '/vehicles/arrived',
                permission: [VEHICLES.TITLE_TRACKING_VIEW],
                icon: <AddTaskIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
          {
            name: 'Cost Analysis',
            key: 'cost_analysis',
            to: '/vehicles/cost_analysis',
            permission: [VEHICLES.COST_ANALYSIS_VIEW],
            icon: <PriceChangeIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Datelines',
            key: 'datelines',
            to: '/vehicles/datelines',
            permission: [VEHICLES.DATE_LINES_VIEW],
            icon: <ManageHistoryIcon sx={{ fontSize: '16px' }} />,
          },

          {
            name: 'Summary',
            key: 'summary',
            to: '/vehicles/vehicle_summary/all',
            permission: [VEHICLES?.VIEW_SUMMARY],
            icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'All',
                key: 'summary',
                to: '/vehicles/vehicle_summary/all',
                icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />,
              },
              // {
              //   name: 'Inventory',
              //   key: 'summary_inv',
              //   to: '/vehicles/vehicle_summary/summary_inv',
              //   icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />,
              // },
            ].concat(
              data?.map((item) =>
                vehicleSummarylocations(item, '/vehicles/vehicle_summary/'),
              ),
            ),
          },
          {
            name: 'Pending Trash',
            key: 'pending_trash',
            to: '/vehicles/pending_trash',
            permission: [VEHICLES.PENDING_TRASH_VIEW],
            icon: <RestoreFromTrashIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Vehicle Trash',
            key: 'trash',
            to: '/vehicles/trash',
            permission: [VEHICLES.TRASH_VIEW],
            icon: <RestoreFromTrashIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },

      {
        name: 'Damages',
        key: 'damages',
        to: '/vehicle_damages/all',
        permission: [DAMAGE.VIEW],
        icon: <CarCrash sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'All',
            key: 'damages_all',
            to: '/vehicle_damages/all',
            permission: [DAMAGE.VIEW],
            icon: <CarCrash sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Process',
            key: 'damages_process',
            to: '/vehicle_damages/under_investigation',
            permission: [
              DAMAGE.UNDER_INVESTIGATION_VIEW,
              DAMAGE.IN_PROCESS_VIEW,
              DAMAGE.PENDING_CA_VIEW,
              DAMAGE.HALF_CUT_VIEW,
              DAMAGE.FORGOTTEN_VIEW,
              DAMAGE.UNLOADING_VIEW,
              DAMAGE.DISMISSED_VIEW,
            ],
            icon: <Cached sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Under Investigation',
                key: 'under_investigation',
                to: '/vehicle_damages/under_investigation',
                permission: [DAMAGE.UNDER_INVESTIGATION_VIEW],
                icon: <RotateLeft sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'In Process',
                key: 'in_process',
                to: '/vehicle_damages/in_process',
                permission: [DAMAGE.IN_PROCESS_VIEW],
                icon: <Cached sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Fair Amount Approval',
                key: 'fair_amount_approval',
                to: '/vehicle_damages/fair_amount_approval',
                permission: [DAMAGE.FAIR_AMOUNT_APPROVAL],
                icon: <Cached sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Pending CA',
                key: 'pending_ca',
                to: '/vehicle_damages/pending_ca',
                permission: [DAMAGE.PENDING_CA_VIEW],
                icon: <PendingActions sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Half Cut',
                key: 'half_cut',
                to: '/vehicle_damages/half_cut',
                permission: [DAMAGE.HALF_CUT_VIEW],
                icon: <MinorCrash sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Forgotten',
                key: 'forgotten',
                to: '/vehicle_damages/forgotten',
                permission: [DAMAGE.FORGOTTEN_VIEW],
                icon: <WorkHistory sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Unloading',
                key: 'unloading',
                to: '/vehicle_damages/unloading',
                permission: [DAMAGE.UNLOADING_VIEW],
                icon: <InventoryIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Dismissed',
                key: 'dismissed',
                to: '/vehicle_damages/dismissed',
                permission: [DAMAGE.DISMISSED_VIEW],
                icon: <CreditCardOff sx={{ fontSize: '16px' }} />,
              },
            ],
          },
          {
            name: 'Initial Review',
            key: 'initial_review',
            to: '/vehicle_damages/initial_review',
            permission: [DAMAGE.INITIAL_REVIEW_VIEW],
            icon: <AssignmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Closed',
            key: 'closed',
            to: '/vehicle_damages/closed',
            permission: [DAMAGE.CLOSED_VIEW],
            icon: <CancelPresentation sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Credit',
            key: 'credit',
            to: '/vehicle_damages/pre_credit',
            permission: [
              DAMAGE.PRE_CREDIT_VIEW,
              DAMAGE.CREDITED_VIEW,
              DAMAGE.AUDIT_REVIEWED_VIEW,
            ],
            icon: <CreditScore sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Pre Credit',
                key: 'pre_credit',
                to: '/vehicle_damages/pre_credit',
                permission: [DAMAGE.PRE_CREDIT_VIEW],
                icon: <AddCard sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Credit',
                key: 'credited',
                to: '/vehicle_damages/credited',
                permission: [DAMAGE.CREDITED_VIEW],
                icon: <CreditScore sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Audit Reviewed',
                key: 'audit_reviewed',
                to: '/vehicle_damages/audit_reviewed',
                permission: [DAMAGE.AUDIT_REVIEWED_VIEW],
                icon: <CreditScore sx={{ fontSize: '16px' }} />,
              },
            ],
          },
          {
            name: 'Damage Trash',
            key: 'damage_trash',
            to: '/vehicle_damages/trash',
            permission: [DAMAGE.TRASH_VIEW],
            icon: <RestoreFromTrashIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },
      {
        name: 'Damages Report',
        key: 'damage_report',
        to: '/vehicle_damages/report',
        permission: [DAMAGE.REPORT_VIEW],
        icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'All',
            key: 'damage_report_all',
            to: '/vehicle_damages/report',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Bonus Reports',
            key: 'bonus_reports',
            to: '/vehicle_damages/report/bonus',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Damaged Parts',
            key: 'damaged_parts',
            to: '/vehicle_damages/report/parts',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Damages Received',
            key: 'damages_received',
            to: '/vehicle_damages/report/received',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Per POL',
            key: 'per_pol',
            to: '/vehicle_damages/report/pol',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Loaded Comparison',
            key: 'loaded_comparison',
            to: '/vehicle_damages/report/comparison',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Customers',
            key: 'customers',
            to: '/vehicle_damages/report/customers',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Overall Customers',
            key: 'overall_customers',
            to: '/vehicle_damages/report/overall',
            permission: [DAMAGE.REPORT_VIEW],
            icon: <AssessmentIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },
    ],
  };
};

export default VehicleMenu;
