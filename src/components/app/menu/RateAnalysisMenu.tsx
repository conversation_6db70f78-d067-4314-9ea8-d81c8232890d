import {
  GENERAL_SHIPPING_RATES,
  MIX_SHIPPING_RATES,
  SHIPPING_RATE_ANALYSIS,
  SPECIAL_SHIPPING_RATES,
} from '@/configs/leftSideMenu/Permissions';
import { Archive, ChartBarIncreasing, ChartBarStacked } from 'lucide-react';
import PaidIcon from '@mui/icons-material/Paid';
const RateAnalysisMenu = () => {
  return {
    title: 'Rate Analysis',
    permission: [SHIPPING_RATE_ANALYSIS.VIEW],
    mainMenu: [
      {
        name: 'Rate Analysis',
        key: 'rate-analysis',
        to: '/rate-analysis',
        permission: [SHIPPING_RATE_ANALYSIS.VIEW],
        icon: <ChartBarStacked />,
        subMenu: [
          {
            name: 'Shipping Rate',
            key: 'rate-analysis-shipping-rates',
            to: '/rate-analysis/shipping-rates',
            permission: [SHIPPING_RATE_ANALYSIS.VIEW],
            icon: <ChartBarIncreasing />,
            subMenu: [
              {
                name: 'Rates',
                key: 'rate-analysis-shipping-rates-list',
                to: '/rate-analysis/shipping-rates',
                permission: [SHIPPING_RATE_ANALYSIS.VIEW],
                icon: <ChartBarIncreasing />,
              },
              {
                name: 'Archive',
                key: 'rate-analysis-shipping-rates-archive',
                to: '/rate-analysis/shipping-rates-archive',
                permission: [SHIPPING_RATE_ANALYSIS.VIEW_ARCHIVE],
                icon: <Archive />,
              },
            ],
          },
          {
            name: 'Mix Shipping Rates',
            key: 'shipping_rate',
            to: '/general/rates/shipping_rates',
            permission: [
              GENERAL_SHIPPING_RATES.VIEW,
              MIX_SHIPPING_RATES.VIEW,
              SPECIAL_SHIPPING_RATES.VIEW,
            ],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Mix ',
                key: 'shipping_cost',
                to: '/general/rates/mix-shipping-rates',
                permission: [MIX_SHIPPING_RATES.VIEW],
                icon: <PaidIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Special',
                key: 'mix_special',
                to: '/general/rates/special-mix-shipping-rates',
                permission: [MIX_SHIPPING_RATES.VIEW],
                icon: <PaidIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
        ],
      },
    ],
  };
};

export default RateAnalysisMenu;
