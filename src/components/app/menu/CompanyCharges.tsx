import { chargesCategory } from '@/app/(withAuth)/(withSidbar)/company-charges/config/constent';
import {
  CHARGES_PERMISSION,
  CHARGES_RATE_PERMISSION,
} from '@/configs/leftSideMenu/Permissions';
import {
  Archive,
  ChartBarIncreasing,
  ChartCandlestick,
  ChartColumn,
  ChartGantt,
} from 'lucide-react';
import { useProfileContext } from '../provoders/profile-context';
import { useContext, useEffect } from 'react';
import { contextProvider } from '@/contexts/ProfileContext';

const CompanyChargesMenu = () => {
  const { hasEveryPermission, profile, getProfile } = useProfileContext();
  const {
    profile: oldProfile,
    getProfile: getOldProfile,
    hasAnyPermission: hasAnyPermissionOld,
  } = useContext(contextProvider);

  const firstCategory = chargesCategory.find((category) => {
    if (profile) {
      return hasEveryPermission(
        CHARGES_RATE_PERMISSION[`VIEW_CHARGES_${category.toUpperCase()}`],
      );
    }
    if (oldProfile) {
      return hasAnyPermissionOld(
        CHARGES_RATE_PERMISSION[`VIEW_CHARGES_${category.toUpperCase()}`],
      );
    }
  });
  const secondCategory = chargesCategory.find((category) => {
    if (profile) {
      return hasEveryPermission(
        CHARGES_PERMISSION[`VIEW_CHARGES_${category.toUpperCase()}`],
      );
    }
    if (oldProfile) {
      return hasAnyPermissionOld(
        CHARGES_PERMISSION[`VIEW_CHARGES_${category.toUpperCase()}`],
      );
    }
  });
  useEffect(() => {
    if (!profile) {
      getProfile();
    }
    if (!oldProfile) {
      getOldProfile();
    }
  }, [oldProfile, profile]);

  return {
    title: 'Company Charges',
    permission: [CHARGES_PERMISSION.VIEW, CHARGES_RATE_PERMISSION.VIEW],
    mainMenu: [
      {
        name: 'Company Charges',
        key: 'company-charges',
        to: `/company-charges/charges/${secondCategory}`,
        icon: <ChartGantt />,
        subMenu: [
          {
            name: 'Charges Name',
            key: 'company-charges',
            to: `/company-charges/charges/${secondCategory}`,
            icon: <ChartCandlestick />,
            permission: [CHARGES_PERMISSION.VIEW],
          },
          {
            name: 'Charges Rate',
            key: 'Charges-Rate',
            to: `/company-charges/charges-rate/${firstCategory}`,
            icon: <ChartColumn />,
            permission: [CHARGES_RATE_PERMISSION.VIEW],
            subMenu: [
              {
                name: 'Rates',
                key: 'Company-Charges-Rate',
                to: `/company-charges/charges-rate/${firstCategory}`,
                permission: [CHARGES_RATE_PERMISSION.VIEW],
                icon: <ChartBarIncreasing />,
              },
              {
                name: 'Archive',
                key: 'Company-Charges-Archive',
                to: `/company-charges/charges-archive/${firstCategory}`,
                permission: [CHARGES_RATE_PERMISSION.ARCHIVE],
                icon: <Archive />,
              },
            ],
          },
        ],
      },
    ],
  };
};

export default CompanyChargesMenu;
