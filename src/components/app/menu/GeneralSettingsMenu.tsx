import React from 'react';
import {
  ANNOUNCEMENT,
  COMPANIES,
  C<PERSON><PERSON>MERS,
  DESTINATIONS,
  LOCATIONS,
  PERMISSIONS,
  ROLES,
  SUPPLIERS,
  USERS,
  YARD_LOCATIONS,
  LATEST_NEWS,
  TOWING_RATES,
  MIX_SHIPPING_RATES,
  DEPARTMENTS,
  DRIVERS,
  LOADERS,
  DRIVER_PAYMENTS,
  SPECIAL_SHIPPING_RATES,
  USERS_MATRIX,
  LOADING_EQUIPMENT,
  SHIPLINE_RATES,
  SHIPLINE_RATES_NAME,
  GENERAL_SHIPPING_RATES,
  EXCHANGE_RATES,
  SHIPLINES,
  HOLIDAYS,
  BUYER_NUMBERS,
  BAN<PERSON>_ACCOUNTS,
  COMPANY_CONSIGNEES,
} from '@/configs/leftSideMenu/Permissions';
import BusinessIcon from '@mui/icons-material/Business';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import HubIcon from '@mui/icons-material/Hub';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
import PeopleIcon from '@mui/icons-material/People';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
import CampaignIcon from '@mui/icons-material/Campaign';
// import { logsTrash } from './LocationName';
// import DeleteIcon from '@mui/icons-material/Delete';
import PaidIcon from '@mui/icons-material/Paid';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import RoofingIcon from '@mui/icons-material/Roofing';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import CollectionsBookmarkIcon from '@mui/icons-material/CollectionsBookmark';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import ArchiveIcon from '@mui/icons-material/Archive';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import LibraryAddCheckIcon from '@mui/icons-material/LibraryAddCheck';
import CardTravelIcon from '@mui/icons-material/CardTravel';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import RestoreFromTrashIcon from '@mui/icons-material/RestoreFromTrash';
const GeneralSettingsMenu = () => {
  return {
    title: 'General Settings',
    permission: [
      COMPANIES.VIEW,
      CUSTOMERS.VIEW,
      ANNOUNCEMENT.VIEW,
      LOCATIONS.VIEW,
      HOLIDAYS.VIEW,
      YARD_LOCATIONS.VIEW,
      DESTINATIONS.VIEW,
      PERMISSIONS.VIEW,
      ROLES.VIEW,
      SUPPLIERS.VIEW,
      USERS.VIEW,
      DRIVERS.VIEW,
      LOADERS.VIEW,
      LOADING_EQUIPMENT.VIEW,
      GENERAL_SHIPPING_RATES.VIEW,
      MIX_SHIPPING_RATES.VIEW,
      SPECIAL_SHIPPING_RATES.VIEW,
      TOWING_RATES.VIEW,
      SHIPLINE_RATES.VIEW,
      SHIPLINES.VIEW,
      HOLIDAYS.VIEW,
      BUYER_NUMBERS.VIEW,
      DEPARTMENTS.VIEW,
      BANK_ACCOUNTS.VIEW,
      COMPANY_CONSIGNEES.VIEW,
    ],
    mainMenu: [
      {
        name: 'Companies',
        key: 'companies',
        to: '/general/companies',
        permission: [COMPANIES.VIEW],
        icon: <BusinessIcon sx={{ fontSize: '16px' }} />,
      },
      // {
      //   name: 'Companies Report',
      //   key: 'companies_report',
      //   to: '/general/companies_report',
      //   permission: [COMPANIES.VIEW],
      //   icon: <BusinessIcon sx={{ fontSize: '16px' }} />,
      // },
      {
        name: 'Customers',
        key: 'customers',
        to: '/general/customers',
        permission: [CUSTOMERS.VIEW],
        icon: <PeopleAltIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Comapny Consigness',
        key: 'consignees',
        to: '/general/company_consignees',
        permission: [COMPANY_CONSIGNEES.VIEW],
        icon: <BusinessIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Shipping Rates',
        key: 'shipping_rate',
        to: '/general/rates/shipping_rates',
        permission: [
          GENERAL_SHIPPING_RATES.VIEW,
          MIX_SHIPPING_RATES.VIEW,
          SPECIAL_SHIPPING_RATES.VIEW,
        ],
        icon: <PaidIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Mix ',
            key: 'shipping_cost',
            to: '/general/rates/mix-shipping-rates',
            permission: [MIX_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Mix Special',
            key: 'mix_special',
            to: '/general/rates/special-mix-shipping-rates',
            permission: [MIX_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'General',
            key: 'shipping_rate',
            to: '/general/rates/shipping_rates',
            permission: [GENERAL_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          //Change this path to new created page (Special)
          {
            name: 'Special',
            key: 'shipping_rate',
            to: '/general/rates/special/complete',
            permission: [SPECIAL_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Half-Cut',
            key: 'special_halfcut',
            to: '/general/rates/special/halfcut',
            permission: [SPECIAL_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Bookings',
            key: 'special_booking',
            to: '/general/rates/special/booking',
            permission: [SPECIAL_SHIPPING_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          },
          // {
          //   name: 'Locations',
          //   key: 'special_shipping_rate_loations',
          //   to: '/general/rates/special_shipping_rate_locations',
          //   permission: [SPECIAL_SHIPPING_RATES.VIEW],
          //   icon: <PaidIcon sx={{ fontSize: '16px' }} />,
          // },
        ],
      },

      {
        name: 'Shipline',
        key: 'profile_rates',
        to: '/general/shipLines/profiles',
        permission: [
          SHIPLINE_RATES.VIEW,
          SHIPLINES.VIEW,
          SHIPLINE_RATES_NAME.VIEW,
        ],
        icon: <BusinessIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Rates Name',
            key: 'rate_name',
            to: '/general/shipLines/rateNames',
            permission: [SHIPLINE_RATES_NAME.VIEW],
            icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Active',
                key: 'active',
                to: '/general/shipLines/rateNames/active',
                permission: [SHIPLINE_RATES_NAME.VIEW],
                icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Inactive',
                key: 'inactive',
                to: '/general/shipLines/rateNames/inactive',
                permission: [SHIPLINE_RATES_NAME.VIEW],
                icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
          {
            name: 'Profiles',
            key: 'profile',
            to: '/general/shipLines/profiles',
            permission: [SHIPLINES.VIEW],
            icon: <CardTravelIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Rates',
            key: 'freight_rate',
            to: '/general/shipLines/rates',
            permission: [SHIPLINE_RATES.VIEW],
            icon: <PaidIcon sx={{ fontSize: '16px' }} />,
            subMenu: [
              {
                name: 'Pendings',
                key: 'pending',
                to: '/general/shipLines/rates/pending',
                permission: [SHIPLINE_RATES.VIEW],
                icon: <AutoModeIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Actives',
                key: 'active',
                to: '/general/shipLines/rates/active',
                permission: [SHIPLINE_RATES.VIEW],
                icon: <LibraryAddCheckIcon sx={{ fontSize: '16px' }} />,
              },
              {
                name: 'Archived',
                key: 'archived',
                to: '/general/shipLines/rates/archived',
                permission: [SHIPLINE_RATES.VIEW],
                icon: <ArchiveIcon sx={{ fontSize: '16px' }} />,
              },
            ],
          },
        ],
      },
      {
        name: 'Towing Rates',
        key: 'towing_rate',
        to: '/general/towing_rates/all',
        permission: [TOWING_RATES.VIEW],
        icon: <TrendingUpIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'All',
            key: 'all',
            to: '/general/towing_rates/all',
            permission: [TOWING_RATES.VIEW],
            icon: <TrendingUpIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Pendings',
            key: 'pending',
            to: '/general/towing_rates/pending',
            permission: [TOWING_RATES.VIEW],
            icon: <AutoModeIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Actives',
            key: 'active',
            to: '/general/towing_rates/active',
            permission: [TOWING_RATES.VIEW],
            icon: <LibraryAddCheckIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'In Active',
            key: 'inactive',
            to: '/general/towing_rates/inactive',
            permission: [TOWING_RATES.VIEW],
            icon: <ArchiveIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Towing Rate Trash',
            key: 'trash',
            to: '/general/towing_rates/trash',
            permission: [TOWING_RATES.TRASH_VIEW],
            icon: <RestoreFromTrashIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },
      {
        name: 'Announcements',
        key: 'announcements',
        to: '/general/announcements',
        permission: [ANNOUNCEMENT.VIEW],
        icon: <CampaignIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Latest News',
        key: 'latest_news',
        to: '/general/latest_news',
        permission: [LATEST_NEWS.VIEW],
        icon: <NewspaperIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Settings',
        key: 'settings',
        to: '/settings',
        permission: [
          LOCATIONS.VIEW,
          YARD_LOCATIONS.VIEW,
          DESTINATIONS.VIEW,
          HOLIDAYS.VIEW,
          BUYER_NUMBERS.VIEW,
          DEPARTMENTS.VIEW,
        ],
        icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Locations',
            key: 'locations',
            to: '/settings/locations',
            permission: [LOCATIONS.VIEW],
            icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'holidays',
            key: 'holidays',
            to: '/settings/holidays',
            permission: [HOLIDAYS.VIEW],
            icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Buyer Numbers',
            key: 'buyer_numbers',
            to: '/settings/buyer_numbers',
            permission: [BUYER_NUMBERS.VIEW],
            icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
          },
          // {
          //   name: 'Yard Locations',
          //   key: 'yard_locations',
          //   to: '/settings/yard_locations',
          //   permission: [YARD_LOCATIONS.VIEW],
          //   icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
          // },
          {
            name: 'Terminals',
            key: 'terminals',
            to: '/settings/terminals',
            permission: [YARD_LOCATIONS.VIEW],
            icon: <HubIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Destinations',
            key: 'Destinations',
            to: '/settings/destinations',
            permission: [DESTINATIONS.VIEW],
            icon: <SettingsSuggestIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Departments',
            key: 'Departments',
            to: '/settings/departments',
            permission: [DEPARTMENTS.VIEW],
            icon: <RoofingIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },

      {
        name: 'Drivers & Loaders',
        key: 'drivers-loaders',
        to: '/drivers-loaders',
        permission: [DRIVERS.VIEW, LOADERS.VIEW, DRIVER_PAYMENTS.VIEW],
        icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'Drivers',
            key: 'drivers',
            to: '/drivers-loaders/drivers/list',
            permission: [DRIVERS.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Driver Reports',
            key: 'driver-reports',
            to: '/drivers-loaders/drivers/reports',
            permission: [DRIVERS.VIEW],
            icon: <CollectionsBookmarkIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Driver Payments',
            key: 'driver-payments',
            to: '/drivers-loaders/drivers/payments',
            permission: [DRIVER_PAYMENTS.VIEW],
            icon: <CollectionsBookmarkIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Loaders',
            key: 'Loaders',
            to: '/drivers-loaders/loaders/list',
            permission: [LOADERS.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Loader Reports',
            key: 'loader-reports',
            to: '/drivers-loaders/loaders/reports',
            permission: [LOADERS.VIEW],
            icon: <CollectionsBookmarkIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },

      {
        name: 'Operation Equipment',
        key: 'loading_equipments',
        to: '/loading_equipments',
        permission: [LOADING_EQUIPMENT.VIEW],
        icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'All',
            key: 'all',
            to: '/loading_equipments/all',
            permission: [LOADING_EQUIPMENT.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Trucks',
            key: 'trucks',
            to: '/loading_equipments/truck',
            permission: [LOADING_EQUIPMENT.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Trailers',
            key: 'trailers',
            to: '/loading_equipments/trailer',
            permission: [LOADING_EQUIPMENT.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Forklifts',
            key: 'forklifts',
            to: '/loading_equipments/forklift',
            permission: [LOADING_EQUIPMENT.VIEW],
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          },
        ],
      },
      {
        name: 'Exchange Rates',
        key: 'exchange_rates',
        to: '/exchange_rates',
        permission: [EXCHANGE_RATES.VIEW],
        icon: <PaidIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Bank Accounts',
        key: 'bank_accounts',
        to: '/bank_accounts',
        permission: [BANK_ACCOUNTS.VIEW],
        icon: <AccountBalanceIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Permissions',
        key: 'permissions',
        to: '/general/permissions',
        permission: [PERMISSIONS.VIEW],
        icon: <PrivacyTipIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Roles',
        key: 'roles',
        to: '/general/roles',
        permission: [ROLES.VIEW],
        icon: <VerifiedUserIcon sx={{ fontSize: '16px' }} />,
      },
      // {
      //   name: 'PGL Profile',
      //   key: 'pgl_profile',
      //   to: '/general_setting/pgl_profile',
      //   icon: <AssignmentIndIcon />
      // },
      {
        name: 'Suppliers',
        key: 'suppliers',
        to: '/general/suppliers',
        permission: [SUPPLIERS.VIEW],
        icon: <SupervisedUserCircleIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Users',
        key: 'users',
        to: '/general/users',
        permission: [USERS.VIEW],
        icon: <PeopleIcon sx={{ fontSize: '16px' }} />,
      },
      {
        name: 'Users Matrix',
        key: 'mantrixs',
        to: '/general/users_matrix',
        permission: [USERS_MATRIX.VIEW],
        icon: <PeopleIcon sx={{ fontSize: '16px' }} />,
      },
      // {
      //   name: 'General Trash',
      //   key: 'general_trash',
      //   to: '',
      // permission: [
      //   USERS.TRASH_VIEW,
      //   SUPPLIERS.TRASH_VIEW,
      //   ROLES.TRASH_VIEW,
      //   DESTINATIONS.TRASH_VIEW,
      //   YARD_LOCATIONS.TRASH_VIEW,
      //   LOCATIONS.TRASH_VIEW,
      //   CUSTOMERS.TRASH_VIEW,
      //   COMPANIES.TRASH_VIEW,
      // ],
      //   icon: <DeleteIcon sx={{ fontSize: '16px' }} />,
      //   subMenu: logsTrash
      //     .filter((item) => item.logItem == undefined)
      //     .map((item) => ({
      //       name: item.name + ' Trash',
      //       key: item.key + '_trash',
      //       to: `/pgl_trash/${item.key}`,
      //       icon: <DeleteIcon sx={{ fontSize: '16px' }} />,
      //     })),
      // },
    ],
  };
};

export default GeneralSettingsMenu;
