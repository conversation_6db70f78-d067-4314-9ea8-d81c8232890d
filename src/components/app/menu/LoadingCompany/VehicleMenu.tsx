import CarRepairIcon from '@mui/icons-material/CarRepair';
import MinorCrashIcon from '@mui/icons-material/MinorCrash';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import CarRentalIcon from '@mui/icons-material/CarRental';
import PlaylistAddCheckCircleIcon from '@mui/icons-material/PlaylistAddCheckCircle';
//@ts-ignore
export default function VehicleMenu({ data }) {
  return {
    title: 'Vehicles',
    mainMenu: [
      {
        //expand: true,
        name: 'Vehicles',
        key: 'vehicles',
        to: '/loading-company/vehicles/all',
        icon: <DirectionsCarIcon sx={{ fontSize: '16px' }} />,
        subMenu: [
          {
            name: 'All',
            key: 'vehicles',
            to: '/loading-company/vehicles/all',
            icon: <DirectionsCarIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On The Way',
            key: 'on_the_way',
            to: '/loading-company/vehicles/on_the_way',
            icon: <CarRepairIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On Hand No Title',
            key: 'on_hand_no_title',
            to: '/loading-company/vehicles/on_hand_no_title',
            icon: <CarRentalIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'On Hand With Title',
            key: 'on_hand_with_title',
            to: '/loading-company/vehicles/on_hand_with_title',
            icon: <MinorCrashIcon sx={{ fontSize: '16px' }} />,
          },
          {
            name: 'Summary',
            key: 'summary',
            to: '/loading-company/vehicles/summary',
            icon: <PlaylistAddCheckCircleIcon sx={{ fontSize: '16px' }} />,
          },
          /* {
            name: 'Shipped',
            key: 'shipped',
            to: '/loading-company/vehicles/shipped',
            icon: <LocalShippingIcon sx={{ fontSize: '16px' }} />,
          }, */
        ],
      },
    ],
  };
}
