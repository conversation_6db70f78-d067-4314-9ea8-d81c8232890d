import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';
import React from 'react';
type FilterCollapseProps = {
  label: string;
  children: React.ReactNode;
  className?: string;
  isOpen: boolean;
  onToggle: () => void;
};
const FilterCollapse = ({
  children,
  label,
  className,
  isOpen,
  onToggle,
}: FilterCollapseProps) => {
  return (
    <Collapsible
      className="group/collapsible w-full shadow-inner"
      open={isOpen}
      onOpenChange={onToggle}
    >
      <SidebarMenuItem className="list-none">
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            className={cn('flex h-10 min-w-full justify-between', className)}
            variant={'outline'}
          >
            <p className={'w-full p-4 font-semibold rtl:text-right'}>{label}</p>
            <ChevronRight className="ml-auto w-full transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 rtl:mr-auto rtl:rotate-180" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="p-1">{children}</CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
};

export default FilterCollapse;
