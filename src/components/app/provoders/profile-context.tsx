'use client';

import {
  useEffect,
  useState,
  createContext,
  ReactNode,
  useContext,
} from 'react';
import axios from '@/lib/axios';
import { setCookie } from '@/utils/cookieHelper';
import { viewPLProfiles } from '@/configs/shipment/ProfitLost';

type ProfileProviderProps = {
  children: ReactNode;
  profileServer?: Profile;
};

type Profile = any;

type ProfileContextValue = {
  profile?: Profile;
  permissions?: Set<string>;
  hasAnyPermission: (...permissionsToCheck: string[]) => boolean;
  hasEveryPermission: (...permissionsToCheck: string[]) => boolean;
  getProfile: () => Promise<void>;
  refreshProfile: () => void;
};

export const contextProvider = createContext<ProfileContextValue>({
  profile: undefined,
  getProfile: async () => {},
  hasAnyPermission: (..._permissionsToCheck: string[]) => false,
  hasEveryPermission: (..._permissionsToCheck: string[]) => false,
  refreshProfile: () => {},
});

export const useProfileContext = () => useContext(contextProvider);

const ProfileProvider = ({ children, profileServer }: ProfileProviderProps) => {
  const [profile, setProfile] = useState<Profile | undefined>(profileServer);
  const [permissions, setPermissions] = useState<Set<string>>(new Set());

  const getProfile = async () => {
    if (localStorage.getItem('token')) {
      const id = localStorage.getItem('userKey');
      try {
        const response = await axios.get(`auth/profile?key=${id}`);
        const profileData = response.data;
        //temporary
        localStorage.setItem('userKey', response.data.data.loginable.id);
        setCookie(
          'userKey',
          response.data.data.loginable.id,
          180 * 24 * 60 * 60,
        ); // 6 months
        setProfile(profileData.data);
        setPermissionData(profileData.data);
      } catch (error) {}
    }
  };

  const setPermissionData = (profile) => {
    const perm = profile?.loginable?.permissions;
    const rolePerm = profile?.loginable?.roles?.flatMap(
      (p: any) => p.permissions,
    );
    if (viewPLProfiles.includes(profile?.loginable?.email)) {
      perm.push({ name: 'view_profit_loss' });
    }
    setPermissions(
      new Set(
        perm
          ?.concat(
            rolePerm?.filter(
              (item: any) => !perm?.some((item2: any) => item.id === item2.id),
            ),
          )
          .map((item: any) => item?.name),
      ),
    );
  };

  const refreshProfile = () => {
    getProfile();
  };

  const hasAnyPermission = (...permissionsToCheck: string[]) => {
    if (permissionsToCheck.length === 0) {
      return true;
    }
    return permissionsToCheck.some((perm) => permissions.has(perm));
  };
  const hasEveryPermission = (...permissionsToCheck: string[]) => {
    return permissionsToCheck.every((perm) => permissions.has(perm));
  };

  useEffect(() => {
    setPermissionData(profile);
    if (profile?.department_id === 3) {
      const dataEntrySearch = localStorage.getItem('data-entery-search');
      if (!dataEntrySearch) localStorage.setItem('data-entery-search', 'false');
      if (dataEntrySearch === 'false') {
        localStorage.setItem(
          `searchBy-vehicle`,
          JSON.stringify(['vin', 'lot_number']),
        );
      }
    } else {
      if (localStorage.getItem('data-entery-search')) {
        localStorage.removeItem('data-entery-search');
        localStorage.removeItem('searchBy-vehicle');
      }
    }
  }, [profile]);

  const profileContextValue: ProfileContextValue = {
    profile,
    permissions,
    hasAnyPermission,
    hasEveryPermission,
    getProfile,
    refreshProfile,
  };

  return (
    <contextProvider.Provider value={profileContextValue}>
      {children}
    </contextProvider.Provider>
  );
};

export default ProfileProvider;
