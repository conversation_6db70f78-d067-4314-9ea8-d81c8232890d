/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2023-04-18
 * @modify date 2023-04-18
 * @desc [This is page header component, use in every page to show the paths]
 */
import { FC, useContext } from 'react';
import {
  Box,
  Paper,
  Typography,
  Breadcrumbs,
  useTheme,
  IconButton,
  FormControlLabel,
  useMediaQuery,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import Router from 'next/router';
import { PageHeaderType } from '@/models/components/iPageHeader';
import { useTranslation } from 'react-i18next';
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import MenuIcon from '@mui/icons-material/Menu';
import AppTooltip from '../cComponents/AppTooltip';
import UserProfileHeader from './UserProfileHeader';
import { MaterialUISwitch } from '../cComponents/Switch';
import { ThemeContext } from '@/theme/ThemeProvider';
import RemindersComponent from '@/layouts/SidebarLayout/Header/Reminders';
import { contextProvider } from '@/contexts/ProfileContext';
import { useSidebar } from '@/components/ui/sidebar';

const PageHeader: FC<PageHeaderType> = ({ breadcrumbs }) => {
  const theme = useTheme();
  const { i18n } = useTranslation();
  const dir = i18n.dir();
  const { open, toggleSidebar } = useSidebar();
  const { mode, toggleMode } = useContext(ThemeContext);
  const matches = useMediaQuery('(max-width:900px)');

  const useProfileContext = useContext(contextProvider);
  const { profile } = useProfileContext;
  const user_type = profile?.data?.loginable?.loginable_type;

  return (
    <>
      {matches ? (
        <></>
      ) : (
        <Box
          sx={{
            alignItems: '',
            '& > :not(style)': {
              width: '100%',
              mt: 0.1,
              height: 35,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',

              ...(dir === 'rtl'
                ? { backgroundPositionX: 'left' }
                : { backgroundPositionX: 'right' }),
            },
          }}
        >
          <Paper
            elevation={2}
            sx={{
              p: '10px',
              display: 'flex',
              alignItems: 'center',
              backgroundColor: theme.colors.alpha.black[10],
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {!matches && (
                <AppTooltip title={'Toggle Side Bar'}>
                  <IconButton
                    size="small"
                    color={!open ? 'primary' : 'inherit'}
                    onClick={toggleSidebar}
                  >
                    {!open ? <MenuIcon /> : <MenuOpenIcon />}
                  </IconButton>
                </AppTooltip>
              )}

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                }}
                style={
                  !matches ? { marginLeft: '36px' } : { marginLeft: '0px' }
                }
              >
                <Breadcrumbs
                  separator={<NavigateNextIcon fontSize="small" />}
                  aria-label="breadcrumb"
                >
                  {breadcrumbs.map((item) => {
                    if (item.href != 'false') {
                      return (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: '12px',
                            '&:hover': {
                              textDecoration: 'underline',
                              cursor: 'pointer',
                            },
                          }}
                          color="inherit"
                          key={item.key}
                          onClick={() => Router.push(item.href)}
                        >
                          <Box component={'span'} sx={{ mr: 0.5 }}>
                            {item.icon}
                          </Box>
                          {item.name}
                        </Box>
                      );
                    } else {
                      return (
                        <Typography
                          key={item.key}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: '12px',
                          }}
                          color="text.primary"
                        >
                          <Box component={'span'} sx={{ mr: 0.5 }}>
                            {item.icon}
                          </Box>
                          {item.name}
                        </Typography>
                      );
                    }
                  })}
                </Breadcrumbs>
              </Box>
            </Box>
            <Box>
              {!matches && (
                <>
                  <FormControlLabel
                    sx={{ mr: 1.5 }}
                    control={
                      <MaterialUISwitch
                        checked={mode === 'dark'}
                        size="small"
                        onChange={toggleMode}
                      />
                    }
                    label=""
                  />
                  {user_type !== 'loader' && <RemindersComponent />}
                  <UserProfileHeader />
                </>
              )}
            </Box>
          </Paper>
        </Box>
      )}
    </>
  );
};

export default PageHeader;
