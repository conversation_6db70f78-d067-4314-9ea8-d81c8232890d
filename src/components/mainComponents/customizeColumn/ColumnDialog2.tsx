import {
  <PERSON>ton,
  <PERSON>,
  Card<PERSON>ctions,
  CardContent,
  CardHeader,
  Checkbox,
  Divider,
  FormControl,
  FormHelperText,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Modal,
  OutlinedInput,
  Switch,
  Typography,
} from '@mui/material';
import { Box } from '@mui/system';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from '@/lib/axios';
import { contextProvider } from '@/contexts/ProfileContext';
import AppConfirmDialog from '../cComponents/AppConfirmDialog';
import { LoadingButton } from '@mui/lab';
import { toast } from 'react-toastify';

const ColumnDialog = ({
  showColumnDialog,
  setShowColumnDialog,
  defaultHeaders,
  selectedHeaders,
  setSelectedHeaders,
  pageName,
  selectedSetting,
  setSelectedSetting,
}) => {
  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 1050,
    bgcolor: 'background.paper',
  };
  const useProfileContext = useContext(contextProvider);
  const { profile, refreshProfile } = useProfileContext;
  const [tempHeader, setTempHeader] = useState([]);
  const [columnSettings, setColumnSettings] = useState([]);
  const [columnDeleteId, setColumnDeleteId] = useState(0);
  const [apiLoadingText, setApiLoadingText] = useState(null);
  const schema = z.object({
    setting_name: z
      .string()
      .min(3, { message: 'Setting name must be at least 3 characters' }),
  });
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: { setting_name: '' },
  });
  useEffect(() => {
    if (showColumnDialog) {
      setTempHeader(selectedHeaders);
      arrangeSavedSetting();
    }
  }, [showColumnDialog]);

  const arrangeSavedSetting = () => {
    const column_settings = profile?.data?.column_settings;
    if (column_settings?.length > 0) {
      const col_set = column_settings.filter(
        (row) => row.page_name == pageName,
      );
      setColumnSettings(col_set);
    }
  };

  const onSubmit = async (data: { setting_name: string }) => {
    try {
      if (columnSettings.length > 2) {
        toast.warn("You can't set more than three setting!");
        return;
      }
      if (tempHeader.length == 0) {
        toast.warn("Column setting can't be empty!");
        return;
      }
      setApiLoadingText('submiting');
      const res = await axios.post(`column-settings`, {
        setting_name: data.setting_name,
        columns: JSON.stringify(tempHeader),
        page_name: pageName,
      });
      if (res.status == 201) {
        let tmpCol = columnSettings;
        tmpCol = tmpCol.map((row) => {
          row.default_setting = false;
          return row;
        });
        setColumnSettings([...tmpCol, res.data.data]);
        setSelectedSetting(res.data.data.id);

        toast.info('Setting saved successfully!');
        refreshProfile();
      }
    } catch (e) {
      toast.error(e?.response?.data?.message);
    }
    setApiLoadingText(null);
  };

  const toggleHeaderColumn = (value) => {
    let newCols = [...tempHeader];
    const notExist = tempHeader.find((row) => row.id == value.id) === undefined;
    if (notExist) {
      newCols.push(value);
    } else {
      newCols = newCols.filter((row) => row.id != value.id);
    }
    setTempHeader(newCols);
  };

  const applySetting = () => {
    setSelectedHeaders(tempHeader);
    setShowColumnDialog(false);
  };
  const applySavedSetting = (id, cols) => {
    setTempHeader(JSON.parse(cols));
    setSelectedSetting(id);
  };
  const updateSavedSetting = async (id: number, target_column: string) => {
    try {
      const targetSetting = columnSettings.find((row) => row.id == id);
      if (target_column == 'default_setting') {
        if (targetSetting.default_setting) return;
        let tempSet = columnSettings;
        tempSet = tempSet.map((row) => {
          if (row.id == id) {
            row.default_setting = true;
            row.columns = JSON.stringify(tempHeader);
          } else {
            row.default_setting = false;
          }
          return row;
        });
        setColumnSettings(tempSet);
      } else {
        if (tempHeader.length == 0) {
          toast.warn("Column setting can't be empty!");

          return;
        }
        setApiLoadingText('update_columns');
      }
      await axios.put(`column-settings/${id}`, {
        target_column: target_column,
        columns: JSON.stringify(tempHeader),
        page_name: pageName,
      });
      setApiLoadingText(null);

      toast.info('Setting saved successfully!');
      refreshProfile();
    } catch (error) {
      console.error(error);
    }
  };
  const deleteSavedSetting = async () => {
    const id = columnDeleteId;
    if (columnSettings.length > 1) {
      const tmpSet = columnSettings.find((row) => row.id == id);
      if (tmpSet !== undefined) {
        if (tmpSet.default_setting) {
          toast.warn(
            "Can't delete setting. setting is set to default, to delete the setting first change the default setting!",
          );

          return;
        }
      }
    }
    setColumnDeleteId(0);
    const res = await axios.delete('column-settings/' + id);
    if (res.status == 200) {
      setColumnSettings(columnSettings.filter((row) => row.id != id));
      if (id == selectedSetting) {
        setSelectedSetting(0);
      }
      refreshProfile();
    }
  };
  const resetHeaders = () => {
    setSelectedHeaders(defaultHeaders);
    setShowColumnDialog(false);
    setSelectedSetting(0);
  };

  const onDragEnd = (result) => {
    if (!result.destination) return; // Dropped outside the list
    const newHeaders = [...tempHeader];
    const [movedHeader] = newHeaders.splice(result.source.index, 1);
    newHeaders.splice(result.destination.index, 0, movedHeader);
    setTempHeader(newHeaders);
  };

  const ColumnSection = ({ children, title }) => {
    return (
      <Card
        key={title}
        variant="outlined"
        sx={{ borderRadius: 1, width: '330px', overflow: 'auto' }}
      >
        <CardHeader
          sx={{ p: 1 }}
          title={
            <Box
              component="p"
              sx={{
                textAlign: 'center',
                my: 0,
                pb: 0,
                fontSize: 18,
                fontWeight: 500,
              }}
            >
              {title}
            </Box>
          }
        />
        <CardContent sx={{ p: 0, px: 1 }}>{children}</CardContent>
      </Card>
    );
  };
  const ColumnItem = ({ labelId, checked, row }) => {
    return (
      <Card
        key={labelId}
        onClick={() => toggleHeaderColumn(row)}
        variant="outlined"
        sx={{
          cursor: 'pointer',
          p: 1,
          marginBottom: '6px',
          height: 50,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: '5px',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Checkbox
            id={labelId}
            edge="start"
            checked={checked}
            tabIndex={-1}
            disableRipple
          />
          <Typography>{row.header}</Typography>
        </Box>
        <Box>
          <IconButton edge="end" aria-label="comments">
            <DragIndicatorIcon />
          </IconButton>
        </Box>
      </Card>
    );
  };

  return (
    <>
      <Modal open={showColumnDialog}>
        <Card sx={style}>
          <Box
            sx={{
              m: 0,
              p: 2,
              py: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant="h5">Customize Column</Typography>
            <IconButton
              aria-label="close"
              sx={{ color: 'grey' }}
              onClick={() => {
                setShowColumnDialog(false);
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />

          <CardContent>
            <Box
              sx={{
                display: 'flex',
                columnGap: 2,
                rowGap: 2,
                justifyContent: 'space-between',
                flexDirection: { xs: 'column', sm: 'row' },
              }}
            >
              <ColumnSection title="My Settings">
                <Typography sx={{ fontSize: '12px', pb: 1 }}>
                  To keep your setting <br></br>please save your column changes!
                </Typography>

                <Box
                  component="form"
                  onSubmit={handleSubmit(onSubmit)}
                  sx={{
                    display: 'flex',
                    alignItems: 'start',
                    justifyContent: 'space-between',
                  }}
                >
                  <FormControl variant="outlined" fullWidth>
                    <OutlinedInput
                      id="outlined-adornment-setting"
                      placeholder="Setting name"
                      {...register('setting_name')}
                      error={!!errors.setting_name}
                      aria-describedby="outlined-weight-helper-text"
                      size="small"
                      fullWidth
                    />
                    <FormHelperText
                      id="outlined-weight-helper-text "
                      sx={{ color: 'red' }}
                    >
                      {errors.setting_name?.message}
                    </FormHelperText>
                  </FormControl>

                  <LoadingButton
                    disableElevation
                    type="submit"
                    loading={apiLoadingText == 'submiting'}
                    variant="contained"
                    sx={{ ml: 1 }}
                  >
                    Save
                  </LoadingButton>
                </Box>

                <Box sx={{ pt: 2 }}>
                  {columnSettings.map((row, index) => {
                    return (
                      <ListItem
                        key={index}
                        secondaryAction={
                          <>
                            <Switch
                              className="mySwitch"
                              checked={row.default_setting}
                              onChange={() => {
                                updateSavedSetting(row.id, 'default_setting');
                              }}
                            />
                            <IconButton
                              edge="end"
                              size="small"
                              onClick={() => setColumnDeleteId(row.id)}
                            >
                              <DeleteIcon color="error" />
                            </IconButton>
                          </>
                        }
                        sx={{
                          border: `1px solid ${
                            selectedSetting == row.id ? '#2196f3' : 'grey'
                          }`,
                          boxShadow: `1px 1px 5px ${
                            selectedSetting == row.id
                              ? 'rgb(33, 150, 243, 0.3)'
                              : 'rgb(0,0,0, 0.2)'
                          } `,
                          marginBottom: '5px',
                          borderRadius: '5px',
                          padding: '7px',
                        }}
                        disablePadding={true}
                      >
                        <ListItemButton
                          role={undefined}
                          dense
                          onClick={() => applySavedSetting(row.id, row.columns)}
                        >
                          <ListItemText
                            primary={row.setting_name}
                            sx={{ pl: 1 }}
                          />
                        </ListItemButton>
                      </ListItem>
                    );
                  })}
                </Box>
              </ColumnSection>
              <ColumnSection title="All Columns">
                <Box
                  style={{ maxHeight: 400, overflowY: 'auto', paddingRight: 5 }}
                >
                  <List sx={{ bgcolor: 'background.paper' }}>
                    {defaultHeaders.map(
                      (row: { id: string; header: string }, index: number) => {
                        const checked =
                          tempHeader.find((h) => h.id == row.id) !== undefined;
                        const labelId = `checkbox-list-label-${index}`;
                        return (
                          <ColumnItem
                            labelId={labelId}
                            checked={checked}
                            key={index}
                            row={row}
                          />
                        );
                      },
                    )}
                  </List>
                </Box>
              </ColumnSection>
              <ColumnSection title="Selected Columns">
                <Box
                  style={{ maxHeight: 400, overflowY: 'auto', paddingRight: 5 }}
                >
                  <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId="droppable">
                      {(provided) => (
                        <Box
                          sx={{ bgcolor: 'background.paper' }}
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                        >
                          {tempHeader.map((h, index) => {
                            return (
                              <Draggable
                                key={h.id}
                                draggableId={h.id}
                                index={index}
                              >
                                {(provided, index2) => (
                                  <Card
                                    key={h.id + index2}
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    variant="outlined"
                                    sx={{
                                      cursor: 'pointer',
                                      p: 1,
                                      marginBottom: '6px',
                                      height: 50,
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      borderRadius: '5px',
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                      }}
                                    >
                                      <DragIndicatorIcon />
                                      <Typography sx={{ pl: 1 }}>
                                        {h.header}
                                      </Typography>
                                    </Box>
                                    <Box>
                                      <IconButton
                                        edge="end"
                                        size="small"
                                        onClick={() => toggleHeaderColumn(h)}
                                      >
                                        <CloseIcon />
                                      </IconButton>
                                    </Box>
                                  </Card>
                                )}
                              </Draggable>
                            );
                          })}
                          {provided.placeholder}
                        </Box>
                      )}
                    </Droppable>
                  </DragDropContext>
                </Box>
              </ColumnSection>
            </Box>
          </CardContent>
          <Divider></Divider>
          <CardActions sx={{ justifyContent: 'end', pr: 2 }}>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={resetHeaders}
            >
              Reset
            </Button>
            {selectedSetting > 0 ? (
              <LoadingButton
                loading={apiLoadingText == 'update_columns'}
                color="success"
                variant="contained"
                size="small"
                onClick={() => updateSavedSetting(selectedSetting, 'columns')}
              >
                Update
              </LoadingButton>
            ) : (
              <></>
            )}
            <Button
              size="small"
              variant="contained"
              onClick={() => applySetting()}
            >
              Apply
            </Button>
          </CardActions>
          <AppConfirmDialog
            maxWidth={'sm'}
            open={columnDeleteId > 0}
            onDeny={() => {
              setColumnDeleteId(0);
            }}
            onConfirm={() => {
              deleteSavedSetting();
            }}
            title="Are you sure to delete column setting!"
            dialogTitle="Delete Column Setting!"
          />
        </Card>
      </Modal>
    </>
  );
};
export default ColumnDialog;
