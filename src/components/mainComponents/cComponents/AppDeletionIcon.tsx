import React, { useEffect, useState } from 'react';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';

import AppConfirmDialog from './AppConfirmDialog';
import IconButton from '@mui/material/IconButton';
import AppTooltip from './AppTooltip';
import { toast } from 'react-toastify';
import {
  Box,
  FormControl,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  TextField,
} from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

interface AppsDeleteIconProps {
  deleteAction?(...args: unknown[]): unknown;
  deleteTitle?: React.ReactNode;
  sx?: object;
  dialogTitle?: React.ReactNode;
  trash?: boolean;
  passwordOnDelete?: string | boolean | null;
  reasonOnDelete?: string | boolean | null;
  deletePassword?: string | null;
  deleteReason?: string | null;
  onDeleteReason?: ((reason: string) => void) | null;
}

const AppsDeleteIcon = ({
  deleteAction,
  deleteTitle,
  dialogTitle,
  sx,
  trash,
  passwordOnDelete = null,
  reasonOnDelete = null,
  deletePassword = null,
  deleteReason = null,
  onDeleteReason = null,
}: AppsDeleteIconProps) => {
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordField, setShowPasswordField] = useState(false);
  const [showReasonField, setShowReasonField] = useState(false);
  const [password, setPassword] = useState('');

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const setPasswordFunction = (event) => {
    const newValue = event?.target?.value;
    if (newValue !== undefined && newValue !== null) {
      setPassword(newValue);
    } else {
      setPassword('');
    }
  };

  const onPasswordCheck = () => {
    if (password == '') {
      toast.warn('Please fill the password field');
    } else if (password == deletePassword) {
      deleteAction();
      setDeleteDialogOpen(false);
    } else {
      toast.warn('This password you have provide is wrong please try again');
    }
  };

  const onReasonCheck = () => {
    if (deleteReason == '') {
      toast.warn('Please fill the delete reason field');
    } else {
      deleteAction();
      setDeleteDialogOpen(false);
    }
  };

  useEffect(() => {
    if (passwordOnDelete) setShowPasswordField(true);
    if (reasonOnDelete) setShowReasonField(true);
  }, [passwordOnDelete, reasonOnDelete]);

  return (
    <>
      <AppTooltip title={trash ? 'Force Delete' : 'Delete'}>
        <IconButton
          sx={sx}
          size="small"
          onClick={() => setDeleteDialogOpen(true)}
        >
          <DeleteOutlinedIcon color="error" />
        </IconButton>
      </AppTooltip>
      <AppConfirmDialog
        maxWidth={'sm'}
        open={isDeleteDialogOpen}
        onDeny={setDeleteDialogOpen}
        onConfirm={
          passwordOnDelete
            ? onPasswordCheck
            : reasonOnDelete
              ? onReasonCheck
              : () => {
                  deleteAction();
                  setDeleteDialogOpen(false);
                }
        }
        title={
          (passwordOnDelete || reasonOnDelete || deleteTitle) && (
            <>
              {deleteTitle && <Box>{deleteTitle}</Box>}

              {passwordOnDelete && showPasswordField && (
                <FormControl
                  size="small"
                  fullWidth
                  sx={{ mt: 1 }}
                  variant="outlined"
                >
                  <InputLabel htmlFor="outlined-adornment-password">
                    Password
                  </InputLabel>
                  <OutlinedInput
                    id="outlined-adornment-password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(event) => setPasswordFunction(event)}
                    endAdornment={
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    }
                    label="Password"
                  />
                </FormControl>
              )}

              {reasonOnDelete && showReasonField && (
                <TextField
                  fullWidth
                  size="small"
                  sx={{ mt: 1 }}
                  variant="outlined"
                  label="Delete Reason"
                  multiline
                  minRows={1}
                  maxRows={5}
                  value={deleteReason}
                  onChange={(event) => onDeleteReason(event.target.value)}
                />
              )}
            </>
          )
        }
        dialogTitle={dialogTitle}
      />
    </>
  );
};

export default AppsDeleteIcon;
