import React from 'react';
import Tooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip';
import { styled } from '@mui/material';

const LightTooltip = styled(Tooltip)(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.colors.alpha.white[100],
    '&:before': { boxShadow: theme.colors.shadows },
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.colors.alpha.black[100],
    color: theme.colors.alpha.white[100],
    boxShadow: theme.colors.shadows,
    fontSize: 11,
  },
}));

interface AppTooltipProps {
  title: TooltipProps['title'];
  children: React.ReactNode;
  placement?: TooltipProps['placement'];
}

const AppTooltip: React.FC<AppTooltipProps> = ({
  title,
  children,
  placement = 'top',
}) => {
  // Ensure children is a ReactElement for MUI Tooltip
  const wrappedChildren = React.isValidElement(children) ? (
    children
  ) : (
    <span>{children}</span>
  );

  return (
    <LightTooltip title={title} placement={placement} arrow>
      {wrappedChildren}
    </LightTooltip>
  );
};

export default AppTooltip;

// AppTooltip.propTypes = {
//   title: PropTypes.oneOfType([
//     PropTypes.string,
//     PropTypes.node,
//     PropTypes.object,
//   ]),
//   placement: PropTypes.string,
//   children: PropTypes.node,
// };
