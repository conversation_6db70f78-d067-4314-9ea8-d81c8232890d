import React from 'react';
import Button from '@mui/material/Button';
import Fade from '@mui/material/Fade';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';

import ThumbDownAltIcon from '@mui/icons-material/ThumbDownAlt';
import { LoadingButton } from '@mui/lab';

interface AppConfirmDialogProps {
  dialogTitle?: React.ReactNode;
  open: boolean;
  onDeny(...args: unknown[]): unknown;
  title?: React.ReactNode;
  onConfirm(...args: unknown[]): unknown;
  confirmText?: string;
  cancelText?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  submitting?: boolean;
  fullWidth?: boolean;
  onClose?: () => void;
}

const AppConfirmDialog = ({
  open,
  onDeny,
  onConfirm,
  title,
  dialogTitle,
  confirmText = 'Yes',
  cancelText = 'No',
  maxWidth,
  submitting = false,
  fullWidth = true,
  onClose = () => onDeny(false),
}: AppConfirmDialogProps) => {
  return (
    <Dialog
      TransitionComponent={Fade}
      open={open}
      onClose={onClose}
      fullWidth={fullWidth}
      maxWidth={maxWidth}
    >
      <DialogTitle>
        <Typography sx={{ fontWeight: 'semi-bold' }} id="alert-dialog-title">
          {dialogTitle}
        </Typography>
      </DialogTitle>
      <DialogContent
        sx={{
          color: 'secondary',
          fontSize: 14,
          overflowY: 'unset',
          width: '100%',
        }}
        id="alert-dialog-description"
      >
        {title}
      </DialogContent>
      <DialogActions sx={{ pb: 2, px: 3, minWidth: '30vw' }}>
        <LoadingButton
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            minWidth: '100px',
          }}
          variant="contained"
          onClick={onConfirm}
          color="primary"
          autoFocus
          loading={submitting}
        >
          <ThumbUpAltIcon />
          {confirmText}
        </LoadingButton>
        <Button
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            minWidth: '80px',
          }}
          variant="outlined"
          onClick={onClose}
          color="error"
        >
          <ThumbDownAltIcon />
          {cancelText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AppConfirmDialog;
