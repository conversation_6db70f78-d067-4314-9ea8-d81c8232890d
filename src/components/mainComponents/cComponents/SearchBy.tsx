import * as React from 'react';
import Checkbox from '@mui/material/Checkbox';
import { Box, Card, Typography } from '@mui/material';

export default function SearchBy(searchFields, ModuleName) {
  const [values, setValues] = React.useState<string[]>([]);
  React.useEffect(() => {
    const storedValues = localStorage.getItem(`searchBy-${ModuleName}`);
    if (storedValues) {
      setValues(JSON.parse(storedValues));
    }
  }, []);

  const handleChange = (event) => {
    const menuItem = searchFields.find((item) => item.name === event?.name);
    const selectedValue = menuItem?.value;

    if (selectedValue) {
      let updatedValues = [...values];

      if (updatedValues.includes(selectedValue)) {
        updatedValues = updatedValues.filter(
          (value) => value !== selectedValue,
        );
      } else {
        updatedValues.push(selectedValue);
      }

      setValues(updatedValues);
      localStorage.setItem(
        `searchBy-${ModuleName}`,
        JSON.stringify(updatedValues),
      );
      if (localStorage.getItem('data-entery-search')) {
        localStorage.setItem('data-entery-search', 'true');
      }
    }
  };
  const ColumnItem = ({ labelId, checked, row }) => {
    return (
      <Card
        onClick={() => handleChange(row)}
        variant="outlined"
        sx={{
          cursor: 'pointer',
          p: 1,
          marginBottom: '6px',
          height: 50,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: '5px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Checkbox
            id={labelId}
            edge="start"
            checked={checked}
            tabIndex={-1}
            disableRipple
          />
          <Typography>{row?.name.replace(/_/g, ' ')}</Typography>
        </Box>
        {/* <Box>
          <IconButton edge="end" aria-label="comments">
            <DragIndicatorIcon />
          </IconButton>
        </Box> */}
      </Card>
    );
  };

  return (
    <>
      {searchFields.map((row) => {
        const checked = values.find((h) => h == row?.value) !== undefined;
        const labelId = `checkbox-list-label-${row?.value}`;
        return (
          <ColumnItem
            labelId={labelId}
            checked={checked}
            key={row?.value}
            row={row}
          />
        );
      })}
    </>
  );
}
