import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { useTheme } from '@mui/material';

interface CBasicTooltipProps {
  onClick?(...args: unknown[]): unknown;
  title?: any;
  icon?: any;
}

export default function CBasicTooltip({
  title,
  onClick,
  icon,
}: CBasicTooltipProps) {
  const theme = useTheme();

  return (
    <Tooltip title={title}>
      <IconButton
        sx={{
          '&:hover': {
            color: theme.colors.primary.main,
          },
        }}
        onClick={onClick}
      >
        {icon}
      </IconButton>
    </Tooltip>
  );
}
