import EditIcon from '@mui/icons-material/Edit';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import AppsDeleteIcon from '../cComponents/AppDeletionIcon';
import AppTooltip from '../cComponents/AppTooltip';

interface DefaultToolbarProps {
  onEdit?(...args: unknown[]): unknown;
  onDelete?(...args: unknown[]): unknown;
  deleteTitle?: React.ReactNode;
  selected?: unknown[];
  selectedItems?: unknown[];
  showDeleteButton?: boolean;
  showEditButton?: boolean;
  dialogTitle?: React.ReactNode;
  customButton?: () => React.ReactNode;
  showCustomButton?: boolean;
}

export default function DefaultToolbar({
  onEdit,
  onDelete,
  deleteTitle,
  dialogTitle,
  selected,
  showDeleteButton,
  showEditButton,
  customButton,
  showCustomButton,
}: DefaultToolbarProps) {
  return (
    <Box style={{ display: 'flex', alignItems: 'center' }} sx={{ mx: 4 }}>
      {showCustomButton && customButton()}

      {showEditButton && selected.length == 1 && (
        <AppTooltip title={'edit'}>
          <IconButton color="secondary" onClick={onEdit}>
            <EditIcon />
          </IconButton>
        </AppTooltip>
      )}

      {showDeleteButton && (
        <AppsDeleteIcon
          deleteAction={onDelete}
          deleteTitle={deleteTitle}
          dialogTitle={dialogTitle}
          trash={false}
          sx={{ cursor: 'pointer', color: 'text.disabled' }}
        />
      )}
    </Box>
  );
}
