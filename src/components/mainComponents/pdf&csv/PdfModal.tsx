import {
  <PERSON><PERSON>,
  <PERSON>,
  CardActions,
  CardContent,
  Checkbox,
  Divider,
  FormControlLabel,
  IconButton,
  Modal,
  Radio,
  RadioGroup,
  Typography,
  useMediaQuery,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Box } from '@mui/system';
import { CsvIcon, ExcelIcon, PdfIcon } from '../svgIcons/SvgIcons';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import CBasicTooltip from '../datatable/cBasicTooltip';
import PdfPreviewModal from './PdfPreviewModal';
import PdfComponent from './PdfComponent';
import { CsvDownload } from './CsvDownload';
import { usePDF } from '@react-pdf/renderer';
import { sortDataForDownload } from '@/utils/sortDataForDownload';
import CompressIcon from '@mui/icons-material/Compress';
import ExcelJS from 'exceljs';
import FileSaver from 'file-saver';
import axios from '@/lib/axios';

const PdfModal = (props) => {
  const {
    selectedItems = [],
    open,
    setShowDownload,
    selectedHeaders,
    fetchDownloadRecords,
    tableRecords,
    pdf_title,
    options,
    vehicleBase = false,
    excelDownload = false,
    csvDownload = true,
    setVehicleBase = () => {},
    defaultDowloadFormat = 'csv',
    pdfPreview = true,
  } = props;

  let temp_headers = selectedHeaders;
  if (props?.hide_columns?.length > 0) {
    temp_headers = selectedHeaders.filter(
      (row) => !props.hide_columns.includes(row.id),
    );
  }
  const matches = useMediaQuery('(max-width:600px)');
  const [showPreview, setShowPreview] = useState(false);
  const [downloadFormat, setDownloadFormat] = useState(defaultDowloadFormat);
  const [downloading, setDownloading] = useState(false);
  const [dataType, setDataType] = useState('current');
  const [pdfData, setPdfData] = useState([]);

  const handleExportToExcel = async () => {
    let data = selectedItems.length > 0 ? selectedItems : tableRecords;

    if (dataType == 'all') {
      const res = await fetchDownloadRecords();
      if (res.result && res.data) {
        data = res.data;
      }
    }
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Data');
    //  headers
    const headerRow = ['NO.', ...temp_headers.map((column) => column.label)];

    worksheet.addRow(headerRow);

    let tr = [];
    if (options?.tab == 'draft_check' && vehicleBase) {
      for (const e of data) {
        for (const v of e?.vehicles) {
          tr.push({ ...e, ...v });
        }
      }
    } else tr = data;

    //  data rows
    tr.forEach((item, index) => {
      const rowData = [
        index + 1,
        ...temp_headers.map((column) => {
          return props[column.id]
            ? props[column.id](item, index)
            : item[column.id];
        }),
      ];
      worksheet.addRow(rowData);

      worksheet.lastRow.eachCell((cell) => {
        cell.alignment = { vertical: 'top', wrapText: true };
      });
    });

    worksheet.getColumn(1).width = 6;
    temp_headers.forEach((column, index) => {
      const columnWidth = column.excelWidth || 15;
      worksheet.getColumn(index + 2).width = columnWidth;
    });

    // Save the workbook
    workbook.xlsx.writeBuffer().then((buffer) => {
      FileSaver.saveAs(new Blob([buffer]), `${pdf_title ?? 'document'}.xlsx`);
    });
  };

  const onCsvDownload = async () => {
    setDownloading(true);
    let data = selectedItems.length > 0 ? selectedItems : tableRecords;

    if (dataType == 'all') {
      const res = await fetchDownloadRecords();
      if (res.result && res.data) {
        data = res.data;
      }
    }
    if (dataType === 'current') {
      try {
        await axios.post('/logs', {
          exportTotal: tableRecords.length,
          exportType: `${pdf_title} CSV`,
        });
      } catch (error) {
        console.error('Error creating log:', error);
      }
    }

    let tr = [];
    if (options?.tab == 'draft_check' && vehicleBase) {
      for (const e of data) {
        for (const v of e?.vehicles) {
          tr.push({ ...e, ...v });
        }
      }
    } else tr = data;

    data = sortDataForDownload(tr, temp_headers, props);
    const heads = temp_headers.map((row) => row.label);

    CsvDownload([heads, ...data], `${pdf_title ?? 'document'}.csv`);
    setDownloading(false);
  };

  useEffect(() => {
    if (open) {
      const data = sortDataForDownload(
        tableRecords.slice(0, 5),
        temp_headers,
        props,
      );
      setPdfData(data);
    }
  }, [open]);

  const CardItem = ({ children }) => {
    return (
      <Card
        variant="outlined"
        sx={{
          p: 1,
          mb: 1,
          height: 60,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: '7px',
          '&:hover': {
            boxShadow: 'md',
            borderColor: 'neutral.outlinedHoverBorder',
          },
        }}
      >
        {children}
      </Card>
    );
  };

  const DownloadPdf = () => {
    const data = sortDataForDownload(tableRecords, temp_headers, props);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [instance, _updateInstance] = usePDF({
      document: (
        <PdfComponent
          items={data}
          headers={temp_headers}
          title={pdf_title}
        ></PdfComponent>
      ),
    });

    const handleDownload = async () => {
      try {
        setLoading(true);
        setError(null);
        try {
          await axios.post('/logs', {
            exportTotal: tableRecords.length,
            exportType: `${pdf_title} PDF`,
          });
        } catch (error) {
          console.error('Error creating log:', error);
        }
        // Trigger the download
        const link = document.createElement('a');
        link.href = instance.url;
        link.download = `${pdf_title ?? 'document'}.pdf`;
        link.click();
      } catch (err) {
        setError('Error');
        console.error('Error downloading PDF:', err);
      } finally {
        setLoading(false);
      }
    };

    if (loading || instance.loading)
      return (
        <Button color="primary" size="small" variant="contained">
          Loading...
        </Button>
      );

    if (error || instance.error)
      return (
        <Button color="error" size="small" variant="contained">
          Error
        </Button>
      );
    return (
      <Button
        onClick={handleDownload}
        color="primary"
        size="small"
        variant="contained"
      >
        Download
      </Button>
    );
  };

  return (
    <Modal
      open={open}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box>
        <Card sx={{ width: matches ? 300 : 450 }}>
          <Box
            sx={{
              m: 0,
              p: 2,
              py: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant="h6">Downloads</Typography>
            <IconButton
              aria-label="close"
              sx={{
                color: 'grey',
              }}
              onClick={() => setShowDownload(false)}
            >
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          <CardContent sx={{ pb: 5 }}>
            {options?.tab == 'draft_check' && (
              <CardItem>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <CompressIcon />
                  <Typography sx={{ pl: 1, fontWeight: 500 }}>
                    Vehicle Base
                  </Typography>
                </Box>
                <Box>
                  <Checkbox
                    checked={vehicleBase}
                    onChange={(e) => setVehicleBase(e.target.checked)}
                  />
                </Box>
              </CardItem>
            )}
            <Typography sx={{ fontSize: '17px', opacity: 0.8, pb: 1 }}>
              Download Types
            </Typography>

            <RadioGroup
              value={downloadFormat}
              onChange={(_event, val) => setDownloadFormat(val)}
            >
              {excelDownload && (
                <CardItem>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <ExcelIcon />
                    <Typography sx={{ pl: 1, fontWeight: 500 }}>
                      Excel Format
                    </Typography>
                  </Box>
                  <Box>
                    <Radio value={'excel'} />
                  </Box>
                </CardItem>
              )}

              {csvDownload && (
                <CardItem>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <CsvIcon />
                    <Typography sx={{ pl: 1, fontWeight: 500 }}>
                      CSV Format
                    </Typography>
                  </Box>
                  <Box>
                    <Radio value={'csv'} />
                  </Box>
                </CardItem>
              )}
              {pdfPreview && (
                <CardItem>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <PdfIcon />
                    <Typography sx={{ pl: 1, fontWeight: 500 }}>
                      PDF Format
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    {matches ? (
                      <></>
                    ) : (
                      <CBasicTooltip
                        onClick={() => setShowPreview(true)}
                        title={'Preview'}
                        icon={<RemoveRedEyeIcon color="primary" />}
                      />
                    )}

                    <Radio value={'pdf'} />
                  </Box>
                </CardItem>
              )}
            </RadioGroup>
            {(downloadFormat == 'csv' || downloadFormat == 'excel') &&
              selectedItems.length == 0 && (
                <>
                  <Typography sx={{ fontSize: '17px', opacity: 0.8, pt: 3 }}>
                    Download Data
                  </Typography>
                  <RadioGroup
                    row
                    aria-labelledby="demo-row-radio-buttons-group-label"
                    name="row-radio-buttons-group"
                    value={dataType}
                    onChange={(_event, val) => setDataType(val)}
                  >
                    <FormControlLabel
                      value={'current'}
                      control={<Radio />}
                      label="Current Data"
                    />
                    <FormControlLabel
                      value={'all'}
                      control={<Radio />}
                      label="All Data"
                    />
                  </RadioGroup>
                </>
              )}
          </CardContent>
          <CardActions
            sx={{ px: 2, pb: 2, display: 'flex', justifyContent: 'end' }}
          >
            <Button
              onClick={() => setShowDownload(false)}
              variant="outlined"
              color="primary"
              size="small"
              sx={{ marginRight: '5px' }}
            >
              Cancel
            </Button>
            {downloadFormat == 'csv' || downloadFormat == 'excel' ? (
              <Button
                color="primary"
                size="small"
                onClick={
                  downloadFormat == 'csv' ? onCsvDownload : handleExportToExcel
                }
                loading={downloading}
                variant="contained"
              >
                Download
              </Button>
            ) : (
              <Box>
                <DownloadPdf></DownloadPdf>
              </Box>
            )}
          </CardActions>
        </Card>
        <PdfPreviewModal
          showPreview={showPreview}
          setShowPreview={setShowPreview}
        >
          <PdfComponent
            items={pdfData}
            headers={temp_headers}
            title={pdf_title}
          ></PdfComponent>
        </PdfPreviewModal>
      </Box>
    </Modal>
  );
};

export default PdfModal;
