import React from 'react';
import Box from '@mui/material/Box';
import { alpha, Avatar, Typography, useTheme } from '@mui/material';
import AppCard from './App/AppCard';

interface CardStateItem {
  color?: string;
  icon?: React.ReactNode;
  count?: number | string;
  title?: string;
}

interface CardStateProps {
  item?: CardStateItem;
  onClick?(...args: unknown[]): unknown;
}

const CardState = ({ item, onClick = () => {} }: CardStateProps) => {
  const theme = useTheme();
  return (
    //@ts-ignore
    <AppCard
      onClick={onClick}
      sxStyle={{
        // height: 1,
        border: `1px solid ${theme.colors.alpha.black[10]}`,
        '&:hover': {
          cursor: 'pointer',
          backgroundColor: theme.colors.alpha.black[10],
        },
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Box sx={{ mr: 1 }}>
          <Avatar
            sx={{
              width: { xs: 30, lg: 35, xl: 35 },
              height: { xs: 30, lg: 35, xl: 35 },
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: item.color
                ? alpha(item?.color, 0.1)
                : 'transparent',
            }}
          >
            <Typography
              sx={{
                color: item.color,
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              className="icon"
            >
              {item.icon}
            </Typography>
          </Avatar>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Box component="h3" sx={{ my: 0 }}>
            {item.count}
          </Box>
          <Box
            component="p"
            sx={{
              my: 0,
              color: 'text.secondary',
              fontSize: 14,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              fontWeight: 'bold',
            }}
          >
            {item.title ?? '0'}
          </Box>
        </Box>
      </Box>
    </AppCard>
  );
};

export default CardState;
