import { useState, useEffect } from 'react';
import { Form<PERSON>abe<PERSON>, Stack } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';

interface DateRange {
  from: string | null;
  to: string | null;
}

interface FilterDateTimeRangeProps {
  label: string;
  values: DateRange;
  changeHandler: (dateRange: DateRange) => void;
}

// Helper function to convert local date to UTC string in the required format
const convertToUTCString = (localDate: Date): string => {
  const pad = (num: number): string => String(num).padStart(2, '0');

  return `${localDate.getUTCFullYear()}-${pad(localDate.getUTCMonth() + 1)}-${pad(localDate.getUTCDate())} ${pad(localDate.getUTCHours())}:${pad(localDate.getUTCMinutes())}:${pad(localDate.getUTCSeconds())}`;
};

// Helper function to convert UTC string back to local date for display
const convertUTCToLocal = (utcString: string): Date => {
  const [datePart, timePart] = utcString.split(' ');
  const [year, month, day] = datePart.split('-').map(Number);
  const [hours, minutes, seconds] = timePart.split(':').map(Number);
  return new Date(Date.UTC(year, month - 1, day, hours, minutes, seconds));
};

// Helper function to create UTC date with specific time
const createUTCDate = (
  date: Date,
  hours: number,
  minutes: number,
  seconds: number,
): Date => {
  return new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      hours,
      minutes,
      seconds,
    ),
  );
};

export default function FilterDateTimeRange({
  label,
  values,
  changeHandler,
}: FilterDateTimeRangeProps) {
  const [dateRange, setDateRange] = useState<DateRange>(values);

  useEffect(() => {
    setDateRange(values);
  }, [values]);

  const handleToDateChange = (event: Date | null) => {
    if (!event) return;

    const localDate = new Date(event);
    const updatedDate =
      localDate.getHours() === 0 && localDate.getMinutes() === 0
        ? createUTCDate(localDate, 20, 29, 59)
        : (() => {
            localDate.setSeconds(59);
            return localDate;
          })();

    const utcString = convertToUTCString(updatedDate);

    setDateRange((state) => {
      const newDateRange = {
        ...state,
        to: utcString,
      };
      changeHandler(newDateRange);
      return newDateRange;
    });
  };

  const handleFromDateChange = (event: Date | null) => {
    if (!event) return;

    const localDate = new Date(event);
    const updatedDate =
      localDate.getHours() === 0 && localDate.getMinutes() === 0
        ? (() => {
            localDate.setHours(1, 0, 0, 0);
            return localDate;
          })()
        : (() => {
            localDate.setSeconds(0);
            return localDate;
          })();

    const utcString = convertToUTCString(updatedDate);

    setDateRange((state) => {
      const newDateRange = {
        ...state,
        from: utcString,
      };
      changeHandler(newDateRange);
      return newDateRange;
    });
  };

  return (
    <>
      <FormLabel>{label}</FormLabel>
      <Stack sx={{ direction: 'column' }} spacing={3}>
        <DateTimePicker
          className="DatePickerCustom"
          sx={{ lineHeight: 10 }}
          format="yyyy/MM/dd hh:mm a"
          value={
            dateRange.from
              ? dayjs(convertUTCToLocal(dateRange.from)).toDate()
              : null
          }
          onChange={handleFromDateChange}
          label="From Date & Time"
          ampm={true}
        />
        <DateTimePicker
          className="DatePickerCustom"
          value={
            dateRange.to
              ? dayjs(convertUTCToLocal(dateRange.to)).toDate()
              : null
          }
          format="yyyy/MM/dd hh:mm a"
          onChange={handleToDateChange}
          label="To Date & Time"
          ampm={true}
        />
      </Stack>
    </>
  );
}
