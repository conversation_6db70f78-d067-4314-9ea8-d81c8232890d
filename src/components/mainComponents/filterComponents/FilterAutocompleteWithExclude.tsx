import { useEffect, useState } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Paper,
  Stack,
  Typography,
  Chip,
  Collapse,
} from '@mui/material';

import FilterAutocomplete from './FilterAutocomplete';

export default function FilterAutocompleteWithExclude({
  url,
  label,
  name,
  keyName,
  values,
  onChange,
  excludeValues,
  onExcludeChange,
  staticOptions = [],
  disabled = false,
  text = 'items',
}) {
  const isExcluding = excludeValues?.includes('exclude') || false;
  const hasSelections = values && values.length > 0;
  const [showDetails, setShowDetails] = useState(false);

  // Auto-expand details when items are selected
  useEffect(() => {
    if (hasSelections && !showDetails) {
      setShowDetails(true);
    }
  }, [hasSelections, showDetails]);

  return (
    <Stack direction="column" spacing={2}>
      <FilterAutocomplete
        url={url}
        label={label}
        name={name}
        keyName={keyName}
        values={values}
        onChange={onChange}
        staticOptions={staticOptions}
        disabled={disabled}
      />

      <Collapse in={hasSelections}>
        <Paper
          variant="outlined"
          sx={{
            borderRadius: 2,
            p: 2,
            backgroundColor: isExcluding ? 'error.50' : 'success.50',
            borderColor: isExcluding ? 'error.main' : 'success.main',
            borderWidth: 2,
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: isExcluding ? 'error.100' : 'success.100',
              transform: 'translateY(-1px)',
              boxShadow: 2,
            },
          }}
        >
          <Stack direction="column" spacing={1.5}>
            {/* Include/Exclude toggle */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                pt: 1,
              }}
            >
              <FormControlLabel
                control={
                  <Checkbox
                    size="small"
                    name="exclude"
                    value="exclude"
                    checked={isExcluding}
                    onChange={(event) => {
                      if (event.target.checked) {
                        onExcludeChange(['exclude']);
                      } else {
                        onExcludeChange([]);
                      }
                    }}
                    sx={{
                      color: isExcluding ? 'error.main' : 'success.main',
                      '&.Mui-checked': {
                        color: isExcluding ? 'error.main' : 'success.main',
                      },
                    }}
                  />
                }
                label={
                  <Typography
                    variant="body2"
                    color={isExcluding ? 'error.dark' : 'success.dark'}
                    fontWeight="medium"
                  >
                    Exclude selected {text}
                  </Typography>
                }
              />

              <Chip
                label={isExcluding ? 'EXCLUDE MODE' : 'INCLUDE MODE'}
                size="small"
                color={isExcluding ? 'error' : 'success'}
                variant="filled"
                sx={{
                  fontSize: '0.7rem',
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                }}
              />
            </Box>
          </Stack>
        </Paper>
      </Collapse>
    </Stack>
  );
}
