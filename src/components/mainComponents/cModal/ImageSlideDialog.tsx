import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Pagination, Navigation } from 'swiper/modules';
import ZoomImage from './ZoomImage';
import { toast } from 'react-toastify';
import axios from '@/lib/axios';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { DialogActions } from '@mui/material';
import Button from '@mui/material/Button';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 2,
};

const ImageSlideDialog = ({
  url,
  files,
  setFullSizeImages,
  imageIndex,
  setOpen,
  open,
  type,
  getVehicleImages,
  setFetchingRecords,
  isDeleteImage,
}) => {
  const activeIndex = React.useRef<number>(0);
  const swiperRef = React.useRef(null);
  const fileKeys = React.useRef<string[]>([]);
  const ids = React.useRef<number[]>([]);
  const [openModal, setOpenModal] = React.useState(false);
  const [isDeletingImage, setIsDeletingImage] = React.useState(false);

  const handleCloseModal = () => {
    setOpenModal(false);
  };
  const handleClose = () => {
    setOpen(false);
  };
  activeIndex.current = imageIndex;
  const setActiveIndex = (index) => {
    activeIndex.current = index;
  };

  const handleOpenDeleteModal = (id, imageUrl) => {
    ids.current.push(id);
    fileKeys.current.push(imageUrl);
    setOpenModal(true);
  };
  const deleteImage = async () => {
    try {
      setIsDeletingImage(true);
      const res = await axios.delete(`${url}/delete-image`, {
        params: { fileKeys: fileKeys.current, ids: ids.current, type },
      });
      if (res?.status == 200) {
        setIsDeletingImage(false);
        fileKeys.current = [];
        const updatedFiles = files.filter((file) => file.id !== ids.current[0]);
        ids.current = [];
        handleCloseModal();
        setFullSizeImages(updatedFiles);
        if (updatedFiles.length === 0) {
          setFetchingRecords(true);
          handleClose();
        }
        setActiveIndex((prevIndex) =>
          prevIndex >= updatedFiles.length ? prevIndex - 1 : prevIndex,
        );
        getVehicleImages();
        toast.success('Image deleted successfully!');
      }
    } catch (error) {
      console.log(error);
    }
  };

  React.useEffect(() => {
    const handleKeyDown = (event) => {
      if (!swiperRef.current) return;

      if (event.key === 'ArrowRight') {
        swiperRef.current.slideNext(); // Move to the next slide
      } else if (event.key === 'ArrowLeft') {
        swiperRef.current.slidePrev(); // Move to the previous slide
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
  return (
    <React.Fragment>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Swiper
              pagination={{ type: 'fraction' }}
              navigation={true}
              modules={[Pagination, Navigation]}
              className="mySwiper"
              initialSlide={activeIndex.current}
              onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
              allowTouchMove={false}
              onSwiper={(swiper) => (swiperRef.current = swiper)}
            >
              {files.map((file) => (
                <SwiperSlide key={file.name}>
                  <ZoomImage
                    file={file}
                    handleClose={handleClose}
                    deleteImage={handleOpenDeleteModal}
                    isDeleteImage={isDeleteImage}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </DialogContentText>
        </DialogContent>
      </Dialog>

      <div>
        <Modal
          open={openModal}
          onClose={(_, reason) => {
            if (reason === 'backdropClick') return;
            handleCloseModal();
          }}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <Typography id="modal-modal-title" variant="h6" component="h2">
              Delete this file?
            </Typography>
            <Typography id="modal-modal-title" component="h6">
              Are you sure you want to delete this file?
            </Typography>
            <DialogActions>
              <Button variant="contained" onClick={deleteImage}>
                <ThumbUpIcon
                  fontSize="small"
                  sx={{ color: 'white', marginRight: '5px' }}
                />
                <span style={{ fontWeight: 'normal', color: 'white' }}>
                  {isDeletingImage ? 'Deleting...' : 'Yes'}
                </span>
              </Button>
              <Button
                variant="outlined"
                color="error"
                onClick={handleCloseModal}
              >
                <ThumbDownIcon
                  fontSize="small"
                  sx={{ color: 'red', marginRight: '5px' }}
                />
                <span style={{ fontWeight: 'normal', color: 'red' }}>No</span>
              </Button>
            </DialogActions>
          </Box>
        </Modal>
      </div>
    </React.Fragment>
  );
};

export default ImageSlideDialog;
