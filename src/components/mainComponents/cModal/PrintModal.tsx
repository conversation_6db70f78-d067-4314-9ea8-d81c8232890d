import React, { useRef } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { useReactToPrint } from 'react-to-print';
import PrintIcon from '@mui/icons-material/Print';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  Divider,
  Fade,
  IconButton,
  Modal,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { LoadingPage } from '@/components/layout/LoadingPage';

interface SectionItemProps {
  title?: string | React.ReactNode;
  children?: React.ReactNode;
}

export const SectionItem = ({
  title,
  children,
  ...reset
}: SectionItemProps) => {
  return (
    <Card
      variant="outlined"
      sx={{
        borderRadius: 1,
        width: '100%',
        maxWidth: '400px',
        height: { sm: '450px' },
        overflow: 'auto',
      }}
    >
      <CardHeader
        sx={{ pb: 0 }}
        title={
          <Box component="p" sx={{ textAlign: 'center', my: 0, pb: 0 }}>
            {title}
          </Box>
        }
      />
      <CardContent {...reset}>{children}</CardContent>
    </Card>
  );
};

export default function PrintModal({
  open = false,
  toggleOpen,
  title,
  ContentPrint,
  initialData,
  maxWidth = '60%',
  fileName = title,
  showNextPrev = false,
  onPrev = () => {},
  onNext = () => {},
  isFirstItem = false,
  isLastItem = false,
  loading = false,
  downloadStatement = false,
  ...rest
}) {
  const componentRef = useRef<HTMLDivElement>(null);
  const handlePrint = useReactToPrint({
    contentRef: componentRef,
    documentTitle: fileName,
  });

  const matches = useMediaQuery('(max-width:950px)');

  return (
    <Modal {...rest} open={open} sx={{ mx: 2 }}>
      <Fade in={open} timeout={50}>
        <Card
          sx={{
            mt: { xs: 2, sm: 8, md: 5 },
            borderRadius: 2,
            mx: 'auto',
            maxWidth: !matches ? maxWidth : '100%',

            bgcolor: 'background.paper',
            boxShadow: 24,
            position: 'relative',
          }}
        >
          <IconButton
            aria-label="close"
            onClick={() => {
              toggleOpen();
            }}
            sx={{ float: 'right', mt: 1 }}
          >
            <CloseIcon sx={{ fontSize: 18 }} />
          </IconButton>
          <CardHeader
            title={
              <Box display="flex" columnGap={3} sx={{ alignItems: 'center' }}>
                <PrintIcon sx={{ fontSize: '30px' }} />
                <Typography variant="h5">{title}</Typography>
              </Box>
            }
          />
          <Divider />
          <CardContent
            sx={{ maxHeight: { xs: '65vh' }, overflowY: 'auto', px: 3 }}
          >
            <Box
              sx={{
                display: 'flex',
                columnGap: 3,
                rowGap: 3,
                justifyContent: 'space-around',
                flexDirection: { xs: 'column', sm: 'row' },
                minHeight: '29.7cm',
              }}
            >
              {loading ? (
                <LoadingPage
                  style={{
                    position: 'absolute',
                    transform: 'translate(-50%, -50%)',
                    top: '50%',
                    left: '50%',
                  }}
                />
              ) : (
                <ContentPrint
                  ref={componentRef}
                  data={initialData}
                  downloadStatement={downloadStatement}
                />
              )}
            </Box>
          </CardContent>
          <Divider />
          <CardActions
            sx={{
              // justifyContent: showNextPrev ? 'space-between' : 'end',
              justifyContent: 'center',
              alignItems: 'center',
              mx: 2,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                mx: 2,
              }}
            >
              {showNextPrev && (
                <Button
                  disabled={isFirstItem}
                  size="small"
                  variant="contained"
                  onClick={() => {
                    onPrev();
                  }}
                >
                  Prev
                </Button>
              )}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: showNextPrev ? 'center' : 'end',
                  width: '100%',
                }}
              >
                <Button
                  sx={{ mx: 0.5 }}
                  size="small"
                  variant="contained"
                  onClick={handlePrint}
                >
                  Print
                </Button>
                <Button
                  size="small"
                  variant="contained"
                  color="warning"
                  onClick={toggleOpen}
                >
                  Cancel
                </Button>
              </Box>
              {showNextPrev && (
                <Button
                  disabled={isLastItem}
                  size="small"
                  variant="contained"
                  onClick={() => {
                    onNext();
                  }}
                >
                  Next
                </Button>
              )}
            </Box>
          </CardActions>
        </Card>
      </Fade>
    </Modal>
  );
}
