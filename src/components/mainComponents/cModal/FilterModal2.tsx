import React, { useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import FilterListRoundedIcon from '@mui/icons-material/FilterListRounded';
import FilterAutocomplete from '../filterComponents/FilterAutocomplete';
import FilterCheckbox from '../filterComponents/FilterCheckbox';
import FilterNumberRange from '../filterComponents/FilterNumberRange';
import FilterDateRange from '../filterComponents/FilterDateRange';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  Divider,
  Fade,
  IconButton,
  Modal,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import FilterRadio from '../filterComponents/FilterRadio';
import NullableFilterRadio from '../filterComponents/NullableFilterRadio';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { formFormatDate } from '@/configs/configs';
import FilterDateTimeRange from '../filterComponents/FilterDateTimeRange';
import FilterAutocompleteWithExclude from '../filterComponents/FilterAutocompleteWithExclude';

interface SectionItemProps {
  title?: string | React.ReactNode;
  children?: React.ReactNode;
}

const SectionItem = ({ title, children, ...reset }: SectionItemProps) => {
  return (
    <Card
      variant="outlined"
      sx={{
        borderRadius: 1,
        width: '100%',
        maxWidth: '400px',
        maxHeight: { sm: '500px' },
        minHeight: { sm: '450px' },
        overflow: 'auto',
      }}
    >
      <CardHeader
        sx={{ pb: 0 }}
        title={
          <Box component="p" sx={{ textAlign: 'center', my: 0, pb: 0 }}>
            {title}
          </Box>
        }
      />
      <CardContent {...reset}>{children}</CardContent>
    </Card>
  );
};

export default function FilterModal2({
  open = false,
  toggleOpen,
  title,
  content,
  options,
  setOptions,
  ...rest
}) {
  const [filterData, setFilterData] = useState(options.filterData);
  const [days, setDays] = useState(0);

  const applyFilter = () => {
    for (const key in filterData) {
      if (filterData[key].length === 0) {
        delete filterData[key];
      }
    }
    setOptions({ ...options, page: 1, filterData: filterData });
    toggleOpen();
  };

  const setDate = (dateRange, item) => {
    if (dateRange?.from === 'NaN-NaN-NaN' || dateRange?.to === 'NaN-NaN-NaN') {
      delete filterData.invoice_date;
    } else {
      setFilterData((state) => {
        return {
          ...state,
          [item.name]: dateRange,
        };
      });
    }
  };
  const resetFun = () => {
    setDays(0);
    setFilterData({});
    setOptions({ ...options, page: 1, filterData: {} });
  };

  return (
    <Modal {...rest} open={open}>
      <Fade in={open} timeout={50}>
        <Card
          sx={{
            borderRadius: 2,
            mx: 'auto',
            width: {
              xs: '100%',
              sm: (content.length == 2 ? 450 : 400) * content.length + 'px',
            },
            bgcolor: 'background.paper',
            boxShadow: 24,
            position: 'absolute',
            left: '50%',
            top: '40%',

            transform: 'translate(-50%, -50%)',
            overflow: 'unset !important',
          }}
        >
          <IconButton
            aria-label="close"
            onClick={toggleOpen}
            sx={{ float: 'right', mt: 1 }}
          >
            <CloseIcon sx={{ fontSize: 18 }} />
          </IconButton>
          <CardHeader
            title={
              <Box display="flex" columnGap={3}>
                <FilterListRoundedIcon />
                <Typography variant="h5">{title}</Typography>
              </Box>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: { xs: '50vh', md: '65vh' },
              overflowY: 'auto',
              px: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                columnGap: 3,
                rowGap: 3,
                justifyContent: 'space-around',
                flexDirection: { xs: 'column', sm: 'row' },
              }}
            >
              {content?.map((section) => (
                <SectionItem title={section.title} key={section.title}>
                  {section.items.map((item, index) => (
                    <Box mb={1} key={index + 'items'}>
                      {item.type == 'autocomplete' && (
                        <FilterAutocomplete
                          url={item.url}
                          label={item.label}
                          name={item.name}
                          keyName={item.keyName ?? item.name}
                          values={filterData[item.name] ?? []}
                          staticOptions={item.options ?? []}
                          onChange={(event) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: event,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'checkbox' && (
                        <FilterCheckbox
                          label={item.label}
                          items={item.items}
                          values={filterData[item.name] ?? []}
                          changeHandler={(checkedItems) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: checkedItems,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'boolean' && (
                        <FilterRadio
                          label={item.label}
                          items={item.items}
                          value={filterData[item.name] ?? []}
                          changeHandler={(checkedItem) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: checkedItem,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'null_check' && (
                        <NullableFilterRadio
                          label={item.label}
                          items={item.items}
                          value={filterData[item.name] ?? []}
                          changeHandler={(checkedItem) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: checkedItem,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'textfield' && (
                        <Stack sx={{ direction: 'column' }} spacing={3}>
                          <TextField
                            size="small"
                            label={item.label}
                            variant="outlined"
                            value={filterData[item.name] ?? ''}
                            onChange={(event) => {
                              setFilterData((state) => {
                                return {
                                  ...state,
                                  [item.name]: event.target.value,
                                };
                              });
                            }}
                          />
                        </Stack>
                      )}
                      {item.type == 'autocomplete_with_exclude' && (
                        <FilterAutocompleteWithExclude
                          url={item.url}
                          label={item.label}
                          name={item.name}
                          keyName={item.keyName ?? item.name}
                          values={filterData[item.name] ?? []}
                          staticOptions={item.options ?? []}
                          onChange={(event) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: event,
                              };
                            });
                          }}
                          excludeValues={filterData[item.excludeName] ?? []}
                          onExcludeChange={(excludeValues) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.excludeName]: excludeValues,
                              };
                            });
                          }}
                          text={item.text || 'items'}
                        />
                      )}
                      {item.type == 'number' && (
                        <Stack sx={{ direction: 'column' }} spacing={3}>
                          <TextField
                            size="small"
                            label={item.label}
                            variant="outlined"
                            value={filterData[item.name] ?? ''}
                            type="number"
                            onChange={(event) => {
                              setFilterData((state) => {
                                return {
                                  ...state,
                                  [item.name]: +event.target.value,
                                };
                              });
                            }}
                          />
                        </Stack>
                      )}
                      {item.type == 'number_range' && (
                        <FilterNumberRange
                          item={item}
                          values={filterData[item.name] ?? []}
                          changeHandler={(numberRange) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: numberRange,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'date_range' && (
                        <>
                          {item.name === 'created_at' &&
                          item.label === 'Report Date to PGL' ? (
                            <FilterDateTimeRange
                              label={item.label}
                              values={filterData[item.name] ?? {}}
                              changeHandler={(dateRange) =>
                                setDate(dateRange, item)
                              }
                            />
                          ) : (
                            <FilterDateRange
                              label={item.label}
                              values={filterData[item.name] ?? {}}
                              changeHandler={(dateRange) =>
                                setDate(dateRange, item)
                              }
                            />
                          )}
                          {section.items.length - 1 != index && (
                            <Divider sx={{ my: 1.5 }} />
                          )}
                        </>
                      )}
                      {item.type == 'date' && (
                        <DatePicker
                          views={['year', 'month', 'day']}
                          className="DatePickerCustom"
                          sx={{
                            lineHeight: 10,
                          }}
                          format="yyyy/MM/dd"
                          value={
                            filterData[item.name]
                              ? dayjs(filterData[item.name]).toDate()
                              : null
                          }
                          onChange={(event) => {
                            if (event) {
                              setFilterData((state) => {
                                return {
                                  ...state,
                                  [item.name]: formFormatDate(event),
                                };
                              });
                            }
                          }}
                          label={item.label}
                        />
                      )}
                      {item.type == 'dateIn' && (
                        <Stack sx={{ direction: 'column' }} spacing={3}>
                          <TextField
                            size="small"
                            label={item.label}
                            variant="outlined"
                            type="number"
                            value={days}
                            onChange={(event) => {
                              const inputDays = +event.target.value;
                              setDays(inputDays);

                              if (inputDays === 0) {
                                setFilterData((state) => ({
                                  ...state,
                                  [item.name]: '',
                                }));
                                return;
                              }
                              const currentDate = dayjs();
                              const futureDate =
                                inputDays >= 0
                                  ? currentDate
                                      .add(inputDays, 'day')
                                      .format('YYYY/MM/DD')
                                  : currentDate
                                      .subtract(Math.abs(inputDays), 'day')
                                      .format('YYYY/MM/DD');

                              setFilterData((state) => ({
                                ...state,
                                [item.name]: {
                                  from:
                                    inputDays >= 0
                                      ? currentDate.format('YYYY/MM/DD')
                                      : futureDate,
                                  to:
                                    inputDays >= 0
                                      ? futureDate
                                      : currentDate.format('YYYY/MM/DD'),
                                },
                              }));
                            }}
                          />
                        </Stack>
                      )}
                    </Box>
                  ))}
                </SectionItem>
              ))}
            </Box>
          </CardContent>
          <Divider />
          <CardActions sx={{ justifyContent: 'center', mx: 2 }}>
            <Button
              size="small"
              variant="contained"
              onClick={() => applyFilter()}
            >
              Apply
            </Button>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={resetFun}
            >
              Reset
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="primary"
              onClick={toggleOpen}
            >
              Cancel
            </Button>
          </CardActions>
        </Card>
      </Fade>
    </Modal>
  );
}
