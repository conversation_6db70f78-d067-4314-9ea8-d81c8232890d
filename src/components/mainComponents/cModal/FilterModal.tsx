import React, { useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import FilterListRoundedIcon from '@mui/icons-material/FilterListRounded';
import FilterAutocomplete from '../filterComponents/FilterAutocomplete';
import FilterCheckbox from '../filterComponents/FilterCheckbox';
import FilterNumberRange from '../filterComponents/FilterNumberRange';
import FilterDateRange from '../filterComponents/FilterDateRange';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  Divider,
  Fade,
  IconButton,
  Modal,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import FilterRadio from '../filterComponents/FilterRadio';

interface SectionItemProps {
  title?: string | React.ReactNode;
  children?: React.ReactNode;
}

const SectionItem = ({ title, children, ...reset }: SectionItemProps) => {
  return (
    <Card
      variant="outlined"
      sx={{
        borderRadius: 1,
        width: '100%',
        maxWidth: '400px',
        maxHeight: { sm: '500px' },
        minHeight: { sm: '450px' },
        overflow: 'auto',
      }}
    >
      <CardHeader
        sx={{ pb: 0 }}
        title={
          <Box component="p" sx={{ textAlign: 'center', my: 0, pb: 0 }}>
            {title}
          </Box>
        }
      />
      <CardContent {...reset}>{children}</CardContent>
    </Card>
  );
};

export default function FilterModal({
  open = false,
  toggleOpen,
  title,
  content,
  initialData,
  updateFilterData,
  ...rest
}) {
  const [filterData, setFilterData] = useState(initialData);

  const resetFun = () => {
    setFilterData({});
    updateFilterData({});
    // toggleOpen();
  };

  return (
    <Modal {...rest} open={open} sx={{ mx: 2 }}>
      <Fade in={open} timeout={50}>
        <Card
          sx={{
            mt: { xs: 2, sm: 8, md: 15 },
            borderRadius: 2,
            mx: 'auto',
            maxWidth: {
              xs: '400px',
              sm: (content.length == 2 ? 450 : 400) * content.length + 'px',
            },
            bgcolor: 'background.paper',
            boxShadow: 24,
            position: 'relative',
          }}
        >
          <IconButton
            aria-label="close"
            onClick={toggleOpen}
            sx={{ float: 'right', mt: 1 }}
          >
            <CloseIcon sx={{ fontSize: 18 }} />
          </IconButton>
          <CardHeader
            title={
              <Box display="flex" columnGap={3}>
                <FilterListRoundedIcon />
                <Typography variant="h5">{title}</Typography>
              </Box>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: { xs: '65vh' },
              overflowY: 'auto',
              px: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                columnGap: 3,
                rowGap: 3,
                justifyContent: 'space-around',
                flexDirection: { xs: 'column', sm: 'row' },
              }}
            >
              {content.map((section) => (
                <SectionItem title={section.title} key={section.title}>
                  {section.items.map((item, index) => (
                    <Box mb={1} key={index + 'items'}>
                      {item.type == 'autocomplete' && (
                        <FilterAutocomplete
                          url={item.url}
                          label={item.label}
                          name={item.name}
                          keyName={item.keyName ?? item.name}
                          values={filterData[item.name] ?? []}
                          staticOptions={item.options ? item.options : []}
                          onChange={(event) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: event,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'checkbox' && (
                        <FilterCheckbox
                          label={item.label}
                          items={item.items}
                          values={filterData[item.name] ?? []}
                          changeHandler={(checkedItems) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: checkedItems,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'boolean' && (
                        <FilterRadio
                          label={item.label}
                          items={item.items}
                          value={filterData[item.name] ?? []}
                          changeHandler={(checkedItem) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: checkedItem,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'textfield' && (
                        <Stack sx={{ direction: 'column' }} spacing={3}>
                          <TextField
                            size="small"
                            label={item.label}
                            variant="outlined"
                            value={filterData[item.name] ?? ''}
                            onChange={(event) => {
                              setFilterData((state) => {
                                return {
                                  ...state,
                                  [item.name]: event.target.value,
                                };
                              });
                            }}
                          />
                        </Stack>
                      )}
                      {item.type == 'number_range' && (
                        <FilterNumberRange
                          item={item}
                          values={filterData[item.name] ?? []}
                          changeHandler={(numberRange) => {
                            setFilterData((state) => {
                              return {
                                ...state,
                                [item.name]: numberRange,
                              };
                            });
                          }}
                        />
                      )}
                      {item.type == 'date_range' && (
                        <>
                          <FilterDateRange
                            label={item.label}
                            values={filterData[item.name] ?? {}}
                            changeHandler={(dateRange) => {
                              setFilterData((state) => {
                                return {
                                  ...state,
                                  [item.name]: dateRange,
                                };
                              });
                            }}
                          />
                          {section.items.length - 1 != index && (
                            <Divider sx={{ my: 1.5 }} />
                          )}
                        </>
                      )}
                    </Box>
                  ))}
                </SectionItem>
              ))}
            </Box>
          </CardContent>
          <Divider />
          <CardActions sx={{ justifyContent: 'end', mx: 2 }}>
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                updateFilterData(filterData);
                toggleOpen();
              }}
            >
              Apply
            </Button>
            <Button
              size="small"
              variant="contained"
              color="warning"
              onClick={resetFun}
            >
              Reset
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="primary"
              onClick={toggleOpen}
            >
              Cancel
            </Button>
          </CardActions>
        </Card>
      </Fade>
    </Modal>
  );
}
