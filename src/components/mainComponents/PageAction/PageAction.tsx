import {
  Badge,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Switch,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { Box } from '@mui/system';
import CBasicTooltip from '../datatable/cBasicTooltip';
import ViewWeekIcon from '@mui/icons-material/ViewWeek';
import RefreshIcon from '@mui/icons-material/Refresh';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import { useEffect, useState } from 'react';
import AppTooltip from '../cComponents/AppTooltip';
import AppsDeleteIcon from '../cComponents/AppDeletionIcon';
import EditIcon from '@mui/icons-material/Edit';
import FilterListRoundedIcon from '@mui/icons-material/FilterListRounded';
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline';
import AppRestoreIcon from '../cComponents/AppRestoreIcon';
import SimCardDownloadIcon from '@mui/icons-material/SimCardDownload';
import AssessmentIcon from '@mui/icons-material/Assessment';

const PageAction = ({
  isExcel = false,
  title,
  total,
  showAddButton = false,
  showUploadButton = false,
  onAdd = () => {},
  onUpload = () => {},
  onEdit = () => {},
  addButtonTitle = 'Add',
  showCustomizeColumn = false,
  setOptions,
  selectedItems,
  showDeleteButton = false,
  showEditButton = false,
  deleteTitle = '',
  dialogTitle = '',
  customActionButtons = () => <></>,
  onCustomizeColumn = () => {},
  onDelete = () => {},
  onFilterClick = () => {},
  isFilterExist = true,
  showDownload = false,
  options,
  onDownload = () => {},
  startCustomComponent = <></>,
  searchComponentSlot = <></>,
  customComponent = <></>,
  hideFilter = false,
  onRefreshPage = () => {},
  showRefresh = false,
  onEnter = () => {},
  showRestore = false,
  onRestore = () => {},
  restoreTitle = '',
  restoreDialogText = '',
  trash = false,
  passwordOnDelete = false,
  reasonOnDelete = false,
  deletePassword = 'pgl system',
  showSearch = true,
  showExactMutch = true,
  hasMultiEdit = false,
  showSummeryDownloadButton = false,
  setOpenDownloadCompaniesSummery = () => {},
  setShowFinancialDueDownload = () => {},
  loadingPDF = false,
  showBKCLDCEditBtn = false,
  handleOpenBookingCostUpdate = () => {},
  showTransactionNumber = false,
  deleteReason = null,
  onDeleteReason = null,

  // searchBy = () => <></>,
}) => {
  const [search, setSearch] = useState('');
  const [timeoutId, setTimeOutId] = useState(null);
  const matches = useMediaQuery('(max-width:491px)');
  const matches2 = useMediaQuery('(max-width:860px)');

  const debounce = (callback, delay: number) => {
    clearTimeout(timeoutId);
    setTimeOutId(setTimeout(callback, delay));
  };
  useEffect(() => {
    if (options && options.search == '') {
      setSearch('');
    }
  }, [options?.search]);

  // const searchInputRef = useRef(null);
  // useEffect(() => {
  //   const handleKeyDown = (event) => {
  //     if (event.ctrlKey && event.key === 'f') {
  //       event.preventDefault();
  //       searchInputRef.current.focus();
  //     }
  //   };
  //   document.addEventListener('keydown', handleKeyDown);
  //   return () => {
  //     document.removeEventListener('keydown', handleKeyDown);
  //   };
  // }, []);

  return (
    <Box
      sx={{
        px: '10px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
      }}
    >
      <Box style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
        <Typography
          variant="h5"
          sx={{ textTransform: 'capitalize', fontSize: '18px' }}
        >
          <Box sx={matches ? { my: 0.7 } : { my: 0 }}>{title}</Box>
        </Typography>

        <Badge
          badgeContent={total}
          max={99999999}
          color="primary"
          sx={{ ml: 4 }}
        />

        {showSearch && (
          <>
            <FormControl
              sx={!matches ? { ml: 5 } : { ml: 0 }}
              variant="outlined"
              size="small"
            >
              <OutlinedInput
                id="search"
                autoComplete="off"
                placeholder="Type something..."
                value={search}
                onChange={(event) => {
                  const e = event.target.value;
                  setSearch(e);
                  debounce(
                    () => setOptions({ ...options, search: e, page: 1 }),
                    1000,
                  );
                }}
                onKeyPress={(event) => {
                  if (event.key === 'Enter') {
                    clearTimeout(timeoutId);
                    if (search == options.search && search != '') {
                      onEnter();
                    } else {
                      setOptions({ ...options, search: search, page: 1 });
                    }
                  }
                }}
                inputProps={{
                  autoComplete: 'off',
                }}
                startAdornment={
                  <InputAdornment position="start">
                    <SearchOutlinedIcon />
                  </InputAdornment>
                }
              />
            </FormControl>
            {showTransactionNumber && (
              <FormControlLabel
                sx={!matches ? { ml: 1 } : { ml: 0 }}
                style={{ fontSize: '12px' }}
                labelPlacement="start"
                control={
                  <Switch
                    className="mySwitch"
                    checked={options?.transactionNumber}
                    onChange={() =>
                      setOptions({
                        ...options,
                        transactionNumber: !options?.transactionNumber,
                      })
                    }
                  />
                }
                label="Transaction Number Match"
              />
            )}
            {showExactMutch && (
              <FormControlLabel
                sx={!matches ? { ml: 1 } : { ml: 0 }}
                style={{ fontSize: '12px' }}
                labelPlacement="start"
                control={
                  <Switch
                    className="mySwitch"
                    checked={options?.exactMatch}
                    onChange={() =>
                      setOptions({
                        ...options,
                        exactMatch: !options?.exactMatch,
                      })
                    }
                  />
                }
                label="Exact match"
              />
            )}

            {searchComponentSlot}
          </>
        )}
        {/* <Box style={{ marginLeft: '20px' }}>{searchBy()}</Box> */}
      </Box>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {startCustomComponent}
        {selectedItems?.length == 0 ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              justifyContent: 'end',
              flexWrap: 'warp',
            }}
            style={matches2 ? { width: '100%' } : { width: 'unset' }}
          >
            {showRefresh && (
              <CBasicTooltip
                onClick={onRefreshPage}
                title="Refresh"
                icon={<RefreshIcon />}
              />
            )}
            {showCustomizeColumn && !matches ? (
              <CBasicTooltip
                onClick={onCustomizeColumn}
                title="Customize Column"
                icon={<ViewWeekIcon />}
              />
            ) : (
              <></>
            )}

            {showUploadButton && (
              <CBasicTooltip
                onClick={onUpload}
                title={'Mix Shipping Rates Excel'}
                icon={<CloudUploadIcon />}
              />
            )}
            {showDownload &&
              (!loadingPDF ? (
                <CBasicTooltip
                  onClick={onDownload}
                  title={'Download'}
                  icon={<DownloadForOfflineIcon />}
                />
              ) : (
                <CircularProgress size="1.5rem" />
              ))}
            {isExcel && (
              <CBasicTooltip
                onClick={onDownload}
                title={'Download'}
                icon={<DownloadForOfflineIcon />}
              />
            )}
            {onFilterClick && !hideFilter && (
              <CBasicTooltip
                onClick={onFilterClick}
                title={'Filter'}
                icon={
                  <Badge
                    color="primary"
                    variant="dot"
                    invisible={isFilterExist}
                  >
                    <FilterListRoundedIcon />{' '}
                  </Badge>
                }
              />
            )}

            {showAddButton && (
              <CBasicTooltip
                onClick={onAdd}
                title={addButtonTitle}
                icon={<AddCircleIcon />}
              />
            )}
          </Box>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'end',
              flexWrap: 'warp',
              alignItems: 'center',
            }}
            style={matches ? { width: '100%' } : { width: 'unset' }}
          >
            {showBKCLDCEditBtn && selectedItems.length === 1 && (
              <CBasicTooltip
                onClick={handleOpenBookingCostUpdate}
                title={'Edit Booking and Loading Costs'}
                icon={<EditIcon />}
              />
            )}
            {showSummeryDownloadButton &&
              selectedItems.length > 0 &&
              selectedItems.length < 2 && (
                <CBasicTooltip
                  title={'Download Summery'}
                  onClick={setOpenDownloadCompaniesSummery}
                  icon={<SimCardDownloadIcon color="success" />}
                />
              )}

            {showSummeryDownloadButton &&
              selectedItems.length > 0 &&
              selectedItems.length < 2 && (
                <CBasicTooltip
                  title={'Download Financial Report'}
                  onClick={setShowFinancialDueDownload}
                  icon={<AssessmentIcon color="success" />}
                />
              )}

            {showDownload &&
              selectedItems.length > 0 &&
              (!loadingPDF ? (
                <CBasicTooltip
                  onClick={onDownload}
                  title={'Download'}
                  icon={<DownloadForOfflineIcon />}
                />
              ) : (
                <CircularProgress size="1.5rem" />
              ))}

            {customActionButtons()}
            {showEditButton &&
              (hasMultiEdit
                ? selectedItems.length <= 500
                : selectedItems.length == 1) && (
                <AppTooltip title={'Edit'}>
                  <IconButton color="secondary" onClick={onEdit}>
                    <EditIcon />
                  </IconButton>
                </AppTooltip>
              )}
            {showRestore && (
              <AppRestoreIcon
                onRestore={onRestore}
                dialogTitle={restoreTitle}
                deleteTitle={restoreDialogText}
                sx={{
                  cursor: 'pointer',
                }}
              />
            )}

            {showDeleteButton && (
              <AppsDeleteIcon
                passwordOnDelete={passwordOnDelete}
                reasonOnDelete={reasonOnDelete}
                deletePassword={deletePassword}
                trash={trash}
                deleteAction={onDelete}
                deleteTitle={deleteTitle}
                dialogTitle={dialogTitle}
                deleteReason={deleteReason}
                onDeleteReason={onDeleteReason}
                sx={{
                  cursor: 'pointer',
                  color: 'text.disabled',
                }}
              />
            )}
          </Box>
        )}
        {customComponent}
      </Box>
    </Box>
  );
};
export default PageAction;
