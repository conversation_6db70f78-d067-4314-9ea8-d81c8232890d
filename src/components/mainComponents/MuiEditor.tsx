import React, { useState, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import { Box } from '@mui/material';
import axios from '@/lib/axios';

const Quill = dynamic(() => import('react-quill-new'), {
  ssr: false, // Disable server-side rendering
});

const QuillEditor = ({
  value,
  onChange,
  height = 300,
  placeholder = '',
  uploadImageUrl = '',
  ...attributes
}) => {
  const [editorValue, setEditorValue] = useState(value);

  const handleEditorChange = (content: string) => {
    setEditorValue(content);
    onChange && onChange(content);
  };

  useEffect(() => {
    onChange(editorValue);
  }, [editorValue]);

  const handleImageUpload = async (file: string | Blob) => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      setEditorValue((val: string) =>
        val == '<p><br></p>'
          ? `<p><img src="/images/placeholder.png" alt="placeholder"/></p>`
          : val +
            `<p><img src="/images/placeholder.png" alt="placeholder" /></p>`,
      );
      const response = await axios.post(uploadImageUrl, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      const imageUrl = response.data.url;

      setEditorValue((val: string) => {
        return val.replace(`/images/placeholder.png`, `${imageUrl}`);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
    }
  };

  const modules = useMemo(() => {
    return {
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike', 'link'],
          [{ color: [] }, { background: [] }],
          [{ script: 'sub' }, { script: 'super' }],
          ['blockquote', 'code-block'],
          [{ list: 'ordered' }, { list: 'bullet' }],
          [{ indent: '-1' }, { indent: '+1' }, { align: [] }],
          ['link', 'image'],
          ['clean'],
        ],
        handlers: {
          [uploadImageUrl ? 'image' : '']: function () {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();
            input.onchange = async function () {
              const file = input.files[0];
              await handleImageUpload(file);
            };
          },
        },
      },
      clipboard: { matchVisual: false },
    };
  }, []);

  return (
    <Box sx={{ height: height + 40 }}>
      <Quill
        placeholder={placeholder}
        style={{ height: height + 'px' }}
        theme="snow"
        value={editorValue}
        onChange={handleEditorChange}
        modules={modules}
        {...attributes}
      />
    </Box>
  );
};

export default QuillEditor;
