import TabPanel from '@mui/lab/TabPanel';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Skeleton,
  useTheme,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import React, { useEffect, useState } from 'react';
import { formatDate } from '@/configs/vehicles/configs';
import axios from '@/lib/axios';
import { removeUnderScore } from '@/configs/common';
import moment from 'moment';

const ViewLogs = ({ data, value = 'logs' }) => {
  const theme = useTheme();
  const [id, setId] = useState<string | false>(false);
  const [detail, setDetail] = useState<any | {}>({});
  const [cancelToken, setCancelToken] = useState<AbortController | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      const fetchData = async () => {
        setLoading(true);
        try {
          if (cancelToken) await cancelToken.abort();
          const controller = new AbortController();
          setCancelToken(controller);
          const res = await axios.get(`logs/getOne/${id}`, {
            signal: controller.signal,
          });
          if (res?.status == 200 || res?.data?.result) {
            setLoading(false);
            setDetail({ ...detail, [id]: res?.data?.data });
          }
        } catch (error) {
          setLoading(false);
          console.log(error);
        }
      };
      fetchData();
    }
  }, [id]);

  const getChangedItems = (row) => {
    const properties = row.properties;
    if (
      (properties &&
        (row?.operation == 'updated' ||
          row?.operation == 'deleted' ||
          row?.operation == 'add to container')) ||
      row?.operation == 'remove from container'
    ) {
      delete properties?.attributes?.deleted_at;
      delete properties?.attributes?.deleted_by;
      delete properties?.attributes?.updated_at;
      delete properties?.attributes?.updated_by;
      let custom = Object.keys(properties?.attributes)?.map((w) => {
        if (properties.attributes[w] !== properties.old[w]) {
          return '[' + removeUnderScore(w.replace(/_id/, '')) + ']';
        }
      });

      custom = custom.filter((item) => item !== undefined);
      return custom.length > 3
        ? custom.slice(0, 3).join(', ') + ' ...'
        : custom.join(', ');
    }
    return row.operation === 'created' ? 'All' : '-';
  };

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (cancelToken) {
        cancelToken.abort();
      }
    };
  }, []);

  // const renderLogDetails = (log) => {
  //   const { properties, operation, operated_by, operated_at } = log;
  //   const { attributes, old } = properties;

  //   return (
  //     <Box>
  //       <Typography variant="subtitle2">
  //         Operation: {operation}
  //       </Typography>
  //       <Typography variant="body2">
  //         By: {operated_by}
  //       </Typography>
  //       <Typography variant="body2">
  //         At: {moment(operated_at).format('YYYY-MM-DD HH:mm:ss')}
  //       </Typography>

  //       {/* Show Changes */}
  //       <Box mt={1}>
  //         {Object.keys(attributes).map((key) => {
  //           if (key !== 'updated_at') {
  //             return (
  //               <Box key={key} mb={1}>
  //                 <Typography variant="body2" color="textSecondary">
  //                   Field: {key}
  //                 </Typography>
  //                 <Typography variant="body2" color="error">
  //                   Old: {old[key]}
  //                 </Typography>
  //                 <Typography variant="body2" color="primary">
  //                   New: {attributes[key]}
  //                 </Typography>
  //               </Box>
  //             );
  //           }
  //           return null;
  //         })}
  //       </Box>
  //     </Box>
  //   );
  // };

  return (
    <Box sx={{ height: '47vh', overflow: 'auto', overflowX: 'hidden' }}>
      {data?.length > 0 ? (
        <TabPanel value={value} sx={{ px: 0, py: 1 }}>
          <Grid
            container
            sx={{
              paddingY: '6px',
              fontWeight: 'bold',
              paddingLeft: '20px',
              backgroundColor:
                theme.palette.mode === 'dark' ? 'gray' : 'lightgray',
            }}
          >
            <Grid
              size={{
                xs: 1,
                sm: 1,
                md: 1,
              }}
            >
              No
            </Grid>
            <Grid
              size={{
                xs: 1,
                sm: 1,
                md: 1,
              }}
            >
              OPERATIONS
            </Grid>
            <Grid
              size={{
                xs: 2,
                sm: 2,
                md: 2,
              }}
            >
              OPERATED TYPE
            </Grid>
            <Grid
              size={{
                xs: 4,
                sm: 4,
                md: 4,
              }}
            >
              CHANGED ITEMS
            </Grid>
            <Grid
              size={{
                xs: 2,
                sm: 2,
                md: 2,
              }}
            >
              OPERATED BY
            </Grid>
            <Grid
              size={{
                xs: 2,
                sm: 2,
                md: 2,
              }}
            >
              OPERATED AT
            </Grid>
          </Grid>
          {data?.map((d, i) => (
            <Accordion
              key={i}
              expanded={id === d.id}
              onChange={() => setId(id === d.id ? false : d.id)}
              sx={{
                background: id === d.id ? 'rgba(26, 102, 214, 0.1)' : '',
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1a-content"
                id="panel1a-header"
                sx={{ fontStyle: id === d.id ? 'italic' : '' }}
              >
                <Grid container sx={{ paddingLeft: '10px' }}>
                  <Grid
                    size={{
                      xs: 1,
                      sm: 1,
                      md: 1,
                    }}
                  >
                    {i + 1}
                  </Grid>
                  <Grid
                    size={{
                      xs: 1,
                      sm: 1,
                      md: 1,
                    }}
                  >
                    {d.operation}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {d.causer_type}
                  </Grid>
                  <Grid
                    size={{
                      xs: 4,
                      sm: 4,
                      md: 4,
                    }}
                  >
                    {getChangedItems(d)}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {d.operated_by}
                  </Grid>
                  <Grid
                    size={{
                      xs: 2,
                      sm: 2,
                      md: 2,
                    }}
                  >
                    {formatDate(d.operated_at, 'YYYY MMMM DD hh:mm A')}
                  </Grid>
                </Grid>
              </AccordionSummary>
              <AccordionDetails>
                {loading ? (
                  <Skeleton animation="wave" />
                ) : (
                  detail[d.id] != undefined &&
                  (d.operation === 'Export' ? (
                    <ExportLogDetail data={d} />
                  ) : (
                    <LogDetails data={detail[d.id]} />
                  ))
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </TabPanel>
      ) : (
        <TabPanel value={value} sx={{ px: 0, py: 1 }}>
          <Box sx={{ textAlign: 'center' }}>Logs Not Found !!!!</Box>
        </TabPanel>
      )}
    </Box>
  );
};

export default ViewLogs;

export function LogDetails({ data }) {
  // console.log(data);

  const theme = useTheme();
  let a = 0;

  const attributes = data?.attributes || {};
  const oldValues = data?.old || {};

  const checkValue = (value: any, field: string) => {
    if (value === null || value === undefined) {
      return '-';
    }
    // Special handling for damage_happened_at and damage_details
    // if (
    //   field === 'damage_happened_at' ||
    //   field === 'damage_details' ||
    //   field === 'payments'
    // ) {
    //   if (Array.isArray(value)) {
    //     // Remove quotes and parse if the value is a stringified array
    //     if (typeof value[0] === 'string' && value[0].startsWith('"')) {
    //       return value.map((v) => JSON.parse(v)).join(', ');
    //     }
    //     return value.join(', ');
    //   }
    //   // Handle stringified array
    //   if (typeof value === 'string' && value.startsWith('[')) {
    //     try {
    //       const parsed = JSON.parse(value);
    //       return parsed.join(', ');
    //     } catch {
    //       return value;
    //     }
    //   }
    //   return value;
    // }

    if (
      field === 'damage_happened_at' ||
      field === 'damage_details' ||
      field === 'payments'
    ) {
      if (Array.isArray(value)) {
        if (typeof value[0] === 'string' && value[0].startsWith('"')) {
          return value.map((v) => JSON.parse(v)).join(', ');
        }

        // 👉 This handles arrays of objects like in `payments`
        if (typeof value[0] === 'object') {
          return (
            <>
              {value.map((v, index) => (
                <Box key={index} sx={{ mb: 1 }}>
                  {Object.entries(v).map(([k, val]) => (
                    <div key={k}>
                      {k}: {val === null ? 'null' : val.toString()}
                    </div>
                  ))}
                </Box>
              ))}
            </>
          );
        }

        return value.join(', ');
      }

      // Handle stringified arrays
      // if (typeof value === 'string' && value.startsWith('[')) {
      //   try {
      //     const parsed = JSON.parse(value);

      //     if (
      //       Array.isArray(parsed) &&
      //       typeof parsed[0] === 'object' &&
      //       parsed[0] !== null
      //     ) {
      //       return (
      //         <Box>
      //           {parsed.map((v, index) => (
      //             <Box key={index} sx={{ mb: 1 }}>
      //               {Object.entries(v).map(([k, val]) => (
      //                 <div key={k}>
      //                   {k}: {val === null ? 'null' : val.toString()}
      //                 </div>
      //               ))}
      //             </Box>
      //           ))}
      //         </Box>
      //       );
      //     }

      //     return parsed.join(', ');
      //   } catch {
      //     return value;
      //   }
      // }

      if (typeof value === 'string' && value.startsWith('[')) {
        try {
          const parsed = JSON.parse(value);

          if (
            Array.isArray(parsed) &&
            typeof parsed[0] === 'object' &&
            parsed[0] !== null
          ) {
            return (
              <Box>
                {parsed.map((v, index) => (
                  <Box key={index} sx={{ mb: 1 }}>
                    {Object.entries(v)
                      .filter(([, val]) => val !== null) // 👈 Filter out null values
                      .map(([k, val]) => (
                        <div key={k}>
                          {k}: {val.toString()}
                        </div>
                      ))}
                  </Box>
                ))}
              </Box>
            );
          }

          return parsed.join(', ');
        } catch {
          return value;
        }
      }

      return value;
    }

    // Handle other arrays and objects
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return '-';
      }
    }

    // Handle dates
    if (
      typeof value === 'string' &&
      value.includes('-') &&
      moment(value, true).isValid()
    ) {
      return formatDate(value, 'YYYY MMMM DD hh:mm A');
    }

    return value.toString();
  };

  // Only render if we have attributes
  if (!Object.keys(attributes).length) {
    return <Box>No changes to display</Box>;
  }

  return (
    <Box sx={{ p: 0 }}>
      <Grid container>
        <Grid size={12}>
          {/* Header */}
          <Box
            sx={{
              mb: 0,
              fontSize: 14,
              fontWeight: 'bold',
              backgroundColor:
                theme.palette.mode === 'dark' ? 'grey.800' : 'grey.300',
              p: 1,
            }}
          >
            <Grid container>
              <Grid size={1}>No.</Grid>
              <Grid size={3}>Field</Grid>
              <Grid size={4}>New Value</Grid>
              <Grid size={4}>Old Value</Grid>
            </Grid>
          </Box>

          {/* Data Rows */}
          {Object.keys(attributes).map((field) => {
            if (['updated_at', 'updated_by'].includes(field)) {
              return null;
            }

            a++;
            return (
              <Grid
                container
                key={field}
                sx={{
                  p: 1,
                  borderBottom: 1,
                  borderColor:
                    theme.palette.mode === 'dark' ? 'grey.500' : 'grey.300',
                  fontSize: 13,
                  '&:hover': {
                    backgroundColor:
                      theme.palette.mode === 'dark' ? 'grey.800' : 'grey.200',
                  },
                }}
              >
                <Grid size={1}>{a}</Grid>
                <Grid size={3}>
                  {removeUnderScore(field.replace(/_id$/, ''))}
                </Grid>
                <Grid sx={{ color: 'green' }} size={4}>
                  {checkValue(attributes[field], field)}
                </Grid>
                <Grid sx={{ color: 'red' }} size={4}>
                  {checkValue(oldValues[field], field)}
                </Grid>
              </Grid>
            );
          })}
        </Grid>
      </Grid>
    </Box>
  );
}

export function ExportLogDetail({ data }) {
  return (
    <>
      <Grid
        container
        sx={{
          fontSize: 15,
          fontWeight: '',
          fontStyle: 'italic ',
          paddingY: '6px !important',
          paddingLeft: '6px',
        }}
      >
        <Grid
          size={{
            xs: 2,
            sm: 2,
            md: 2,
          }}
        >
          Export Type
        </Grid>

        <Grid
          size={{
            xs: 2,
            sm: 2,
            md: 2,
          }}
        >
          Number of Records
        </Grid>
      </Grid>
      <Grid
        container
        sx={{
          fontSize: 13,
          paddingLeft: '6px',
          paddingTop: '8px',
          paddingBottom: '4px',
          borderBottom: 1,
        }}
      >
        <Grid
          size={{
            xs: 2,
            sm: 2,
            md: 2,
          }}
        >
          {data?.properties?.exportType}
        </Grid>
        <Grid
          size={{
            xs: 2,
            sm: 2,
            md: 2,
          }}
        >
          {data?.properties?.exportTotal}
        </Grid>
      </Grid>
    </>
  );
}
