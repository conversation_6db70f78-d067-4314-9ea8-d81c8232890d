import { Box, Grid, useTheme } from '@mui/material';

const ProfileCustomRow = (props) => {
  const {
    singleRow = false,
    itemName,
    itemText,
    itemName2,
    itemText2,
    bgColor = false,
  } = props;

  const theme = useTheme();

  return (
    <Box>
      {singleRow ? (
        <Grid
          container
          spacing={2}
          sx={{
            backgroundColor: bgColor
              ? theme.palette.mode == 'dark'
                ? '#3b3b3b'
                : 'lightgray'
              : 'transparent',
            m: 0,
          }}
        >
          <Grid
            sx={{
              padding: '8px 18px !important',
            }}
            size={{
              xs: 12,
              md: 2.6,
            }}
          >
            {itemName}
          </Grid>
          <Grid
            sx={{
              padding: '8px 18px !important',
            }}
            size={{
              xs: 12,
              md: 9.4,
            }}
          >
            {itemText}
          </Grid>
        </Grid>
      ) : (
        <Grid
          container
          spacing={2}
          sx={{
            backgroundColor: bgColor
              ? theme.palette.mode == 'dark'
                ? '#3b3b3b'
                : 'lightgray'
              : 'transparent',
            m: 0,
          }}
        >
          <Grid
            sx={{
              paddingTop: '8px !important',
              paddingBottom: '8px !important',
            }}
            size={{
              xs: 12,
              md: singleRow ? 12 : 6,
            }}
          >
            <Box sx={{ display: 'flex' }}>
              <Box sx={{ width: '45%' }}>{itemName}</Box>
              <Box>{itemText}</Box>
            </Box>
          </Grid>

          <Grid
            sx={{
              paddingTop: '8px !important',
              paddingBottom: '8px !important',
              display: singleRow ? 'none' : 'block',
            }}
            size={{
              xs: 12,
              md: 6,
            }}
          >
            <Box sx={{ display: 'flex' }}>
              <Box sx={{ width: '45%' }}>{itemName2}</Box>
              <Box>{itemText2}</Box>
            </Box>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ProfileCustomRow;
