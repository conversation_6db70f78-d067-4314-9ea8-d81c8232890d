import { useEffect, useState } from 'react';
import {
  Box,
  Modal,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Button,
  useTheme,
  Typography,
  IconButton,
  StepContent,
  stepConnectorClasses,
  StepConnector,
  useMediaQuery,
} from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import RotateLeftIcon from '@mui/icons-material/RotateLeft';
import CloseIcon from '@mui/icons-material/Close';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import { styled } from '@mui/material/styles';
import CelebrationIcon from '@mui/icons-material/Celebration';
import { StepIconProps } from '@mui/material/StepIcon';
import SaveIcon from '@mui/icons-material/Save';
import Done from './Done';
import { LoadingPage } from '@/components/layout/LoadingPage';
import React from 'react';

const Root = styled('div')(({ theme }) => ({
  display: 'flex',
  [theme.breakpoints.up('md')]: {
    width: '75vw',
    height: '78vh',
  },
  [theme.breakpoints.down('md')]: {
    width: '95%',
    flexWrap: 'wrap',
  },
}));

const CStepper = ({
  show,
  setShow,
  steps,
  form,
  submit,
  title,
  isUpdate,
  done,
  setDone,
  hasQuickSave = false,
  setSelectedItems = ([]) => {},
  setPerms = ([]) => {},
  onCloseModal = () => {},
  loading = false,
  loadingButton = false,
  sidbarTop = null,
  enterToSubmit = true,
  titleFontSize = '16px',
  extraBtns = null,
  buttonText = null,
  disableButton = false,
}) => {
  const theme = useTheme();
  const [active, setActive] = useState(0);
  const matches = useMediaQuery(theme.breakpoints.up('md'));

  useEffect(() => {
    if (done && !hasQuickSave) {
      setActive((active) => active + 1);
    }
    if (done && hasQuickSave) {
      setActive(stepInside?.length - 1);
    }
  }, [done]);
  useEffect(() => {
    if (done) {
      setTimeout(() => {
        onClose();
      }, 1000);
    }
  }, [done, isUpdate]);

  let stepInside = [
    ...steps,
    {
      label: 'Done',
      icon: <CelebrationIcon />,
      step: <Done />,
    },
  ];

  const next = async () => {
    if (active < stepInside?.length - 1) {
      let res = await stepInside[active].validate();
      if (res) {
        setActive(active + 1);
        form.clearErrors();
      } else {
      }
    }
  };

  const quickSave = async () => {
    if (active < stepInside?.length - 1) {
      let res = await stepInside[active].validate();
      if (res) {
        if (form.isValid) {
          await submit();
        }
        form.clearErrors();
      } else {
        // show error
      }
    }
  };
  const prev = () => {
    if (active > 0) {
      setActive(active - 1);
    }
  };

  const restart = () => {
    setActive(0);
    form.reset();
    setDone(false);
  };

  const submitInside = async () => {
    if (active < stepInside?.length - 1) {
      let res = await stepInside[active].validate();
      if (res) {
        const clear = await submit();
        if (clear) {
          form.clearErrors();
        }
      } else {
        // show error
      }
    }
  };

  const changeStep = async (index) => {
    if (active !== stepInside?.length - 1) {
      let res = true;

      for (let i = 0; i < index; i++) {
        let stepRes = await stepInside[i].validate();
        if (!stepRes) {
          setActive(i);
          return;
        }
        res = res && stepRes;
      }
      if (res && index !== stepInside?.length - 1) {
        setActive(index);
      }
    }
  };

  const onClose = () => {
    setSelectedItems([]);
    setShow(false);
    form.reset();
    form.clearErrors();
    setActive(0);
    setDone(false);
    setPerms != undefined ? setPerms([]) : '';
    onCloseModal();
  };

  const ColorlibStepIconRoot = styled('div')<{
    ownerState: { completed?: boolean; active?: boolean };
  }>(({ theme, ownerState }) => ({
    backgroundColor:
      theme.palette.mode === 'dark' ? theme.colors.alpha.black[70] : '#ccc',
    zIndex: 1,
    color: '#fff',
    width: 35,
    height: 35,
    display: 'flex',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    ...(ownerState.active && {
      backgroundImage:
        'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
      boxShadow: '0 4px 10px 0 rgba(0,0,0,.25)',
    }),
    ...(ownerState.completed && {
      backgroundImage:
        'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
    }),
  }));

  const style = {
    position: 'absolute' as 'absolute',
    top: '43.5%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    bgcolor: ` ${theme.colors.alpha.white[100]}`,
    border: `2px solid ${theme.colors.alpha.black[50]}`,
    boxShadow: 24,
    borderRadius: '10px',
  };

  const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
    [`&.${stepConnectorClasses.active}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        backgroundImage:
          'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
      },
    },
    [`&.${stepConnectorClasses.completed}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        backgroundImage:
          'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
      },
    },
    [`& .${stepConnectorClasses.line}`]: {
      height: 40,
      width: 2,
      border: 0,
      margin: 'inherit',
      backgroundColor:
        theme.palette.mode === 'dark'
          ? theme.colors.alpha.black[50]
          : '#eaeaf0',
      borderRadius: 1,
    },
  }));
  function ColorlibStepIcon(props: StepIconProps) {
    const { active, completed, className } = props;

    const icons: { [index: string]: string } = stepInside.reduce(
      (acc, step, index) => {
        acc[index + 1] = step.icon;
        return acc;
      },
      {},
    );

    return (
      <ColorlibStepIconRoot
        // sx={{ cursor: 'pointer' }}
        ownerState={{ completed, active }}
        className={className}
      >
        {icons[String(props.icon)]}
      </ColorlibStepIconRoot>
    );
  }

  const ColorlibConnector2 = styled(StepConnector)(({ theme }) => ({
    [`&.${stepConnectorClasses.alternativeLabel}`]: {
      top: 22,
    },
    [`&.${stepConnectorClasses.active}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        backgroundImage:
          'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
      },
    },
    [`&.${stepConnectorClasses.completed}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        backgroundImage:
          'linear-gradient(342deg, rgba(26,102,214,1) 39%, rgba(0,212,255,1) 100%)',
      },
    },
    [`& .${stepConnectorClasses.line}`]: {
      height: 3,
      border: 0,
      backgroundColor:
        theme.palette.mode === 'dark'
          ? theme.colors.alpha.black[50]
          : '#eaeaf0',
      borderRadius: 1,
    },
  }));

  const handleKeyDown = async (event) => {
    if (event.key === 'Enter' && !event.shiftKey && enterToSubmit) {
      event.preventDefault();
      setTimeout(() => {
        submitInside();
      }, 100);
    }
  };

  return (
    <Modal
      onKeyDown={handleKeyDown}
      open={show}
      aria-labelledby="parent-modal-title"
      aria-describedby="parent-modal-description"
    >
      <Root
        sx={
          !matches
            ? {
                height: '90vh',
                position: 'absolute' as 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                bgcolor: ` ${theme.colors.alpha.white[100]}`,
                border: `2px solid ${theme.colors.alpha.black[50]}`,
                boxShadow: 24,
                borderRadius: '10px',
              }
            : style
        }
      >
        {matches ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '22%',
            }}
          >
            <Typography
              variant="h5"
              sx={{
                pt: '20px',
                px: '10px',
                fontSize: titleFontSize,
                fontWeight: 'bold',
              }}
            >
              {title}
            </Typography>
            {sidbarTop}
            <Box
              sx={{
                my: sidbarTop ? 'unset' : 'auto',
                mb: 'auto',
                width: '100%',
                maxHeight: '620px',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '5px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#6E6E6E',
                  borderRadius: '20px',
                },
              }}
            >
              <Box
                sx={{
                  p: '10px',
                }}
              >
                <Stepper
                  orientation="vertical"
                  activeStep={active}
                  connector={<ColorlibConnector />}
                >
                  {stepInside.map((step, index) => (
                    <Step key={index}>
                      <StepLabel
                        sx={
                          step.label == 'Done'
                            ? {}
                            : { cursor: 'pointer', fontSize: '10px !important' }
                        }
                        onClick={
                          step.label == 'Done'
                            ? () => {}
                            : () => changeStep(index)
                        }
                        StepIconComponent={ColorlibStepIcon}
                      >
                        {step.label}
                      </StepLabel>
                    </Step>
                  ))}
                </Stepper>
              </Box>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              width: '100%',
              py: '10px',
              px: '20px',
              borderBottom: `2px solid ${theme.colors.alpha.black[50]}`,
            }}
          >
            {!matches ? (
              <Box
                sx={{
                  display: 'flex',
                  width: '100%',
                  justifyContent: 'space-between',
                }}
              >
                <Typography
                  variant="h5"
                  sx={{ fontSize: '14px', fontWeight: 'bold' }}
                >
                  {title}
                </Typography>
                <IconButton onClick={onClose} aria-label="delete">
                  <CloseIcon />
                </IconButton>
              </Box>
            ) : (
              <></>
            )}
            <Stepper
              alternativeLabel
              activeStep={active}
              connector={<ColorlibConnector2 />}
            >
              {stepInside.map((step, index) => (
                <Step key={index}>
                  <StepLabel
                    sx={{ cursor: 'pointer' }}
                    StepIconComponent={ColorlibStepIcon}
                  >
                    {step.label}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            px: '15px',
            py: '10px',
            borderLeft: matches
              ? `2px solid ${theme.colors.alpha.black[50]}`
              : 'none',
            height: !matches ? '60vh' : 'unset',
          }}
        >
          {/* close button */}
          {matches ? (
            <Box
              sx={{
                position: 'absolute',
                right: '10px',
              }}
            >
              {/* <Typography variant="h5">{title}</Typography> */}
              <IconButton sx={{ p: 0 }} onClick={onClose} aria-label="delete">
                <CloseIcon />
              </IconButton>
            </Box>
          ) : (
            <></>
          )}

          {/* stepper cards */}
          <Box
            sx={{
              height: '94%',
              overflow: 'auto',
              px: '5px',
              direction: 'rtl',
              '&::-webkit-scrollbar': {
                width: '5px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: '#6E6E6E',
                borderRadius: '20px',
              },
            }}
          >
            <Box sx={{ direction: 'ltr' }}>
              {loading ? (
                <LoadingPage
                  style={{
                    position: 'absolute',
                    transform: 'translate(-50%, -50%)',
                    top: '50%',
                    left: '50%',
                  }}
                />
              ) : (
                <Stepper
                  activeStep={active}
                  orientation="vertical"
                  className="stepper-content"
                  sx={{ mx: '16px' }}
                >
                  {stepInside.map((step, index) => (
                    <Step key={index}>
                      <StepContent>{step.step}</StepContent>
                    </Step>
                  ))}
                </Stepper>
              )}
            </Box>
          </Box>

          {/* stepper actions */}
          <Box
            sx={{
              display: 'flex',
              position: 'absolute',
              justifyContent: 'center',
              width: '97.5%',
              bottom: !matches ? '-25px' : 10,
              right: 10,
              left: 10,
            }}
          >
            <Box>
              <Button
                sx={{
                  mx: '2.5px',
                  visibility:
                    active !== 0 && active !== stepInside?.length - 1
                      ? 'visible'
                      : 'hidden',
                }}
                startIcon={<ChevronLeftIcon />}
                variant="contained"
                onClick={prev}
              >
                Back
              </Button>
            </Box>
            <Box>
              {active == stepInside?.length - 2 ? (
                <Button
                  disabled={loadingButton || disableButton}
                  endIcon={<SaveIcon />}
                  variant="contained"
                  // type="submit"
                  color="success"
                  sx={{ mx: '2.5px' }}
                  onClick={() => {
                    submitInside();
                  }}
                  loading={loadingButton}
                  loadingPosition="end"
                >
                  <span>{buttonText ?? (isUpdate ? 'Update' : 'Save')}</span>
                </Button>
              ) : active == stepInside?.length - 1 ? (
                !isUpdate ? (
                  <Button
                    endIcon={<RotateLeftIcon />}
                    variant="contained"
                    onClick={restart}
                  >
                    Restart
                  </Button>
                ) : (
                  <Button
                    startIcon={<CloseIcon />}
                    color="error"
                    sx={{ mx: '2.5px' }}
                    onClick={onClose}
                  >
                    Close
                  </Button>
                )
              ) : (
                <>
                  {hasQuickSave ? (
                    <Button
                      disabled={loadingButton || disableButton}
                      endIcon={<SaveIcon />}
                      variant="contained"
                      color="success"
                      // color="warning"
                      type="submit"
                      sx={{ mx: '2.5px' }}
                      onClick={quickSave}
                      loading={loadingButton}
                      loadingPosition="end"
                    >
                      {buttonText ?? (isUpdate ? 'Update' : 'Save')}
                    </Button>
                  ) : (
                    ''
                  )}
                </>
              )}
              {extraBtns ? extraBtns() : <></>}
              <Button
                sx={{
                  mx: '2.5px',
                  visibility:
                    active == stepInside?.length - 2 ||
                    active == stepInside?.length - 1
                      ? 'hidden'
                      : 'visible',
                }}
                endIcon={<ChevronRightIcon />}
                variant="contained"
                onClick={next}
                disabled={disableButton}
              >
                Next
              </Button>
            </Box>
          </Box>
        </Box>
      </Root>
    </Modal>
  );
};

export default CStepper;
