import StepConnector, {
  stepConnectorClasses,
} from '@mui/material/StepConnector';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import { styled } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Step from '@mui/material/Step';
import Box from '@mui/material/Box';

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: { top: 20 },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: theme.colors.primary.main,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: theme.colors.primary.main,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor:
      theme.palette.mode === 'dark' ? theme.colors.alpha.black[70] : '#eaeaf0',
    borderTopWidth: 2,
    borderRadius: 1,
  },
}));

interface StepItem {
  icon?: React.ReactNode;
  label?: string;
}

interface ValidationSchema {
  validate: (values: any) => Promise<any>;
}

interface CustomStepperProps {
  activeStep: number;
  steps: StepItem[];
  setActiveStep(...args: unknown[]): unknown;
  customValidation?(...args: unknown[]): unknown;
  actions?: any;
  values?: any;
  validationSchema?: ValidationSchema[];
}

export default function CustomStepper(props: CustomStepperProps) {
  const handleActiveStep = async (index: number) => {
    for (let i = 0; i <= index; i++) {
      try {
        await props.validationSchema[i].validate(props.values);
      } catch (err) {
        props.setActiveStep(i);
        if (i == props.activeStep) {
          let validationErrors = await props.actions.validateForm();
          if (Object.keys(validationErrors).length > 0) {
            let errors = {};
            Object.keys(validationErrors).forEach((key) => {
              errors[key] = true;
            });
            if (Object.keys(errors).length) props.actions.setTouched(errors);
          }
        }
        return;
      }
      if (props.customValidation) {
        const isCustomValid = await props.customValidation(
          props.values,
          props.actions,
          props.activeStep + i + 1,
        );
        if (!isCustomValid) return;
      }
    }
    let validationErrors = await props.actions.validateForm();
    if (props.activeStep > index) {
      props.setActiveStep(index);
    } else if (Object.keys(validationErrors).length === 0) {
      props.setActiveStep(index);
    }
  };

  return (
    <Stepper
      activeStep={props.activeStep}
      alternativeLabel
      connector={<ColorlibConnector />}
      sx={{ overflowX: 'auto' }}
    >
      {props.steps.map((step, index) => (
        <Step key={index}>
          <StepLabel
            sx={{ cursor: 'pointer' }}
            onClick={() => handleActiveStep(index)}
            StepIconComponent={() => (
              <Avatar
                sx={{
                  cursor: 'pointer',
                  bgcolor: (theme) => {
                    return props.activeStep >= index
                      ? theme.colors.primary.main
                      : theme.colors.alpha.black[70];
                  },
                }}
              >
                {step.icon}
              </Avatar>
            )}
          >
            <Box
              sx={{
                cursor: 'pointer',

                color: (theme) => {
                  return props.activeStep >= index
                    ? theme.colors.primary.main
                    : theme.colors.alpha.black[70];
                },
              }}
            >
              {step.label}
            </Box>
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
}
