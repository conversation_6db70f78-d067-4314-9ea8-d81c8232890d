import {
  useContext,
  createContext,
  ReactNode,
  useState,
  useEffect,
} from 'react';

import axios from '@/lib/axios';

type LocationsProviderProps = { children: ReactNode };

type LocationsContextValue = {
  map(
    arg0: (item: any) => {
      name: any;
      id: number;
      key: any;
      icon: React.ReactNode;
      to: string;
    },
  ): ConcatArray<{
    name: string;
    id: number;
    key: string;
    to: string;
    icon: React.ReactNode;
  }>;
  refreshLocations: () => void;
  isLoadingLocations: boolean;
};

export const contextProvider = createContext<LocationsContextValue>({
  map: () => [],
  refreshLocations: () => {},
  isLoadingLocations: true,
});

const LocationsProvider = ({ children }: LocationsProviderProps) => {
  const [dataTem, setDataTem] = useState([]);
  const [refreshCounter, setRefreshCounter] = useState(0);

  const [isLoadingLocations, setIsLoadingLocations] = useState(true);

  const getLocations = async () => {
    setIsLoadingLocations(true);
    if (localStorage.getItem('token')) {
      try {
        let res = await axios.get('locations/getAll');
        if (res.status == 200) {
          setDataTem(res?.data?.data);
          setIsLoadingLocations(false);
        }
      } catch (e) {
        console.log(e.message);

        if (e?.response?.status == 404) {
          // Handle error case
        }
        setIsLoadingLocations(false);
      }
    }
  };

  const refreshLocations = () => {
    setRefreshCounter((prevCounter) => prevCounter + 1);
  };

  useEffect(() => {
    getLocations();
  }, [refreshCounter]);

  return (
    <contextProvider.Provider
      value={{
        refreshLocations: refreshLocations,
        map: (callback) => dataTem.map(callback),
        isLoadingLocations,
      }}
    >
      {children}
    </contextProvider.Provider>
  );
};

export default LocationsProvider;
export const useLocationContext = () => useContext(contextProvider);
