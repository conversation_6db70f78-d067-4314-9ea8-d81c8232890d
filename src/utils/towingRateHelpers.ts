import axios from '@/lib/axios';

export const checkDuplicateTowingRate = async (values: {
  location_id: number | null;
  loading_city_id: number | null;
  half_location_id?: number | null;
}) => {
  if (!values.location_id || !values.loading_city_id) return false;

  try {
    const { data } = await axios.post('/towing-rates/check-duplicate', {
      location_id: values.location_id,
      loading_city_id: values.loading_city_id,
      half_location_id: values.half_location_id || undefined,
    });
    return data;
  } catch (error) {
    console.error('Error checking duplicate rate:', error);
    return { isDuplicate: false };
  }
};
