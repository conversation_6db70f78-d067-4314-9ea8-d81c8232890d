const getNestedValue = (obj, key) => {
  if (!key) return null;
  const keys = key.split('.');
  let value = obj;
  for (let i = 0; i < keys.length; i++) {
    if (value === null || value === undefined) {
      return null;
    }
    value = value[keys[i]];
    if (value === undefined) {
      return null;
    }
  }
  return value;
};
// const handleSort = (head, items, orderBy) => {
//   const firstCol = head?.sortColumnName ?? head?.id;
//   const secondCol = head?.sortColumnName2 ?? null;
//   let newOrder = orderBy.order;
//   const sortedData = [...items].sort((a, b) => {
//     if (head?.sort) {
//       if (head?.sort(a) < head.sort(b)) return newOrder === 'asc' ? -1 : 1;
//       if (head?.sort(a) > head.sort(b)) return newOrder === 'asc' ? 1 : -1;
//     }

//     let aValue = getNestedValue(a, firstCol);
//     let bValue = getNestedValue(b, firstCol);
//     // Handle missing data
//     if (aValue === null) return newOrder === 'asc' ? 1 : -1;
//     if (bValue === null) return newOrder === 'asc' ? -1 : 1;
//     // Convert values to numbers if they represent numeric data
//     if (!isNaN(aValue)) aValue = Number(aValue);
//     if (!isNaN(bValue)) bValue = Number(bValue);

//     // Compare the first column
//     if (typeof aValue === 'number' && typeof bValue === 'number') {
//       if (aValue < bValue) return newOrder === 'asc' ? -1 : 1;
//       if (aValue > bValue) return newOrder === 'asc' ? 1 : -1;
//     } else if (typeof aValue === 'string' && typeof bValue === 'string') {
//       aValue = aValue.toLowerCase();
//       bValue = bValue.toLowerCase();
//       if (aValue < bValue) return newOrder === 'asc' ? -1 : 1;
//       if (aValue > bValue) return newOrder === 'asc' ? 1 : -1;
//     }

//     // Compare the second column
//     let aSecondValue = getNestedValue(a, secondCol);
//     let bSecondValue = getNestedValue(b, secondCol);

//     // Handle missing data for the second column
//     if (aSecondValue === null) return newOrder === 'asc' ? 1 : -1;
//     if (bSecondValue === null) return newOrder === 'asc' ? -1 : 1;

//     // Convert values to numbers if they represent numeric data
//     if (!isNaN(aSecondValue)) aSecondValue = Number(aSecondValue);
//     if (!isNaN(bSecondValue)) bSecondValue = Number(bSecondValue);

//     if (typeof aSecondValue === 'number' && typeof bSecondValue === 'number') {
//       if (aSecondValue < bSecondValue) return newOrder === 'asc' ? -1 : 1;
//       if (aSecondValue > bSecondValue) return newOrder === 'asc' ? 1 : -1;
//     } else if (
//       typeof aSecondValue === 'string' &&
//       typeof bSecondValue === 'string'
//     ) {
//       aSecondValue = aSecondValue.toLowerCase();
//       bSecondValue = bSecondValue.toLowerCase();
//       if (aSecondValue < bSecondValue) return newOrder === 'asc' ? -1 : 1;
//       if (aSecondValue > bSecondValue) return newOrder === 'asc' ? 1 : -1;
//     }
//     // Fallback to default comparison
//     return 0;
//   });
//   return sortedData;
// };

const handleSort = (head, items, orderBy) => {
  const firstCol = head?.sortColumnName ?? head?.id;
  const secondCol = head?.sortColumnName2 ?? null;
  const newOrder = orderBy.order;

  const sortedData = [...items].sort((a, b) => {
    // Use custom sort function if provided
    if (head?.sort) {
      const result = head.sort(a, b);
      if (result !== 0) return newOrder === 'asc' ? result : -result;
    }

    // Get values for the first column
    let aValue = getNestedValue(a, firstCol);
    let bValue = getNestedValue(b, firstCol);

    // Handle missing data
    if (aValue === null) return newOrder === 'asc' ? 1 : -1;
    if (bValue === null) return newOrder === 'asc' ? -1 : 1;

    // Convert values to numbers if they represent numeric data
    if (!isNaN(aValue)) aValue = Number(aValue);
    if (!isNaN(bValue)) bValue = Number(bValue);

    // Compare the first column
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      if (aValue < bValue) return newOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return newOrder === 'asc' ? 1 : -1;
    } else if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
      if (aValue < bValue) return newOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return newOrder === 'asc' ? 1 : -1;
    }

    // If first column values are equal, compare the second column
    if (secondCol) {
      let aSecondValue = getNestedValue(a, secondCol);
      let bSecondValue = getNestedValue(b, secondCol);

      // Handle missing data for the second column
      if (aSecondValue === null) return newOrder === 'asc' ? 1 : -1;
      if (bSecondValue === null) return newOrder === 'asc' ? -1 : 1;

      // Convert values to numbers if they represent numeric data
      if (!isNaN(aSecondValue)) aSecondValue = Number(aSecondValue);
      if (!isNaN(bSecondValue)) bSecondValue = Number(bSecondValue);

      if (
        typeof aSecondValue === 'number' &&
        typeof bSecondValue === 'number'
      ) {
        if (aSecondValue < bSecondValue) return newOrder === 'asc' ? -1 : 1;
        if (aSecondValue > bSecondValue) return newOrder === 'asc' ? 1 : -1;
      } else if (
        typeof aSecondValue === 'string' &&
        typeof bSecondValue === 'string'
      ) {
        aSecondValue = aSecondValue.toLowerCase();
        bSecondValue = bSecondValue.toLowerCase();
        if (aSecondValue < bSecondValue) return newOrder === 'asc' ? -1 : 1;
        if (aSecondValue > bSecondValue) return newOrder === 'asc' ? 1 : -1;
      }
    }

    // Fallback to default comparison
    return 0;
  });

  return sortedData;
};

const transformField = (value, defaultValue = '') => {
  if (value === null || value === undefined) {
    return defaultValue; // Return default value for null or undefined values
  }
  if (typeof value === 'string' && value.includes('T')) {
    // Assume it's a date string in ISO format
    return formatDate(value);
  }
  if (Array.isArray(value)) {
    // Extract 'detail', 'happened_at', or vehicle data from each object
    return value
      .map((obj) => obj.detail || obj.happened_at || JSON.stringify(obj))
      .join(', ');
  } else if (typeof value === 'object' && value !== null) {
    // Handle single objects
    return Object.entries(value)
      .map(([key, val]) => `${key}: ${val}`)
      .join(', ');
  }
  return value;
};

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown Date';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const sortDataForDownloadDamages = (data, headers, props) => {
  let sortedData = data;
  if (props.options) {
    const sortCol = props?.options?.orderBy ?? { order: 'desc', column: 'id' };
    const temp_head = headers.find(
      (row) => (row.sortColumnName ?? row.id) == sortCol.column,
    );

    // Ensure temp_head is found before sorting
    if (temp_head) {
      sortedData = handleSort(temp_head, data, sortCol);
    } else {
      console.warn('No matching header found for sorting');
    }
  }

  return sortedData?.map((row) => {
    let tempArray = [];
    headers.forEach((col) => {
      let value;
      switch (col.id) {
        case 'loaded_by':
          value = row.vehicles?.containers?.loaders?.[0]?.name || '';
          break;
        case 'pol_locations':
          value = row.vehicles?.pol_locations?.name || '';
          break;
        case 'description':
          let vehicle = row.vehicles || {};
          value = `${vehicle.year || ''} ${vehicle.make || ''} `;
          break;
        case 'eta':
          value = row.vehicles?.containers?.bookings?.eta
            ? formatDate(row.vehicles.containers.bookings.eta)
            : '';
          break;
        case 'vin':
          value = row.vehicles?.vin || 'undefined';
          break;
        case 'price':
          value =
            row.vehicles?.price !== undefined
              ? row.vehicles.price.toString()
              : '';
          break;
        case 'lot_number':
          value = row.vehicles?.lot_number || '';
          break;
        case 'company':
          value = row.vehicles?.customers?.companies?.name || '';
          break;
        case 'container_number':
          value = row.vehicles?.containers?.container_number || '';
          break;
        case 'loading_date':
          value = row.vehicles?.containers?.loading_date
            ? formatDate(row.vehicles.containers.loading_date)
            : '';
          break;
        default:
          value = props[col.id]
            ? props[col.id](row)
            : getNestedValue(row, col.id);
      }
      value = transformField(value);
      let tmp = String(value);
      tempArray.push(tmp === 'undefined' || tmp === 'null' ? '' : tmp);
    });

    // Log the tempArray to see what is being pushed
    // console.log('TempArray after processing headers:', tempArray);

    return tempArray;
  });
};
